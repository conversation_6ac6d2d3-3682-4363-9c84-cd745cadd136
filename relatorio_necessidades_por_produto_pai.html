<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Necessidades de Compras por Produto Pai</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --secondary-color: #6c757d;
      --secondary-hover: #5a6268;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1600px;
      margin: 30px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-secondary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-secondary:hover {
      background-color: var(--secondary-hover);
    }

    .filters-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #e9ecef;
    }

    .filters-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      font-weight: 600;
      margin-bottom: 5px;
      color: var(--text-color);
    }

    .filter-group select,
    .filter-group input {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .filter-group select:focus,
    .filter-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .filters-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      padding-top: 15px;
      border-top: 1px solid #e9ecef;
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .stat-card.success {
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }

    .stat-card.warning {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .stat-card.info {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .stat-value {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }

    .produto-pai-section {
      margin-bottom: 30px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow: hidden;
      background: white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .produto-pai-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .produto-pai-header:hover {
      background: linear-gradient(135deg, var(--primary-hover) 0%, #073d6b 100%);
    }

    .produto-pai-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .produto-pai-badge {
      background: rgba(255,255,255,0.2);
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
    }

    .produto-pai-stats {
      display: flex;
      gap: 20px;
      font-size: 14px;
    }

    .produto-pai-content {
      display: none;
      padding: 0;
    }

    .produto-pai-content.expanded {
      display: block;
    }

    .necessidades-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .necessidades-table th {
      background: #f8f9fa;
      color: var(--text-color);
      padding: 12px 8px;
      text-align: left;
      font-weight: 600;
      font-size: 13px;
      border-bottom: 2px solid #dee2e6;
    }

    .necessidades-table td {
      padding: 12px 8px;
      border-bottom: 1px solid #e9ecef;
      vertical-align: middle;
    }

    .necessidades-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .necessidades-table tbody tr:last-child td {
      border-bottom: none;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-data {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
    }

    .no-data i {
      font-size: 48px;
      margin-bottom: 15px;
      opacity: 0.5;
    }

    .criticidade-alta {
      color: #dc3545;
      font-weight: bold;
    }

    .criticidade-media {
      color: #ffc107;
      font-weight: bold;
    }

    .criticidade-baixa {
      color: #28a745;
      font-weight: bold;
    }

    .deficit-badge {
      background: #dc3545;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: bold;
    }

    .superavit-badge {
      background: #28a745;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: bold;
    }

    .neutro-badge {
      background: #6c757d;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: bold;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 5px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    }

    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }

    .notification.success {
      background-color: #28a745;
    }

    .notification.error {
      background-color: #dc3545;
    }

    .notification.info {
      background-color: #17a2b8;
    }

    .expand-icon {
      transition: transform 0.2s ease;
    }

    .expand-icon.rotated {
      transform: rotate(180deg);
    }

    @media (max-width: 768px) {
      .container {
        width: 98%;
        margin: 10px auto;
        padding: 15px;
      }

      .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
      }

      .filters-grid {
        grid-template-columns: 1fr;
      }

      .stats-section {
        grid-template-columns: repeat(2, 1fr);
      }

      .produto-pai-stats {
        flex-direction: column;
        gap: 5px;
      }

      .necessidades-table {
        font-size: 12px;
      }

      .necessidades-table th,
      .necessidades-table td {
        padding: 8px 4px;
      }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-shopping-cart"></i> Necessidades de Compras por Produto Pai</h1>
      <div class="header-actions">
        <button class="btn btn-secondary" onclick="window.location.href='relatorio_ops_pai.html'">
          <i class="fas fa-industry"></i> OPs Pai
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='relatorio_necessidades_compras.html'">
          <i class="fas fa-list"></i> Necessidades Geral
        </button>
        <button class="btn btn-primary" onclick="recarregarDados()">
          <i class="fas fa-sync"></i> Atualizar
        </button>
      </div>
    </div>

    <!-- Seção de Filtros -->
    <div class="filters-section">
      <div class="filters-title">
        <i class="fas fa-filter"></i> Filtros de Análise
      </div>

      <div class="filters-grid">
        <div class="filter-group">
          <label for="filtroProdutoPai">Produto Pai:</label>
          <select id="filtroProdutoPai">
            <option value="">Todos os Produtos Pai</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroGrupo">Grupo MP:</label>
          <select id="filtroGrupo">
            <option value="">Todos os Grupos</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroFamilia">Família MP:</label>
          <select id="filtroFamilia">
            <option value="">Todas as Famílias</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroCriticidade">Criticidade:</label>
          <select id="filtroCriticidade">
            <option value="">Todas</option>
            <option value="ALTA">Alta</option>
            <option value="MEDIA">Média</option>
            <option value="BAIXA">Baixa</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroSituacao">Situação Estoque:</label>
          <select id="filtroSituacao">
            <option value="">Todas</option>
            <option value="DEFICIT">Déficit</option>
            <option value="SUPERAVIT">Superávit</option>
            <option value="NEUTRO">Neutro</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroBusca">Buscar MP:</label>
          <input type="text" id="filtroBusca" placeholder="Código ou descrição...">
        </div>
      </div>

      <div class="filters-actions">
        <button class="btn btn-secondary" onclick="limparFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button class="btn btn-primary" onclick="aplicarFiltros()">
          <i class="fas fa-search"></i> Aplicar Filtros
        </button>
        <button class="btn btn-success" onclick="expandirTodos()">
          <i class="fas fa-expand-arrows-alt"></i> Expandir Todos
        </button>
      </div>
    </div>

    <!-- Seção de Estatísticas -->
    <div class="stats-section" id="statsSection">
      <div class="stat-card">
        <div class="stat-value" id="totalProdutosPai">-</div>
        <div class="stat-label">Produtos Pai</div>
      </div>
      <div class="stat-card success">
        <div class="stat-value" id="totalMateriais">-</div>
        <div class="stat-label">Materiais MP</div>
      </div>
      <div class="stat-card warning">
        <div class="stat-value" id="materiaisDeficit">-</div>
        <div class="stat-label">Em Déficit</div>
      </div>
      <div class="stat-card info">
        <div class="stat-value" id="valorTotal">-</div>
        <div class="stat-label">Valor Total</div>
      </div>
    </div>

    <!-- Conteúdo Principal -->
    <div id="loadingContainer" class="loading">
      <div class="loading-spinner"></div>
      <p>Carregando necessidades por produto pai...</p>
    </div>

    <div id="noDataContainer" class="no-data" style="display: none;">
      <i class="fas fa-inbox"></i>
      <h3>Nenhuma necessidade encontrada</h3>
      <p>Não há necessidades de compras para os filtros aplicados.</p>
    </div>

    <div id="produtosPaiContainer" style="display: none;">
      <!-- Seções de produtos pai serão geradas dinamicamente -->
    </div>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>
  </div>

  <script type="module">
    // Importações do Firebase
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
    import {
      getFirestore,
      collection,
      getDocs,
      query,
      where,
      orderBy
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // Configuração do Firebase (usar a mesma do sistema)
    const firebaseConfig = {
      apiKey: "AIzaSyBs7Qhf2HpjZKbdyQVhLGdHjXDKHjCJ8nE",
      authDomain: "banco-mrp.firebaseapp.com",
      projectId: "banco-mrp",
      storageBucket: "banco-mrp.appspot.com",
      messagingSenderId: "1234567890",
      appId: "1:1234567890:web:abcdef123456"
    };

    // Inicializar Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Variáveis globais
    let ordensProducao = [];
    let produtos = [];
    let estoques = [];
    let estruturas = [];
    let grupos = [];
    let familias = [];
    let necessidadesPorProdutoPai = new Map();

    // Função para mostrar notificação
    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = `notification ${type}`;
      notification.classList.add('show');

      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // Função para calcular saldo disponível
    function calcularSaldoDisponivel(estoque) {
      if (!estoque) return 0;

      const saldo = Number(estoque.saldo) || 0;
      const saldoReservado = Number(estoque.saldoReservado) || 0;
      const empenho = Number(estoque.empenho) || 0;

      return Math.max(0, saldo - Math.max(saldoReservado, empenho));
    }

    // Função para calcular criticidade
    function calcularCriticidade(necessidade, saldoDisponivel, leadTime = 0) {
      const deficit = Math.max(0, necessidade - saldoDisponivel);

      if (deficit === 0) return 'BAIXA';
      if (deficit > necessidade * 0.5 || leadTime > 30) return 'ALTA';
      return 'MEDIA';
    }

    // Função para carregar dados
    async function carregarDados() {
      try {
        console.log('🔄 Carregando dados...');

        // Mostrar loading
        document.getElementById('loadingContainer').style.display = 'block';
        document.getElementById('noDataContainer').style.display = 'none';
        document.getElementById('produtosPaiContainer').style.display = 'none';

        // Carregar todos os dados necessários
        const [ordensSnap, produtosSnap, estoquesSnap, estruturasSnap, gruposSnap, familiasSnap] = await Promise.all([
          getDocs(query(collection(db, "ordensProducao"), orderBy("numero", "desc"))),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "grupos")),
          getDocs(collection(db, "familias"))
        ]);

        // Processar dados
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        familias = familiasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`📊 Dados carregados:`, {
          ordensProducao: ordensProducao.length,
          produtos: produtos.length,
          estoques: estoques.length,
          estruturas: estruturas.length
        });

        // Carregar filtros
        carregarFiltros();

        // Processar necessidades por produto pai
        await processarNecessidadesPorProdutoPai();

      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        showNotification('Erro ao carregar dados: ' + error.message, 'error');

        document.getElementById('loadingContainer').style.display = 'none';
        document.getElementById('noDataContainer').style.display = 'block';
      }
    }

    // Função para carregar filtros
    function carregarFiltros() {
      // Carregar produtos pai (OPs principais)
      const produtosPai = ordensProducao
        .filter(op => !op.produtoPaiId || op.produtoPaiId === op.produtoId || op.nivel === 0)
        .map(op => {
          const produto = produtos.find(p => p.id === op.produtoId);
          return {
            id: op.produtoId,
            codigo: produto?.codigo || 'N/A',
            descricao: produto?.descricao || 'N/A'
          };
        })
        .filter((produto, index, self) =>
          index === self.findIndex(p => p.id === produto.id)
        )
        .sort((a, b) => a.codigo.localeCompare(b.codigo));

      const filtroProdutoPai = document.getElementById('filtroProdutoPai');
      filtroProdutoPai.innerHTML = '<option value="">Todos os Produtos Pai</option>';
      produtosPai.forEach(produto => {
        filtroProdutoPai.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
      });

      // Carregar grupos
      const filtroGrupo = document.getElementById('filtroGrupo');
      filtroGrupo.innerHTML = '<option value="">Todos os Grupos</option>';
      grupos.forEach(grupo => {
        filtroGrupo.innerHTML += `<option value="${grupo.codigoGrupo}">${grupo.codigoGrupo} - ${grupo.nomeGrupo}</option>`;
      });

      // Carregar famílias
      const filtroFamilia = document.getElementById('filtroFamilia');
      filtroFamilia.innerHTML = '<option value="">Todas as Famílias</option>';
      familias.forEach(familia => {
        filtroFamilia.innerHTML += `<option value="${familia.codigoFamilia}">${familia.codigoFamilia} - ${familia.nomeFamilia}</option>`;
      });
    }

    // Função para processar necessidades por produto pai (usando lógica do relatório original)
    async function processarNecessidadesPorProdutoPai() {
      console.log('🏭 Processando necessidades por produto pai...');

      necessidadesPorProdutoPai.clear();

      // Carregar estruturas
      const estruturasCarregadas = await carregarEstruturas();
      console.log('📋 Estruturas carregadas:', estruturasCarregadas.length);

      // Usar a mesma lógica do relatório original para processar necessidades
      // Mapa para armazenar necessidades separadas por produto pai
      // Chave: produtoId + "|" + produtoPaiId (ou "DIRETO" se não há produto pai)
      const necessidadesMap = new Map();

      // Filtrar apenas ordens pendentes ou em produção
      const ordensValidas = ordensProducao.filter(op => {
        const status = (op.status || '').toLowerCase();
        return status === 'pendente' || status === 'em produção';
      });

      console.log(`Total de ordens pendentes ou em produção encontradas: ${ordensValidas.length}`);

      // Processar cada ordem (igual ao relatório original)
      for (const ordem of ordensValidas) {
        console.log(`\nProcessando OP ${ordem.numero} (ID: ${ordem.id}, Status: ${ordem.status})...`);

        // Verificar se tem materiais necessários
        if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) {
          console.log(`OP ${ordem.numero} pulada: sem materiais necessários.`);
          continue;
        }

        console.log(`OP ${ordem.numero}: ${ordem.materiaisNecessarios.length} materiais necessários encontrados.`);

        // Processar cada material da ordem
        for (const material of ordem.materiaisNecessarios) {
          const produto = produtos.find(p => p.id === material.produtoId);
          if (!produto) {
            console.warn(`Produto não encontrado para material com produtoId ${material.produtoId} na OP ${ordem.numero}`);
            continue;
          }

          console.log(`Processando material ${produto.codigo} para OP ${ordem.numero}. Tipo: ${produto.tipo}`);

          // Se é produto PA ou SP, explodir estrutura para encontrar MPs
          if (produto.tipo === 'PA' || produto.tipo === 'SP') {
            console.log(`🔍 Explodindo estrutura de ${produto.codigo} (${produto.tipo}) - Qtd: ${material.quantidade}`);

            const quantidadeNecessaria = Number(material.quantidade) || 0;
            if (!quantidadeNecessaria || isNaN(quantidadeNecessaria)) {
              console.warn(`Quantidade inválida para explosão de ${produto.codigo}: ${material.quantidade}`);
              continue;
            }

            // Explodir estrutura para encontrar MPs necessárias
            const necessidadesMP = await explodirEstrutura(
              produto.id,
              quantidadeNecessaria,
              estruturasCarregadas
            );

            console.log(`📋 Estrutura de ${produto.codigo} explodida: ${necessidadesMP.length} MPs encontradas`);

            // Adicionar cada MP encontrada ao mapa de necessidades (separado por produto pai)
            for (const necessidadeMP of necessidadesMP) {
              const mpProduto = necessidadeMP.produto;

              // Criar chave única: produtoMP + produtoPai (IGUAL AO RELATÓRIO ORIGINAL)
              const chaveNecessidade = `${mpProduto.id}|${produto.id}`;

              console.log(`➕ Adicionando MP ${mpProduto.codigo} para produto pai ${produto.codigo} - Qtd: ${necessidadeMP.quantidade}`);

              // Criar ou atualizar a necessidade no mapa (separado por produto pai)
              if (!necessidadesMap.has(chaveNecessidade)) {
                necessidadesMap.set(chaveNecessidade, {
                  produtoId: mpProduto.id,
                  produtoPaiId: produto.id,
                  produtoPai: {
                    id: produto.id,
                    codigo: produto.codigo,
                    descricao: produto.descricao,
                    tipo: produto.tipo
                  },
                  produto: {
                    id: mpProduto.id,
                    codigo: mpProduto.codigo,
                    descricao: mpProduto.descricao,
                    unidade: mpProduto.unidade,
                    grupo: mpProduto.grupo || 'Sem Grupo',
                    familia: mpProduto.familia || 'Sem Família',
                    tipo: mpProduto.tipo,
                    leadTime: mpProduto.leadTime || 0,
                    pontoPedido: mpProduto.pontoPedido || 0,
                    estoqueMinimo: mpProduto.estoqueMinimo || 0,
                    loteCompra: mpProduto.loteCompra || 0,
                    precoUnitario: mpProduto.precoUnitario || 0
                  },
                  quantidade: 0,
                  ordens: new Set(),
                  consumoPorOP: new Map()
                });
              }

              const necessidadeExistente = necessidadesMap.get(chaveNecessidade);
              necessidadeExistente.quantidade += necessidadeMP.quantidade;
              necessidadeExistente.ordens.add(ordem.numero);

              // Somar ao consumo existente para esta OP
              const consumoAtual = necessidadeExistente.consumoPorOP.get(ordem.numero) || 0;
              necessidadeExistente.consumoPorOP.set(ordem.numero, consumoAtual + necessidadeMP.quantidade);

              console.log(`📊 MP ${mpProduto.codigo} para ${produto.codigo} - Total acumulado: ${necessidadeExistente.quantidade}`);
            }

            continue; // Pular para próximo material
          }

          // Para produtos MP diretos, usar chave especial "DIRETO" (IGUAL AO RELATÓRIO ORIGINAL)
          const quantidadeNecessaria = Number(material.quantidade) || 0;

          if (!quantidadeNecessaria || isNaN(quantidadeNecessaria)) {
            console.warn(`Quantidade necessária inválida para ${produto.codigo} na OP ${ordem.numero}`);
            continue;
          }

          // Para produtos MP diretos, usar chave especial "DIRETO"
          const chaveNecessidade = `${produto.id}|DIRETO`;

          // Criar ou atualizar a necessidade no mapa (MP direto)
          if (!necessidadesMap.has(chaveNecessidade)) {
            necessidadesMap.set(chaveNecessidade, {
              produtoId: produto.id,
              produtoPaiId: 'DIRETO',
              produtoPai: {
                id: 'DIRETO',
                codigo: 'DIRETO',
                descricao: 'Necessidade Direta (MP)',
                tipo: 'DIRETO'
              },
              produto: {
                id: produto.id,
                codigo: produto.codigo,
                descricao: produto.descricao,
                unidade: produto.unidade,
                grupo: produto.grupo || 'Sem Grupo',
                familia: produto.familia || 'Sem Família',
                tipo: produto.tipo,
                leadTime: produto.leadTime || 0,
                pontoPedido: produto.pontoPedido || 0,
                estoqueMinimo: produto.estoqueMinimo || 0,
                loteCompra: produto.loteCompra || 0,
                precoUnitario: produto.precoUnitario || 0
              },
              quantidade: 0,
              ordens: new Set(),
              consumoPorOP: new Map()
            });
          }

          const necessidadeExistente = necessidadesMap.get(chaveNecessidade);
          necessidadeExistente.quantidade += quantidadeNecessaria;
          necessidadeExistente.ordens.add(ordem.numero);

          // Somar ao consumo existente para esta OP
          const consumoAtual = necessidadeExistente.consumoPorOP.get(ordem.numero) || 0;
          necessidadeExistente.consumoPorOP.set(ordem.numero, consumoAtual + quantidadeNecessaria);

          console.log(`➕ MP direto: ${produto.codigo} - Qtd: ${quantidadeNecessaria}`);
        }
      }

      // Agora agrupar as necessidades por produto pai
      console.log('🎯 Agrupando necessidades por produto pai...');

      for (const [chaveNecessidade, dados] of necessidadesMap) {
        const produtoPaiId = dados.produtoPaiId;

        // Pular necessidades diretas para este relatório (ou incluir se quiser)
        if (produtoPaiId === 'DIRETO') {
          continue; // Pular DIRETO neste relatório focado em produtos pai
        }

        // Verificar se já existe entrada para este produto pai
        if (!necessidadesPorProdutoPai.has(produtoPaiId)) {
          const produtoPai = produtos.find(p => p.id === produtoPaiId);
          if (produtoPai) {
            necessidadesPorProdutoPai.set(produtoPaiId, {
              produtoPai: produtoPai,
              necessidades: [],
              totalOPs: 0
            });
          }
        }

        // Adicionar esta necessidade ao produto pai
        const grupoProdutoPai = necessidadesPorProdutoPai.get(produtoPaiId);
        if (grupoProdutoPai) {
          grupoProdutoPai.necessidades.push({
            produto: dados.produto,
            quantidade: dados.quantidade,
            ordens: dados.ordens,
            consumoPorOP: dados.consumoPorOP
          });
        }
      }

      console.log(`🎯 Total de produtos pai com necessidades: ${necessidadesPorProdutoPai.size}`);

      // Exibir resultados
      exibirNecessidadesPorProdutoPai();
    }

    // Função para carregar estruturas de produtos
    async function carregarEstruturas() {
      try {
        return estruturas;
      } catch (error) {
        console.error('Erro ao carregar estruturas:', error);
        return [];
      }
    }

    // Função para explodir estrutura recursivamente e calcular necessidades de MP
    async function explodirEstrutura(produtoId, quantidade, estruturas, nivel = 0, visited = new Set()) {
      if (nivel > 10) {
        console.warn('Estrutura muito profunda, possível ciclo detectado');
        return [];
      }

      if (visited.has(produtoId)) {
        console.warn('Ciclo detectado na estrutura:', produtoId);
        return [];
      }

      visited.add(produtoId);

      const produto = produtos.find(p => p.id === produtoId);
      if (!produto) {
        console.warn('Produto não encontrado:', produtoId);
        return [];
      }

      const estruturasProduto = estruturas.filter(e => e.produtoId === produtoId);
      if (estruturasProduto.length === 0) {
        console.log(`📋 Produto ${produto.codigo} não tem estrutura`);
        return [];
      }

      const necessidadesMP = [];

      for (const componente of estruturasProduto) {
        const componenteProduto = produtos.find(p => p.id === componente.componenteId);
        if (!componenteProduto) {
          console.warn('Componente não encontrado:', componente.componenteId);
          continue;
        }

        const quantidadeComponente = quantidade * (Number(componente.quantidade) || 0);

        if (componenteProduto.tipo === 'MP') {
          // É MP, adicionar à lista de necessidades
          necessidadesMP.push({
            produto: componenteProduto,
            quantidade: quantidadeComponente
          });
        } else if (componenteProduto.tipo === 'SP' || componenteProduto.tipo === 'PA') {
          // É SP/PA, explode recursivamente
          const subNecessidades = await explodirEstrutura(
            componente.componenteId,
            quantidadeComponente,
            estruturas,
            nivel + 1,
            new Set(visited)
          );
          necessidadesMP.push(...subNecessidades);
        }
      }

      return necessidadesMP;
    }

    // Função para exibir necessidades por produto pai
    function exibirNecessidadesPorProdutoPai() {
      const container = document.getElementById('produtosPaiContainer');
      const loadingContainer = document.getElementById('loadingContainer');
      const noDataContainer = document.getElementById('noDataContainer');

      // Esconder loading
      loadingContainer.style.display = 'none';

      if (necessidadesPorProdutoPai.size === 0) {
        noDataContainer.style.display = 'block';
        container.style.display = 'none';
        return;
      }

      // Mostrar container
      noDataContainer.style.display = 'none';
      container.style.display = 'block';

      // Gerar HTML para cada produto pai
      container.innerHTML = '';

      for (const [produtoId, dados] of necessidadesPorProdutoPai) {
        const secaoProdutoPai = criarSecaoProdutoPai(dados);
        container.appendChild(secaoProdutoPai);
      }

      // Atualizar estatísticas
      atualizarEstatisticas();
    }

    // Função para criar seção de produto pai
    function criarSecaoProdutoPai(dados) {
      const { produtoPai, opPai, necessidades, totalOPs } = dados;

      // Calcular estatísticas
      const totalMateriais = necessidades.length;
      const materiaisDeficit = necessidades.filter(n => {
        const estoque = estoques.find(e => e.produtoId === n.produto.id);
        const saldoDisponivel = calcularSaldoDisponivel(estoque);
        return n.quantidade > saldoDisponivel;
      }).length;

      const valorTotal = necessidades.reduce((total, n) => {
        const preco = Number(n.produto.precoUnitario) || 0;
        return total + (n.quantidade * preco);
      }, 0);

      // Criar elemento da seção
      const secao = document.createElement('div');
      secao.className = 'produto-pai-section';
      secao.id = `produto-pai-${produtoPai.id}`;

      secao.innerHTML = `
        <div class="produto-pai-header" onclick="toggleProdutoPai('${produtoPai.id}')">
          <div class="produto-pai-info">
            <div>
              <strong style="font-size: 18px;">🏭 ${produtoPai.codigo}</strong>
              <div style="font-size: 14px; opacity: 0.9;">${produtoPai.descricao}</div>
              <div style="font-size: 12px; opacity: 0.8; margin-top: 4px;">
                <i class="fas fa-clipboard-list"></i> OP Principal: ${opPai.numero} | Qtd: ${(opPai.quantidade || 0).toLocaleString('pt-BR')} ${produtoPai.unidade || 'UN'}
              </div>
            </div>
            <div class="produto-pai-badge" style="background: #28a745;">🛒 Lista de Compras</div>
          </div>
          <div class="produto-pai-stats">
            <div><i class="fas fa-shopping-cart"></i> ${totalMateriais} itens para comprar</div>
            <div><i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i> ${materiaisDeficit} em falta</div>
            <div><i class="fas fa-dollar-sign" style="color: #28a745;"></i> R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
            <div><i class="fas fa-industry"></i> ${totalOPs} OPs relacionadas</div>
            <i class="fas fa-chevron-down expand-icon" id="icon-${produtoPai.id}"></i>
          </div>
        </div>
        <div class="produto-pai-content" id="content-${produtoPai.id}">
          ${criarTabelaNecessidades(necessidades)}
        </div>
      `;

      return secao;
    }

    // Função para criar resumo de compras
    function criarResumoCompras(necessidades) {
      let totalItensParaComprar = 0;
      let valorTotalParaComprar = 0;
      let itensParaComprar = [];

      necessidades.forEach(necessidade => {
        const estoque = estoques.find(e => e.produtoId === necessidade.produto.id);
        const saldoDisponivel = calcularSaldoDisponivel(estoque);
        const deficit = Math.max(0, necessidade.quantidade - saldoDisponivel);

        if (deficit > 0) {
          totalItensParaComprar++;
          const precoUnitario = Number(necessidade.produto.precoUnitario) || 0;
          const valorParaComprar = deficit * precoUnitario;
          valorTotalParaComprar += valorParaComprar;

          itensParaComprar.push({
            codigo: necessidade.produto.codigo,
            quantidade: deficit,
            unidade: necessidade.produto.unidade || 'UN',
            valor: valorParaComprar
          });
        }
      });

      if (totalItensParaComprar === 0) {
        return `
          <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin-top: 15px; border-radius: 6px;">
            <h5 style="margin: 0; color: #155724;">
              <i class="fas fa-check-circle"></i> ✅ Nenhuma compra necessária!
            </h5>
            <p style="margin: 5px 0 0 0; color: #155724;">
              Todos os materiais estão disponíveis em estoque para esta OP Pai.
            </p>
          </div>
        `;
      }

      const listaItens = itensParaComprar.map(item =>
        `<li><strong>${item.codigo}</strong>: ${item.quantidade.toLocaleString('pt-BR')} ${item.unidade} - R$ ${item.valor.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</li>`
      ).join('');

      return `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-top: 15px; border-radius: 6px;">
          <h5 style="margin: 0 0 10px 0; color: #856404;">
            <i class="fas fa-shopping-cart"></i> 🛒 Resumo de Compras para esta OP Pai
          </h5>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 10px;">
            <div>
              <strong style="color: #856404;">📦 Itens para comprar:</strong> ${totalItensParaComprar}
            </div>
            <div>
              <strong style="color: #856404;">💰 Valor total:</strong> R$ ${valorTotalParaComprar.toLocaleString('pt-BR', {minimumFractionDigits: 2})}
            </div>
          </div>
          <details style="margin-top: 10px;">
            <summary style="cursor: pointer; color: #856404; font-weight: bold;">
              <i class="fas fa-list"></i> Ver lista detalhada de compras
            </summary>
            <ul style="margin: 10px 0 0 20px; color: #856404;">
              ${listaItens}
            </ul>
          </details>
        </div>
      `;
    }

    // Função para criar tabela de necessidades
    function criarTabelaNecessidades(necessidades) {
      if (necessidades.length === 0) {
        return '<div style="padding: 20px; text-align: center; color: #666;">Nenhuma necessidade de MP encontrada</div>';
      }

      const linhas = necessidades.map(necessidade => {
        const estoque = estoques.find(e => e.produtoId === necessidade.produto.id);
        const saldoTotal = estoque ? (Number(estoque.saldo) || 0) : 0;
        const saldoDisponivel = calcularSaldoDisponivel(estoque);
        const deficit = Math.max(0, necessidade.quantidade - saldoDisponivel);
        const criticidade = calcularCriticidade(necessidade.quantidade, saldoDisponivel, necessidade.produto.leadTime);

        let situacaoBadge = '';
        if (deficit > 0) {
          situacaoBadge = `<span class="deficit-badge">Déficit: ${deficit.toLocaleString('pt-BR')}</span>`;
        } else if (saldoDisponivel > necessidade.quantidade) {
          const superavit = saldoDisponivel - necessidade.quantidade;
          situacaoBadge = `<span class="superavit-badge">Superávit: ${superavit.toLocaleString('pt-BR')}</span>`;
        } else {
          situacaoBadge = `<span class="neutro-badge">Neutro</span>`;
        }

        const ordensTexto = Array.from(necessidade.ordens).join(', ');
        const precoUnitario = Number(necessidade.produto.precoUnitario) || 0;
        const valorTotal = necessidade.quantidade * precoUnitario;

        // Calcular quanto realmente precisa comprar
        const quantidadeParaComprar = Math.max(0, deficit);
        const valorParaComprar = quantidadeParaComprar * precoUnitario;

        // Ícone baseado na situação
        let iconeStatus = '';
        if (deficit > 0) {
          iconeStatus = '🛒'; // Precisa comprar
        } else if (saldoDisponivel > necessidade.quantidade) {
          iconeStatus = '✅'; // Tem estoque suficiente
        } else {
          iconeStatus = '⚖️'; // Estoque exato
        }

        return `
          <tr style="${deficit > 0 ? 'background-color: #fff5f5;' : ''}">
            <td>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 18px;">${iconeStatus}</span>
                <div>
                  <div><strong style="color: #0854a0;">${necessidade.produto.codigo}</strong></div>
                  <div style="font-size: 12px; color: #666;">${necessidade.produto.descricao}</div>
                  ${deficit > 0 ? `<div style="font-size: 11px; color: #dc3545; font-weight: bold;">⚠️ COMPRAR: ${quantidadeParaComprar.toLocaleString('pt-BR')} ${necessidade.produto.unidade || 'UN'}</div>` : ''}
                </div>
              </div>
            </td>
            <td style="text-align: right;">
              <strong style="color: #0854a0;">${necessidade.quantidade.toLocaleString('pt-BR')} ${necessidade.produto.unidade || 'UN'}</strong>
            </td>
            <td style="text-align: right;">
              ${saldoTotal.toLocaleString('pt-BR')} ${necessidade.produto.unidade || 'UN'}
            </td>
            <td style="text-align: right;">
              <span style="color: ${saldoDisponivel >= necessidade.quantidade ? '#28a745' : '#dc3545'};">
                ${saldoDisponivel.toLocaleString('pt-BR')} ${necessidade.produto.unidade || 'UN'}
              </span>
            </td>
            <td style="text-align: center;">
              ${situacaoBadge}
            </td>
            <td style="text-align: center;">
              <span class="criticidade-${criticidade.toLowerCase()}">${criticidade}</span>
            </td>
            <td style="text-align: right;">
              R$ ${precoUnitario.toLocaleString('pt-BR', {minimumFractionDigits: 2})}
            </td>
            <td style="text-align: right;">
              <div><strong>R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</strong></div>
              ${deficit > 0 ? `<div style="font-size: 11px; color: #dc3545;">Comprar: R$ ${valorParaComprar.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>` : ''}
            </td>
          </tr>
        `;
      }).join('');

      return `
        <div style="background: #f8f9fa; padding: 15px; margin-bottom: 10px; border-radius: 6px; border-left: 4px solid #28a745;">
          <h4 style="margin: 0 0 10px 0; color: #28a745;">
            <i class="fas fa-shopping-cart"></i> Lista de Compras - O que comprar para esta OP Pai
          </h4>
          <p style="margin: 0; color: #666; font-size: 14px;">
            Materiais necessários exclusivamente para produzir <strong>${necessidades.length}</strong> tipos de MP para este produto pai.
          </p>
        </div>
        <table class="necessidades-table">
          <thead>
            <tr>
              <th style="width: 25%;">🛒 Material para Comprar</th>
              <th style="width: 12%;">📦 Quantidade Necessária</th>
              <th style="width: 10%;">📊 Estoque Atual</th>
              <th style="width: 10%;">✅ Disponível</th>
              <th style="width: 12%;">🚨 Situação</th>
              <th style="width: 8%;">⚠️ Criticidade</th>
              <th style="width: 10%;">💰 Preço Unit.</th>
              <th style="width: 13%;">💵 Valor Total</th>
            </tr>
          </thead>
          <tbody>
            ${linhas}
          </tbody>
        </table>
        ${criarResumoCompras(necessidades)}
      `;
    }

    // Função para toggle da seção do produto pai
    window.toggleProdutoPai = function(produtoId) {
      const content = document.getElementById(`content-${produtoId}`);
      const icon = document.getElementById(`icon-${produtoId}`);

      if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.classList.remove('rotated');
      } else {
        content.classList.add('expanded');
        icon.classList.add('rotated');
      }
    };

    // Função para expandir todos
    window.expandirTodos = function() {
      const contents = document.querySelectorAll('.produto-pai-content');
      const icons = document.querySelectorAll('.expand-icon');

      contents.forEach(content => content.classList.add('expanded'));
      icons.forEach(icon => icon.classList.add('rotated'));

      showNotification('Todas as seções foram expandidas', 'success');
    };

    // Função para atualizar estatísticas
    function atualizarEstatisticas() {
      const totalProdutosPai = necessidadesPorProdutoPai.size;
      let totalMateriais = 0;
      let materiaisDeficit = 0;
      let valorTotal = 0;

      for (const [produtoId, dados] of necessidadesPorProdutoPai) {
        totalMateriais += dados.necessidades.length;

        dados.necessidades.forEach(necessidade => {
          const estoque = estoques.find(e => e.produtoId === necessidade.produto.id);
          const saldoDisponivel = calcularSaldoDisponivel(estoque);

          if (necessidade.quantidade > saldoDisponivel) {
            materiaisDeficit++;
          }

          const preco = Number(necessidade.produto.precoUnitario) || 0;
          valorTotal += necessidade.quantidade * preco;
        });
      }

      document.getElementById('totalProdutosPai').textContent = totalProdutosPai;
      document.getElementById('totalMateriais').textContent = totalMateriais;
      document.getElementById('materiaisDeficit').textContent = materiaisDeficit;
      document.getElementById('valorTotal').textContent = `R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
    }

    // Função para aplicar filtros
    window.aplicarFiltros = function() {
      // Implementar filtros se necessário
      showNotification('Filtros aplicados', 'success');
    };

    // Função para limpar filtros
    window.limparFiltros = function() {
      document.getElementById('filtroProdutoPai').value = '';
      document.getElementById('filtroGrupo').value = '';
      document.getElementById('filtroFamilia').value = '';
      document.getElementById('filtroCriticidade').value = '';
      document.getElementById('filtroSituacao').value = '';
      document.getElementById('filtroBusca').value = '';

      showNotification('Filtros limpos', 'info');
    };

    // Função para recarregar dados
    window.recarregarDados = function() {
      showNotification('Recarregando dados...', 'info');
      carregarDados();
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 Iniciando Relatório de Necessidades por Produto Pai...');
      carregarDados();
    });

  </script>

</body>
</html>
