<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de OPs Pai</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --secondary-color: #6c757d;
      --secondary-hover: #5a6268;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1400px;
      margin: 30px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-secondary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-secondary:hover {
      background-color: var(--secondary-hover);
    }

    .filters-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #e9ecef;
    }

    .filters-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      font-weight: 600;
      margin-bottom: 5px;
      color: var(--text-color);
    }

    .filter-group input,
    .filter-group select {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .filter-group input:focus,
    .filter-group select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    .filters-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      padding-top: 15px;
      border-top: 1px solid #e9ecef;
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .stat-card.success {
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }

    .stat-card.warning {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .stat-card.info {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .stat-value {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }

    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table-header {
      background: var(--primary-color);
      color: white;
      padding: 15px 20px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-responsive {
      overflow-x: auto;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    th {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      padding: 12px 8px;
      text-align: left;
      font-weight: 600;
      font-size: 13px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #fff;
    }

    td {
      padding: 12px 8px;
      border-bottom: 1px solid #e9ecef;
      vertical-align: middle;
    }

    tbody tr:hover {
      background-color: #f8f9fa;
    }

    tbody tr:last-child td {
      border-bottom: none;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-planejada {
      background: #e3f2fd;
      color: #1976d2;
    }

    .status-liberada {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .status-producao {
      background: #fff3e0;
      color: #f57c00;
    }

    .status-finalizada {
      background: #e8f5e8;
      color: #388e3c;
    }

    .status-cancelada {
      background: #ffebee;
      color: #d32f2f;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-data {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
    }

    .no-data i {
      font-size: 48px;
      margin-bottom: 15px;
      opacity: 0.5;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 3px;
    }

    .btn-view {
      background-color: #17a2b8;
      color: white;
    }

    .btn-view:hover {
      background-color: #138496;
    }

    .btn-edit {
      background-color: #ffc107;
      color: #212529;
    }

    .btn-edit:hover {
      background-color: #e0a800;
    }

    .priority-alta {
      color: #dc3545;
      font-weight: bold;
    }

    .priority-media {
      color: #ffc107;
      font-weight: bold;
    }

    .priority-baixa {
      color: #28a745;
      font-weight: bold;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 5px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    }

    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }

    .notification.success {
      background-color: #28a745;
    }

    .notification.error {
      background-color: #dc3545;
    }

    .notification.info {
      background-color: #17a2b8;
    }

    @media (max-width: 768px) {
      .container {
        width: 98%;
        margin: 10px auto;
        padding: 15px;
      }

      .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
      }

      .filters-grid {
        grid-template-columns: 1fr;
      }

      .stats-section {
        grid-template-columns: repeat(2, 1fr);
      }

      .filters-actions {
        justify-content: center;
      }

      table {
        font-size: 12px;
      }

      th, td {
        padding: 8px 4px;
      }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-industry"></i> Relatório de OPs Pai</h1>
      <div class="header-actions">
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-home"></i> Voltar
        </button>
        <button class="btn btn-primary" onclick="recarregarDados()">
          <i class="fas fa-sync"></i> Atualizar
        </button>
      </div>
    </div>

    <!-- Seção de Filtros -->
    <div class="filters-section">
      <div class="filters-title">
        <i class="fas fa-filter"></i> Filtros de Pesquisa
      </div>

      <div class="filters-grid">
        <div class="filter-group">
          <label for="filtroStatus">Status da OP:</label>
          <select id="filtroStatus">
            <option value="">Todos os Status</option>
            <option value="PLANEJADA">Planejada</option>
            <option value="LIBERADA">Liberada</option>
            <option value="EM_PRODUCAO">Em Produção</option>
            <option value="FINALIZADA">Finalizada</option>
            <option value="CANCELADA">Cancelada</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroPrioridade">Prioridade:</label>
          <select id="filtroPrioridade">
            <option value="">Todas as Prioridades</option>
            <option value="ALTA">Alta</option>
            <option value="MEDIA">Média</option>
            <option value="BAIXA">Baixa</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filtroDataInicio">Data Início (De):</label>
          <input type="date" id="filtroDataInicio">
        </div>

        <div class="filter-group">
          <label for="filtroDataFim">Data Início (Até):</label>
          <input type="date" id="filtroDataFim">
        </div>

        <div class="filter-group">
          <label for="filtroNumero">Número da OP:</label>
          <input type="text" id="filtroNumero" placeholder="Digite o número da OP...">
        </div>

        <div class="filter-group">
          <label for="filtroProduto">Produto:</label>
          <input type="text" id="filtroProduto" placeholder="Código ou descrição do produto...">
        </div>
      </div>

      <div class="filters-actions">
        <button class="btn btn-secondary" onclick="limparFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button class="btn btn-primary" onclick="aplicarFiltros()">
          <i class="fas fa-search"></i> Aplicar Filtros
        </button>
      </div>
    </div>

    <!-- Seção de Estatísticas -->
    <div class="stats-section" id="statsSection">
      <div class="stat-card">
        <div class="stat-value" id="totalOPs">-</div>
        <div class="stat-label">Total de OPs Pai</div>
      </div>
      <div class="stat-card success">
        <div class="stat-value" id="opsFinalizadas">-</div>
        <div class="stat-label">Finalizadas</div>
      </div>
      <div class="stat-card warning">
        <div class="stat-value" id="opsProducao">-</div>
        <div class="stat-label">Em Produção</div>
      </div>
      <div class="stat-card info">
        <div class="stat-value" id="opsPlanejadas">-</div>
        <div class="stat-label">Planejadas</div>
      </div>
    </div>

    <!-- Tabela de OPs Pai -->
    <div class="table-container">
      <div class="table-header">
        <span><i class="fas fa-table"></i> OPs Pai Cadastradas</span>
        <span id="contadorOPs">Carregando...</span>
      </div>

      <div class="table-responsive">
        <div id="loadingContainer" class="loading">
          <div class="loading-spinner"></div>
          <p>Carregando OPs Pai...</p>
        </div>

        <div id="noDataContainer" class="no-data" style="display: none;">
          <i class="fas fa-inbox"></i>
          <h3>Nenhuma OP Pai encontrada</h3>
          <p>Não há OPs Pai (OPs principais/raiz) cadastradas no sistema ou que atendam aos filtros aplicados.</p>
        </div>

        <table id="tabelaOPs" style="display: none;">
          <thead>
            <tr>
              <th>Número</th>
              <th>Produto Pai</th>
              <th>Descrição</th>
              <th>Quantidade</th>
              <th>Status</th>
              <th>Prioridade</th>
              <th>Data Início</th>
              <th>Data Prevista</th>
              <th>Progresso</th>
              <th>Total Filhas</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="corpoTabela">
          </tbody>
        </table>
      </div>
    </div>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>
  </div>

  <script type="module">
    // Importações do Firebase
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
    import {
      getFirestore,
      collection,
      getDocs,
      query,
      where,
      orderBy,
      onSnapshot
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // Usar configuração centralizada do Firebase
    import { db } from './firebase-config.js';

    // Variáveis globais
    let ordensProducao = [];
    let produtos = [];
    let opsPaiFiltradas = [];

    // Função para mostrar notificação
    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = `notification ${type}`;
      notification.classList.add('show');

      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // Função para carregar dados
    async function carregarDados() {
      try {
        console.log('🔄 Carregando dados...');

        // Mostrar loading
        document.getElementById('loadingContainer').style.display = 'block';
        document.getElementById('noDataContainer').style.display = 'none';
        document.getElementById('tabelaOPs').style.display = 'none';

        // Carregar ordens de produção
        const ordensSnapshot = await getDocs(
          query(collection(db, "ordensProducao"), orderBy("numero", "desc"))
        );
        ordensProducao = ordensSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Carregar produtos
        const produtosSnapshot = await getDocs(collection(db, "produtos"));
        produtos = produtosSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📊 Carregadas ${ordensProducao.length} ordens de produção`);
        console.log(`📦 Carregados ${produtos.length} produtos`);

        // Processar OPs Pai
        processarOPsPai();

      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        showNotification('Erro ao carregar dados: ' + error.message, 'error');

        // Esconder loading e mostrar erro
        document.getElementById('loadingContainer').style.display = 'none';
        document.getElementById('noDataContainer').style.display = 'block';
      }
    }

    // Função para processar OPs Pai
    function processarOPsPai() {
      console.log('🏭 Processando OPs Pai...');

      // Filtrar apenas OPs Pai RAIZ - aquelas que você digita o código do produto
      // para abrir a explosão dos filhos (primeira OP gerada de uma sequência)
      const opsPai = ordensProducao.filter(op => {
        // OP Pai é aquela que não tem produtoPaiId ou tem produtoPaiId igual ao próprio produtoId
        // Ou seja, é a primeira OP criada quando você digita o código do produto
        return !op.produtoPaiId || op.produtoPaiId === op.produtoId || op.nivel === 0;
      });

      console.log(`🎯 Encontradas ${opsPai.length} OPs Pai (OPs raiz/principais)`);

      // Enriquecer dados das OPs Pai
      opsPaiFiltradas = opsPai.map(op => {
        const produto = produtos.find(p => p.id === op.produtoId);

        // Contar TODAS as OPs filhas que derivam desta OP pai
        // Incluindo todos os níveis da explosão gerada por esta OP principal
        const opsFilhas = ordensProducao.filter(opFilha =>
          opFilha.id !== op.id && (
            opFilha.produtoPaiId === op.id ||
            opFilha.produtoPaiId === op.produtoId ||
            (opFilha.opPaiOriginal === op.id) || // Se houver campo de OP pai original
            (opFilha.opRaiz === op.id) // Se houver campo de OP raiz
          )
        );

        // Calcular progresso baseado no status
        let progresso = 0;
        switch(op.status) {
          case 'PLANEJADA': progresso = 10; break;
          case 'LIBERADA': progresso = 25; break;
          case 'EM_PRODUCAO': progresso = 60; break;
          case 'FINALIZADA': progresso = 100; break;
          case 'CANCELADA': progresso = 0; break;
          default: progresso = 0;
        }

        return {
          ...op,
          produto: produto || { codigo: 'N/A', descricao: 'Produto não encontrado' },
          opsFilhas: opsFilhas.length,
          opsFilhasDetalhes: opsFilhas,
          progresso: progresso
        };
      });

      // Aplicar filtros e exibir
      aplicarFiltros();
    }

    // Função para aplicar filtros
    window.aplicarFiltros = function() {
      const filtroStatus = document.getElementById('filtroStatus').value;
      const filtroPrioridade = document.getElementById('filtroPrioridade').value;
      const filtroDataInicio = document.getElementById('filtroDataInicio').value;
      const filtroDataFim = document.getElementById('filtroDataFim').value;
      const filtroNumero = document.getElementById('filtroNumero').value.toLowerCase();
      const filtroProduto = document.getElementById('filtroProduto').value.toLowerCase();

      let opsFiltradas = [...opsPaiFiltradas];

      // Aplicar filtros
      if (filtroStatus) {
        opsFiltradas = opsFiltradas.filter(op => op.status === filtroStatus);
      }

      if (filtroPrioridade) {
        opsFiltradas = opsFiltradas.filter(op => op.prioridade === filtroPrioridade);
      }

      if (filtroDataInicio) {
        const dataInicio = new Date(filtroDataInicio);
        opsFiltradas = opsFiltradas.filter(op => {
          const dataOP = op.dataInicio?.toDate?.() || new Date(op.dataInicio);
          return dataOP >= dataInicio;
        });
      }

      if (filtroDataFim) {
        const dataFim = new Date(filtroDataFim);
        opsFiltradas = opsFiltradas.filter(op => {
          const dataOP = op.dataInicio?.toDate?.() || new Date(op.dataInicio);
          return dataOP <= dataFim;
        });
      }

      if (filtroNumero) {
        opsFiltradas = opsFiltradas.filter(op =>
          op.numero.toLowerCase().includes(filtroNumero)
        );
      }

      if (filtroProduto) {
        opsFiltradas = opsFiltradas.filter(op =>
          op.produto.codigo.toLowerCase().includes(filtroProduto) ||
          op.produto.descricao.toLowerCase().includes(filtroProduto)
        );
      }

      // Exibir resultados
      exibirOPs(opsFiltradas);
      atualizarEstatisticas(opsFiltradas);
    };

    // Função para exibir OPs na tabela
    function exibirOPs(ops) {
      const corpoTabela = document.getElementById('corpoTabela');
      const loadingContainer = document.getElementById('loadingContainer');
      const noDataContainer = document.getElementById('noDataContainer');
      const tabelaOPs = document.getElementById('tabelaOPs');
      const contadorOPs = document.getElementById('contadorOPs');

      // Esconder loading
      loadingContainer.style.display = 'none';

      if (ops.length === 0) {
        // Mostrar mensagem de sem dados
        noDataContainer.style.display = 'block';
        tabelaOPs.style.display = 'none';
        contadorOPs.textContent = '0 OPs encontradas';
        return;
      }

      // Mostrar tabela
      noDataContainer.style.display = 'none';
      tabelaOPs.style.display = 'table';
      contadorOPs.textContent = `${ops.length} OP${ops.length !== 1 ? 's' : ''} encontrada${ops.length !== 1 ? 's' : ''}`;

      // Gerar linhas da tabela
      corpoTabela.innerHTML = ops.map(op => {
        const dataInicio = op.dataInicio?.toDate?.()?.toLocaleDateString('pt-BR') ||
                          (op.dataInicio ? new Date(op.dataInicio).toLocaleDateString('pt-BR') : 'N/A');

        const dataPrevista = op.dataPrevista?.toDate?.()?.toLocaleDateString('pt-BR') ||
                            (op.dataPrevista ? new Date(op.dataPrevista).toLocaleDateString('pt-BR') : 'N/A');

        const statusClass = `status-${op.status?.toLowerCase() || 'planejada'}`;
        const priorityClass = `priority-${op.prioridade?.toLowerCase() || 'media'}`;

        return `
          <tr>
            <td><strong>${op.numero}</strong></td>
            <td>
              <div><strong style="color: #0854a0;">${op.produto.codigo}</strong></div>
              <div style="font-size: 12px; color: #666;">${op.produto.descricao}</div>
            </td>
            <td>${op.produto.descricao}</td>
            <td style="text-align: right;">
              <strong>${(op.quantidade || 0).toLocaleString('pt-BR')} ${op.produto.unidade || 'UN'}</strong>
            </td>
            <td>
              <span class="status-badge ${statusClass}">
                ${op.status || 'PLANEJADA'}
              </span>
            </td>
            <td>
              <span class="${priorityClass}">
                ${op.prioridade || 'MÉDIA'}
              </span>
            </td>
            <td>${dataInicio}</td>
            <td>${dataPrevista}</td>
            <td>
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="flex: 1; background: #e9ecef; border-radius: 10px; height: 8px; overflow: hidden;">
                  <div style="width: ${op.progresso}%; height: 100%; background: ${getProgressColor(op.progresso)}; transition: width 0.3s ease;"></div>
                </div>
                <span style="font-size: 12px; font-weight: bold;">${op.progresso}%</span>
              </div>
            </td>
            <td style="text-align: center;">
              <span style="background: #e8f5e8; color: #388e3c; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                ${op.opsFilhas} ${op.opsFilhas === 1 ? 'filha' : 'filhas'}
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="btn btn-view btn-small" onclick="visualizarOP('${op.id}')" title="Visualizar OP">
                  <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-edit btn-small" onclick="editarOP('${op.id}')" title="Editar OP">
                  <i class="fas fa-edit"></i>
                </button>
              </div>
            </td>
          </tr>
        `;
      }).join('');
    }

    // Função para obter cor do progresso
    function getProgressColor(progresso) {
      if (progresso >= 80) return '#28a745';
      if (progresso >= 50) return '#ffc107';
      if (progresso >= 25) return '#fd7e14';
      return '#dc3545';
    }

    // Função para atualizar estatísticas
    function atualizarEstatisticas(ops) {
      const totalOPs = ops.length;
      const opsFinalizadas = ops.filter(op => op.status === 'FINALIZADA').length;
      const opsProducao = ops.filter(op => op.status === 'EM_PRODUCAO').length;
      const opsPlanejadas = ops.filter(op => op.status === 'PLANEJADA' || op.status === 'LIBERADA').length;

      document.getElementById('totalOPs').textContent = totalOPs;
      document.getElementById('opsFinalizadas').textContent = opsFinalizadas;
      document.getElementById('opsProducao').textContent = opsProducao;
      document.getElementById('opsPlanejadas').textContent = opsPlanejadas;
    }

    // Função para limpar filtros
    window.limparFiltros = function() {
      document.getElementById('filtroStatus').value = '';
      document.getElementById('filtroPrioridade').value = '';
      document.getElementById('filtroDataInicio').value = '';
      document.getElementById('filtroDataFim').value = '';
      document.getElementById('filtroNumero').value = '';
      document.getElementById('filtroProduto').value = '';

      aplicarFiltros();
      showNotification('Filtros limpos com sucesso!', 'info');
    };

    // Função para recarregar dados
    window.recarregarDados = function() {
      showNotification('Recarregando dados...', 'info');
      carregarDados();
    };

    // Função para visualizar OP
    window.visualizarOP = function(opId) {
      const op = opsPaiFiltradas.find(o => o.id === opId);
      if (op) {
        // Aqui você pode implementar um modal ou redirecionar para página de detalhes
        alert(`Visualizar OP: ${op.numero}\nProduto: ${op.produto.codigo}\nStatus: ${op.status}`);
      }
    };

    // Função para editar OP
    window.editarOP = function(opId) {
      const op = opsPaiFiltradas.find(o => o.id === opId);
      if (op) {
        // Aqui você pode implementar edição ou redirecionar para página de edição
        alert(`Editar OP: ${op.numero}\nProduto: ${op.produto.codigo}`);
      }
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 Iniciando Relatório de OPs Pai...');
      carregarDados();
    });

  </script>

</body>
</html>
