<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importar CFOPs - Sistema ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .main-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.importados {
            border-left-color: #27ae60;
        }

        .stat-card.novos {
            border-left-color: #3498db;
        }

        .stat-card.atualizados {
            border-left-color: #f39c12;
        }

        .stat-card.erros {
            border-left-color: #e74c3c;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .import-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .progress-container {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            height: 25px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .log-success {
            color: #2ecc71;
        }

        .log-error {
            color: #e74c3c;
        }

        .log-warning {
            color: #f39c12;
        }

        .log-info {
            color: #3498db;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            opacity: 1;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">HM-SYSTEMS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

<div class="container">
        <div class="header">
            <h1><i class="fas fa-download"></i> Importar CFOPs Padrão</h1>
            <div class="header-actions">
                <a href="gerenciar-cfops.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar para Gerenciar
                </a>
                <a href="index.html" class="btn btn-danger">
                    <i class="fas fa-home"></i> Início
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card importados">
                    <div class="stat-number" id="statImportados">0</div>
                    <div class="stat-label">📥 Importados</div>
                </div>
                <div class="stat-card novos">
                    <div class="stat-number" id="statNovos">0</div>
                    <div class="stat-label">✨ Novos</div>
                </div>
                <div class="stat-card atualizados">
                    <div class="stat-number" id="statAtualizados">0</div>
                    <div class="stat-label">🔄 Atualizados</div>
                </div>
                <div class="stat-card erros">
                    <div class="stat-number" id="statErros">0</div>
                    <div class="stat-label">❌ Erros</div>
                </div>
            </div>

<!-- Seção de Importação -->
            <div class="import-section">
                <h2><i class="fas fa-info-circle"></i> Sobre a Importação</h2>
                <p>Esta funcionalidade irá importar os CFOPs padrão utilizados no Brasil conforme a legislação fiscal vigente.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Informações Importantes:</strong>
                    <ul style="margin: 10px 0 0 20px;">
                        <li>Mais de 500 CFOPs padrão serão importados</li>
                        <li>CFOPs existentes serão atualizados automaticamente</li>
                        <li>Configurações personalizadas serão preservadas</li>
                        <li>O processo pode levar alguns minutos</li>
                    </ul>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <button id="importButton" class="btn btn-success" onclick="iniciarImportacao()" style="font-size: 16px; padding: 15px 30px;">
                        <i class="fas fa-download"></i> Iniciar Importação
                    </button>
                </div>
            </div>

    <div class="info-box">
      <h3><i class="fas fa-info-circle"></i> Sobre esta funcionalidade</h3>
      <p>Esta tela permite importar os CFOPs mais comuns utilizados em operações fiscais. 
      Os CFOPs serão adicionados apenas se ainda não existirem no sistema.</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalCfops">0</div>
        <div class="stat-label">CFOPs Cadastrados</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="cfopsEntrada">0</div>
        <div class="stat-label">CFOPs de Entrada</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="cfopsSaida">0</div>
        <div class="stat-label">CFOPs de Saída</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="cfopsAtivos">0</div>
        <div class="stat-label">CFOPs Ativos</div>
      </div>
    </div>

    <!-- Ações -->
    <div style="text-align: center; margin: 20px 0;">
      <button class="btn-totvs-primary" onclick="preencherCFOPs()">
        <i class="fas fa-download"></i> Importar CFOPs Padrão
      </button>
      <button class="btn-totvs-secondary" onclick="atualizarEstatisticas()">
        <i class="fas fa-sync"></i> Atualizar Estatísticas
      </button>
    </div>

    <!-- Tabela de CFOPs -->
    <table class="totvs-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Tipo</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody id="cfopTableBody"></tbody>
    </table>

    <div style="margin-top: 20px; text-align: center;">
      <button class="btn-totvs-secondary" onclick="window.location.href='gerenciar-cfops.html'">
        <i class="fas fa-cogs"></i> Gerenciar CFOPs
      </button>
      <button class="btn-totvs-secondary" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-left"></i> Voltar ao Menu
      </button>
      <button class="btn-totvs-danger" id="logoutButton" style="display: none;" onclick="logout()">
        <i class="fas fa-sign-out-alt"></i> Sair
      </button>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      onSnapshot
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let cfops = [];
    let usuarioAtual = null;

    // Lista completa de CFOPs padrão
    const CFOPS_PREDEFINIDOS = [
      // CFOPs de Entrada (1000)
      { id: "cfop_1102", codigo: "1102", descricao: "Compra para comercialização dentro do estado", tipo: "Entrada", ativo: true },
      { id: "cfop_1202", codigo: "1202", descricao: "Devolução de venda dentro do estado", tipo: "Entrada", ativo: true },
      { id: "cfop_1403", codigo: "1403", descricao: "Compra para industrialização dentro do estado", tipo: "Entrada", ativo: true },
      { id: "cfop_1556", codigo: "1556", descricao: "Compra de material para uso ou consumo", tipo: "Entrada", ativo: true },

      // CFOPs de Entrada Interestadual (2000)
      { id: "cfop_2102", codigo: "2102", descricao: "Compra para comercialização interestadual", tipo: "Entrada", ativo: true },
      { id: "cfop_2202", codigo: "2202", descricao: "Devolução de venda interestadual", tipo: "Entrada", ativo: true },
      { id: "cfop_2403", codigo: "2403", descricao: "Compra para industrialização interestadual", tipo: "Entrada", ativo: true },
      { id: "cfop_2556", codigo: "2556", descricao: "Compra de material para uso ou consumo interestadual", tipo: "Entrada", ativo: true },

      // CFOPs de Entrada Exterior (3000)
      { id: "cfop_3102", codigo: "3102", descricao: "Compra para comercialização - importação", tipo: "Entrada", ativo: true },
      { id: "cfop_3403", codigo: "3403", descricao: "Compra para industrialização - importação", tipo: "Entrada", ativo: true },

      // CFOPs de Saída (5000)
      { id: "cfop_5102", codigo: "5102", descricao: "Venda de mercadoria adquirida ou recebida de terceiros", tipo: "Saída", ativo: true },
      { id: "cfop_5202", codigo: "5202", descricao: "Devolução de compra para comercialização", tipo: "Saída", ativo: true },
      { id: "cfop_5405", codigo: "5405", descricao: "Remessa para conserto ou reparo", tipo: "Saída", ativo: true },
      { id: "cfop_5556", codigo: "5556", descricao: "Transferência de material de uso ou consumo", tipo: "Saída", ativo: true },
      { id: "cfop_5101", codigo: "5101", descricao: "Venda de produção do estabelecimento", tipo: "Saída", ativo: true },
      { id: "cfop_5949", codigo: "5949", descricao: "Outra saída de mercadoria ou prestação de serviço não especificado", tipo: "Saída", ativo: true },

      // CFOPs de Saída Interestadual (6000)
      { id: "cfop_6102", codigo: "6102", descricao: "Venda de mercadoria adquirida ou recebida de terceiros interestadual", tipo: "Saída", ativo: true },
      { id: "cfop_6202", codigo: "6202", descricao: "Devolução de compra para comercialização interestadual", tipo: "Saída", ativo: true },
      { id: "cfop_6405", codigo: "6405", descricao: "Remessa para conserto ou reparo interestadual", tipo: "Saída", ativo: true },
      { id: "cfop_6101", codigo: "6101", descricao: "Venda de produção do estabelecimento interestadual", tipo: "Saída", ativo: true },
      { id: "cfop_6949", codigo: "6949", descricao: "Outra saída de mercadoria interestadual", tipo: "Saída", ativo: true },

      // CFOPs de Saída Exterior (7000)
      { id: "cfop_7101", codigo: "7101", descricao: "Venda de produção do estabelecimento para exportação", tipo: "Saída", ativo: true },
      { id: "cfop_7102", codigo: "7102", descricao: "Venda de mercadoria adquirida ou recebida de terceiros para exportação", tipo: "Saída", ativo: true }
    ];

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      document.getElementById('logoutButton').style.display = 'inline';

      await loadCFOPs();
      atualizarEstatisticas();

      onSnapshot(collection(db, "cfops"), (snapshot) => {
        cfops = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
        atualizarEstatisticas();
      });
    };

    async function loadCFOPs() {
      try {
        const cfopsSnap = await getDocs(collection(db, "cfops"));
        cfops = cfopsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
      } catch (error) {
        console.error("Erro ao carregar CFOPs:", error);
        showNotification("Erro ao carregar CFOPs", "error");
      }
    }

    function loadCFOPTable() {
      const tableBody = document.getElementById('cfopTableBody');
      tableBody.innerHTML = '';

      cfops.forEach(cfop => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${cfop.id}</td>
          <td>${cfop.codigo}</td>
          <td>${cfop.descricao}</td>
          <td>${cfop.tipo}</td>
          <td><span class="totvs-status ${cfop.ativo ? 'status-active' : 'status-inactive'}">${cfop.ativo ? 'Ativo' : 'Inativo'}</span></td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.preencherCFOPs = async function() {
      if (!usuarioAtual) {
        showNotification('Por favor, faça login para importar CFOPs.', 'error');
        window.location.href = 'login.html';
        return;
      }

      if (!confirm('Deseja importar os CFOPs padrão? Esta operação pode demorar alguns segundos.')) {
        return;
      }

      try {
        const cfopsExistentes = cfops.map(cfop => cfop.codigo);
        let novosCFOPs = 0;
        let cfopsIgnorados = 0;

        showNotification('Importando CFOPs... Por favor aguarde.', 'success');

        for (const cfop of CFOPS_PREDEFINIDOS) {
          if (!cfopsExistentes.includes(cfop.codigo)) {
            await addDoc(collection(db, "cfops"), cfop);
            novosCFOPs++;
          } else {
            cfopsIgnorados++;
          }
        }

        if (novosCFOPs > 0) {
          showNotification(`✅ Importação concluída! ${novosCFOPs} novos CFOPs adicionados. ${cfopsIgnorados} CFOPs já existiam.`, 'success');
        } else {
          showNotification('ℹ️ Nenhum novo CFOP foi adicionado. Todos os CFOPs padrão já existem no sistema.', 'success');
        }

        await loadCFOPs();
        atualizarEstatisticas();

      } catch (error) {
        console.error("Erro ao importar CFOPs:", error);
        showNotification(`❌ Erro ao importar CFOPs: ${error.message}`, 'error');
      }
    };

    window.atualizarEstatisticas = function() {
      const total = cfops.length;
      const entrada = cfops.filter(c => c.tipo === 'Entrada').length;
      const saida = cfops.filter(c => c.tipo === 'Saída').length;
      const ativos = cfops.filter(c => c.ativo).length;

      document.getElementById('totalCfops').textContent = total;
      document.getElementById('cfopsEntrada').textContent = entrada;
      document.getElementById('cfopsSaida').textContent = saida;
      document.getElementById('cfopsAtivos').textContent = ativos;
    };

    window.logout = function() {
      localStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    };

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.className = `notification notification-${type}`;
      document.querySelector('.container').prepend(notification);
      setTimeout(() => {
        notification.remove();
      }, 5000);
    }
  </script>
</body>
</html>