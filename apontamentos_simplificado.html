<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apontamento de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    input, select, textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      margin-bottom: 8px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
      transform: translateY(-1px);
    }

    textarea {
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }
    .search-bar {
      background: white;
      border-radius: 12px;
      padding: 25px;
      margin-bottom: 30px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 25px;
    }

    .form-col {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #7f8c8d;
      font-size: 14px;
    }
    .orders-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      font-size: 13px;
    }
    .orders-table th, .orders-table td {
      padding: 10px 8px;
      border: 1px solid #e9ecef;
      text-align: left;
    }
    .orders-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 18px 15px;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
    }
    .orders-table td {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }
    .orders-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }
    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-align: center;
      min-width: 100px;
    }
    .status-pendente {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    .status-em-producao {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }
    .status-concluida {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status-cancelada {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(5px);
      animation: modalFadeIn 0.3s ease;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-content {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 900px;
      border-radius: 16px;
      position: relative;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      animation: modalSlideIn 0.4s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
      color: white;
      padding: 25px 30px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .modal-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .modal-header h2 {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
      z-index: 1;
    }

    .modal-header h2::before {
      content: '🏭';
      font-size: 28px;
      opacity: 0.9;
    }
    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 30px;
      max-height: calc(85vh - 150px);
      background: white;
      position: relative;
    }

    /* Seção de informações da ordem */
    #orderInfo {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 25px;
      border-left: 4px solid #0854a0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    #orderInfo h3 {
      color: #0854a0;
      margin: 0 0 15px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    #orderInfo h3::before {
      content: '📋';
      font-size: 20px;
    }

    .order-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .order-detail-item {
      background: white;
      padding: 12px 15px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .order-detail-label {
      font-size: 12px;
      color: #6c757d;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
    }

    .order-detail-value {
      font-size: 16px;
      color: #212529;
      font-weight: 600;
    }
    .modal-footer {
      padding: 25px 30px;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      text-align: right;
      border-radius: 0 0 16px 16px;
    }

    .modal-footer button {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(16, 126, 62, 0.3);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modal-footer button:hover {
      background: linear-gradient(135deg, #0d6e36, #0a5a2e);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(16, 126, 62, 0.4);
    }

    .modal-footer button:disabled {
      background: linear-gradient(135deg, #6c757d, #5a6268);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .close-button {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      position: relative;
      z-index: 2;
    }

    .close-button:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: rotate(90deg) scale(1.1);
    }
    .materials-list {
      max-height: 400px;
      overflow-y: auto;
      margin: 25px 0;
      padding: 0;
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .materials-list h4 {
      background: linear-gradient(135deg, #6f42c1, #5a32a3);
      color: white;
      margin: 0;
      padding: 15px 20px;
      border-radius: 12px 12px 0 0;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .materials-list h4::before {
      content: '📦';
      font-size: 18px;
    }

    .materials-container {
      padding: 15px;
    }

    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background: white;
      margin-bottom: 10px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .material-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .material-item:last-child {
      margin-bottom: 0;
    }

    .material-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .material-code {
      font-weight: 600;
      color: #0854a0;
      font-size: 14px;
    }

    .material-description {
      color: #495057;
      font-size: 13px;
      line-height: 1.4;
    }

    .material-status {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      font-weight: 600;
    }

    .status-ok {
      color: #107e3e;
    }

    .status-warning {
      color: #e9730c;
    }

    .status-error {
      color: #bb0000;
    }

    .material-quantities {
      text-align: right;
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
    }

    .generate-stock-btn {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .generate-stock-btn:hover {
      background: linear-gradient(135deg, #d66a0b, #c4600a);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(233, 115, 12, 0.3);
    }

    /* Estilos específicos para modal de materiais faltantes */
    #modalMateriaisFaltantes .modal-content {
      box-shadow: 0 20px 60px rgba(220, 53, 69, 0.15);
      border: 2px solid #dc3545;
    }

    #modalMateriaisFaltantes .modal-header {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    #modalMateriaisFaltantes .close {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    #modalMateriaisFaltantes .close:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: rotate(90deg) scale(1.1);
    }

    #modalMateriaisFaltantes table tbody tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    #modalMateriaisFaltantes table tbody tr:hover {
      background-color: #fff3cd;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    #modalMateriaisFaltantes button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* Animação para linhas da tabela */
    #modalMateriaisFaltantes tbody tr {
      animation: slideInRow 0.3s ease-out;
    }

    @keyframes slideInRow {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    /* Animações CSS removidas para interface mais limpa */

    /* Estilos para botões de filtro */
    .search-bar button {
      transition: all 0.3s ease;
    }

    .search-bar button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .search-bar button:active {
      transform: translateY(0);
    }

    /* Estilo para indicador de filtros salvos */
    #indicadorFiltrosPersistidos {
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: scale(0.8);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    /* Estilo para valores em falta */
    #modalMateriaisFaltantes .falta-valor {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: bold;
      font-size: 12px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }
    .material-status {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-ok { background: #e8f3e8; color: #107e3e; border: 1px solid #107e3e; }
    .status-warning { background: #fff4cc; color: #8c6c00; border: 1px solid #ffd43b; }
    .status-error { background: #ffeaea; color: #bb0000; border: 1px solid #bb0000; }
    .progress-bar {
      width: 100%;
      height: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 5px;
      border: 1px solid #e9ecef;
    }
    .progress-fill {
      height: 100%;
      background-color: #27ae60;
      transition: width 0.3s ease;
    }
    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Estilos para notificações */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 2000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: linear-gradient(135deg, #107e3e, #0d6e36);
    }

    .notification.warning {
      background: linear-gradient(135deg, #e9730c, #d66a0b);
    }

    .notification.error {
      background: linear-gradient(135deg, #bb0000, #a30000);
    }

    .notification.info {
      background: linear-gradient(135deg, #0854a0, #0a4d8c);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border-left: 5px solid;
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card.total {
      border-left-color: #3498db;
    }

    .stat-card.producao {
      border-left-color: #27ae60;
    }

    .stat-card.falta {
      border-left-color: #f39c12;
    }

    .stat-card.atrasadas {
      border-left-color: #e74c3c;
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .stat-label {
      color: #7f8c8d;
      font-weight: 600;
      font-size: 14px;
    }

    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      margin-bottom: 30px;
    }

    /* Animação do spinner */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Animação de entrada para cards */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.5s ease-out;
    }
  </style>

    <script type="module" src="js/main.js"></script>
</head>
<body>
  <!-- Indicador de Carregamento -->
  <div id="loadingOverlay" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
  ">
    <div style="
      background: white;
      padding: 40px;
      border-radius: 15px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      text-align: center;
      max-width: 400px;
      width: 90%;
    ">
      <div style="
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #0854a0;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      "></div>

      <h3 style="color: #2c3e50; margin-bottom: 10px; font-size: 18px;">
        <i class="fas fa-cogs" style="color: #0854a0; margin-right: 8px;"></i>
        Carregando Sistema
      </h3>

      <p id="loadingText" style="color: #7f8c8d; margin-bottom: 20px; font-size: 14px;">
        Inicializando conexão com Firebase...
      </p>

      <!-- Barra de Progresso -->
      <div style="
        width: 100%;
        height: 8px;
        background: #ecf0f1;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 15px;
      ">
        <div id="progressBar" style="
          width: 0%;
          height: 100%;
          background: linear-gradient(90deg, #0854a0, #0a4d8c);
          transition: width 0.3s ease;
          border-radius: 4px;
        "></div>
      </div>

      <div id="progressPercent" style="
        color: #0854a0;
        font-weight: 600;
        font-size: 14px;
      ">0%</div>
    </div>
  </div>

  <div class="container">
    <div class="header">
      <h1>Apontamento de Produção</h1>
      <div style="display: flex; gap: 10px; align-items: center;">
        <button onclick="abrirDivisaoOP()"
                style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; display: flex; align-items: center; gap: 8px;"
                title="Dividir OP para produção parcial">
          <i class="fas fa-cut"></i> Dividir OP
        </button>
        <button onclick="abrirPainelEmpenhos()"
                style="background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; display: flex; align-items: center; gap: 8px;"
                title="Abrir painel de empenhos em nova aba">
          <i class="fas fa-bolt"></i> Empenhos
        </button>
        <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
      </div>
    </div>

    <!-- Dashboard de Métricas -->
    <div class="main-content">
      <div class="stats-grid">
        <div class="stat-card total">
          <div class="stat-number" id="totalOPs">0</div>
          <div class="stat-label">OPs Ativas</div>
        </div>
        <div class="stat-card producao">
          <div class="stat-number" id="emProducao">0</div>
          <div class="stat-label">Em Produção</div>
        </div>
        <div class="stat-card falta">
          <div class="stat-number" id="materiaisFalta">0</div>
          <div class="stat-label">Materiais em Falta</div>
        </div>
        <div class="stat-card atrasadas">
          <div class="stat-number" id="opsAtrasadas">0</div>
          <div class="stat-label">OPs Atrasadas</div>
        </div>
      </div>

    <div class="search-bar">
      <div class="form-row">
        <div class="form-col">
          <input type="text" id="searchInput" placeholder="Buscar por número da ordem ou produto...">
        </div>
        <div class="form-col">
          <select id="statusFilter" onchange="filterOrders()">
            <option value="">Todos os status</option>
            <option value="Pendente">Pendente</option>
            <option value="Em Produção">Em Produção</option>
          </select>
        </div>
        <div class="form-col">
          <button onclick="atualizarDashboard()" class="btn btn-success">
            <i class="fas fa-sync-alt"></i> Atualizar Métricas
          </button>
        </div>
      </div>

      <!-- 🔍 NOVA SEÇÃO: Controles de Filtros -->
      <div class="form-row" style="margin-top: 10px; border-top: 1px solid #e9ecef; padding-top: 10px;">
        <div class="form-col">
          <button onclick="limparFiltros()" style="background: linear-gradient(135deg, #6c757d, #545b62); color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 13px; cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-eraser"></i> Limpar Filtros
          </button>
        </div>
        <div class="form-col">
          <button onclick="mostrarStatusFiltros()" style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 13px; cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-info-circle"></i> Status dos Filtros
          </button>
        </div>
        <div class="form-col">
          <span id="indicadorFiltrosPersistidos" style="display: none; background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; cursor: pointer;" onclick="mostrarResumoFiltros()">
            <i class="fas fa-save"></i> Filtros Salvos
          </span>
        </div>
      </div>
    </div>

      <div class="table-container">
        <table class="orders-table">
          <thead>
            <tr>
              <th>Ordem</th>
              <th>Produto</th>
              <th>Quantidade</th>
              <th>Produzido</th>
              <th>Status</th>
              <th>Data Entrega</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="ordersTableBody">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Apontamento de Produção</h2>
        <span class="close-button" onclick="closeModal()">×</span>
      </div>

      <div class="modal-body">
        <div id="orderInfo"></div>

        <div id="materialsList" class="materials-list">
          <h4>Materiais Necessários</h4>
          <div class="materials-container"></div>
        </div>

        <form id="appointmentForm" onsubmit="submitAppointment(event)">
          <div class="form-row">
            <div class="form-col">
              <label for="quantity">Quantidade Produzida</label>
              <input type="number" id="quantity" min="0.001" step="0.001" required placeholder="Digite a quantidade produzida" onblur="formatarCampoNumerico(this)">
            </div>
            <div class="form-col">
              <label for="scrap">Quantidade de Refugo</label>
              <input type="number" id="scrap" min="0" step="0.001" value="0" placeholder="Digite a quantidade de refugo" onblur="formatarCampoNumerico(this)">
            </div>
          </div>
          <div class="form-row">
            <div class="form-col">
              <label for="observations">Observações</label>
              <textarea id="observations" rows="3" placeholder="Digite observações sobre a produção (opcional)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="submit" form="appointmentForm" id="submitButton">
          <i class="fas fa-check"></i> Confirmar Apontamento
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de Materiais Faltantes -->
  <div id="modalMateriaisFaltantes" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
      <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border-radius: 8px 8px 0 0;">
        <h2><i class="fas fa-exclamation-triangle"></i> Materiais Insuficientes para Impressão</h2>
        <span class="close" onclick="fecharModalMateriaisFaltantes()" style="color: white; font-size: 28px;">&times;</span>
      </div>

      <div class="modal-body" style="padding: 0;">
        <!-- Seção: Informações da OP -->
        <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6;">
          <div style="display: flex; align-items: center; gap: 15px;">
            <div style="background: #dc3545; color: white; padding: 10px; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-industry" style="font-size: 20px;"></i>
            </div>
            <div>
              <h3 style="margin: 0; color: #495057;">Ordem de Produção: <span id="numeroOPFaltantes" style="color: #dc3545; font-weight: bold;"></span></h3>
              <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">Produto: <span id="produtoOPFaltantes"></span></p>
            </div>
          </div>
        </div>

        <!-- Seção: Alerta -->
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px; border-radius: 8px;">
          <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle" style="color: #856404; font-size: 20px;"></i>
            <div>
              <h4 style="margin: 0; color: #856404;">⚠️ Atenção: Estoque Insuficiente</h4>
              <p style="margin: 5px 0 0 0; color: #856404; font-size: 14px;">
                Não é possível imprimir a OP pois há materiais com estoque insuficiente.
                Transfira os materiais necessários ou ajuste o estoque antes de prosseguir.
              </p>
            </div>
          </div>
        </div>

        <!-- Seção: Lista de Materiais Faltantes -->
        <div style="padding: 20px;">
          <h4 style="color: #495057; margin-bottom: 15px;">
            <i class="fas fa-list-ul" style="color: #dc3545;"></i>
            Materiais com Estoque Insuficiente
          </h4>

          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <thead style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                <tr>
                  <th style="padding: 12px 8px; text-align: left; font-weight: 600; font-size: 12px; text-transform: uppercase;">Código</th>
                  <th style="padding: 12px 8px; text-align: left; font-weight: 600; font-size: 12px; text-transform: uppercase;">Descrição</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Tipo</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Necessário</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Disponível</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Falta</th>
                  <th style="padding: 12px 8px; text-align: center; font-weight: 600; font-size: 12px; text-transform: uppercase;">Unidade</th>
                </tr>
              </thead>
              <tbody id="tbodyMateriaisFaltantes">
                <!-- Preenchido dinamicamente -->
              </tbody>

              <!-- Elemento para conteúdo dinâmico -->
              <div id="conteudoMateriaisFaltantes" style="display: none;">
                <!-- Conteúdo será preenchido dinamicamente pela função JavaScript -->
              </div>
            </table>
          </div>
        </div>

        <!-- Seção: Ações -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; display: flex; gap: 15px; justify-content: space-between;">
          <button type="button" onclick="dividirOPDoModal()" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; border: none; cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-cut"></i> Dividir OP
          </button>
          <div style="display: flex; gap: 15px;">
            <button type="button" onclick="fecharModalMateriaisFaltantes()" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: #6c757d; color: white; border: none; cursor: pointer; transition: all 0.3s ease;">
              <i class="fas fa-times"></i> Cancelar
            </button>
            <button type="button" onclick="forcarImpressaoOP()" style="padding: 12px 24px; border-radius: 25px; font-weight: 600; background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; cursor: pointer; transition: all 0.3s ease;">
              <i class="fas fa-exclamation-triangle"></i> Forçar Impressão
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Área oculta para impressão -->
  <div id="printArea" style="display:none"></div>

  <!-- Modal para análise de produção viável -->
  <div id="modalAnaliseProducao" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto;">
      <span class="close" onclick="fecharModalAnalise()">&times;</span>
      <h2>🎯 Análise de Produção Viável</h2>
      <div id="conteudoAnaliseProducao"></div>
      <div class="modal-actions">
        <button onclick="exportarRelatorio()" class="btn-primary">📊 Exportar Relatório</button>
        <button onclick="iniciarProducaoLote()" class="btn-success">🚀 Iniciar Lote Viável</button>
        <button onclick="fecharModalAnalise()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <!-- Modal para diagnóstico de impossibilidade -->
  <div id="modalDiagnosticoImpossibilidade" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 95%; max-height: 95%; overflow-y: auto;">
      <span class="close" onclick="fecharModalDiagnostico()">&times;</span>
      <h2>🔍 Diagnóstico Completo de Impossibilidade</h2>
      <div id="conteudoDiagnosticoImpossibilidade"></div>
      <div class="modal-actions">
        <button onclick="exportarDiagnostico()" class="btn-primary">📊 Exportar Diagnóstico</button>
        <button onclick="executarSugestoes()" class="btn-warning">💡 Executar Sugestões</button>
        <button onclick="fecharModalDiagnostico()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <!-- Modal para decodificador de produtos -->
  <div id="modalDecodificadorProdutos" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto;">
      <span class="close" onclick="fecharModalDecodificador()">&times;</span>
      <h2>🔍 Decodificador de Produtos & Correção de Estoque</h2>
      <div id="conteudoDecodificadorProdutos">
        <div style="padding: 20px;">
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">🔍 Buscar Produto por Código Críptico</h4>
            <div style="display: flex; gap: 10px; align-items: center;">
              <input type="text" id="codigoCriptico" placeholder="Ex: 9QIS3qMMN0Ca9Bhc2R73"
                     style="flex: 1; padding: 10px; border: 1px solid #ced4da; border-radius: 4px; font-family: monospace;">
              <button onclick="buscarProdutoPorCodigo()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                🔍 Buscar
              </button>
            </div>
          </div>

          <div id="resultadoBusca" style="display: none;">
            <!-- Resultado da busca será inserido aqui -->
          </div>

          <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">📊 Produtos com Problemas de Estoque</h4>
            <div id="produtosComProblemas">
              <p style="color: #6c757d; margin: 0;">Clique em "🔍 Analisar Produtos" para identificar produtos com estoque negativo ou problemas.</p>
            </div>
            <button onclick="analisarProdutosComProblemas()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
              🔍 Analisar Produtos
            </button>
          </div>
        </div>
      </div>
      <div class="modal-actions">
        <button onclick="exportarRelatorioDecodificacao()" class="btn-primary">📊 Exportar Relatório</button>
        <button onclick="corrigirEstoquesLote()" class="btn-success">🔧 Corrigir Estoques em Lote</button>
        <button onclick="fecharModalDecodificador()" class="btn-secondary">Fechar</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { doc, getDoc, collection, onSnapshot, updateDoc, Timestamp, addDoc, writeBatch, getDocs, query, where, runTransaction } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // ===================================================================
    // CLASSES DE SERVIÇO MELHORADAS - AUDITORIA 18/07/2025
    // ===================================================================

    // 🔧 VALIDADOR DE QUANTIDADES
    class QuantityValidator {
        static validate(quantity, maxAllowed = Infinity, fieldName = 'Quantidade') {
            if (quantity === null || quantity === undefined || quantity === '') {
                throw new Error(`${fieldName} é obrigatória`);
            }

            const num = parseFloat(quantity);
            if (isNaN(num)) {
                throw new Error(`${fieldName} deve ser um número válido`);
            }
            if (num <= 0) {
                throw new Error(`${fieldName} deve ser positiva`);
            }
            if (!isFinite(num)) {
                throw new Error(`${fieldName} deve ser um número finito`);
            }
            if (num > maxAllowed) {
                throw new Error(`${fieldName} não pode exceder ${maxAllowed}`);
            }

            // Verificar precisão (máximo 3 casas decimais)
            const decimalPlaces = (num.toString().split('.')[1] || '').length;
            if (decimalPlaces > 3) {
                throw new Error(`${fieldName} não pode ter mais de 3 casas decimais`);
            }

            return num;
        }

        static validatePositiveInteger(value, fieldName = 'Valor') {
            const num = parseInt(value);
            if (isNaN(num) || num <= 0 || num !== parseFloat(value)) {
                throw new Error(`${fieldName} deve ser um número inteiro positivo`);
            }
            return num;
        }
    }

    // 🔒 GERENCIADOR DE TRANSAÇÕES ATÔMICAS
    class TransactionManager {
        static async executeAtomicOperation(operationName, operations) {
            console.log(`🔄 Iniciando transação atômica: ${operationName}`);

            try {
                const result = await runTransaction(db, async (transaction) => {
                    const results = [];

                    for (let i = 0; i < operations.length; i++) {
                        const op = operations[i];
                        console.log(`  📝 Operação ${i + 1}/${operations.length}: ${op.type}`);

                        switch (op.type) {
                            case 'UPDATE':
                                transaction.update(op.ref, {
                                    ...op.data,
                                    ultimaMovimentacao: Timestamp.now(),
                                    transacaoId: operationName,
                                    transacaoTimestamp: Timestamp.now()
                                });
                                break;

                            case 'CREATE':
                                transaction.set(op.ref, {
                                    ...op.data,
                                    criadoEm: Timestamp.now(),
                                    transacaoId: operationName,
                                    transacaoTimestamp: Timestamp.now()
                                });
                                break;

                            case 'DELETE':
                                transaction.delete(op.ref);
                                break;

                            case 'READ':
                                const docSnap = await transaction.get(op.ref);
                                results.push({ id: op.ref.id, data: docSnap.data(), exists: docSnap.exists() });
                                break;

                            default:
                                throw new Error(`Tipo de operação não suportado: ${op.type}`);
                        }
                    }

                    return results;
                });

                console.log(`✅ Transação atômica concluída: ${operationName}`);
                return result;

            } catch (error) {
                console.error(`❌ Erro na transação atômica ${operationName}:`, error);
                throw new Error(`Falha na transação ${operationName}: ${error.message}`);
            }
        }
    }

    // 📦 SERVIÇO DE ESTOQUE MELHORADO
    class EstoqueService {
        static async getSaldoDisponivel(produtoId, armazemId) {
            try {
                const estoqueQuery = query(
                    collection(db, "estoques"),
                    where("produtoId", "==", produtoId),
                    where("armazemId", "==", armazemId)
                );

                const estoqueSnap = await getDocs(estoqueQuery);
                if (estoqueSnap.empty) {
                    return 0;
                }

                const estoque = estoqueSnap.docs[0].data();
                const saldoTotal = estoque.saldo || 0;
                const saldoReservado = estoque.saldoReservado || 0;
                const saldoEmpenhado = estoque.saldoEmpenhado || 0;

                return Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);

            } catch (error) {
                console.error('Erro ao buscar saldo disponível:', error);
                return 0;
            }
        }

        static async transferirMaterialAtomico(origem, destino, quantidade, motivo, numeroOP) {
            // Validar quantidade
            const qtdValidada = QuantityValidator.validate(quantidade, origem.saldo, 'Quantidade a transferir');

            const operationName = `TRANSFER_${numeroOP}_${Date.now()}`;

            const operations = [
                {
                    type: 'UPDATE',
                    ref: doc(db, 'estoques', origem.id),
                    data: {
                        saldo: origem.saldo - qtdValidada,
                        observacoes: `${origem.observacoes || ''} | Transferência: -${qtdValidada} (${motivo})`
                    }
                },
                {
                    type: 'UPDATE',
                    ref: doc(db, 'estoques', destino.id),
                    data: {
                        saldo: destino.saldo + qtdValidada,
                        observacoes: `${destino.observacoes || ''} | Transferência: +${qtdValidada} (${motivo})`
                    }
                },
                {
                    type: 'CREATE',
                    ref: doc(collection(db, 'movimentacoesEstoque')),
                    data: {
                        produtoId: origem.produtoId,
                        tipo: 'SAIDA',
                        quantidade: qtdValidada,
                        armazemId: origem.armazemId,
                        motivo: motivo,
                        numeroOP: numeroOP,
                        tipoDocumento: 'TRANSFERENCIA_ATOMICA'
                    }
                },
                {
                    type: 'CREATE',
                    ref: doc(collection(db, 'movimentacoesEstoque')),
                    data: {
                        produtoId: destino.produtoId,
                        tipo: 'ENTRADA',
                        quantidade: qtdValidada,
                        armazemId: destino.armazemId,
                        motivo: motivo,
                        numeroOP: numeroOP,
                        tipoDocumento: 'TRANSFERENCIA_ATOMICA'
                    }
                }
            ];

            return await TransactionManager.executeAtomicOperation(operationName, operations);
        }
    }

    // ===================================================================
    // SERVIÇO DE EMPENHOS - MELHORADO
    // ===================================================================
    class EmpenhoService {
        static async criarEmpenhoAtomico(ordemProducaoId, produtoId, armazemId, quantidade, motivo = 'Empenho automático') {
            // Validar entrada
            const qtdValidada = QuantityValidator.validate(quantidade, Infinity, 'Quantidade do empenho');

            if (!ordemProducaoId || !produtoId || !armazemId) {
                throw new Error('Parâmetros obrigatórios: ordemProducaoId, produtoId, armazemId');
            }

            const operationName = `EMPENHO_${ordemProducaoId}_${Date.now()}`;

            const operations = [
                {
                    type: 'CREATE',
                    ref: doc(collection(db, 'empenhos')),
                    data: {
                        ordemProducaoId,
                        produtoId,
                        armazemId,
                        quantidadeEmpenhada: qtdValidada,
                        quantidadeConsumida: 0,
                        status: 'ATIVO',
                        dataEmpenho: Timestamp.now(),
                        motivo,
                        origemReserva: true,
                        criadoAutomaticamente: true
                    }
                },
                {
                    type: 'UPDATE',
                    ref: doc(db, 'estoques', `${produtoId}_${armazemId}`),
                    data: {
                        saldoEmpenhado: (await this.getSaldoEmpenhado(produtoId, armazemId)) + qtdValidada
                    }
                }
            ];

            return await TransactionManager.executeAtomicOperation(operationName, operations);
        }

        static async getSaldoEmpenhado(produtoId, armazemId) {
            try {
                const estoqueQuery = query(
                    collection(db, "estoques"),
                    where("produtoId", "==", produtoId),
                    where("armazemId", "==", armazemId)
                );

                const estoqueSnap = await getDocs(estoqueQuery);
                if (estoqueSnap.empty) return 0;

                const estoque = estoqueSnap.docs[0].data();
                return estoque.saldoEmpenhado || 0;

            } catch (error) {
                console.error('Erro ao buscar saldo empenhado:', error);
                return 0;
            }
        }

        static async transferirReservasParaEmpenhos(ordemProducaoId) {
            console.log(`🔄 Transferindo reservas para empenhos - OP: ${ordemProducaoId}`);

            // Validar entrada
            if (!ordemProducaoId) {
                throw new Error('ID da ordem de produção é obrigatório');
            }

            return runTransaction(db, async (transaction) => {
                const opRef = doc(db, "ordensProducao", ordemProducaoId);
                const opDoc = await transaction.get(opRef);

                if (!opDoc.exists()) {
                    throw new Error('Ordem de produção não encontrada');
                }

                const op = opDoc.data();
                const materiaisNecessarios = op.materiaisNecessarios || [];

                // 🔍 PRIMEIRO: Verificar se já existem empenhos da transferência
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("status", "==", "ATIVO")
                );

                const empenhosSnapshot = await getDocs(empenhosQuery);
                const empenhosExistentes = new Map();

                empenhosSnapshot.docs.forEach(doc => {
                    const empenho = doc.data();
                    const key = `${empenho.produtoId}_${empenho.armazemId}`;
                    empenhosExistentes.set(key, {
                        id: doc.id,
                        ...empenho
                    });
                });

                console.log(`📋 Encontrados ${empenhosExistentes.size} empenhos ativos existentes para OP ${ordemProducaoId}`);

                let empenhosJaExistiam = 0;
                let empenhosNovos = 0;
                let erros = [];

                for (const material of materiaisNecessarios) {
                    if (!material.quantidadeReservada || material.quantidadeReservada <= 0) {
                        continue;
                    }

                    try {
                        const chaveEmpenho = `${material.produtoId}_${op.armazemProducaoId}`;
                        const empenhoExistente = empenhosExistentes.get(chaveEmpenho);

                        if (empenhoExistente && !empenhoExistente.origemReserva) {
                            // ✅ JÁ EXISTE EMPENHO DA TRANSFERÊNCIA - apenas contar
                            console.log(`✅ Empenho da transferência já existe para material ${material.produtoId}: ${empenhoExistente.quantidadeEmpenhada} empenhado`);
                            empenhosJaExistiam++;
                            continue;
                        }

                        // 🔄 CRIAR NOVO EMPENHO (para compatibilidade com OPs antigas)
                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", material.produtoId),
                            where("armazemId", "==", op.armazemProducaoId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (estoqueSnapshot.empty) {
                            erros.push(`Estoque não encontrado para produto ${material.produtoId}`);
                            continue;
                        }

                        const estoqueDoc = estoqueSnapshot.docs[0];
                        const estoque = estoqueDoc.data();

                        const quantidadeTransferir = material.quantidadeReservada;
                        const novoSaldoReservado = Math.max(0, (estoque.saldoReservado || 0) - quantidadeTransferir);
                        const novoSaldoEmpenhado = (estoque.saldoEmpenhado || 0) + quantidadeTransferir;

                        transaction.update(doc(db, "estoques", estoqueDoc.id), {
                            saldoReservado: novoSaldoReservado,
                            saldoEmpenhado: novoSaldoEmpenhado,
                            ultimaMovimentacao: Timestamp.now()
                        });

                        const empenhoRef = doc(collection(db, "empenhos"));
                        transaction.set(empenhoRef, {
                            ordemProducaoId,
                            produtoId: material.produtoId,
                            armazemId: op.armazemProducaoId,
                            quantidadeEmpenhada: quantidadeTransferir,
                            quantidadeConsumida: 0,
                            status: 'ATIVO',
                            dataEmpenho: Timestamp.now(),
                            origemReserva: true, // Marca como empenho de reserva (método antigo)
                            observacoes: 'Empenho criado por compatibilidade - material não transferido previamente'
                        });

                        empenhosNovos++;
                        console.log(`🔄 Empenho de compatibilidade criado para material ${material.produtoId}`);

                    } catch (error) {
                        erros.push(`Erro no material ${material.produtoId}: ${error.message}`);
                    }
                }

                const totalEmpenhos = empenhosJaExistiam + empenhosNovos;

                transaction.update(opRef, {
                    status: 'Em Produção',
                    dataInicioProducao: Timestamp.now(),
                    empenhosAtivos: totalEmpenhos,
                    empenhosExistentes: empenhosJaExistiam,
                    empenhosNovos: empenhosNovos
                });

                console.log(`✅ Empenhos processados: ${empenhosJaExistiam} já existiam + ${empenhosNovos} novos = ${totalEmpenhos} total`);

                return {
                    transferencias: totalEmpenhos,
                    empenhosExistentes: empenhosJaExistiam,
                    empenhosNovos: empenhosNovos,
                    erros,
                    ordemProducaoId
                };
            });
        }

        static async consumirMaterialEmpenhado(ordemProducaoId, consumos) {
            console.log(`⚡ Consumindo materiais empenhados - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                let consumosRealizados = 0;
                let erros = [];

                for (const consumo of consumos) {
                    try {
                        const empenhoQuery = query(
                            collection(db, "empenhos"),
                            where("ordemProducaoId", "==", ordemProducaoId),
                            where("produtoId", "==", consumo.produtoId),
                            where("status", "==", "ATIVO")
                        );

                        const empenhoSnapshot = await getDocs(empenhoQuery);
                        if (empenhoSnapshot.empty) {
                            erros.push(`Empenho não encontrado para produto ${consumo.produtoId}`);
                            continue;
                        }

                        const empenhoDoc = empenhoSnapshot.docs[0];
                        const empenho = empenhoDoc.data();

                        const quantidadeDisponivel = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                        const quantidadeConsumir = Math.min(consumo.quantidade, quantidadeDisponivel);

                        if (quantidadeConsumir <= 0) {
                            erros.push(`Sem quantidade empenhada disponível para ${consumo.produtoId}`);
                            continue;
                        }

                        const novaQuantidadeConsumida = empenho.quantidadeConsumida + quantidadeConsumir;
                        const novoStatus = novaQuantidadeConsumida >= empenho.quantidadeEmpenhada ? 'CONSUMIDO' : 'ATIVO';

                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            quantidadeConsumida: novaQuantidadeConsumida,
                            status: novoStatus,
                            ultimoConsumo: Timestamp.now()
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", consumo.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldo: estoque.saldo - quantidadeConsumir,
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeConsumir),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                        transaction.set(movimentacaoRef, {
                            produtoId: consumo.produtoId,
                            armazemId: empenho.armazemId,
                            tipo: 'SAIDA',
                            quantidade: quantidadeConsumir,
                            tipoDocumento: 'CONSUMO_PRODUCAO',
                            numeroDocumento: ordemProducaoId,
                            observacoes: `Consumo OP ${ordemProducaoId} - Empenho`,
                            dataHora: Timestamp.now(),
                            empenhoId: empenhoDoc.id
                        });

                        consumosRealizados++;

                    } catch (error) {
                        erros.push(`Erro no consumo ${consumo.produtoId}: ${error.message}`);
                    }
                }

                return { consumosRealizados, erros };
            });
        }

        static async liberarEmpenhosRestantes(ordemProducaoId, motivo = 'OP_FINALIZADA') {
            console.log(`🔓 Liberando empenhos restantes - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("status", "==", "ATIVO")
                );

                const empenhosSnapshot = await getDocs(empenhosQuery);
                let liberacoes = 0;

                for (const empenhoDoc of empenhosSnapshot.docs) {
                    const empenho = empenhoDoc.data();
                    const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                    if (quantidadeRestante > 0) {
                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            status: 'LIBERADO',
                            quantidadeLiberada: quantidadeRestante,
                            dataLiberacao: Timestamp.now(),
                            motivoLiberacao: motivo
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", empenho.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeRestante),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        liberacoes++;
                    }
                }

                return { liberacoes, ordemProducaoId };
            });
        }

        static async consultarEmpenhosOP(ordemProducaoId) {
            const empenhosQuery = query(
                collection(db, "empenhos"),
                where("ordemProducaoId", "==", ordemProducaoId)
            );

            const empenhosSnapshot = await getDocs(empenhosQuery);
            return empenhosSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        }

        static async inicializarCampoEmpenho() {
            console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');

            const estoquesSnapshot = await getDocs(collection(db, "estoques"));
            let atualizados = 0;

            for (const estoqueDoc of estoquesSnapshot.docs) {
                const estoque = estoqueDoc.data();

                if (estoque.saldoEmpenhado === undefined) {
                    await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                        saldoEmpenhado: 0
                    });
                    atualizados++;
                }
            }

            console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
            return atualizados;
        }
    }

    let produtos = [];
    let ordensProducao = [];
    let estoques = [];
    let empenhos = []; // Adicionar variável empenhos
    let armazens = [];
    let estruturas = [];

    // ===================================================================
    // FUNÇÃO PADRONIZADA PARA CÁLCULO DE SALDO DISPONÍVEL
    // ===================================================================

    /**
     * Calcula o saldo disponível de um estoque considerando reservas e empenhos
     * @param {Object} estoque - Objeto do estoque
     * @param {string} opId - ID da OP atual (opcional)
     * @returns {number} - Saldo disponível (nunca negativo)
     */
    function calcularSaldoDisponivel(estoque, opId = null) {
        if (!estoque) return 0;

        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;

        // Se há uma OP específica e o estoque tem reserva, verificar se a reserva é para esta OP
        let saldoReservadoParaOutrasOPs = saldoReservado;

        if (opId && saldoReservado > 0) {
            // ESTRATÉGIA: Assumir que a reserva é para a OP atual se:
            // 1. A OP atual existe e tem o mesmo ID, OU
            // 2. Não há informação específica sobre para qual OP é a reserva

            const opAtual = currentOrder || ordensProducao?.find(op => op.id === opId);

            if (opAtual && (opAtual.id === opId || currentOrder?.id === opId)) {
                // A reserva é para a OP atual, então ela pode usar o material reservado
                saldoReservadoParaOutrasOPs = 0;
                console.log(`✅ OP ${opAtual.numero || opId} pode usar sua própria reserva de ${saldoReservado.toFixed(3)}`);
            } else {
                // Estratégia conservadora: assumir que a reserva é para a OP atual
                // (isso resolve a maioria dos casos onde material foi transferido para a OP)
                saldoReservadoParaOutrasOPs = 0;
                console.log(`🔄 Assumindo que reserva de ${saldoReservado.toFixed(3)} é para OP ${opId}`);
            }
        }

        // Saldo disponível = Total - Reservado para outras OPs - Empenhado
        const saldoDisponivel = saldoTotal - saldoReservadoParaOutrasOPs - saldoEmpenhado;

        // Log para debug quando há inconsistências
        if (saldoReservado > saldoTotal || saldoEmpenhado > saldoTotal) {
            console.warn(`⚠️ Inconsistência no estoque:`, {
                produtoId: estoque.produtoId,
                armazemId: estoque.armazemId,
                saldoTotal,
                saldoReservado,
                saldoEmpenhado,
                saldoReservadoParaOutrasOPs,
                saldoDisponivel: Math.max(0, saldoDisponivel),
                opId
            });
        }

        return Math.max(0, saldoDisponivel); // Nunca retornar negativo
    }

    /**
     * Versão específica para apontamentos que considera a OP atual
     */
    function calcularSaldoDisponivelParaOP(estoque, opId) {
        return calcularSaldoDisponivel(estoque, opId);
    }

    /**
     * Função de arredondamento inteligente para cálculos de peças possíveis
     * Arredonda para cima quando o valor está muito próximo do próximo inteiro
     * @param {number} valor - Valor a ser arredondado
     * @param {number} threshold - Limite para arredondar para cima (padrão: 0.95)
     * @returns {number} - Valor arredondado
     */
    function arredondamentoInteligente(valor, threshold = 0.95) {
        if (valor <= 0) return 0;

        const parteInteira = Math.floor(valor);
        const parteDecimal = valor - parteInteira;

        // Se a parte decimal é >= threshold, arredondar para cima
        if (parteDecimal >= threshold) {
            console.log(`🔄 Arredondamento inteligente: ${valor.toFixed(6)} → ${parteInteira + 1} (decimal: ${parteDecimal.toFixed(6)})`);
            return parteInteira + 1;
        }

        return parteInteira;
    }
    let operacoes = [];
    let recursos = [];
    let currentOrder = null;
    let permitirProducaoSemEstoque = false;
    let permitirGerarEstoqueAutomatico = false;

    // Função global para formatar números (máximo 3 casas decimais, sem notação científica, evita dízimas)
    function formatarNumero(num) {
      if (num === undefined || num === null) return '0';
      const numero = parseFloat(num);
      if (isNaN(numero)) return '0';

      // Se o número for muito pequeno (notação científica), mostrar como 0
      if (Math.abs(numero) < 0.001 && numero !== 0) return '0';

      // 🔧 CORREÇÃO PARA DÍZIMAS: Arredondar para evitar problemas de precisão
      const numeroArredondado = Math.round(numero * 1000) / 1000;

      // Se o número arredondado é muito próximo de um inteiro, mostrar como inteiro
      if (Math.abs(numeroArredondado - Math.round(numeroArredondado)) < 0.001) {
        return Math.round(numeroArredondado).toString();
      }

      // Formatar com até 3 casas decimais e remover zeros desnecessários
      return numeroArredondado.toFixed(3).replace(/\.?0+$/, '');
    }

    // 🔧 NOVA FUNÇÃO: Sempre mostrar exatamente 3 casas decimais
    function formatarNumero3Casas(num) {
      if (num === undefined || num === null) return '0.000';
      const numero = parseFloat(num);
      if (isNaN(numero)) return '0.000';

      // Arredondar para 3 casas decimais e sempre mostrar 3 casas
      const numeroArredondado = Math.round(numero * 1000) / 1000;
      return numeroArredondado.toFixed(3);
    }

    // Função para formatar campos numéricos em tempo real (sempre 3 casas decimais)
    function formatarCampoNumerico(campo) {
      if (campo.value && !isNaN(campo.value)) {
        campo.value = formatarNumero3Casas(parseFloat(campo.value));
      }
    }

    // Variáveis para preservar estado da busca
    let estadoBusca = {
      termo: '',
      filtroStatus: '',
      filtroDataInicio: '',
      filtroDataFim: '',
      ordemAtual: [],
      preservar: false
    };

    window.onload = async function() {
      try {
        // Iniciar progresso
        await LoadingProgress.nextStep('Verificando autenticação...');

        // Verificar se o usuário está logado
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser) {
          window.location.href = 'login.html';
          return;
        }

        await LoadingProgress.nextStep('Carregando parâmetros do sistema...');
        await loadSystemParameters();

        await setupRealTimeListeners();

        // Inicializar sistema de filtros (já incluído no setupRealTimeListeners)
        setTimeout(() => {
          inicializarSistemaFiltros();
        }, 500);

      } catch (error) {
        console.error('❌ Erro durante carregamento:', error);

        // Mostrar erro e esconder loading
        const loadingText = document.getElementById('loadingText');
        const progressBar = document.getElementById('progressBar');

        if (loadingText) {
          loadingText.innerHTML = `<span style="color: #e74c3c;">❌ Erro no carregamento: ${error.message}</span>`;
        }

        if (progressBar) {
          progressBar.style.background = '#e74c3c';
        }

        setTimeout(() => {
          const overlay = document.getElementById('loadingOverlay');
          if (overlay) overlay.remove();
        }, 3000);
      }
    };

    async function loadSystemParameters() {
      try {
        const docSnap = await getDoc(doc(db, "parametros", "sistema"));
        if (docSnap.exists()) {
          permitirProducaoSemEstoque = docSnap.data().permitirProducaoSemEstoque || false;
          permitirGerarEstoqueAutomatico = docSnap.data().permitirGerarEstoqueAutomatico || false;
        } else {
          // Se não existe, criar com valores padrão
          await setDoc(doc(db, "parametros", "sistema"), {
            permitirProducaoSemEstoque: false,
            permitirGerarEstoqueAutomatico: true // Habilitar por padrão
          });
          permitirGerarEstoqueAutomatico = true;
        }
        console.log(`📋 Parâmetros carregados - Gerar Estoque: ${permitirGerarEstoqueAutomatico}`);
      } catch (error) {
        console.error("Erro ao carregar parâmetros do sistema:", error);
        // Em caso de erro, habilitar por padrão
        permitirGerarEstoqueAutomatico = true;
      }
    }

    // ==================== INTEGRAÇÃO AUTOMÁTICA COM TRANSFERÊNCIAS ====================

    // Função para verificar e atualizar status de transferências automaticamente
    async function verificarEAtualizarStatusTransferencias(transferenciasRelevantes) {
      try {
        // Agrupar transferências por OP
        const transferenciasPorOP = {};
        transferenciasRelevantes.forEach(transferencia => {
          const opId = transferencia.ordemProducaoId;
          if (!transferenciasPorOP[opId]) {
            transferenciasPorOP[opId] = [];
          }
          transferenciasPorOP[opId].push(transferencia);
        });

        // Processar cada OP afetada
        for (const [opId, transferenciasOP] of Object.entries(transferenciasPorOP)) {
          await analisarEAtualizarStatusOP(opId, transferenciasOP);
        }

        // Atualizar interface após processamento
        debounceUpdate('transferenciasProcessadas');

      } catch (error) {
        console.error('❌ Erro ao processar transferências:', error);
      }
    }

    // Função para analisar e atualizar status de uma OP específica
    async function analisarEAtualizarStatusOP(opId, transferenciasOP) {
      try {
        // Buscar dados atuais da OP
        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          return;
        }

        const opData = opDoc.data();
        const materiaisNecessarios = opData.materiaisNecessarios || [];

        if (materiaisNecessarios.length === 0) {
          return;
        }

        // Buscar TODAS as transferências desta OP (consulta simplificada para evitar índice)
        const todasTransferenciasSnap = await getDocs(
          query(
            collection(db, "transferenciasArmazem"),
            where("ordemProducaoId", "==", opId)
          )
        );

        const todasTransferencias = todasTransferenciasSnap.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          .filter(transferencia =>
            transferencia.tipo === "OP" &&
            transferencia.status !== "CANCELADA"
          );

        // Analisar status de transferência por material
        const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, todasTransferencias);

        // Determinar status geral da transferência
        const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

        // Verificar se precisa atualizar a OP
        const precisaAtualizar = opData.materialTransferido !== (statusGeral !== 'nao_transferido');

        if (precisaAtualizar) {

          const dadosTransferencia = {
            statusGeral: statusGeral,
            statusMateriais: statusMateriais,
            dataRegistro: new Date().toISOString(),
            usuario: 'Sistema - Integração Automática',
            metodo: 'integracao_automatica',
            ultimaVerificacao: new Date().toISOString(),
            totalTransferencias: todasTransferencias.length
          };

          // Atualizar OP no Firebase
          await updateDoc(doc(db, "ordensProducao", opId), {
            materialTransferido: statusGeral !== 'nao_transferido',
            dadosTransferencia: dadosTransferencia,
            ultimaAtualizacaoTransferencia: Timestamp.now()
          });

          // Mostrar notificação se for transferência significativa
          if (statusGeral === 'completo') {
            mostrarNotificacao(`✅ OP ${opData.numero || opId}: Material transferido completamente`, 'success', 4000);
          } else if (statusGeral === 'parcial') {
            mostrarNotificacao(`⚠️ OP ${opData.numero || opId}: Material transferido parcialmente`, 'warning', 4000);
          }
        }

      } catch (error) {
        console.error(`❌ Erro ao analisar OP ${opId}:`, error);
      }
    }

    // Função para analisar status de transferência por material (ATUALIZADA - COMPATÍVEL COM NOVO SISTEMA)
    function analisarStatusTransferenciaMateriais(materiaisNecessarios, transferencias) {
      const statusMateriais = [];

      materiaisNecessarios.forEach(material => {
        let quantidadeTransferida = 0;
        let transferenciasDoMaterial = [];

        // 🆕 COMPATIBILIDADE COM NOVO SISTEMA: Verificar ambos os formatos
        transferencias.forEach(transferencia => {
          // Formato antigo: transferência direta por produto
          if (transferencia.produtoId === material.produtoId) {
            quantidadeTransferida += transferencia.quantidade || 0;
            transferenciasDoMaterial.push(transferencia);
          }

          // 🆕 Formato novo: transferência com array de materiais
          if (transferencia.materiais && Array.isArray(transferencia.materiais)) {
            const materialNaTransferencia = transferencia.materiais.find(m => m.produtoId === material.produtoId);
            if (materialNaTransferencia) {
              quantidadeTransferida += materialNaTransferencia.quantidade || 0;
              transferenciasDoMaterial.push({
                ...transferencia,
                quantidade: materialNaTransferencia.quantidade,
                produtoId: material.produtoId
              });
            }
          }
        });

        const quantidadeNecessaria = material.quantidade || 0;
        const percentual = quantidadeNecessaria > 0 ? (quantidadeTransferida / quantidadeNecessaria) * 100 : 0;

        let status = 'nao_transferido';
        // 🔧 CORREÇÃO: Usar tolerância para problemas de precisão decimal
        if (percentual >= 99.99) { // Tolerância para arredondamentos de floating point
          status = 'completo';
        } else if (percentual > 0) {
          status = 'parcial';
        }

        statusMateriais.push({
          produtoId: material.produtoId,
          status: status,
          quantidadeNecessaria: quantidadeNecessaria,
          quantidadeTransferida: quantidadeTransferida,
          percentual: Math.round(percentual),
          transferencias: transferenciasDoMaterial.length,
          // 🆕 Metadados adicionais
          formatosEncontrados: {
            formato_antigo: transferencias.some(t => t.produtoId === material.produtoId),
            formato_novo: transferencias.some(t => t.materiais?.some(m => m.produtoId === material.produtoId))
          }
        });
      });

      return statusMateriais;
    }

    // Função para determinar status geral da transferência
    function determinarStatusGeralTransferencia(statusMateriais) {
      if (statusMateriais.length === 0) {
        return 'nao_transferido';
      }

      const materiaisCompletos = statusMateriais.filter(m => m.status === 'completo').length;
      const materiaisParciais = statusMateriais.filter(m => m.status === 'parcial').length;
      const materiaisNaoTransferidos = statusMateriais.filter(m => m.status === 'nao_transferido').length;

      if (materiaisCompletos === statusMateriais.length) {
        return 'completo';
      } else if (materiaisCompletos > 0 || materiaisParciais > 0) {
        return 'parcial';
      } else {
        return 'nao_transferido';
      }
    }

    // 🔧 Função para verificar transferência de uma OP específica (DEBUG)
    window.verificarTransferenciaOP = async function(opId) {
      try {

        // Buscar dados da OP
        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          alert(`❌ OP ${opId} não encontrada`);
          return;
        }

        const opData = opDoc.data();
        const materiaisNecessarios = opData.materiaisNecessarios || [];

        // Buscar todas as transferências (consulta simplificada)
        const transferenciasSnap = await getDocs(
          query(
            collection(db, "transferenciasArmazem"),
            where("ordemProducaoId", "==", opId)
          )
        );

        const transferencias = transferenciasSnap.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          .filter(transferencia =>
            transferencia.tipo === "OP" &&
            transferencia.status !== "CANCELADA"
          );

        // Analisar status
        const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferencias);
        const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

        // Mostrar resultado detalhado (apenas para debug específico)

        let detalhes = `🔍 ANÁLISE DE TRANSFERÊNCIA\n\nOP: ${opData.numero || opId}\n\n`;
        detalhes += `📦 Materiais necessários: ${materiaisNecessarios.length}\n`;
        detalhes += `🔄 Transferências encontradas: ${transferencias.length}\n\n`;

        // Separar materiais por status
        const materiaisCompletos = statusMateriais.filter(m => m.status === 'completo');
        const materiaisParciais = statusMateriais.filter(m => m.status === 'parcial');
        const materiaisNaoTransferidos = statusMateriais.filter(m => m.status === 'nao_transferido');

        detalhes += `📊 RESUMO:\n`;
        detalhes += `✅ Completos: ${materiaisCompletos.length}\n`;
        detalhes += `⚠️ Parciais: ${materiaisParciais.length}\n`;
        detalhes += `❌ Não transferidos: ${materiaisNaoTransferidos.length}\n\n`;

        // Mostrar apenas materiais problemáticos
        if (materiaisNaoTransferidos.length > 0) {
          detalhes += `❌ MATERIAIS NÃO TRANSFERIDOS:\n`;
          materiaisNaoTransferidos.forEach((mat, index) => {
            const produto = produtos.find(p => p.id === mat.produtoId);
            const codigo = produto?.codigo || mat.produtoId;
            const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';

            detalhes += `${index + 1}. ${codigo}`;
            if (ehSubproduto) {
              detalhes += ` (SUBPRODUTO)`;
            }
            detalhes += `\n`;
            detalhes += `   Necessário: ${mat.quantidadeNecessaria}\n`;
            detalhes += `   Transferido: ${mat.quantidadeTransferida}\n`;
            if (ehSubproduto) {
              detalhes += `   💡 Precisa produzir/apontar este SP\n`;
            }
            detalhes += `\n`;
          });
        }

        if (materiaisParciais.length > 0) {
          detalhes += `⚠️ MATERIAIS PARCIAIS:\n`;
          materiaisParciais.forEach((mat, index) => {
            const produto = produtos.find(p => p.id === mat.produtoId);
            detalhes += `${index + 1}. ${produto?.codigo || mat.produtoId}\n`;
            detalhes += `   Necessário: ${mat.quantidadeNecessaria}\n`;
            detalhes += `   Transferido: ${mat.quantidadeTransferida}\n`;
            detalhes += `   Percentual: ${mat.percentual}%\n\n`;
          });
        }

        detalhes += `🎯 STATUS GERAL: ${statusGeral.toUpperCase()}\n`;
        detalhes += `📋 Status atual na OP: ${opData.materialTransferido ? 'Transferido' : 'Não transferido'}\n\n`;

        if (statusGeral === 'parcial') {
          detalhes += `💡 EXPLICAÇÃO:\n`;
          detalhes += `O status "PARCIAL" significa que alguns materiais\n`;
          detalhes += `ainda não foram transferidos ou estão incompletos.\n\n`;

          // Analisar tipos de materiais faltantes
          const temSubprodutos = materiaisNaoTransferidos.some(mat => {
            const produto = produtos.find(p => p.id === mat.produtoId);
            const codigo = produto?.codigo || mat.produtoId;
            return codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
          });

          const temMateriaPrima = materiaisNaoTransferidos.some(mat => {
            const produto = produtos.find(p => p.id === mat.produtoId);
            const codigo = produto?.codigo || mat.produtoId;
            return !codigo.includes('-SP') && !codigo.includes('SP-') && produto?.tipo !== 'SP';
          });

          detalhes += `🔧 AÇÕES NECESSÁRIAS:\n`;
          if (temMateriaPrima) {
            detalhes += `• Transferir matérias-primas faltantes do estoque\n`;
          }
          if (temSubprodutos) {
            detalhes += `• Verificar se há OPs de SP (subprodutos) pendentes\n`;
            detalhes += `• Apontar produção dos subprodutos necessários\n`;
            detalhes += `• Aguardar conclusão das OPs de SP\n`;
          }
          detalhes += `• Após resolver, o status mudará automaticamente`;
        }

        alert(detalhes);

        // Forçar atualização se necessário
        if (confirm('🔄 Deseja forçar a atualização do status desta OP?')) {
          await analisarEAtualizarStatusOP(opId, transferencias);
          alert('✅ Status atualizado! Recarregue a página para ver as mudanças.');
        }

      } catch (error) {
        console.error('❌ Erro ao verificar OP:', error);
        alert('❌ Erro ao verificar OP: ' + error.message);
      }
    };

    // Função para verificar transferências de todas as OPs manualmente
    window.verificarTodasTransferencias = async function() {
      try {
        if (!confirm('🔍 VERIFICAR TRANSFERÊNCIAS\n\nEsta função irá verificar o status de transferências de todas as Ordens de Produção ativas e atualizar os flags automaticamente.\n\nEste processo pode levar alguns segundos.\n\nDeseja continuar?')) {
          return;
        }

        mostrarNotificacao('🔄 Verificando transferências de todas as OPs...', 'info', 5000);

        console.log('🔍 Iniciando verificação manual de transferências...');

        // Buscar todas as OPs ativas
        const opsAtivasSnap = await getDocs(
          query(
            collection(db, "ordensProducao"),
            where("status", "in", ["Aguardando", "Em Produção", "Pausada"])
          )
        );

        const opsAtivas = opsAtivasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        console.log(`📋 Encontradas ${opsAtivas.length} OPs ativas para verificação`);

        let opsProcessadas = 0;
        let opsAtualizadas = 0;

        // Processar cada OP
        for (const op of opsAtivas) {
          try {
            // Buscar transferências desta OP
            const transferenciasSnap = await getDocs(
              query(
                collection(db, "transferenciasArmazem"),
                where("ordemProducaoId", "==", op.id),
                where("tipo", "==", "OP"),
                where("status", "!=", "CANCELADA")
              )
            );

            const transferencias = transferenciasSnap.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            if (transferencias.length > 0) {
              console.log(`📦 OP ${op.numero || op.id}: ${transferencias.length} transferência(s) encontrada(s)`);

              // Analisar e atualizar se necessário
              const materiaisNecessarios = op.materiaisNecessarios || [];
              if (materiaisNecessarios.length > 0) {
                const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferencias);
                const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

                // Verificar se precisa atualizar
                const precisaAtualizar = op.materialTransferido !== (statusGeral !== 'nao_transferido');

                if (precisaAtualizar) {
                  const dadosTransferencia = {
                    statusGeral: statusGeral,
                    statusMateriais: statusMateriais,
                    dataRegistro: new Date().toISOString(),
                    usuario: 'Sistema - Verificação Manual',
                    metodo: 'verificacao_manual',
                    ultimaVerificacao: new Date().toISOString(),
                    totalTransferencias: transferencias.length
                  };

                  await updateDoc(doc(db, "ordensProducao", op.id), {
                    materialTransferido: statusGeral !== 'nao_transferido',
                    dadosTransferencia: dadosTransferencia,
                    ultimaAtualizacaoTransferencia: Timestamp.now()
                  });

                  opsAtualizadas++;
                  console.log(`✅ OP ${op.numero || op.id} atualizada: ${statusGeral}`);
                }
              }
            }

            opsProcessadas++;

          } catch (error) {
            console.error(`❌ Erro ao processar OP ${op.id}:`, error);
          }
        }

        console.log(`✅ Verificação concluída: ${opsProcessadas} OPs processadas, ${opsAtualizadas} atualizadas`);

        mostrarNotificacao(`✅ Verificação concluída: ${opsAtualizadas} OPs atualizadas de ${opsProcessadas} processadas`, 'success', 6000);

        // Atualizar interface
        await debounceUpdate('verificacaoManual');

      } catch (error) {
        console.error('❌ Erro na verificação de transferências:', error);
        mostrarNotificacao('❌ Erro ao verificar transferências: ' + error.message, 'error', 5000);
      }
    };

    // Função para processar transferências históricas (já realizadas)
    window.processarTransferenciasHistoricas = async function() {
      try {
        const confirmacao = confirm(`🕒 PROCESSAR TRANSFERÊNCIAS HISTÓRICAS

Esta função irá:
• Buscar TODAS as transferências já realizadas no sistema
• Analisar quais OPs foram afetadas
• Atualizar os flags de material transferido baseado no histórico real
• Processar transferências que podem ter sido feitas antes da integração automática

⚠️ IMPORTANTE:
- Este processo pode levar alguns minutos dependendo da quantidade de transferências
- Irá sobrescrever flags que foram marcados manualmente
- Recomendado fazer backup antes de executar

🔍 Deseja continuar com o processamento do histórico?`);

        if (!confirmacao) {
          return;
        }

        mostrarNotificacao('🕒 Iniciando processamento de transferências históricas...', 'info', 8000);
        console.log('🕒 Iniciando processamento de transferências históricas...');

        // 1. Buscar TODAS as transferências do sistema
        mostrarNotificacao('📦 Carregando todas as transferências...', 'info', 5000);
        const todasTransferenciasSnap = await getDocs(collection(db, "transferenciasArmazem"));
        const todasTransferencias = todasTransferenciasSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📦 Total de transferências encontradas: ${todasTransferencias.length}`);

        // 2. Filtrar transferências para OPs válidas
        const transferenciasParaOPs = todasTransferencias.filter(t =>
          t.ordemProducaoId &&
          t.tipo === 'OP' &&
          t.status !== 'CANCELADA'
        );

        console.log(`🎯 Transferências para OPs válidas: ${transferenciasParaOPs.length}`);

        if (transferenciasParaOPs.length === 0) {
          mostrarNotificacao('ℹ️ Nenhuma transferência histórica para OPs encontrada', 'info', 4000);
          return;
        }

        // 3. Agrupar por OP
        const transferenciasAgrupadasPorOP = {};
        transferenciasParaOPs.forEach(transferencia => {
          const opId = transferencia.ordemProducaoId;
          if (!transferenciasAgrupadasPorOP[opId]) {
            transferenciasAgrupadasPorOP[opId] = [];
          }
          transferenciasAgrupadasPorOP[opId].push(transferencia);
        });

        const opsAfetadas = Object.keys(transferenciasAgrupadasPorOP);
        console.log(`📋 OPs afetadas por transferências: ${opsAfetadas.length}`);

        mostrarNotificacao(`🔄 Processando ${opsAfetadas.length} OPs com transferências históricas...`, 'info', 6000);

        // 4. Processar cada OP
        let opsProcessadas = 0;
        let opsAtualizadas = 0;
        let opsComErro = 0;

        for (const opId of opsAfetadas) {
          try {
            console.log(`📋 Processando OP: ${opId}`);

            // Buscar dados da OP
            const opDoc = await getDoc(doc(db, "ordensProducao", opId));
            if (!opDoc.exists()) {
              console.warn(`⚠️ OP ${opId} não encontrada no banco`);
              opsComErro++;
              continue;
            }

            const opData = opDoc.data();
            const materiaisNecessarios = opData.materiaisNecessarios || [];

            if (materiaisNecessarios.length === 0) {
              console.log(`📋 OP ${opId} não possui materiais necessários`);
              opsProcessadas++;
              continue;
            }

            // Analisar transferências desta OP
            const transferenciasOP = transferenciasAgrupadasPorOP[opId];
            const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferenciasOP);
            const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

            // Preparar dados da transferência histórica
            const dadosTransferencia = {
              statusGeral: statusGeral,
              statusMateriais: statusMateriais,
              dataRegistro: new Date().toISOString(),
              usuario: 'Sistema - Processamento Histórico',
              metodo: 'processamento_historico',
              ultimaVerificacao: new Date().toISOString(),
              totalTransferencias: transferenciasOP.length,
              primeiraTransferencia: transferenciasOP.length > 0 ?
                (transferenciasOP[0].dataHora?.toDate?.()?.toISOString() || 'Data não disponível') : null,
              ultimaTransferencia: transferenciasOP.length > 0 ?
                (transferenciasOP[transferenciasOP.length - 1].dataHora?.toDate?.()?.toISOString() || 'Data não disponível') : null
            };

            // Atualizar OP no Firebase
            await updateDoc(doc(db, "ordensProducao", opId), {
              materialTransferido: statusGeral !== 'nao_transferido',
              dadosTransferencia: dadosTransferencia,
              ultimaAtualizacaoTransferencia: Timestamp.now(),
              processamentoHistoricoRealizado: true,
              dataProcessamentoHistorico: Timestamp.now()
            });

            opsAtualizadas++;
            console.log(`✅ OP ${opId} processada: ${statusGeral} (${transferenciasOP.length} transferências)`);

          } catch (error) {
            console.error(`❌ Erro ao processar OP ${opId}:`, error);
            opsComErro++;
          }

          opsProcessadas++;

          // Mostrar progresso a cada 10 OPs
          if (opsProcessadas % 10 === 0) {
            mostrarNotificacao(`🔄 Progresso: ${opsProcessadas}/${opsAfetadas.length} OPs processadas...`, 'info', 3000);
          }
        }

        // 5. Relatório final
        const relatorio = `✅ PROCESSAMENTO HISTÓRICO CONCLUÍDO

📊 ESTATÍSTICAS:
• Total de transferências analisadas: ${transferenciasParaOPs.length}
• OPs processadas: ${opsProcessadas}
• OPs atualizadas: ${opsAtualizadas}
• OPs com erro: ${opsComErro}

🎯 RESULTADO:
${opsAtualizadas} OPs tiveram seus flags atualizados baseado no histórico real de transferências.

⚡ A interface será atualizada automaticamente.`;

        console.log(relatorio);
        alert(relatorio);

        mostrarNotificacao(`✅ Histórico processado: ${opsAtualizadas} OPs atualizadas`, 'success', 8000);

        // 6. Atualizar interface
        await debounceUpdate('processamentoHistorico');

      } catch (error) {
        console.error('❌ Erro no processamento histórico:', error);
        mostrarNotificacao('❌ Erro no processamento histórico: ' + error.message, 'error', 8000);
      }
    };

    // 🩺 FUNÇÃO DE DIAGNÓSTICO ESPECÍFICA PARA PROBLEMAS DE INTEGRAÇÃO
    window.diagnosticarProblemasIntegracao = async function() {
      try {
        mostrarNotificacao('🩺 Executando diagnóstico de integração...', 'info', 5000);
        console.log('🩺 Iniciando diagnóstico de problemas de integração...');

        // 1. Verificar transferências com botão "Gerar Estoque" que podem não estar sendo reconhecidas
        const movimentacoesGerarEstoque = await getDocs(
          query(
            collection(db, "movimentacoesEstoque"),
            where("tipoDocumento", "==", "TRANSFERENCIA"),
            where("observacoes", ">=", "Transferência automática"),
            where("observacoes", "<", "Transferência automáticb") // Range query
          )
        );

        console.log(`🔍 Movimentações de "Gerar Estoque" encontradas: ${movimentacoesGerarEstoque.docs.length}`);

        // 2. Verificar se essas transferências existem na coleção transferenciasArmazem
        const transferenciasOrfas = [];
        const opsAfetadas = new Set();

        for (const movDoc of movimentacoesGerarEstoque.docs) {
          const mov = movDoc.data();

          // Extrair OP da observação
          const matchOP = mov.observacoes?.match(/OP\s+([^\s]+)/);
          if (matchOP) {
            const numeroOP = matchOP[1];

            // Buscar se existe transferência correspondente
            const transferenciasCorrespondentes = await getDocs(
              query(
                collection(db, "transferenciasArmazem"),
                where("numeroDocumentoMovimentacao", "==", mov.numeroDocumento)
              )
            );

            if (transferenciasCorrespondentes.empty) {
              transferenciasOrfas.push({
                movimentacao: mov,
                numeroOP: numeroOP,
                numeroDocumento: mov.numeroDocumento,
                dataHora: mov.dataHora,
                produtoId: mov.produtoId,
                quantidade: mov.quantidade
              });

              // Buscar ID da OP pelo número
              const opQuery = await getDocs(
                query(
                  collection(db, "ordensProducao"),
                  where("numero", "==", numeroOP)
                )
              );

              if (!opQuery.empty) {
                opsAfetadas.add(opQuery.docs[0].id);
              }
            }
          }
        }

        console.log(`🚨 Transferências órfãs encontradas: ${transferenciasOrfas.length}`);
        console.log(`📋 OPs afetadas: ${opsAfetadas.size}`);

        // 3. Verificar OPs com flag incorreto
        const opsComProblema = [];
        for (const opId of opsAfetadas) {
          const opDoc = await getDoc(doc(db, "ordensProducao", opId));
          if (opDoc.exists()) {
            const opData = opDoc.data();
            if (!opData.materialTransferido) {
              opsComProblema.push({
                id: opId,
                numero: opData.numero,
                status: opData.status,
                materialTransferido: opData.materialTransferido
              });
            }
          }
        }

        // 4. Gerar relatório detalhado
        const relatorio = `🩺 DIAGNÓSTICO DE INTEGRAÇÃO CONCLUÍDO

🔍 PROBLEMAS DETECTADOS:

📦 TRANSFERÊNCIAS ÓRFÃS:
• ${transferenciasOrfas.length} transferências de "Gerar Estoque" não registradas na coleção transferenciasArmazem
• Estas transferências existem apenas em movimentacoesEstoque

📋 OPS AFETADAS:
• ${opsComProblema.length} OPs têm transferências não reconhecidas
• Estas OPs mostram "Material não transferido" mas possuem transferências

🛠️ AÇÕES DISPONÍVEIS:
1. "Corrigir Transferências Órfãs" - Criar registros na coleção transferenciasArmazem
2. "Atualizar Flags das OPs" - Corrigir flags materialTransferido
3. "Correção Completa" - Executar ambas as ações

Deseja ver a lista detalhada dos problemas?`;

        if (confirm(relatorio)) {
          // Mostrar detalhes
          let detalhes = `📋 DETALHES DOS PROBLEMAS:\n\n`;

          if (transferenciasOrfas.length > 0) {
            detalhes += `🚨 TRANSFERÊNCIAS ÓRFÃS:\n`;
            transferenciasOrfas.slice(0, 10).forEach(item => {
              detalhes += `• OP ${item.numeroOP} - Produto ${item.produtoId} - Qtd: ${item.quantidade}\n`;
            });
            if (transferenciasOrfas.length > 10) {
              detalhes += `... e mais ${transferenciasOrfas.length - 10} transferências\n`;
            }
          }

          if (opsComProblema.length > 0) {
            detalhes += `\n📋 OPS COM FLAG INCORRETO:\n`;
            opsComProblema.forEach(op => {
              detalhes += `• ${op.numero} (${op.status}) - Flag: ${op.materialTransferido}\n`;
            });
          }

          alert(detalhes);

          // Oferecer opções de correção
          const opcaoCorrecao = prompt(`🛠️ ESCOLHA UMA OPÇÃO DE CORREÇÃO:

1 - Corrigir apenas transferências órfãs
2 - Corrigir apenas flags das OPs  
3 - Correção completa (ambos)
4 - Apenas mostrar relatório (não corrigir)

Digite o número da opção (1-4):`);

          switch (opcaoCorrecao) {
            case '1':
              await corrigirTransferenciasOrfas(transferenciasOrfas);
              break;
            case '2':
              await corrigirFlagsOPs(Array.from(opsAfetadas));
              break;
            case '3':
              await corrigirTransferenciasOrfas(transferenciasOrfas);
              await corrigirFlagsOPs(Array.from(opsAfetadas));
              mostrarNotificacao('✅ Correção completa realizada!', 'success', 5000);
              break;
            case '4':
              console.log('Diagnóstico concluído - nenhuma correção aplicada');
              break;
            default:
              console.log('Opção inválida');
          }

          // Atualizar interface
          await debounceUpdate('diagnosticoIntegracao');
        }

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        mostrarNotificacao('❌ Erro no diagnóstico: ' + error.message, 'error', 5000);
      }
    };

    // Função para corrigir transferências órfãs
    async function corrigirTransferenciasOrfas(transferenciasOrfas) {
      try {
        mostrarNotificacao('🔧 Corrigindo transferências órfãs...', 'info', 5000);
        console.log(`🔧 Corrigindo ${transferenciasOrfas.length} transferências órfãs...`);

        let corrigidas = 0;

        for (const item of transferenciasOrfas) {
          try {
            // Buscar dados do produto
            const produto = produtos.find(p => p.id === item.produtoId) || { codigo: item.produtoId, descricao: 'Produto' };

            // Buscar dados da OP
            const opQuery = await getDocs(
              query(
                collection(db, "ordensProducao"),
                where("numero", "==", item.numeroOP)
              )
            );

            if (opQuery.empty) continue;

            const opData = opQuery.docs[0].data();
            const opId = opQuery.docs[0].id;

            // Determinar armazéns (usar os mesmos padrões do sistema)
            const ALM01 = armazens.find(a => a.codigo === 'ALM01');
            const PROD1 = armazens.find(a => a.id === opData.armazemProducaoId);

            if (!ALM01 || !PROD1) continue;

            // Criar registro de transferência
            const transferencia = {
              numeroTransferencia: item.numeroDocumento,
              tipo: 'OP',
              ordemProducaoId: opId,
              armazemOrigemId: ALM01.id,
              armazemDestinoId: PROD1.id,
              dataTransferencia: item.dataHora,
              usuario: 'Sistema - Correção Automática',
              status: 'CONCLUIDA',
              observacoes: `Transferência corrigida automaticamente - OP ${item.numeroOP}`,

              materiais: [{
                produtoId: item.produtoId,
                codigo: produto.codigo || item.produtoId,
                descricao: produto.descricao || 'Produto',
                quantidade: item.quantidade,
                unidade: produto.unidade || 'UN',
                valorUnitario: produto.valorUnitario || 0
              }],

              quantidadeTotalItens: 1,
              valorTotal: (produto.valorUnitario || 0) * item.quantidade,

              origem: 'CORRECAO_SISTEMA',
              integradoComMovimentacoes: true,
              numeroDocumentoMovimentacao: item.numeroDocumento,
              corrigidoEm: Timestamp.now()
            };

            await addDoc(collection(db, "transferenciasArmazem"), transferencia);
            corrigidas++;

            console.log(`✅ Transferência corrigida: OP ${item.numeroOP} - Produto ${item.produtoId}`);

          } catch (error) {
            console.error(`❌ Erro ao corrigir transferência da OP ${item.numeroOP}:`, error);
          }
        }

        mostrarNotificacao(`✅ ${corrigidas} transferências órfãs corrigidas`, 'success', 5000);
        console.log(`✅ Correção concluída: ${corrigidas} transferências corrigidas`);

      } catch (error) {
        console.error('❌ Erro ao corrigir transferências órfãs:', error);
        mostrarNotificacao('❌ Erro na correção: ' + error.message, 'error', 5000);
      }
    }

    // Função para corrigir flags das OPs
    async function corrigirFlagsOPs(opsIds) {
      try {
        mostrarNotificacao('🔧 Corrigindo flags das OPs...', 'info', 5000);
        console.log(`🔧 Corrigindo flags de ${opsIds.length} OPs...`);

        let corrigidas = 0;

        for (const opId of opsIds) {
          try {
            // Re-analisar transferências da OP
            const transferenciasSnap = await getDocs(
              query(
                collection(db, "transferenciasArmazem"),
                where("ordemProducaoId", "==", opId),
                where("tipo", "==", "OP"),
                where("status", "!=", "CANCELADA")
              )
            );

            const transferencias = transferenciasSnap.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            // Buscar dados da OP
            const opDoc = await getDoc(doc(db, "ordensProducao", opId));
            if (!opDoc.exists()) continue;

            const opData = opDoc.data();
            const materiaisNecessarios = opData.materiaisNecessarios || [];

            if (materiaisNecessarios.length === 0 || transferencias.length === 0) continue;

            // Analisar status
            const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferencias);
            const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

            // Atualizar OP
            const dadosTransferencia = {
              statusGeral: statusGeral,
              statusMateriais: statusMateriais,
              dataRegistro: new Date().toISOString(),
              usuario: 'Sistema - Correção de Flags',
              metodo: 'correcao_flags',
              ultimaVerificacao: new Date().toISOString(),
              totalTransferencias: transferencias.length
            };

            await updateDoc(doc(db, "ordensProducao", opId), {
              materialTransferido: statusGeral !== 'nao_transferido',
              dadosTransferencia: dadosTransferencia,
              ultimaAtualizacaoTransferencia: Timestamp.now(),
              flagCorrigidoEm: Timestamp.now()
            });

            corrigidas++;
            console.log(`✅ Flag corrigido: OP ${opData.numero || opId} - Status: ${statusGeral}`);

          } catch (error) {
            console.error(`❌ Erro ao corrigir flag da OP ${opId}:`, error);
          }
        }

        mostrarNotificacao(`✅ ${corrigidas} flags de OPs corrigidos`, 'success', 5000);
        console.log(`✅ Correção de flags concluída: ${corrigidas} OPs corrigidas`);

      } catch (error) {
        console.error('❌ Erro ao corrigir flags:', error);
        mostrarNotificacao('❌ Erro na correção de flags: ' + error.message, 'error', 5000);
      }
    }

    // Função para gerar relatório de transferências históricas
    window.gerarRelatorioTransferencias = async function() {
      try {
        mostrarNotificacao('📊 Gerando relatório de transferências...', 'info', 4000);

        // Buscar todas as transferências
        const transferenciasSnap = await getDocs(collection(db, "transferenciasArmazem"));
        const todasTransferencias = transferenciasSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Buscar todas as OPs
        const opsSnap = await getDocs(collection(db, "ordensProducao"));
        const todasOPs = opsSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Filtrar transferências para OPs
        const transferenciasParaOPs = todasTransferencias.filter(t =>
          t.ordemProducaoId && t.tipo === 'OP' && t.status !== 'CANCELADA'
        );

        // Agrupar por OP
        const transferenciasAgrupadasPorOP = {};
        transferenciasParaOPs.forEach(transferencia => {
          const opId = transferencia.ordemProducaoId;
          if (!transferenciasAgrupadasPorOP[opId]) {
            transferenciasAgrupadasPorOP[opId] = [];
          }
          transferenciasAgrupadasPorOP[opId].push(transferencia);
        });

        // Analisar cada OP
        const analiseOPs = [];
        for (const [opId, transferenciasOP] of Object.entries(transferenciasAgrupadasPorOP)) {
          const op = todasOPs.find(o => o.id === opId);
          if (!op) continue;

          const materiaisNecessarios = op.materiaisNecessarios || [];
          let statusCalculado = 'nao_transferido';

          if (materiaisNecessarios.length > 0) {
            const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferenciasOP);
            statusCalculado = determinarStatusGeralTransferencia(statusMateriais);
          }

          const statusAtual = op.materialTransferido ? 'transferido' : 'nao_transferido';
          const precisaAtualizar = (statusCalculado !== 'nao_transferido') !== op.materialTransferido;

          analiseOPs.push({
            opId: opId,
            numero: op.numero || opId,
            status: op.status,
            statusAtual: statusAtual,
            statusCalculado: statusCalculado,
            precisaAtualizar: precisaAtualizar,
            totalTransferencias: transferenciasOP.length,
            materiaisNecessarios: materiaisNecessarios.length,
            primeiraTransferencia: transferenciasOP.length > 0 ?
              (transferenciasOP[0].dataHora?.toDate?.() || new Date()) : null,
            ultimaTransferencia: transferenciasOP.length > 0 ?
              (transferenciasOP[transferenciasOP.length - 1].dataHora?.toDate?.() || new Date()) : null
          });
        }

        // Estatísticas
        const totalOPsComTransferencias = analiseOPs.length;
        const opsQuePrecisamAtualizar = analiseOPs.filter(op => op.precisaAtualizar).length;
        const opsCompletas = analiseOPs.filter(op => op.statusCalculado === 'completo').length;
        const opsParciais = analiseOPs.filter(op => op.statusCalculado === 'parcial').length;

        // Gerar relatório
        let relatorio = `📊 RELATÓRIO DE TRANSFERÊNCIAS HISTÓRICAS

📈 ESTATÍSTICAS GERAIS:
• Total de transferências no sistema: ${todasTransferencias.length}
• Transferências para OPs: ${transferenciasParaOPs.length}
• OPs com transferências: ${totalOPsComTransferencias}
• OPs que precisam atualizar: ${opsQuePrecisamAtualizar}

🎯 STATUS CALCULADO:
• OPs com material completo: ${opsCompletas}
• OPs com material parcial: ${opsParciais}
• OPs sem transferência: ${totalOPsComTransferencias - opsCompletas - opsParciais}

`;

        if (opsQuePrecisamAtualizar > 0) {
          relatorio += `\n🔄 OPs QUE SERÃO ATUALIZADAS:\n`;

          const opsParaAtualizar = analiseOPs.filter(op => op.precisaAtualizar).slice(0, 20); // Mostrar apenas as primeiras 20

          opsParaAtualizar.forEach(op => {
            relatorio += `• ${op.numero} - ${op.statusAtual} → ${op.statusCalculado} (${op.totalTransferencias} transferências)\n`;
          });

          if (opsQuePrecisamAtualizar > 20) {
            relatorio += `... e mais ${opsQuePrecisamAtualizar - 20} OPs\n`;
          }
        }

        relatorio += `\n💡 RECOMENDAÇÃO:
${opsQuePrecisamAtualizar > 0 ?
  'Execute o "Processar Histórico" para atualizar os flags baseado nas transferências reais.' :
  'Todos os flags estão atualizados conforme as transferências históricas.'}`;

        console.log(relatorio);
        alert(relatorio);

        mostrarNotificacao(`📊 Relatório gerado: ${opsQuePrecisamAtualizar} OPs precisam atualizar`, 'info', 6000);

      } catch (error) {
        console.error('❌ Erro ao gerar relatório:', error);
        mostrarNotificacao('❌ Erro ao gerar relatório: ' + error.message, 'error', 5000);
      }
    };

    // ===================================================================
    // SISTEMA DE ATUALIZAÇÃO EM TEMPO REAL MELHORADO
    // ===================================================================

    let unsubscribeFunctions = []; // Para gerenciar listeners
    let lastUpdateTime = Date.now();
    let isUpdating = false;
    let transferenciasTimeout; // Para debounce das transferências

    // Sistema de Progresso de Carregamento
    const LoadingProgress = {
      steps: [
        { name: 'Inicializando conexão com Firebase...', duration: 500 },
        { name: 'Carregando parâmetros do sistema...', duration: 800 },
        { name: 'Configurando listeners em tempo real...', duration: 1000 },
        { name: 'Carregando produtos...', duration: 1200 },
        { name: 'Carregando ordens de produção...', duration: 1500 },
        { name: 'Carregando estoques...', duration: 1000 },
        { name: 'Carregando dados auxiliares...', duration: 800 },
        { name: 'Inicializando sistema de filtros...', duration: 500 },
        { name: 'Finalizando carregamento...', duration: 300 }
      ],
      currentStep: 0,

      updateProgress(stepIndex, customText = null) {
        const progressBar = document.getElementById('progressBar');
        const progressPercent = document.getElementById('progressPercent');
        const loadingText = document.getElementById('loadingText');

        if (!progressBar || !progressPercent || !loadingText) return;

        const progress = Math.round(((stepIndex + 1) / this.steps.length) * 100);
        const text = customText || this.steps[stepIndex]?.name || 'Processando...';

        progressBar.style.width = `${progress}%`;
        progressPercent.textContent = `${progress}%`;
        loadingText.textContent = text;

        console.log(`📊 Progresso: ${progress}% - ${text}`);
      },

      async nextStep(customText = null) {
        this.updateProgress(this.currentStep, customText);

        // Simular tempo de processamento para cada etapa
        if (this.currentStep < this.steps.length) {
          const stepDuration = this.steps[this.currentStep]?.duration || 500;
          await new Promise(resolve => setTimeout(resolve, Math.min(stepDuration, 300))); // Máximo 300ms por etapa
        }

        this.currentStep++;
      },

      complete() {
        this.updateProgress(this.steps.length - 1, 'Carregamento concluído!');

        setTimeout(() => {
          const overlay = document.getElementById('loadingOverlay');
          if (overlay) {
            overlay.style.opacity = '0';
            overlay.style.transition = 'opacity 0.5s ease';
            setTimeout(() => overlay.remove(), 500);
          }

          // Aplicar animação de entrada aos elementos principais
          document.querySelector('.container').classList.add('fade-in-up');
        }, 200);
      }
    };

    async function setupRealTimeListeners() {
      try {
        await LoadingProgress.nextStep();

        // Limpar listeners anteriores
        unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
        unsubscribeFunctions = [];

        await LoadingProgress.nextStep();

        // 1. LISTENER PARA PRODUTOS
        await LoadingProgress.nextStep('Configurando listener de produtos...');
        const unsubscribeProdutos = onSnapshot(collection(db, "produtos"), (snap) => {
          produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('produtos');
        });
        unsubscribeFunctions.push(unsubscribeProdutos);

        // 2. LISTENER PARA ORDENS DE PRODUÇÃO (PRINCIPAL)
        await LoadingProgress.nextStep('Configurando listener de ordens de produção...');
        const unsubscribeOPs = onSnapshot(collection(db, "ordensProducao"), (snap) => {
          ordensProducao = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('ordensProducao');
        });
        unsubscribeFunctions.push(unsubscribeOPs);

        // 3. LISTENER PARA ESTOQUES
        await LoadingProgress.nextStep('Configurando listener de estoques...');
        const unsubscribeEstoques = onSnapshot(collection(db, "estoques"), (snap) => {
          estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('estoques');
        });
        unsubscribeFunctions.push(unsubscribeEstoques);

        // 4. LISTENER PARA TRANSFERÊNCIAS DE ARMAZÉM (INTEGRAÇÃO AUTOMÁTICA)
        const unsubscribeTransferencias = onSnapshot(collection(db, "transferenciasArmazem"), (snapshot) => {
          // Processar apenas mudanças recentes para evitar sobrecarga
          const changes = snapshot.docChanges();
          const transferenciasRelevantes = [];

          changes.forEach(change => {
            if (change.type === 'added' || change.type === 'modified') {
              const transferencia = { id: change.doc.id, ...change.doc.data() };

              // Verificar se é transferência para OP e não cancelada
              if (transferencia.ordemProducaoId &&
                  transferencia.tipo === 'OP' &&
                  transferencia.status !== 'CANCELADA') {
                transferenciasRelevantes.push(transferencia);
              }
            }
          });

          if (transferenciasRelevantes.length > 0) {
            // Aguardar um pouco para evitar múltiplas atualizações simultâneas
            clearTimeout(transferenciasTimeout);
            transferenciasTimeout = setTimeout(async () => {
              await verificarEAtualizarStatusTransferencias(transferenciasRelevantes);
            }, 2000);
          }
        });
        unsubscribeFunctions.push(unsubscribeTransferencias);

        // 4. LISTENER PARA EMPENHOS
        const unsubscribeEmpenhos = onSnapshot(collection(db, "empenhos"), (snap) => {
          empenhos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          debounceUpdate('empenhos');
        });
        unsubscribeFunctions.push(unsubscribeEmpenhos);

        // 5. LISTENERS PARA DADOS AUXILIARES
        await LoadingProgress.nextStep('Carregando dados auxiliares...');
        const unsubscribeArmazens = onSnapshot(collection(db, "armazens"), (snap) => {
          armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeArmazens);

        const unsubscribeEstruturas = onSnapshot(collection(db, "estruturas"), (snap) => {
          estruturas = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeEstruturas);

        const unsubscribeOperacoes = onSnapshot(collection(db, "operacoes"), (snap) => {
          operacoes = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeOperacoes);

        const unsubscribeRecursos = onSnapshot(collection(db, "recursos"), (snap) => {
          recursos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        });
        unsubscribeFunctions.push(unsubscribeRecursos);

        // Carregar dados iniciais
        await LoadingProgress.nextStep('Carregando dados das ordens...');
        await loadOrders();

        // Configurar event listeners para detectar nova busca
        await LoadingProgress.nextStep('Configurando sistema de filtros...');
        setupSearchListeners();
        atualizarDashboard();

        // Finalizar carregamento
        await LoadingProgress.nextStep('Finalizando...');
        LoadingProgress.complete();

      } catch (error) {
        console.error("❌ Erro ao configurar listeners:", error);
        mostrarNotificacao('❌ Erro ao configurar atualização automática', 'error', 5000);
      }
    }

    // Função para debounce das atualizações (evita atualizações excessivas)
    function debounceUpdate(source) {
      if (isUpdating) return;

      const now = Date.now();
      if (now - lastUpdateTime < 3000) { // Mínimo 3 segundos entre atualizações
        return;
      }

      lastUpdateTime = now;
      isUpdating = true;



      setTimeout(async () => {
        try {
          // Apenas atualizar dados sem mexer nos filtros
          await loadOrders();
          atualizarDashboard();

        } catch (error) {
          console.error('Erro na atualização:', error);
        } finally {
          isUpdating = false;
        }
      }, 100);
    }

    // Função para forçar atualização manual
    window.forcarAtualizacao = async function() {

      mostrarNotificacao('🔄 Atualizando dados...', 'info', 1000);

      try {
        await setupRealTimeListeners();
        mostrarNotificacao('✅ Dados atualizados com sucesso!', 'success', 2000);
      } catch (error) {
        console.error('Erro na atualização manual:', error);
        mostrarNotificacao('❌ Erro ao atualizar dados', 'error', 3000);
      }
    };

    // Listeners de visibilidade removidos para evitar atualizações excessivas

    // ===================================================================
    // MONITORAMENTO DE CONEXÃO
    // ===================================================================

    function atualizarStatusConexao(online = true) {
      const statusIndicator = document.getElementById('connectionStatus');
      if (statusIndicator) {
        if (online) {
          statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> Online';
          statusIndicator.style.color = '#28a745';
        } else {
          statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #dc3545;"></i> Offline';
          statusIndicator.style.color = '#dc3545';
        }
      }
    }

    // Monitorar conexão com a internet
    window.addEventListener('online', function() {
      console.log('🌐 Conexão restaurada');
      atualizarStatusConexao(true);
      mostrarNotificacao('🌐 Conexão restaurada', 'success', 3000);
      // Removido auto-update para evitar instabilidade
    });

    window.addEventListener('offline', function() {
      console.log('🚫 Conexão perdida');
      atualizarStatusConexao(false);
      mostrarNotificacao('🚫 Conexão perdida - Dados podem estar desatualizados', 'warning', 5000);
    });

    // Verificar conexão periodicamente
    setInterval(() => {
      if (navigator.onLine) {
        atualizarStatusConexao(true);
      } else {
        atualizarStatusConexao(false);
      }
    }, 30000); // Verificar a cada 30 segundos

    async function loadOrders() {
      const tableBody = document.getElementById('ordersTableBody');
      tableBody.innerHTML = '';

      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Atualizar métricas do dashboard
      atualizarDashboard();

      // Primeiro, vamos calcular as etapas para cada OP e fazer debug
      ordensAtivas.forEach(ordem => {
        let etapas = 0;
        let detalhes = [];

        // 1. Material transferido
        if (ordem.materialTransferido) {
          etapas++;
          detalhes.push('Material✅');
        } else {
          detalhes.push('Material❌');
        }

        // 2. Saldo verificado
        if (ordem.temSaldo) {
          etapas++;
          detalhes.push('Saldo✅');
        } else {
          detalhes.push('Saldo❌');
        }

        // 3. OP impressa
        if (ordem.impressa) {
          etapas++;
          detalhes.push('Impressa✅');
        } else {
          detalhes.push('Impressa❌');
        }

        // 4. Enviado para fábrica
        if (ordem.enviadoFabrica) {
          etapas++;
          detalhes.push('Fábrica✅');
        } else {
          detalhes.push('Fábrica❌');
        }

        // 5. Tem apontamentos
        if (ordem.quantidadeProduzida && ordem.quantidadeProduzida > 0) {
          etapas++;
          detalhes.push('Apontado✅');
        } else {
          detalhes.push('Apontado❌');
        }


        ordem._etapasConcluidas = etapas; // Salvar para usar na ordenação
      });

      ordensAtivas
        .sort((a, b) => {
          // ÚNICA PRIORIDADE: Mais etapas concluídas primeiro
          const etapasA = a._etapasConcluidas || 0;
          const etapasB = b._etapasConcluidas || 0;

          if (etapasA !== etapasB) {
            const resultado = etapasB - etapasA; // Mais etapas primeiro (decrescente)
            return resultado;
          }

          // Se mesmo número de etapas, ordena por número da OP
          return a.numero.localeCompare(b.numero);
        })
        .forEach(ordem => {
          const produto = produtos.find(p => p.id === ordem.produtoId);
          if (!produto) return;

          const row = document.createElement('tr');
          const progress = ordem.quantidadeProduzida ?
            formatarNumero((ordem.quantidadeProduzida / ordem.quantidade * 100)) : 0;

          // Trata a exibição da data de entrega para casos onde pode estar ausente
          const dataEntrega = ordem.dataEntrega?.seconds 
            ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() 
            : 'Não definida';

          row.innerHTML = `
            <td>${ordem.numero}</td>
            <td>${produto.codigo} - ${produto.descricao}</td>
            <td>${formatarNumero(ordem.quantidade)} ${produto.unidade}</td>
            <td>
              ${formatarNumero(ordem.quantidadeProduzida || 0)} ${produto.unidade}
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
              </div>
            </td>
            <td>
              <span class="status-badge status-${ordem.status.toLowerCase()}">${ordem.status}</span>
              ${renderProgressIndicator(ordem)}
            </td>
            <td>${dataEntrega}</td>
            <td>
              ${renderFlowButtons(ordem)}
            </td>
          `;
          tableBody.appendChild(row);
        });
    }

    window.filterOrders = function() {
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;
      const dataInicioFilter = document.getElementById('dataInicioFilter')?.value || '';
      const dataFimFilter = document.getElementById('dataFimFilter')?.value || '';

      const rows = document.getElementById('ordersTableBody').getElementsByTagName('tr');

      for (const row of rows) {
        const numero = row.cells[0].textContent.toLowerCase();
        const produto = row.cells[1].textContent.toLowerCase();
        const status = row.cells[4].textContent;

        const matchesSearch = numero.includes(searchText) || produto.includes(searchText);
        const matchesStatus = !statusFilter || status.includes(statusFilter);

        row.style.display = matchesSearch && matchesStatus ? '' : 'none';
      }

      // Salvar filtros automaticamente sempre que houver mudança
      salvarFiltrosLocalStorage();

      // Atualizar indicador de filtros
      atualizarIndicadorFiltros();
    };

    window.openAppointmentModal = async function(orderId) {
      console.log('🔄 APONTAMENTO: Iniciando verificação para OP:', orderId);

      // 🔧 CORREÇÃO: Forçar atualização completa dos dados antes do apontamento
      console.log('📦 Atualizando estoques...');
      const estoquesSnap = await getDocs(collection(db, "estoques"));
      estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      console.log(`✅ ${estoques.length} registros de estoque carregados`);

      // Atualizar também transferências para verificar se há transferências recentes
      console.log('🚚 Atualizando transferências...');
      const transferenciasSnap = await getDocs(collection(db, "transferenciasArmazem"));
      const transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      console.log(`✅ ${transferencias.length} transferências carregadas`);

      // BUSCAR DADOS ATUALIZADOS DO BANCO (mesma lógica do Verificar Saldo)
      const opDoc = await getDoc(doc(db, "ordensProducao", orderId));
      if (!opDoc.exists()) {
        console.error(`❌ OP ${orderId} não encontrada no banco`);
        return;
      }
      currentOrder = { id: orderId, ...opDoc.data() };
      console.log('📋 Dados da OP carregados do banco para apontamento:', currentOrder);

      const produto = produtos.find(p => p.id === currentOrder.produtoId);
      let materialsHtml = '';
      let canProduce = true;

      // Verifica se o armazém de produção da OP é do tipo PRODUCAO
      const armazemProducao = armazens.find(a => a.id === currentOrder.armazemProducaoId);
      if (!armazemProducao || armazemProducao.tipo !== 'PRODUCAO') {
        alert('A ordem de produção deve estar associada a um armazém do tipo Produção.');
        return;
      }

      if (Array.isArray(currentOrder.materiaisNecessarios) && currentOrder.materiaisNecessarios.length > 0) {
        for (const material of currentOrder.materiaisNecessarios) {
          const materialProduto = produtos.find(p => p.id === material.produtoId);
          const armazemProducaoMateriais = armazens.find(a => a.id === currentOrder.armazemProducaoId && a.tipo === 'PRODUCAO');

          // Garantir que temos dados do produto
          if (!materialProduto) {
            console.warn(`Produto não encontrado: ${material.produtoId}`);
            canProduce = false;
            materialsHtml += `
              <div class="material-item">
                <div class="material-info">
                  <div class="material-code">${material.produtoId}</div>
                  <div class="material-description">Produto não encontrado no cadastro</div>
                </div>
                <div class="material-quantities">
                  <div>Necessário: ${material.quantidade.toFixed(3)}</div>
                  <div>Disponível: 0</div>
                  <div>Armazém: Produto não cadastrado</div>
                </div>
                <div class="material-status status-error">
                  <i class="fas fa-times-circle"></i> 0%
                </div>
              </div>`;
            continue;
          }

          if (!armazemProducaoMateriais) {
            canProduce = false;
            materialsHtml += `
              <div class="material-item">
                <div class="material-info">
                  <div class="material-code">${materialProduto.codigo || material.produtoId}</div>
                  <div class="material-description">${materialProduto.descricao || 'Descrição não disponível'}</div>
                </div>
                <div class="material-quantities">
                  <div>Necessário: ${material.quantidade.toFixed(3)} ${materialProduto.unidade || 'UN'}</div>
                  <div>Disponível: 0 ${materialProduto.unidade || 'UN'}</div>
                  <div>Armazém: Não encontrado</div>
                </div>
                <div class="material-status status-error">
                  <i class="fas fa-times-circle"></i> 0%
                </div>
              </div>`;
            continue;
          }

          const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === armazemProducaoMateriais.id);

          // 🔧 LOGS DETALHADOS PARA DEBUG
          console.log(`🔍 MATERIAL: ${materialProduto.codigo}`);
          console.log(`📦 Buscando estoque para produtoId: ${material.produtoId}, armazemId: ${armazemProducaoMateriais.id}`);
          console.log(`📊 Estoque encontrado:`, estoque);

          // Verificar se há transferências recentes para este material
          const transferenciasRecentes = transferencias.filter(t =>
            t.produtoId === material.produtoId &&
            t.ordemProducaoId === orderId &&
            t.status === 'CONCLUIDA'
          );
          console.log(`🚚 Transferências recentes para este material:`, transferenciasRecentes);

          // CALCULAR SALDO DISPONÍVEL CORRETAMENTE
          let saldoTotal = 0;
          let saldoReservado = 0;
          let saldoEmpenhado = 0;
          let saldoDisponivel = 0;

          if (estoque) {
            saldoTotal = estoque.saldo || 0;
            saldoReservado = estoque.saldoReservado || 0;
            saldoEmpenhado = estoque.saldoEmpenhado || 0;

            console.log(`💰 Saldo Total: ${saldoTotal}, Reservado: ${saldoReservado}, Empenhado: ${saldoEmpenhado}`);

            // CORREÇÃO: Verificar se a reserva é para esta OP
            // Se todo o saldo está reservado e não há informação específica,
            // assumir que a reserva é para esta OP (cenário mais comum)
            let quantidadeReservadaParaEstaOP = 0;

            if (material.quantidadeReservada) {
              // Caso específico: material tem reserva definida
              quantidadeReservadaParaEstaOP = material.quantidadeReservada;
            } else if (saldoReservado > 0) {
              // Caso geral: assumir que a reserva é para esta OP
              // (isso resolve o problema do material 100231)
              quantidadeReservadaParaEstaOP = saldoReservado;
              console.log(`🔄 Assumindo que reserva de ${saldoReservado.toFixed(3)} é para OP ${currentOrder.numero}`);
            }

            // Saldo livre = total - reservado - empenhado
            const saldoLivre = saldoTotal - saldoReservado - saldoEmpenhado;

            // Saldo disponível para esta OP = saldo livre + reservado para esta OP
            saldoDisponivel = saldoLivre + quantidadeReservadaParaEstaOP;

            console.log(`🧮 CÁLCULO: Livre: ${saldoLivre}, Reservado para OP: ${quantidadeReservadaParaEstaOP}, Disponível: ${saldoDisponivel}`);
          } else {
            console.log(`❌ ESTOQUE NÃO ENCONTRADO para produto ${materialProduto.codigo} no armazém ${armazemProducaoMateriais.codigo}`);
          }

          const quantidadeRestante = material.quantidade - (currentOrder.quantidadeProduzida || 0) * (material.quantidade / currentOrder.quantidade);
          const percentualDisponibilidade = saldoDisponivel >= quantidadeRestante ? 100 : (saldoDisponivel / quantidadeRestante * 100).toFixed(1);
          let statusClass = 'status-ok';

          console.log(`📏 NECESSÁRIO: ${quantidadeRestante.toFixed(3)}, DISPONÍVEL: ${saldoDisponivel.toFixed(3)}`);

          // SEMPRE bloquear apontamento se não há material suficiente
          if (saldoDisponivel < quantidadeRestante) {
            statusClass = 'status-error';
            canProduce = false;
            console.log(`❌ BLOQUEADO: Material ${materialProduto.codigo} insuficiente (${saldoDisponivel.toFixed(3)} < ${quantidadeRestante.toFixed(3)})`);
          } else if (saldoDisponivel < quantidadeRestante * 1.2) {
            statusClass = 'status-warning';
            console.log(`⚠️ AVISO: Material ${materialProduto.codigo} com estoque baixo`);
          } else {
            console.log(`✅ OK: Material ${materialProduto.codigo} suficiente`);
          }

          const statusIcon = statusClass === 'status-ok' ? 'check-circle' :
                            statusClass === 'status-warning' ? 'exclamation-triangle' : 'times-circle';

          materialsHtml += `
            <div class="material-item">
              <div class="material-info">
                <div class="material-code">${materialProduto.codigo || material.produtoId}</div>
                <div class="material-description">${materialProduto.descricao || 'Descrição não disponível'}</div>
              </div>
              <div class="material-quantities">
                <div>Necessário: ${quantidadeRestante.toFixed(3)} ${materialProduto.unidade || 'UN'}</div>
                <div>Disponível: ${saldoDisponivel.toFixed(3)} ${materialProduto.unidade || 'UN'}</div>
                <div>Saldo Total: ${saldoTotal.toFixed(3)} | Livre: ${(saldoTotal - saldoReservado - saldoEmpenhado).toFixed(3)} | Reservado: ${saldoReservado.toFixed(3)}</div>
                <div>Armazém: ${armazemProducaoMateriais.codigo}</div>
              </div>
              <div class="material-status ${statusClass}">
                <i class="fas fa-${statusIcon}"></i> ${percentualDisponibilidade}%
              </div>
              ${(saldoDisponivel < quantidadeRestante && permitirGerarEstoqueAutomatico) ?
                `<button class='generate-stock-btn' onclick='ajustarETransferirMaterial("${material.produtoId}", ${quantidadeRestante - saldoDisponivel})'>
                  <i class="fas fa-magic"></i> Gerar Estoque
                </button>` : ''}
            </div>`;
        }
      } else {
        materialsHtml += '<p>Sem materiais necessários registrados.</p>';
      }

      document.getElementById('orderInfo').innerHTML = `
        <h3>Ordem de Produção: ${currentOrder.numero}</h3>
        <div class="order-details">
          <div class="order-detail-item">
            <div class="order-detail-label">Produto</div>
            <div class="order-detail-value">${produto.codigo} - ${produto.descricao}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Quantidade Total</div>
            <div class="order-detail-value">${currentOrder.quantidade} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Já Produzido</div>
            <div class="order-detail-value">${currentOrder.quantidadeProduzida || 0} ${produto.unidade}</div>
          </div>
          <div class="order-detail-item">
            <div class="order-detail-label">Armazém de Produção</div>
            <div class="order-detail-value">${armazemProducao.codigo} - ${armazemProducao.nome}</div>
          </div>
        </div>
      `;

      document.querySelector('#materialsList .materials-container').innerHTML = materialsHtml;

      // 🔧 LOG FINAL DA VERIFICAÇÃO
      console.log(`🎯 RESULTADO FINAL: canProduce = ${canProduce}`);

      // SEMPRE verificar se pode produzir antes de abrir o modal (independente da configuração)
      if (!canProduce) {
        console.log('❌ APONTAMENTO BLOQUEADO: Verificando materiais faltantes...');

        // Mostrar modal de materiais faltantes em vez de apenas um alert
        const materiaisFaltantes = verificarMateriaisFaltantesParaApontamento(currentOrder);
        console.log('📋 Materiais faltantes encontrados:', materiaisFaltantes);

        if (materiaisFaltantes.length > 0) {
          console.log('🔴 Mostrando modal de materiais faltantes');
          mostrarModalMateriaisFaltantesApontamento(materiaisFaltantes, currentOrder);
          return; // Não abrir o modal de apontamento
        } else {
          console.log('⚠️ Nenhum material faltante específico encontrado');
          alert('Não há material suficiente no armazém de produção para realizar o apontamento. Transfira os materiais necessários do armazém tipo Almoxarifado para o armazém tipo Produção usando o módulo de movimentação.');
          return; // Não abrir o modal de apontamento
        }
      } else {
        console.log('✅ APONTAMENTO LIBERADO: Todos os materiais disponíveis');
      }

      document.getElementById('submitButton').disabled = !canProduce;

      // 🔧 CORREÇÃO: Definir valores padrão formatados com 3 casas decimais
      const quantidadeRestante = currentOrder.quantidade - (currentOrder.quantidadeProduzida || 0);
      document.getElementById('quantity').value = formatarNumero3Casas(quantidadeRestante);
      document.getElementById('scrap').value = '0.000';

      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('appointmentModal').style.display = 'none';
      document.getElementById('appointmentForm').reset();
      currentOrder = null;
    };

    async function updateInventory(produtoId, armazemId, quantidade, tipo) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldo = tipo === 'entrada' ? estoque.saldo + quantidade : estoque.saldo - quantidade;
        if (novoSaldo < 0 && !permitirProducaoSemEstoque) throw new Error(`Saldo insuficiente no armazém ${armazemId}.`);
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldo = novoSaldo;
      } else if (tipo === 'entrada') {
        const novoEstoque = { produtoId, armazemId, saldo: quantidade, ultimaMovimentacao: Timestamp.now() };
        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        estoques.push({ ...novoEstoque, id: docRef.id });
      }
    }

    async function updateInventoryReservation(produtoId, armazemId, quantidade) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade;
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldoReservado: Math.max(0, novoSaldoReservado),
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
      }
    }

    window.submitAppointment = async function(event) {
      event.preventDefault();
      const submitButton = document.getElementById('submitButton');
      submitButton.disabled = true;

      try {
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser) {
          throw new Error('Usuário não está logado');
        }

        const quantidade = parseFloat(document.getElementById('quantity').value);
        const refugo = parseFloat(document.getElementById('scrap').value) || 0;
        const observacoes = document.getElementById('observations').value;

        if (quantidade <= 0) {
          throw new Error('A quantidade produzida deve ser maior que zero');
        }

        // 🔧 VALIDAÇÃO COM TOLERÂNCIA PEQUENA E 3 CASAS DECIMAIS
        const quantidadeJaProduzida = parseFloat((currentOrder.quantidadeProduzida || 0).toFixed(3));
        const quantidadeTotal = parseFloat(currentOrder.quantidade.toFixed(3));
        const quantidadeApontar = parseFloat(quantidade.toFixed(3));
        const novaQuantidadeTotal = parseFloat((quantidadeJaProduzida + quantidadeApontar).toFixed(3));

        // Tolerância de 0.1% (muito pequena) para permitir apenas diferenças de arredondamento
        const toleranciaPercentual = 0.001; // 0.1%
        const toleranciaAbsoluta = parseFloat((quantidadeTotal * toleranciaPercentual).toFixed(3));
        const toleranciaMinima = Math.max(toleranciaAbsoluta, 0.001); // Mínimo de 0.001
        const limiteMaximo = parseFloat((quantidadeTotal + toleranciaMinima).toFixed(3));

        console.log('🔍 VALIDAÇÃO DE QUANTIDADE (3 casas decimais):');
        console.log(`   📊 OP: ${currentOrder.numero}`);
        console.log(`   🎯 Quantidade da OP: ${quantidadeTotal}`);
        console.log(`   ✅ Já produzida: ${quantidadeJaProduzida}`);
        console.log(`   📝 Tentando apontar: ${quantidadeApontar}`);
        console.log(`   🧮 Total após apontamento: ${novaQuantidadeTotal}`);
        console.log(`   📏 Tolerância: ${toleranciaMinima}`);
        console.log(`   🚪 Limite máximo: ${limiteMaximo}`);

        if (novaQuantidadeTotal > limiteMaximo) {
          const excedente = parseFloat((novaQuantidadeTotal - quantidadeTotal).toFixed(3));
          const maximoPermitido = parseFloat((limiteMaximo - quantidadeJaProduzida).toFixed(3));

          throw new Error(`❌ QUANTIDADE EXCEDENTE!\n\n📊 Quantidade da OP: ${quantidadeTotal}\n✅ Já produzida: ${quantidadeJaProduzida}\n📝 Tentando apontar: ${quantidadeApontar}\n🧮 Total seria: ${novaQuantidadeTotal}\n\n⚠️ Excedente: ${excedente}\n📏 Tolerância: ${toleranciaMinima}\n\n💡 Máximo permitido para este apontamento: ${maximoPermitido}`);
        } else if (novaQuantidadeTotal > quantidadeTotal) {
          console.log(`   ✅ Excedente mínimo (${parseFloat((novaQuantidadeTotal - quantidadeTotal).toFixed(3))}) dentro da tolerância, permitindo apontamento`);
        }

        // Atualizar ordem de produção
        const orderRef = doc(db, "ordensProducao", currentOrder.id);
        const novaQuantidadeProduzida = (currentOrder.quantidadeProduzida || 0) + quantidade;
        const novoRefugo = (currentOrder.refugo || 0) + refugo;

        // Determinar novo status
        let novoStatus = currentOrder.status;
        if (novaQuantidadeProduzida >= currentOrder.quantidade) {
          novoStatus = 'Concluída';
        } else if (novoStatus === 'Pendente') {
          novoStatus = 'Em Produção';
        }

        // 🔄 CONSUMIR MATERIAIS EMPENHADOS
        if (currentOrder.materiaisNecessarios) {
          const consumos = [];

          // CORREÇÃO: Consumir material para TODAS as peças (aprovadas + reprovadas)
          const totalPecasProduzidas = quantidade + refugo;
          console.log(`📊 Consumo de material: ${quantidade} aprovadas + ${refugo} reprovadas = ${totalPecasProduzidas} total`);

          for (const material of currentOrder.materiaisNecessarios) {
            // Calcular material necessário para o total de peças (aprovadas + reprovadas)
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * totalPecasProduzidas;

            // Preparar dados para consumo de empenho
            consumos.push({
              produtoId: material.produtoId,
              quantidade: quantidadeNecessaria
            });

            console.log(`🔧 Material ${material.produtoId}: ${quantidadeNecessaria.toFixed(3)} (${totalPecasProduzidas} peças)`);
          }

          // ⚡ CONSUMIR EMPENHOS (se existirem)
          try {
            const resultadoConsumo = await EmpenhoService.consumirMaterialEmpenhado(currentOrder.id, consumos);
            if (resultadoConsumo.consumosRealizados > 0) {
              console.log(`✅ ${resultadoConsumo.consumosRealizados} empenho(s) consumido(s)`);
              mostrarNotificacao(`⚡ ${resultadoConsumo.consumosRealizados} empenho(s) consumido(s)`, 'info', 2000);
            }
            if (resultadoConsumo.erros.length > 0) {
              console.warn('⚠️ Erros no consumo de empenhos:', resultadoConsumo.erros);
            }
          } catch (empenhoError) {
            console.warn('⚠️ Erro ao consumir empenhos (continuando com apontamento):', empenhoError);
          }
        }

        // Baixar materiais do estoque (lógica tradicional)
        const batch = writeBatch(db);

        if (currentOrder.materiaisNecessarios) {
          // CORREÇÃO: Usar o mesmo total de peças (aprovadas + reprovadas)
          const totalPecasProduzidas = quantidade + refugo;

          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * totalPecasProduzidas;
            const armazemId = material.armazemId || currentOrder.armazemProducaoId;

            // Encontrar o estoque correspondente
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === armazemId
            );

            if (estoque) {
              const saldoAtual = estoque.saldo;
              const saldoReservado = estoque.saldoReservado || 0;

              if (saldoAtual - saldoReservado < quantidadeNecessaria && !permitirProducaoSemEstoque) {
                throw new Error(`Saldo insuficiente para o material ${material.codigo}`);
              }

              batch.update(doc(db, "estoques", estoque.id), {
                saldo: saldoAtual - quantidadeNecessaria,
                ultimaMovimentacao: Timestamp.now()
              });
            } else if (!permitirProducaoSemEstoque) {
              throw new Error(`Estoque não encontrado para o material ${material.codigo}`);
            }
          }
        }

        // Atualizar ordem
        batch.update(orderRef, {
          quantidadeProduzida: novaQuantidadeProduzida,
          refugo: novoRefugo,
          status: novoStatus,
          ultimaAtualizacao: Timestamp.now()
        });

        // 🔓 LIBERAR EMPENHOS RESTANTES (se OP foi finalizada)
        if (novoStatus === 'Concluída') {
          try {
            const resultadoLiberacao = await EmpenhoService.liberarEmpenhosRestantes(currentOrder.id, 'OP_FINALIZADA');
            if (resultadoLiberacao.liberacoes > 0) {
              console.log(`✅ ${resultadoLiberacao.liberacoes} empenho(s) liberado(s) - OP finalizada`);
              mostrarNotificacao(`🔓 ${resultadoLiberacao.liberacoes} empenho(s) liberado(s)`, 'success', 3000);
            }
          } catch (liberacaoError) {
            console.warn('⚠️ Erro ao liberar empenhos (OP finalizada):', liberacaoError);
          }
        }

        // Registrar apontamento
        const apontamentoRef = collection(db, "apontamentos");
        const novoApontamento = {
          ordemId: currentOrder.id,
          numeroOrdem: currentOrder.numero,
          produtoId: currentOrder.produtoId,
          quantidade: quantidade,
          refugo: refugo,
          observacoes: observacoes,
          usuario: currentUser.email,
          nomeUsuario: currentUser.nome,
          dataHora: Timestamp.now()
        };

        // Primeiro executar o batch para atualizar estoque e ordem
        await batch.commit();

        // Depois adicionar o apontamento
        await addDoc(apontamentoRef, novoApontamento);

        // Registrar movimentações de estoque
        // 1. Consumo de materiais
        if (!currentOrder.materiaisNecessarios || currentOrder.materiaisNecessarios.length === 0) {
          alert('Atenção: Esta ordem de produção não possui materiais necessários cadastrados. Nenhuma movimentação de consumo será registrada!');
        } else {
          // CORREÇÃO: Usar o mesmo total de peças (aprovadas + reprovadas)
          const totalPecasProduzidas = quantidade + refugo;

          for (const material of currentOrder.materiaisNecessarios) {
            const quantidadeNecessaria = (material.quantidade / currentOrder.quantidade) * totalPecasProduzidas;
            const produtoMaterial = produtos.find(p => p.id === material.produtoId);

            // Registrar movimentação de consumo total
            await addDoc(collection(db, "movimentacoesEstoque"), {
              produtoId: material.produtoId,
              tipo: 'SAIDA',
              quantidade: quantidadeNecessaria,
              unidade: produtoMaterial?.unidade || '',
              tipoDocumento: 'PRODUCAO',
              numeroDocumento: currentOrder.numero,
              observacoes: `Consumo OP ${currentOrder.numero} - ${quantidade} aprovadas + ${refugo} reprovadas = ${totalPecasProduzidas} total`,
              dataHora: Timestamp.now(),
              armazemId: material.armazemId || currentOrder.armazemProducaoId
            });
          }
        }
        // 2. Entrada do produto acabado
        const produtoAcabado = produtos.find(p => p.id === currentOrder.produtoId);
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId: currentOrder.produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidade,
          unidade: produtoAcabado?.unidade || '',
          tipoDocumento: 'PRODUCAO',
          numeroDocumento: currentOrder.numero,
          observacoes: `Produção OP ${currentOrder.numero}`,
          dataHora: Timestamp.now(),
          armazemId: currentOrder.armazemProducaoId
        });
        // Atualizar saldo do estoque do produto acabado no armazem de produção
        let estoqueProd = estoques.find(e => e.produtoId === currentOrder.produtoId && e.armazemId === currentOrder.armazemProducaoId);
        if (estoqueProd) {
          await updateDoc(doc(db, "estoques", estoqueProd.id), {
            saldo: (estoqueProd.saldo || 0) + quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoqueProd.saldo += quantidade;
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId: currentOrder.produtoId,
            armazemId: currentOrder.armazemProducaoId,
            saldo: quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
          estoques.push({ id: docRef.id, produtoId: currentOrder.produtoId, armazemId: currentOrder.armazemProducaoId, saldo: quantidade });
        }

        mostrarNotificacao('✅ Apontamento registrado com sucesso!', 'success');
        closeModal();

      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert(error.message || 'Erro ao registrar apontamento');
      } finally {
        submitButton.disabled = false;
      }
    };

    window.onclick = function(event) {
      const modal = document.getElementById('appointmentModal');
      if (event.target === modal) {
        closeModal();
      }
    };

    // Adicionar função global para ajuste e transferência automática (ATUALIZADA - COMPATÍVEL COM NOVO SISTEMA)
    window.ajustarETransferirMaterial = async function(produtoId, quantidadeNecessaria) {
      try {
        const ALM01 = armazens.find(a => a.codigo === 'ALM01');
        const PROD1 = armazens.find(a => a.id === currentOrder.armazemProducaoId);
        if (!ALM01) {
          alert('Armazém ALM01 não encontrado!');
          return;
        }
        if (!PROD1) {
          alert('Armazém de produção não encontrado!');
          return;
        }

        // Gerar número de transferência único
        const numeroTransferencia = `AUT-${Date.now()}`;
        const dataHoraAtual = Timestamp.now();
        const usuarioAtual = localStorage.getItem('currentUser') || 'Sistema';
        const produto = produtos.find(p => p.id === produtoId);

        // 1. Ajuste de inventário (entrada) no ALM01
        let estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        let novoSaldo = (estoqueAlm01?.saldo || 0) + quantidadeNecessaria;

        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: novoSaldo,
            ultimaMovimentacao: dataHoraAtual
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: ALM01.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: dataHoraAtual
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: ALM01.id, saldo: quantidadeNecessaria });
        }

        // Registrar movimentação de ajuste
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'AJUSTE',
          numeroDocumento: `AJU-${Date.now()}`,
          observacoes: `Ajuste automático para suprir OP ${currentOrder.numero}`,
          dataHora: dataHoraAtual,
          armazemId: ALM01.id,
          usuario: usuarioAtual
        });

        // 2. Transferência ALM01 -> PROD1
        // Atualizar saldo ALM01
        estoqueAlm01 = estoques.find(e => e.produtoId === produtoId && e.armazemId === ALM01.id);
        if (estoqueAlm01) {
          await updateDoc(doc(db, "estoques", estoqueAlm01.id), {
            saldo: estoqueAlm01.saldo - quantidadeNecessaria,
            ultimaMovimentacao: dataHoraAtual
          });
        }

        // Atualizar saldo PROD1
        let estoqueProd1 = estoques.find(e => e.produtoId === produtoId && e.armazemId === PROD1.id);
        if (estoqueProd1) {
          await updateDoc(doc(db, "estoques", estoqueProd1.id), {
            saldo: (estoqueProd1.saldo || 0) + quantidadeNecessaria,
            ultimaMovimentacao: dataHoraAtual
          });
        } else {
          const docRef = await addDoc(collection(db, "estoques"), {
            produtoId,
            armazemId: PROD1.id,
            saldo: quantidadeNecessaria,
            ultimaMovimentacao: dataHoraAtual
          });
          estoques.push({ id: docRef.id, produtoId, armazemId: PROD1.id, saldo: quantidadeNecessaria });
        }

        // Registrar movimentações de estoque
        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'SAIDA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Armazém ${PROD1.codigo}`,
          dataHora: dataHoraAtual,
          armazemId: ALM01.id,
          usuario: usuarioAtual
        });

        await addDoc(collection(db, "movimentacoesEstoque"), {
          produtoId,
          tipo: 'ENTRADA',
          quantidade: quantidadeNecessaria,
          unidade: produto.unidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência automática para OP ${currentOrder.numero} - Do armazém ${ALM01.codigo}`,
          dataHora: dataHoraAtual,
          armazemId: PROD1.id,
          usuario: usuarioAtual
        });

        // 🆕 REGISTRAR NA COLEÇÃO transferenciasArmazem (COMPATIBILIDADE COM NOVO SISTEMA)
        const transferencia = {
          numeroTransferencia: numeroTransferencia,
          tipo: 'OP', // Transferência para Ordem de Produção
          ordemProducaoId: currentOrder.id,
          armazemOrigemId: ALM01.id,
          armazemDestinoId: PROD1.id,
          dataTransferencia: dataHoraAtual,
          usuario: usuarioAtual,
          status: 'CONCLUIDA',
          observacoes: `Transferência automática via botão "Gerar Estoque" para OP ${currentOrder.numero}`,

          // Materiais transferidos
          materiais: [{
            produtoId: produtoId,
            codigo: produto.codigo || produtoId,
            descricao: produto.descricao || 'Produto',
            quantidade: quantidadeNecessaria,
            unidade: produto.unidade || 'UN',
            valorUnitario: produto.valorUnitario || 0
          }],

          // Totais
          quantidadeTotalItens: 1,
          valorTotal: (produto.valorUnitario || 0) * quantidadeNecessaria,

          // Metadados para compatibilidade com sistema melhorado
          origem: 'GERAR_ESTOQUE_AUTOMATICO',
          integradoComMovimentacoes: true,
          numeroDocumentoMovimentacao: numeroTransferencia
        };

        await addDoc(collection(db, "transferenciasArmazem"), transferencia);

        mostrarNotificacao('✅ Ajuste e transferência realizados com sucesso!', 'success');
        debounceUpdate('ajusteTransferencia'); // Atualiza os dados na tela
        openAppointmentModal(currentOrder.id); // Reabre o modal atualizado

      } catch (error) {
        console.error(error);
        alert('Erro ao ajustar e transferir material: ' + error.message);
      }
    };

    // Função para verificar materiais faltantes para apontamento
    function verificarMateriaisFaltantesParaApontamento(ordem) {
      if (!ordem || !ordem.materiaisNecessarios) return [];

      const materiaisFaltantes = [];

      ordem.materiaisNecessarios.forEach(material => {
        const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
        const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId);
        const estoque = estoques.find(e =>
          e.produtoId === material.produtoId &&
          e.armazemId === ordem.armazemProducaoId
        ) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };

        // CORREÇÃO: Calcular saldo disponível corretamente (considerando reservas da própria OP)
        const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, ordem.id);
        const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

        // SEMPRE considerar material em falta se saldo insuficiente (independente da configuração)
        if (saldoDisponivel < quantidadeRestante) {
          materiaisFaltantes.push({
            produtoId: material.produtoId,
            codigo: materialProduto.codigo || 'N/A',
            descricao: materialProduto.descricao || 'N/A',
            tipo: materialProduto.tipo || 'N/A',
            unidade: materialProduto.unidade || 'UN',
            quantidadeNecessaria: quantidadeRestante,
            saldoDisponivel: saldoDisponivel,
            falta: quantidadeRestante - saldoDisponivel,
            armazemId: ordem.armazemProducaoId,
            armazemCodigo: armazemProducao?.codigo || 'N/A',
            armazemNome: armazemProducao?.nome || 'N/A'
          });
        }
      });

      return materiaisFaltantes;
    }

    // Função para verificar estoque antes da impressão
    function verificarEstoqueParaImpressao(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
      if (!ordem || !ordem.materiaisNecessarios) return [];

      const materiaisFaltantes = [];

      ordem.materiaisNecessarios.forEach(material => {
        const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
        const estoque = estoques.find(e =>
          e.produtoId === material.produtoId &&
          e.armazemId === (material.armazemId || ordem.armazemProducaoId)
        ) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };

        // CORREÇÃO: Calcular saldo disponível corretamente (considerando reservas da própria OP)
        const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, ordem.id);
        const quantidadeNecessaria = material.quantidade;

        if (saldoDisponivel < quantidadeNecessaria) {
          materiaisFaltantes.push({
            produtoId: material.produtoId,
            codigo: materialProduto.codigo || 'N/A',
            descricao: materialProduto.descricao || 'N/A',
            tipo: materialProduto.tipo || 'N/A',
            unidade: materialProduto.unidade || 'UN',
            quantidadeNecessaria: quantidadeNecessaria,
            saldoDisponivel: saldoDisponivel,
            falta: quantidadeNecessaria - saldoDisponivel,
            armazemId: material.armazemId || ordem.armazemProducaoId
          });
        }
      });

      return materiaisFaltantes;
    }

    // Função para mostrar modal de materiais faltantes para apontamento
    function mostrarModalMateriaisFaltantesApontamento(materiaisFaltantes, ordem) {
      // Criar modal dinamicamente se não existir
      let modal = document.getElementById('modalMateriaisFaltantesApontamento');
      if (!modal) {
        modal = document.createElement('div');
        modal.id = 'modalMateriaisFaltantesApontamento';
        modal.className = 'modal';
        modal.style.display = 'none';
        modal.innerHTML = `
          <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header" style="background: #dc3545; color: white;">
              <h2 style="margin: 0; color: white;">⚠️ Apontamento Bloqueado - Materiais em Falta</h2>
              <span class="close-button" onclick="fecharModalMateriaisFaltantesApontamento()" style="color: white;">&times;</span>
            </div>
            <div class="modal-body">
              <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                <h4 style="color: #721c24; margin: 0 0 10px 0;">🚫 Não é possível realizar o apontamento</h4>
                <p style="color: #721c24; margin: 0;">
                  Os materiais abaixo não possuem saldo suficiente no armazém de produção.
                  É necessário transferir os materiais do almoxarifado para o armazém de produção antes de realizar o apontamento.
                </p>
              </div>

              <div style="margin-bottom: 20px;">
                <h4>📋 Ordem de Produção: <span id="numeroOPFaltantesApontamento"></span></h4>
                <p><strong>Produto:</strong> <span id="produtoOPFaltantesApontamento"></span></p>
                <p><strong>Armazém de Produção:</strong> <span id="armazemProducaoApontamento"></span></p>
              </div>

              <h4 style="color: #dc3545;">📦 Materiais em Falta:</h4>
              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                  <thead>
                    <tr style="background: #f8f9fa;">
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Código</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Descrição</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Tipo</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Necessário</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Disponível</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Falta</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Unidade</th>
                      <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Ação</th>
                    </tr>
                  </thead>
                  <tbody id="tbodyMateriaisFaltantesApontamento">
                  </tbody>
                </table>
              </div>

              <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px; margin-top: 20px;">
                <h5 style="color: #0c5460; margin: 0 0 10px 0;">💡 Como resolver:</h5>
                <ol style="color: #0c5460; margin: 0; padding-left: 20px;">
                  <li>Acesse o módulo de <strong>Movimentação de Estoque</strong></li>
                  <li>Realize a <strong>transferência</strong> dos materiais do almoxarifado para o armazém de produção</li>
                  <li>Ou use o botão <strong>"Gerar Estoque"</strong> nos materiais em falta (se disponível)</li>
                  <li>Após a transferência, tente realizar o apontamento novamente</li>
                </ol>
              </div>
            </div>
            <div class="modal-footer" style="text-align: right; padding: 15px; border-top: 1px solid #ddd;">
              <button onclick="fecharModalMateriaisFaltantesApontamento()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                Fechar
              </button>
            </div>
          </div>
        `;
        document.body.appendChild(modal);
      }

      // Preencher informações da OP
      document.getElementById('numeroOPFaltantesApontamento').textContent = ordem.numero || 'N/A';
      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      document.getElementById('produtoOPFaltantesApontamento').textContent = `${produto.codigo || 'N/A'} - ${produto.descricao || 'N/A'}`;

      const armazemProducao = armazens.find(a => a.id === ordem.armazemProducaoId) || {};
      document.getElementById('armazemProducaoApontamento').textContent = `${armazemProducao.codigo || 'N/A'} - ${armazemProducao.nome || 'N/A'}`;

      // Limpar e preencher tabela
      const tbody = document.getElementById('tbodyMateriaisFaltantesApontamento');
      tbody.innerHTML = '';

      materiaisFaltantes.forEach(material => {
        const row = document.createElement('tr');

        // Verificar se é MP e se pode gerar estoque
        const ehMP = material.tipo === 'MP' || material.tipo === 'MATÉRIA-PRIMA (MP)';
        const botaoGerarEstoque = (ehMP && permitirGerarEstoqueAutomatico) ?
          `<button onclick="ajustarETransferirMaterialApontamento('${material.produtoId}', ${material.falta}, '${ordem.id}')"
                   style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">
             <i class="fas fa-magic"></i> Gerar Estoque
           </button>` :
          '<span style="color: #6c757d; font-size: 12px;">N/A</span>';

        row.innerHTML = `
          <td style="padding: 10px; border: 1px solid #ddd;">${material.codigo}</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${material.descricao}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.tipo}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.quantidadeNecessaria.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.saldoDisponivel.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;">${material.falta.toFixed(3)}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${material.unidade}</td>
          <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${botaoGerarEstoque}</td>
        `;
        tbody.appendChild(row);
      });

      // Mostrar modal
      modal.style.display = 'block';
    }

    // Função para mostrar modal de materiais faltantes
    function mostrarModalMateriaisFaltantes(materiaisFaltantes, ordem) {
      const modal = document.getElementById('modalMateriaisFaltantes');
      const tbody = document.getElementById('tbodyMateriaisFaltantes');
      const numeroOP = document.getElementById('numeroOPFaltantes');
      const produtoOP = document.getElementById('produtoOPFaltantes');

      // Preencher informações da OP
      numeroOP.textContent = ordem.numero || 'N/A';
      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      produtoOP.textContent = `${produto.codigo || 'N/A'} - ${produto.descricao || 'N/A'}`;

      // Limpar tabela
      tbody.innerHTML = '';

      // Preencher tabela com materiais faltantes
      materiaisFaltantes.forEach(material => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${material.codigo}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${material.descricao}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.tipo}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.quantidadeNecessaria}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.saldoDisponivel}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;">${material.falta}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${material.unidade}</td>
        `;
        tbody.appendChild(row);
      });

      // Mostrar modal
      modal.style.display = 'block';
    }

    // Função para gerar estoque no contexto de apontamento
    window.ajustarETransferirMaterialApontamento = async function(produtoId, quantidadeNecessaria, opId) {
      try {
        console.log(`🔄 Gerando estoque para apontamento - Produto: ${produtoId}, Quantidade: ${quantidadeNecessaria}, OP: ${opId}`);

        // Usar a função existente mas com contexto de apontamento
        currentOrder = ordensProducao.find(op => op.id === opId);
        if (!currentOrder) {
          alert('❌ Ordem de produção não encontrada!');
          return;
        }

        await ajustarETransferirMaterial(produtoId, quantidadeNecessaria);

        // Fechar modal atual e tentar abrir apontamento novamente
        fecharModalMateriaisFaltantesApontamento();

        // Aguardar um pouco para os dados serem atualizados
        setTimeout(() => {
          openAppointmentModal(opId);
        }, 1000);

      } catch (error) {
        console.error('❌ Erro ao gerar estoque para apontamento:', error);
        alert('❌ Erro ao gerar estoque: ' + error.message);
      }
    };

    // Função para fechar modal de materiais faltantes para apontamento
    window.fecharModalMateriaisFaltantesApontamento = function() {
      const modal = document.getElementById('modalMateriaisFaltantesApontamento');
      if (modal) {
        modal.style.display = 'none';
      }
    };

    // Função para fechar modal de materiais faltantes
    window.fecharModalMateriaisFaltantes = function() {
      document.getElementById('modalMateriaisFaltantes').style.display = 'none';
    };

    // Função para dividir OP diretamente do modal de materiais faltantes
    window.dividirOPDoModal = function() {
      const orderId = document.getElementById('modalMateriaisFaltantes').dataset.orderId;
      const ordem = ordensProducao.find(op => op.id === orderId);

      if (!ordem) {
        alert('❌ Ordem não encontrada!');
        return;
      }

      // Fechar modal atual
      fecharModalMateriaisFaltantes();

      // Abrir divisão de OP com a OP específica
      const url = `divisao_op_parcial.html?op=${encodeURIComponent(ordem.numero)}`;
      window.open(url, '_blank');

      mostrarNotificacao(`🔄 Abrindo divisão para OP ${ordem.numero} - Materiais insuficientes detectados`, 'info', 4000);
    };

    // Função para forçar impressão mesmo com materiais faltantes
    window.forcarImpressaoOP = function() {
      const orderId = document.getElementById('modalMateriaisFaltantes').dataset.orderId;
      fecharModalMateriaisFaltantes();

      // Mostrar confirmação
      if (confirm('⚠️ ATENÇÃO: Existem materiais em falta!\n\nDeseja realmente imprimir a OP mesmo assim?\n\nEsta ação pode causar problemas na produção.')) {
        imprimirOPSemValidacao(orderId);
      }
    };

    // Função para imprimir OP sem validação de estoque
    function imprimirOPSemValidacao(orderId) {
      const ordem = ordensProducao.find(op => op.id === orderId);
      if (!ordem) return alert('Ordem não encontrada!');

      const produto = produtos.find(p => p.id === ordem.produtoId) || {};
      const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
      const printArea = document.getElementById('printArea');

      // Validar se printArea existe
      if (!printArea) {
        console.error('❌ Elemento printArea não encontrado');
        alert('❌ Erro: Área de impressão não encontrada. Recarregue a página e tente novamente.');
        return;
      }

      // Continuar com a impressão normal...
      processarImpressaoOP(ordem, produto, estrutura, printArea);
    }

    // Função removida - substituída pela versão do fluxo sequencial

    // Função para processar a impressão da OP
    function processarImpressaoOP(ordem, produto, estrutura, printArea) {
      // Validar se printArea existe
      if (!printArea) {
        console.error('❌ Elemento printArea não encontrado');
        alert('❌ Erro: Área de impressão não encontrada. Recarregue a página e tente novamente.');
        return;
      }
      let roteiroHtml = '';
      if (estrutura && Array.isArray(estrutura.operacoes) && estrutura.operacoes.length > 0) {
        roteiroHtml = `
          <div class='section' style='margin-bottom:15px;'>
            <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>ROTEIRO DE PRODUÇÃO</div>
            <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
              <thead>
                <tr>
                  <th style='border:1px solid #ccc;padding:4px;'>Seq.</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Operação</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Recurso</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Tempo (min)</th>
                  <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                </tr>
              </thead>
              <tbody>
                ${estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(op => {
                  const operacao = operacoes.find(o => o.id === op.operacaoId) || {};
                  const recurso = recursos.find(r => r.id === op.recursoId) || {};
                  return `<tr>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.sequencia}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${operacao.operacao || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${recurso.codigo || ''} - ${recurso.maquina || ''}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.tempo}</td>
                    <td style='border:1px solid #ccc;padding:4px;'>${op.descricao || ''}</td>
                  </tr>`;
                }).join('')}
              </tbody>
            </table>
          </div>`;
      }
      printArea.innerHTML = `
        <div class='container' style='max-width:210mm;margin:0 auto;font-family:Arial,sans-serif;font-size:12px;'>
          <div class='order-page' style='background:white;padding:15px;margin-bottom:20px;box-shadow:0 2px 8px rgba(0,0,0,0.1);'>
            <div class='header' style='display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:10px;border-bottom:1px solid #000;padding-bottom:5px;'>
              <img src='https://www.naliteck.com.br/img/logo.png' alt='Logo' style='width:100px;height:auto;'>
              <div class='order-title' style='text-align:center;flex-grow:1;margin:0 10px;'>
                <h1 style='margin:0;font-size:18px;'>ORDEM DE PRODUÇÃO</h1>
                <h2 style='margin:3px 0;font-size:16px;'>${ordem.numero}</h2>
              </div>
              <div style='text-align:right;font-size:10px;'>
                <strong>Data: </strong>${new Date().toLocaleDateString()}<br>
                <strong>Hora: </strong>${new Date().toLocaleTimeString()}
              </div>
            </div>
            <div class='order-info' style='display:grid;grid-template-columns:repeat(4,1fr);gap:5px;margin-bottom:15px;border:1px solid #ccc;padding:5px;'>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Produto:</strong><span style='font-size:12px;'>${produto.codigo || ''} - ${produto.descricao || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Tipo:</strong><span style='font-size:12px;'>${produto.tipo || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Quantidade:</strong><span style='font-size:12px;'>${ordem.quantidade} ${produto.unidade || ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Status:</strong><span class='status-badge status-${ordem.status.toLowerCase()}' style='font-size:10px;font-weight:bold;'>${ordem.status}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Criação:</strong><span style='font-size:12px;'>${ordem.dataCriacao ? new Date(ordem.dataCriacao.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Data de Entrega:</strong><span style='font-size:12px;'>${ordem.dataEntrega ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString() : ''}</span></div>
              <div class='info-item' style='border:1px solid #ddd;padding:3px;'><strong style='font-size:8px;color:#666;'>Prioridade:</strong><span style='font-size:12px;'>${ordem.prioridade || 'Normal'}</span></div>
            </div>
            ${(Array.isArray(ordem.materiaisNecessarios) && ordem.materiaisNecessarios.length > 0) ? `
            <div class='section' style='margin-bottom:15px;'>
              <div class='section-title' style='background:#f0f0f0;padding:3px 8px;font-weight:bold;border:1px solid #ccc;font-size:11px;'>LISTA DE MATERIAIS</div>
              <table style='width:100%;border-collapse:collapse;margin-top:3px;font-size:11px;'>
                <thead>
                  <tr>
                    <th style='border:1px solid #ccc;padding:4px;'>Código</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Descrição</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Tipo</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Quantidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Unidade</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Disponível</th>
                    <th style='border:1px solid #ccc;padding:4px;'>Necessidade</th>
                  </tr>
                </thead>
                <tbody>
                  ${ordem.materiaisNecessarios.map(material => {
                    const materialProduto = produtos.find(p => p.id === material.produtoId) || {};
                    const estoque = estoques.find(e => e.produtoId === material.produtoId && e.armazemId === (material.armazemId || ordem.armazemProducaoId)) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };
                    const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, ordem.id);

                    return `<tr>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.codigo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.descricao || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.tipo || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${formatarNumero(material.quantidade)}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${materialProduto.unidade || ''}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${formatarNumero(saldoDisponivel)}</td>
                      <td style='border:1px solid #ccc;padding:4px;'>${formatarNumero(material.necessidade !== undefined ? material.necessidade : material.quantidade)}</td>
                    </tr>`;
                  }).join('')}
                </tbody>
              </table>
            </div>` : ''}
            ${roteiroHtml}
            <div class='signatures' style='margin-top:20px;display:flex;justify-content:space-between;'>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Produção</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Qualidade</div></div>
              <div class='signature' style='flex:1;margin:0 10px;text-align:center;'><div class='signature-line' style='width:100%;border-top:1px solid #000;margin-top:20px;padding-top:3px;font-size:10px;'>Supervisor</div></div>
            </div>
          </div>
        </div>
      `;
      // Abrir nova janela e imprimir
      const printWindow = window.open('', '_blank');

      // Verificar se a janela foi aberta com sucesso (pode ser bloqueada por pop-up blocker)
      if (!printWindow) {
        alert('❌ Pop-up bloqueado!\n\nPor favor, permita pop-ups para este site e tente novamente.\n\nOu use Ctrl+P para imprimir a tela atual.');
        return;
      }

      try {
        printWindow.document.write(`<!DOCTYPE html><html><head><title>Impressão OP</title><meta charset='UTF-8'><style>@page{size:A4 portrait;margin:15mm;}@media print{.no-print{display:none;}}</style></head><body>${printArea.innerHTML}<script>window.onload=function(){window.print();}<' + '/script></body></html>`);
        printWindow.document.close();
      } catch (error) {
        console.error('Erro ao preparar impressão:', error);
        alert('❌ Erro ao preparar impressão: ' + error.message);
        if (printWindow) {
          printWindow.close();
        }
      }
    }

    // Função removida: marcarEnviadaParaFabrica (redundante com iniciarProducao)

    // Função para atualizar dashboard de métricas
    window.atualizarDashboard = function() {
      try {
        const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');
        const ordensEmProducao = ordensAtivas.filter(op => op.status === 'Em Produção');

        // Calcular OPs atrasadas (data de entrega passou)
        const hoje = new Date();
        const opsAtrasadas = ordensAtivas.filter(op => {
          if (!op.dataEntrega?.seconds) return false;
          const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
          return dataEntrega < hoje;
        });

        // Calcular materiais em falta
        let materiaisEmFalta = 0;
        ordensAtivas.forEach(ordem => {
          if (ordem.materiaisNecessarios) {
            ordem.materiaisNecessarios.forEach(material => {
              const estoque = estoques.find(e =>
                e.produtoId === material.produtoId &&
                e.armazemId === (material.armazemId || ordem.armazemProducaoId)
              ) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };

              const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, ordem.id);
              const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

              if (saldoDisponivel < quantidadeRestante) {
                materiaisEmFalta++;
              }
            });
          }
        });

        // Atualizar elementos do dashboard
        document.getElementById('totalOPs').textContent = ordensAtivas.length;
        document.getElementById('emProducao').textContent = ordensEmProducao.length;
        document.getElementById('materiaisFalta').textContent = materiaisEmFalta;
        document.getElementById('opsAtrasadas').textContent = opsAtrasadas.length;

        // Dashboard atualizado silenciosamente

      } catch (error) {
        console.error('Erro ao atualizar dashboard:', error);
      }
    };

    // Sistema de notificações
    function mostrarNotificacao(mensagem, tipo = 'info', duracao = 5000) {
      // Remover notificações existentes
      const existingNotifications = document.querySelectorAll('.notification');
      existingNotifications.forEach(n => n.remove());

      // Criar nova notificação
      const notification = document.createElement('div');
      notification.className = `notification ${tipo}`;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="fas fa-${getIconForType(tipo)}"></i>
          <span>${mensagem}</span>
          <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
        </div>
      `;

      document.body.appendChild(notification);

      // Mostrar notificação
      setTimeout(() => notification.classList.add('show'), 100);

      // Remover automaticamente
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
      }, duracao);
    }

    function getIconForType(tipo) {
      switch (tipo) {
        case 'success': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'error': return 'times-circle';
        case 'info':
        default: return 'info-circle';
      }
    }

    // Verificar alertas automaticamente
    function verificarAlertas() {
      const ordensAtivas = ordensProducao.filter(op => op.status !== 'Concluída' && op.status !== 'Cancelada');

      // Verificar OPs atrasadas
      const hoje = new Date();
      const opsAtrasadas = ordensAtivas.filter(op => {
        if (!op.dataEntrega?.seconds) return false;
        const dataEntrega = new Date(op.dataEntrega.seconds * 1000);
        return dataEntrega < hoje;
      });

      if (opsAtrasadas.length > 0) {
        mostrarNotificacao(`⚠️ ${opsAtrasadas.length} ordem(ns) de produção atrasada(s)!`, 'warning', 8000);
      }

      // Verificar materiais em falta críticos
      let materiaisCriticos = 0;
      ordensAtivas.forEach(ordem => {
        if (ordem.materiaisNecessarios) {
          ordem.materiaisNecessarios.forEach(material => {
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || ordem.armazemProducaoId)
            ) || { saldo: 0, saldoReservado: 0, saldoEmpenhado: 0 };

            const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, ordem.id);
            const quantidadeRestante = material.quantidade - (ordem.quantidadeProduzida || 0) * (material.quantidade / ordem.quantidade);

            if (saldoDisponivel <= 0 && quantidadeRestante > 0) {
              materiaisCriticos++;
            }
          });
        }
      });

      if (materiaisCriticos > 0) {
        mostrarNotificacao(`🚨 ${materiaisCriticos} material(is) em falta crítica!`, 'error', 10000);
      }
    }

    // Executar verificação de alertas a cada 5 minutos
    setInterval(verificarAlertas, 5 * 60 * 1000);

    // ===================================================================
    // SISTEMA DE PERSISTÊNCIA DE FILTROS MELHORADO
    // ===================================================================

    // Chave única para localStorage
    const FILTROS_STORAGE_KEY = 'apontamentos_filtros_v2';

    // Função para salvar filtros no localStorage
    function salvarFiltrosLocalStorage() {
      try {
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');
        const dataInicioFilter = document.getElementById('dataInicioFilter');
        const dataFimFilter = document.getElementById('dataFimFilter');

        const filtros = {
          termo: searchInput ? searchInput.value : '',
          filtroStatus: statusFilter ? statusFilter.value : '',
          filtroDataInicio: dataInicioFilter ? dataInicioFilter.value : '',
          filtroDataFim: dataFimFilter ? dataFimFilter.value : '',
          timestamp: Date.now() // Para verificar idade dos filtros
        };

        localStorage.setItem(FILTROS_STORAGE_KEY, JSON.stringify(filtros));

      } catch (error) {
        console.warn('⚠️ Erro ao salvar filtros:', error);
      }
    }

    // Função para carregar filtros do localStorage
    function carregarFiltrosLocalStorage() {
      try {
        const filtrosSalvos = localStorage.getItem(FILTROS_STORAGE_KEY);
        if (!filtrosSalvos) return null;

        const filtros = JSON.parse(filtrosSalvos);

        // Verificar se os filtros não são muito antigos (24 horas = 86400000ms)
        const idade = Date.now() - (filtros.timestamp || 0);
        if (idade > 86400000) {
          console.log('🗑️ Filtros muito antigos (>24h), limpando...');
          localStorage.removeItem(FILTROS_STORAGE_KEY);
          return null;
        }


        return filtros;
      } catch (error) {
        console.warn('⚠️ Erro ao carregar filtros:', error);
        localStorage.removeItem(FILTROS_STORAGE_KEY); // Limpar se houver erro
        return null;
      }
    }

    // Função para aplicar filtros salvos aos elementos
    function aplicarFiltrosSalvos(filtros) {
      if (!filtros) return;

      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      if (searchInput && filtros.termo) {
        searchInput.value = filtros.termo;
      }

      if (statusFilter && filtros.filtroStatus) {
        statusFilter.value = filtros.filtroStatus;
      }

      if (dataInicioFilter && filtros.filtroDataInicio) {
        dataInicioFilter.value = filtros.filtroDataInicio;
      }

      if (dataFimFilter && filtros.filtroDataFim) {
        dataFimFilter.value = filtros.filtroDataFim;
      }

      // 🔧 CORREÇÃO: Aplicar filtros após definir os valores
      console.log('🔄 Aplicando filtros salvos:', filtros);
      setTimeout(() => {
        filterOrders();
        console.log('✅ Filtros salvos aplicados com sucesso');
      }, 100);
    }

    // Função para limpar filtros persistidos
    function limparFiltrosPersistidos() {
      try {
        localStorage.removeItem(FILTROS_STORAGE_KEY);
        console.log('🗑️ Filtros persistidos limpos');
      } catch (error) {
        console.warn('⚠️ Erro ao limpar filtros:', error);
      }
    }

    // Função para verificar se há filtros ativos
    function temFiltrosAtivos() {
      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      return (searchInput && searchInput.value.trim()) ||
             (statusFilter && statusFilter.value) ||
             (dataInicioFilter && dataInicioFilter.value) ||
             (dataFimFilter && dataFimFilter.value);
    }

    // Função MELHORADA para salvar estado da busca
    function salvarEstadoBusca() {
      salvarFiltrosLocalStorage();

      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      estadoBusca = {
        termo: searchInput ? searchInput.value : '',
        filtroStatus: statusFilter ? statusFilter.value : '',
        filtroDataInicio: dataInicioFilter ? dataInicioFilter.value : '',
        filtroDataFim: dataFimFilter ? dataFimFilter.value : '',
        ordemAtual: [...ordensProducao],
        preservar: true,
        timestamp: Date.now()
      };


    }

    // Função MELHORADA para restaurar estado da busca
    function restaurarEstadoBusca() {
      // Primeiro tentar carregar do localStorage
      const filtrosSalvos = carregarFiltrosLocalStorage();

      if (filtrosSalvos) {
        console.log('🔄 Restaurando filtros do localStorage:', filtrosSalvos);
        aplicarFiltrosSalvos(filtrosSalvos);
        // A função aplicarFiltrosSalvos já chama filterOrders()
        return;
      }

      // Fallback para sistema antigo
      if (!estadoBusca.preservar) {
        console.log('⚠️ Estado de busca não preservado');
        return;
      }



      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      if (searchInput) searchInput.value = estadoBusca.termo;
      if (statusFilter) statusFilter.value = estadoBusca.filtroStatus;
      if (dataInicioFilter) dataInicioFilter.value = estadoBusca.filtroDataInicio;
      if (dataFimFilter) dataFimFilter.value = estadoBusca.filtroDataFim;

      if (estadoBusca.termo || estadoBusca.filtroStatus || 
          estadoBusca.filtroDataInicio || estadoBusca.filtroDataFim) {
        setTimeout(() => {
          filterOrders();
          console.log('✅ Filtros aplicados via sistema legado');
        }, 100);
      }
    }

    // Função para preservar busca e recarregar dados (MELHORADA)
    async function preservarERecarregar() {
      console.log('🔄 Preservando filtros e recarregando...');

      // Salvar filtros antes de recarregar
      salvarEstadoBusca();

      // Recarregar dados
      await loadOrders();

      // 🔧 CORREÇÃO: Restaurar filtros após recarregar
      setTimeout(() => {
        restaurarEstadoBusca();
        console.log('✅ Filtros restaurados após recarregamento');
      }, 200);
    }

    // Função para configurar listeners de busca (MELHORADA)
    function setupSearchListeners() {
      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');

      // 🔧 CORREÇÃO: Aplicar filtros imediatamente + salvar com debounce
      if (searchInput) {
        let debounceTimer;

        // Aplicar filtro imediatamente quando usuário digita
        searchInput.addEventListener('input', () => {
          // Aplicar filtro imediatamente para resposta rápida
          filterOrders();

          // Salvar com debounce para evitar excesso de salvamentos
          clearTimeout(debounceTimer);
          debounceTimer = setTimeout(() => {
            salvarFiltrosLocalStorage();
            atualizarIndicadorFiltros();
            console.log('💾 Filtro salvo automaticamente');
          }, 300);
        });

        searchInput.addEventListener('keyup', () => {
          // Garantir que filtro seja aplicado em qualquer tecla
          filterOrders();

          clearTimeout(debounceTimer);
          debounceTimer = setTimeout(() => {
            salvarFiltrosLocalStorage();
            atualizarIndicadorFiltros();
          }, 300);
        });
      }

      if (statusFilter) {
        statusFilter.addEventListener('change', () => {
          // Aplicar filtro imediatamente
          filterOrders();

          // Salvar filtros
          salvarFiltrosLocalStorage();
          atualizarIndicadorFiltros();
          console.log('💾 Status filter salvo automaticamente');
        });
      }


    }

    // Função para limpar preservação (compatibilidade)
    function limparPreservacao() {
      // Não limpar automaticamente - apenas quando solicitado explicitamente
      console.log('🗑️ Limpeza de preservação solicitada (ignorada para manter filtros)');
    }

    // Função para limpar filtros manualmente
    window.limparFiltros = function() {
      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      if (searchInput) searchInput.value = '';
      if (statusFilter) statusFilter.value = '';
      if (dataInicioFilter) dataInicioFilter.value = '';
      if (dataFimFilter) dataFimFilter.value = '';

      limparFiltrosPersistidos();
      limparPreservacao(); // Limpar preservação também
      filterOrders(); // Já inclui atualizarIndicadorFiltros()

      mostrarNotificacao('🗑️ Filtros limpos', 'info', 2000);
      console.log('🗑️ Filtros limpos manualmente');
    }

    // Função para mostrar status dos filtros
    window.mostrarStatusFiltros = function() {
      const filtrosSalvos = carregarFiltrosLocalStorage();
      const temFiltros = temFiltrosAtivos();

      let mensagem = '🔍 STATUS DOS FILTROS:\n\n';

      if (temFiltros) {
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');
        const dataInicioFilter = document.getElementById('dataInicioFilter');
        const dataFimFilter = document.getElementById('dataFimFilter');

        mensagem += '✅ FILTROS ATIVOS:\n';
        if (searchInput && searchInput.value) mensagem += `• Busca: "${searchInput.value}"\n`;
        if (statusFilter && statusFilter.value) mensagem += `• Status: ${statusFilter.value}\n`;
        if (dataInicioFilter && dataInicioFilter.value) mensagem += `• Data Início: ${dataInicioFilter.value}\n`;
        if (dataFimFilter && dataFimFilter.value) mensagem += `• Data Fim: ${dataFimFilter.value}\n`;
      } else {
        mensagem += '❌ Nenhum filtro ativo\n';
      }

      if (filtrosSalvos) {
        mensagem += `\n💾 Filtros salvos: ${new Date(filtrosSalvos.timestamp).toLocaleString()}`;
      } else {
        mensagem += '\n💾 Nenhum filtro salvo';
      }

      alert(mensagem);
    }

    // Função para atualizar indicador visual dos filtros persistidos
    function atualizarIndicadorFiltros() {
      const indicador = document.getElementById('indicadorFiltrosPersistidos');
      const temFiltros = temFiltrosAtivos();

      if (indicador) {
        if (temFiltros) {
          indicador.style.display = 'inline-block';
          indicador.innerHTML = '<i class="fas fa-save"></i> Filtros Salvos';

          // Criar tooltip com informações dos filtros ativos
          const filtrosAtivos = [];
          const searchInput = document.getElementById('searchInput');
          const statusFilter = document.getElementById('statusFilter');
          const dataInicioFilter = document.getElementById('dataInicioFilter');
          const dataFimFilter = document.getElementById('dataFimFilter');

          if (searchInput && searchInput.value) filtrosAtivos.push(`Busca: "${searchInput.value}"`);
          if (statusFilter && statusFilter.value) filtrosAtivos.push(`Status: ${statusFilter.value}`);
          if (dataInicioFilter && dataInicioFilter.value) filtrosAtivos.push(`Data início: ${dataInicioFilter.value}`);
          if (dataFimFilter && dataFimFilter.value) filtrosAtivos.push(`Data fim: ${dataFimFilter.value}`);

          const tooltipText = `Filtros aplicados:\n${filtrosAtivos.join('\n')}\n\nOs filtros são salvos automaticamente\n\n💡 Clique para ver resumo detalhado`;
          indicador.title = tooltipText;
        } else {
          indicador.style.display = 'none';
        }
      }
    }

    // Função para inicializar sistema de filtros
    function inicializarSistemaFiltros() {


      // Aguardar elementos estarem disponíveis
      setTimeout(() => {
        // Configurar listeners
        setupSearchListeners();

        // Carregar filtros salvos
        const filtrosSalvos = carregarFiltrosLocalStorage();
        if (filtrosSalvos) {
          console.log('🔄 Inicializando com filtros salvos:', filtrosSalvos);
          aplicarFiltrosSalvos(filtrosSalvos);
          atualizarIndicadorFiltros();
          console.log('✅ Filtros iniciais aplicados');
        }

        // Configurar listeners para filtros de data
        const dataInicioFilter = document.getElementById('dataInicioFilter');
        const dataFimFilter = document.getElementById('dataFimFilter');

        if (dataInicioFilter) {
          dataInicioFilter.addEventListener('change', () => {
            filterOrders();
            salvarFiltrosLocalStorage();
            atualizarIndicadorFiltros();
            console.log('💾 Data início salva automaticamente');
          });
        }

        if (dataFimFilter) {
          dataFimFilter.addEventListener('change', () => {
            filterOrders();
            salvarFiltrosLocalStorage();
            atualizarIndicadorFiltros();
            console.log('💾 Data fim salva automaticamente');
          });
        }


      }, 100);
    }

    // Função para mostrar resumo dos filtros aplicados
    window.mostrarResumoFiltros = function() {
      const filtrosAtivos = [];
      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const dataInicioFilter = document.getElementById('dataInicioFilter');
      const dataFimFilter = document.getElementById('dataFimFilter');

      if (searchInput && searchInput.value) filtrosAtivos.push(`📝 Busca: "${searchInput.value}"`);
      if (statusFilter && statusFilter.value) filtrosAtivos.push(`📊 Status: ${statusFilter.value}`);
      if (dataInicioFilter && dataInicioFilter.value) filtrosAtivos.push(`📅 Data início: ${dataInicioFilter.value}`);
      if (dataFimFilter && dataFimFilter.value) filtrosAtivos.push(`📅 Data fim: ${dataFimFilter.value}`);

      const filtrosSalvos = carregarFiltrosLocalStorage();
      const dataUltimaSalva = filtrosSalvos ? new Date(filtrosSalvos.timestamp).toLocaleString() : 'Nunca';

      // Contar linhas visíveis
      const linhasVisiveis = document.querySelectorAll('#ordersTableBody tr[style=""], #ordersTableBody tr:not([style*="display: none"])').length;
      const totalLinhas = document.querySelectorAll('#ordersTableBody tr').length;

      let mensagem = `🔍 RESUMO DOS FILTROS APLICADOS\n\n`;

      if (filtrosAtivos.length > 0) {
        mensagem += `✅ FILTROS ATIVOS (${filtrosAtivos.length}):\n`;
        filtrosAtivos.forEach(filtro => {
          mensagem += `  ${filtro}\n`;
        });
      } else {
        mensagem += `❌ Nenhum filtro ativo\n`;
      }

      mensagem += `\n📊 RESULTADOS:\n`;
      mensagem += `  • ${linhasVisiveis} de ${totalLinhas} OPs visíveis\n`;
      mensagem += `  • ${((linhasVisiveis / totalLinhas) * 100).toFixed(1)}% das OPs são mostradas\n\n`;

      mensagem += `💾 PERSISTÊNCIA:\n`;
      mensagem += `  • Última gravação: ${dataUltimaSalva}\n`;
      mensagem += `  • Auto-save: ${filtrosAtivos.length > 0 ? 'Ativo' : 'Inativo'}\n`;
      mensagem += `  • Expiração: 1 hora após última gravação\n\n`;

      mensagem += `💡 DICAS:\n`;
      mensagem += `  • Os filtros são salvos automaticamente\n`;
      mensagem += `  • Use "Limpar Filtros" para remover tudo\n`;
      mensagem += `  • Clique em "Status dos Filtros" para mais detalhes`;

      alert(mensagem);
    }

    // ===================================================================
    // CONTROLE DE FLUXO SEQUENCIAL DE PRODUÇÃO
    // ===================================================================

    // Função para verificar o status atual da OP e determinar quais ações estão disponíveis
    function getOPFlowStatus(ordem) {
      const status = {
        temSaldo: false,
        podeImprimir: false,
        podeEnviarFabrica: false,
        podeApontar: false,
        etapaAtual: 'VERIFICAR_SALDO',
        proximaEtapa: null
      };

      // 1. TRANSFERIR MATERIAL (primeira etapa)
      if (!ordem.materialTransferido) {
        status.etapaAtual = 'TRANSFERIR_MATERIAL';
        status.podeTransferirMaterial = true;
        status.proximaEtapa = 'VERIFICAR_SALDO';
        return status;
      }

      // 2. VERIFICAR SALDO (segunda etapa - após transferência)
      if (!ordem.saldoValidado) {
        status.etapaAtual = 'VERIFICAR_SALDO';
        status.podeTransferirMaterial = true;
        status.podeVerificarSaldo = true;
        status.proximaEtapa = 'IMPRIMIR';
        return status;
      }
      status.temSaldo = true;

      // 3. IMPRIMIR OP (terceira etapa)
      if (!ordem.impressa) {
        status.etapaAtual = 'IMPRIMIR';
        status.podeTransferirMaterial = true;
        status.podeVerificarSaldo = true;
        status.podeImprimir = true;
        status.proximaEtapa = 'ENVIAR_FABRICA';
        return status;
      }

      // 4. ENVIAR PARA FÁBRICA (quarta etapa)
      if (!ordem.enviadaFabrica) {
        status.etapaAtual = 'ENVIAR_FABRICA';
        status.podeTransferirMaterial = true;
        status.podeVerificarSaldo = true;
        status.podeImprimir = true;
        status.podeEnviarFabrica = true;
        status.proximaEtapa = 'APONTAR';
        return status;
      }

      // 5. APONTAR (última etapa)
      status.etapaAtual = 'APONTAR';
      status.podeTransferirMaterial = true;
      status.podeVerificarSaldo = true;
      status.podeImprimir = true;
      status.podeEnviarFabrica = true;
      status.podeApontar = true;
      status.proximaEtapa = null;

      return status;
    }

    // Função para atualizar o status da OP no banco de dados
    async function updateOPFlowStatus(opId, campo, valor) {
      try {
        const updateData = {};
        updateData[campo] = valor;
        updateData.ultimaAtualizacao = Timestamp.now();

        await updateDoc(doc(db, "ordensProducao", opId), updateData);

        // Atualizar localmente
        const ordem = ordensProducao.find(op => op.id === opId);
        if (ordem) {
          ordem[campo] = valor;
          ordem.ultimaAtualizacao = new Date();
        }

        console.log(`✅ Status atualizado: ${campo} = ${valor} para OP ${opId}`);
        return true;
      } catch (error) {
        console.error('Erro ao atualizar status da OP:', error);
        return false;
      }
    }

    // Função para renderizar botões baseado no fluxo sequencial INTELIGENTE
    function renderFlowButtons(ordem) {
      const flowStatus = getOPFlowStatus(ordem);
      let buttons = '';

      // ===== VERIFICAR SE OP ESTÁ FINALIZADA =====
      if (ordem.status === 'Concluída' || ordem.status === 'Cancelada') {
        return `
          <div style="text-align: center; padding: 10px; color: #6c757d; font-style: italic;">
            <i class="fas fa-${ordem.status === 'Concluída' ? 'check-circle' : 'times-circle'}"></i>
            OP ${ordem.status}
          </div>
        `;
      }

      // ===== DEFINIR ETAPAS DO FLUXO =====
      const etapas = [
        {
          id: 'transferencia',
          nome: 'Transferir Material',
          numero: 1,
          concluida: ordem.materialTransferido,
          podeExecutar: !ordem.materialTransferido,
          funcao: 'transferirMaterialProducao',
          icone: 'exchange-alt'
        },
        {
          id: 'saldo',
          nome: 'Verificar Saldo',
          numero: 2,
          concluida: ordem.saldoValidado,
          podeExecutar: ordem.materialTransferido && !ordem.saldoValidado,
          funcao: 'verificarSaldoOP',
          icone: 'search'
        },
        {
          id: 'impressao',
          nome: 'Imprimir OP',
          numero: 3,
          concluida: ordem.impressa,
          podeExecutar: ordem.saldoValidado && !ordem.impressa,
          funcao: 'printOrderReport',
          icone: 'print'
        },
        {
          id: 'fabrica',
          nome: 'Enviar Fábrica',
          numero: 4,
          concluida: ordem.enviadaFabrica,
          podeExecutar: ordem.impressa && !ordem.enviadaFabrica,
          funcao: 'enviarParaFabrica',
          icone: 'industry'
        },
        {
          id: 'apontamento',
          nome: 'Apontar',
          numero: 5,
          concluida: false, // Apontamento pode ser feito múltiplas vezes
          podeExecutar: ordem.enviadaFabrica,
          funcao: 'openAppointmentModal',
          icone: 'clipboard'
        }
      ];

      // ===== RENDERIZAR ETAPAS =====
      etapas.forEach(etapa => {
        if (etapa.id === 'transferencia') {
          // ETAPA 1: Transferência de Material (tratamento especial)
          const dadosTransferencia = ordem.dadosTransferencia || {};
          const statusGeral = dadosTransferencia.statusGeral || (ordem.materialTransferido ? 'completo' : 'nao_transferido');

          let statusTexto = '';
          let corStatus = '';
          let iconeStatus = '';
          let tituloStatus = '';
          let metodoTexto = '';

          // Verificar método de registro
          if (dadosTransferencia.metodo) {
            switch(dadosTransferencia.metodo) {
              case 'integracao_automatica':
                metodoTexto = ' 🤖';
                break;
              case 'verificacao_manual':
                metodoTexto = ' ✅';
                break;
              case 'marcacao_rapida':
                metodoTexto = ' 👤';
                break;
            }
          }

          switch(statusGeral) {
            case 'completo':
              statusTexto = 'Transferido' + metodoTexto;
              corStatus = '#28a745'; // Verde
              iconeStatus = 'fas fa-check-circle';
              tituloStatus = 'Material transferido completamente para produção';
              if (dadosTransferencia.totalTransferencias) {
                tituloStatus += `\n📦 ${dadosTransferencia.totalTransferencias} transferência(s) registrada(s)`;
              }
              if (dadosTransferencia.ultimaVerificacao) {
                tituloStatus += `\n🕒 Última verificação: ${new Date(dadosTransferencia.ultimaVerificacao).toLocaleString()}`;
              }
              break;
            case 'parcial':
              statusTexto = 'Parcial' + metodoTexto;
              corStatus = '#ffc107'; // Amarelo
              iconeStatus = 'fas fa-exclamation-triangle';
              tituloStatus = 'Material transferido parcialmente para produção';
              if (dadosTransferencia.statusMateriais) {
                const materiaisCompletos = dadosTransferencia.statusMateriais.filter(m => m.status === 'completo').length;
                const totalMateriais = dadosTransferencia.statusMateriais.length;
                tituloStatus += `\n📊 ${materiaisCompletos}/${totalMateriais} materiais transferidos`;
              }
              break;
            default: // nao_transferido
              statusTexto = 'Não Transferido';
              corStatus = '#17a2b8'; // Azul
              iconeStatus = 'fas fa-clock';
              tituloStatus = 'Material ainda não foi transferido para produção';
          }

          // RENDERIZAR ETAPA 1: Transferência (ÍCONE PEQUENO)
          if (etapa.concluida) {
            // ✅ CONCLUÍDA: Ícone pequeno verde
            buttons += `
              <span style="display: inline-block; background: #28a745; color: white; margin-right: 3px; padding: 6px; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 20px; opacity: 0.9;"
                    title="${tituloStatus} - Etapa concluída">
                <i class="fas fa-check" style="font-size: 12px;"></i>
              </span>
            `;
          } else {
            // 🔄 PENDENTE: Ícone pequeno clicável
            buttons += `
              <button onclick="${etapa.funcao}('${ordem.id}')"
                      style="background: ${corStatus}; color: white; margin-right: 3px; border: none; padding: 6px; border-radius: 50%; width: 32px; height: 32px; cursor: pointer; font-size: 12px;"
                      title="${tituloStatus} - Clique para executar">
                <i class="${iconeStatus}" style="font-size: 12px;"></i>
              </button>
            `;
          }

        } else {
          // OUTRAS ETAPAS: Ícones pequenos
          if (etapa.concluida) {
            // ✅ CONCLUÍDA: Ícone pequeno verde
            buttons += `
              <span style="display: inline-block; background: #28a745; color: white; margin-right: 3px; padding: 6px; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 20px; opacity: 0.9;"
                    title="Etapa ${etapa.numero}: ${etapa.nome} - Concluída">
                <i class="fas fa-check" style="font-size: 12px;"></i>
              </span>
            `;
          } else if (etapa.podeExecutar) {
            // 🔄 PODE EXECUTAR: Ícone pequeno clicável
            const corBotao = etapa.id === 'saldo' ? '#ffc107' :
                           etapa.id === 'impressao' ? '#6c757d' :
                           etapa.id === 'fabrica' ? '#28a745' : '#fd7e14';

            buttons += `
              <button onclick="${etapa.funcao}('${ordem.id}')"
                      style="background: ${corBotao}; color: white; margin-right: 3px; border: none; padding: 6px; border-radius: 50%; width: 32px; height: 32px; cursor: pointer; font-size: 12px;"
                      title="Etapa ${etapa.numero}: ${etapa.nome} - Clique para executar">
                <i class="fas fa-${etapa.icone}" style="font-size: 12px;"></i>
              </button>
            `;
          } else {
            // ⏳ BLOQUEADA: Ícone pequeno desabilitado
            buttons += `
              <span style="display: inline-block; background: #e9ecef; color: #6c757d; margin-right: 3px; padding: 6px; border-radius: 50%; width: 32px; height: 32px; text-align: center; line-height: 20px; opacity: 0.6;"
                    title="Etapa ${etapa.numero}: ${etapa.nome} - Bloqueada (complete etapas anteriores)">
                <i class="fas fa-${etapa.icone}" style="font-size: 12px;"></i>
              </span>
            `;
          }
        }
      });

      // ===== BOTÕES DE EMPENHOS E DEBUG REMOVIDOS =====
      // Empenhos agora será gerenciado em tela separada
      // Debug removido pois o sistema agora é automático

      return buttons;
    }

    // Função auxiliar para obter próxima etapa
    function getProximaEtapa(ordem) {
      if (!ordem.materialTransferido) return 'Transferir Material';
      if (!ordem.saldoValidado) return 'Verificar Saldo';
      if (!ordem.impressa) return 'Imprimir OP';
      if (!ordem.enviadaFabrica) return 'Enviar para Fábrica';
      if (ordem.status !== 'Concluída') return 'Fazer Apontamentos';
      return 'Processo Concluído';
    }

    // Função para calcular progresso da OP
    function calcularProgressoOP(ordem) {
      if (ordem.status === 'Concluída') return 100;
      if (ordem.status === 'Cancelada') return 0;

      let etapas = 0;
      if (ordem.materialTransferido) etapas++;
      if (ordem.saldoValidado) etapas++;
      if (ordem.impressa) etapas++;
      if (ordem.enviadaFabrica) etapas++;

      return Math.round((etapas / 4) * 100);
    }

    // Função para renderizar indicador de progresso
    function renderProgressIndicator(ordem) {
      if (ordem.status === 'Concluída' || ordem.status === 'Cancelada') {
        return '';
      }

      const steps = [
        { key: 'materialTransferido', icon: 'exchange-alt', label: 'Transferido' },
        { key: 'saldoValidado', icon: 'search', label: 'Saldo' },
        { key: 'impressa', icon: 'print', label: 'Impressa' },
        { key: 'enviadaFabrica', icon: 'industry', label: 'Fábrica' },
        { key: 'podeApontar', icon: 'clipboard', label: 'Apontar', check: () => ordem.enviadaFabrica }
      ];

      let progressHtml = '<div class="progress-indicator" style="margin-top: 5px; display: flex; gap: 3px;">';

      steps.forEach((step, index) => {
        const isCompleted = step.check ? step.check() : ordem[step.key];
        const stepClass = isCompleted ? 'step-completed' : 'step-pending';
        const stepIcon = isCompleted ? 'check' : step.icon;

        progressHtml += `
          <div class="progress-step ${stepClass}"
               title="${step.label}${isCompleted ? ' ✓' : ''}"
               style="
                 width: 20px;
                 height: 20px;
                 border-radius: 50%;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 font-size: 10px;
                 ${isCompleted
                   ? 'background: #28a745; color: white;'
                   : 'background: #e9ecef; color: #6c757d; border: 1px solid #dee2e6;'
                 }
               ">
            <i class="fas fa-${stepIcon}"></i>
          </div>
        `;
      });

      progressHtml += '</div>';
      return progressHtml;
    }

    // ===================================================================
    // FUNÇÕES DO FLUXO SEQUENCIAL
    // ===================================================================

    // ETAPA 1: Visualizar Status da Transferência de Material
    window.transferirMaterialProducao = async function(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        // Verificar status atual da transferência
        if (ordem.materialTransferido) {
          const dadosTransferencia = ordem.dadosTransferencia || {};
          const statusGeral = dadosTransferencia.statusGeral || 'completo';
          const dataRegistro = dadosTransferencia.dataRegistro ? new Date(dadosTransferencia.dataRegistro).toLocaleString() : 'Data não informada';

          let statusTexto = '';
          let icone = '';

          switch(statusGeral) {
            case 'completo':
              statusTexto = 'TRANSFERÊNCIA COMPLETA - Todos os materiais na produção';
              icone = '✅';
              break;
            case 'parcial':
              statusTexto = 'TRANSFERÊNCIA PARCIAL - Alguns materiais transferidos';
              icone = '⚠️';
              break;
            default:
              statusTexto = 'TRANSFERÊNCIA REGISTRADA';
              icone = '📋';
          }

          alert(`📦 STATUS DA TRANSFERÊNCIA\n\nOP: ${nomeOP}\n\n${icone} ${statusTexto}\n\n📅 Registrado: ${dataRegistro}\n\n💡 A transferência é feita na tela "Movimentação de Armazém".\nEsta tela apenas mostra o status atual.`);
        } else {
          alert(`📦 TRANSFERÊNCIA DE MATERIAL\n\nOP: ${nomeOP}\n\n🔵 STATUS: Não transferido\n\n💡 Para transferir materiais:\n1. Acesse "Movimentação de Armazém"\n2. Faça as transferências necessárias\n3. O status será atualizado automaticamente aqui`);
        }

      } catch (error) {
        console.error('Erro ao verificar status de transferência:', error);
        alert('❌ Erro ao verificar status de transferência: ' + error.message);
      }
    };

    // Funções de transferência removidas - transferência ocorre na tela movimentacao_armazem.html

    // Função para mostrar modal de transferência de material (REMOVIDA - não será mais usada)
    window.mostrarModalTransferenciaMaterial_OLD = function(opId, nomeOP, materiais, dadosExistentes = null) {
      // Criar overlay do modal
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.7); z-index: 10000; display: flex;
        align-items: center; justify-content: center;
      `;

      // Criar conteúdo do modal
      const modal = document.createElement('div');
      modal.style.cssText = `
        background: white; border-radius: 12px; padding: 25px;
        max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      `;

      // Gerar lista de materiais
      let materiaisHtml = '';
      materiais.forEach((material, index) => {
        // Buscar dados do produto
        const produto = produtos.find(p => p.id === material.produtoId) || {};
        const nome = produto.descricao || produto.codigo || material.nome || material.codigo || `Material ${index + 1}`;
        const codigo = produto.codigo || material.codigo || material.produtoId;
        const unidade = produto.unidade || material.unidade || 'UN';

        materiaisHtml += `
          <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; background: #f8f9fa;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
              <div>
                <strong style="color: #2c3e50;">${nome}</strong>
                <br><small style="color: #6c757d;">Código: ${codigo}</small>
              </div>
              <span style="background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                Necessário: ${material.quantidade} ${unidade}
              </span>
            </div>
            <div style="display: flex; gap: 10px; align-items: center;">
              <label style="margin: 0; display: flex; align-items: center; gap: 5px;">
                <input type="radio" name="status_${index}" value="completo" style="margin: 0;" ${dadosExistentes && dadosExistentes.statusMateriais && dadosExistentes.statusMateriais[index] === 'completo' ? 'checked' : ''}>
                <span style="color: #28a745;">✅ Transferido Completo</span>
              </label>
              <label style="margin: 0; display: flex; align-items: center; gap: 5px;">
                <input type="radio" name="status_${index}" value="parcial" style="margin: 0;" ${dadosExistentes && dadosExistentes.statusMateriais && dadosExistentes.statusMateriais[index] === 'parcial' ? 'checked' : ''}>
                <span style="color: #ffc107;">⚠️ Transferido Parcial</span>
              </label>
              <label style="margin: 0; display: flex; align-items: center; gap: 5px;">
                <input type="radio" name="status_${index}" value="nao_transferido" style="margin: 0;" ${dadosExistentes && dadosExistentes.statusMateriais && dadosExistentes.statusMateriais[index] === 'nao_transferido' ? 'checked' : ''}>
                <span style="color: #dc3545;">❌ Não Transferido</span>
              </label>
            </div>
          </div>
        `;
      });

      const isAlteracao = dadosExistentes !== null;
      const titulo = isAlteracao ? '🔄 Alterar Status de Transferência' : '📦 Registro de Transferência de Material';
      const subtitulo = isAlteracao ?
        `Status atual registrado em: ${dadosExistentes.dataRegistro ? new Date(dadosExistentes.dataRegistro).toLocaleString() : 'Data não informada'}` :
        'Marque o status de cada material conforme transferido pelo almoxarife';

      modal.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
          <h3 style="color: #2c3e50; margin: 0 0 10px 0;">${titulo}</h3>
          <p style="color: #6c757d; margin: 0;">OP: <strong>${nomeOP}</strong></p>
          ${isAlteracao ? `<p style="color: #856404; margin: 5px 0 0 0; font-size: 12px;">${subtitulo}</p>` : ''}
        </div>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <p style="margin: 0; color: #1976d2; font-size: 14px;">
            <strong>📋 ${isAlteracao ? 'Alteração de Status:' : 'Instruções:'}</strong><br>
            ${isAlteracao ?
              '• Ajuste o status conforme a situação atual dos materiais<br>• As alterações serão registradas com nova data/hora' :
              '• Marque o status de cada material conforme transferido pelo almoxarife<br>• Transferência parcial permite continuar o fluxo com restrições<br>• Material não transferido bloqueia o próximo passo'
            }
          </p>
        </div>

        <div id="materiais-lista">
          ${materiaisHtml}
        </div>

        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 25px;">
          <button onclick="confirmarTransferenciaMaterial('${opId}', '${nomeOP}', ${materiais.length})"
                  style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-weight: bold;">
            ✅ Confirmar Registro
          </button>
          <button onclick="this.closest('[style*=\"position: fixed\"]').remove()"
                  style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
            ❌ Cancelar
          </button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Fechar modal clicando fora
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          overlay.remove();
        }
      });
    };

    // Função para confirmar transferência de material
    window.confirmarTransferenciaMaterial = async function(opId, nomeOP, totalMateriais) {
      try {
        // Coletar status de todos os materiais
        const statusMateriais = [];
        let temCompleto = false;
        let temParcial = false;
        let temNaoTransferido = false;

        for (let i = 0; i < totalMateriais; i++) {
          const radios = document.querySelectorAll(`input[name="status_${i}"]`);
          let statusSelecionado = null;

          radios.forEach(radio => {
            if (radio.checked) {
              statusSelecionado = radio.value;
            }
          });

          if (!statusSelecionado) {
            alert(`⚠️ Por favor, selecione o status para todos os materiais.\n\nMaterial ${i + 1} não foi marcado.`);
            return;
          }

          statusMateriais.push(statusSelecionado);

          if (statusSelecionado === 'completo') temCompleto = true;
          if (statusSelecionado === 'parcial') temParcial = true;
          if (statusSelecionado === 'nao_transferido') temNaoTransferido = true;
        }

        // Determinar status geral da transferência
        let statusGeral = 'nao_transferido';
        let mensagemStatus = '';

        if (temCompleto && !temParcial && !temNaoTransferido) {
          statusGeral = 'completo';
          mensagemStatus = '✅ Todos os materiais foram transferidos completamente';
        } else if ((temCompleto || temParcial) && !temNaoTransferido) {
          statusGeral = 'parcial';
          mensagemStatus = '⚠️ Materiais transferidos parcialmente - Fluxo pode continuar com restrições';
        } else if (temNaoTransferido) {
          statusGeral = 'bloqueado';
          mensagemStatus = '❌ Alguns materiais não foram transferidos - Próximo passo será bloqueado';
        }

        // Confirmar com o usuário
        if (!confirm(`📋 CONFIRMAR REGISTRO DE TRANSFERÊNCIA\n\nOP: ${nomeOP}\n\n${mensagemStatus}\n\n🔄 Confirma o registro?`)) {
          return;
        }

        // Salvar no banco de dados
        const dadosTransferencia = {
          statusGeral: statusGeral,
          statusMateriais: statusMateriais,
          dataRegistro: new Date().toISOString(),
          usuario: localStorage.getItem('currentUser') || 'Sistema'
        };

        const sucesso = await updateOPFlowStatus(opId, 'materialTransferido', statusGeral !== 'bloqueado', dadosTransferencia);

        if (sucesso) {
          // Fechar modal
          document.querySelector('[style*="position: fixed"]').remove();

          // Mostrar resultado
          alert(`✅ TRANSFERÊNCIA REGISTRADA!\n\nOP: ${nomeOP}\nStatus: ${mensagemStatus}\n\n🎯 ${statusGeral !== 'bloqueado' ? 'PRÓXIMO PASSO: Verificar saldo' : 'Aguarde transferência dos materiais pendentes'}`);

          mostrarNotificacao(`✅ Transferência registrada - ${statusGeral}`, 'success', 3000);
          await preservarERecarregar();
        }

      } catch (error) {
        console.error('Erro ao confirmar transferência:', error);
        alert('❌ Erro ao confirmar transferência: ' + error.message);
      }
    };

    // Função para marcar material como já transferido (botão rápido)
    window.marcarMaterialTransferido = async function(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        if (confirm(`✅ CONFIRMAR TRANSFERÊNCIA COMPLETA\n\nOP: ${nomeOP}\n\nConfirma que TODOS os materiais necessários foram transferidos completamente para o armazém de produção?\n\n⚠️ Use esta opção apenas se tem certeza que tudo foi transferido.`)) {

          const dadosTransferencia = {
            statusGeral: 'completo',
            statusMateriais: ['completo'], // Marca como completo genérico
            dataRegistro: new Date().toISOString(),
            usuario: localStorage.getItem('currentUser') || 'Sistema',
            metodo: 'marcacao_rapida'
          };

          const sucesso = await updateOPFlowStatus(opId, 'materialTransferido', true, dadosTransferencia);

          if (sucesso) {
            alert(`✅ MATERIAL TRANSFERIDO!\n\nOP: ${nomeOP}\nStatus: Todos os materiais na produção\n\n🎯 PRÓXIMO PASSO: Verificar saldo no armazém de produção`);
            mostrarNotificacao('✅ Material transferido completamente', 'success', 3000);
            await preservarERecarregar();
          }
        }
      } catch (error) {
        console.error('Erro ao marcar transferência:', error);
        alert('❌ Erro ao marcar transferência: ' + error.message);
      }
    };

    // ETAPA 2: Verificar Saldo
    window.verificarSaldoOP = async function(opId) {
      try {
        console.log(`🔍 INÍCIO - verificarSaldoOP chamada para OP: ${opId}`);

        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        console.log(`📋 Ordem encontrada:`, ordem);

        // Verificar se material foi transferido primeiro
        if (!ordem.materialTransferido) {
          console.log(`🚫 Material não transferido, bloqueando OP: ${opId}`);
          alert('🚫 BLOQUEADO!\n\nPrimeiro você deve transferir o material para produção.\n\nClique em "1. Transferir Material" primeiro.');
          return false;
        }

        console.log(`🔍 Verificando saldo no armazém de produção para OP: ${opId}`);

        // 🔧 ETAPA 2: Verificar apenas MATÉRIAS-PRIMAS (ignorar SP)
        console.log(`🔄 Chamando verificarMateriaisEtapa2 (apenas MP)...`);
        const resultadoValidacao = await verificarMateriaisEtapa2(opId, nomeOP);

        if (resultadoValidacao.sucesso) {
          // Saldo OK - marcar como validado
          const sucesso = await updateOPFlowStatus(opId, 'saldoValidado', true);

          if (sucesso) {
            let mensagem = `✅ SALDO VALIDADO COM SUCESSO!\n\n📊 OP: ${nomeOP}\n✅ Todas as matérias-primas disponíveis`;

            if (resultadoValidacao.alertas && resultadoValidacao.alertas.length > 0) {
              mensagem += `\n\n⚠️ ALERTAS:\n${resultadoValidacao.alertas.join('\n')}`;
            }

            mensagem += `\n\n💡 NOTA: Subprodutos (SP) serão verificados apenas no apontamento final`;
            mensagem += `\n\n🎯 PRÓXIMO PASSO: Imprimir a OP`;

            alert(mensagem);
            mostrarNotificacao('✅ Saldo validado - Pode imprimir OP', 'success', 3000);

            // CORRIGIDO: Salvar filtros ANTES de recarregar
            salvarEstadoBusca();
            await loadOrders();
            // Restaurar filtros APÓS recarregar
            setTimeout(() => {
              restaurarEstadoBusca();
            }, 100);
            return true;
          }
        } else {
          // Mostrar modal com materiais faltantes (apenas MP)
          return false;
        }



      } catch (error) {
        console.error('Erro ao verificar saldo:', error);
        alert('❌ Erro ao verificar saldo: ' + error.message);
        return false;
      }
    };

    // ETAPA 2: Imprimir OP (substituir função existente)
    window.printOrderReport = async function(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        if (!ordem) {
          alert('❌ Ordem não encontrada!');
          return false;
        }

        // Verificar se saldo foi validado
        if (!ordem.saldoValidado) {
          alert('🚫 BLOQUEADO!\n\nPrimeiro você deve verificar o saldo da OP.\n\nClique em "2. Verificar Saldo" primeiro.');
          return false;
        }

        console.log(`✅ Saldo já validado - prosseguindo com impressão da OP: ${opId}`);
        const nomeOP = ordem.numero || opId;

        // 3. PROCESSAR IMPRESSÃO
        const produto = produtos.find(p => p.id === ordem.produtoId) || {};
        const estrutura = estruturas.find(e => e.produtoPaiId === ordem.produtoId);
        const printArea = document.getElementById('printArea');

        // Validar se printArea existe
        if (!printArea) {
          console.error('❌ Elemento printArea não encontrado');
          alert('❌ Erro: Área de impressão não encontrada. Recarregue a página e tente novamente.');
          return false;
        }

        // Processar impressão
        await processarImpressaoOP(ordem, produto, estrutura, printArea);

        // Se chegou até aqui, impressão foi bem-sucedida
        await updateOPFlowStatus(opId, 'impressa', true);
        alert(`✅ OP IMPRESSA COM SUCESSO!\n\n📊 OP: ${nomeOP}\n🖨️ Documento enviado para impressão\n\n🎯 PRÓXIMO PASSO: Enviar para fábrica`);
        mostrarNotificacao('✅ OP impressa - Pode enviar para fábrica', 'success', 3000);

        // Preservar busca atual e recarregar
        await preservarERecarregar();
        return true;

      } catch (error) {
        console.error('❌ Erro ao validar estoque para impressão:', error);

        // Verificar se é erro específico de document
        if (error.message && error.message.includes('document')) {
          mostrarNotificacao('❌ Erro de pop-up bloqueado - Permita pop-ups e tente novamente', 'error', 8000);
          alert('❌ Pop-up bloqueado!\n\nPor favor:\n1. Permita pop-ups para este site\n2. Tente imprimir novamente\n\nOu use Ctrl+P para imprimir a tela atual.');
        } else {
          mostrarNotificacao('❌ Erro ao validar estoque para impressão', 'error', 5000);
          alert('❌ Erro ao validar estoque para impressão: ' + error.message);
        }
        return false;
      }
    };

    // Função duplicada removida - usar apenas a versão correta da ETAPA 1

    // ===== HOOK AUTOMÁTICO PARA SINCRONIZAÇÃO DE FLAGS =====

    /**
     * Atualiza automaticamente o flag materialTransferido da OP após transferência
     * Esta função elimina a necessidade do botão "Processar Histórico"
     */
    window.atualizarFlagOPAposTransferencia = async function(opId, produtoId = null, quantidade = null) {
        try {
            console.log(`🔄 Atualizando flag da OP ${opId} automaticamente...`);

            // Buscar a OP
            const opDoc = await getDoc(doc(db, "ordensProducao", opId));
            if (!opDoc.exists()) {
                console.warn(`⚠️ OP ${opId} não encontrada`);
                return false;
            }

            const opData = opDoc.data();

            // Buscar todas as transferências desta OP
            const transferenciasQuery = query(
                collection(db, "transferenciasArmazem"),
                where("ordemProducaoId", "==", opId),
                where("status", "==", "CONCLUIDA")
            );

            const transferenciasSnap = await getDocs(transferenciasQuery);
            const transferencias = transferenciasSnap.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            console.log(`📦 Encontradas ${transferencias.length} transferências para OP ${opId}`);

            // Se não há materiais necessários, considerar como não transferido
            if (!opData.materiaisNecessarios || opData.materiaisNecessarios.length === 0) {
                console.log(`⚠️ OP ${opId} não possui materiais necessários definidos`);
                return false;
            }

            // Analisar status dos materiais usando a função existente
            const statusMateriais = analisarStatusTransferenciaMateriais(
                opData.materiaisNecessarios,
                transferencias
            );

            const statusGeral = determinarStatusGeralTransferencia(statusMateriais);

            // Verificar se precisa atualizar
            const flagAtual = opData.materialTransferido || false;
            const novoFlag = statusGeral !== 'nao_transferido';

            if (flagAtual === novoFlag) {
                console.log(`✅ Flag da OP ${opId} já está correto: ${novoFlag}`);
                return true;
            }

            // Preparar dados da transferência
            const dadosTransferencia = {
                statusGeral: statusGeral,
                statusMateriais: statusMateriais,
                ultimaAtualizacao: new Date().toISOString(),
                atualizacaoAutomatica: true,
                totalTransferencias: transferencias.length,
                produtoAtualizado: produtoId,
                quantidadeAtualizada: quantidade,
                usuario: localStorage.getItem('currentUser') || 'Sistema'
            };

            // Atualizar OP automaticamente
            await updateDoc(doc(db, "ordensProducao", opId), {
                materialTransferido: novoFlag,
                dadosTransferencia: dadosTransferencia,
                ultimaAtualizacaoTransferencia: Timestamp.now()
            });

            console.log(`✅ Flag da OP ${opId} atualizado automaticamente: ${flagAtual} → ${novoFlag} (${statusGeral})`);

            // Mostrar notificação discreta
            mostrarNotificacao(
                `🔄 OP ${opData.numero || opId}: Flag atualizado automaticamente (${statusGeral})`,
                'info',
                2000
            );

            return true;

        } catch (error) {
            console.error(`❌ Erro ao atualizar flag da OP ${opId}:`, error);
            // Não mostrar alert para não interromper o fluxo do usuário
            mostrarNotificacao('⚠️ Erro na sincronização automática', 'warning', 3000);
            return false;
        }
    };

    // ETAPA 4: Enviar para Fábrica
    window.enviarParaFabrica = async function(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        // Verificar se OP foi impressa
        if (!ordem.impressa) {
          alert('🚫 BLOQUEADO!\n\nPrimeiro você deve imprimir a OP.\n\nClique em "3. Imprimir OP" primeiro.');
          return;
        }

        // Confirmar envio
        if (!confirm(`🏭 ENVIAR PARA FÁBRICA\n\nOP: ${nomeOP}\n\n✅ Saldo validado\n✅ OP impressa\n\n🚀 Confirma o envio para a fábrica?`)) {
          return;
        }

        // Marcar como enviada para fábrica e alterar status
        const sucesso1 = await updateOPFlowStatus(opId, 'enviadaFabrica', true);
        const sucesso2 = await updateOPFlowStatus(opId, 'status', 'Em Produção');

        if (sucesso1 && sucesso2) {
          alert(`✅ OP ENVIADA PARA FÁBRICA!\n\nOP: ${nomeOP}\nStatus: Em Produção\n\n🎯 PRÓXIMO PASSO: Fazer apontamentos`);
          mostrarNotificacao('✅ OP enviada para fábrica - Pode apontar', 'success', 3000);
          await preservarERecarregar(); // Preservar busca e recarregar para atualizar botões
        }

      } catch (error) {
        console.error('Erro ao enviar para fábrica:', error);
        alert('❌ Erro ao enviar para fábrica: ' + error.message);
      }
    };

    // ETAPA 4: Apontar (função já existe, vamos apenas adicionar validação)
    const originalOpenAppointmentModal = window.openAppointmentModal;
    window.openAppointmentModal = function(opId) {
      const ordem = ordensProducao.find(op => op.id === opId);

      // Verificar se OP foi enviada para fábrica
      if (!ordem.enviadaFabrica) {
        alert('🚫 BLOQUEADO!\n\nPrimeiro você deve enviar a OP para a fábrica.\n\nClique em "4. Enviar Fábrica" primeiro.');
        return;
      }

      // Salvar estado da busca antes de abrir modal
      salvarEstadoBusca();

      // Chamar função original de apontamento
      originalOpenAppointmentModal(opId);
    };

    // ===================================================================
    // VALIDAÇÃO UNIFICADA DE ESTOQUE PARA PRODUÇÃO
    // ===================================================================

    // Função unificada para calcular disponibilidade de material
    function calcularDisponibilidadeMaterial(material, estoque, ordemProducaoId) {
      if (!estoque) {
        return {
          saldoTotal: 0,
          saldoLivre: 0,
          saldoReservado: 0,
          saldoEmpenhado: 0,
          quantidadeReservadaParaEstaOP: 0,
          saldoDisponivel: 0,
          temEstoque: false
        };
      }

      const saldoTotal = estoque.saldo || 0;
      const saldoReservado = estoque.saldoReservado || 0;
      const saldoEmpenhado = estoque.saldoEmpenhado || 0;
      const saldoLivre = saldoTotal - saldoReservado - saldoEmpenhado;

      // VERIFICAR SE JÁ ESTÁ RESERVADO PARA ESTA OP
      let quantidadeReservadaParaEstaOP = 0;
      if (material.quantidadeReservada) {
        quantidadeReservadaParaEstaOP = material.quantidadeReservada;
      }

      // DISPONIBILIDADE REAL = SALDO LIVRE + RESERVADO PARA ESTA OP
      const saldoDisponivel = saldoLivre + quantidadeReservadaParaEstaOP;

      return {
        saldoTotal,
        saldoLivre,
        saldoReservado,
        saldoEmpenhado,
        quantidadeReservadaParaEstaOP,
        saldoDisponivel,
        temEstoque: true
      };
    }

    // ===================================================================
    // VALIDAÇÃO DE ESTOQUE PARA PRODUÇÃO
    // ===================================================================

    // Função para validar se há estoque suficiente para produzir
    async function validarEstoqueParaProducao(opId) {
      try {
        const ordem = ordensProducao.find(op => op.id === opId);
        if (!ordem) {
          throw new Error('Ordem de produção não encontrada');
        }

        const materiaisInsuficientes = [];
        const alertas = [];

        // Verificar cada material necessário
        if (ordem.materiaisNecessarios && ordem.materiaisNecessarios.length > 0) {
          for (const material of ordem.materiaisNecessarios) {
            // Buscar estoque do material
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || ordem.armazemProducaoId)
            );

            if (!estoque) {
              materiaisInsuficientes.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: 0,
                falta: material.quantidade,
                motivo: 'Estoque não encontrado'
              });
              continue;
            }

            // Usar função unificada para calcular disponibilidade
            const disponibilidade = calcularDisponibilidadeMaterial(material, estoque, opId);
            const disponivelParaEstaOP = disponibilidade.saldoDisponivel;

            // Verificar se há quantidade suficiente
            if (disponivelParaEstaOP < material.quantidade) {
              const falta = material.quantidade - disponivelParaEstaOP;
              materiaisInsuficientes.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: disponivelParaEstaOP,
                saldoLivre: disponibilidade.saldoLivre,
                quantidadeReservadaParaEstaOP: disponibilidade.quantidadeReservadaParaEstaOP,
                falta: falta,
                motivo: disponivelParaEstaOP <= 0 ? 'Sem estoque' : 'Estoque insuficiente',
                usandoReserva: disponibilidade.quantidadeReservadaParaEstaOP > 0
              });
            }

            // Alertas para estoque baixo (menos de 20% do necessário extra)
            const margemSeguranca = material.quantidade * 0.2;
            if (disponivelParaEstaOP >= material.quantidade && disponivelParaEstaOP < (material.quantidade + margemSeguranca)) {
              alertas.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                disponivel: disponivelParaEstaOP,
                necessario: material.quantidade,
                motivo: 'Estoque baixo - pouca margem de segurança'
              });
            }
          }
        }

        return {
          podeProuzir: materiaisInsuficientes.length === 0,
          materiaisInsuficientes,
          alertas,
          totalMateriais: ordem.materiaisNecessarios?.length || 0
        };

      } catch (error) {
        console.error('Erro ao validar estoque:', error);
        return {
          podeProuzir: false,
          materiaisInsuficientes: [],
          alertas: [],
          erro: error.message
        };
      }
    }

    // Função para mostrar detalhes da validação
    function mostrarDetalhesValidacao(validacao, nomeOP) {
      let mensagem = `📋 VALIDAÇÃO DE ESTOQUE - OP: ${nomeOP}\n\n`;

      if (validacao.erro) {
        mensagem += `❌ ERRO: ${validacao.erro}\n`;
        return mensagem;
      }

      if (validacao.podeProuzir) {
        mensagem += `✅ PODE PRODUZIR!\n`;
        mensagem += `📊 ${validacao.totalMateriais} material(is) verificado(s)\n\n`;

        if (validacao.alertas.length > 0) {
          mensagem += `⚠️ ALERTAS (${validacao.alertas.length}):\n`;
          validacao.alertas.forEach(alerta => {
            mensagem += `🔸 ${alerta.codigo}: ${alerta.disponivel.toFixed(3)} disponível (necessário: ${alerta.necessario.toFixed(3)})\n`;
            mensagem += `   ${alerta.motivo}\n\n`;
          });
        }
      } else {
        mensagem += `❌ NÃO PODE PRODUZIR!\n`;
        mensagem += `🚫 ${validacao.materiaisInsuficientes.length} material(is) insuficiente(s):\n\n`;

        validacao.materiaisInsuficientes.forEach(material => {
          mensagem += `🔸 ${material.codigo}:\n`;
          mensagem += `   Necessário: ${material.necessario.toFixed(3)}\n`;
          mensagem += `   Disponível: ${material.disponivel.toFixed(3)}\n`;
          mensagem += `   Falta: ${material.falta.toFixed(3)}\n`;
          mensagem += `   Motivo: ${material.motivo}\n\n`;
        });

        mensagem += `💡 AÇÕES SUGERIDAS:\n`;
        mensagem += `• Verificar compras pendentes\n`;
        mensagem += `• Solicitar transferência entre armazéns\n`;
        mensagem += `• Ajustar quantidade da OP\n`;
        mensagem += `• Aguardar recebimento de materiais\n`;
      }

      return mensagem;
    }

    // ===================================================================
    // DECODIFICADOR DE PRODUTOS & CORREÇÃO DE ESTOQUE
    // ===================================================================

    // Função principal para abrir decodificador
    window.abrirDecodificadorProdutos = function() {
      document.getElementById('modalDecodificadorProdutos').style.display = 'block';
      document.getElementById('codigoCriptico').focus();
    };

    // Buscar produto por código críptico
    window.buscarProdutoPorCodigo = function() {
      const codigoCriptico = document.getElementById('codigoCriptico').value.trim();

      if (!codigoCriptico) {
        alert('Por favor, digite um código para buscar.');
        return;
      }

      console.log(`🔍 Buscando produto com código: ${codigoCriptico}`);

      // Buscar produto no array de produtos
      const produto = produtos.find(p =>
        p.id === codigoCriptico ||
        p.codigo === codigoCriptico ||
        (p.codigoInterno && p.codigoInterno === codigoCriptico)
      );

      // Buscar estoque do produto
      const estoque = estoques.find(e => e.produtoId === codigoCriptico);

      // Buscar em estruturas (pode ser componente)
      const estruturasQueUsam = estruturas.filter(est =>
        est.itens && est.itens.some(item => item.produtoId === codigoCriptico)
      );

      // Buscar em OPs que usam este material
      const opsQueUsam = ordensProducao.filter(op =>
        op.materiaisNecessarios && op.materiaisNecessarios.some(mat => mat.produtoId === codigoCriptico)
      );

      mostrarResultadoBusca(codigoCriptico, produto, estoque, estruturasQueUsam, opsQueUsam);
    };

    // Mostrar resultado da busca
    function mostrarResultadoBusca(codigoCriptico, produto, estoque, estruturasQueUsam, opsQueUsam) {
      const resultadoDiv = document.getElementById('resultadoBusca');

      let html = `
        <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px;">
          <h5 style="color: #495057; margin: 0 0 15px 0;">🔍 Resultado da Busca: <code>${codigoCriptico}</code></h5>
      `;

      // Informações do produto
      if (produto) {
        html += `
          <div style="background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #155724; margin: 0 0 10px 0;">✅ Produto Encontrado</h6>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="padding: 5px; font-weight: bold;">ID:</td><td style="padding: 5px;">${produto.id}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Código:</td><td style="padding: 5px;">${produto.codigo || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Descrição:</td><td style="padding: 5px;">${produto.descricao || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Unidade:</td><td style="padding: 5px;">${produto.unidade || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Tipo:</td><td style="padding: 5px;">${produto.tipo || 'N/A'}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Família:</td><td style="padding: 5px;">${produto.familia || 'N/A'}</td></tr>
            </table>
          </div>
        `;
      } else {
        html += `
          <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #721c24; margin: 0;">❌ Produto não encontrado no cadastro</h6>
            <p style="color: #721c24; margin: 5px 0 0 0; font-size: 14px;">
              O código pode ser um ID interno do Firebase ou um código não cadastrado.
            </p>
          </div>
        `;
      }

      // Informações do estoque
      if (estoque) {
        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        const corSaldo = saldoTotal < 0 ? '#dc3545' : saldoTotal === 0 ? '#ffc107' : '#28a745';

        html += `
          <div style="background: #e2e3e5; border-left: 4px solid ${corSaldo}; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #383d41; margin: 0 0 10px 0;">📦 Informações de Estoque</h6>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="padding: 5px; font-weight: bold;">Armazém:</td><td style="padding: 5px;">${armazem?.nome || estoque.armazemId}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Total:</td><td style="padding: 5px; color: ${corSaldo}; font-weight: bold;">${saldoTotal.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Reservado:</td><td style="padding: 5px;">${saldoReservado.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Empenhado:</td><td style="padding: 5px;">${saldoEmpenhado.toFixed(3)}</td></tr>
              <tr><td style="padding: 5px; font-weight: bold;">Saldo Disponível:</td><td style="padding: 5px; color: ${saldoDisponivel < 0 ? '#dc3545' : '#28a745'}; font-weight: bold;">${saldoDisponivel.toFixed(3)}</td></tr>
            </table>

            ${saldoTotal < 0 ? `
              <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 10px;">
                <strong style="color: #721c24;">⚠️ ESTOQUE NEGATIVO DETECTADO!</strong>
                <br><button onclick="corrigirEstoque('${estoque.id}', '${codigoCriptico}')"
                           style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 5px; cursor: pointer;">
                  🔧 Corrigir Estoque
                </button>
              </div>
            ` : ''}
          </div>
        `;
      } else {
        html += `
          <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #856404; margin: 0;">⚠️ Estoque não encontrado</h6>
            <p style="color: #856404; margin: 5px 0 0 0; font-size: 14px;">
              Este produto não possui registro de estoque.
            </p>
          </div>
        `;
      }

      // OPs que usam este material
      if (opsQueUsam.length > 0) {
        html += `
          <div style="background: #cce5ff; border-left: 4px solid #007bff; padding: 15px; margin-bottom: 15px;">
            <h6 style="color: #004085; margin: 0 0 10px 0;">🏭 OPs que usam este material (${opsQueUsam.length})</h6>
            <ul style="margin: 0; padding-left: 20px;">
        `;

        opsQueUsam.slice(0, 5).forEach(op => {
          const material = op.materiaisNecessarios.find(m => m.produtoId === codigoCriptico);
          html += `<li>OP ${op.numero} - Necessário: ${material.quantidade?.toFixed(3) || 'N/A'} - Status: ${op.status}</li>`;
        });

        if (opsQueUsam.length > 5) {
          html += `<li><em>... e mais ${opsQueUsam.length - 5} OP(s)</em></li>`;
        }

        html += `</ul></div>`;
      }

      html += `</div>`;

      resultadoDiv.innerHTML = html;
      resultadoDiv.style.display = 'block';
    }

    // Analisar produtos com problemas de estoque
    window.analisarProdutosComProblemas = function() {
      console.log('🔍 Analisando produtos com problemas de estoque...');

      const produtosComProblemas = [];

      estoques.forEach(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);

        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

        // Identificar problemas
        const problemas = [];
        if (saldoTotal < 0) problemas.push('ESTOQUE_NEGATIVO');
        if (saldoReservado > saldoTotal) problemas.push('RESERVA_MAIOR_QUE_TOTAL');
        if (saldoEmpenhado > saldoTotal) problemas.push('EMPENHO_MAIOR_QUE_TOTAL');
        if (saldoDisponivel < 0) problemas.push('DISPONIVEL_NEGATIVO');
        if (!produto) problemas.push('PRODUTO_NAO_ENCONTRADO');

        if (problemas.length > 0) {
          produtosComProblemas.push({
            estoque,
            produto,
            armazem,
            saldoTotal,
            saldoReservado,
            saldoEmpenhado,
            saldoDisponivel,
            problemas
          });
        }
      });

      mostrarProdutosComProblemas(produtosComProblemas);
    };

    // Mostrar produtos com problemas
    function mostrarProdutosComProblemas(produtosComProblemas) {
      const container = document.getElementById('produtosComProblemas');

      if (produtosComProblemas.length === 0) {
        container.innerHTML = `
          <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center;">
            <strong style="color: #155724;">✅ Nenhum problema de estoque detectado!</strong>
            <p style="color: #155724; margin: 5px 0 0 0;">Todos os estoques estão consistentes.</p>
          </div>
        `;
        return;
      }

      let html = `
        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
          <strong style="color: #721c24;">⚠️ ${produtosComProblemas.length} produto(s) com problemas detectado(s)</strong>
        </div>

        <div style="max-height: 400px; overflow-y: auto;">
          <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
              <tr style="background: #e9ecef; position: sticky; top: 0;">
                <th style="padding: 8px; text-align: left; border: 1px solid #dee2e6;">Código</th>
                <th style="padding: 8px; text-align: left; border: 1px solid #dee2e6;">Descrição</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Armazém</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Saldo</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Problemas</th>
                <th style="padding: 8px; text-align: center; border: 1px solid #dee2e6;">Ação</th>
              </tr>
            </thead>
            <tbody>
      `;

      produtosComProblemas.forEach((item, index) => {
        const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
        const codigoProduto = item.produto?.codigo || item.estoque.produtoId;
        const descricaoProduto = item.produto?.descricao || 'Produto não encontrado';
        const nomeArmazem = item.armazem?.nome || item.estoque.armazemId;

        const problemasTexto = item.problemas.map(p => {
          switch(p) {
            case 'ESTOQUE_NEGATIVO': return '🔴 Negativo';
            case 'RESERVA_MAIOR_QUE_TOTAL': return '🟡 Reserva > Total';
            case 'EMPENHO_MAIOR_QUE_TOTAL': return '🟠 Empenho > Total';
            case 'DISPONIVEL_NEGATIVO': return '🔴 Disponível < 0';
            case 'PRODUTO_NAO_ENCONTRADO': return '❓ Produto não encontrado';
            default: return p;
          }
        }).join('<br>');

        html += `
          <tr style="background: ${rowColor};">
            <td style="padding: 8px; border: 1px solid #dee2e6; font-family: monospace;">${codigoProduto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">${descricaoProduto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">${nomeArmazem}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
              <div>Total: <strong style="color: ${item.saldoTotal < 0 ? '#dc3545' : '#28a745'};">${item.saldoTotal.toFixed(3)}</strong></div>
              <div>Disp: <strong style="color: ${item.saldoDisponivel < 0 ? '#dc3545' : '#28a745'};">${item.saldoDisponivel.toFixed(3)}</strong></div>
            </td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center; font-size: 10px;">${problemasTexto}</td>
            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
              <button onclick="corrigirEstoque('${item.estoque.id}', '${item.estoque.produtoId}')"
                      style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer;">
                🔧 Corrigir
              </button>
            </td>
          </tr>
        `;
      });

      html += `
            </tbody>
          </table>
        </div>
      `;

      container.innerHTML = html;
    }

    // Corrigir estoque individual
    window.corrigirEstoque = function(estoqueId, produtoId) {
      const estoque = estoques.find(e => e.id === estoqueId);
      const produto = produtos.find(p => p.id === produtoId);

      if (!estoque) {
        alert('Estoque não encontrado!');
        return;
      }

      const codigoProduto = produto?.codigo || produtoId;
      const descricaoProduto = produto?.descricao || 'Produto não identificado';

      const novoSaldo = prompt(
        `🔧 CORREÇÃO DE ESTOQUE\n\n` +
        `Produto: ${codigoProduto}\n` +
        `Descrição: ${descricaoProduto}\n` +
        `Saldo Atual: ${(estoque.saldo || 0).toFixed(3)}\n\n` +
        `Digite o novo saldo correto:`,
        '0.000'
      );

      if (novoSaldo === null) return; // Cancelou

      const saldoNumerico = parseFloat(novoSaldo);
      if (isNaN(saldoNumerico)) {
        alert('Valor inválido! Digite um número.');
        return;
      }

      if (!confirm(`Confirma a correção?\n\nDe: ${(estoque.saldo || 0).toFixed(3)}\nPara: ${saldoNumerico.toFixed(3)}`)) {
        return;
      }

      // Aqui seria a integração com Firebase para atualizar o estoque
      console.log(`🔧 Corrigindo estoque ${estoqueId}: ${estoque.saldo} → ${saldoNumerico}`);

      // Simular atualização local (em produção seria updateDoc do Firebase)
      estoque.saldo = saldoNumerico;

      mostrarNotificacao(`✅ Estoque corrigido: ${codigoProduto}`, 'success', 3000);

      // Recarregar análise
      analisarProdutosComProblemas();
    };

    // Gerar código mais legível a partir do código críptico
    function gerarCodigoLegivel(codigoCriptico, produto) {
      if (!produto) {
        return codigoCriptico.length > 12 ? codigoCriptico.substring(0, 12) + '...' : codigoCriptico;
      }

      // Se tem código do produto, usar ele
      if (produto.codigo && produto.codigo !== codigoCriptico) {
        return produto.codigo;
      }

      // Se tem descrição, tentar extrair código dela
      if (produto.descricao) {
        const desc = produto.descricao.toUpperCase();

        // Padrões comuns de códigos em descrições
        const padroes = [
          /([A-Z]{2,}-[A-Z0-9-]+)/,  // Ex: MP-ACUCAR, FERRO-CHATO
          /([A-Z]+\s*\d+[X\*]\d+)/,  // Ex: FERRO 2X5, CHAPA 4*8
          /([A-Z]+[-_][A-Z0-9]+)/,   // Ex: FERRO_CHATO, MP_001
          /^([A-Z\s]+)\s*\(/,        // Ex: FERRO CHATO (antes do parênteses)
        ];

        for (const padrao of padroes) {
          const match = desc.match(padrao);
          if (match) {
            return match[1].trim().replace(/\s+/g, '-');
          }
        }

        // Se não achou padrão, usar primeiras palavras
        const palavras = desc.split(/\s+/).slice(0, 2);
        if (palavras.length > 0) {
          return palavras.join('-');
        }
      }

      // Fallback: código críptico abreviado
      return codigoCriptico.length > 12 ? codigoCriptico.substring(0, 12) + '...' : codigoCriptico;
    }

    // Mostrar detalhes completos do código críptico
    window.mostrarDetalhesCodigoCriptico = function(codigoCriptico, produtoId) {
      const produto = produtos.find(p => p.id === produtoId || p.id === codigoCriptico);
      const estoque = estoques.find(e => e.produtoId === produtoId || e.produtoId === codigoCriptico);

      let detalhes = `🔍 DETALHES DO CÓDIGO CRÍPTICO\n\n`;
      detalhes += `Código Críptico: ${codigoCriptico}\n`;
      detalhes += `ID do Produto: ${produtoId}\n\n`;

      if (produto) {
        detalhes += `✅ PRODUTO ENCONTRADO:\n`;
        detalhes += `• Código: ${produto.codigo || 'N/A'}\n`;
        detalhes += `• Descrição: ${produto.descricao || 'N/A'}\n`;
        detalhes += `• Unidade: ${produto.unidade || 'N/A'}\n`;
        detalhes += `• Tipo: ${produto.tipo || 'N/A'}\n`;
        detalhes += `• Família: ${produto.familia || 'N/A'}\n\n`;
      } else {
        detalhes += `❌ PRODUTO NÃO ENCONTRADO\n\n`;
      }

      if (estoque) {
        detalhes += `📦 ESTOQUE:\n`;
        detalhes += `• Saldo Total: ${(estoque.saldo || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Reservado: ${(estoque.saldoReservado || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Empenhado: ${(estoque.saldoEmpenhado || 0).toFixed(3)}\n`;
        detalhes += `• Saldo Disponível: ${((estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0)).toFixed(3)}\n`;

        const armazem = armazens.find(a => a.id === estoque.armazemId);
        detalhes += `• Armazém: ${armazem?.nome || estoque.armazemId}\n\n`;
      } else {
        detalhes += `❌ ESTOQUE NÃO ENCONTRADO\n\n`;
      }

      detalhes += `💡 DICA: Use o botão "🔍 Decodificar" para abrir o decodificador completo.`;

      alert(detalhes);
    };

    // Abrir decodificador com código pré-preenchido
    window.abrirDecodificadorComCodigo = function(codigo) {
      abrirDecodificadorProdutos();
      setTimeout(() => {
        document.getElementById('codigoCriptico').value = codigo;
        buscarProdutoPorCodigo();
      }, 100);
    };

    // Corrigir estoque diretamente
    window.corrigirEstoqueDireto = function(produtoId) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      const produto = produtos.find(p => p.id === produtoId);

      if (!estoque) {
        alert('❌ Estoque não encontrado para este produto!');
        return;
      }

      const codigoLegivel = gerarCodigoLegivel(produtoId, produto);
      const descricaoProduto = produto?.descricao || 'Produto não identificado';

      const novoSaldo = prompt(
        `🔧 CORREÇÃO RÁPIDA DE ESTOQUE\n\n` +
        `Produto: ${codigoLegivel}\n` +
        `Descrição: ${descricaoProduto}\n` +
        `Saldo Atual: ${(estoque.saldo || 0).toFixed(3)}\n\n` +
        `Digite o novo saldo correto:`,
        '0.000'
      );

      if (novoSaldo === null) return; // Cancelou

      const saldoNumerico = parseFloat(novoSaldo);
      if (isNaN(saldoNumerico)) {
        alert('❌ Valor inválido! Digite um número.');
        return;
      }

      if (!confirm(`✅ Confirma a correção?\n\nDe: ${(estoque.saldo || 0).toFixed(3)}\nPara: ${saldoNumerico.toFixed(3)}`)) {
        return;
      }

      // Aqui seria a integração com Firebase para atualizar o estoque
      console.log(`🔧 Corrigindo estoque ${estoque.id}: ${estoque.saldo} → ${saldoNumerico}`);

      // Simular atualização local (em produção seria updateDoc do Firebase)
      estoque.saldo = saldoNumerico;

      mostrarNotificacao(`✅ Estoque corrigido: ${codigoLegivel}`, 'success', 3000);

      // Forçar atualização da interface
      debounceUpdate('correcaoEstoque');
    };

    // Funções do modal
    window.fecharModalDecodificador = function() {
      document.getElementById('modalDecodificadorProdutos').style.display = 'none';
    };

    window.exportarRelatorioDecodificacao = function() {
      // Implementar exportação de relatório
      alert('📊 Funcionalidade de exportação será implementada');
    };

    window.corrigirEstoquesLote = function() {
      // Implementar correção em lote
      alert('🔧 Funcionalidade de correção em lote será implementada');
    };

    // ===================================================================
    // DIAGNÓSTICO COMPLETO DE IMPOSSIBILIDADE
    // ===================================================================

    // Função principal para diagnóstico completo
    window.executarDiagnosticoCompleto = async function() {
      try {
        console.log('🔍 Iniciando diagnóstico completo de impossibilidade...');
        mostrarNotificacao('🔍 Executando diagnóstico profundo...', 'info', 3000);

        // 1. ANÁLISE BÁSICA
        const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');
        if (opsPendentes.length === 0) {
          alert('ℹ️ Nenhuma OP pendente para diagnosticar.');
          return;
        }

        // 2. ANÁLISE DETALHADA DE IMPOSSIBILIDADE
        const diagnostico = await analisarImpossibilidadeCompleta(opsPendentes);

        // 3. MOSTRAR RELATÓRIO DE DIAGNÓSTICO
        mostrarRelatorioImpossibilidade(diagnostico);

        console.log('✅ Diagnóstico completo concluído');
        mostrarNotificacao('✅ Diagnóstico concluído!', 'success', 2000);

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        mostrarNotificacao('❌ Erro no diagnóstico', 'error', 5000);
        alert('❌ Erro no diagnóstico: ' + error.message);
      }
    };

    // Análise completa de impossibilidade
    async function analisarImpossibilidadeCompleta(opsPendentes) {
      const diagnostico = {
        totalOPs: opsPendentes.length,
        opsAnalisadas: [],
        materiaisDisponiveis: {},
        materiaisNecessarios: {},
        gargalosIdentificados: [],
        oportunidadesProducao: [],
        sugestoesDesbloqueio: [],
        resumoImpossibilidade: {}
      };

      // 1. MAPEAR TODOS OS MATERIAIS DISPONÍVEIS
      console.log('📦 Mapeando materiais disponíveis...');
      diagnostico.materiaisDisponiveis = mapearMateriaisDisponiveis();

      // 2. MAPEAR TODOS OS MATERIAIS NECESSÁRIOS
      console.log('📋 Mapeando materiais necessários...');
      diagnostico.materiaisNecessarios = mapearMateriaisNecessarios(opsPendentes);

      // 3. ANÁLISE DETALHADA DE CADA OP
      console.log('🔍 Analisando cada OP em detalhes...');
      for (const op of opsPendentes) {
        const analiseDetalhada = await analisarOPDetalhadamente(op, diagnostico.materiaisDisponiveis);
        diagnostico.opsAnalisadas.push(analiseDetalhada);
      }

      // 4. IDENTIFICAR GARGALOS CRÍTICOS
      console.log('🚫 Identificando gargalos críticos...');
      diagnostico.gargalosIdentificados = identificarGargalosCriticos(diagnostico);

      // 5. BUSCAR OPORTUNIDADES DE PRODUÇÃO
      console.log('🎯 Buscando oportunidades de produção...');
      diagnostico.oportunidadesProducao = buscarOportunidadesProducao(diagnostico);

      // 6. GERAR SUGESTÕES DE DESBLOQUEIO
      console.log('💡 Gerando sugestões de desbloqueio...');
      diagnostico.sugestoesDesbloqueio = gerarSugestoesDesbloqueio(diagnostico);

      // 7. RESUMO FINAL
      diagnostico.resumoImpossibilidade = gerarResumoImpossibilidade(diagnostico);

      return diagnostico;
    }

    // Mapear todos os materiais disponíveis no estoque (POR ARMAZÉM)
    function mapearMateriaisDisponiveis() {
      const materiaisDisponiveis = {};

      estoques.forEach(estoque => {
        const produto = produtos.find(p => p.id === estoque.produtoId);
        const armazem = armazens.find(a => a.id === estoque.armazemId);
        const saldoTotal = estoque.saldo || 0;
        const saldoReservado = estoque.saldoReservado || 0;
        const saldoEmpenhado = estoque.saldoEmpenhado || 0;
        const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);

        // Chave única: produtoId + armazemId
        const chaveEstoque = `${estoque.produtoId}_${estoque.armazemId}`;

        materiaisDisponiveis[chaveEstoque] = {
          produto: produto,
          codigo: produto?.codigo || estoque.produtoId,
          descricao: produto?.descricao || 'Sem descrição',
          unidade: produto?.unidade || 'UN',
          saldoTotal,
          saldoReservado,
          saldoEmpenhado,
          saldoDisponivel,
          armazemId: estoque.armazemId,
          armazemNome: armazem?.nome || 'Armazém não identificado',
          armazemCodigo: armazem?.codigo || estoque.armazemId,
          status: saldoDisponivel > 0 ? 'DISPONIVEL' : 'ZERADO'
        };
      });

      // Também criar um mapa consolidado por produto (soma de todos os armazéns)
      const materiaisConsolidados = {};
      Object.values(materiaisDisponiveis).forEach(material => {
        const produtoId = material.produto?.id;
        if (!produtoId) return;

        if (!materiaisConsolidados[produtoId]) {
          materiaisConsolidados[produtoId] = {
            produto: material.produto,
            codigo: material.codigo,
            descricao: material.descricao,
            unidade: material.unidade,
            saldoTotalConsolidado: 0,
            saldoDisponivelConsolidado: 0,
            armazensComEstoque: [],
            armazensDetalhes: {}
          };
        }

        materiaisConsolidados[produtoId].saldoTotalConsolidado += material.saldoTotal;
        materiaisConsolidados[produtoId].saldoDisponivelConsolidado += material.saldoDisponivel;

        if (material.saldoDisponivel > 0) {
          materiaisConsolidados[produtoId].armazensComEstoque.push({
            armazemId: material.armazemId,
            armazemNome: material.armazemNome,
            saldoDisponivel: material.saldoDisponivel
          });
        }

        materiaisConsolidados[produtoId].armazensDetalhes[material.armazemId] = {
          armazemNome: material.armazemNome,
          saldoTotal: material.saldoTotal,
          saldoDisponivel: material.saldoDisponivel,
          saldoReservado: material.saldoReservado,
          saldoEmpenhado: material.saldoEmpenhado
        };
      });

      return {
        porArmazem: materiaisDisponiveis,
        consolidado: materiaisConsolidados
      };
    }

    // Mapear todos os materiais necessários para as OPs
    function mapearMateriaisNecessarios(opsPendentes) {
      const materiaisNecessarios = {};

      opsPendentes.forEach(op => {
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          op.materiaisNecessarios.forEach(material => {
            const materialId = material.produtoId;

            if (!materiaisNecessarios[materialId]) {
              const produto = produtos.find(p => p.id === materialId);
              materiaisNecessarios[materialId] = {
                produto: produto,
                codigo: produto?.codigo || materialId,
                descricao: produto?.descricao || 'Sem descrição',
                unidade: produto?.unidade || 'UN',
                quantidadeTotal: 0,
                opsQueUsam: [],
                prioridadeMaxima: 'BAIXA'
              };
            }

            materiaisNecessarios[materialId].quantidadeTotal += material.quantidade || 0;
            materiaisNecessarios[materialId].opsQueUsam.push({
              opId: op.id,
              opNumero: op.numero,
              quantidade: material.quantidade,
              prioridade: op.prioridade || 'NORMAL'
            });

            // Atualizar prioridade máxima
            const prioridades = ['BAIXA', 'NORMAL', 'MÉDIA', 'ALTA'];
            const prioridadeAtual = prioridades.indexOf(materiaisNecessarios[materialId].prioridadeMaxima);
            const prioridadeOP = prioridades.indexOf(op.prioridade || 'NORMAL');
            if (prioridadeOP > prioridadeAtual) {
              materiaisNecessarios[materialId].prioridadeMaxima = op.prioridade || 'NORMAL';
            }
          });
        }
      });

      return materiaisNecessarios;
    }

    // Análise detalhada de uma OP específica (COM CONTROLE DE ARMAZÉM)
    async function analisarOPDetalhadamente(op, materiaisDisponiveis) {
      const produto = produtos.find(p => p.id === op.produtoId);
      const armazemProducao = armazens.find(a => a.id === op.armazemProducaoId);

      const analise = {
        op: op,
        produto: produto,
        armazemProducaoId: op.armazemProducaoId,
        armazemProducaoNome: armazemProducao?.nome || 'Armazém não identificado',
        podeProuzir: false,
        quantidadeMaximaPossivel: 0,
        limitadoPor: null,
        materiaisAnalise: [],
        motivosImpossibilidade: [],
        percentualViabilidade: 0,
        alertasTransferencia: []
      };

      if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
        // OP sem materiais necessários - pode produzir
        analise.podeProuzir = true;
        analise.quantidadeMaximaPossivel = op.quantidade;
        analise.percentualViabilidade = 100;
        analise.motivosImpossibilidade.push('OP sem estrutura de materiais definida');
        return analise;
      }

      let quantidadeMaxima = op.quantidade;
      let podeProuzirTudo = true;

      // Analisar cada material necessário
      for (const material of op.materiaisNecessarios) {
        // 1. VERIFICAR NO ARMAZÉM DE PRODUÇÃO ESPECÍFICO
        const armazemMaterial = material.armazemId || op.armazemProducaoId;
        const chaveEstoque = `${material.produtoId}_${armazemMaterial}`;
        const materialNoArmazem = materiaisDisponiveis.porArmazem[chaveEstoque];

        // 2. VERIFICAR EM TODOS OS ARMAZÉNS (para sugestões de transferência)
        const materialConsolidado = materiaisDisponiveis.consolidado[material.produtoId];

        const materialAnalise = {
          produtoId: material.produtoId,
          codigo: material.codigo || material.produtoId,
          necessario: material.quantidade,
          armazemNecessario: armazemMaterial,
          armazemNecessarioNome: armazens.find(a => a.id === armazemMaterial)?.nome || 'N/A',
          disponivelNoArmazem: materialNoArmazem ? materialNoArmazem.saldoDisponivel : 0,
          disponivelTotal: materialConsolidado ? materialConsolidado.saldoDisponivelConsolidado : 0,
          suficiente: false,
          quantidadeMaximaPossivel: 0,
          problema: null,
          podeTransferir: false,
          armazensAlternativos: []
        };

        // 3. ANÁLISE NO ARMAZÉM CORRETO (CONSIDERANDO RESERVAS DA OP)
        if (!materialNoArmazem) {
          materialAnalise.problema = `Material não encontrado no armazém ${materialAnalise.armazemNecessarioNome}`;
          materialAnalise.suficiente = false;
          podeProuzirTudo = false;

          // Verificar se existe em outros armazéns
          if (materialConsolidado && materialConsolidado.armazensComEstoque.length > 0) {
            materialAnalise.podeTransferir = true;
            materialAnalise.armazensAlternativos = materialConsolidado.armazensComEstoque;
            analise.alertasTransferencia.push({
              material: materialAnalise.codigo,
              armazemOrigem: materialAnalise.armazemNecessarioNome,
              armazensComEstoque: materialConsolidado.armazensComEstoque
            });
          }

          analise.motivosImpossibilidade.push(
            `${materialAnalise.codigo}: não encontrado no armazém ${materialAnalise.armazemNecessarioNome}${materialAnalise.podeTransferir ? ' (disponível em outros armazéns)' : ''}`
          );
        } else {
          // LÓGICA CORRIGIDA: CONSIDERAR RESERVAS DA OP
          const saldoTotal = materialNoArmazem.saldoTotal;
          const saldoReservado = materialNoArmazem.saldoReservado;
          const saldoEmpenhado = materialNoArmazem.saldoEmpenhado;
          const saldoLivre = materialNoArmazem.saldoDisponivel; // Já calculado: total - reservado - empenhado
          const necessario = material.quantidade;

          // VERIFICAR SE JÁ ESTÁ RESERVADO PARA ESTA OP
          let quantidadeReservadaParaEstaOP = 0;
          if (op.materiaisNecessarios) {
            const materialNaOP = op.materiaisNecessarios.find(m => m.produtoId === material.produtoId);
            if (materialNaOP && materialNaOP.quantidadeReservada) {
              quantidadeReservadaParaEstaOP = materialNaOP.quantidadeReservada;
            }
          }

          // DISPONIBILIDADE REAL = SALDO LIVRE + RESERVADO PARA ESTA OP
          const disponivelParaEstaOP = saldoLivre + quantidadeReservadaParaEstaOP;

          // Adicionar informações de reserva na análise
          materialAnalise.saldoTotal = saldoTotal;
          materialAnalise.saldoReservado = saldoReservado;
          materialAnalise.saldoEmpenhado = saldoEmpenhado;
          materialAnalise.saldoLivre = saldoLivre;
          materialAnalise.quantidadeReservadaParaEstaOP = quantidadeReservadaParaEstaOP;
          materialAnalise.disponivelParaEstaOP = disponivelParaEstaOP;

          if (disponivelParaEstaOP >= necessario) {
            materialAnalise.suficiente = true;
            // CORREÇÃO: Usar arredondamento inteligente
            const divisaoExata = disponivelParaEstaOP / (necessario / op.quantidade);
            materialAnalise.quantidadeMaximaPossivel = arredondamentoInteligente(divisaoExata);

            // Indicar se está usando reserva
            if (quantidadeReservadaParaEstaOP > 0) {
              materialAnalise.usandoReserva = true;
              materialAnalise.observacao = `Usando ${quantidadeReservadaParaEstaOP.toFixed(3)} já reservado para esta OP`;
            }
          } else {
            materialAnalise.suficiente = false;
            // CORREÇÃO: Usar arredondamento inteligente
            const divisaoExata = disponivelParaEstaOP / (necessario / op.quantidade);
            materialAnalise.quantidadeMaximaPossivel = arredondamentoInteligente(divisaoExata);

            const falta = necessario - disponivelParaEstaOP;
            materialAnalise.problema = disponivelParaEstaOP <= 0 ? 'Sem estoque no armazém' : 'Estoque insuficiente no armazém';

            podeProuzirTudo = false;
            quantidadeMaxima = Math.min(quantidadeMaxima, materialAnalise.quantidadeMaximaPossivel);

            if (!analise.limitadoPor || materialAnalise.quantidadeMaximaPossivel < quantidadeMaxima) {
              analise.limitadoPor = material.produtoId;
            }

            // Verificar se transferência resolveria
            if (materialConsolidado && materialConsolidado.saldoDisponivelConsolidado >= falta) {
              materialAnalise.podeTransferir = true;
              materialAnalise.armazensAlternativos = materialConsolidado.armazensComEstoque;
              analise.alertasTransferencia.push({
                material: materialAnalise.codigo,
                faltaNoArmazem: falta,
                disponivelTotal: materialConsolidado.saldoDisponivelConsolidado,
                armazensComEstoque: materialConsolidado.armazensComEstoque
              });
            }

            let motivoDetalhado = `${materialAnalise.codigo}: falta ${falta.toFixed(3)} ${materialNoArmazem.unidade}`;
            if (quantidadeReservadaParaEstaOP > 0) {
              motivoDetalhado += ` (já tem ${quantidadeReservadaParaEstaOP.toFixed(3)} reservado)`;
            }
            motivoDetalhado += ` no armazém ${materialAnalise.armazemNecessarioNome}`;
            if (materialAnalise.podeTransferir) {
              motivoDetalhado += ' (transferência resolveria)';
            }

            analise.motivosImpossibilidade.push(motivoDetalhado);
          }
        }

        analise.materiaisAnalise.push(materialAnalise);
      }

      analise.podeProuzir = podeProuzirTudo;
      analise.quantidadeMaximaPossivel = Math.max(0, quantidadeMaxima);
      analise.percentualViabilidade = (analise.quantidadeMaximaPossivel / op.quantidade * 100);

      return analise;
    }

    // Identificar gargalos críticos
    function identificarGargalosCriticos(diagnostico) {
      const gargalos = [];
      const materiaisCriticos = {};

      // Analisar materiais que bloqueiam múltiplas OPs
      Object.values(diagnostico.materiaisNecessarios).forEach(material => {
        const materialDisponivel = diagnostico.materiaisDisponiveis[material.produto?.id];
        const disponivel = materialDisponivel ? materialDisponivel.saldoDisponivel : 0;
        const necessario = material.quantidadeTotal;

        if (disponivel < necessario) {
          const falta = necessario - disponivel;
          const opsAfetadas = material.opsQueUsam.length;
          const criticidade = calcularCriticidade(falta, necessario, opsAfetadas, material.prioridadeMaxima);

          gargalos.push({
            materialId: material.produto?.id,
            codigo: material.codigo,
            descricao: material.descricao,
            unidade: material.unidade,
            disponivel,
            necessario,
            falta,
            opsAfetadas,
            prioridadeMaxima: material.prioridadeMaxima,
            criticidade,
            opsDetalhes: material.opsQueUsam
          });
        }
      });

      // Ordenar por criticidade
      gargalos.sort((a, b) => b.criticidade - a.criticidade);

      return gargalos;
    }

    // Calcular criticidade de um gargalo
    function calcularCriticidade(falta, necessario, opsAfetadas, prioridade) {
      const percentualFalta = (falta / necessario) * 100;
      const pesoPrioridade = { 'BAIXA': 1, 'NORMAL': 2, 'MÉDIA': 3, 'ALTA': 4 };
      const pesoOPs = Math.min(opsAfetadas * 10, 50); // Máximo 50 pontos
      const pesoFalta = Math.min(percentualFalta, 100); // Máximo 100 pontos
      const pesoPrio = (pesoPrioridade[prioridade] || 2) * 10; // Máximo 40 pontos

      return pesoFalta + pesoOPs + pesoPrio;
    }

    // Buscar oportunidades de produção
    function buscarOportunidadesProducao(diagnostico) {
      const oportunidades = [];

      // 1. OPs que podem ser produzidas parcialmente
      diagnostico.opsAnalisadas.forEach(analise => {
        if (!analise.podeProuzir && analise.quantidadeMaximaPossivel > 0) {
          oportunidades.push({
            tipo: 'PRODUCAO_PARCIAL',
            opId: analise.op.id,
            opNumero: analise.op.numero,
            produtoCodigo: analise.produto?.codigo,
            quantidadeOriginal: analise.op.quantidade,
            quantidadePossivel: analise.quantidadeMaximaPossivel,
            percentual: analise.percentualViabilidade,
            limitadoPor: analise.limitadoPor,
            descricao: `Produzir ${analise.quantidadeMaximaPossivel} de ${analise.op.quantidade} (${analise.percentualViabilidade.toFixed(1)}%)`
          });
        }
      });

      // 2. Materiais próximos do limite
      Object.values(diagnostico.materiaisDisponiveis).forEach(material => {
        if (material.saldoDisponivel > 0 && material.saldoDisponivel < 10) { // Menos de 10 unidades
          const materialNecessario = diagnostico.materiaisNecessarios[material.produto?.id];
          if (materialNecessario) {
            oportunidades.push({
              tipo: 'MATERIAL_LIMITE',
              materialId: material.produto?.id,
              codigo: material.codigo,
              disponivel: material.saldoDisponivel,
              unidade: material.unidade,
              opsAfetadas: materialNecessario.opsQueUsam.length,
              descricao: `Material ${material.codigo} com apenas ${material.saldoDisponivel} ${material.unidade} disponível`
            });
          }
        }
      });

      return oportunidades;
    }

    // Gerar sugestões de desbloqueio
    function gerarSugestoesDesbloqueio(diagnostico) {
      const sugestoes = [];

      // 1. Compras urgentes baseadas em gargalos
      const top5Gargalos = diagnostico.gargalosIdentificados.slice(0, 5);
      if (top5Gargalos.length > 0) {
        sugestoes.push({
          tipo: 'COMPRA_URGENTE',
          prioridade: 'ALTA',
          titulo: 'Compras Urgentes - Top 5 Gargalos',
          descricao: 'Solicitar compra imediata dos materiais mais críticos',
          materiais: top5Gargalos.map(g => ({
            codigo: g.codigo,
            falta: g.falta,
            unidade: g.unidade,
            opsAfetadas: g.opsAfetadas
          })),
          impacto: `Desbloquearia ${top5Gargalos.reduce((total, g) => total + g.opsAfetadas, 0)} OP(s)`
        });
      }

      // 2. Transferências entre armazéns
      sugestoes.push({
        tipo: 'TRANSFERENCIA',
        prioridade: 'MÉDIA',
        titulo: 'Verificar Transferências',
        descricao: 'Verificar se há materiais em outros armazéns que podem ser transferidos',
        acao: 'Consultar saldos em todos os armazéns para os materiais críticos'
      });

      // 3. Produção parcial estratégica
      const oportunidadesParciais = diagnostico.oportunidadesProducao.filter(o => o.tipo === 'PRODUCAO_PARCIAL');
      if (oportunidadesParciais.length > 0) {
        const melhorOportunidade = oportunidadesParciais.sort((a, b) => b.percentual - a.percentual)[0];
        sugestoes.push({
          tipo: 'PRODUCAO_PARCIAL',
          prioridade: 'MÉDIA',
          titulo: 'Produção Parcial Estratégica',
          descricao: `Produzir parcialmente OP ${melhorOportunidade.opNumero} (${melhorOportunidade.percentual.toFixed(1)}% viável)`,
          detalhes: melhorOportunidade
        });
      }

      // 4. Revisão de prioridades
      sugestoes.push({
        tipo: 'REVISAO_PRIORIDADES',
        prioridade: 'BAIXA',
        titulo: 'Revisar Prioridades',
        descricao: 'Considerar alterar prioridades das OPs conforme disponibilidade de material',
        acao: 'Priorizar OPs com materiais disponíveis'
      });

      return sugestoes;
    }

    // Gerar resumo final da impossibilidade
    function gerarResumoImpossibilidade(diagnostico) {
      const totalOPs = diagnostico.totalOPs;
      const opsViaveis = diagnostico.opsAnalisadas.filter(a => a.podeProuzir).length;
      const opsParciais = diagnostico.opsAnalisadas.filter(a => !a.podeProuzir && a.quantidadeMaximaPossivel > 0).length;
      const opsInviaveis = diagnostico.opsAnalisadas.filter(a => a.quantidadeMaximaPossivel === 0).length;

      const totalMateriais = Object.keys(diagnostico.materiaisDisponiveis.porArmazem).length;
      const materiaisDisponiveis = Object.values(diagnostico.materiaisDisponiveis.porArmazem).filter(m => m.saldoDisponivel > 0).length;
      const materiaisZerados = totalMateriais - materiaisDisponiveis;

      // Contar alertas de transferência
      let totalAlertasTransferencia = 0;
      diagnostico.opsAnalisadas.forEach(analise => {
        if (analise.alertasTransferencia) {
          totalAlertasTransferencia += analise.alertasTransferencia.length;
        }
      });

      return {
        totalOPs,
        opsViaveis,
        opsParciais,
        opsInviaveis,
        percentualViabilidade: Math.round((opsViaveis / totalOPs) * 100),
        totalMateriais,
        materiaisDisponiveis,
        materiaisZerados,
        percentualMateriaisDisponiveis: Math.round((materiaisDisponiveis / totalMateriais) * 100),
        gargalosCriticos: diagnostico.gargalosIdentificados.length,
        oportunidades: diagnostico.oportunidadesProducao.length,
        alertasTransferencia: totalAlertasTransferencia,
        confirmacaoImpossibilidade: opsViaveis === 0 && opsParciais === 0
      };
    }

    // Função para solicitar transferência
    window.solicitarTransferencia = function(materialCodigo, opNumero) {
      const mensagem = `🔄 SOLICITAÇÃO DE TRANSFERÊNCIA\n\n` +
                      `Material: ${materialCodigo}\n` +
                      `Para OP: ${opNumero}\n\n` +
                      `Esta funcionalidade pode ser integrada com:\n` +
                      `• Sistema de transferências entre armazéns\n` +
                      `• Workflow de aprovação\n` +
                      `• Notificações automáticas\n` +
                      `• Controle de movimentação\n\n` +
                      `Deseja implementar esta integração?`;

      alert(mensagem);

      // Aqui seria a integração real com sistema de transferências
      mostrarNotificacao(`🔄 Transferência solicitada: ${materialCodigo} para OP ${opNumero}`, 'info', 3000);
    };

    // Mostrar relatório de impossibilidade
    function mostrarRelatorioImpossibilidade(diagnostico) {
      // Salvar diagnóstico globalmente
      window.diagnosticoAtual = diagnostico;

      const resumo = diagnostico.resumoImpossibilidade;

      let html = `
        <div style="padding: 20px;">
          <!-- RESUMO EXECUTIVO -->
          <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle"></i> Diagnóstico de Impossibilidade
              ${resumo.confirmacaoImpossibilidade ? '<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">CONFIRMADO</span>' : '<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">PARCIAL</span>'}
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;">${resumo.totalOPs}</div>
                <div style="font-size: 12px;">OPs Analisadas</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: ${resumo.opsViaveis > 0 ? '#90EE90' : '#FFB6C1'};">${resumo.opsViaveis}</div>
                <div style="font-size: 12px;">Viáveis (${resumo.percentualViabilidade}%)</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFD700;">${resumo.opsParciais}</div>
                <div style="font-size: 12px;">Parciais</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFB6C1;">${resumo.opsInviaveis}</div>
                <div style="font-size: 12px;">Impossíveis</div>
              </div>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
              <h6 style="margin: 0 0 8px 0; display: flex; align-items: center; gap: 5px;">
                <i class="fas fa-warehouse"></i> Status do Estoque por Armazém
              </h6>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 12px;">
                <div>📦 <strong>${resumo.totalMateriais}</strong> posições de estoque</div>
                <div>✅ <strong>${resumo.materiaisDisponiveis}</strong> com saldo</div>
                <div>❌ <strong>${resumo.materiaisZerados}</strong> zeradas</div>
                <div>📊 <strong>${resumo.percentualMateriaisDisponiveis}%</strong> disponibilidade</div>
              </div>
              <div style="margin-top: 8px; font-size: 11px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-info-circle"></i> Análise considera armazém específico de cada OP
              </div>
            </div>
          </div>
      `;

      // CONFIRMAÇÃO DE IMPOSSIBILIDADE
      if (resumo.confirmacaoImpossibilidade) {
        html += `
          <div style="background: #f8d7da; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px; text-align: center;">
            <h4 style="color: #721c24; margin: 0 0 10px 0; display: flex; align-items: center; justify-content: center; gap: 10px;">
              <i class="fas fa-ban" style="font-size: 24px;"></i> CONFIRMAÇÃO DE IMPOSSIBILIDADE
            </h4>
            <p style="color: #721c24; margin: 0; font-size: 16px; font-weight: bold;">
              ✅ CONFIRMADO: Com os materiais disponíveis no estoque atual, NÃO É POSSÍVEL produzir nenhuma OP completamente.
            </p>
            <p style="color: #721c24; margin: 10px 0 0 0; font-size: 14px;">
              ${resumo.opsParciais > 0 ? `Porém, ${resumo.opsParciais} OP(s) podem ser produzidas parcialmente.` : 'Nenhuma produção parcial é possível.'}
            </p>
          </div>
        `;
      } else {
        html += `
          <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin-bottom: 20px; text-align: center;">
            <h4 style="color: #856404; margin: 0 0 10px 0; display: flex; align-items: center; justify-content: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i> IMPOSSIBILIDADE PARCIAL
            </h4>
            <p style="color: #856404; margin: 0; font-size: 16px; font-weight: bold;">
              ⚠️ A maioria das OPs não pode ser produzida, mas ${resumo.opsViaveis + resumo.opsParciais} OP(s) têm alguma viabilidade.
            </p>
          </div>
        `;
      }

      // GARGALOS CRÍTICOS
      if (diagnostico.gargalosIdentificados.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #dc3545; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-circle"></i> Gargalos Críticos (${diagnostico.gargalosIdentificados.length})
            </h4>
            <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: 5px;">
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden;">
                <thead>
                  <tr style="background: linear-gradient(135deg, #dc3545, #c82333); color: white;">
                    <th style="padding: 12px; text-align: left;">Material</th>
                    <th style="padding: 12px; text-align: center;">Disponível</th>
                    <th style="padding: 12px; text-align: center;">Necessário</th>
                    <th style="padding: 12px; text-align: center;">Falta</th>
                    <th style="padding: 12px; text-align: center;">OPs Afetadas</th>
                    <th style="padding: 12px; text-align: center;">Criticidade</th>
                  </tr>
                </thead>
                <tbody>
        `;

        diagnostico.gargalosIdentificados.slice(0, 10).forEach((gargalo, index) => {
          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
          const criticidadeCor = gargalo.criticidade > 150 ? '#dc3545' : gargalo.criticidade > 100 ? '#fd7e14' : '#ffc107';

          html += `
            <tr style="background: ${rowColor};">
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${gargalo.codigo}</div>
                <div style="font-size: 12px; color: #6c757d;">${gargalo.descricao}</div>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="color: #dc3545; font-weight: bold;">${gargalo.disponivel.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="font-weight: bold;">${gargalo.necessario.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="color: #dc3545; font-weight: bold; font-size: 14px;">${gargalo.falta.toFixed(3)}</span>
                <br><small>${gargalo.unidade}</small>
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-weight: bold;">
                  ${gargalo.opsAfetadas}
                </span>
              </td>
              <td style="padding: 10px; text-align: center;">
                <div style="background: ${criticidadeCor}; color: white; padding: 4px 8px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                  ${gargalo.criticidade.toFixed(0)}
                </div>
                <small style="color: #6c757d;">${gargalo.prioridadeMaxima}</small>
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // ALERTAS DE TRANSFERÊNCIA
      const alertasTransferencia = [];
      diagnostico.opsAnalisadas.forEach(analise => {
        if (analise.alertasTransferencia && analise.alertasTransferencia.length > 0) {
          analise.alertasTransferencia.forEach(alerta => {
            alerta.opNumero = analise.op.numero;
            alerta.armazemProducao = analise.armazemProducaoNome;
            alertasTransferencia.push(alerta);
          });
        }
      });

      if (alertasTransferencia.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #17a2b8; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exchange-alt"></i> Oportunidades de Transferência (${alertasTransferencia.length})
            </h4>
            <div style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; border-radius: 5px;">
              <div style="background: #bee5eb; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                <p style="margin: 0; color: #0c5460; font-weight: bold;">
                  <i class="fas fa-lightbulb"></i> Materiais disponíveis em outros armazéns podem resolver alguns bloqueios!
                </p>
              </div>
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden;">
                <thead>
                  <tr style="background: linear-gradient(135deg, #17a2b8, #138496); color: white;">
                    <th style="padding: 12px; text-align: left;">OP</th>
                    <th style="padding: 12px; text-align: left;">Material</th>
                    <th style="padding: 12px; text-align: center;">Armazém Produção</th>
                    <th style="padding: 12px; text-align: center;">Disponível Em</th>
                    <th style="padding: 12px; text-align: center;">Ação</th>
                  </tr>
                </thead>
                <tbody>
        `;

        alertasTransferencia.slice(0, 15).forEach((alerta, index) => {
          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

          html += `
            <tr style="background: ${rowColor};">
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${alerta.opNumero}</div>
                <div style="font-size: 12px; color: #6c757d;">${alerta.armazemProducao}</div>
              </td>
              <td style="padding: 10px;">
                <div style="font-weight: bold;">${alerta.material}</div>
                ${alerta.faltaNoArmazem ? `<div style="font-size: 12px; color: #dc3545;">Falta: ${alerta.faltaNoArmazem.toFixed(3)}</div>` : ''}
              </td>
              <td style="padding: 10px; text-align: center;">
                <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">
                  SEM ESTOQUE
                </span>
              </td>
              <td style="padding: 10px; text-align: center;">
          `;

          alerta.armazensComEstoque.forEach((arm, armIndex) => {
            if (armIndex < 2) { // Mostrar apenas os 2 primeiros
              html += `
                <div style="margin: 2px 0;">
                  <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px;">
                    ${arm.armazemNome}: ${arm.saldoDisponivel.toFixed(3)}
                  </span>
                </div>
              `;
            }
          });

          if (alerta.armazensComEstoque.length > 2) {
            html += `<div style="font-size: 10px; color: #6c757d;">+${alerta.armazensComEstoque.length - 2} outros</div>`;
          }

          html += `
              </td>
              <td style="padding: 10px; text-align: center;">
                <button onclick="solicitarTransferencia('${alerta.material}', '${alerta.opNumero}')"
                        style="background: #17a2b8; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
                  🔄 Transferir
                </button>
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      html += `</div>`;

      // Mostrar no modal
      document.getElementById('conteudoDiagnosticoImpossibilidade').innerHTML = html;
      document.getElementById('modalDiagnosticoImpossibilidade').style.display = 'block';


    }

    // Funções do modal de diagnóstico
    window.fecharModalDiagnostico = function() {
      document.getElementById('modalDiagnosticoImpossibilidade').style.display = 'none';
    };

    // Funções do modal de materiais faltantes
    window.fecharModalMateriaisFaltantes = function() {
      document.getElementById('modalMateriaisFaltantes').style.display = 'none';
    };

    // 🔧 NOVA FUNÇÃO: Verificar apenas MATÉRIAS-PRIMAS na Etapa 2 (ignora SP)
    async function verificarMateriaisEtapa2(opId, nomeOP) {
      try {
        console.log('🔍 ETAPA 2: Verificando apenas MATÉRIAS-PRIMAS (ignorando SP) para OP:', opId);

        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          console.error(`❌ OP ${opId} não encontrada`);
          return { sucesso: false, erro: 'OP não encontrada' };
        }

        const opData = opDoc.data();
        const materiaisNecessarios = opData.materiaisNecessarios || [];
        const armazemProducaoId = opData.armazemProducaoId;

        console.log(`📦 Verificando ${materiaisNecessarios.length} materiais no armazém ${armazemProducaoId}`);

        // 🔧 FILTRAR APENAS MATÉRIAS-PRIMAS (ignorar SP)
        const materiaisMP = materiaisNecessarios.filter(material => {
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;
          const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';

          if (ehSubproduto) {
            console.log(`⏭️ IGNORANDO SP na Etapa 2: ${codigo}`);
            return false;
          }
          return true;
        });

        console.log(`🎯 ETAPA 2: Verificando ${materiaisMP.length} matérias-primas (${materiaisNecessarios.length - materiaisMP.length} SP ignorados)`);

        const materiaisComProblemas = [];
        const materiaisOK = [];
        const alertas = [];

        for (const material of materiaisMP) {
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;

          console.log(`🔍 Verificando MP: ${codigo}`);

          // Verificar SALDO REAL no armazém de produção
          const estoqueQuery = query(
            collection(db, "estoques"),
            where("produtoId", "==", material.produtoId),
            where("armazemId", "==", armazemProducaoId)
          );

          const estoqueSnap = await getDocs(estoqueQuery);
          let saldoDisponivel = 0;

          if (!estoqueSnap.empty) {
            const estoque = estoqueSnap.docs[0].data();
            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;

            // 🔧 CORREÇÃO DEFINITIVA: Material transferido = empenhado para a OP
            // Na Etapa 2, verificar se há empenho ativo para esta OP
            const empenhosQuery = query(
              collection(db, "empenhos"),
              where("produtoId", "==", material.produtoId),
              where("armazemId", "==", armazemProducaoId),
              where("ordemProducaoId", "==", opId),
              where("status", "==", "ATIVO")
            );

            const empenhosSnap = await getDocs(empenhosQuery);
            let quantidadeEmpenhada = 0;

            if (!empenhosSnap.empty) {
              empenhosSnap.docs.forEach(doc => {
                const empenho = doc.data();
                quantidadeEmpenhada += (empenho.quantidadeEmpenhada || 0) - (empenho.quantidadeConsumida || 0);
              });
            }

            // Saldo disponível = saldo empenhado para esta OP + saldo livre
            const saldoLivre = saldoTotal - saldoReservado - saldoEmpenhado;
            saldoDisponivel = quantidadeEmpenhada + saldoLivre;

            console.log(`✅ MP ${codigo}: Total = ${saldoTotal}, Reservado = ${saldoReservado}, Empenhado = ${saldoEmpenhado}`);
            console.log(`🔒 MP ${codigo}: Empenhado para esta OP = ${quantidadeEmpenhada}`);
            console.log(`🔧 MP ${codigo}: Disponível = ${saldoDisponivel} (empenhado + livre)`);
          } else {
            console.log(`❌ MP ${codigo}: Nenhum estoque encontrado`);
            saldoDisponivel = 0;
          }

          // Verificar se há material suficiente
          const quantidadeNecessaria = material.quantidade || 0;

          if (saldoDisponivel >= quantidadeNecessaria) {
            materiaisOK.push({
              codigo,
              descricao: produto?.descricao || 'Sem descrição',
              necessario: quantidadeNecessaria,
              disponivel: saldoDisponivel,
              status: 'OK'
            });
            console.log(`✅ MP ${codigo}: OK (${saldoDisponivel} >= ${quantidadeNecessaria})`);
          } else {
            materiaisComProblemas.push({
              codigo,
              descricao: produto?.descricao || 'Sem descrição',
              necessario: quantidadeNecessaria,
              disponivel: saldoDisponivel,
              falta: quantidadeNecessaria - saldoDisponivel,
              status: 'INSUFICIENTE'
            });
            console.log(`❌ MP ${codigo}: INSUFICIENTE (${saldoDisponivel} < ${quantidadeNecessaria})`);
          }
        }

        // Resultado da validação
        const resultado = {
          sucesso: materiaisComProblemas.length === 0,
          materiaisOK: materiaisOK.length,
          materiaisComProblemas: materiaisComProblemas.length,
          alertas: alertas
        };

        console.log(`📊 RESULTADO ETAPA 2:`, resultado);

        if (resultado.sucesso) {
          console.log(`✅ ETAPA 2: Todas as matérias-primas OK!`);
          return resultado;
        } else {
          console.log(`❌ ETAPA 2: ${materiaisComProblemas.length} matérias-primas com problemas`);

          // Mostrar modal específico para Etapa 2 (apenas MP)
          mostrarModalMateriaisEtapa2(materiaisComProblemas, nomeOP);
          return resultado;
        }

      } catch (error) {
        console.error('❌ Erro ao verificar materiais na Etapa 2:', error);
        return { sucesso: false, erro: error.message };
      }
    }

    // Função para mostrar modal específico da Etapa 2 (apenas matérias-primas)
    function mostrarModalMateriaisEtapa2(materiaisComProblemas, nomeOP) {
      console.log('🔴 ETAPA 2: Mostrando modal com matérias-primas insuficientes:', materiaisComProblemas.length);

      const modal = document.getElementById('modalMateriaisFaltantes');
      const conteudo = document.getElementById('conteudoMateriaisFaltantes');

      if (!modal || !conteudo) {
        console.error('❌ Modal não encontrado!');
        return;
      }

      let html = `
        <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle"></i>
            ETAPA 2: VERIFICAÇÃO DE MATÉRIAS-PRIMAS
          </h3>
          <div style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #ffc107;">
            <p style="margin: 0 0 10px 0; color: #856404; font-weight: 500;">
              📋 OP: <strong>${nomeOP}</strong>
            </p>
            <p style="margin: 0; color: #856404; font-size: 14px;">
              💡 <strong>Nota:</strong> Na Etapa 2, verificamos apenas <strong>matérias-primas (MP)</strong>.
              Os subprodutos (SP) serão verificados apenas no apontamento final.
            </p>
          </div>
        </div>
      `;

      if (materiaisComProblemas.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0; border-bottom: 1px solid #dc3545; padding-bottom: 8px;">
              ❌ MATÉRIAS-PRIMAS COM SALDO INSUFICIENTE (${materiaisComProblemas.length})
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #dc3545; color: white;">
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Código</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Descrição</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Necessário</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Disponível</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Faltante</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">Ação</th>
                  </tr>
                </thead>
                <tbody>
        `;

        materiaisComProblemas.forEach(material => {
          html += `
            <tr style="background: #f8f9fa;">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${material.codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                ${material.descricao}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-weight: bold;">
                ${material.necessario.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${material.disponivel.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">
                ${material.falta.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                <span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                  Transferir MP
                </span>
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      html += `
        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h5 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES RECOMENDADAS</h5>
          <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
            <li><strong>Transferir materiais:</strong> Use o módulo de movimentação para transferir as matérias-primas faltantes</li>
            <li><strong>Verificar estoque:</strong> Confirme se há estoque suficiente no almoxarifado</li>
            <li><strong>Após resolver:</strong> Clique novamente em "Verificar Saldo" para validar</li>
            <li><strong>Subprodutos:</strong> Serão verificados apenas no apontamento final</li>
          </ul>
        </div>
      `;

      conteudo.innerHTML = html;
      modal.style.display = 'block';
      console.log('✅ Modal ETAPA 2 exibido com sucesso!');
    }

    // Função que verifica materiais considerando SALDO REAL no armazém de produção
    async function verificarMateriaisEMostrarModal(opId, nomeOP) {
      try {
        console.log('🔍 Verificando materiais com saldo REAL no armazém para OP:', opId);

        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          console.error(`❌ OP ${opId} não encontrada`);
          return;
        }

        const opData = opDoc.data();
        const materiaisNecessarios = opData.materiaisNecessarios || [];
        // Usar o mesmo armazemProducaoId que é usado no apontamento
        const armazemProducaoId = opData.armazemProducaoId;

        console.log(`📦 Verificando ${materiaisNecessarios.length} materiais no armazém ${armazemProducaoId}`);

        // Verificar cada material individualmente
        const materiaisComProblemas = [];
        const materiaisOK = [];

        for (const material of materiaisNecessarios) {
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;
          const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';

          console.log(`🔍 Verificando material: ${codigo} (${ehSubproduto ? 'SP' : 'MP'})`);
          console.log(`📝 Produto encontrado:`, produto);
          console.log(`📝 Descrição do produto:`, produto?.descricao);

          let saldoDisponivel = 0;
          let quantidadeTransferida = 0;

          if (ehSubproduto) {
            // Para SUBPRODUTOS: buscar saldo na coleção estoques usando o MESMO armazemProducaoId do apontamento
            console.log(`🔍 SP ${codigo}: Buscando no armazém ${armazemProducaoId}, produtoId: ${material.produtoId}`);

            const estoqueQuery = query(
              collection(db, "estoques"),
              where("produtoId", "==", material.produtoId),
              where("armazemId", "==", armazemProducaoId)
            );

            const estoqueSnap = await getDocs(estoqueQuery);
            console.log(`📦 SP ${codigo}: Encontrados ${estoqueSnap.docs.length} registros de estoque`);

            if (!estoqueSnap.empty) {
              const estoque = estoqueSnap.docs[0].data();
              saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, opId);
              quantidadeTransferida = saldoDisponivel;

              console.log(`✅ SP ${codigo}: Saldo encontrado = ${estoque.saldo}, Reservado = ${estoque.saldoReservado || 0}, Disponível = ${saldoDisponivel}`);

              // Se há saldo reservado, verificar para qual OP está reservado
              if ((estoque.saldoReservado || 0) > 0) {
                console.log(`🔍 Verificando reservas para SP ${codigo}...`);

                // Buscar empenhos que podem estar reservando este material
                const empenhosQuery = query(
                  collection(db, "empenhos"),
                  where("produtoId", "==", material.produtoId),
                  where("armazemId", "==", armazemProducaoId),
                  where("status", "in", ["EMPENHADO", "PARCIAL"])
                );

                const empenhosSnap = await getDocs(empenhosQuery);

                if (!empenhosSnap.empty) {
                  console.log(`📋 SP ${codigo}: Encontrados ${empenhosSnap.docs.length} empenhos ativos:`);

                  empenhosSnap.docs.forEach((doc, index) => {
                    const empenho = doc.data();
                    console.log(`  ${index + 1}. OP: ${empenho.ordemProducaoId || 'N/A'}, Quantidade: ${empenho.quantidade || 0}, Status: ${empenho.status}`);
                  });

                  // Verificar se algum empenho é da OP atual
                  const empenhoAtual = empenhosSnap.docs.find(doc => {
                    const empenho = doc.data();
                    return empenho.ordemProducaoId === opId;
                  });

                  if (empenhoAtual) {
                    console.log(`🎯 SP ${codigo}: RESERVADO PARA A PRÓPRIA OP ATUAL (${opId})`);
                  } else {
                    const outrasOPs = empenhosSnap.docs.map(doc => doc.data().ordemProducaoId).filter(Boolean);
                    console.log(`⚠️ SP ${codigo}: RESERVADO PARA OUTRAS OPs: ${outrasOPs.join(', ')}`);
                  }
                } else {
                  console.log(`❓ SP ${codigo}: Saldo reservado mas nenhum empenho ativo encontrado`);
                  console.log(`🔧 SP ${codigo}: VERIFICANDO SE É SEGURO LIMPAR RESERVA ÓRFÃ...`);

                  // Limpar reserva órfã com segurança
                  try {
                    // Verificar se outras OPs dependem desta reserva
                    const opsQuery = query(
                      collection(db, "ordensProducao"),
                      where("status", "in", ["Em Produção", "Aguardando Material"])
                    );

                    const opsSnap = await getDocs(opsQuery);
                    let reservaUsadaPorOutraOP = false;

                    for (const opDoc of opsSnap.docs) {
                      if (opDoc.id === opId) continue; // Pular a OP atual

                      const opData = opDoc.data();
                      const materialNaOP = opData.materiaisNecessarios?.find(m =>
                        m.produtoId === material.produtoId && (m.saldoReservado || 0) > 0
                      );

                      if (materialNaOP) {
                        console.log(`⚠️ SP ${codigo}: Reserva está sendo usada pela OP ${opData.numero}: ${materialNaOP.saldoReservado}`);
                        reservaUsadaPorOutraOP = true;
                        break;
                      }
                    }

                    if (!reservaUsadaPorOutraOP) {
                      // Seguro para limpar - usar transação atômica
                      await TransactionManager.executeAtomicOperation(`LIMPAR_RESERVA_ORFA_${codigo}_${Date.now()}`, [
                        {
                          type: 'UPDATE',
                          ref: doc(db, "estoques", estoqueSnap.docs[0].id),
                          data: {
                            saldoReservado: 0,
                            observacoes: `Reserva órfã removida: ${estoque.saldoReservado} (sem empenho correspondente)`
                          }
                        }
                      ]);

                      // Recalcular saldo disponível
                      saldoDisponivel = estoque.saldo || 0;
                      quantidadeTransferida = saldoDisponivel;

                      console.log(`✅ SP ${codigo}: Reserva órfã removida com segurança! Novo saldo disponível: ${saldoDisponivel}`);
                    } else {
                      console.log(`❌ SP ${codigo}: Não é seguro limpar reserva - está sendo usada por outra OP`);
                      // Manter saldo disponível como estava
                      saldoDisponivel = Math.max(0, (estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0));
                    }

                  } catch (error) {
                    console.error(`❌ Erro ao verificar/limpar reserva órfã para SP ${codigo}:`, error);
                    // Em caso de erro, usar saldo conservador
                    saldoDisponivel = Math.max(0, (estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0));
                  }
                }
              }
            } else {
              console.log(`❌ SP ${codigo}: Nenhum estoque encontrado no armazém ${armazemProducaoId}`);
            }
          } else {
            // Para MATÉRIAS-PRIMAS: verificar SALDO REAL no armazém de produção
            console.log(`🔍 MP ${codigo}: Buscando saldo real no armazém ${armazemProducaoId}, produtoId: ${material.produtoId}`);

            const estoqueQuery = query(
              collection(db, "estoques"),
              where("produtoId", "==", material.produtoId),
              where("armazemId", "==", armazemProducaoId)
            );

            const estoqueSnap = await getDocs(estoqueQuery);
            console.log(`📦 MP ${codigo}: Encontrados ${estoqueSnap.docs.length} registros de estoque`);

            if (!estoqueSnap.empty) {
              const estoque = estoqueSnap.docs[0].data();
              saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, opId);
              console.log(`✅ MP ${codigo}: Saldo encontrado = ${estoque.saldo}, Reservado = ${estoque.saldoReservado || 0}, Empenhado = ${estoque.saldoEmpenhado || 0}, Disponível = ${saldoDisponivel}`);
            } else {
              console.log(`❌ MP ${codigo}: Nenhum estoque encontrado no armazém ${armazemProducaoId}`);
              saldoDisponivel = 0;
            }

            // Também verificar transferências para referência
            const transferenciasSnap = await getDocs(
              query(
                collection(db, "transferenciasArmazem"),
                where("ordemProducaoId", "==", opId),
                where("produtoId", "==", material.produtoId)
              )
            );

            transferenciasSnap.docs.forEach(doc => {
              const transferencia = doc.data();
              if (transferencia.tipo === "OP" && transferencia.status !== "CANCELADA") {
                quantidadeTransferida += transferencia.quantidade || 0;
              }
            });

            console.log(`📊 MP ${codigo}: Saldo Real = ${saldoDisponivel}, Transferido = ${quantidadeTransferida}`);
          }

          const quantidadeNecessaria = material.quantidadeNecessaria || material.quantidade || 0;
          const faltante = Math.max(0, quantidadeNecessaria - saldoDisponivel);

          const materialStatus = {
            produtoId: material.produtoId,
            codigo: codigo,
            descricao: produto?.descricao || produto?.nome || 'Descrição não disponível',
            ehSubproduto: ehSubproduto,
            quantidadeNecessaria: quantidadeNecessaria,
            quantidadeTransferida: quantidadeTransferida,
            saldoDisponivel: saldoDisponivel,
            faltante: faltante,
            status: faltante > 0 ? 'nao_transferido' : 'completo'
          };

          if (faltante > 0) {
            materiaisComProblemas.push(materialStatus);
          } else {
            materiaisOK.push(materialStatus);
          }
        }

        console.log('📊 Resultado da verificação:', {
          materiaisOK: materiaisOK.length,
          materiaisComProblemas: materiaisComProblemas.length
        });

        // Se há materiais com problemas, mostrar modal
        if (materiaisComProblemas.length > 0) {
          mostrarModalComMateriaisCorrigidos(materiaisComProblemas, [], nomeOP);
          mostrarNotificacao('❌ Saldo insuficiente - Verifique materiais faltantes', 'error', 5000);
        } else {
          // Todos os materiais estão OK - marcar como validado
          const sucesso = await updateOPFlowStatus(opId, 'saldoValidado', true);
          if (sucesso) {
            alert(`✅ SALDO VALIDADO COM SUCESSO!\n\n📊 OP: ${nomeOP}\n✅ Todos os materiais disponíveis (${materiaisOK.length} itens)\n\n🎯 PRÓXIMO PASSO: Imprimir a OP`);
            mostrarNotificacao('✅ Saldo validado - Pode imprimir OP', 'success', 3000);
            await preservarERecarregar();
          }
        }

      } catch (error) {
        console.error('❌ Erro ao verificar materiais:', error);
        alert('❌ Erro ao verificar materiais. Tente novamente.');
      }
    }

    // Função para mostrar modal usando dados EXATOS do Debug
    function mostrarModalComMateriaisDoDebug(materiaisNaoTransferidos, materiaisParciais, nomeOP) {
      console.log('🔴 Mostrando modal com materiais do Debug:', {
        naoTransferidos: materiaisNaoTransferidos.length,
        parciais: materiaisParciais.length
      });

      const conteudo = document.getElementById('conteudoMateriaisFaltantes');
      if (!conteudo) {
        console.error('❌ Elemento conteudoMateriaisFaltantes não encontrado!');
        return;
      }

      let html = `
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h3 style="color: #721c24; margin: 0 0 10px 0;">
            📋 OP: ${nomeOP}
          </h3>
          <p style="color: #721c24; margin: 0;">
            ⚠️ Os materiais abaixo não foram transferidos ou estão com saldo insuficiente:
          </p>
        </div>
      `;

      // Mostrar materiais NÃO TRANSFERIDOS (igual ao Debug)
      if (materiaisNaoTransferidos.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0; border-bottom: 1px solid #dc3545; padding-bottom: 8px;">
              ❌ MATERIAIS NÃO TRANSFERIDOS (${materiaisNaoTransferidos.length})
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #dc3545; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Código</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Descrição</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Tipo</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Necessário</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Transferido</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Faltante</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Ação Necessária</th>
                  </tr>
                </thead>
                <tbody>
        `;

        materiaisNaoTransferidos.forEach((material, index) => {
          // Usar EXATAMENTE a mesma lógica do Debug
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;
          const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
          const tipo = ehSubproduto ? 'SUBPRODUTO (SP)' : 'MATÉRIA-PRIMA (MP)';
          const corLinha = index % 2 === 0 ? '#fff' : '#f8f9fa';
          const corTipo = ehSubproduto ? '#fd7e14' : '#28a745';

          const necessario = material.quantidadeNecessaria || 0;
          const transferido = material.quantidadeTransferida || 0;
          const faltante = necessario - transferido;

          let acaoNecessaria = '';
          if (ehSubproduto) {
            acaoNecessaria = '🏭 Produzir/Apontar OP de SP';
          } else {
            acaoNecessaria = '📦 Transferir do estoque';
          }

          html += `
            <tr style="background: ${corLinha};">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; color: #495057;">
                ${produto?.descricao || produto?.nome || 'Descrição não disponível'}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                <span style="background: ${corTipo}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${tipo}
                </span>
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-weight: bold;">
                ${necessario.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${transferido.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">
                ${faltante.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                ${acaoNecessaria}
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // Mostrar materiais PARCIAIS se houver
      if (materiaisParciais.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #856404; margin: 0 0 15px 0; border-bottom: 1px solid #ffc107; padding-bottom: 8px;">
              ⚠️ MATERIAIS PARCIAIS (${materiaisParciais.length})
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #ffc107; color: #212529;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #ffc107;">Código</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #ffc107;">Necessário</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #ffc107;">Transferido</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #ffc107;">Percentual</th>
                  </tr>
                </thead>
                <tbody>
        `;

        materiaisParciais.forEach((material, index) => {
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;
          const corLinha = index % 2 === 0 ? '#fff' : '#f8f9fa';

          html += `
            <tr style="background: ${corLinha};">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${material.quantidadeNecessaria.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${material.quantidadeTransferida.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #856404; font-weight: bold;">
                ${material.percentual.toFixed(1)}%
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // Resumo
      const totalProblemas = materiaisNaoTransferidos.length + materiaisParciais.length;
      const subprodutos = materiaisNaoTransferidos.filter(m => {
        const produto = produtos.find(p => p.id === m.produtoId);
        const codigo = produto?.codigo || m.produtoId;
        return codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
      }).length;
      const materiasPrimas = materiaisNaoTransferidos.length - subprodutos;

      html += `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h4 style="color: #856404; margin: 0 0 10px 0;">📊 RESUMO</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${totalProblemas}</div>
              <div style="color: #856404;">Total de Problemas</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #fd7e14;">${subprodutos}</div>
              <div style="color: #856404;">Subprodutos (SP)</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #28a745;">${materiasPrimas}</div>
              <div style="color: #856404;">Matérias-Primas (MP)</div>
            </div>
          </div>
        </div>

        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
          <h4 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES NECESSÁRIAS</h4>
          <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
      `;

      if (materiasPrimas > 0) {
        html += `<li><strong>Matérias-Primas:</strong> Transferir do estoque para o armazém de produção</li>`;
      }
      if (subprodutos > 0) {
        html += `<li><strong>Subprodutos:</strong> Verificar e apontar as OPs de SP correspondentes</li>`;
      }
      if (materiaisParciais.length > 0) {
        html += `<li><strong>Materiais Parciais:</strong> Completar as transferências pendentes</li>`;
      }

      html += `
            <li><strong>Após resolver:</strong> Clique novamente em "Verificar Saldo" para validar</li>
          </ul>
        </div>
      `;

      // Substituir todo o conteúdo do modal
      const modal = document.getElementById('modalMateriaisFaltantes');
      if (!modal) {
        console.error('❌ Modal modalMateriaisFaltantes não encontrado!');
        return;
      }

      // Substituir o conteúdo completo do modal
      modal.innerHTML = `
        <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto; border: 3px solid #dc3545;">
          <span class="close" onclick="fecharModalMateriaisFaltantes()" style="color: #dc3545; font-size: 28px; position: absolute; top: 15px; right: 25px; cursor: pointer;">&times;</span>
          <div style="padding: 20px;">
            ${html}
          </div>
          <div style="text-align: center; padding: 20px; border-top: 1px solid #dc3545;">
            <button onclick="fecharModalMateriaisFaltantes()"
                    style="background: #dc3545; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Fechar
            </button>
          </div>
        </div>
      `;

      modal.style.display = 'block';
      console.log('✅ Modal exibido com sucesso!');
    }

    // Função para mostrar modal com dados CORRIGIDOS (considerando saldo real de SP)
    function mostrarModalComMateriaisCorrigidos(materiaisComProblemas, materiaisParciais, nomeOP) {
      console.log('🔴 Mostrando modal CORRIGIDO com materiais:', {
        comProblemas: materiaisComProblemas.length,
        parciais: materiaisParciais.length
      });

      let html = `
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h3 style="color: #721c24; margin: 0 0 10px 0;">
            📋 OP: ${nomeOP}
          </h3>
          <p style="color: #721c24; margin: 0;">
            ⚠️ Os materiais abaixo não foram transferidos ou estão com saldo insuficiente:
          </p>
        </div>
      `;

      // Mostrar materiais COM PROBLEMAS
      if (materiaisComProblemas.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0; border-bottom: 1px solid #dc3545; padding-bottom: 8px;">
              ❌ MATERIAIS COM SALDO INSUFICIENTE (${materiaisComProblemas.length})
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #dc3545; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Código</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Descrição</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Tipo</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Necessário</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Disponível</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Faltante</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Ação Necessária</th>
                  </tr>
                </thead>
                <tbody>
        `;

        materiaisComProblemas.forEach((material, index) => {
          const tipo = material.ehSubproduto ? 'SUBPRODUTO (SP)' : 'MATÉRIA-PRIMA (MP)';
          const corLinha = index % 2 === 0 ? '#fff' : '#f8f9fa';
          const corTipo = material.ehSubproduto ? '#fd7e14' : '#28a745';

          let acaoNecessaria = '';
          if (material.ehSubproduto) {
            acaoNecessaria = '🏭 Produzir/Apontar OP de SP';
          } else {
            acaoNecessaria = '📦 Transferir do estoque';
          }

          html += `
            <tr style="background: ${corLinha};">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${material.codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; color: #495057;">
                ${material.descricao || material.nome || 'Descrição não disponível'}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                <span style="background: ${corTipo}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${tipo}
                </span>
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-weight: bold;">
                ${material.quantidadeNecessaria.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${material.saldoDisponivel.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">
                ${material.faltante.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                ${acaoNecessaria}
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // Resumo
      const subprodutos = materiaisComProblemas.filter(m => m.ehSubproduto).length;
      const materiasPrimas = materiaisComProblemas.length - subprodutos;

      html += `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h4 style="color: #856404; margin: 0 0 10px 0;">📊 RESUMO</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${materiaisComProblemas.length}</div>
              <div style="color: #856404;">Total de Problemas</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #fd7e14;">${subprodutos}</div>
              <div style="color: #856404;">Subprodutos (SP)</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #28a745;">${materiasPrimas}</div>
              <div style="color: #856404;">Matérias-Primas (MP)</div>
            </div>
          </div>
        </div>

        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
          <h4 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES NECESSÁRIAS</h4>
          <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
      `;

      if (materiasPrimas > 0) {
        html += `<li><strong>Matérias-Primas:</strong> Transferir do estoque para o armazém de produção</li>`;
      }
      if (subprodutos > 0) {
        html += `<li><strong>Subprodutos:</strong> Verificar e apontar as OPs de SP correspondentes</li>`;
      }

      html += `
            <li><strong>Após resolver:</strong> Clique novamente em "Verificar Saldo" para validar</li>
          </ul>
        </div>
      `;

      // Substituir todo o conteúdo do modal
      const modal = document.getElementById('modalMateriaisFaltantes');
      if (!modal) {
        console.error('❌ Modal modalMateriaisFaltantes não encontrado!');
        return;
      }

      // Substituir o conteúdo completo do modal
      modal.innerHTML = `
        <div class="modal-content" style="max-width: 90%; max-height: 90%; overflow-y: auto; border: 3px solid #dc3545;">
          <span class="close" onclick="fecharModalMateriaisFaltantes()" style="color: #dc3545; font-size: 28px; position: absolute; top: 15px; right: 25px; cursor: pointer;">&times;</span>
          <div style="padding: 20px;">
            ${html}
          </div>
          <div style="text-align: center; padding: 20px; border-top: 1px solid #dc3545;">
            <button onclick="fecharModalMateriaisFaltantes()"
                    style="background: #dc3545; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Fechar
            </button>
          </div>
        </div>
      `;

      modal.style.display = 'block';
      console.log('✅ Modal CORRIGIDO exibido com sucesso!');
    }

    // Função para analisar transferências de uma OP (mesma lógica do debug, mas retorna dados)
    async function analisarTransferenciaOP(opId) {
      try {
        console.log('🔍 Iniciando análise de transferência para OP:', opId);

        // Buscar dados da OP
        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          console.error(`❌ OP ${opId} não encontrada`);
          return null;
        }

        const opData = opDoc.data();
        const materiaisNecessarios = opData.materiaisNecessarios || [];
        console.log('📦 Materiais necessários encontrados:', materiaisNecessarios.length);

        // Buscar todas as transferências
        console.log('🔍 Buscando transferências para OP:', opId);
        const transferenciasSnap = await getDocs(
          query(
            collection(db, "transferenciasArmazem"),
            where("ordemProducaoId", "==", opId)
          )
        );

        const transferencias = transferenciasSnap.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          .filter(transferencia =>
            transferencia.tipo === "OP" &&
            transferencia.status !== "CANCELADA"
          );

        console.log('🔄 Transferências encontradas:', transferencias.length);

        // Analisar status usando a mesma função do debug
        console.log('📊 Analisando status dos materiais...');
        const statusMateriais = analisarStatusTransferenciaMateriais(materiaisNecessarios, transferencias);
        const statusGeral = determinarStatusGeralTransferencia(statusMateriais);
        console.log('🎯 Status geral:', statusGeral);

        // Separar materiais por status
        const materiaisCompletos = statusMateriais.filter(m => m.status === 'completo');
        const materiaisParciais = statusMateriais.filter(m => m.status === 'parcial');
        const materiaisNaoTransferidos = statusMateriais.filter(m => m.status === 'nao_transferido');

        console.log('📊 Resumo:', {
          completos: materiaisCompletos.length,
          parciais: materiaisParciais.length,
          naoTransferidos: materiaisNaoTransferidos.length
        });

        // Retornar dados estruturados
        const resultado = {
          opId: opId,
          opData: opData,
          materiaisNecessarios: materiaisNecessarios,
          transferencias: transferencias,
          statusGeral: statusGeral,
          materiaisCompletos: materiaisCompletos,
          materiaisParciais: materiaisParciais,
          materiaisNaoTransferidos: materiaisNaoTransferidos,
          resumo: {
            totalMateriais: materiaisNecessarios.length,
            totalTransferencias: transferencias.length,
            completos: materiaisCompletos.length,
            parciais: materiaisParciais.length,
            naoTransferidos: materiaisNaoTransferidos.length
          }
        };

        console.log('✅ Análise concluída, retornando resultado:', resultado);
        return resultado;

      } catch (error) {
        console.error('❌ Erro ao analisar transferências:', error);
        return null;
      }
    }

    // Função para mostrar materiais faltantes usando análise completa (mesma lógica do debug)
    async function mostrarMateriaisFaltantesComAnalise(opId, nomeOP) {
      try {
        console.log('🔍 Analisando materiais para OP:', opId);

        // Usar a mesma lógica do debug para analisar transferências
        const analiseTransferencia = await analisarTransferenciaOP(opId);
        console.log('📋 Resultado da análise:', analiseTransferencia);

        if (analiseTransferencia && analiseTransferencia.materiaisNaoTransferidos) {
          console.log('✅ Materiais não transferidos encontrados:', analiseTransferencia.materiaisNaoTransferidos.length);
          mostrarMateriaisFaltantesModalComDados(analiseTransferencia, nomeOP);
        } else {
          console.log('⚠️ Nenhum material não transferido encontrado, usando modal básico');
          // Fallback: mostrar modal com informação básica
          mostrarMateriaisFaltantesModalBasico(nomeOP);
        }
      } catch (error) {
        console.error('❌ Erro ao analisar materiais faltantes:', error);
        mostrarMateriaisFaltantesModalBasico(nomeOP);
      }
    }

    // Função para mostrar materiais faltantes em modal vermelho
    function mostrarMateriaisFaltantesModal(validacao, nomeOP) {
      const conteudo = document.getElementById('conteudoMateriaisFaltantes');

      // Debug: mostrar estrutura dos dados
      console.log('🔍 DEBUG - Dados da validação:', validacao);
      console.log('🔍 DEBUG - Nome da OP:', nomeOP);

      let html = `
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h3 style="color: #721c24; margin: 0 0 10px 0;">
            📋 OP: ${nomeOP}
          </h3>
          <p style="color: #721c24; margin: 0;">
            ⚠️ Os materiais abaixo estão com saldo insuficiente no armazém de produção:
          </p>
        </div>
      `;

      // Tentar diferentes propriedades para encontrar os materiais faltantes
      let materiaisFaltantes = [];

      if (validacao.materiaisFaltantes && validacao.materiaisFaltantes.length > 0) {
        materiaisFaltantes = validacao.materiaisFaltantes;
      } else if (validacao.materiais && validacao.materiais.length > 0) {
        materiaisFaltantes = validacao.materiais.filter(m => (m.saldoDisponivel || 0) < (m.quantidadeNecessaria || 0));
      } else if (validacao.detalhes && validacao.detalhes.length > 0) {
        materiaisFaltantes = validacao.detalhes;
      } else if (validacao.problemas && validacao.problemas.length > 0) {
        materiaisFaltantes = validacao.problemas;
      }

      console.log('🔍 DEBUG - Materiais faltantes encontrados:', materiaisFaltantes);

      if (materiaisFaltantes && materiaisFaltantes.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0; border-bottom: 1px solid #dc3545; padding-bottom: 8px;">
              ❌ MATERIAIS FALTANTES
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #dc3545; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Código</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Descrição</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Tipo</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Necessário</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Saldo Atual</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Faltante</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Ação Necessária</th>
                  </tr>
                </thead>
                <tbody>
        `;

        materiaisFaltantes.forEach((material, index) => {
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId;
          const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
          const tipo = ehSubproduto ? 'SUBPRODUTO (SP)' : 'MATÉRIA-PRIMA (MP)';
          const corLinha = index % 2 === 0 ? '#fff' : '#f8f9fa';
          const corTipo = ehSubproduto ? '#fd7e14' : '#28a745';

          const necessario = material.quantidadeNecessaria || 0;
          const saldoAtual = material.saldoDisponivel || 0;
          const faltante = necessario - saldoAtual;

          let acaoNecessaria = '';
          if (ehSubproduto) {
            acaoNecessaria = '🏭 Produzir/Apontar OP de SP';
          } else {
            acaoNecessaria = '📦 Transferir do estoque';
          }

          html += `
            <tr style="background: ${corLinha};">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; color: #495057;">
                ${produto?.descricao || produto?.nome || 'Descrição não disponível'}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                <span style="background: ${corTipo}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${tipo}
                </span>
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-weight: bold;">
                ${necessario.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${saldoAtual.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">
                ${faltante.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                ${acaoNecessaria}
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      } else {
        // Fallback: mostrar informações gerais da validação
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0;">⚠️ PROBLEMA DE SALDO DETECTADO</h4>
            <p style="color: #721c24; margin: 0 0 10px 0;">
              O sistema detectou que há problemas de saldo para esta OP, mas não conseguiu identificar os materiais específicos.
            </p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;">
              <strong>Dados da validação:</strong>
              <pre style="margin: 10px 0; font-size: 12px; overflow-x: auto;">${JSON.stringify(validacao, null, 2)}</pre>
            </div>
            <p style="color: #721c24; margin: 10px 0 0 0;">
              <strong>Ações sugeridas:</strong><br>
              • Verifique manualmente o estoque no armazém de produção<br>
              • Use o botão "Debug" na OP para análise detalhada<br>
              • Contate o suporte técnico se o problema persistir
            </p>
          </div>
        `;
      }

      // Resumo e ações
      const totalMateriais = materiaisFaltantes?.length || 0;
      const subprodutos = materiaisFaltantes?.filter(m => {
        const produto = produtos.find(p => p.id === m.produtoId);
        const codigo = produto?.codigo || m.produtoId;
        return codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
      }).length || 0;
      const materiasPrimas = totalMateriais - subprodutos;

      html += `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h4 style="color: #856404; margin: 0 0 10px 0;">📊 RESUMO</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${totalMateriais}</div>
              <div style="color: #856404;">Total de Materiais Faltantes</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #fd7e14;">${subprodutos}</div>
              <div style="color: #856404;">Subprodutos (SP)</div>
            </div>
            <div style="text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #28a745;">${materiasPrimas}</div>
              <div style="color: #856404;">Matérias-Primas (MP)</div>
            </div>
          </div>
        </div>

        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
          <h4 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES NECESSÁRIAS</h4>
          <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
      `;

      if (materiasPrimas > 0) {
        html += `<li><strong>Matérias-Primas:</strong> Transferir do estoque para o armazém de produção</li>`;
      }
      if (subprodutos > 0) {
        html += `<li><strong>Subprodutos:</strong> Verificar e apontar as OPs de SP correspondentes</li>`;
      }

      html += `
            <li><strong>Após resolver:</strong> Clique novamente em "Verificar Saldo" para validar</li>
          </ul>
        </div>
      `;

      conteudo.innerHTML = html;
      document.getElementById('modalMateriaisFaltantes').style.display = 'block';
    }

    // Função para mostrar materiais faltantes com dados da análise de transferência
    function mostrarMateriaisFaltantesModalComDados(analise, nomeOP) {
      console.log('🔴 Iniciando exibição do modal com dados:', analise);
      const conteudo = document.getElementById('conteudoMateriaisFaltantes');

      if (!conteudo) {
        console.error('❌ Elemento conteudoMateriaisFaltantes não encontrado!');
        return;
      }

      let html = `
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h3 style="color: #721c24; margin: 0 0 10px 0;">
            📋 OP: ${nomeOP}
          </h3>
          <p style="color: #721c24; margin: 0;">
            ⚠️ Os materiais abaixo não foram transferidos ou estão com saldo insuficiente:
          </p>
        </div>
      `;

      if (analise.materiaisNaoTransferidos && analise.materiaisNaoTransferidos.length > 0) {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0; border-bottom: 1px solid #dc3545; padding-bottom: 8px;">
              ❌ MATERIAIS NÃO TRANSFERIDOS
            </h4>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #dc3545; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Código</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Descrição</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Tipo</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Necessário</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Transferido</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #dc3545;">Faltante</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #dc3545;">Ação Necessária</th>
                  </tr>
                </thead>
                <tbody>
        `;

        analise.materiaisNaoTransferidos.forEach((material, index) => {
          // Buscar dados do produto
          const produto = produtos.find(p => p.id === material.produtoId);
          const codigo = produto?.codigo || material.produtoId || 'N/A';
          const ehSubproduto = codigo.includes('-SP') || codigo.includes('SP-') || produto?.tipo === 'SP';
          const tipo = ehSubproduto ? 'SUBPRODUTO (SP)' : 'MATÉRIA-PRIMA (MP)';
          const corLinha = index % 2 === 0 ? '#fff' : '#f8f9fa';
          const corTipo = ehSubproduto ? '#fd7e14' : '#28a745';

          const necessario = material.quantidadeNecessaria || 0;
          const transferido = material.quantidadeTransferida || 0;
          const faltante = necessario - transferido;

          let acaoNecessaria = '';
          if (ehSubproduto) {
            acaoNecessaria = '🏭 Produzir/Apontar OP de SP';
          } else {
            acaoNecessaria = '📦 Transferir do estoque';
          }

          html += `
            <tr style="background: ${corLinha};">
              <td style="padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-weight: bold;">
                ${codigo}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; color: #495057;">
                ${produto?.descricao || produto?.nome || 'Descrição não disponível'}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                <span style="background: ${corTipo}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                  ${tipo}
                </span>
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-weight: bold;">
                ${necessario.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                ${transferido.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545; font-weight: bold;">
                ${faltante.toFixed(3)}
              </td>
              <td style="padding: 10px; border: 1px solid #dee2e6;">
                ${acaoNecessaria}
              </td>
            </tr>
          `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;

        // Resumo
        const totalMateriais = analise.materiaisNaoTransferidos.length;
        const subprodutos = analise.materiaisNaoTransferidos.filter(m => {
          const codigo = m.codigo || m.produtoId || '';
          return codigo.includes('-SP') || codigo.includes('SP-') || m.tipo === 'SP';
        }).length;
        const materiasPrimas = totalMateriais - subprodutos;

        html += `
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">📊 RESUMO</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${totalMateriais}</div>
                <div style="color: #856404;">Total de Materiais Faltantes</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #fd7e14;">${subprodutos}</div>
                <div style="color: #856404;">Subprodutos (SP)</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">${materiasPrimas}</div>
                <div style="color: #856404;">Matérias-Primas (MP)</div>
              </div>
            </div>
          </div>

          <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
            <h4 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES NECESSÁRIAS</h4>
            <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
        `;

        if (materiasPrimas > 0) {
          html += `<li><strong>Matérias-Primas:</strong> Transferir do estoque para o armazém de produção</li>`;
        }
        if (subprodutos > 0) {
          html += `<li><strong>Subprodutos:</strong> Verificar e apontar as OPs de SP correspondentes</li>`;
        }

        html += `
              <li><strong>Após resolver:</strong> Clique novamente em "Verificar Saldo" para validar</li>
            </ul>
          </div>
        `;
      } else {
        html += `
          <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: #dc3545; margin: 0 0 15px 0;">⚠️ PROBLEMA DE SALDO DETECTADO</h4>
            <p style="color: #721c24;">
              O sistema detectou problemas de saldo, mas não conseguiu identificar materiais específicos.
              Use o botão "Debug" para análise detalhada.
            </p>
          </div>
        `;
      }

      conteudo.innerHTML = html;

      const modal = document.getElementById('modalMateriaisFaltantes');
      if (!modal) {
        console.error('❌ Modal modalMateriaisFaltantes não encontrado!');
        return;
      }

      modal.style.display = 'block';
      console.log('✅ Modal exibido com sucesso!');
    }

    // Função básica para casos onde não conseguimos obter dados detalhados
    function mostrarMateriaisFaltantesModalBasico(nomeOP) {
      const conteudo = document.getElementById('conteudoMateriaisFaltantes');

      const html = `
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <h3 style="color: #721c24; margin: 0 0 10px 0;">
            📋 OP: ${nomeOP}
          </h3>
          <p style="color: #721c24; margin: 0;">
            ⚠️ Saldo insuficiente detectado no armazém de produção
          </p>
        </div>

        <div style="background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h4 style="color: #dc3545; margin: 0 0 15px 0;">⚠️ SALDO INSUFICIENTE</h4>
          <p style="color: #721c24; margin: 0 0 15px 0;">
            O sistema detectou que há materiais com saldo insuficiente para esta OP.
          </p>
          <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
            <h5 style="color: #0c5460; margin: 0 0 10px 0;">🔧 AÇÕES RECOMENDADAS</h5>
            <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
              <li>Clique no botão <strong>"Debug"</strong> na OP para ver detalhes específicos</li>
              <li>Verifique se há <strong>subprodutos (SP)</strong> que precisam ser produzidos</li>
              <li>Confirme se as <strong>matérias-primas</strong> foram transferidas</li>
              <li>Após resolver, clique novamente em "Verificar Saldo"</li>
            </ul>
          </div>
        </div>
      `;

      conteudo.innerHTML = html;
      document.getElementById('modalMateriaisFaltantes').style.display = 'block';
    }

    window.exportarDiagnostico = function() {
      if (!window.diagnosticoAtual) return;

      const diagnostico = window.diagnosticoAtual;
      const resumo = diagnostico.resumoImpossibilidade;

      let texto = `DIAGNÓSTICO COMPLETO DE IMPOSSIBILIDADE - ${new Date().toLocaleString()}\n\n`;
      texto += `RESUMO:\n`;
      texto += `- Total de OPs: ${resumo.totalOPs}\n`;
      texto += `- OPs Viáveis: ${resumo.opsViaveis}\n`;
      texto += `- OPs Parciais: ${resumo.opsParciais}\n`;
      texto += `- OPs Impossíveis: ${resumo.opsInviaveis}\n`;
      texto += `- Confirmação de Impossibilidade: ${resumo.confirmacaoImpossibilidade ? 'SIM' : 'NÃO'}\n\n`;

      texto += `GARGALOS CRÍTICOS:\n`;
      diagnostico.gargalosIdentificados.forEach((gargalo, index) => {
        texto += `${index + 1}. ${gargalo.codigo} - Falta: ${gargalo.falta.toFixed(3)} ${gargalo.unidade} (${gargalo.opsAfetadas} OPs afetadas)\n`;
      });

      const blob = new Blob([texto], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `diagnostico-impossibilidade-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      window.URL.revokeObjectURL(url);

      mostrarNotificacao('📊 Diagnóstico exportado!', 'success', 2000);
    };

    window.executarSugestoes = function() {
      if (!window.diagnosticoAtual) return;

      const sugestoes = window.diagnosticoAtual.sugestoesDesbloqueio;
      if (sugestoes.length === 0) {
        alert('Nenhuma sugestão disponível.');
        return;
      }

      let mensagem = '💡 SUGESTÕES DE DESBLOQUEIO:\n\n';
      sugestoes.forEach((sugestao, index) => {
        mensagem += `${index + 1}. ${sugestao.titulo} (${sugestao.prioridade})\n`;
        mensagem += `   ${sugestao.descricao}\n\n`;
      });

      alert(mensagem);
    };

    // ===================================================================
    // ANÁLISE DE PRODUÇÃO VIÁVEL - PCP INTELIGENTE (BOTTOM-UP)
    // ===================================================================

    // Função para análise bottom-up (de baixo para cima)
    async function analisarProducaoBottomUp() {
      try {


        // 1. MAPEAR ESTRUTURAS POR NÍVEL
        const estruturasPorNivel = mapearEstruturasPorNivel();

        // 2. ANALISAR DISPONIBILIDADE POR NÍVEL (DO MAIS BAIXO PARA O MAIS ALTO)
        const disponibilidadePorNivel = {};
        const niveisOrdenados = Object.keys(estruturasPorNivel).sort((a, b) => b - a); // Maior para menor

        for (const nivel of niveisOrdenados) {

          disponibilidadePorNivel[nivel] = await analisarDisponibilidadeNivel(nivel, estruturasPorNivel[nivel], disponibilidadePorNivel);
        }

        // 3. IDENTIFICAR OPs VIÁVEIS BASEADO NA DISPONIBILIDADE REAL
        const opsViaveisBottomUp = identificarOPsViaveisBottomUp(disponibilidadePorNivel);

        return {
          estruturasPorNivel,
          disponibilidadePorNivel,
          opsViaveisBottomUp,
          resumo: gerarResumoBottomUp(disponibilidadePorNivel, opsViaveisBottomUp)
        };

      } catch (error) {
        console.error('❌ Erro na análise bottom-up:', error);
        throw error;
      }
    }

    // Mapear estruturas por nível hierárquico
    function mapearEstruturasPorNivel() {
      const estruturasPorNivel = {};

      // Analisar todas as estruturas para determinar níveis
      estruturas.forEach(estrutura => {
        const nivel = calcularNivelEstrutura(estrutura.produtoPaiId);

        if (!estruturasPorNivel[nivel]) {
          estruturasPorNivel[nivel] = [];
        }

        estruturasPorNivel[nivel].push({
          produtoId: estrutura.produtoPaiId,
          estrutura: estrutura,
          produto: produtos.find(p => p.id === estrutura.produtoPaiId)
        });
      });

      return estruturasPorNivel;
    }

    // Calcular nível hierárquico de um produto
    function calcularNivelEstrutura(produtoId, visitados = new Set()) {
      // Evitar loops infinitos
      if (visitados.has(produtoId)) {
        return 0;
      }
      visitados.add(produtoId);

      // Buscar estrutura do produto
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!estrutura || !estrutura.itens || estrutura.itens.length === 0) {
        // Produto sem estrutura = nível mais baixo (MP)
        return 0;
      }

      // Calcular nível máximo dos componentes + 1
      let nivelMaximo = 0;
      estrutura.itens.forEach(item => {
        const nivelComponente = calcularNivelEstrutura(item.produtoId, new Set(visitados));
        nivelMaximo = Math.max(nivelMaximo, nivelComponente);
      });

      return nivelMaximo + 1;
    }

    // Analisar disponibilidade de um nível específico
    async function analisarDisponibilidadeNivel(nivel, produtosNivel, disponibilidadeNiveisInferiores) {
      const disponibilidade = {};

      for (const item of produtosNivel) {
        const produtoId = item.produtoId;
        const estrutura = item.estrutura;

        // Calcular disponibilidade baseada em estoque + produção possível dos níveis inferiores
        const disponibilidadeTotal = calcularDisponibilidadeTotal(produtoId, estrutura, disponibilidadeNiveisInferiores);

        disponibilidade[produtoId] = {
          produto: item.produto,
          nivel: nivel,
          disponibilidadeEstoque: obterSaldoDisponivel(produtoId),
          disponibilidadeProducao: disponibilidadeTotal.producao,
          disponibilidadeTotal: disponibilidadeTotal.total,
          limitadoPor: disponibilidadeTotal.limitadoPor,
          detalhesComponentes: disponibilidadeTotal.detalhes
        };
      }

      return disponibilidade;
    }

    // Calcular disponibilidade total (estoque + produção possível)
    function calcularDisponibilidadeTotal(produtoId, estrutura, disponibilidadeNiveisInferiores) {
      // Saldo em estoque
      const saldoEstoque = obterSaldoDisponivel(produtoId);

      if (!estrutura || !estrutura.itens || estrutura.itens.length === 0) {
        // Produto sem estrutura (MP) - só tem estoque
        return {
          total: saldoEstoque,
          producao: 0,
          limitadoPor: saldoEstoque <= 0 ? 'SEM_ESTOQUE' : null,
          detalhes: []
        };
      }

      // Calcular quantas unidades podem ser produzidas baseado nos componentes
      let quantidadeMaximaProduzivel = Infinity;
      let limitadoPor = null;
      const detalhes = [];

      estrutura.itens.forEach(componente => {
        const componenteId = componente.produtoId;
        const quantidadeNecessaria = componente.quantidade || 1;

        // Disponibilidade do componente (estoque + produção de níveis inferiores)
        let disponibilidadeComponente = obterSaldoDisponivel(componenteId);

        // Se o componente tem disponibilidade de produção de níveis inferiores
        Object.values(disponibilidadeNiveisInferiores).forEach(nivelDisp => {
          if (nivelDisp[componenteId]) {
            disponibilidadeComponente += nivelDisp[componenteId].disponibilidadeTotal;
          }
        });

        // CORREÇÃO: Calcular quantas unidades do produto pai podem ser feitas com arredondamento inteligente
        const divisaoExata = disponibilidadeComponente / quantidadeNecessaria;
        const quantidadePossivel = arredondamentoInteligente(divisaoExata);

        if (quantidadePossivel < quantidadeMaximaProduzivel) {
          quantidadeMaximaProduzivel = quantidadePossivel;
          limitadoPor = componenteId;
        }

        detalhes.push({
          componenteId,
          produto: produtos.find(p => p.id === componenteId),
          necessario: quantidadeNecessaria,
          disponivel: disponibilidadeComponente,
          quantidadePossivel
        });
      });

      const producaoPossivel = quantidadeMaximaProduzivel === Infinity ? 0 : quantidadeMaximaProduzivel;

      return {
        total: saldoEstoque + producaoPossivel,
        producao: producaoPossivel,
        limitadoPor,
        detalhes
      };
    }

    // Obter saldo disponível de um produto
    function obterSaldoDisponivel(produtoId) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      if (!estoque) return 0;

      const saldoTotal = estoque.saldo || 0;
      const saldoReservado = estoque.saldoReservado || 0;
      const saldoEmpenhado = estoque.saldoEmpenhado || 0;

      return Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);
    }

    // Identificar OPs viáveis baseado na análise bottom-up
    function identificarOPsViaveisBottomUp(disponibilidadePorNivel) {
      const opsViaveisBottomUp = [];

      // Filtrar OPs pendentes
      const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');

      opsPendentes.forEach(op => {
        const produto = produtos.find(p => p.id === op.produtoId);
        if (!produto) return;

        // Verificar disponibilidade do produto final
        let disponibilidadeTotal = 0;
        Object.values(disponibilidadePorNivel).forEach(nivel => {
          if (nivel[op.produtoId]) {
            disponibilidadeTotal = nivel[op.produtoId].disponibilidadeTotal;
          }
        });

        // Se não encontrou na análise por níveis, verificar estoque direto
        if (disponibilidadeTotal === 0) {
          disponibilidadeTotal = obterSaldoDisponivel(op.produtoId);
        }

        const quantidadePossivel = Math.min(disponibilidadeTotal, op.quantidade);

        let status = 'INVIAVEL';
        if (quantidadePossivel >= op.quantidade) {
          status = 'VIAVEL';
        } else if (quantidadePossivel > 0) {
          status = 'PARCIAL';
        }

        opsViaveisBottomUp.push({
          op,
          produto,
          status,
          quantidadePossivel,
          disponibilidadeTotal,
          percentualViabilidade: (quantidadePossivel / op.quantidade * 100).toFixed(1),
          origemAnalise: 'BOTTOM_UP'
        });
      });

      return opsViaveisBottomUp;
    }

    // Gerar resumo da análise bottom-up
    function gerarResumoBottomUp(disponibilidadePorNivel, opsViaveisBottomUp) {
      const totalNiveis = Object.keys(disponibilidadePorNivel).length;
      const totalProdutos = Object.values(disponibilidadePorNivel).reduce((total, nivel) =>
        total + Object.keys(nivel).length, 0);

      const viaveisBottomUp = opsViaveisBottomUp.filter(op => op.status === 'VIAVEL');
      const parciaisBottomUp = opsViaveisBottomUp.filter(op => op.status === 'PARCIAL');
      const inviaveis = opsViaveisBottomUp.filter(op => op.status === 'INVIAVEL');

      return {
        totalNiveis,
        totalProdutos,
        totalOPs: opsViaveisBottomUp.length,
        viaveisBottomUp: viaveisBottomUp.length,
        parciaisBottomUp: parciaisBottomUp.length,
        inviaveis: inviaveis.length,
        percentualViabilidade: Math.round((viaveisBottomUp.length / opsViaveisBottomUp.length) * 100)
      };
    }

    // Função principal para analisar produção viável (HÍBRIDA: TOP-DOWN + BOTTOM-UP)
    window.analisarProducaoViavel = async function() {
      try {
        console.log('🎯 Iniciando análise híbrida de produção viável...');
        mostrarNotificacao('🔍 Analisando produção viável (método inteligente)...', 'info', 2000);

        // Filtrar apenas OPs Pendentes
        const opsPendentes = ordensProducao.filter(op => op.status === 'Pendente');

        if (opsPendentes.length === 0) {
          alert('ℹ️ Nenhuma OP pendente encontrada para análise.');
          return;
        }

        console.log(`📋 Analisando ${opsPendentes.length} OP(s) pendente(s)...`);

        // 1. ANÁLISE BOTTOM-UP (NOVA ABORDAGEM)
        const analiseBottomUp = await analisarProducaoBottomUp();

        // 2. ANÁLISE TOP-DOWN TRADICIONAL (para comparação)
        const opsParaAnalisar = opsPendentes.slice(0, 100);
        if (opsPendentes.length > 100) {
          console.warn(`⚠️ Limitando análise a 100 OPs (total: ${opsPendentes.length})`);
          mostrarNotificacao(`⚠️ Analisando apenas as primeiras 100 OPs de ${opsPendentes.length}`, 'warning', 3000);
        }

        const analises = [];
        for (const op of opsParaAnalisar) {
          try {
            const analise = await analisarViabilidadeOP(op);
            analises.push(analise);
          } catch (error) {
            console.error(`Erro ao analisar OP ${op.numero}:`, error);
          }
        }

        // 3. COMBINAR RESULTADOS (HÍBRIDO)
        const resultadoHibrido = combinarAnalises(analises, analiseBottomUp.opsViaveisBottomUp);

        // 4. Classificar resultados finais
        const podeProuzir = resultadoHibrido.filter(a => a && a.status === 'VIAVEL');
        const producaoParcial = resultadoHibrido.filter(a => a && a.status === 'PARCIAL');
        const naoPodeProuzir = resultadoHibrido.filter(a => a && a.status === 'INVIAVEL');



        // 5. Gerar relatório com informações bottom-up
        const relatorio = gerarRelatorioProducaoViavel(podeProuzir, producaoParcial, naoPodeProuzir);
        relatorio.analiseBottomUp = analiseBottomUp;
        relatorio.metodo = 'HIBRIDO';

        // Mostrar relatório
        mostrarRelatorioProducaoViavel(relatorio);

        console.log('✅ Análise híbrida de produção viável concluída');
        mostrarNotificacao('✅ Análise híbrida concluída!', 'success', 2000);

      } catch (error) {
        console.error('❌ Erro na análise de produção viável:', error);
        mostrarNotificacao('❌ Erro na análise de produção', 'error', 5000);
        alert('❌ Erro na análise de produção: ' + error.message);
      }
    };

    // Combinar análises top-down e bottom-up
    function combinarAnalises(analiseTopDown, analiseBottomUp) {
      const resultadoCombinado = [];

      // Criar mapa da análise bottom-up para consulta rápida
      const mapBottomUp = {};
      analiseBottomUp.forEach(item => {
        mapBottomUp[item.op.id] = item;
      });

      // Combinar resultados
      analiseTopDown.forEach(analiseTop => {
        const analiseBottom = mapBottomUp[analiseTop.op.id];

        if (analiseBottom) {
          // Usar o resultado mais otimista (bottom-up geralmente é mais preciso)
          const statusFinal = escolherMelhorStatus(analiseTop.status, analiseBottom.status);
          const quantidadeFinal = Math.max(analiseTop.quantidadePossivel || 0, analiseBottom.quantidadePossivel || 0);

          resultadoCombinado.push({
            ...analiseTop,
            status: statusFinal,
            quantidadePossivel: quantidadeFinal,
            analiseBottomUp: analiseBottom,
            metodoAnalise: 'HIBRIDO',
            observacoes: [
              ...analiseTop.observacoes || [],
              `Análise bottom-up: ${analiseBottom.percentualViabilidade}% viável`
            ]
          });
        } else {
          // Só análise top-down disponível
          resultadoCombinado.push({
            ...analiseTop,
            metodoAnalise: 'TOP_DOWN'
          });
        }
      });

      return resultadoCombinado;
    }

    // Escolher melhor status entre duas análises
    function escolherMelhorStatus(statusTop, statusBottom) {
      const prioridade = { 'VIAVEL': 3, 'PARCIAL': 2, 'INVIAVEL': 1 };

      const prioTop = prioridade[statusTop] || 1;
      const prioBottom = prioridade[statusBottom] || 1;

      return prioTop >= prioBottom ? statusTop : statusBottom;
    }

    // Analisar viabilidade de uma OP específica
    async function analisarViabilidadeOP(op) {
      try {
        const produto = produtos.find(p => p.id === op.produtoId) || {};
        const analise = {
          op: op,
          produto: produto,
          status: 'VIAVEL', // VIAVEL | PARCIAL | INVIAVEL
          quantidadePossivel: op.quantidade,
          materiaisProblema: [],
          prioridade: op.prioridade || 'NORMAL',
          dataEntrega: op.dataEntrega,
          observacoes: []
        };

        // Verificar cada material necessário
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          let menorQuantidadePossivel = op.quantidade;

          for (const material of op.materiaisNecessarios) {
            // Verificações de segurança
            if (!material || !material.produtoId || !material.quantidade) {
              console.warn(`Material inválido na OP ${op.numero}:`, material);
              continue;
            }
            // Buscar estoque do material
            const estoque = estoques.find(e =>
              e.produtoId === material.produtoId &&
              e.armazemId === (material.armazemId || op.armazemProducaoId)
            );

            if (!estoque) {
              analise.status = 'INVIAVEL';
              analise.quantidadePossivel = 0;
              analise.materiaisProblema.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: 0,
                problema: 'Estoque não encontrado'
              });
              continue;
            }

            // Calcular saldo disponível
            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;
            const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

            // Verificar disponibilidade
            if (saldoDisponivel < material.quantidade) {
              // CORREÇÃO: Calcular quantas unidades do produto podem ser feitas com arredondamento inteligente
              const divisaoExata = saldoDisponivel / (material.quantidade / op.quantidade);
              const quantidadePossivelComEsteMaterial = arredondamentoInteligente(divisaoExata);

              if (quantidadePossivelComEsteMaterial === 0) {
                analise.status = 'INVIAVEL';
                analise.quantidadePossivel = 0;
              } else {
                analise.status = 'PARCIAL';
                menorQuantidadePossivel = Math.min(menorQuantidadePossivel, quantidadePossivelComEsteMaterial);
              }

              analise.materiaisProblema.push({
                produtoId: material.produtoId,
                codigo: material.codigo || material.produtoId,
                necessario: material.quantidade,
                disponivel: saldoDisponivel,
                falta: material.quantidade - saldoDisponivel,
                problema: saldoDisponivel <= 0 ? 'Sem estoque' : 'Estoque insuficiente'
              });
            }

            // Alertas de estoque baixo
            const margemSeguranca = material.quantidade * 0.1; // 10% de margem
            if (saldoDisponivel >= material.quantidade && saldoDisponivel < (material.quantidade + margemSeguranca)) {
              analise.observacoes.push(`Material ${material.codigo || material.produtoId}: estoque baixo (margem < 10%)`);
            }
          }

          if (analise.status === 'PARCIAL') {
            analise.quantidadePossivel = menorQuantidadePossivel;
          }
        }

        return analise;

      } catch (error) {
        console.error(`Erro ao analisar OP ${op.id}:`, error);
        return {
          op: op,
          produto: produtos.find(p => p.id === op.produtoId) || {},
          status: 'ERRO',
          quantidadePossivel: 0,
          materiaisProblema: [],
          erro: error.message
        };
      }
    }

    // Gerar relatório de produção viável
    function gerarRelatorioProducaoViavel(viaveis, parciais, inviaveis) {
      const agora = new Date();
      const relatorio = {
        dataHora: agora.toLocaleString(),
        resumo: {
          totalOPs: viaveis.length + parciais.length + inviaveis.length,
          viaveis: viaveis.length,
          parciais: parciais.length,
          inviaveis: inviaveis.length,
          percentualViabilidade: Math.round((viaveis.length / (viaveis.length + parciais.length + inviaveis.length)) * 100)
        },
        viaveis: viaveis.sort((a, b) => {
          // Ordenar por prioridade e data de entrega
          const prioridadeOrder = { 'ALTA': 1, 'MÉDIA': 2, 'NORMAL': 3, 'BAIXA': 4 };
          const prioA = prioridadeOrder[a.prioridade] || 3;
          const prioB = prioridadeOrder[b.prioridade] || 3;

          if (prioA !== prioB) return prioA - prioB;

          // Se mesma prioridade, ordenar por data de entrega
          if (a.dataEntrega && b.dataEntrega) {
            return new Date(a.dataEntrega.seconds * 1000) - new Date(b.dataEntrega.seconds * 1000);
          }

          return 0;
        }),
        parciais: parciais.sort((a, b) => b.quantidadePossivel - a.quantidadePossivel),
        inviaveis: inviaveis,
        sugestoes: gerarSugestoesSequenciamento(viaveis, parciais, inviaveis)
      };

      return relatorio;
    }

    // Gerar sugestões de sequenciamento
    function gerarSugestoesSequenciamento(viaveis, parciais, inviaveis) {
      const sugestoes = [];

      // Verificações de segurança
      if (!viaveis) viaveis = [];
      if (!parciais) parciais = [];
      if (!inviaveis) inviaveis = [];

      // Sugestão 1: Priorizar por urgência e viabilidade
      if (viaveis.length > 0) {
        const maisUrgente = viaveis[0]; // Já ordenado por prioridade e data
        sugestoes.push({
          tipo: 'PRIORIDADE',
          titulo: 'Iniciar pela mais urgente',
          descricao: `Começar pela OP ${maisUrgente.op.numero} (${maisUrgente.produto.codigo}) - Prioridade ${maisUrgente.prioridade}`,
          acao: `iniciarProducao('${maisUrgente.op.id}')`
        });
      }

      // Sugestão 2: Agrupar por família de produtos
      const familias = {};
      viaveis.forEach(analise => {
        const familia = analise.produto.familia || 'GERAL';
        if (!familias[familia]) familias[familia] = [];
        familias[familia].push(analise);
      });

      Object.keys(familias).forEach(familia => {
        if (familias[familia].length > 1) {
          sugestoes.push({
            tipo: 'AGRUPAMENTO',
            titulo: `Agrupar família ${familia}`,
            descricao: `Produzir ${familias[familia].length} OPs da família ${familia} em sequência (reduz setup)`,
            ops: familias[familia].map(a => a.op.numero).join(', ')
          });
        }
      });

      // Sugestão 3: Produção parcial para liberar material
      if (parciais.length > 0) {
        const melhorParcial = parciais[0]; // Maior quantidade possível
        sugestoes.push({
          tipo: 'PARCIAL',
          titulo: 'Produção parcial estratégica',
          descricao: `Produzir ${melhorParcial.quantidadePossivel} de ${melhorParcial.op.quantidade} da OP ${melhorParcial.op.numero} para liberar material`,
          acao: `ajustarQuantidadeOP('${melhorParcial.op.id}', ${melhorParcial.quantidadePossivel})`
        });
      }

      // Sugestão 4: Compras urgentes
      const materiaisCriticos = {};
      [...parciais, ...inviaveis].forEach(analise => {
        analise.materiaisProblema.forEach(material => {
          if (!materiaisCriticos[material.codigo]) {
            materiaisCriticos[material.codigo] = {
              codigo: material.codigo,
              faltaTotal: 0,
              opsAfetadas: 0
            };
          }
          materiaisCriticos[material.codigo].faltaTotal += material.falta || 0;
          materiaisCriticos[material.codigo].opsAfetadas++;
        });
      });

      const materiaisOrdenados = Object.values(materiaisCriticos)
        .sort((a, b) => b.opsAfetadas - a.opsAfetadas)
        .slice(0, 3);

      if (materiaisOrdenados.length > 0) {
        sugestoes.push({
          tipo: 'COMPRA',
          titulo: 'Compras prioritárias',
          descricao: `Solicitar urgente: ${materiaisOrdenados.map(m => `${m.codigo} (${m.opsAfetadas} OPs afetadas)`).join(', ')}`,
          materiais: materiaisOrdenados
        });
      }

      return sugestoes;
    }

    // Mostrar relatório de produção viável
    function mostrarRelatorioProducaoViavel(relatorio) {
      // Salvar relatório globalmente para outras funções
      window.relatorioAtual = relatorio;

      // Gerar HTML visual para o modal
      let html = `
        <div style="padding: 20px;">
          <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-chart-line"></i> Resumo Executivo
              ${relatorio.metodo ? `<span style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 10px;">ANÁLISE ${relatorio.metodo}</span>` : ''}
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;">${relatorio.resumo.totalOPs}</div>
                <div style="font-size: 12px;">OPs Analisadas</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #90EE90;">${relatorio.resumo.viaveis}</div>
                <div style="font-size: 12px;">Viáveis (${relatorio.resumo.percentualViabilidade}%)</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFD700;">${relatorio.resumo.parciais}</div>
                <div style="font-size: 12px;">Parciais</div>
              </div>
              <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #FFB6C1;">${relatorio.resumo.inviaveis}</div>
                <div style="font-size: 12px;">Inviáveis</div>
              </div>
            </div>

            ${relatorio.analiseBottomUp ? `
              <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                <h6 style="margin: 0 0 8px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-layer-group"></i> Análise Hierárquica (Bottom-Up)
                </h6>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 12px;">
                  <div>📊 <strong>${relatorio.analiseBottomUp.resumo.totalNiveis}</strong> níveis estruturais</div>
                  <div>🏗️ <strong>${relatorio.analiseBottomUp.resumo.totalProdutos}</strong> produtos mapeados</div>
                  <div>🎯 <strong>${relatorio.analiseBottomUp.resumo.percentualViabilidade}%</strong> viabilidade bottom-up</div>
                </div>
              </div>
            ` : ''}
          </div>
      `;

      // OPs Viáveis
      if (relatorio.viaveis.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #28a745; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-check-circle"></i> Pode Produzir Agora (${relatorio.viaveis.length} OPs)
            </h4>
            <div style="background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; border-radius: 5px;">
              <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <thead>
                  <tr style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #1e7e34;">OP</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #1e7e34;">Produto</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Quantidade</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Prioridade</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Entrega</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #1e7e34;">Ação</th>
                  </tr>
                </thead>
                <tbody>
        `;

        relatorio.viaveis.forEach((analise, index) => {
          const dataEntrega = analise.dataEntrega ? new Date(analise.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem prazo';
          const prioridadeCor = {
            'ALTA': '#dc3545',
            'MÉDIA': '#ffc107',
            'NORMAL': '#6c757d',
            'BAIXA': '#28a745'
          };

          const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

          html += `
            <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
              <td style="padding: 12px; font-weight: bold; color: #495057;">${analise.op.numero}</td>
              <td style="padding: 12px;">
                <div style="font-weight: bold; color: #495057;">${analise.produto.codigo || 'N/A'}</div>
                <div style="font-size: 12px; color: #6c757d;">${analise.produto.descricao || 'Sem descrição'}</div>
              </td>
              <td style="padding: 12px; text-align: center;">
                <span style="font-weight: bold;">${analise.op.quantidade}</span>
                <br><small style="color: #6c757d;">${analise.produto.unidade || 'UN'}</small>
              </td>
              <td style="padding: 12px; text-align: center;">
                <span style="background: ${prioridadeCor[analise.prioridade] || '#6c757d'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                  ${analise.prioridade}
                </span>
              </td>
              <td style="padding: 12px; text-align: center; font-size: 12px;">${dataEntrega}</td>
              <td style="padding: 12px; text-align: center;">
                <button onclick="iniciarProducao('${analise.op.id}')" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 12px; font-weight: bold; transition: all 0.3s;">
                  🚀 Iniciar
                </button>
              </td>
            </tr>
          `;

          // Adicionar observações e informações de análise
          const temObservacoes = analise.observacoes && analise.observacoes.length > 0;
          const temAnaliseBottomUp = analise.analiseBottomUp;
          const metodoAnalise = analise.metodoAnalise;

          if (temObservacoes || temAnaliseBottomUp || metodoAnalise) {
            html += `
              <tr style="background: #f8f9fa; border-left: 4px solid #17a2b8;">
                <td colspan="6" style="padding: 8px 12px; font-size: 11px;">
            `;

            if (metodoAnalise) {
              const iconeMetodo = metodoAnalise === 'HIBRIDO' ? 'fas fa-exchange-alt' :
                                 metodoAnalise === 'BOTTOM_UP' ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
              html += `
                <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 8px; margin-right: 8px;">
                  <i class="${iconeMetodo}"></i> ${metodoAnalise}
                </span>
              `;
            }

            if (temAnaliseBottomUp) {
              html += `
                <span style="color: #17a2b8; margin-right: 8px;">
                  <i class="fas fa-layer-group"></i> Bottom-up: ${analise.analiseBottomUp.percentualViabilidade}% viável
                </span>
              `;
            }

            if (temObservacoes) {
              html += `
                <span style="color: #856404;">
                  <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                  <strong>Alertas:</strong> ${analise.observacoes.join(', ')}
                </span>
              `;
            }

            html += `
                </td>
              </tr>
            `;
          }
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
      }

      // OPs Parciais
      if (relatorio.parciais.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #ffc107; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-exclamation-triangle"></i> Produção Parcial Possível (${relatorio.parciais.length} OPs)
            </h4>
            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; border-radius: 5px;">
        `;

        relatorio.parciais.forEach((analise, index) => {
          const percentualPossivel = ((analise.quantidadePossivel / analise.op.quantidade) * 100).toFixed(1);

          html += `
            <div style="margin-bottom: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="background: linear-gradient(135deg, #ffc107, #e0a800); color: white; padding: 12px;">
                <h5 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                  <span>OP ${analise.op.numero} - ${analise.produto.codigo}</span>
                  <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                    ${percentualPossivel}% Viável
                  </span>
                </h5>
                <div style="font-size: 12px; margin-top: 5px;">
                  ${analise.produto.descricao || 'Sem descrição'} |
                  Pode produzir: <strong>${analise.quantidadePossivel}/${analise.op.quantidade} ${analise.produto.unidade || 'UN'}</strong>
                </div>
              </div>

              <div style="padding: 15px;">
                <h6 style="color: #dc3545; margin: 0 0 10px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-exclamation-circle"></i> Materiais em Falta
                </h6>
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Código</th>
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Descrição</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Necessário</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Disponível</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Falta</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Status</th>
                    </tr>
                  </thead>
                  <tbody>
          `;

          analise.materiaisProblema.forEach((material, matIndex) => {
            const produtoMaterial = produtos.find(p => p.id === material.produtoId) || {};
            const rowColor = matIndex % 2 === 0 ? '#ffffff' : '#f8f9fa';
            const statusColor = material.disponivel <= 0 ? '#dc3545' : '#ffc107';

            // Criar código mais legível
            const codigoLegivel = gerarCodigoLegivel(material.codigo, produtoMaterial);

            html += `
              <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
                <td style="padding: 8px; font-size: 12px;">
                  <div style="font-weight: bold; color: #007bff; cursor: pointer;"
                       onclick="mostrarDetalhesCodigoCriptico('${material.codigo}', '${material.produtoId}')"
                       title="Clique para ver detalhes completos">
                    ${codigoLegivel}
                  </div>
                  <div style="font-size: 10px; color: #6c757d; font-family: monospace; margin-top: 2px;">
                    ${material.codigo.length > 15 ? material.codigo.substring(0, 15) + '...' : material.codigo}
                  </div>
                  <button onclick="abrirDecodificadorComCodigo('${material.codigo}')"
                          style="background: #fd7e14; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 9px; cursor: pointer; margin-top: 2px;">
                    🔍 Decodificar
                  </button>
                </td>
                <td style="padding: 8px; font-size: 12px;">
                  <div style="font-weight: bold;">${produtoMaterial.descricao || 'Sem descrição'}</div>
                  ${produtoMaterial.familia ? `<div style="font-size: 10px; color: #6c757d;">Família: ${produtoMaterial.familia}</div>` : ''}
                  ${produtoMaterial.tipo ? `<div style="font-size: 10px; color: #6c757d;">Tipo: ${produtoMaterial.tipo}</div>` : ''}
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold;">${material.necessario?.toFixed(3) || 'N/A'}</span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: ${material.disponivel > 0 ? '#28a745' : '#dc3545'};">
                    ${material.disponivel?.toFixed(3) || '0.000'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                  ${material.quantidadeReservadaParaEstaOP > 0 ? `
                    <br><span style="background: #17a2b8; color: white; padding: 1px 4px; border-radius: 6px; font-size: 9px;">
                      📌 ${material.quantidadeReservadaParaEstaOP.toFixed(3)} reservado
                    </span>
                  ` : ''}
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: #dc3545;">
                    ${material.falta?.toFixed(3) || 'N/A'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center;">
                  <span style="background: ${statusColor}; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: bold;">
                    ${material.problema}
                  </span>
                  ${material.usandoReserva ? `
                    <br><small style="color: #17a2b8; font-weight: bold; font-size: 9px;">
                      ✅ Usando reserva
                    </small>
                  ` : ''}
                  <br><button onclick="corrigirEstoqueDireto('${material.produtoId}')"
                             style="background: #28a745; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 9px; cursor: pointer; margin-top: 2px;">
                    🔧 Corrigir
                  </button>
                </td>
              </tr>
            `;
          });

          html += `
                  </tbody>
                </table>
              </div>
            </div>
          `;
        });

        html += `</div></div>`;
      }

      // OPs Inviáveis
      if (relatorio.inviaveis.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #dc3545; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-times-circle"></i> Não Pode Produzir (${relatorio.inviaveis.length} OPs)
            </h4>
            <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: 5px;">
        `;

        relatorio.inviaveis.forEach((analise, index) => {
          html += `
            <div style="margin-bottom: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 12px;">
                <h5 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                  <span>OP ${analise.op.numero} - ${analise.produto.codigo}</span>
                  <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                    <i class="fas fa-ban"></i> Bloqueada
                  </span>
                </h5>
                <div style="font-size: 12px; margin-top: 5px;">
                  ${analise.produto.descricao || 'Sem descrição'} |
                  Quantidade: <strong>${analise.op.quantidade} ${analise.produto.unidade || 'UN'}</strong>
                </div>
              </div>

              <div style="padding: 15px;">
                <h6 style="color: #dc3545; margin: 0 0 10px 0; display: flex; align-items: center; gap: 5px;">
                  <i class="fas fa-exclamation-circle"></i> Materiais Críticos em Falta
                </h6>
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Código</th>
                      <th style="padding: 8px; text-align: left; font-size: 12px; color: #495057;">Descrição</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Necessário</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Disponível</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Falta</th>
                      <th style="padding: 8px; text-align: center; font-size: 12px; color: #495057;">Criticidade</th>
                    </tr>
                  </thead>
                  <tbody>
          `;

          analise.materiaisProblema.forEach((material, matIndex) => {
            const produtoMaterial = produtos.find(p => p.id === material.produtoId) || {};
            const rowColor = matIndex % 2 === 0 ? '#ffffff' : '#f8f9fa';

            // Calcular criticidade baseada na falta
            let criticidade = 'BAIXA';
            let criticidadeCor = '#ffc107';
            if (material.disponivel <= 0) {
              criticidade = 'CRÍTICA';
              criticidadeCor = '#dc3545';
            } else if (material.falta > (material.necessario * 0.5)) {
              criticidade = 'ALTA';
              criticidadeCor = '#fd7e14';
            }

            html += `
              <tr style="background: ${rowColor}; border-bottom: 1px solid #dee2e6;">
                <td style="padding: 8px; font-weight: bold; font-size: 12px;">${material.codigo}</td>
                <td style="padding: 8px; font-size: 12px;">${produtoMaterial.descricao || 'Sem descrição'}</td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold;">${material.necessario?.toFixed(3) || 'N/A'}</span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: ${material.disponivel > 0 ? '#28a745' : '#dc3545'};">
                    ${material.disponivel?.toFixed(3) || '0.000'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center; font-size: 12px;">
                  <span style="font-weight: bold; color: #dc3545; font-size: 14px;">
                    ${material.falta?.toFixed(3) || 'N/A'}
                  </span>
                  <br><small style="color: #6c757d;">${produtoMaterial.unidade || 'UN'}</small>
                </td>
                <td style="padding: 8px; text-align: center;">
                  <span style="background: ${criticidadeCor}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px; font-weight: bold;">
                    ${criticidade}
                  </span>
                  <br><small style="color: #6c757d; font-size: 10px;">${material.problema}</small>
                </td>
              </tr>
            `;
          });

          html += `
                  </tbody>
                </table>

                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                  <h6 style="margin: 0 0 5px 0; color: #856404; display: flex; align-items: center; gap: 5px;">
                    <i class="fas fa-lightbulb"></i> Ações Recomendadas
                  </h6>
                  <ul style="margin: 5px 0; padding-left: 20px; font-size: 12px; color: #856404;">
                    <li>Solicitar compra urgente dos materiais críticos</li>
                    <li>Verificar transferências entre armazéns</li>
                    <li>Considerar fornecedores alternativos</li>
                    <li>Revisar prioridade da OP conforme necessidade</li>
                  </ul>
                </div>
              </div>
            </div>
          `;
        });

        html += `</div></div>`;
      }

      // Sugestões
      if (relatorio.sugestoes.length > 0) {
        html += `
          <div style="margin-bottom: 20px;">
            <h4 style="color: #6f42c1; display: flex; align-items: center; gap: 10px;">
              <i class="fas fa-lightbulb"></i> Sugestões Inteligentes de PCP
            </h4>
            <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-left: 4px solid #6f42c1; padding: 15px; border-radius: 5px;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
        `;

        relatorio.sugestoes.forEach((sugestao, index) => {
          const icones = {
            'PRIORIDADE': 'fas fa-flag',
            'AGRUPAMENTO': 'fas fa-layer-group',
            'PARCIAL': 'fas fa-cut',
            'COMPRA': 'fas fa-shopping-cart'
          };

          const cores = {
            'PRIORIDADE': '#dc3545',
            'AGRUPAMENTO': '#28a745',
            'PARCIAL': '#ffc107',
            'COMPRA': '#17a2b8'
          };

          const icone = icones[sugestao.tipo] || 'fas fa-lightbulb';
          const cor = cores[sugestao.tipo] || '#6f42c1';

          html += `
            <div style="background: white; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-top: 4px solid ${cor};">
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <div style="background: ${cor}; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <i class="${icone}"></i>
                </div>
                <div>
                  <h6 style="margin: 0; color: ${cor}; font-weight: bold;">${sugestao.titulo}</h6>
                  <small style="color: #6c757d; text-transform: uppercase; font-weight: bold;">${sugestao.tipo}</small>
                </div>
              </div>
              <p style="margin: 0; font-size: 13px; color: #495057; line-height: 1.4;">
                ${sugestao.descricao}
              </p>
          `;

          // Adicionar informações específicas por tipo
          if (sugestao.tipo === 'COMPRA' && sugestao.materiais) {
            html += `
              <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <small style="font-weight: bold; color: #495057;">Materiais prioritários:</small>
                <ul style="margin: 5px 0; padding-left: 15px; font-size: 12px;">
            `;
            sugestao.materiais.forEach(material => {
              html += `<li>${material.codigo} - ${material.opsAfetadas} OP(s) afetada(s)</li>`;
            });
            html += `</ul></div>`;
          }

          if (sugestao.tipo === 'AGRUPAMENTO' && sugestao.ops) {
            html += `
              <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <small style="font-weight: bold; color: #495057;">OPs envolvidas:</small>
                <div style="font-size: 12px; margin-top: 3px;">${sugestao.ops}</div>
              </div>
            `;
          }

          html += `</div>`;
        });

        html += `
              </div>
            </div>
          </div>
        `;
      }

      html += `</div>`;

      // Mostrar no modal
      document.getElementById('conteudoAnaliseProducao').innerHTML = html;
      document.getElementById('modalAnaliseProducao').style.display = 'block';


    }

    // Funções do modal
    window.fecharModalAnalise = function() {
      document.getElementById('modalAnaliseProducao').style.display = 'none';
    };

    window.exportarRelatorio = function() {
      if (!window.relatorioAtual) return;

      // Gerar texto para exportação
      let texto = `ANÁLISE DE PRODUÇÃO VIÁVEL - ${window.relatorioAtual.dataHora}\n\n`;
      texto += `RESUMO: ${window.relatorioAtual.resumo.viaveis} viáveis, ${window.relatorioAtual.resumo.parciais} parciais, ${window.relatorioAtual.resumo.inviaveis} inviáveis\n\n`;

      // Criar e baixar arquivo
      const blob = new Blob([texto], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analise-producao-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      window.URL.revokeObjectURL(url);

      mostrarNotificacao('📊 Relatório exportado!', 'success', 2000);
    };

    window.iniciarProducaoLote = async function() {
      if (!window.relatorioAtual || window.relatorioAtual.viaveis.length === 0) {
        alert('Nenhuma OP viável para iniciar em lote.');
        return;
      }

      if (!confirm(`🚀 Iniciar produção de ${window.relatorioAtual.viaveis.length} OP(s) viáveis?\n\nIsso irá transferir todas as reservas para empenhos.`)) {
        return;
      }

      let sucessos = 0;
      let erros = 0;

      for (const analise of window.relatorioAtual.viaveis) {
        try {
          await EmpenhoService.transferirReservasParaEmpenhos(analise.op.id);
          sucessos++;
        } catch (error) {
          console.error(`Erro ao iniciar OP ${analise.op.numero}:`, error);
          erros++;
        }
      }

      mostrarNotificacao(`✅ Lote iniciado: ${sucessos} sucessos, ${erros} erros`, sucessos > 0 ? 'success' : 'error', 5000);
      fecharModalAnalise();
      debounceUpdate('iniciarLote');
    };

    // ===================================================================
    // FUNÇÕES DE EMPENHOS
    // ===================================================================

    // Função para iniciar produção (transferir reservas para empenhos)
    window.iniciarProducao = async function(opId) {
      try {
        console.log(`🔍 Validando estoque para OP: ${opId}`);

        // 1. VALIDAR ESTOQUE ANTES DE INICIAR
        const validacao = await validarEstoqueParaProducao(opId);
        const ordem = ordensProducao.find(op => op.id === opId);
        const nomeOP = ordem ? (ordem.numero || opId) : opId;

        if (!validacao.podeProuzir) {
          // Mostrar detalhes dos materiais em falta
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          alert(detalhes);

          mostrarNotificacao('❌ Não é possível iniciar produção - Materiais insuficientes', 'error', 8000);
          return;
        }

        // 2. MOSTRAR ALERTAS SE HOUVER
        if (validacao.alertas.length > 0) {
          const detalhes = mostrarDetalhesValidacao(validacao, nomeOP);
          if (!confirm(`${detalhes}\n⚠️ Há alertas de estoque baixo. Deseja continuar mesmo assim?`)) {
            return;
          }
        }

        // 3. CONFIRMAR INÍCIO DA PRODUÇÃO
        const confirmacao = validacao.alertas.length > 0
          ? '🚀 Iniciar produção desta OP?\n\n⚠️ Atenção aos alertas de estoque baixo mostrados acima.'
          : '🚀 Iniciar produção desta OP?\n\n✅ Todos os materiais estão disponíveis.\nIsso irá transferir as reservas para empenhos.';

        if (!confirm(confirmacao)) {
          return;
        }

        console.log(`🚀 Iniciando produção da OP: ${opId}`);

        // 4. TRANSFERIR RESERVAS PARA EMPENHOS
        const resultado = await EmpenhoService.transferirReservasParaEmpenhos(opId);

        if (resultado.erros.length > 0) {
          console.warn('⚠️ Erros durante transferência:', resultado.erros);
          mostrarNotificacao(`⚠️ Produção iniciada com ${resultado.erros.length} erro(s)`, 'warning', 5000);
          alert(`⚠️ Produção iniciada com ${resultado.erros.length} erro(s):\n${resultado.erros.join('\n')}`);
        } else {
          mostrarNotificacao(`✅ Produção iniciada! ${resultado.transferencias} material(is) empenhado(s)`, 'success', 3000);
          alert(`✅ Produção iniciada com sucesso!\n${resultado.transferencias} material(is) empenhado(s).`);
        }

        // 5. ATUALIZAR INTERFACE (não precisa reconfigurar listeners)
        debounceUpdate('iniciarProducao');
      } catch (error) {
        console.error("❌ Erro ao iniciar produção:", error);
        mostrarNotificacao('❌ Erro ao iniciar produção', 'error', 5000);
        alert("❌ Erro ao iniciar produção: " + error.message);
      }
    };

    // Função para consultar empenhos de uma OP
    window.consultarEmpenhosOP = async function(opId) {
      try {
        const empenhosOP = await EmpenhoService.consultarEmpenhosOP(opId);

        if (empenhosOP.length === 0) {
          alert(`ℹ️ Nenhum empenho encontrado para a OP: ${opId}`);
          return;
        }

        let detalhes = `📋 EMPENHOS DA OP: ${opId}\n\n`;

        empenhosOP.forEach(empenho => {
          const produto = produtos.find(p => p.id === empenho.produtoId);
          const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

          detalhes += `🔹 Produto: ${produto ? produto.codigo + ' - ' + produto.descricao : empenho.produtoId}\n`;
          detalhes += `   Status: ${empenho.status}\n`;
          detalhes += `   Empenhado: ${empenho.quantidadeEmpenhada.toFixed(3)}\n`;
          detalhes += `   Consumido: ${empenho.quantidadeConsumida.toFixed(3)}\n`;
          detalhes += `   Restante: ${quantidadeRestante.toFixed(3)}\n`;
          detalhes += `   Data: ${empenho.dataEmpenho ? new Date(empenho.dataEmpenho.seconds * 1000).toLocaleDateString() : 'N/A'}\n\n`;
        });

        alert(detalhes);
      } catch (error) {
        console.error('❌ Erro ao consultar empenhos:', error);
        mostrarNotificacao('❌ Erro ao consultar empenhos', 'error', 5000);
        alert('❌ Erro ao consultar empenhos: ' + error.message);
      }
    };

    // Função para abrir painel de empenhos
    window.abrirPainelEmpenhos = function() {
      window.open('painel_empenhos.html', '_blank');
    };

    /**
     * Diagnóstica especificamente o material 100231 na OP atual
     */
    window.debugMaterial100231 = function() {
        if (!currentOrder) {
            console.error('❌ Nenhuma OP selecionada');
            return;
        }

        const material100231 = currentOrder.materiaisNecessarios?.find(m => {
            const produto = produtos.find(p => p.id === m.produtoId);
            return produto?.codigo === '100231';
        });

        if (!material100231) {
            console.error('❌ Material 100231 não encontrado na OP atual');
            return;
        }

        const produto = produtos.find(p => p.id === material100231.produtoId);
        const armazemProducao = armazens.find(a => a.id === currentOrder.armazemProducaoId);

        console.log('🔍 DEBUG MATERIAL 100231');
        console.log('📋 OP:', currentOrder.numero);
        console.log('🏭 Armazém Produção:', armazemProducao?.codigo, '-', armazemProducao?.nome);
        console.log('📦 Material:', produto?.codigo, '-', produto?.descricao);
        console.log('🆔 ProdutoId:', material100231.produtoId);
        console.log('🆔 ArmazemId:', currentOrder.armazemProducaoId);

        // Buscar TODOS os estoques deste produto
        const todosEstoques = estoques.filter(e => e.produtoId === material100231.produtoId);
        console.log(`📊 Total de estoques encontrados: ${todosEstoques.length}`);

        todosEstoques.forEach((estoque, index) => {
            const armazem = armazens.find(a => a.id === estoque.armazemId);
            const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, currentOrder?.id);

            console.log(`\n📦 Estoque ${index + 1}:`);
            console.log(`   🏭 Armazém: ${armazem?.codigo || estoque.armazemId} (${armazem?.nome || 'N/A'})`);
            console.log(`   💰 Saldo: ${(estoque.saldo || 0).toFixed(3)}`);
            console.log(`   🔒 Reservado: ${(estoque.saldoReservado || 0).toFixed(3)}`);
            console.log(`   ⚡ Empenhado: ${(estoque.saldoEmpenhado || 0).toFixed(3)}`);
            console.log(`   ✅ Disponível: ${saldoDisponivel.toFixed(3)}`);
            console.log(`   🎯 É o armazém da OP? ${estoque.armazemId === currentOrder.armazemProducaoId ? 'SIM' : 'NÃO'}`);
        });

        // Buscar estoque específico no armazém de produção
        const estoqueProducao = estoques.find(e =>
            e.produtoId === material100231.produtoId &&
            e.armazemId === currentOrder.armazemProducaoId
        );

        console.log('\n🎯 ESTOQUE NO ARMAZÉM DE PRODUÇÃO:');
        if (estoqueProducao) {
            const saldoDisponivel = calcularSaldoDisponivel(estoqueProducao);
            console.log(`   ✅ Encontrado!`);
            console.log(`   💰 Saldo: ${(estoqueProducao.saldo || 0).toFixed(3)}`);
            console.log(`   🔒 Reservado: ${(estoqueProducao.saldoReservado || 0).toFixed(3)}`);
            console.log(`   ⚡ Empenhado: ${(estoqueProducao.saldoEmpenhado || 0).toFixed(3)}`);
            console.log(`   ✅ Disponível: ${saldoDisponivel.toFixed(3)}`);
        } else {
            console.log(`   ❌ NÃO ENCONTRADO no armazém ${currentOrder.armazemProducaoId}`);
        }

        // Verificar se a função calcularSaldoDisponivel existe
        console.log('\n🔧 VERIFICAÇÃO DE FUNÇÃO:');
        console.log(`   calcularSaldoDisponivel existe? ${typeof calcularSaldoDisponivel === 'function' ? 'SIM' : 'NÃO'}`);
    };

    // Adicionar apenas botão para análises (redireciona para tela específica)
    document.addEventListener('DOMContentLoaded', function() {
      // 🔧 INICIALIZAR SISTEMA DE FILTROS PRIMEIRO
      inicializarSistemaFiltros();

      // ===== BOTÕES DO HEADER REMOVIDOS =====
      // Todos os botões coloridos foram removidos para interface mais limpa
      const header = document.querySelector('.header');
      if (header) {

        // Indicador de status da conexão
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'connectionStatus';
        statusIndicator.innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> Online';
        statusIndicator.style.cssText = `
          display: inline-flex;
          align-items: center;
          gap: 5px;
          margin-left: 15px;
          font-size: 12px;
          color: #666;
        `;
        header.appendChild(statusIndicator);
      }

      // Atalho de teclado para atualização (Ctrl+R ou F5)
      document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
          e.preventDefault();
          forcarAtualizacao();
        }
      });
    });

    // ===================================================================
    // FUNÇÃO PARA DIVISÃO DE OP
    // ===================================================================

    /**
     * Abre o sistema de divisão de OP para produção parcial
     */
    window.abrirDivisaoOP = function() {
      // Verificar se há OPs selecionadas ou em contexto
      const opSelecionada = obterOPSelecionada();

      if (opSelecionada) {
        // Se há uma OP em contexto, abrir diretamente com ela
        const url = `divisao_op_parcial.html?op=${encodeURIComponent(opSelecionada.numero)}`;
        window.open(url, '_blank');
        mostrarNotificacao(`🔄 Abrindo divisão para OP ${opSelecionada.numero}`, 'info', 3000);
      } else {
        // Abrir sem OP específica - usuário escolherá lá
        window.open('divisao_op_parcial.html', '_blank');
        mostrarNotificacao('🔄 Abrindo sistema de divisão de OP', 'info', 3000);
      }
    };

    /**
     * Tenta obter uma OP que esteja em contexto (modal aberto, última visualizada, etc.)
     */
    function obterOPSelecionada() {
      // 1. Verificar se há modal de apontamento aberto
      const modalApontamento = document.getElementById('appointmentModal');
      if (modalApontamento && modalApontamento.style.display !== 'none') {
        const opIdModal = modalApontamento.getAttribute('data-op-id');
        if (opIdModal) {
          const op = ordensProducao.find(o => o.id === opIdModal);
          if (op) return op;
        }
      }

      // 2. Verificar se há modal de materiais aberto
      const modalMateriais = document.getElementById('modalMateriaisFaltantes');
      if (modalMateriais && modalMateriais.style.display !== 'none') {
        const opIdModal = modalMateriais.getAttribute('data-op-id');
        if (opIdModal) {
          const op = ordensProducao.find(o => o.id === opIdModal);
          if (op) return op;
        }
      }

      // 3. Se não encontrou nenhuma, retornar null
      return null;
    }
  </script>
</body>
</html>