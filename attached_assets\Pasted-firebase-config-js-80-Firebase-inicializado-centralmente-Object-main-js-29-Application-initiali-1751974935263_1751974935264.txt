firebase-config.js:80 🔥 Firebase inicializado centralmente: Object
main.js:29 Application initialized successfully
movimentacao_armazem.html:2045 🚀 Iniciando webhook de sincronização automática...
movimentacao_armazem.html:2092 ✅ Webhook de sincronização ativo!
movimentacao_armazem.html:2084 ❌ Erro no webhook de sincronização: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=Cldwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL3RyYW5zZmVyZW5jaWFzQXJtYXplbS9pbmRleGVzL18QARoKCgZzdGF0dXMQARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
(anonymous) @ movimentacao_armazem.html:2084
movimentacao_armazem.html:5715 Carregando histórico de transferências de produção...
movimentacao_armazem.html:5743 ✅ 279 transferências de produção carregadas
movimentacao_armazem.html:2087 🔄 Tentando reconectar webhook...
movimentacao_armazem.html:2045 🚀 Iniciando webhook de sincronização automática...
movimentacao_armazem.html:2092 ✅ Webhook de sincronização ativo!
movimentacao_armazem.html:2084 ❌ Erro no webhook de sincronização: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=Cldwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL3RyYW5zZmVyZW5jaWFzQXJtYXplbS9pbmRleGVzL18QARoKCgZzdGF0dXMQARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
(anonymous) @ movimentacao_armazem.html:2084
(anonymous) @ bundle_reader_impl.ts:43
setTimeout
Oa @ bundle_reader_impl.ts:32
error @ async_observer.ts:48
onError @ event_manager.ts:345
__PRIVATE_eventManagerOnWatchError @ event_manager.ts:238
__PRIVATE_removeAndCleanupTarget @ sync_engine_impl.ts:875
(anonymous) @ sync_engine_impl.ts:666
Promise.then
__PRIVATE_syncEngineRejectListen @ sync_engine_impl.ts:665
__PRIVATE_handleTargetError @ remote_store.ts:648
__PRIVATE_onWatchStreamChange @ remote_store.ts:59
onMessage @ persistent_stream.ts:655
(anonymous) @ persistent_stream.ts:529
(anonymous) @ persistent_stream.ts:574
(anonymous) @ async_queue_impl.ts:137
(anonymous) @ async_queue_impl.ts:327
Promise.then
ou @ async_queue_impl.ts:188
enqueue @ async_queue_impl.ts:135
enqueueAndForget @ async_queue_impl.ts:96
(anonymous) @ persistent_stream.ts:572
(anonymous) @ persistent_stream.ts:527
Vo @ webchannel_connection.ts:56
(anonymous) @ webchannel_connection.ts:397
(anonymous) @ webchannel_connection.ts:298
ib @ eventtarget.js:91
C$1 @ eventtarget.js:490
R.Aa @ webchannelbasetransport.js:429
qc @ structs.js:194
rc @ channelrequest.js:950
m.Pa @ channelrequest.js:626
m.nb @ channelrequest.js:656
ib @ eventtarget.js:91
C$1 @ eventtarget.js:490
Ad @ xhrio.js:922
m.kb @ xhrio.js:1052
m.La @ xhrio.js:1050
od @ fetchxmlhttpfactory.js:237
m.Xa @ fetchxmlhttpfactory.js:419
