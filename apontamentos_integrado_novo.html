<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Apontamentos Integrados - Sistema ERP</title>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Clear any cached Firebase data first
        console.log('🧹 Limpando cache Firebase...');

        // Clear localStorage Firebase data
        Object.keys(localStorage).forEach(key => {
            if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                localStorage.removeItem(key);
                console.log('🗑️ Removido do localStorage:', key);
            }
        });

        // Clear sessionStorage Firebase data
        Object.keys(sessionStorage).forEach(key => {
            if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                sessionStorage.removeItem(key);
                console.log('🗑️ Removido do sessionStorage:', key);
            }
        });

        // Import Firebase normally (without cache busting for file:// protocol)
        import { db } from './firebase-config.js';
        import { collection, doc, getDocs, getDoc, addDoc, updateDoc, deleteDoc, query, where, orderBy, limit, Timestamp, runTransaction, onSnapshot } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.doc = doc;
        window.getDocs = getDocs;
        window.getDoc = getDoc;
        window.addDoc = addDoc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.limit = limit;
        window.Timestamp = Timestamp;
        window.runTransaction = runTransaction;
        window.onSnapshot = onSnapshot;

        // Log Firebase initialization
        console.log('🔥 Firebase imports carregados');
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 25px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .integration-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .integration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .sync-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
        }

        .sync-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .sync-status.syncing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .action-card .icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #3498db;
        }

        .action-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .action-card p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .ops-grid {
            display: block;
            width: 100%;
            min-height: 400px;
        }

        /* Quando for cards, usar grid */
        .ops-grid.cards-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        /* Quando for lista, usar block */
        .ops-grid.lista-view {
            display: block;
            width: 100%;
        }

        .op-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .op-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .op-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .op-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .op-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-planejada { background: #e3f2fd; color: #1976d2; }
        .status-liberada { background: #f3e5f5; color: #7b1fa2; }
        .status-producao { background: #fff3e0; color: #f57c00; }
        .status-concluida { background: #e8f5e8; color: #388e3c; }

        .material-status {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }

        .material-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-info { background: #d1ecf1; color: #0c5460; }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ecf0f1;
            z-index: 1;
        }

        .step {
            background: white;
            border: 3px solid #ecf0f1;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8rem;
            z-index: 2;
            position: relative;
        }

        .step.completed {
            background: #27ae60;
            border-color: #27ae60;
            color: white;
        }

        .step.current {
            background: #3498db;
            border-color: #3498db;
            color: white;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #ecf0f1;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: #27ae60; }
        .notification.error { background: #e74c3c; }
        .notification.warning { background: #f39c12; }
        .notification.info { background: #3498db; }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Estilos para Modais */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-body {
            padding: 25px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        .material-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .material-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .material-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-ok { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }

        /* Estilos para Cards de OP */
        .op-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #3498db;
        }

        .op-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .op-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .op-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .op-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente { background: #fff3cd; color: #856404; }
        .status-em-produção { background: #d1ecf1; color: #0c5460; }
        .status-concluída { background: #d4edda; color: #155724; }
        .status-cancelada { background: #f8d7da; color: #721c24; }

        .material-status {
            margin: 15px 0;
        }

        .material-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-right: 5px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .step {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: help;
        }

        .step.completed {
            background: #27ae60;
            color: white;
        }

        .step.pending {
            background: #f39c12;
            color: white;
        }

        .step.disabled {
            background: #bdc3c7;
            color: #7f8c8d;
        }

        .next-action {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 10px 0;
            font-size: 13px;
            color: #0c5460;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .action-buttons .btn {
            flex: 1;
            min-width: 80px;
            font-size: 12px;
            padding: 8px 12px;
        }

        /* Estilos para tabela - VERSÃO EXPANDIDA */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            width: 100%;
            min-height: 400px;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            table-layout: fixed;
        }

        .orders-table th, .orders-table td {
            padding: 12px 10px;
            border: 1px solid #e9ecef;
            text-align: left;
            vertical-align: middle;
        }

        .orders-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            font-weight: 600;
            font-size: 15px;
            border-bottom: 3px solid #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .orders-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e9ecef;
            font-size: 13px;
            line-height: 1.4;
        }

        .orders-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.005);
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Larguras específicas das colunas */
        .orders-table th:nth-child(1), .orders-table td:nth-child(1) { width: 12%; } /* Ordem */
        .orders-table th:nth-child(2), .orders-table td:nth-child(2) { width: 25%; } /* Produto */
        .orders-table th:nth-child(3), .orders-table td:nth-child(3) { width: 12%; } /* Quantidade */
        .orders-table th:nth-child(4), .orders-table td:nth-child(4) { width: 15%; } /* Produzido */
        .orders-table th:nth-child(5), .orders-table td:nth-child(5) { width: 18%; } /* Status */
        .orders-table th:nth-child(6), .orders-table td:nth-child(6) { width: 12%; } /* Data Entrega */
        .orders-table th:nth-child(7), .orders-table td:nth-child(7) { width: 16%; } /* Ações */

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 5px;
        }

        .status-pendente { background: #ffc107; color: #212529; }
        .status-em-andamento { background: #007bff; color: white; }
        .status-concluída { background: #28a745; color: white; }
        .status-cancelada { background: #dc3545; color: white; }

        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 8px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
            border-radius: 5px;
        }

        .flag-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 4px;
        }

        .flag-ok { background: #28a745; }
        .flag-warning { background: #ffc107; }
        .flag-error { background: #dc3545; }
        .flag-info { background: #17a2b8; }
        .flag-disabled { background: #6c757d; }

        .btn-xs {
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .ops-grid.cards-view {
                grid-template-columns: 1fr;
            }

            .table-container {
                margin: 10px 0;
                border-radius: 8px;
            }

            .orders-table th, .orders-table td {
                padding: 8px 6px;
                font-size: 12px;
            }

            .orders-table th {
                padding: 12px 8px;
                font-size: 13px;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .modal-body {
                padding: 15px;
            }

            .btn-group {
                flex-direction: column;
            }

            .ops-lista {
                font-size: 10px;
            }

            .ops-lista th,
            .ops-lista td {
                padding: 6px 3px;
                font-size: 10px;
            }

            .ops-lista table {
                min-width: 800px;
            }

            .ops-lista .col-produto {
                min-width: 120px;
            }

            .flag-icon {
                width: 10px;
                height: 10px;
            }

            .btn-xs {
                padding: 1px 4px;
                font-size: 9px;
            }
        }

        /* 🆕 Estilos para flags de progresso */
        .progress-flags {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 5px;
        }

        .progress-flag {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-size: 11px;
            transition: all 0.3s ease;
            cursor: help;
            border: 2px solid transparent;
        }

        .progress-flag.completed {
            background-color: #28a745;
            color: white;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
            border-color: #1e7e34;
        }

        .progress-flag.pending {
            background-color: #e9ecef;
            color: #6c757d;
            border-color: #dee2e6;
        }

        .progress-flag:hover {
            transform: scale(1.1);
        }

        /* Animação para flags que acabaram de ser completadas */
        .progress-flag.completed.new-completion {
            animation: flagComplete 0.6s ease-out;
        }

        @keyframes flagComplete {
            0% {
                background-color: #e9ecef;
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
                box-shadow: 0 4px 8px rgba(40, 167, 69, 0.5);
            }
            100% {
                background-color: #28a745;
                transform: scale(1);
                box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
            }
        }

        /* 🚨 Animação para indicador de materiais aguardando */
        @keyframes pulse-aguardando {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-industry"></i>
                Apontamentos Integrados
            </h1>
            <div class="sync-status connected" id="syncStatus">
                <i class="fas fa-check-circle"></i>
                <span>Sincronizado com Movimentação</span>
            </div>
        </div>

        <!-- Painel de Integração -->
        <div class="integration-panel">
            <div class="integration-header">
                <h2><i class="fas fa-link"></i> Integração com Movimentação de Armazém</h2>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn btn-success" onclick="buscarOPsManualmente()" title="Buscar OPs em todas as coleções">
                        <i class="fas fa-search"></i> Buscar OPs
                    </button>
                    <button class="btn btn-warning" onclick="criarOPTesteSeNecessario()" title="Criar OP de teste">
                        <i class="fas fa-plus"></i> Criar OP Teste
                    </button>
                    <button class="btn btn-info" onclick="listarColecoes()" title="Verificar coleções disponíveis">
                        <i class="fas fa-list"></i> Verificar Coleções
                    </button>
                    <button class="btn btn-danger" onclick="limparCacheFirebase()" title="Limpar cache Firebase">
                        <i class="fas fa-trash"></i> Limpar Cache
                    </button>
                    <button class="btn btn-primary" onclick="sincronizarTodosSistemas()">
                        <i class="fas fa-sync"></i> Sincronizar Agora
                    </button>
                </div>
            </div>
            
            <div class="quick-actions">
                <div class="action-card" onclick="abrirMovimentacao()">
                    <div class="icon"><i class="fas fa-truck"></i></div>
                    <h3>Movimentação</h3>
                    <p>Transferir materiais entre armazéns</p>
                </div>
                
                <div class="action-card" onclick="verificarSaldosGeral()">
                    <div class="icon"><i class="fas fa-chart-bar"></i></div>
                    <h3>Verificar Saldos</h3>
                    <p>Validar disponibilidade de materiais</p>
                </div>
                
                <div class="action-card" onclick="diagnosticarProblemas()">
                    <div class="icon"><i class="fas fa-stethoscope"></i></div>
                    <h3>Diagnóstico</h3>
                    <p>Identificar problemas de integração</p>
                </div>
                
                <div class="action-card" onclick="relatorioIntegrado()">
                    <div class="icon"><i class="fas fa-file-alt"></i></div>
                    <h3>Relatório</h3>
                    <p>Visão consolidada de produção</p>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <div class="filters-grid">
                <div class="form-group">
                    <label>Status da OP</label>
                    <select class="form-control" id="filtroStatus" onchange="aplicarFiltros()">
                        <option value="">Ativas (Excluir Concluídas/Canceladas)</option>
                        <option value="TODOS">Todos os Status</option>
                        <option value="Planejada">Planejada</option>
                        <option value="Liberada">Liberada</option>
                        <option value="Em Produção">Em Produção</option>
                        <option value="Concluída">Concluída</option>
                        <option value="Cancelada">Cancelada</option>
                    </select>
                    <small style="color: #007bff; font-size: 11px; margin-top: 5px; display: block;">
                        <i class="fas fa-sort-amount-down"></i>
                        Ordenação: Mais flags acionados primeiro
                    </small>
                </div>
                
                <div class="form-group">
                    <label>Material Transferido</label>
                    <select class="form-control" id="filtroMaterial" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                        <option value="true">Transferido</option>
                        <option value="false">Pendente</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Buscar OP</label>
                    <input type="text" class="form-control" id="buscaOP" placeholder="Digite o número da OP..." onkeyup="aplicarFiltros()">
                </div>

                <div class="form-group">
                    <label>Buscar por Produto</label>
                    <input type="text" class="form-control" id="buscaProduto" placeholder="Digite o código do produto..." onkeyup="aplicarFiltros()">
                </div>

                <div class="form-group">
                    <label>Visualização</label>
                    <div style="display: flex; gap: 5px;">
                        <button class="btn btn-primary btn-sm" id="btnCards" onclick="alternarVisualizacao('cards')">
                            <i class="fas fa-th"></i> Cards
                        </button>
                        <button class="btn btn-outline-primary btn-sm" id="btnLista" onclick="alternarVisualizacao('lista')">
                            <i class="fas fa-list"></i> Lista
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-secondary" onclick="limparFiltros()">
                        <i class="fas fa-eraser"></i> Limpar
                    </button>
                </div>
            </div>
        </div>

        <!-- Grid de OPs -->
        <div class="ops-grid" id="opsGrid">
            <!-- OPs serão carregadas dinamicamente -->
        </div>
    </div>

    <!-- Modal de Apontamento -->
    <div id="modalApontamento" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-clipboard-check"></i> Apontamento de Produção</h3>
                <button onclick="fecharModal('modalApontamento')" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" id="modalApontamentoBody">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal de Materiais -->
    <div id="modalMateriais" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-boxes"></i> Materiais da OP</h3>
                <button onclick="fecharModal('modalMateriais')" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" id="modalMateriaisBody">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal de Diagnóstico -->
    <div id="modalDiagnostico" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-stethoscope"></i> Diagnóstico do Sistema</h3>
                <button onclick="fecharModal('modalDiagnostico')" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" id="modalDiagnosticoBody">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal de Materiais -->
    <div id="modalMateriais" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-boxes"></i> Status dos Materiais</h3>
                <button onclick="fecharModal('modalMateriais')" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" id="modalMateriaisBody">
                <!-- Conteúdo do modal será carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <script>
        // ===================================================================
        // VARIÁVEIS GLOBAIS E CONFIGURAÇÃO
        // ===================================================================
        
        let ordensProducao = [];
        let produtos = [];
        let armazens = [];
        let estoques = [];
        let transferencias = [];
        let movimentacoes = [];
        let apontamentos = [];
        let estruturas = [];
        let currentOrder = null;
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', email: '<EMAIL>' };
        let tipoVisualizacao = 'cards'; // 'cards' ou 'lista'
        
        // Configuração de integração
        const INTEGRATION_CONFIG = {
            autoSync: true,
            syncInterval: 30000, // 30 segundos
            enableRealTime: true,
            debugMode: true
        };

        // ===================================================================
        // INICIALIZAÇÃO DO SISTEMA
        // ===================================================================
        
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Iniciando Sistema de Apontamentos Integrados...');

            try {
                // Aguardar um pouco para garantir que o Firebase foi carregado
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Verificar se Firebase está disponível
                if (!window.db) {
                    throw new Error('Firebase não foi carregado corretamente. Recarregue a página.');
                }

                console.log('🔥 Firebase verificado e disponível');

                await inicializarSistema();
                // await carregarDados(); // Desabilitado para evitar loops
                await configurarIntegracao();

                console.log('✅ Sistema inicializado com sucesso!');
                mostrarNotificacao('Sistema carregado com sucesso!', 'success');

            } catch (error) {
                console.error('❌ Erro ao inicializar sistema:', error);
                mostrarNotificacao('Erro ao carregar sistema: ' + error.message, 'error');
            }
        });

        // ===================================================================
        // FUNÇÕES DE INICIALIZAÇÃO
        // ===================================================================
        
        async function inicializarSistema() {
            // Configurar listeners de eventos
            window.addEventListener('beforeunload', salvarEstadoLocal);

            // Inicializar visualização padrão
            const grid = document.getElementById('opsGrid');
            if (grid) {
                grid.className = 'ops-grid cards-view'; // Padrão é cards
            }

            // Verificar conexão com movimentação
            await verificarConexaoMovimentacao();
        }

        async function listarColecoes() {
            console.log('📋 Listando coleções disponíveis...');

            try {
                // Tentar algumas coleções comuns para verificar se existem
                const colecoesParaTestar = [
                    'ordensProducao',
                    'ordens_producao',
                    'ordensproducao',
                    'ops',
                    'production_orders',
                    'ordens',
                    'produtos',
                    'estoques',
                    'armazens'
                ];

                const resultados = {};

                for (const colecao of colecoesParaTestar) {
                    try {
                        const snapshot = await getDocs(collection(db, colecao));
                        resultados[colecao] = snapshot.docs.length;
                        console.log(`✅ ${colecao}: ${snapshot.docs.length} documentos`);
                    } catch (error) {
                        resultados[colecao] = `Erro: ${error.message}`;
                        console.log(`❌ ${colecao}: ${error.message}`);
                    }
                }

                console.table(resultados);
                mostrarNotificacao('Verificação de coleções concluída. Veja o console.', 'info');

            } catch (error) {
                console.error('❌ Erro ao listar coleções:', error);
                mostrarNotificacao('Erro ao verificar coleções: ' + error.message, 'error');
            }
        }

        async function limparCacheFirebase() {
            try {
                console.log('🧹 Limpando cache Firebase...');

                // Limpar localStorage
                let removedLS = 0;
                Object.keys(localStorage).forEach(key => {
                    if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                        localStorage.removeItem(key);
                        removedLS++;
                        console.log('🗑️ Removido localStorage:', key);
                    }
                });

                // Limpar sessionStorage
                let removedSS = 0;
                Object.keys(sessionStorage).forEach(key => {
                    if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                        sessionStorage.removeItem(key);
                        removedSS++;
                        console.log('🗑️ Removido sessionStorage:', key);
                    }
                });

                // Tentar limpar IndexedDB
                if ('indexedDB' in window) {
                    const databases = [
                        'firebase-heartbeat-database',
                        'firebase-installations-database',
                        'firebaseLocalStorageDb',
                        'sistema-mrp-9a8c5',
                        'banco-mrp'
                    ];

                    for (const dbName of databases) {
                        try {
                            await new Promise((resolve) => {
                                const deleteReq = indexedDB.deleteDatabase(dbName);
                                deleteReq.onsuccess = () => {
                                    console.log('🗑️ Database removida:', dbName);
                                    resolve();
                                };
                                deleteReq.onerror = () => resolve(); // Não falhar se não existir
                                deleteReq.onblocked = () => resolve(); // Não falhar se bloqueado
                                // Timeout para evitar travamento
                                setTimeout(resolve, 1000);
                            });
                        } catch (error) {
                            console.warn('⚠️ Erro ao remover database:', dbName, error);
                        }
                    }
                }

                console.log(`✅ Cache Firebase limpo: ${removedLS} localStorage, ${removedSS} sessionStorage`);
                mostrarNotificacao('Cache Firebase limpo com sucesso!', 'success');

            } catch (error) {
                console.warn('⚠️ Erro ao limpar cache Firebase:', error);
                mostrarNotificacao('Erro ao limpar cache: ' + error.message, 'error');
            }
        }

        // Array para armazenar funções de unsubscribe dos listeners
        let unsubscribeFunctions = [];

        // Função para encontrar a coleção de OPs
        async function encontrarColecaoOPs() {
            // Usar diretamente o nome correto da coleção
            const nomeColecao = 'ordensProducao';

            try {
                const snapshot = await getDocs(collection(db, nomeColecao));
                console.log(`📋 ${nomeColecao}: ${snapshot.docs.length} documentos encontrados`);

                if (snapshot.docs.length > 0) {
                    console.log(`✅ Coleção de OPs confirmada: ${nomeColecao}`);
                    return nomeColecao;
                } else {
                    console.log(`⚠️ Coleção ${nomeColecao} existe mas está vazia`);
                    return nomeColecao;
                }
            } catch (error) {
                console.error(`❌ Erro ao acessar coleção ${nomeColecao}:`, error);
                return nomeColecao;
            }
        }

        // Função para criar OP de teste se necessário
        async function criarOPTesteSeNecessario() {
            if (ordensProducao.length === 0) {
                console.log('🧪 Nenhuma OP encontrada. Criando OP de teste...');

                try {
                    const opTeste = {
                        numero: 'OP-TEST-001',
                        numeroOP: 'OP-TEST-001',
                        produtoId: 'PROD-TESTE',
                        produto: 'Produto de Teste',
                        descricaoProduto: 'Produto para teste do sistema',
                        quantidade: 100,
                        unidade: 'UN',
                        status: 'Pendente',
                        dataEntrega: new Date(),
                        dataCriacao: new Date(),
                        armazemProducaoId: 'PROD1',
                        armazemAcabadosId: 'ACABADOS',
                        materiaisNecessarios: [
                            {
                                produtoId: 'MAT-001',
                                quantidade: 50,
                                necessidade: 50,
                                saldoReservado: 0
                            }
                        ],
                        criadoEm: new Date(),
                        criadoPor: 'Sistema de Teste'
                    };

                    const docRef = await addDoc(collection(db, "ordensProducao"), opTeste);
                    console.log('✅ OP de teste criada com ID:', docRef.id);

                    // Adicionar à lista local imediatamente
                    ordensProducao.push({ id: docRef.id, ...opTeste });
                    renderizarOPs();

                } catch (error) {
                    console.error('❌ Erro ao criar OP de teste:', error);
                }
            }
        }

        async function carregarDados() {
            console.log('📊 Carregando dados iniciais (listeners desabilitados para evitar loops)...');

            // Debug: Verificar configuração Firebase atual
            if (window.db && window.db.app) {
                console.log('🔍 Configuração Firebase atual:', {
                    projectId: window.db.app.options.projectId,
                    authDomain: window.db.app.options.authDomain,
                    appName: window.db.app.name
                });
            }

            try {
                // 1. LISTENER PARA PRODUTOS
                console.log('� Configurando listener de produtos...');
                const unsubscribeProdutos = onSnapshot(collection(db, "produtos"), (snap) => {
                    produtos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`� Produtos atualizados: ${produtos.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeProdutos);

                // 2. LISTENER PARA ORDENS DE PRODUÇÃO (PRINCIPAL)
                console.log('� Configurando listener de ordens de produção...');

                const unsubscribeOPs = onSnapshot(collection(db, "ordensProducao"), (snap) => {
                    // 🆕 Salvar estado anterior antes da atualização
                    ordensProducaoAnterior = [...ordensProducao];

                    ordensProducao = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`📋 OPs atualizadas: ${ordensProducao.length} itens`);

                    // Debug detalhado das OPs
                    if (ordensProducao.length === 0) {
                        console.warn('⚠️ Nenhuma OP encontrada na coleção "ordensProducao"');
                    } else {
                        console.log('📋 Primeiras 3 OPs encontradas:', ordensProducao.slice(0, 3).map(op => ({
                            id: op.id,
                            numero: op.numero,
                            status: op.status,
                            produto: op.produto || op.descricaoProduto
                        })));

                        // 🆕 Detectar mudanças no status de materiais (apenas se não é a primeira carga)
                        if (ordensProducaoAnterior.length > 0) {
                            detectarMudancasGlobaisStatus();
                        }

                        // Renderizar OPs quando dados chegarem
                        renderizarOPs();
                        atualizarContadores();
                    }
                });
                unsubscribeFunctions.push(unsubscribeOPs);

                // 3. LISTENER PARA ESTOQUES
                console.log('📊 Configurando listener de estoques...');
                const unsubscribeEstoques = onSnapshot(collection(db, "estoques"), (snap) => {
                    estoques = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`📊 Estoques atualizados: ${estoques.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeEstoques);

                // 4. LISTENER PARA ARMAZÉNS
                console.log('🏪 Configurando listener de armazéns...');
                const unsubscribeArmazens = onSnapshot(collection(db, "armazens"), (snap) => {
                    armazens = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`🏪 Armazéns atualizados: ${armazens.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeArmazens);

                // 5. LISTENER PARA TRANSFERÊNCIAS
                console.log('� Configurando listener de transferências...');
                const unsubscribeTransferencias = onSnapshot(collection(db, "transferenciasArmazem"), (snap) => {
                    transferencias = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`🔄 Transferências atualizadas: ${transferencias.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeTransferencias);

                // 6. LISTENER PARA APONTAMENTOS
                console.log('📝 Configurando listener de apontamentos...');
                const unsubscribeApontamentos = onSnapshot(collection(db, "apontamentos"), (snap) => {
                    apontamentos = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`📝 Apontamentos atualizados: ${apontamentos.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeApontamentos);

                // 7. LISTENER PARA ESTRUTURAS
                console.log('🏗️ Configurando listener de estruturas...');
                const unsubscribeEstruturas = onSnapshot(collection(db, "estruturas"), (snap) => {
                    estruturas = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                    console.log(`🏗️ Estruturas atualizadas: ${estruturas.length} itens`);
                });
                unsubscribeFunctions.push(unsubscribeEstruturas);

                console.log('✅ Todos os listeners configurados com sucesso!');

                // Aguardar um pouco e verificar se OPs foram carregadas
                setTimeout(async () => {
                    if (ordensProducao.length === 0) {
                        console.log('⚠️ Nenhuma OP carregada após 3 segundos. Tentando busca manual...');
                        await buscarOPsManualmente();
                    }
                }, 3000);

            } catch (error) {
                console.error('❌ Erro ao configurar listeners:', error);
                mostrarNotificacao('Erro ao configurar listeners do Firebase. Tente recarregar.', 'error');
                throw error;
            }
        }

        // 🆕 Variável para armazenar estado anterior das OPs (para detectar mudanças)
        let ordensProducaoAnterior = [];

        // Função para buscar OPs manualmente
        async function buscarOPsManualmente() {
            console.log('🔍 Buscando OPs manualmente...');

            const colecaoOPs = await encontrarColecaoOPs();

            try {
                // 🆕 Salvar estado anterior
                ordensProducaoAnterior = [...ordensProducao];

                const snapshot = await getDocs(collection(db, colecaoOPs));
                ordensProducao = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`📋 ${ordensProducao.length} OPs carregadas manualmente da coleção ${colecaoOPs}`);

                // 🆕 Detectar mudanças no status de materiais
                if (ordensProducaoAnterior.length > 0) {
                    detectarMudancasGlobaisStatus();
                }

                if (ordensProducao.length === 0) {
                    await criarOPTesteSeNecessario();
                } else {
                    renderizarOPs();
                    atualizarContadores();
                }

            } catch (error) {
                console.error('❌ Erro na busca manual:', error);
                await criarOPTesteSeNecessario();
            }
        }

        // 🆕 Função para detectar mudanças globais no status dos materiais
        function detectarMudancasGlobaisStatus() {
            if (ordensProducaoAnterior.length === 0) return;

            let opsLiberadas = [];
            let opsBloqueadas = [];
            let opsAlteradas = [];

            ordensProducao.forEach(opAtual => {
                const opAnterior = ordensProducaoAnterior.find(op => op.id === opAtual.id);
                const mudanca = detectarMudancasStatusMaterial(opAnterior, opAtual);

                if (mudanca) {
                    if (mudanca.tipo === 'LIBERADO') {
                        opsLiberadas.push(mudanca);
                    } else if (mudanca.tipo === 'BLOQUEADO') {
                        opsBloqueadas.push(mudanca);
                    } else if (mudanca.tipo === 'ALTERADO') {
                        opsAlteradas.push(mudanca);
                    }
                }
            });

            // 🎉 Notificar OPs liberadas (prioridade alta)
            if (opsLiberadas.length > 0) {
                opsLiberadas.forEach(mudanca => {
                    mostrarNotificacao(`✅ OP ${mudanca.op.numero} - Materiais liberados! Pode prosseguir.`, 'success', 8000);
                    console.log('🎉 OP LIBERADA:', mudanca.mensagem);
                });

                // Som de notificação (opcional)
                if (typeof Audio !== 'undefined') {
                    try {
                        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuR2O/Eeyw');
                        audio.volume = 0.3;
                        audio.play().catch(() => {}); // Ignorar erros de áudio
                    } catch (e) {}
                }
            }

            // ⚠️ Notificar OPs bloqueadas
            if (opsBloqueadas.length > 0) {
                opsBloqueadas.forEach(mudanca => {
                    mostrarNotificacao(`⚠️ OP ${mudanca.op.numero} - Dependência de material detectada`, 'warning', 6000);
                    console.log('⚠️ OP BLOQUEADA:', mudanca.mensagem);
                });
            }

            // 🔄 Notificar alterações
            if (opsAlteradas.length > 0) {
                opsAlteradas.forEach(mudanca => {
                    mostrarNotificacao(`🔄 OP ${mudanca.op.numero} - Status de materiais alterado`, 'info', 4000);
                    console.log('🔄 OP ALTERADA:', mudanca.mensagem);
                });
            }
        }

        async function configurarIntegracao() {
            // Verificar se movimentacao_armazem_novo.html está disponível
            try {
                const response = await fetch('movimentacao_armazem_novo.html');
                if (response.ok) {
                    console.log('✅ Integração com movimentação disponível');
                    atualizarStatusSincronizacao('connected');
                } else {
                    console.warn('⚠️ Arquivo de movimentação não encontrado');
                    atualizarStatusSincronizacao('disconnected');
                }
            } catch (error) {
                console.warn('⚠️ Erro ao verificar integração:', error);
                atualizarStatusSincronizacao('error');
            }
        }

        // ===================================================================
        // FUNÇÕES DE SINCRONIZAÇÃO
        // ===================================================================
        
        async function sincronizarTodosSistemas() {
            console.log('🔄 Iniciando sincronização completa...');
            atualizarStatusSincronizacao('syncing');
            
            try {
                // 1. Recarregar dados do Firebase (desabilitado)
                // await carregarDados();
                
                // 2. Sincronizar com transferências
                await sincronizarTransferencias();
                
                // 3. Atualizar flags de OPs
                await atualizarFlagsOPs();
                
                // 4. Re-renderizar interface
                renderizarOPs();
                
                atualizarStatusSincronizacao('connected');
                mostrarNotificacao('Sincronização concluída com sucesso!', 'success');
                
            } catch (error) {
                console.error('❌ Erro na sincronização:', error);
                atualizarStatusSincronizacao('error');
                mostrarNotificacao('Erro na sincronização: ' + error.message, 'error');
            }
        }

        async function sincronizarTransferencias() {
            console.log('🔄 Sincronizando transferências...');
            
            for (const op of ordensProducao) {
                if (!op.materiaisNecessarios) continue;
                
                // Buscar transferências da OP
                const transferenciasOP = transferencias.filter(t => 
                    t.ordemProducaoId === op.id && 
                    t.status === 'CONCLUIDA'
                );
                
                if (transferenciasOP.length > 0) {
                    // Atualizar saldos reservados baseado nas transferências
                    const materiaisAtualizados = op.materiaisNecessarios.map(material => {
                        let totalTransferido = 0;
                        
                        transferenciasOP.forEach(transferencia => {
                            if (transferencia.materiais) {
                                const materialTransferido = transferencia.materiais.find(m => 
                                    m.produtoId === material.produtoId
                                );
                                if (materialTransferido) {
                                    totalTransferido += materialTransferido.quantidade;
                                }
                            }
                        });
                        
                        return {
                            ...material,
                            saldoReservado: totalTransferido
                        };
                    });
                    
                    // Atualizar no Firebase
                    await updateDoc(doc(db, "ordensProducao", op.id), {
                        materiaisNecessarios: materiaisAtualizados,
                        ultimaSincronizacao: Timestamp.now()
                    });
                    
                    // Atualizar localmente
                    op.materiaisNecessarios = materiaisAtualizados;
                }
            }
        }

        async function atualizarFlagsOPs() {
            console.log('🚩 Atualizando flags das OPs...');
            
            for (const op of ordensProducao) {
                if (!op.materiaisNecessarios) continue;
                
                // Verificar se material foi transferido
                const materialTransferido = verificarMaterialTransferido(op);
                
                // Verificar se saldo foi validado
                const saldoValidado = verificarSaldoValidado(op);
                
                // Atualizar flags se necessário
                if (op.materialTransferido !== materialTransferido || op.saldoValidado !== saldoValidado) {
                    await updateDoc(doc(db, "ordensProducao", op.id), {
                        materialTransferido,
                        saldoValidado,
                        ultimaAtualizacaoFlags: Timestamp.now()
                    });
                    
                    op.materialTransferido = materialTransferido;
                    op.saldoValidado = saldoValidado;
                }
            }
        }

        // ===================================================================
        // FUNÇÕES DE VALIDAÇÃO
        // ===================================================================
        
        function verificarMaterialTransferido(op) {
            if (!op.materiaisNecessarios) return false;
            
            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });
            
            if (materiaisMP.length === 0) return true;
            
            return materiaisMP.every(material => {
                const necessario = material.necessidade || material.quantidade || 0;
                const transferido = material.saldoReservado || 0;
                return transferido >= (necessario - 0.001); // Tolerância decimal
            });
        }

        function verificarSaldoValidado(op) {
            if (!op.materialTransferido) return false;
            
            const armazemProducao = op.armazemProducaoId;
            if (!armazemProducao) return false;
            
            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });
            
            return materiaisMP.every(material => {
                const estoque = estoques.find(e => 
                    e.produtoId === material.produtoId && 
                    e.armazemId === armazemProducao
                );
                
                const saldoDisponivel = estoque ? (estoque.saldo || 0) : 0;
                const necessario = material.necessidade || material.quantidade || 0;
                
                return saldoDisponivel >= (necessario - 0.001);
            });
        }

        // ===================================================================
        // FUNÇÕES DE INTERFACE
        // ===================================================================
        
        function renderizarOPs() {
            const grid = document.getElementById('opsGrid');
            if (!grid) {
                console.error('❌ Grid de OPs não encontrado!');
                return;
            }

            console.log(`🎨 Renderizando OPs... Total: ${ordensProducao.length}`);

            // Se não há OPs carregadas
            if (ordensProducao.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <i class="fas fa-clipboard-list" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                        <h3 style="color: #7f8c8d; margin-bottom: 15px;">Nenhuma OP carregada</h3>
                        <p style="color: #95a5a6; margin-bottom: 25px;">Não há ordens de produção no sistema</p>

                        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                            <button class="btn btn-success" onclick="buscarOPsManualmente()">
                                <i class="fas fa-search"></i> Buscar OPs
                            </button>
                            <button class="btn btn-warning" onclick="criarOPTesteSeNecessario()">
                                <i class="fas fa-plus"></i> Criar OP de Teste
                            </button>
                            <button class="btn btn-info" onclick="listarColecoes()">
                                <i class="fas fa-list"></i> Verificar Coleções
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            const opsFiltradas = aplicarFiltrosOPs();

            // Se há OPs mas nenhuma passou pelos filtros
            if (opsFiltradas.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                        <i class="fas fa-filter" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                        <h3 style="color: #7f8c8d;">Nenhuma OP corresponde aos filtros</h3>
                        <p style="color: #95a5a6;">Há ${ordensProducao.length} OPs no total. Ajuste os filtros para visualizá-las.</p>
                        <button class="btn btn-secondary" onclick="limparFiltros()">
                            <i class="fas fa-times"></i> Limpar Filtros
                        </button>
                    </div>
                `;
                return;
            }

            console.log(`✅ Renderizando ${opsFiltradas.length} OPs filtradas`);

            if (tipoVisualizacao === 'lista') {
                grid.innerHTML = criarTabelaOPs(opsFiltradas);
            } else {
                grid.innerHTML = opsFiltradas.map(op => criarCardOP(op)).join('');
            }
        }

        // Função para alternar tipo de visualização
        function alternarVisualizacao(tipo) {
            tipoVisualizacao = tipo;

            // Atualizar botões
            const btnCards = document.getElementById('btnCards');
            const btnLista = document.getElementById('btnLista');
            const grid = document.getElementById('opsGrid');

            if (tipo === 'cards') {
                btnCards.className = 'btn btn-primary btn-sm';
                btnLista.className = 'btn btn-outline-primary btn-sm';
                grid.className = 'ops-grid cards-view';
            } else {
                btnCards.className = 'btn btn-outline-primary btn-sm';
                btnLista.className = 'btn btn-primary btn-sm';
                grid.className = 'ops-grid lista-view';
            }

            // Re-renderizar
            renderizarOPs();
        }

        // Função para criar tabela de OPs (igual ao simplificado)
        function criarTabelaOPs(ops) {
            if (ops.length === 0) {
                return `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-search" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                        <h3 style="color: #7f8c8d;">Nenhuma OP encontrada</h3>
                        <p style="color: #95a5a6;">Ajuste os filtros para ver as OPs</p>
                    </div>
                `;
            }

            return `
                <div class="table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>Ordem</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>
                                    <i class="fas fa-sort-amount-down" style="color: #007bff;"></i>
                                    Produzido
                                    <small style="display: block; font-weight: normal; color: #6c757d; font-size: 10px;">
                                        Ordenado por progresso
                                    </small>
                                </th>
                                <th>Status</th>
                                <th>Data Entrega</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${ops.map(op => criarLinhaOPSimplificada(op)).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // Função para criar linha da tabela (igual ao simplificado)
        function criarLinhaOPSimplificada(ordem) {
            const produto = produtos.find(p => p.id === ordem.produtoId);
            if (!produto) return '';

            const progress = ordem.quantidadeProduzida ?
                formatarNumero((ordem.quantidadeProduzida / ordem.quantidade * 100)) : 0;

            // Trata a exibição da data de entrega para casos onde pode estar ausente
            const dataEntrega = ordem.dataEntrega?.seconds
                ? new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString()
                : 'Não definida';

            return `
                <tr data-op-id="${ordem.id}">
                    <td>${ordem.numero}</td>
                    <td>${produto.codigo} - ${produto.descricao}</td>
                    <td>${formatarNumero(ordem.quantidade)} ${produto.unidade}</td>
                    <td>
                        ${formatarNumero(ordem.quantidadeProduzida || 0)} ${produto.unidade}
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-${ordem.status.toLowerCase()}">${ordem.status}</span>
                        ${renderProgressIndicator(ordem)}
                    </td>
                    <td>${dataEntrega}</td>
                    <td>
                        ${gerarBotoesAcaoTabela(ordem)}
                    </td>
                </tr>
            `;
        }

        // Função para calcular flags de controle
        function calcularFlags(op) {
            const flags = [];

            // Flag 1: Material Transferido
            if (op.materialTransferido) {
                flags.push({
                    class: 'flag-ok',
                    tooltip: 'Material transferido para produção'
                });
            } else {
                flags.push({
                    class: 'flag-error',
                    tooltip: 'Material não transferido'
                });
            }

            // Flag 2: Saldo Validado
            if (op.saldoValidado) {
                flags.push({
                    class: 'flag-ok',
                    tooltip: 'Saldo validado'
                });
            } else if (op.materialTransferido) {
                flags.push({
                    class: 'flag-warning',
                    tooltip: 'Saldo pendente de validação'
                });
            } else {
                flags.push({
                    class: 'flag-disabled',
                    tooltip: 'Aguardando transferência de material'
                });
            }

            // Flag 3: OP Impressa
            if (op.impressa) {
                flags.push({
                    class: 'flag-ok',
                    tooltip: 'OP impressa'
                });
            } else if (op.saldoValidado) {
                flags.push({
                    class: 'flag-warning',
                    tooltip: 'Pronta para impressão'
                });
            } else {
                flags.push({
                    class: 'flag-disabled',
                    tooltip: 'Aguardando validação de saldo'
                });
            }

            // Flag 4: Enviada para Fábrica
            if (op.enviadaFabrica) {
                flags.push({
                    class: 'flag-ok',
                    tooltip: 'Enviada para fábrica'
                });
            } else if (op.impressa) {
                flags.push({
                    class: 'flag-warning',
                    tooltip: 'Pronta para envio'
                });
            } else {
                flags.push({
                    class: 'flag-disabled',
                    tooltip: 'Aguardando impressão'
                });
            }

            // Flag 5: Produção Iniciada
            if (op.quantidadeProduzida > 0) {
                flags.push({
                    class: 'flag-ok',
                    tooltip: 'Produção iniciada'
                });
            } else if (op.enviadaFabrica) {
                flags.push({
                    class: 'flag-info',
                    tooltip: 'Aguardando início da produção'
                });
            } else {
                flags.push({
                    class: 'flag-disabled',
                    tooltip: 'Aguardando envio para fábrica'
                });
            }

            return flags;
        }

        // Função para renderizar indicador de progresso (do simplificado)
        function renderProgressIndicator(ordem) {
            if (ordem.status === 'Concluída' || ordem.status === 'Cancelada') {
                return '';
            }

            const steps = [
                { key: 'materialTransferido', icon: 'exchange-alt', label: 'Transferido' },
                { key: 'saldoValidado', icon: 'search', label: 'Saldo' },
                { key: 'impressa', icon: 'print', label: 'Impressa' },
                { key: 'enviadaFabrica', icon: 'industry', label: 'Fábrica' },
                { key: 'podeApontar', icon: 'clipboard', label: 'Apontar', check: () => ordem.enviadaFabrica }
            ];

            let progressHtml = '<div class="progress-indicator" style="margin-top: 5px; display: flex; gap: 3px;">';

            steps.forEach((step, index) => {
                const isCompleted = step.check ? step.check() : ordem[step.key];
                const stepClass = isCompleted ? 'step-completed' : 'step-pending';
                const stepIcon = isCompleted ? 'check' : step.icon;

                progressHtml += `
                    <span class="progress-step ${stepClass}"
                          title="${step.label}: ${isCompleted ? 'Concluído' : 'Pendente'}"
                          style="display: inline-block; width: 16px; height: 16px; border-radius: 50%;
                                 background: ${isCompleted ? '#28a745' : '#e9ecef'};
                                 color: ${isCompleted ? 'white' : '#6c757d'};
                                 text-align: center; line-height: 16px; font-size: 8px;">
                        <i class="fas fa-${stepIcon}"></i>
                    </span>
                `;
            });

            progressHtml += '</div>';
            return progressHtml;
        }

        // Função para renderizar botões baseado no fluxo sequencial (do simplificado)
        function renderFlowButtons(ordem) {
            // ===== VERIFICAR SE OP ESTÁ FINALIZADA =====
            if (ordem.status === 'Concluída' || ordem.status === 'Cancelada') {
                return `
                    <div style="text-align: center; padding: 10px; color: #6c757d; font-style: italic;">
                        <i class="fas fa-${ordem.status === 'Concluída' ? 'check-circle' : 'times-circle'}"></i>
                        OP ${ordem.status}
                    </div>
                `;
            }

            let buttons = '';

            // Botão de apontamento sempre disponível
            buttons += `
                <button onclick="abrirModalApontamento('${ordem.id}')"
                        class="btn btn-sm btn-primary"
                        style="margin: 2px; padding: 4px 8px; font-size: 11px;"
                        title="Fazer apontamento">
                    <i class="fas fa-clipboard"></i> Apontar
                </button>
            `;

            // Botão de visualizar
            buttons += `
                <button onclick="visualizarOP('${ordem.id}')"
                        class="btn btn-sm btn-info"
                        style="margin: 2px; padding: 4px 8px; font-size: 11px;"
                        title="Visualizar detalhes">
                    <i class="fas fa-eye"></i> Ver
                </button>
            `;

            return `<div style="display: flex; flex-wrap: wrap; gap: 2px;">${buttons}</div>`;
        }

        // Função para formatar números (do simplificado)
        function formatarNumero(numero) {
            if (numero === null || numero === undefined) return '0';
            return parseFloat(numero).toFixed(3).replace(/\.?0+$/, '');
        }

        // 🆕 Função para gerar botões de ação para tabela (apenas próxima ação)
        function gerarBotoesAcaoTabela(op) {
            // ===== VERIFICAR SE OP ESTÁ FINALIZADA =====
            if (op.status === 'Concluída' || op.status === 'Cancelada') {
                return `
                    <span style="color: #6c757d; font-style: italic; font-size: 11px;">
                        <i class="fas fa-${op.status === 'Concluída' ? 'check-circle' : 'times-circle'}"></i>
                        ${op.status}
                    </span>
                `;
            }

            let buttons = '';

            // ===== DETERMINAR PRÓXIMA AÇÃO =====
            if (!op.materialTransferido) {
                // ETAPA 1: Transferir Material
                buttons += `
                    <button onclick="transferirMateriais('${op.id}')" class="btn btn-warning btn-xs" style="margin: 1px;"
                            title="Transferir Material">
                        <i class="fas fa-truck"></i>
                    </button>
                `;
            } else if (!op.saldoValidado) {
                // ETAPA 2: Verificar Saldo
                buttons += `
                    <button onclick="verificarSaldo('${op.id}')" class="btn btn-info btn-xs" style="margin: 1px;"
                            title="Verificar Saldo">
                        <i class="fas fa-search"></i>
                    </button>
                `;
            } else if (!op.impressa) {
                // ETAPA 3: Imprimir OP
                buttons += `
                    <button onclick="imprimirOP('${op.id}')" class="btn btn-secondary btn-xs" style="margin: 1px;"
                            title="Imprimir OP">
                        <i class="fas fa-print"></i>
                    </button>
                `;
            } else if (!op.enviadaFabrica) {
                // ETAPA 4: Enviar para Fábrica
                buttons += `
                    <button onclick="enviarFabrica('${op.id}')" class="btn btn-success btn-xs" style="margin: 1px;"
                            title="Enviar para Fábrica">
                        <i class="fas fa-industry"></i>
                    </button>
                `;
            } else if (op.status !== 'Concluída') {
                // ETAPA 5: Apontar
                buttons += `
                    <button onclick="abrirApontamento('${op.id}')" class="btn btn-primary btn-xs" style="margin: 1px;"
                            title="Fazer Apontamento">
                        <i class="fas fa-clipboard-check"></i>
                    </button>
                `;
            }

            // ===== BOTÃO DE MATERIAIS (SEMPRE DISPONÍVEL) =====
            buttons += `
                <button class="btn btn-info btn-xs" onclick="verMateriais('${op.id}')" style="margin: 1px;" title="Ver Materiais">
                    <i class="fas fa-boxes"></i>
                </button>
            `;

            return buttons;
        }

        function criarCardOP(op) {
            const produto = produtos.find(p => p.id === op.produtoId);
            const statusClass = `status-${op.status?.toLowerCase().replace(' ', '-') || 'planejada'}`;

            // Calcular progresso das etapas
            const etapas = calcularProgressoEtapas(op);

            // Status dos materiais
            const statusMateriais = calcularStatusMateriais(op);

            // Calcular progresso de produção
            const apontamentosOP = apontamentos.filter(a => a.ordemId === op.id);
            const totalProduzido = apontamentosOP.reduce((total, a) => total + (a.quantidade || 0), 0);
            const percentualProduzido = op.quantidade > 0 ? Math.round((totalProduzido / op.quantidade) * 100) : 0;

            // Determinar próxima ação
            const proximaAcao = determinarProximaAcao(op);

            return `
                <div class="op-card" data-op-id="${op.id}">
                    <div class="op-header">
                        <div class="op-number">${op.numero || op.numeroOP || op.id}</div>
                        <div class="op-status ${statusClass}">${op.status || 'Pendente'}</div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>Produto:</strong> ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}<br>
                        <strong>Quantidade:</strong> ${op.quantidade || 0} ${produto?.unidade || 'UN'}<br>
                        ${totalProduzido > 0 ? `<strong>Produzido:</strong> ${totalProduzido} (${percentualProduzido}%)` : ''}
                    </div>

                    <div class="material-status">
                        ${statusMateriais.map(status =>
                            `<span class="material-badge ${status.class}">${status.label}</span>`
                        ).join('')}
                    </div>

                    <div class="progress-steps">
                        ${etapas.map((etapa, index) =>
                            `<div class="step ${etapa.status}" title="${etapa.titulo}">
                                <i class="fas ${etapa.icon}"></i>
                            </div>`
                        ).join('')}
                    </div>

                    ${proximaAcao ? `
                        <div class="next-action">
                            <i class="fas fa-arrow-right"></i> <strong>Próximo:</strong> ${proximaAcao}
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        ${gerarBotoesAcao(op)}
                    </div>
                </div>
            `;
        }

        function calcularProgressoEtapas(op) {
            const etapas = [
                {
                    titulo: 'Transferir Material',
                    icon: 'fa-truck',
                    status: op.materialTransferido ? 'completed' : 'pending'
                },
                {
                    titulo: 'Verificar Saldo',
                    icon: 'fa-check-circle',
                    status: op.saldoValidado ? 'completed' : (op.materialTransferido ? 'pending' : 'disabled')
                },
                {
                    titulo: 'Imprimir OP',
                    icon: 'fa-print',
                    status: op.impressa ? 'completed' : (op.saldoValidado ? 'pending' : 'disabled')
                },
                {
                    titulo: 'Enviar Fábrica',
                    icon: 'fa-paper-plane',
                    status: op.enviadaFabrica ? 'completed' : (op.impressa ? 'pending' : 'disabled')
                },
                {
                    titulo: 'Apontar Produção',
                    icon: 'fa-clipboard-check',
                    status: op.quantidadeProduzida > 0 ? 'completed' : (op.enviadaFabrica ? 'pending' : 'disabled')
                }
            ];

            return etapas;
        }

        function calcularStatusMateriais(op) {
            if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                return [{ label: 'Sem materiais', class: 'status-info' }];
            }

            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });

            if (materiaisMP.length === 0) {
                return [{ label: 'Apenas SP', class: 'status-info' }];
            }

            const materiaisTransferidos = materiaisMP.filter(m => (m.saldoReservado || 0) > 0);
            const materiaisCompletos = materiaisMP.filter(m => {
                const necessario = m.necessidade || m.quantidade || 0;
                const transferido = m.saldoReservado || 0;
                return transferido >= (necessario - 0.001);
            });

            const status = [];

            if (materiaisCompletos.length === materiaisMP.length) {
                status.push({ label: 'Material OK', class: 'status-ok' });
            } else if (materiaisTransferidos.length > 0) {
                status.push({ label: 'Parcial', class: 'status-warning' });
            } else {
                status.push({ label: 'Pendente', class: 'status-error' });
            }

            return status;
        }

        function determinarProximaAcao(op) {
            if (op.status === 'Concluída') return null;
            if (op.status === 'Cancelada') return null;

            if (!op.materialTransferido) return 'Transferir materiais';
            if (!op.saldoValidado) return 'Verificar saldo';
            if (!op.impressa) return 'Imprimir OP';
            if (!op.enviadaFabrica) return 'Enviar para fábrica';
            if (!op.quantidadeProduzida || op.quantidadeProduzida < op.quantidade) return 'Apontar produção';

            return null;
        }

        // 🆕 Função para renderizar apenas a próxima ação disponível
        function gerarBotoesAcao(op) {
            // ===== VERIFICAR SE OP ESTÁ FINALIZADA =====
            if (op.status === 'Concluída' || op.status === 'Cancelada') {
                return `
                    <div style="text-align: center; padding: 10px; color: #6c757d; font-style: italic;">
                        <i class="fas fa-${op.status === 'Concluída' ? 'check-circle' : 'times-circle'}"></i>
                        OP ${op.status}
                    </div>
                `;
            }

            // 🚨 VERIFICAR MATERIAIS AGUARDANDO
            const aguardandoInfo = verificarMateriaisAguardando(op);
            let buttons = '';

            // 🚨 AVISO DE DEPENDÊNCIA DE MATERIAL
            if (aguardandoInfo.temAguardando) {
                const materiaisTexto = aguardandoInfo.materiais.map(m => `• ${m.codigo} - ${m.descricao}`).join('\n');
                buttons += `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 8px; margin-bottom: 8px;">
                        <div style="color: #856404; font-size: 12px; font-weight: 600;">
                            <i class="fas fa-exclamation-triangle"></i>
                            DEPENDÊNCIA DE MATERIAL (${aguardandoInfo.quantidade})
                        </div>
                        <div style="color: #856404; font-size: 11px; margin-top: 4px;">
                            Esta OP não pode prosseguir até a chegada dos materiais marcados como "aguardando" no sistema de movimentação.
                        </div>
                        <button class="btn btn-outline-warning btn-sm" onclick="mostrarDetalhesAguardando('${op.id}')"
                                style="margin-top: 6px; font-size: 11px;">
                            <i class="fas fa-list"></i> Ver Detalhes
                        </button>
                    </div>
                `;
            }

            // ===== DETERMINAR PRÓXIMA AÇÃO =====
            if (!op.materialTransferido) {
                // ETAPA 1: Transferir Material
                const desabilitado = aguardandoInfo.temAguardando;
                buttons += `
                    <button onclick="${desabilitado ? 'alertarDependenciaMaterial' : 'transferirMateriais'}('${op.id}')"
                            class="btn ${desabilitado ? 'btn-outline-warning' : 'btn-warning'}"
                            style="margin-right: 8px; margin-bottom: 5px;"
                            ${desabilitado ? 'title="Há materiais aguardando chegada"' : ''}>
                        <i class="fas fa-${desabilitado ? 'exclamation-triangle' : 'truck'}"></i>
                        ${desabilitado ? 'Aguardando Material' : 'Transferir Material'}
                    </button>
                `;
            } else if (!op.saldoValidado) {
                // ETAPA 2: Verificar Saldo
                buttons += `
                    <button onclick="verificarSaldo('${op.id}')" class="btn btn-info" style="margin-right: 8px; margin-bottom: 5px;">
                        <i class="fas fa-search"></i> Verificar Saldo
                    </button>
                `;
            } else if (!op.impressa) {
                // ETAPA 3: Imprimir OP
                buttons += `
                    <button onclick="imprimirOP('${op.id}')" class="btn btn-secondary" style="margin-right: 8px; margin-bottom: 5px;">
                        <i class="fas fa-print"></i> Imprimir OP
                    </button>
                `;
            } else if (!op.enviadaFabrica) {
                // ETAPA 4: Enviar para Fábrica
                buttons += `
                    <button onclick="enviarFabrica('${op.id}')" class="btn btn-success" style="margin-right: 8px; margin-bottom: 5px;">
                        <i class="fas fa-industry"></i> Enviar Fábrica
                    </button>
                `;
            } else if (op.status !== 'Concluída') {
                // ETAPA 5: Apontar
                buttons += `
                    <button onclick="abrirApontamento('${op.id}')" class="btn btn-primary" style="margin-right: 8px; margin-bottom: 5px;">
                        <i class="fas fa-clipboard-check"></i> Fazer Apontamento
                    </button>
                `;
            }

            // ===== BOTÃO DE MATERIAIS (SEMPRE DISPONÍVEL) =====
            buttons += `
                <button class="btn btn-info btn-sm" onclick="verMateriais('${op.id}')" style="margin-bottom: 5px;"
                        title="Visualizar materiais da OP">
                    <i class="fas fa-boxes"></i> Materiais
                </button>
            `;

            return buttons;
        }

        // ===================================================================
        // 🔄 CONTROLE SEQUENCIAL DE FASES - SISTEMA INTELIGENTE
        // ===================================================================

        // Função para verificar o status atual da OP e determinar quais ações estão disponíveis
        function getOPFlowStatus(ordem) {
            const status = {
                temSaldo: false,
                podeImprimir: false,
                podeEnviarFabrica: false,
                podeApontar: false,
                etapaAtual: 'TRANSFERIR_MATERIAL',
                proximaEtapa: null
            };

            // 1. TRANSFERIR MATERIAL (primeira etapa)
            if (!ordem.materialTransferido) {
                status.etapaAtual = 'TRANSFERIR_MATERIAL';
                status.podeTransferirMaterial = true;
                status.proximaEtapa = 'VERIFICAR_SALDO';
                return status;
            }

            // 2. VERIFICAR SALDO (segunda etapa - após transferência)
            if (!ordem.saldoValidado) {
                status.etapaAtual = 'VERIFICAR_SALDO';
                status.podeTransferirMaterial = true;
                status.podeVerificarSaldo = true;
                status.proximaEtapa = 'IMPRIMIR';
                return status;
            }
            status.temSaldo = true;

            // 3. IMPRIMIR OP (terceira etapa)
            if (!ordem.impressa) {
                status.etapaAtual = 'IMPRIMIR';
                status.podeTransferirMaterial = true;
                status.podeVerificarSaldo = true;
                status.podeImprimir = true;
                status.proximaEtapa = 'ENVIAR_FABRICA';
                return status;
            }

            // 4. ENVIAR PARA FÁBRICA (quarta etapa)
            if (!ordem.enviadaFabrica) {
                status.etapaAtual = 'ENVIAR_FABRICA';
                status.podeTransferirMaterial = true;
                status.podeVerificarSaldo = true;
                status.podeImprimir = true;
                status.podeEnviarFabrica = true;
                status.proximaEtapa = 'APONTAR';
                return status;
            }

            // 5. APONTAR (quinta etapa - após envio para fábrica)
            if (ordem.status !== 'Concluída') {
                status.etapaAtual = 'APONTAR';
                status.podeTransferirMaterial = true;
                status.podeVerificarSaldo = true;
                status.podeImprimir = true;
                status.podeEnviarFabrica = true;
                status.podeApontar = true;
                status.proximaEtapa = 'CONCLUIR';
                return status;
            }

            // 6. PROCESSO CONCLUÍDO
            status.etapaAtual = 'CONCLUIDA';
            status.temSaldo = true;
            status.podeImprimir = true;
            status.podeEnviarFabrica = true;
            status.podeApontar = true;

            return status;
        }

        // Função para atualizar o status da OP no banco de dados
        async function updateOPFlowStatus(opId, campo, valor, dadosAdicionais = null) {
            try {
                const updateData = {};
                updateData[campo] = valor;
                updateData.ultimaAtualizacao = Timestamp.now();

                // Adicionar dados extras se fornecidos
                if (dadosAdicionais) {
                    Object.assign(updateData, dadosAdicionais);
                }

                await updateDoc(doc(db, "ordensProducao", opId), updateData);

                // Atualizar localmente
                const ordem = ordensProducao.find(op => op.id === opId);
                if (ordem) {
                    ordem[campo] = valor;
                    ordem.ultimaAtualizacao = new Date();
                    if (dadosAdicionais) {
                        Object.assign(ordem, dadosAdicionais);
                    }
                }

                // 🆕 Animar flag que foi completada
                if (valor === true) {
                    animateCompletedFlag(opId, campo);
                }

                console.log(`✅ Status atualizado: ${campo} = ${valor} para OP ${opId}`);
                return true;

            } catch (error) {
                console.error(`❌ Erro ao atualizar status da OP:`, error);
                return false;
            }
        }

        // 🆕 Função para animar flag completada
        function animateCompletedFlag(opId, campo) {
            // Mapear campos para índices dos flags
            const flagMapping = {
                'materialTransferido': 0,
                'saldoValidado': 1,
                'impressa': 2,
                'enviadaFabrica': 3,
                'status': 4 // quando status vira 'Concluída'
            };

            const flagIndex = flagMapping[campo];
            if (flagIndex === undefined) return;

            // Encontrar o flag específico e animar
            setTimeout(() => {
                const opElements = document.querySelectorAll(`[data-op-id="${opId}"]`);
                opElements.forEach(opElement => {
                    const flags = opElement.querySelectorAll('.progress-flag');
                    if (flags[flagIndex]) {
                        flags[flagIndex].classList.add('new-completion');
                        // Remover a classe após a animação
                        setTimeout(() => {
                            flags[flagIndex].classList.remove('new-completion');
                        }, 600);
                    }
                });
            }, 100);
        }

        // 🆕 Função para renderizar flags de progresso (estilo da imagem)
        function renderProgressIndicator(ordem) {
            if (ordem.status === 'Concluída' || ordem.status === 'Cancelada') {
                return `
                    <div class="progress-flags">
                        <span style="font-size: 11px; color: #6c757d; font-style: italic;">
                            <i class="fas fa-${ordem.status === 'Concluída' ? 'check-circle' : 'times-circle'}"></i>
                            ${ordem.status}
                        </span>
                    </div>
                `;
            }

            const flags = [
                {
                    key: 'materialTransferido',
                    icon: 'truck',
                    label: 'Material Transferido',
                    completed: ordem.materialTransferido
                },
                {
                    key: 'saldoValidado',
                    icon: 'search',
                    label: 'Saldo Validado',
                    completed: ordem.saldoValidado
                },
                {
                    key: 'impressa',
                    icon: 'print',
                    label: 'OP Impressa',
                    completed: ordem.impressa
                },
                {
                    key: 'enviadaFabrica',
                    icon: 'industry',
                    label: 'Enviada para Fábrica',
                    completed: ordem.enviadaFabrica
                },
                {
                    key: 'apontamentos',
                    icon: 'clipboard-check',
                    label: 'Apontamentos Feitos',
                    completed: ordem.status === 'Concluída'
                }
            ];

            // 🆕 Verificar materiais aguardando
            const aguardandoInfo = verificarMateriaisAguardando(ordem);

            // 🆕 Calcular progresso
            const pontuacao = calcularPontuacaoProgresso(ordem);
            const porcentagem = (pontuacao / 5) * 100;

            let flagsHtml = '<div class="progress-flags">';

            // 🚨 INDICADOR DE MATERIAIS AGUARDANDO (prioridade máxima)
            if (aguardandoInfo.temAguardando) {
                const materiaisTexto = aguardandoInfo.materiais.map(m => `${m.codigo} - ${m.descricao}`).join('\n');
                flagsHtml += `
                    <span style="font-size: 10px; color: #dc3545; font-weight: 600; margin-right: 8px;
                                 background: rgba(220, 53, 69, 0.1); padding: 2px 6px; border-radius: 10px;
                                 border: 1px solid #dc3545; animation: pulse-aguardando 2s infinite;"
                          title="⚠️ DEPENDÊNCIA DE MATERIAL!\n\n${aguardandoInfo.quantidade} material(is) aguardando chegada:\n${materiaisTexto}">
                        <i class="fas fa-exclamation-triangle"></i> ${aguardandoInfo.quantidade} AGUARDANDO
                    </span>
                `;
            }

            // 🆕 Adicionar indicador de pontuação
            flagsHtml += `
                <span style="font-size: 10px; color: #007bff; font-weight: 600; margin-right: 8px;
                             background: rgba(0, 123, 255, 0.1); padding: 2px 6px; border-radius: 10px;"
                      title="Progresso: ${pontuacao}/5 etapas concluídas (${porcentagem.toFixed(0)}%)">
                    ${pontuacao}/5
                </span>
            `;

            flags.forEach((flag, index) => {
                const statusClass = flag.completed ? 'completed' : 'pending';

                flagsHtml += `
                    <span class="progress-flag ${statusClass}"
                          title="${flag.label}${flag.completed ? ' ✅ Concluída' : ' ⏳ Pendente'}">
                        <i class="fas fa-${flag.icon}"></i>
                    </span>
                `;
            });

            flagsHtml += '</div>';
            return flagsHtml;
        }

        function calcularProgressoEtapas(op) {
            return [
                {
                    titulo: 'Material Transferido',
                    status: op.materialTransferido ? 'completed' : 'current'
                },
                {
                    titulo: 'Saldo Validado',
                    status: op.saldoValidado ? 'completed' : (op.materialTransferido ? 'current' : '')
                },
                {
                    titulo: 'OP Impressa',
                    status: op.impressa ? 'completed' : (op.saldoValidado ? 'current' : '')
                },
                {
                    titulo: 'Enviada Fábrica',
                    status: op.enviadaFabrica ? 'completed' : (op.impressa ? 'current' : '')
                },
                {
                    titulo: 'Produção Concluída',
                    status: op.status === 'Concluída' ? 'completed' : (op.enviadaFabrica ? 'current' : '')
                }
            ];
        }

        function calcularStatusMateriais(op) {
            if (!op.materiaisNecessarios) return [{ label: 'Sem materiais', class: 'badge-secondary' }];
            
            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });
            
            if (materiaisMP.length === 0) return [{ label: 'Sem MP', class: 'badge-info' }];
            
            const status = [];
            
            if (op.materialTransferido) {
                status.push({ label: 'Transferido', class: 'badge-success' });
            } else {
                status.push({ label: 'Pendente', class: 'badge-danger' });
            }
            
            if (op.saldoValidado) {
                status.push({ label: 'Validado', class: 'badge-success' });
            }
            
            return status;
        }

        // ===================================================================
        // FUNÇÕES DE FILTROS
        // ===================================================================
        
        // 🆕 Função para verificar se OP tem materiais aguardando
        function verificarMateriaisAguardando(op) {
            if (!op.materiaisNecessarios || !Array.isArray(op.materiaisNecessarios)) {
                return {
                    temAguardando: false,
                    quantidade: 0,
                    materiais: []
                };
            }

            // 🔄 Verificar tanto aguardandoMaterial quanto dataAguardando para compatibilidade
            const materiaisAguardando = op.materiaisNecessarios.filter(material => {
                // Material está aguardando se:
                // 1. aguardandoMaterial === true OU
                // 2. dataAguardando/dataAguardo existe e não é null
                return material.aguardandoMaterial === true ||
                       (material.dataAguardando && material.dataAguardando !== null) ||
                       (material.dataAguardo && material.dataAguardo !== null);
            });

            return {
                temAguardando: materiaisAguardando.length > 0,
                quantidade: materiaisAguardando.length,
                materiais: materiaisAguardando.map(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    return {
                        produtoId: material.produtoId,
                        codigo: produto?.codigo || 'N/A',
                        descricao: produto?.descricao || 'N/A',
                        dataAguardo: material.dataAguardo || material.dataAguardando,
                        usuarioAguardo: material.usuarioAguardo || 'Sistema',
                        // 🆕 Status atual do material
                        statusAtual: material.aguardandoMaterial === true ? 'AGUARDANDO' :
                                   (material.dataAguardando || material.dataAguardo) ? 'AGUARDANDO' : 'DISPONIVEL'
                    };
                })
            };
        }

        // 🆕 Função para detectar mudanças no status de materiais aguardando
        function detectarMudancasStatusMaterial(opAnterior, opAtual) {
            if (!opAnterior || !opAtual) return null;

            const aguardandoAnterior = verificarMateriaisAguardando(opAnterior);
            const aguardandoAtual = verificarMateriaisAguardando(opAtual);

            // Se havia materiais aguardando e agora não há mais
            if (aguardandoAnterior.temAguardando && !aguardandoAtual.temAguardando) {
                return {
                    tipo: 'LIBERADO',
                    mensagem: `✅ MATERIAIS LIBERADOS!\n\nOP: ${opAtual.numero || opAtual.numeroOP}\n\n🎉 Todos os materiais estão disponíveis!\nA OP pode prosseguir para as próximas etapas.`,
                    op: opAtual
                };
            }

            // Se não havia materiais aguardando e agora há
            if (!aguardandoAnterior.temAguardando && aguardandoAtual.temAguardando) {
                return {
                    tipo: 'BLOQUEADO',
                    mensagem: `⚠️ DEPENDÊNCIA DE MATERIAL!\n\nOP: ${opAtual.numero || opAtual.numeroOP}\n\n${aguardandoAtual.quantidade} material(is) aguardando chegada.\nA OP foi movida para o final da lista.`,
                    op: opAtual
                };
            }

            // Se mudou a quantidade de materiais aguardando
            if (aguardandoAnterior.quantidade !== aguardandoAtual.quantidade) {
                return {
                    tipo: 'ALTERADO',
                    mensagem: `🔄 STATUS ALTERADO!\n\nOP: ${opAtual.numero || opAtual.numeroOP}\n\nMateriais aguardando: ${aguardandoAnterior.quantidade} → ${aguardandoAtual.quantidade}`,
                    op: opAtual
                };
            }

            return null;
        }

        // 🆕 Função para calcular pontuação de progresso (número de flags acionados)
        function calcularPontuacaoProgresso(op) {
            let pontuacao = 0;

            // Flag 1: Material Transferido
            if (op.materialTransferido) pontuacao += 1;

            // Flag 2: Saldo Validado
            if (op.saldoValidado) pontuacao += 1;

            // Flag 3: OP Impressa
            if (op.impressa) pontuacao += 1;

            // Flag 4: Enviada para Fábrica
            if (op.enviadaFabrica) pontuacao += 1;

            // Flag 5: Apontamentos Feitos (OP Concluída)
            if (op.status === 'Concluída') pontuacao += 1;

            return pontuacao;
        }

        // 🆕 Função para classificar OPs por progresso (mais flags primeiro)
        function classificarOPsPorProgresso(ops) {
            return ops.sort((a, b) => {
                // 🚨 PRIMEIRO CRITÉRIO: OPs SEM materiais aguardando têm prioridade
                const aguardandoA = verificarMateriaisAguardando(a);
                const aguardandoB = verificarMateriaisAguardando(b);

                if (aguardandoA.temAguardando !== aguardandoB.temAguardando) {
                    // OPs sem materiais aguardando primeiro (false < true)
                    return aguardandoA.temAguardando - aguardandoB.temAguardando;
                }

                // 🏆 SEGUNDO CRITÉRIO: Mais flags acionados primeiro
                const pontuacaoA = calcularPontuacaoProgresso(a);
                const pontuacaoB = calcularPontuacaoProgresso(b);

                if (pontuacaoB !== pontuacaoA) {
                    return pontuacaoB - pontuacaoA;
                }

                // 📊 TERCEIRO CRITÉRIO: Por status (prioridade)
                const statusPrioridade = {
                    'Em Produção': 5,
                    'Liberada': 4,
                    'Planejada': 3,
                    'Pendente': 2,
                    'Concluída': 1,
                    'Cancelada': 0
                };

                const prioridadeA = statusPrioridade[a.status] || 1;
                const prioridadeB = statusPrioridade[b.status] || 1;

                if (prioridadeB !== prioridadeA) {
                    return prioridadeB - prioridadeA;
                }

                // 📅 QUARTO CRITÉRIO: Por data de criação (mais recente primeiro)
                const dataA = a.dataCriacao?.seconds || 0;
                const dataB = b.dataCriacao?.seconds || 0;

                return dataB - dataA;
            });
        }

        function aplicarFiltrosOPs() {
            let ops = [...ordensProducao];

            // 🆕 FILTRO PADRÃO: Excluir OPs concluídas e canceladas por padrão
            const filtroStatus = document.getElementById('filtroStatus')?.value;
            if (!filtroStatus || filtroStatus === '') {
                // Filtro padrão: excluir OPs concluídas e canceladas
                ops = ops.filter(op => {
                    const status = op.status;
                    return status !== 'Concluída' &&
                           status !== 'Concluida' &&
                           status !== 'Cancelada' &&
                           status !== 'Cancelado';
                });
            } else if (filtroStatus === 'TODOS') {
                // Mostrar todas as OPs, incluindo concluídas e canceladas
                // Não aplicar nenhum filtro de status
            } else {
                // Filtro específico selecionado
                ops = ops.filter(op => op.status === filtroStatus);
            }

            // Filtro por material transferido
            const filtroMaterial = document.getElementById('filtroMaterial')?.value;
            if (filtroMaterial !== '') {
                const materialTransferido = filtroMaterial === 'true';
                ops = ops.filter(op => !!op.materialTransferido === materialTransferido);
            }

            // Busca por número da OP
            const buscaOP = document.getElementById('buscaOP')?.value?.toLowerCase();
            if (buscaOP) {
                ops = ops.filter(op =>
                    (op.numero || op.id).toLowerCase().includes(buscaOP)
                );
            }

            // Busca por código do produto
            const buscaProduto = document.getElementById('buscaProduto')?.value?.toLowerCase();
            if (buscaProduto) {
                ops = ops.filter(op => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    return produto && (
                        produto.codigo?.toLowerCase().includes(buscaProduto) ||
                        produto.descricao?.toLowerCase().includes(buscaProduto)
                    );
                });
            }

            // 🆕 CLASSIFICAR POR PROGRESSO (mais flags acionados primeiro)
            ops = classificarOPsPorProgresso(ops);

            return ops;
        }

        function aplicarFiltros() {
            renderizarOPs();
        }

        function limparFiltros() {
            // Limpar todos os filtros, mantendo o comportamento padrão de excluir concluídas/canceladas
            document.getElementById('filtroStatus').value = '';
            document.getElementById('filtroMaterial').value = '';
            document.getElementById('buscaOP').value = '';
            document.getElementById('buscaProduto').value = '';
            renderizarOPs();
        }

        // ===================================================================
        // MELHORIAS DE INTERFACE E UX
        // ===================================================================

        // Adicionar atalhos de teclado
        document.addEventListener('keydown', function(e) {
            // ESC para fechar modais
            if (e.key === 'Escape') {
                const modais = document.querySelectorAll('.modal');
                modais.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                    }
                });
            }

            // Ctrl+F para focar na busca
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('buscaOP').focus();
            }

            // F5 para sincronizar
            if (e.key === 'F5') {
                e.preventDefault();
                sincronizarTodosSistemas();
            }
        });

        // Melhorar feedback visual
        function adicionarFeedbackVisual() {
            // Adicionar loading states nos botões
            const botoes = document.querySelectorAll('.btn');
            botoes.forEach(botao => {
                botao.addEventListener('click', function() {
                    if (!this.disabled) {
                        this.style.opacity = '0.7';
                        setTimeout(() => {
                            this.style.opacity = '1';
                        }, 300);
                    }
                });
            });

            // Adicionar hover effects nos cards
            const cards = document.querySelectorAll('.op-card, .action-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }

        // Salvar preferências do usuário
        function salvarPreferencias() {
            const preferencias = {
                filtroStatus: document.getElementById('filtroStatus').value,
                filtroMaterial: document.getElementById('filtroMaterial').value,
                tipoVisualizacao: tipoVisualizacao,
                ultimaAtualizacao: new Date().toISOString()
            };

            localStorage.setItem('apontamentos_preferencias', JSON.stringify(preferencias));
        }

        function carregarPreferencias() {
            try {
                const preferencias = JSON.parse(localStorage.getItem('apontamentos_preferencias'));
                if (preferencias) {
                    document.getElementById('filtroStatus').value = preferencias.filtroStatus || '';
                    document.getElementById('filtroMaterial').value = preferencias.filtroMaterial || '';

                    // Carregar tipo de visualização
                    if (preferencias.tipoVisualizacao) {
                        tipoVisualizacao = preferencias.tipoVisualizacao;
                        alternarVisualizacao(tipoVisualizacao);
                    }
                }
            } catch (error) {
                console.warn('Erro ao carregar preferências:', error);
            }
        }

        // Auto-save das preferências
        document.getElementById('filtroStatus').addEventListener('change', salvarPreferencias);
        document.getElementById('filtroMaterial').addEventListener('change', salvarPreferencias);

        // Adicionar tooltips informativos
        function adicionarTooltips() {
            const elementos = [
                { selector: '.step.completed', texto: 'Etapa concluída' },
                { selector: '.step.pending', texto: 'Próxima etapa' },
                { selector: '.step.disabled', texto: 'Etapa bloqueada' },
                { selector: '.material-badge.status-ok', texto: 'Material disponível' },
                { selector: '.material-badge.status-warning', texto: 'Material parcialmente disponível' },
                { selector: '.material-badge.status-error', texto: 'Material indisponível' }
            ];

            elementos.forEach(({ selector, texto }) => {
                const els = document.querySelectorAll(selector);
                els.forEach(el => {
                    if (!el.title) {
                        el.title = texto;
                    }
                });
            });
        }

        // Função para atualizar contadores em tempo real
        function atualizarContadores() {
            const opsAtivas = ordensProducao.filter(op =>
                op.status !== 'Cancelada' && op.status !== 'Concluída'
            ).length;

            const opsPendentes = ordensProducao.filter(op =>
                !op.materialTransferido
            ).length;

            const opsProducao = ordensProducao.filter(op =>
                op.status === 'Em Produção'
            ).length;

            // 🆕 Contadores por progresso (flags acionados)
            const progressoContadores = {
                flags0: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 0).length,
                flags1: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 1).length,
                flags2: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 2).length,
                flags3: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 3).length,
                flags4: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 4).length,
                flags5: ordensProducao.filter(op => calcularPontuacaoProgresso(op) === 5).length
            };

            // 🚨 Contadores de dependências de material
            const opsComAguardando = ordensProducao.filter(op => verificarMateriaisAguardando(op).temAguardando).length;
            const totalMateriaisAguardando = ordensProducao.reduce((total, op) => {
                return total + verificarMateriaisAguardando(op).quantidade;
            }, 0);

            // Atualizar título da página com informação de progresso e dependências
            const maisAvancadas = progressoContadores.flags4 + progressoContadores.flags5;
            const tituloBase = `🏭 Apontamentos (${opsAtivas} ativas, ${maisAvancadas} avançadas`;
            const tituloDependencias = opsComAguardando > 0 ? `, ⚠️ ${opsComAguardando} com dependências` : '';
            document.title = `${tituloBase}${tituloDependencias}) - Sistema ERP`;

            // Atualizar status de sincronização com informação de progresso
            const statusElement = document.getElementById('syncStatus');
            if (statusElement) {
                const agora = new Date();
                const ultimaSync = localStorage.getItem('ultimaSincronizacao');

                if (ultimaSync) {
                    const diffMinutos = Math.floor((agora - new Date(ultimaSync)) / 60000);
                    if (diffMinutos > 5) {
                        statusElement.className = 'sync-status syncing';
                        statusElement.innerHTML = '<i class="fas fa-clock"></i> Sincronização pendente';
                    } else {
                        const statusTexto = opsComAguardando > 0
                            ? `⚠️ ${opsComAguardando} OPs com dependências | 🚀 ${maisAvancadas} avançadas`
                            : `🚀 ${maisAvancadas} OPs avançadas (4-5 flags)`;

                        statusElement.innerHTML = `
                            <i class="fas fa-check-circle"></i>
                            Sincronizado |
                            <span style="font-size: 11px;">
                                ${statusTexto}
                            </span>
                        `;
                    }
                }
            }
        }

        // Executar melhorias quando a página carregar
        setTimeout(() => {
            adicionarFeedbackVisual();
            carregarPreferencias();
            adicionarTooltips();
            atualizarContadores();

            // Atualizar contadores a cada 30 segundos
            setInterval(atualizarContadores, 30000);
        }, 1000);

        // ===================================================================
        // 🚨 FUNÇÕES DE DEPENDÊNCIA DE MATERIAL
        // ===================================================================

        // Função para alertar sobre dependência de material
        window.alertarDependenciaMaterial = function(opId) {
            const ordem = ordensProducao.find(op => op.id === opId);
            if (!ordem) return;

            const aguardandoInfo = verificarMateriaisAguardando(ordem);
            const nomeOP = ordem.numero || ordem.numeroOP || opId;

            const materiaisTexto = aguardandoInfo.materiais.map(m =>
                `• ${m.codigo} - ${m.descricao}${m.dataAguardo ? `\n  Aguardando desde: ${new Date(m.dataAguardo.seconds * 1000).toLocaleDateString('pt-BR')}` : ''}`
            ).join('\n\n');

            alert(`🚨 DEPENDÊNCIA DE MATERIAL\n\nOP: ${nomeOP}\n\n⚠️ Esta OP não pode prosseguir pois há ${aguardandoInfo.quantidade} material(is) aguardando chegada:\n\n${materiaisTexto}\n\n💡 Acesse o sistema de Movimentação de Armazém para gerenciar o status dos materiais.`);
        };

        // Função para mostrar detalhes dos materiais aguardando
        window.mostrarDetalhesAguardando = function(opId) {
            const ordem = ordensProducao.find(op => op.id === opId);
            if (!ordem) return;

            const aguardandoInfo = verificarMateriaisAguardando(ordem);
            const nomeOP = ordem.numero || ordem.numeroOP || opId;

            let detalhes = `📋 MATERIAIS AGUARDANDO - OP ${nomeOP}\n\n`;

            aguardandoInfo.materiais.forEach((material, index) => {
                detalhes += `${index + 1}. ${material.codigo} - ${material.descricao}\n`;
                if (material.dataAguardo) {
                    const dataFormatada = new Date(material.dataAguardo.seconds * 1000).toLocaleDateString('pt-BR');
                    const horaFormatada = new Date(material.dataAguardo.seconds * 1000).toLocaleTimeString('pt-BR');
                    detalhes += `   📅 Aguardando desde: ${dataFormatada} às ${horaFormatada}\n`;
                }
                if (material.usuarioAguardo) {
                    detalhes += `   👤 Marcado por: ${material.usuarioAguardo}\n`;
                }
                detalhes += '\n';
            });

            detalhes += `💡 Para alterar o status destes materiais, acesse:\n"Movimentação de Armazém" → Selecionar OP → Botão "Aguardar"`;

            alert(detalhes);
        };

        // ===================================================================
        // 🔧 FUNÇÕES DE AÇÃO SEQUENCIAIS
        // ===================================================================

        // ETAPA 1: Transferir Material
        window.transferirMateriais = async function(opId) {
            try {
                const ordem = ordensProducao.find(op => op.id === opId);
                if (!ordem) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                const nomeOP = ordem.numero || ordem.numeroOP || opId;

                // 🚨 Verificar se há materiais aguardando
                const aguardandoInfo = verificarMateriaisAguardando(ordem);
                if (aguardandoInfo.temAguardando) {
                    const materiaisTexto = aguardandoInfo.materiais.map(m => `• ${m.codigo} - ${m.descricao}`).join('\n');
                    alert(`🚨 IMPOSSÍVEL TRANSFERIR!\n\nOP: ${nomeOP}\n\n⚠️ Há ${aguardandoInfo.quantidade} material(is) aguardando chegada:\n\n${materiaisTexto}\n\n💡 Resolva as dependências no sistema de Movimentação de Armazém antes de prosseguir.`);
                    return;
                }

                if (confirm(`🚚 TRANSFERIR MATERIAL\n\nOP: ${nomeOP}\n\n🔄 Confirma a transferência de todos os materiais para produção?\n\n✅ Todos os materiais estão disponíveis`)) {
                    // Marcar como transferido
                    const sucesso = await updateOPFlowStatus(opId, 'materialTransferido', true, {
                        dataTransferencia: Timestamp.now(),
                        usuario: localStorage.getItem('currentUser') || 'Sistema',
                        metodo: 'marcacao_integrada'
                    });

                    if (sucesso) {
                        alert(`✅ MATERIAL TRANSFERIDO!\n\nOP: ${nomeOP}\nStatus: Todos os materiais na produção\n\n🎯 PRÓXIMO PASSO: Verificar saldo no armazém de produção`);
                        mostrarNotificacao('✅ Material transferido completamente', 'success', 3000);
                        renderizarOPs();
                    }
                }
            } catch (error) {
                console.error('Erro ao transferir materiais:', error);
                alert('❌ Erro ao transferir materiais: ' + error.message);
            }
        };

        // ETAPA 2: Verificar Saldo
        window.verificarSaldo = async function(opId) {
            try {
                const ordem = ordensProducao.find(op => op.id === opId);
                if (!ordem) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                const nomeOP = ordem.numero || ordem.numeroOP || opId;

                if (confirm(`🔍 VERIFICAR SALDO\n\nOP: ${nomeOP}\n\n📊 Confirma a validação do saldo de materiais?`)) {
                    // Marcar saldo como validado
                    const sucesso = await updateOPFlowStatus(opId, 'saldoValidado', true, {
                        dataValidacao: Timestamp.now(),
                        usuario: localStorage.getItem('currentUser') || 'Sistema'
                    });

                    if (sucesso) {
                        alert(`✅ SALDO VALIDADO!\n\nOP: ${nomeOP}\n✅ Todos os materiais disponíveis\n\n🎯 PRÓXIMO PASSO: Imprimir a OP`);
                        mostrarNotificacao('✅ Saldo validado - Pode imprimir OP', 'success', 3000);
                        renderizarOPs();
                    }
                }
            } catch (error) {
                console.error('Erro ao verificar saldo:', error);
                alert('❌ Erro ao verificar saldo: ' + error.message);
            }
        };

        // ETAPA 3: Imprimir OP
        window.imprimirOP = async function(opId) {
            try {
                const ordem = ordensProducao.find(op => op.id === opId);
                if (!ordem) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                const nomeOP = ordem.numero || ordem.numeroOP || opId;

                if (confirm(`🖨️ IMPRIMIR OP\n\nOP: ${nomeOP}\n\n📄 Confirma a impressão da ordem de produção?`)) {
                    // Marcar como impressa
                    const sucesso = await updateOPFlowStatus(opId, 'impressa', true, {
                        dataImpressao: Timestamp.now(),
                        usuario: localStorage.getItem('currentUser') || 'Sistema'
                    });

                    if (sucesso) {
                        alert(`✅ OP IMPRESSA!\n\nOP: ${nomeOP}\n🖨️ Documento enviado para impressão\n\n🎯 PRÓXIMO PASSO: Enviar para fábrica`);
                        mostrarNotificacao('✅ OP impressa - Pode enviar para fábrica', 'success', 3000);

                        // Abrir janela de impressão (opcional)
                        const abrirImpressao = confirm('Deseja abrir a janela de impressão agora?');
                        if (abrirImpressao) {
                            window.print();
                        }

                        renderizarOPs();
                    }
                }
            } catch (error) {
                console.error('Erro ao imprimir OP:', error);
                alert('❌ Erro ao imprimir OP: ' + error.message);
            }
        };

        // ETAPA 4: Enviar para Fábrica
        window.enviarFabrica = async function(opId) {
            try {
                const ordem = ordensProducao.find(op => op.id === opId);
                if (!ordem) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                const nomeOP = ordem.numero || ordem.numeroOP || opId;

                if (confirm(`🏭 ENVIAR PARA FÁBRICA\n\nOP: ${nomeOP}\n\n✅ Saldo validado\n✅ OP impressa\n\n🚀 Confirma o envio para a fábrica?`)) {
                    // Marcar como enviada para fábrica e alterar status
                    const sucesso1 = await updateOPFlowStatus(opId, 'enviadaFabrica', true, {
                        dataEnvio: Timestamp.now(),
                        usuario: localStorage.getItem('currentUser') || 'Sistema'
                    });
                    const sucesso2 = await updateOPFlowStatus(opId, 'status', 'Em Produção');

                    if (sucesso1 && sucesso2) {
                        alert(`✅ OP ENVIADA PARA FÁBRICA!\n\nOP: ${nomeOP}\nStatus: Em Produção\n\n🎯 PRÓXIMO PASSO: Fazer apontamentos`);
                        mostrarNotificacao('✅ OP enviada para fábrica - Pode apontar', 'success', 3000);
                        renderizarOPs();
                    }
                }
            } catch (error) {
                console.error('Erro ao enviar para fábrica:', error);
                alert('❌ Erro ao enviar para fábrica: ' + error.message);
            }
        };

        // ===================================================================
        // FUNÇÕES DE AÇÃO EXISTENTES
        // ===================================================================
        
        function abrirMovimentacao(opId = null) {
            const url = opId ? 
                `movimentacao_armazem_novo.html?op=${opId}` : 
                'movimentacao_armazem_novo.html';
            window.open(url, '_blank');
        }

        function transferirMateriais(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            // Verificar se há materiais para transferir
            if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                mostrarNotificacao('Esta OP não possui materiais para transferir!', 'warning');
                return;
            }

            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });

            if (materiaisMP.length === 0) {
                mostrarNotificacao('Esta OP não possui matérias-primas para transferir!', 'info');
                return;
            }

            // Verificar disponibilidade antes de transferir
            const problemasEstoque = verificarDisponibilidadeMateriais(op);

            if (problemasEstoque.length > 0) {
                let mensagem = '⚠️ Problemas de estoque encontrados:\n\n';
                problemasEstoque.forEach(problema => {
                    mensagem += `• ${problema.codigo}: Disponível ${problema.disponivel}, Necessário ${problema.necessario}\n`;
                });
                mensagem += '\nDeseja continuar mesmo assim?';

                if (!confirm(mensagem)) {
                    return;
                }
            }

            // Redirecionar para movimentação de armazém
            const url = `movimentacao_armazem_novo.html?op=${opId}&auto=true`;
            window.open(url, '_blank');
            mostrarNotificacao(`Abrindo movimentação para OP ${op.numero || op.numeroOP || opId}`, 'info');
        }

        function verificarDisponibilidadeMateriais(op) {
            const problemas = [];

            if (!op.materiaisNecessarios) return problemas;

            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });

            materiaisMP.forEach(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto) return;

                // Calcular saldo disponível considerando empenhos
                const saldoDisponivel = calcularSaldoDisponivelComEmpenhos(material.produtoId);
                const necessario = material.necessidade || material.quantidade || 0;

                if (saldoDisponivel < necessario) {
                    problemas.push({
                        produtoId: material.produtoId,
                        codigo: produto.codigo,
                        disponivel: saldoDisponivel,
                        necessario: necessario,
                        falta: necessario - saldoDisponivel
                    });
                }
            });

            return problemas;
        }

        function calcularSaldoDisponivelComEmpenhos(produtoId) {
            // Somar todos os estoques do produto (exceto armazém de produção)
            const estoquesTotal = estoques
                .filter(e => e.produtoId === produtoId && !e.armazemId.includes('PROD'))
                .reduce((total, e) => total + (e.saldo || 0), 0);

            // Calcular empenhos de todas as OPs ativas
            let totalEmpenhado = 0;
            ordensProducao.forEach(op => {
                if (op.status === 'Cancelada' || op.status === 'Concluída') return;

                if (op.materiaisNecessarios) {
                    const material = op.materiaisNecessarios.find(m => m.produtoId === produtoId);
                    if (material) {
                        const necessario = material.necessidade || material.quantidade || 0;
                        const jaTransferido = material.saldoReservado || 0;
                        const pendente = Math.max(0, necessario - jaTransferido);
                        totalEmpenhado += pendente;
                    }
                }
            });

            return Math.max(0, estoquesTotal - totalEmpenhado);
        }

        async function verificarSaldosGeral() {
            console.log('🔍 Verificando saldos de todas as OPs...');
            
            const opsComProblemas = [];
            
            for (const op of ordensProducao) {
                if (op.status === 'Concluída' || op.status === 'Cancelada') continue;
                
                const temProblemas = !verificarSaldoValidado(op);
                if (temProblemas) {
                    opsComProblemas.push(op);
                }
            }
            
            if (opsComProblemas.length === 0) {
                mostrarNotificacao('✅ Todos os saldos estão OK!', 'success');
            } else {
                mostrarNotificacao(`⚠️ ${opsComProblemas.length} OPs com problemas de saldo`, 'warning');
                // Aqui você pode abrir um modal com detalhes
            }
        }

        async function diagnosticarProblemas() {
            console.log('🔍 Iniciando diagnóstico completo do sistema...');

            const modalBody = document.getElementById('modalDiagnosticoBody');
            modalBody.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #3498db;"></i>
                    <h4 style="margin: 15px 0;">Executando Diagnóstico...</h4>
                    <p>Analisando sistema, aguarde...</p>
                </div>
            `;

            document.getElementById('modalDiagnostico').style.display = 'block';

            try {
                const diagnostico = await executarDiagnosticoCompleto();
                exibirResultadoDiagnostico(diagnostico);

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                modalBody.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                        <h4 style="margin: 15px 0; color: #e74c3c;">Erro no Diagnóstico</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function executarDiagnosticoCompleto() {
            const diagnostico = {
                timestamp: new Date().toLocaleString('pt-BR'),
                resumo: {
                    totalOPs: ordensProducao.length,
                    opsAtivas: ordensProducao.filter(op => op.status !== 'Cancelada' && op.status !== 'Concluída').length,
                    totalProdutos: produtos.length,
                    totalEstoques: estoques.length,
                    totalTransferencias: transferencias.length,
                    totalApontamentos: apontamentos.length
                },
                problemas: {
                    opsProblematicas: [],
                    materiaisIndisponiveis: [],
                    transferenciasOrfas: [],
                    inconsistenciasEstoque: []
                },
                recomendacoes: []
            };

            // 1. Analisar OPs problemáticas
            for (const op of ordensProducao) {
                if (op.status === 'Cancelada' || op.status === 'Concluída') continue;

                const problemasOP = analisarProblemasOP(op);
                if (problemasOP.length > 0) {
                    diagnostico.problemas.opsProblematicas.push({
                        id: op.id,
                        numero: op.numero || op.numeroOP,
                        problemas: problemasOP
                    });
                }
            }

            // 2. Verificar materiais indisponíveis
            const materiaisIndisponiveis = verificarMateriaisIndisponiveis();
            diagnostico.problemas.materiaisIndisponiveis = materiaisIndisponiveis;

            // 3. Gerar recomendações
            diagnostico.recomendacoes = gerarRecomendacoes(diagnostico.problemas);

            return diagnostico;
        }

        function analisarProblemasOP(op) {
            const problemas = [];

            if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                problemas.push('Sem materiais cadastrados');
            } else {
                const materiaisProblematicos = verificarDisponibilidadeMateriais(op);
                if (materiaisProblematicos.length > 0) {
                    problemas.push(`${materiaisProblematicos.length} materiais indisponíveis`);
                }
            }

            const produto = produtos.find(p => p.id === op.produtoId);
            if (!produto) {
                problemas.push('Produto não encontrado');
            }

            return problemas;
        }

        function verificarMateriaisIndisponiveis() {
            const materiaisIndisponiveis = [];
            const materiaisVerificados = new Set();

            ordensProducao.forEach(op => {
                if (op.status === 'Cancelada' || op.status === 'Concluída') return;
                if (!op.materiaisNecessarios) return;

                op.materiaisNecessarios.forEach(material => {
                    if (materiaisVerificados.has(material.produtoId)) return;
                    materiaisVerificados.add(material.produtoId);

                    const produto = produtos.find(p => p.id === material.produtoId);
                    if (!produto || produto.tipo !== 'MP') return;

                    const saldoDisponivel = calcularSaldoDisponivelComEmpenhos(material.produtoId);
                    const totalNecessario = calcularTotalNecessarioProduto(material.produtoId);

                    if (saldoDisponivel < totalNecessario) {
                        materiaisIndisponiveis.push({
                            produtoId: material.produtoId,
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            disponivel: saldoDisponivel,
                            necessario: totalNecessario,
                            falta: totalNecessario - saldoDisponivel
                        });
                    }
                });
            });

            return materiaisIndisponiveis;
        }

        function calcularTotalNecessarioProduto(produtoId) {
            let total = 0;

            ordensProducao.forEach(op => {
                if (op.status === 'Cancelada' || op.status === 'Concluída') return;
                if (!op.materiaisNecessarios) return;

                const material = op.materiaisNecessarios.find(m => m.produtoId === produtoId);
                if (material) {
                    const necessario = material.necessidade || material.quantidade || 0;
                    const jaTransferido = material.saldoReservado || 0;
                    total += Math.max(0, necessario - jaTransferido);
                }
            });

            return total;
        }

        function gerarRecomendacoes(problemas) {
            const recomendacoes = [];

            if (problemas.opsProblematicas.length > 0) {
                recomendacoes.push(`Revisar ${problemas.opsProblematicas.length} OPs com problemas`);
            }

            if (problemas.materiaisIndisponiveis.length > 0) {
                recomendacoes.push(`Providenciar compra de ${problemas.materiaisIndisponiveis.length} materiais`);
            }

            if (recomendacoes.length === 0) {
                recomendacoes.push('Sistema funcionando corretamente');
            }

            return recomendacoes;
        }

        function exibirResultadoDiagnostico(diagnostico) {
            const modalBody = document.getElementById('modalDiagnosticoBody');

            let html = `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">
                        <i class="fas fa-chart-line"></i> Resumo do Sistema
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #3498db;">${diagnostico.resumo.totalOPs}</div>
                            <div style="font-size: 12px; color: #6c757d;">Total OPs</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #f39c12;">${diagnostico.resumo.opsAtivas}</div>
                            <div style="font-size: 12px; color: #6c757d;">OPs Ativas</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #27ae60;">${diagnostico.resumo.totalProdutos}</div>
                            <div style="font-size: 12px; color: #6c757d;">Produtos</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #9b59b6;">${diagnostico.resumo.totalApontamentos}</div>
                            <div style="font-size: 12px; color: #6c757d;">Apontamentos</div>
                        </div>
                    </div>
                </div>
            `;

            // Problemas encontrados
            const totalProblemas = diagnostico.problemas.opsProblematicas.length +
                                 diagnostico.problemas.materiaisIndisponiveis.length;

            if (totalProblemas > 0) {
                html += `
                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #e74c3c; margin-bottom: 15px;">
                            <i class="fas fa-exclamation-triangle"></i> Problemas Encontrados (${totalProblemas})
                        </h4>
                `;

                if (diagnostico.problemas.opsProblematicas.length > 0) {
                    html += `
                        <div style="margin-bottom: 15px;">
                            <h5 style="color: #2c3e50;">OPs Problemáticas:</h5>
                            ${diagnostico.problemas.opsProblematicas.map(op => `
                                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin-bottom: 8px;">
                                    <strong>OP ${op.numero}:</strong> ${op.problemas.join(', ')}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }

                if (diagnostico.problemas.materiaisIndisponiveis.length > 0) {
                    html += `
                        <div style="margin-bottom: 15px;">
                            <h5 style="color: #2c3e50;">Materiais Indisponíveis:</h5>
                            ${diagnostico.problemas.materiaisIndisponiveis.map(material => `
                                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 10px; margin-bottom: 8px;">
                                    <strong>${material.codigo}:</strong> Falta ${material.falta} (tem ${material.disponivel}, precisa ${material.necessario})
                                </div>
                            `).join('')}
                        </div>
                    `;
                }

                html += `</div>`;
            } else {
                html += `
                    <div style="text-align: center; padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; margin-bottom: 25px;">
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #155724; margin-bottom: 10px;"></i>
                        <h4 style="color: #155724; margin: 0;">Nenhum Problema Encontrado</h4>
                        <p style="color: #155724; margin: 5px 0 0 0;">Sistema funcionando corretamente!</p>
                    </div>
                `;
            }

            // Recomendações
            html += `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">
                        <i class="fas fa-lightbulb"></i> Recomendações
                    </h4>
                    ${diagnostico.recomendacoes.map(rec => `
                        <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 5px; padding: 10px; margin-bottom: 8px;">
                            <i class="fas fa-arrow-right" style="color: #0c5460; margin-right: 8px;"></i>
                            ${rec}
                        </div>
                    `).join('')}
                </div>
            `;

            html += `
                <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                    <small style="color: #6c757d;">Diagnóstico executado em ${diagnostico.timestamp}</small>
                </div>
            `;

            modalBody.innerHTML = html;
        }

        function relatorioIntegrado() {
            // Implementar relatório integrado
            mostrarNotificacao('Funcionalidade em desenvolvimento', 'info');
        }

        // ===================================================================
        // FUNÇÕES DE MODAL
        // ===================================================================
        
        async function abrirApontamento(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            // Validar se OP pode ser apontada
            if (op.status === 'Concluída') {
                mostrarNotificacao('OP já foi concluída!', 'warning');
                return;
            }

            if (op.status === 'Cancelada') {
                mostrarNotificacao('OP foi cancelada!', 'error');
                return;
            }

            currentOrder = op;

            // Buscar produto
            const produto = produtos.find(p => p.id === op.produtoId);
            if (!produto) {
                mostrarNotificacao('Produto não encontrado!', 'error');
                return;
            }

            // Buscar apontamentos existentes
            const apontamentosOP = apontamentos.filter(a => a.ordemId === opId);
            const totalProduzido = apontamentosOP.reduce((total, a) => total + (a.quantidade || 0), 0);
            const totalRefugo = apontamentosOP.reduce((total, a) => total + (a.refugo || 0), 0);
            const restante = Math.max(0, op.quantidade - totalProduzido);

            // Montar conteúdo do modal
            const modalBody = document.getElementById('modalApontamentoBody');
            modalBody.innerHTML = `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">
                        <i class="fas fa-industry"></i> Informações da OP
                    </h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div><strong>OP:</strong> ${op.numero || op.numeroOP || opId}</div>
                            <div><strong>Status:</strong> <span style="color: ${getStatusColor(op.status)}">${op.status}</span></div>
                            <div><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</div>
                            <div><strong>Quantidade:</strong> ${op.quantidade} ${produto.unidade || 'UN'}</div>
                            <div><strong>Produzido:</strong> ${totalProduzido} ${produto.unidade || 'UN'}</div>
                            <div><strong>Restante:</strong> ${restante} ${produto.unidade || 'UN'}</div>
                        </div>
                    </div>
                </div>

                <form id="formApontamento" onsubmit="return false;">
                    <div class="form-group">
                        <label for="quantidadeProduzida">
                            <i class="fas fa-check-circle"></i> Quantidade Produzida *
                        </label>
                        <input type="number"
                               id="quantidadeProduzida"
                               class="form-control"
                               step="0.001"
                               min="0"
                               max="${restante}"
                               placeholder="Digite a quantidade produzida"
                               required>
                        <small style="color: #6c757d;">Máximo disponível: ${restante} ${produto.unidade || 'UN'}</small>
                    </div>

                    <div class="form-group">
                        <label for="quantidadeRefugo">
                            <i class="fas fa-exclamation-triangle"></i> Quantidade de Refugo
                        </label>
                        <input type="number"
                               id="quantidadeRefugo"
                               class="form-control"
                               step="0.001"
                               min="0"
                               value="0"
                               placeholder="Digite a quantidade de refugo">
                    </div>

                    <div class="form-group">
                        <label for="observacoesApontamento">
                            <i class="fas fa-comment"></i> Observações
                        </label>
                        <textarea id="observacoesApontamento"
                                  class="form-control"
                                  rows="3"
                                  placeholder="Observações sobre a produção (opcional)"></textarea>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="fecharModal('modalApontamento')">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="button" class="btn btn-success" onclick="confirmarApontamento()">
                            <i class="fas fa-save"></i> Confirmar Apontamento
                        </button>
                    </div>
                </form>

                ${apontamentosOP.length > 0 ? `
                    <div style="margin-top: 30px; border-top: 1px solid #dee2e6; padding-top: 20px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">
                            <i class="fas fa-history"></i> Histórico de Apontamentos
                        </h5>
                        <div style="max-height: 200px; overflow-y: auto;">
                            ${apontamentosOP.map(apt => `
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 8px; font-size: 13px;">
                                    <div style="display: flex; justify-content: space-between;">
                                        <span><strong>Produzido:</strong> ${apt.quantidade} ${produto.unidade || 'UN'}</span>
                                        <span><strong>Data:</strong> ${apt.dataHora ? new Date(apt.dataHora.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}</span>
                                    </div>
                                    ${apt.refugo > 0 ? `<div><strong>Refugo:</strong> ${apt.refugo} ${produto.unidade || 'UN'}</div>` : ''}
                                    ${apt.observacoes ? `<div><strong>Obs:</strong> ${apt.observacoes}</div>` : ''}
                                    <div><strong>Usuário:</strong> ${apt.nomeUsuario || apt.usuario || 'N/A'}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;

            // Mostrar modal
            document.getElementById('modalApontamento').style.display = 'block';

            // Focar no campo de quantidade
            setTimeout(() => {
                document.getElementById('quantidadeProduzida').focus();
            }, 300);
        }

        function verMateriais(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            const modalBody = document.getElementById('modalMateriaisBody');

            if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                modalBody.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-box-open" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                        <h4 style="color: #7f8c8d;">Nenhum material necessário</h4>
                        <p style="color: #95a5a6;">Esta OP não possui materiais cadastrados</p>
                    </div>
                `;
                document.getElementById('modalMateriais').style.display = 'block';
                return;
            }

            // Separar materiais por tipo
            const materiaisMP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'MP';
            });

            const materiaisSP = op.materiaisNecessarios.filter(m => {
                const produto = produtos.find(p => p.id === m.produtoId);
                return produto && produto.tipo === 'SP';
            });

            let html = `
                <div style="margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">
                        <i class="fas fa-industry"></i> OP: ${op.numero || op.numeroOP || opId}
                    </h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <strong>Total de materiais:</strong> ${op.materiaisNecessarios.length}
                        (${materiaisMP.length} MP + ${materiaisSP.length} SP)
                    </div>
                </div>
            `;

            if (materiaisMP.length > 0) {
                html += `
                    <div style="margin-bottom: 30px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">
                            <i class="fas fa-cogs"></i> Matérias-Primas (${materiaisMP.length})
                        </h5>
                        ${materiaisMP.map(material => criarItemMaterial(material, op)).join('')}
                    </div>
                `;
            }

            if (materiaisSP.length > 0) {
                html += `
                    <div style="margin-bottom: 30px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">
                            <i class="fas fa-puzzle-piece"></i> Sub-Produtos (${materiaisSP.length})
                        </h5>
                        ${materiaisSP.map(material => criarItemMaterial(material, op)).join('')}
                    </div>
                `;
            }

            modalBody.innerHTML = html;
            document.getElementById('modalMateriais').style.display = 'block';
        }

        function criarItemMaterial(material, op) {
            const produto = produtos.find(p => p.id === material.produtoId);
            if (!produto) return '';

            const necessario = material.necessidade || material.quantidade || 0;
            const transferido = material.saldoReservado || 0;
            const percentualTransferido = necessario > 0 ? Math.round((transferido / necessario) * 100) : 0;

            // Verificar estoque no armazém de produção
            const armazemProducao = op.armazemProducaoId || 'PROD1';
            const estoque = estoques.find(e =>
                e.produtoId === material.produtoId &&
                e.armazemId === armazemProducao
            );
            const saldoProducao = estoque ? (estoque.saldo || 0) : 0;

            let statusClass = 'status-error';
            let statusText = 'Pendente';

            if (transferido >= (necessario - 0.001)) {
                statusClass = 'status-ok';
                statusText = 'Completo';
            } else if (transferido > 0) {
                statusClass = 'status-warning';
                statusText = 'Parcial';
            }

            return `
                <div class="material-item">
                    <div class="material-header">
                        <div>
                            <strong>${produto.codigo}</strong> - ${produto.descricao}
                            <br><small style="color: #6c757d;">Tipo: ${produto.tipo} | Unidade: ${produto.unidade || 'UN'}</small>
                        </div>
                        <span class="material-status ${statusClass}">${statusText}</span>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 10px;">
                        <div>
                            <small style="color: #6c757d;">Necessário:</small><br>
                            <strong>${necessario} ${produto.unidade || 'UN'}</strong>
                        </div>
                        <div>
                            <small style="color: #6c757d;">Transferido:</small><br>
                            <strong style="color: ${transferido > 0 ? '#27ae60' : '#e74c3c'}">${transferido} ${produto.unidade || 'UN'}</strong>
                        </div>
                        <div>
                            <small style="color: #6c757d;">Saldo Produção:</small><br>
                            <strong style="color: ${saldoProducao >= necessario ? '#27ae60' : '#e74c3c'}">${saldoProducao} ${produto.unidade || 'UN'}</strong>
                        </div>
                    </div>

                    ${transferido > 0 ? `
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${percentualTransferido}%"></div>
                        </div>
                        <small style="color: #6c757d;">${percentualTransferido}% transferido</small>
                    ` : ''}
                </div>
            `;
        }

        function fecharModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'modalApontamento') {
                currentOrder = null;
            }
        }

        // ===================================================================
        // FUNÇÕES DE WORKFLOW
        // ===================================================================

        async function verificarSaldo(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            if (!op.materialTransferido) {
                mostrarNotificacao('Primeiro transfira os materiais!', 'warning');
                return;
            }

            try {
                // Verificar saldo no armazém de produção
                const armazemProducao = op.armazemProducaoId || 'PROD1';
                const materiaisMP = op.materiaisNecessarios?.filter(m => {
                    const produto = produtos.find(p => p.id === m.produtoId);
                    return produto && produto.tipo === 'MP';
                }) || [];

                let todosOK = true;
                const problemas = [];

                for (const material of materiaisMP) {
                    const estoque = estoques.find(e =>
                        e.produtoId === material.produtoId &&
                        e.armazemId === armazemProducao
                    );

                    const saldoDisponivel = estoque ? (estoque.saldo || 0) : 0;
                    const necessario = material.necessidade || material.quantidade || 0;

                    if (saldoDisponivel < necessario) {
                        todosOK = false;
                        const produto = produtos.find(p => p.id === material.produtoId);
                        problemas.push({
                            codigo: produto?.codigo || material.produtoId,
                            necessario: necessario,
                            disponivel: saldoDisponivel,
                            falta: necessario - saldoDisponivel
                        });
                    }
                }

                if (todosOK) {
                    // Marcar saldo como validado
                    await updateDoc(doc(db, "ordensProducao", opId), {
                        saldoValidado: true,
                        dataValidacaoSaldo: Timestamp.now()
                    });

                    mostrarNotificacao('✅ Saldo validado com sucesso!', 'success');
                    await carregarDados();
                    renderizarOPs();
                } else {
                    let mensagem = '❌ Saldo insuficiente:\n\n';
                    problemas.forEach(p => {
                        mensagem += `• ${p.codigo}: Falta ${p.falta} (tem ${p.disponivel}, precisa ${p.necessario})\n`;
                    });

                    alert(mensagem);
                }

            } catch (error) {
                console.error('Erro ao verificar saldo:', error);
                mostrarNotificacao('Erro ao verificar saldo: ' + error.message, 'error');
            }
        }

        async function imprimirOP(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            if (!op.saldoValidado) {
                mostrarNotificacao('Primeiro valide o saldo!', 'warning');
                return;
            }

            try {
                // Marcar como impressa
                await updateDoc(doc(db, "ordensProducao", opId), {
                    impressa: true,
                    dataImpressao: Timestamp.now()
                });

                // Simular impressão (aqui você pode integrar com sistema de impressão real)
                mostrarNotificacao('✅ OP marcada como impressa!', 'success');

                // Abrir janela de impressão (opcional)
                const confirmar = confirm('OP marcada como impressa!\n\nDeseja abrir a janela de impressão?');
                if (confirmar) {
                    window.open(`print_op.html?id=${opId}`, '_blank');
                }

                await carregarDados();
                renderizarOPs();

            } catch (error) {
                console.error('Erro ao imprimir OP:', error);
                mostrarNotificacao('Erro ao imprimir OP: ' + error.message, 'error');
            }
        }

        async function enviarFabrica(opId) {
            const op = ordensProducao.find(o => o.id === opId);
            if (!op) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            if (!op.impressa) {
                mostrarNotificacao('Primeiro imprima a OP!', 'warning');
                return;
            }

            try {
                // Marcar como enviada para fábrica e alterar status
                await updateDoc(doc(db, "ordensProducao", opId), {
                    enviadaFabrica: true,
                    dataEnvioFabrica: Timestamp.now(),
                    status: 'Em Produção'
                });

                mostrarNotificacao('✅ OP enviada para fábrica!', 'success');
                await carregarDados();
                renderizarOPs();

            } catch (error) {
                console.error('Erro ao enviar para fábrica:', error);
                mostrarNotificacao('Erro ao enviar para fábrica: ' + error.message, 'error');
            }
        }

        function getStatusColor(status) {
            switch (status) {
                case 'Pendente': return '#f39c12';
                case 'Em Produção': return '#3498db';
                case 'Concluída': return '#27ae60';
                case 'Cancelada': return '#e74c3c';
                default: return '#6c757d';
            }
        }

        async function confirmarApontamento() {
            if (!currentOrder) {
                mostrarNotificacao('Nenhuma OP selecionada!', 'error');
                return;
            }

            const quantidade = parseFloat(document.getElementById('quantidadeProduzida').value) || 0;
            const refugo = parseFloat(document.getElementById('quantidadeRefugo').value) || 0;
            const observacoes = document.getElementById('observacoesApontamento').value.trim();

            // Validações
            if (quantidade <= 0) {
                mostrarNotificacao('Quantidade produzida deve ser maior que zero!', 'error');
                document.getElementById('quantidadeProduzida').focus();
                return;
            }

            if (refugo < 0) {
                mostrarNotificacao('Quantidade de refugo não pode ser negativa!', 'error');
                document.getElementById('quantidadeRefugo').focus();
                return;
            }

            // Verificar se não excede o restante
            const apontamentosOP = apontamentos.filter(a => a.ordemId === currentOrder.id);
            const totalProduzido = apontamentosOP.reduce((total, a) => total + (a.quantidade || 0), 0);
            const restante = Math.max(0, currentOrder.quantidade - totalProduzido);

            if (quantidade > restante) {
                mostrarNotificacao(`Quantidade excede o restante da OP! Máximo: ${restante}`, 'error');
                document.getElementById('quantidadeProduzida').focus();
                return;
            }

            try {
                // Desabilitar botão para evitar duplo clique
                const btnConfirmar = document.querySelector('#modalApontamento .btn-success');
                btnConfirmar.disabled = true;
                btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';

                await processarApontamento(quantidade, refugo, observacoes);

                // Fechar modal
                fecharModal('modalApontamento');

                // Recarregar dados
                await carregarDados();
                renderizarOPs();

                mostrarNotificacao('Apontamento realizado com sucesso!', 'success');

            } catch (error) {
                console.error('Erro ao processar apontamento:', error);
                mostrarNotificacao('Erro ao processar apontamento: ' + error.message, 'error');

                // Reabilitar botão
                const btnConfirmar = document.querySelector('#modalApontamento .btn-success');
                if (btnConfirmar) {
                    btnConfirmar.disabled = false;
                    btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Confirmar Apontamento';
                }
            }
        }

        async function processarApontamento(quantidade, refugo, observacoes) {
            const produto = produtos.find(p => p.id === currentOrder.produtoId);
            if (!produto) {
                throw new Error('Produto não encontrado');
            }

            // Usar transação para garantir consistência
            await runTransaction(db, async (transaction) => {
                // 1. Registrar apontamento
                const apontamentoRef = doc(collection(db, "apontamentos"));
                const novoApontamento = {
                    ordemId: currentOrder.id,
                    numeroOrdem: currentOrder.numero || currentOrder.numeroOP,
                    produtoId: currentOrder.produtoId,
                    quantidade: quantidade,
                    refugo: refugo,
                    observacoes: observacoes,
                    usuario: currentUser.email,
                    nomeUsuario: currentUser.nome,
                    dataHora: Timestamp.now()
                };

                transaction.set(apontamentoRef, novoApontamento);

                // 2. Atualizar OP
                const opRef = doc(db, "ordensProducao", currentOrder.id);
                const novaQuantidadeProduzida = (currentOrder.quantidadeProduzida || 0) + quantidade;
                const novoRefugoTotal = (currentOrder.refugoTotal || 0) + refugo;

                let novoStatus = currentOrder.status;
                if (novaQuantidadeProduzida >= currentOrder.quantidade) {
                    novoStatus = 'Concluída';
                }

                transaction.update(opRef, {
                    quantidadeProduzida: novaQuantidadeProduzida,
                    refugoTotal: novoRefugoTotal,
                    status: novoStatus,
                    ultimoApontamento: Timestamp.now()
                });

                // 3. Atualizar estoque do produto acabado
                if (quantidade > 0) {
                    const armazemAcabados = currentOrder.armazemAcabadosId || 'ACABADOS';
                    const estoqueId = `${currentOrder.produtoId}_${armazemAcabados}`;
                    const estoqueRef = doc(db, "estoques", estoqueId);

                    // Buscar estoque atual
                    const estoqueDoc = await transaction.get(estoqueRef);
                    const saldoAtual = estoqueDoc.exists() ? (estoqueDoc.data().saldo || 0) : 0;

                    transaction.set(estoqueRef, {
                        produtoId: currentOrder.produtoId,
                        armazemId: armazemAcabados,
                        saldo: saldoAtual + quantidade,
                        ultimaMovimentacao: Timestamp.now()
                    }, { merge: true });

                    // 4. Registrar movimentação de entrada
                    const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                    transaction.set(movimentacaoRef, {
                        produtoId: currentOrder.produtoId,
                        armazemId: armazemAcabados,
                        tipo: 'ENTRADA',
                        quantidade: quantidade,
                        tipoDocumento: 'APONTAMENTO',
                        numeroDocumento: currentOrder.numero || currentOrder.numeroOP,
                        observacoes: `Apontamento de produção - OP ${currentOrder.numero || currentOrder.numeroOP}`,
                        dataHora: Timestamp.now(),
                        usuario: currentUser.nome,
                        apontamentoId: apontamentoRef.id
                    });
                }

                console.log('✅ Apontamento processado com sucesso');
            });
        }

        // ===================================================================
        // FUNÇÕES UTILITÁRIAS
        // ===================================================================
        
        function atualizarStatusSincronizacao(status) {
            const elemento = document.getElementById('syncStatus');
            if (!elemento) return;
            
            elemento.className = `sync-status ${status}`;
            
            const statusTexts = {
                connected: '<i class="fas fa-check-circle"></i> Sincronizado',
                syncing: '<i class="fas fa-sync fa-spin"></i> Sincronizando...',
                disconnected: '<i class="fas fa-exclamation-triangle"></i> Desconectado',
                error: '<i class="fas fa-times-circle"></i> Erro'
            };
            
            elemento.innerHTML = statusTexts[status] || statusTexts.disconnected;
        }

        function mostrarNotificacao(mensagem, tipo = 'info', duracao = 3000) {
            const notification = document.createElement('div');
            notification.className = `notification ${tipo}`;
            notification.textContent = mensagem;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, duracao);
        }

        async function sincronizarAutomatico() {
            if (INTEGRATION_CONFIG.debugMode) {
                console.log('🔄 Sincronização automática executada');
            }
            
            try {
                // await carregarDados(); // Desabilitado para evitar loops
                renderizarOPs();
            } catch (error) {
                console.warn('⚠️ Erro na sincronização automática:', error);
            }
        }

        function salvarEstadoLocal() {
            const estado = {
                filtros: {
                    status: document.getElementById('filtroStatus')?.value,
                    material: document.getElementById('filtroMaterial')?.value,
                    busca: document.getElementById('buscaOP')?.value
                },
                timestamp: Date.now()
            };
            
            localStorage.setItem('apontamentos_estado', JSON.stringify(estado));
        }

        async function verificarConexaoMovimentacao() {
            // Implementar verificação de conexão
            return true;
        }

        // ===================================================================
        // EXPOSIÇÃO DE FUNÇÕES GLOBAIS
        // ===================================================================
        
        // Função para limpar listeners
        function limparListeners() {
            console.log('🧹 Limpando listeners Firebase...');
            unsubscribeFunctions.forEach(unsubscribe => {
                try {
                    unsubscribe();
                } catch (error) {
                    console.warn('⚠️ Erro ao limpar listener:', error);
                }
            });
            unsubscribeFunctions = [];
        }

        // Limpar listeners quando a página for fechada
        window.addEventListener('beforeunload', limparListeners);

        // ===================================================================
        // FUNÇÕES DE DEBUG PARA OPs
        // ===================================================================

        window.debugOPs = async function() {
            console.log('🔍 DEBUG - Verificando OPs...');

            // 1. Verificar variável global
            console.log('📊 OPs na variável global:', ordensProducao.length);
            if (ordensProducao.length > 0) {
                console.log('📋 Primeira OP:', ordensProducao[0]);
            }

            // 2. Verificar coleções possíveis
            const colecoes = ['ordensProducao', 'ordens_producao', 'ordensproducao', 'ops', 'production_orders'];

            for (const colecao of colecoes) {
                try {
                    const snapshot = await getDocs(collection(db, colecao));
                    console.log(`✅ ${colecao}: ${snapshot.docs.length} documentos`);

                    if (snapshot.docs.length > 0) {
                        console.log(`📋 Primeira OP de ${colecao}:`, snapshot.docs[0].data());

                        // Se encontrou OPs, tentar carregar manualmente
                        if (snapshot.docs.length > 0 && ordensProducao.length === 0) {
                            console.log(`🔄 Carregando OPs de ${colecao} manualmente...`);
                            ordensProducao = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                            renderizarOPs();
                            console.log('✅ OPs carregadas manualmente!');
                        }
                    }
                } catch (error) {
                    console.log(`❌ ${colecao}: ${error.message}`);
                }
            }

            // 3. Verificar renderização
            const grid = document.getElementById('opsGrid');
            console.log('🎨 Grid de OPs:', grid ? 'Encontrado' : 'Não encontrado');
            if (grid) {
                console.log('📄 Conteúdo do grid:', grid.innerHTML.length > 0 ? 'Tem conteúdo' : 'Vazio');
            }

            // 4. Forçar renderização
            console.log('🔄 Forçando renderização...');
            renderizarOPs();
        };

        window.criarOPTeste = async function() {
            console.log('🧪 Criando OP de teste...');

            try {
                const opTeste = {
                    numero: 'TEST001',
                    numeroOP: 'TEST001',
                    produtoId: 'PROD_TESTE',
                    quantidade: 100,
                    status: 'Pendente',
                    dataEntrega: new Date(),
                    materiaisNecessarios: [],
                    criadoEm: new Date(),
                    criadoPor: 'Sistema de Teste'
                };

                const docRef = await addDoc(collection(db, "ordensProducao"), opTeste);
                console.log('✅ OP de teste criada:', docRef.id);

                // Aguardar um pouco e verificar se apareceu
                setTimeout(() => {
                    console.log('📊 OPs após criar teste:', ordensProducao.length);
                    renderizarOPs();
                }, 2000);

            } catch (error) {
                console.error('❌ Erro ao criar OP de teste:', error);
            }
        };

        window.sincronizarTodosSistemas = sincronizarTodosSistemas;
        window.limparCacheFirebase = limparCacheFirebase;
        window.listarColecoes = listarColecoes;
        window.limparListeners = limparListeners;
        window.abrirMovimentacao = abrirMovimentacao;
        window.verificarSaldosGeral = verificarSaldosGeral;
        window.diagnosticarProblemas = diagnosticarProblemas;
        window.relatorioIntegrado = relatorioIntegrado;
        window.aplicarFiltros = aplicarFiltros;
        window.limparFiltros = limparFiltros;
        window.abrirApontamento = abrirApontamento;
        window.verMateriais = verMateriais;
        window.transferirMateriais = transferirMateriais;
        window.fecharModal = fecharModal;
        window.verificarSaldo = verificarSaldo;
        window.imprimirOP = imprimirOP;
        window.enviarFabrica = enviarFabrica;
        window.confirmarApontamento = confirmarApontamento;
        window.buscarOPsManualmente = buscarOPsManualmente;
        window.criarOPTesteSeNecessario = criarOPTesteSeNecessario;
        window.encontrarColecaoOPs = encontrarColecaoOPs;
        window.alternarVisualizacao = alternarVisualizacao;

        // Inicializar carregamento de dados quando Firebase estiver pronto
        setTimeout(async () => {
            if (window.db) {
                try {
                    await carregarDados();
                    console.log('✅ Dados carregados com sucesso!');
                } catch (error) {
                    console.error('❌ Erro ao carregar dados:', error);
                }
            }
        }, 2000);
    </script>
</body>
</html>

