<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title><PERSON>erifica<PERSON> as Solicitaç<PERSON><PERSON></title>
  <script type="module">
    import { db } from '/js/firebase-config.js';
    import { getDocs, collection, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    function formatValue(value) {
      if (value instanceof Timestamp) {
        return value.toDate().toLocaleString();
      }
      if (typeof value === 'object' && value !== null) {
        return renderObject(value);
      }
      return value;
    }

    function renderObject(obj) {
      if (Array.isArray(obj)) {
        return `<ul style="margin-left:20px;">` + obj.map(item => `<li>${formatValue(item)}</li>`).join('') + `</ul>`;
      }
      if (typeof obj === 'object' && obj !== null) {
        return `<ul style="margin-left:20px;">` +
          Object.entries(obj).map(([k, v]) =>
            `<li><strong>${k}:</strong> ${formatValue(v)}</li>`
          ).join('') +
        `</ul>`;
      }
      return obj;
    }

    async function buscarTodasSolicitacoes() {
      const resultadoDiv = document.getElementById('resultado');
      resultadoDiv.innerHTML = 'Buscando...';
      try {
        const snap = await getDocs(collection(db, 'solicitacoesCompra'));
        if (snap.empty) {
          resultadoDiv.textContent = 'Nenhuma solicitação encontrada.';
          return;
        }
        let html = '';
        snap.forEach(docSnap => {
          const solicitacao = docSnap.data();
          html += `<div style="border:1px solid #ccc; margin-bottom:30px; padding:10px;">
            <strong>ID:</strong> ${docSnap.id}
            <div>${renderObject(solicitacao)}</div>
          </div>`;
        });
        resultadoDiv.innerHTML = html;
      } catch (e) {
        resultadoDiv.textContent = 'Erro ao buscar solicitações: ' + e.message;
      }
    }
    window.buscarTodasSolicitacoes = buscarTodasSolicitacoes;
  </script>
</head>
<body>
  <h2>Verificar Todas as Solicitações</h2>
  <button onclick="buscarTodasSolicitacoes()">Buscar Todas</button>
  <div id="resultado" style="margin-top:20px;"></div>
</body>
</html> 