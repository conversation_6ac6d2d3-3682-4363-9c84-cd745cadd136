modules = ["web", "nodejs-20"]
run = "npm run start"

[nix]
channel = "stable-24_05"

[deployment]
run = ["sh", "-c", "npm run start"]

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 8080
externalPort = 80

[[ports]]
localPort = 39617
externalPort = 3001

[workflows]
runButton = "Iniciar Servidor"

[[workflows.workflow]]
name = "Iniciar Servidor"
author = 42521700
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx serve . -p 5000"

[[workflows.workflow]]
name = "<PERSON>vidor Estático"
author = 42521700
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx serve . -p 5000"
