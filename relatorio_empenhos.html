<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Relatório <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .content {
            padding: 30px;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group input,
        .filter-group select {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .stat-card p {
            opacity: 0.9;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #34495e;
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 10px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-ativo {
            background: #d4edda;
            color: #155724;
        }

        .status-consumido {
            background: #cce5ff;
            color: #004085;
        }

        .status-liberado {
            background: #fff3cd;
            color: #856404;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading i {
            font-size: 3em;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }

        .origem-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .origem-transferencia {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .origem-reserva {
            background: #fff3e0;
            color: #f57c00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-lock"></i>
                RELATÓRIO DE EMPENHOS
            </h1>
            <p>Controle de materiais empenhados por ordem de produção</p>
        </div>

        <div class="content">
            <!-- Filtros -->
            <div class="filters">
                <div class="filter-group">
                    <label>🔍 Buscar OP:</label>
                    <input type="text" id="searchOP" placeholder="Ex: OP25070821" oninput="aplicarFiltros()">
                </div>
                <div class="filter-group">
                    <label>📦 Produto:</label>
                    <input type="text" id="searchProduto" placeholder="Código ou descrição" oninput="aplicarFiltros()">
                </div>
                <div class="filter-group">
                    <label>📊 Status:</label>
                    <select id="filterStatus" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                        <option value="ATIVO">Ativo</option>
                        <option value="CONSUMIDO">Consumido</option>
                        <option value="LIBERADO">Liberado</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>🏭 Armazém:</label>
                    <select id="filterArmazem" onchange="aplicarFiltros()">
                        <option value="">Todos</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>🔗 Origem:</label>
                    <select id="filterOrigem" onchange="aplicarFiltros()">
                        <option value="">Todas</option>
                        <option value="transferencia">Transferência</option>
                        <option value="reserva">Reserva</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>📅 Período:</label>
                    <select id="filterPeriodo" onchange="aplicarFiltros()">
                        <option value="hoje">Hoje</option>
                        <option value="semana">Esta semana</option>
                        <option value="mes">Este mês</option>
                        <option value="todos">Todos</option>
                    </select>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="stats">
                <div class="stat-card">
                    <h3 id="totalEmpenhos">0</h3>
                    <p>Total de Empenhos</p>
                </div>
                <div class="stat-card success">
                    <h3 id="empenhosAtivos">0</h3>
                    <p>Empenhos Ativos</p>
                </div>
                <div class="stat-card warning">
                    <h3 id="empenhosConsumidos">0</h3>
                    <p>Empenhos Consumidos</p>
                </div>
                <div class="stat-card danger">
                    <h3 id="empenhosLiberados">0</h3>
                    <p>Empenhos Liberados</p>
                </div>
            </div>

            <!-- Tabela de Empenhos -->
            <div class="table-container">
                <div id="loading" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Carregando empenhos...</p>
                </div>

                <table class="table" id="empenhosTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>OP</th>
                            <th>Produto</th>
                            <th>Armazém</th>
                            <th>Qtd Empenhada</th>
                            <th>Qtd Consumida</th>
                            <th>Restante</th>
                            <th>% Consumo</th>
                            <th>Status</th>
                            <th>Origem</th>
                            <th>Data Empenho</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="empenhosTableBody">
                    </tbody>
                </table>

                <div id="emptyState" class="empty-state" style="display: none;">
                    <i class="fas fa-inbox"></i>
                    <h3>Nenhum empenho encontrado</h3>
                    <p>Não há empenhos que correspondam aos filtros aplicados</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, onSnapshot, query, where, orderBy, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Dados globais
        let empenhos = [];
        let ordensProducao = [];
        let produtos = [];
        let armazens = [];
        let empenhosFiltered = [];

        // Inicializar
        window.addEventListener('DOMContentLoaded', async () => {
            await carregarDados();
            setupListeners();
        });

        async function carregarDados() {
            try {
                console.log('🔄 Carregando dados...');

                // Carregar dados em paralelo
                const [empenhosSnap, opsSnap, produtosSnap, armazensSnap] = await Promise.all([
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens"))
                ]);

                empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ Dados carregados: ${empenhos.length} empenhos, ${ordensProducao.length} OPs`);

                popularFiltros();
                aplicarFiltros();
                document.getElementById('loading').style.display = 'none';
                document.getElementById('empenhosTable').style.display = 'table';

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                document.getElementById('loading').innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Erro ao carregar dados: ${error.message}</p>
                `;
            }
        }

        function popularFiltros() {
            // Popular filtro de armazéns
            const filterArmazem = document.getElementById('filterArmazem');
            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                filterArmazem.appendChild(option);
            });
        }

        function setupListeners() {
            // Listener em tempo real para empenhos
            onSnapshot(collection(db, "empenhos"), (snapshot) => {
                empenhos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                aplicarFiltros();
            });
        }

        window.aplicarFiltros = function() {
            const searchOP = document.getElementById('searchOP').value.toLowerCase();
            const searchProduto = document.getElementById('searchProduto').value.toLowerCase();
            const filterStatus = document.getElementById('filterStatus').value;
            const filterArmazem = document.getElementById('filterArmazem').value;
            const filterOrigem = document.getElementById('filterOrigem').value;
            const filterPeriodo = document.getElementById('filterPeriodo').value;

            empenhosFiltered = empenhos.filter(empenho => {
                // Filtro por OP
                const op = ordensProducao.find(o => o.id === empenho.ordemProducaoId);
                const opMatch = !searchOP || (op?.numero?.toLowerCase().includes(searchOP));

                // Filtro por produto
                const produto = produtos.find(p => p.id === empenho.produtoId);
                const produtoMatch = !searchProduto || 
                    (produto?.codigo?.toLowerCase().includes(searchProduto)) ||
                    (produto?.descricao?.toLowerCase().includes(searchProduto));

                // Filtro por status
                const statusMatch = !filterStatus || empenho.status === filterStatus;

                // Filtro por armazém
                const armazemMatch = !filterArmazem || empenho.armazemId === filterArmazem;

                // Filtro por origem
                let origemMatch = true;
                if (filterOrigem === 'transferencia') {
                    origemMatch = empenho.origemTransferencia && !empenho.origemReserva;
                } else if (filterOrigem === 'reserva') {
                    origemMatch = empenho.origemReserva === true;
                }

                // Filtro por período
                let periodoMatch = true;
                if (filterPeriodo !== 'todos' && empenho.dataEmpenho) {
                    const dataEmpenho = empenho.dataEmpenho.toDate ? empenho.dataEmpenho.toDate() : new Date(empenho.dataEmpenho.seconds * 1000);
                    const hoje = new Date();
                    
                    switch (filterPeriodo) {
                        case 'hoje':
                            const inicioHoje = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
                            periodoMatch = dataEmpenho >= inicioHoje;
                            break;
                        case 'semana':
                            const inicioSemana = new Date(hoje);
                            inicioSemana.setDate(hoje.getDate() - hoje.getDay());
                            periodoMatch = dataEmpenho >= inicioSemana;
                            break;
                        case 'mes':
                            const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
                            periodoMatch = dataEmpenho >= inicioMes;
                            break;
                    }
                }

                return opMatch && produtoMatch && statusMatch && armazemMatch && origemMatch && periodoMatch;
            });

            renderizarTabela();
            atualizarEstatisticas();
        };

        function renderizarTabela() {
            const tbody = document.getElementById('empenhosTableBody');
            const emptyState = document.getElementById('emptyState');
            const table = document.getElementById('empenhosTable');

            if (empenhosFiltered.length === 0) {
                table.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            table.style.display = 'table';
            emptyState.style.display = 'none';

            tbody.innerHTML = '';

            empenhosFiltered.forEach(empenho => {
                const op = ordensProducao.find(o => o.id === empenho.ordemProducaoId);
                const produto = produtos.find(p => p.id === empenho.produtoId);
                const armazem = armazens.find(a => a.id === empenho.armazemId);

                const quantidadeRestante = empenho.quantidadeEmpenhada - (empenho.quantidadeConsumida || 0);
                const percentualConsumo = ((empenho.quantidadeConsumida || 0) / empenho.quantidadeEmpenhada * 100).toFixed(1);

                const dataEmpenho = empenho.dataEmpenho ? 
                    (empenho.dataEmpenho.toDate ? empenho.dataEmpenho.toDate() : new Date(empenho.dataEmpenho.seconds * 1000)).toLocaleString('pt-BR') : 
                    'N/A';

                const origemBadge = empenho.origemReserva ? 
                    '<span class="origem-badge origem-reserva">RESERVA</span>' :
                    '<span class="origem-badge origem-transferencia">TRANSFERÊNCIA</span>';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${op?.numero || 'N/A'}</strong></td>
                    <td>
                        <strong>${produto?.codigo || 'N/A'}</strong><br>
                        <small>${produto?.descricao || 'N/A'}</small>
                    </td>
                    <td>${armazem?.codigo || 'N/A'}</td>
                    <td><strong>${empenho.quantidadeEmpenhada.toFixed(3)}</strong></td>
                    <td>${(empenho.quantidadeConsumida || 0).toFixed(3)}</td>
                    <td><strong>${quantidadeRestante.toFixed(3)}</strong></td>
                    <td>
                        ${percentualConsumo}%
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${percentualConsumo}%"></div>
                        </div>
                    </td>
                    <td><span class="status-badge status-${empenho.status.toLowerCase()}">${empenho.status}</span></td>
                    <td>${origemBadge}</td>
                    <td><small>${dataEmpenho}</small></td>
                    <td>
                        <button class="btn btn-primary" onclick="verDetalhes('${empenho.id}')">
                            <i class="fas fa-eye"></i> Ver
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function atualizarEstatisticas() {
            const total = empenhosFiltered.length;
            const ativos = empenhosFiltered.filter(e => e.status === 'ATIVO').length;
            const consumidos = empenhosFiltered.filter(e => e.status === 'CONSUMIDO').length;
            const liberados = empenhosFiltered.filter(e => e.status === 'LIBERADO').length;

            document.getElementById('totalEmpenhos').textContent = total;
            document.getElementById('empenhosAtivos').textContent = ativos;
            document.getElementById('empenhosConsumidos').textContent = consumidos;
            document.getElementById('empenhosLiberados').textContent = liberados;
        }

        window.verDetalhes = function(empenhoId) {
            const empenho = empenhos.find(e => e.id === empenhoId);
            if (!empenho) return;

            const op = ordensProducao.find(o => o.id === empenho.ordemProducaoId);
            const produto = produtos.find(p => p.id === empenho.produtoId);
            const armazem = armazens.find(a => a.id === empenho.armazemId);

            alert(`🔒 DETALHES DO EMPENHO

📋 OP: ${op?.numero || 'N/A'}
📦 Produto: ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}
🏭 Armazém: ${armazem?.codigo || 'N/A'} - ${armazem?.nome || 'N/A'}

📊 QUANTIDADES:
• Empenhada: ${empenho.quantidadeEmpenhada.toFixed(3)}
• Consumida: ${(empenho.quantidadeConsumida || 0).toFixed(3)}
• Restante: ${(empenho.quantidadeEmpenhada - (empenho.quantidadeConsumida || 0)).toFixed(3)}

📅 DATAS:
• Empenho: ${empenho.dataEmpenho ? (empenho.dataEmpenho.toDate ? empenho.dataEmpenho.toDate() : new Date(empenho.dataEmpenho.seconds * 1000)).toLocaleString('pt-BR') : 'N/A'}
• Último Consumo: ${empenho.ultimoConsumo ? (empenho.ultimoConsumo.toDate ? empenho.ultimoConsumo.toDate() : new Date(empenho.ultimoConsumo.seconds * 1000)).toLocaleString('pt-BR') : 'N/A'}

🔗 ORIGEM: ${empenho.origemReserva ? 'Reserva (Método Antigo)' : 'Transferência (Novo Método)'}
📝 OBSERVAÇÕES: ${empenho.observacoes || 'Nenhuma'}

🆔 ID: ${empenho.id}`);
        };
    </script>
</body>
</html>
