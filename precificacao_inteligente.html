<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Precificação Inteligente - Sistema TOTVS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --totvs-primary: #0854a0;
            --totvs-secondary: #f0f3f6;
            --totvs-success: #107e3e;
            --totvs-warning: #e9730c;
            --totvs-danger: #bb0000;
            --totvs-info: #17a2b8;
            --totvs-border: #d4d4d4;
            --totvs-text: #333;
            --totvs-bg: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: var(--totvs-bg);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--totvs-primary) 0%, #0a4d8c 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--totvs-border);
        }

        .card h3 {
            color: var(--totvs-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            border-bottom: 2px solid var(--totvs-secondary);
            padding-bottom: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--totvs-text);
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-control {
            padding: 12px;
            border: 2px solid var(--totvs-border);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--totvs-primary);
            box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--totvs-primary);
            color: white;
        }

        .btn-success {
            background: var(--totvs-success);
            color: white;
        }

        .btn-warning {
            background: var(--totvs-warning);
            color: white;
        }

        .btn-danger {
            background: var(--totvs-danger);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .cost-breakdown {
            background: var(--totvs-secondary);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }

        .cost-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 16px;
            color: var(--totvs-primary);
        }

        .margin-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }

        .margin-excellent {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .margin-good {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .margin-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .margin-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .price-suggestion {
            background: linear-gradient(135deg, var(--totvs-info) 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .price-suggestion h4 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .price-suggestion .price {
            font-size: 32px;
            font-weight: 700;
            margin: 10px 0;
        }

        .competitive-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .competitor-card {
            background: white;
            border: 1px solid var(--totvs-border);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .competitor-card h5 {
            color: var(--totvs-primary);
            margin-bottom: 10px;
        }

        .competitor-price {
            font-size: 20px;
            font-weight: 600;
            color: var(--totvs-success);
        }

        .approval-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }

        .approval-approved {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .approval-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .approval-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .history-table th,
        .history-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--totvs-border);
        }

        .history-table th {
            background: var(--totvs-secondary);
            font-weight: 600;
            color: var(--totvs-text);
        }

        .currency-input {
            position: relative;
        }

        .currency-input::before {
            content: "R$";
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 600;
            z-index: 1;
        }

        .currency-input input {
            padding-left: 35px;
        }

        .percentage-input {
            position: relative;
        }

        .percentage-input::after {
            content: "%";
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 600;
        }

        .percentage-input input {
            padding-right: 25px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .chart-container {
            height: 300px;
            background: var(--totvs-secondary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--totvs-success);
        }

        .notification-error {
            background: var(--totvs-danger);
        }

        .notification-info {
            background: var(--totvs-primary);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--totvs-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-brain"></i>
                Precificação Inteligente
            </h1>
            <div>
                <button class="btn btn-success" onclick="calcularPrecificacao()">
                    <i class="fas fa-calculator"></i> Calcular
                </button>
                <button class="btn btn-primary" onclick="salvarPrecificacao()">
                    <i class="fas fa-save"></i> Salvar
                </button>
                <a href="index.html" class="btn btn-warning">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Dados do Produto -->
            <div class="card">
                <h3><i class="fas fa-box"></i> Dados do Produto</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label><i class="fas fa-search"></i> Produto</label>
                        <select id="produtoSelect" class="form-control" onchange="carregarDadosProduto()">
                            <option value="">Selecione um produto...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-user"></i> Cliente</label>
                        <select id="clienteSelect" class="form-control" onchange="aplicarAjusteCliente()">
                            <option value="">Selecione um cliente...</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label><i class="fas fa-hashtag"></i> Quantidade</label>
                        <input type="number" id="quantidade" class="form-control" value="1" min="1" onchange="recalcular()">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-calendar"></i> Data Entrega</label>
                        <input type="date" id="dataEntrega" class="form-control" onchange="aplicarSazonalidade()">
                    </div>
                </div>

                <!-- Breakdown de Custos -->
                <div class="cost-breakdown">
                    <h4><i class="fas fa-chart-pie"></i> Breakdown de Custos</h4>
                    <div class="cost-item">
                        <span>Custo Material:</span>
                        <span id="custoMaterial">R$ 0,00</span>
                    </div>
                    <div class="cost-item">
                        <span>Custo Mão de Obra:</span>
                        <span id="custoMaoObra">R$ 0,00</span>
                    </div>
                    <div class="cost-item">
                        <span>Custos Indiretos:</span>
                        <span id="custosIndiretos">R$ 0,00</span>
                    </div>
                    <div class="cost-item">
                        <span>Overhead:</span>
                        <span id="overhead">R$ 0,00</span>
                    </div>
                    <div class="cost-item">
                        <span><strong>Custo Total:</strong></span>
                        <span id="custoTotal"><strong>R$ 0,00</strong></span>
                    </div>
                </div>
            </div>

            <!-- Análise de Precificação -->
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> Análise de Precificação</h3>
                
                <div class="form-row">
                    <div class="form-group percentage-input">
                        <label><i class="fas fa-percentage"></i> Margem Desejada</label>
                        <input type="number" id="margemDesejada" class="form-control" value="25" min="0" max="100" onchange="recalcular()">
                    </div>
                    <div class="form-group currency-input">
                        <label><i class="fas fa-dollar-sign"></i> Preço Sugerido</label>
                        <input type="number" id="precoSugerido" class="form-control" step="0.01" readonly>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group currency-input">
                        <label><i class="fas fa-edit"></i> Preço Final</label>
                        <input type="number" id="precoFinal" class="form-control" step="0.01" onchange="calcularMargemFinal()">
                    </div>
                    <div class="form-group percentage-input">
                        <label><i class="fas fa-chart-bar"></i> Margem Final</label>
                        <input type="number" id="margemFinal" class="form-control" readonly>
                    </div>
                </div>

                <!-- Indicador de Margem -->
                <div id="margemIndicator" class="margin-indicator margin-good">
                    <i class="fas fa-check-circle"></i>
                    <span>Margem adequada para aprovação automática</span>
                </div>

                <!-- Sugestão de Preço -->
                <div class="price-suggestion">
                    <h4><i class="fas fa-lightbulb"></i> Recomendação IA</h4>
                    <div class="price">R$ <span id="precoRecomendado">0,00</span></div>
                    <p id="justificativaPreco">Baseado na análise de mercado e histórico de vendas</p>
                </div>

                <!-- Status de Aprovação -->
                <div id="aprovacaoStatus" class="approval-status approval-approved">
                    <i class="fas fa-check-circle"></i>
                    <span>Aprovação automática - Margem dentro dos parâmetros</span>
                </div>
            </div>

            <!-- Análise Competitiva -->
            <div class="card">
                <h3><i class="fas fa-users"></i> Análise Competitiva</h3>
                
                <div class="competitive-analysis">
                    <div class="competitor-card">
                        <h5>Concorrente A</h5>
                        <div class="competitor-price">R$ 145,00</div>
                        <small>Último preço conhecido</small>
                    </div>
                    <div class="competitor-card">
                        <h5>Concorrente B</h5>
                        <div class="competitor-price">R$ 152,00</div>
                        <small>Preço médio</small>
                    </div>
                    <div class="competitor-card">
                        <h5>Mercado</h5>
                        <div class="competitor-price">R$ 148,50</div>
                        <small>Preço médio geral</small>
                    </div>
                </div>

                <div class="chart-container">
                    <i class="fas fa-chart-area" style="font-size: 48px; margin-right: 15px;"></i>
                    Gráfico de Análise Competitiva
                </div>
            </div>

            <!-- Histórico de Preços -->
            <div class="card">
                <h3><i class="fas fa-history"></i> Histórico de Preços</h3>
                
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Preço</th>
                            <th>Margem</th>
                            <th>Usuário</th>
                            <th>Motivo</th>
                        </tr>
                    </thead>
                    <tbody id="historicoTableBody">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                Nenhum histórico disponível
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            Timestamp,
            query,
            orderBy,
            where
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

        // Variáveis globais
        let produtos = [];
        let clientes = [];
        let produtoSelecionado = null;
        let clienteSelecionado = null;
        let configuracoes = {
            margemMinima: 15,
            margemIdeal: 25,
            overheadPadrao: 12,
            custosIndiretosPadrao: 8
        };

        // Inicialização
        document.addEventListener('DOMContentLoaded', async () => {
            await loadInitialData();
            setDefaultDate();
        });

        // Carregar dados iniciais
        async function loadInitialData() {
            try {
                showLoading(true);

                const [produtosSnap, clientesSnap] = await Promise.all([
                    getDocs(query(collection(db, "produtos"), orderBy("codigo"))),
                    getDocs(query(collection(db, "clientes"), orderBy("razaoSocial")))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                clientes = clientesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                populateSelects();

                console.log('Dados carregados:', {
                    produtos: produtos.length,
                    clientes: clientes.length
                });

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados do sistema', 'error');
            } finally {
                showLoading(false);
            }
        }

        // Popular selects
        function populateSelects() {
            // Produtos
            const produtoSelect = document.getElementById('produtoSelect');
            produtoSelect.innerHTML = '<option value="">Selecione um produto...</option>';

            produtos.forEach(produto => {
                const option = document.createElement('option');
                option.value = produto.id;
                option.textContent = `${produto.codigo} - ${produto.descricao}`;
                produtoSelect.appendChild(option);
            });

            // Clientes
            const clienteSelect = document.getElementById('clienteSelect');
            clienteSelect.innerHTML = '<option value="">Selecione um cliente...</option>';

            clientes.forEach(cliente => {
                const option = document.createElement('option');
                option.value = cliente.id;
                option.textContent = `${cliente.codigo || ''} - ${cliente.razaoSocial}`;
                clienteSelect.appendChild(option);
            });
        }

        // Definir data padrão
        function setDefaultDate() {
            const today = new Date();
            const futureDate = new Date(today.getTime() + (15 * 24 * 60 * 60 * 1000));
            document.getElementById('dataEntrega').value = futureDate.toISOString().split('T')[0];
        }

        // Carregar dados do produto
        window.carregarDadosProduto = function() {
            const produtoId = document.getElementById('produtoSelect').value;

            if (!produtoId) {
                produtoSelecionado = null;
                limparCalculos();
                return;
            }

            produtoSelecionado = produtos.find(p => p.id === produtoId);

            if (produtoSelecionado) {
                calcularCustos();
                carregarHistoricoPrecos();
                showNotification(`Produto ${produtoSelecionado.codigo} carregado`, 'info');
            }
        };

        // Aplicar ajuste de cliente
        window.aplicarAjusteCliente = function() {
            const clienteId = document.getElementById('clienteSelect').value;

            if (!clienteId) {
                clienteSelecionado = null;
                return;
            }

            clienteSelecionado = clientes.find(c => c.id === clienteId);
            recalcular();
        };

        // Aplicar sazonalidade
        window.aplicarSazonalidade = function() {
            recalcular();
        };

        // Calcular custos do produto
        function calcularCustos() {
            if (!produtoSelecionado) return;

            const quantidade = parseFloat(document.getElementById('quantidade').value) || 1;

            // Custos base do produto
            const custoMaterial = (produtoSelecionado.custoMaterial || produtoSelecionado.custoMedio || 0) * quantidade;
            const custoMaoObra = (produtoSelecionado.custoMaoObra || 0) * quantidade;

            // Custos indiretos (% sobre custo direto)
            const custosDiretos = custoMaterial + custoMaoObra;
            const custosIndiretos = custosDiretos * (configuracoes.custosIndiretosPadrao / 100);

            // Overhead (% sobre custo total)
            const custoSemOverhead = custosDiretos + custosIndiretos;
            const overhead = custoSemOverhead * (configuracoes.overheadPadrao / 100);

            const custoTotal = custoSemOverhead + overhead;

            // Atualizar interface
            document.getElementById('custoMaterial').textContent = `R$ ${custoMaterial.toFixed(2)}`;
            document.getElementById('custoMaoObra').textContent = `R$ ${custoMaoObra.toFixed(2)}`;
            document.getElementById('custosIndiretos').textContent = `R$ ${custosIndiretos.toFixed(2)}`;
            document.getElementById('overhead').textContent = `R$ ${overhead.toFixed(2)}`;
            document.getElementById('custoTotal').textContent = `R$ ${custoTotal.toFixed(2)}`;

            return {
                custoMaterial,
                custoMaoObra,
                custosIndiretos,
                overhead,
                custoTotal
            };
        }

        // Recalcular precificação
        window.recalcular = function() {
            if (!produtoSelecionado) return;

            const custos = calcularCustos();
            const margemDesejada = parseFloat(document.getElementById('margemDesejada').value) || 25;

            // Calcular preço sugerido
            const precoSugerido = custos.custoTotal / (1 - margemDesejada / 100);

            // Aplicar ajustes
            const precoComAjustes = aplicarAjustes(precoSugerido);

            // Atualizar interface
            document.getElementById('precoSugerido').value = precoSugerido.toFixed(2);
            document.getElementById('precoFinal').value = precoComAjustes.toFixed(2);

            calcularMargemFinal();
            atualizarIndicadorMargem();
            atualizarStatusAprovacao();
            gerarRecomendacaoIA();
        };

        // Aplicar ajustes de cliente, sazonalidade, etc.
        function aplicarAjustes(precoBase) {
            let precoAjustado = precoBase;

            // Ajuste por cliente
            if (clienteSelecionado) {
                const ajusteCliente = clienteSelecionado.ajustePreco || 0;
                precoAjustado *= (1 + ajusteCliente / 100);
            }

            // Ajuste por sazonalidade
            const dataEntrega = new Date(document.getElementById('dataEntrega').value);
            const ajusteSazonalidade = calcularAjusteSazonalidade(dataEntrega);
            precoAjustado *= (1 + ajusteSazonalidade / 100);

            return precoAjustado;
        }

        // Calcular ajuste de sazonalidade
        function calcularAjusteSazonalidade(data) {
            const mes = data.getMonth() + 1;

            // Exemplo: dezembro tem demanda maior (+5%), janeiro menor (-3%)
            const ajustesPorMes = {
                1: -3, 2: 0, 3: 2, 4: 1, 5: 0, 6: -1,
                7: 1, 8: 2, 9: 1, 10: 3, 11: 4, 12: 5
            };

            return ajustesPorMes[mes] || 0;
        }

        // Calcular margem final
        window.calcularMargemFinal = function() {
            if (!produtoSelecionado) return;

            const custos = calcularCustos();
            const precoFinal = parseFloat(document.getElementById('precoFinal').value) || 0;

            if (precoFinal > 0) {
                const margemFinal = ((precoFinal - custos.custoTotal) / precoFinal) * 100;
                document.getElementById('margemFinal').value = margemFinal.toFixed(2);

                atualizarIndicadorMargem();
                atualizarStatusAprovacao();
            }
        };

        // Atualizar indicador de margem
        function atualizarIndicadorMargem() {
            const margemFinal = parseFloat(document.getElementById('margemFinal').value) || 0;
            const indicator = document.getElementById('margemIndicator');

            indicator.className = 'margin-indicator';

            if (margemFinal >= configuracoes.margemIdeal) {
                indicator.classList.add('margin-excellent');
                indicator.innerHTML = '<i class="fas fa-star"></i><span>Margem excelente - Muito competitivo</span>';
            } else if (margemFinal >= configuracoes.margemMinima) {
                indicator.classList.add('margin-good');
                indicator.innerHTML = '<i class="fas fa-check-circle"></i><span>Margem adequada - Dentro dos parâmetros</span>';
            } else if (margemFinal >= configuracoes.margemMinima * 0.8) {
                indicator.classList.add('margin-warning');
                indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>Margem baixa - Requer aprovação</span>';
            } else {
                indicator.classList.add('margin-danger');
                indicator.innerHTML = '<i class="fas fa-times-circle"></i><span>Margem insuficiente - Não recomendado</span>';
            }
        }

        // Atualizar status de aprovação
        function atualizarStatusAprovacao() {
            const margemFinal = parseFloat(document.getElementById('margemFinal').value) || 0;
            const precoFinal = parseFloat(document.getElementById('precoFinal').value) || 0;
            const status = document.getElementById('aprovacaoStatus');

            status.className = 'approval-status';

            if (margemFinal >= configuracoes.margemMinima && precoFinal <= 10000) {
                status.classList.add('approval-approved');
                status.innerHTML = '<i class="fas fa-check-circle"></i><span>Aprovação automática - Margem e valor adequados</span>';
            } else if (margemFinal >= configuracoes.margemMinima * 0.8) {
                status.classList.add('approval-pending');
                status.innerHTML = '<i class="fas fa-clock"></i><span>Aprovação necessária - Supervisor de vendas</span>';
            } else {
                status.classList.add('approval-rejected');
                status.innerHTML = '<i class="fas fa-times-circle"></i><span>Aprovação gerencial necessária - Margem muito baixa</span>';
            }
        }

        // Gerar recomendação IA
        function gerarRecomendacaoIA() {
            if (!produtoSelecionado) return;

            const custos = calcularCustos();
            const precoMercado = 148.50; // Simulado - viria de análise de mercado
            const precoOtimo = custos.custoTotal * 1.35; // 35% de margem ideal

            document.getElementById('precoRecomendado').textContent = precoOtimo.toFixed(2);

            let justificativa = '';
            if (precoOtimo < precoMercado * 0.95) {
                justificativa = 'Preço competitivo - Oportunidade de aumentar margem';
            } else if (precoOtimo > precoMercado * 1.05) {
                justificativa = 'Preço acima do mercado - Considere reduzir para competir';
            } else {
                justificativa = 'Preço equilibrado - Boa relação custo-benefício';
            }

            document.getElementById('justificativaPreco').textContent = justificativa;
        }

        // Carregar histórico de preços
        async function carregarHistoricoPrecos() {
            if (!produtoSelecionado) return;

            try {
                const historicoSnap = await getDocs(
                    query(
                        collection(db, "historicoPrecos"),
                        where("produtoId", "==", produtoSelecionado.id),
                        orderBy("dataAlteracao", "desc")
                    )
                );

                const historico = historicoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const tbody = document.getElementById('historicoTableBody');

                if (historico.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                Nenhum histórico disponível para este produto
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = '';

                historico.slice(0, 5).forEach(item => {
                    const row = document.createElement('tr');
                    const data = item.dataAlteracao?.toDate?.()?.toLocaleDateString('pt-BR') || 'N/A';

                    row.innerHTML = `
                        <td>${data}</td>
                        <td>R$ ${(item.preco || 0).toFixed(2)}</td>
                        <td>${(item.margem || 0).toFixed(1)}%</td>
                        <td>${item.usuario || 'Sistema'}</td>
                        <td>${item.motivo || 'Atualização automática'}</td>
                    `;

                    tbody.appendChild(row);
                });

            } catch (error) {
                console.error('Erro ao carregar histórico:', error);
            }
        }

        // Calcular precificação
        window.calcularPrecificacao = function() {
            if (!produtoSelecionado) {
                showNotification('Selecione um produto primeiro', 'error');
                return;
            }

            recalcular();
            showNotification('Precificação calculada com sucesso!', 'success');
        };

        // Salvar precificação
        window.salvarPrecificacao = async function() {
            if (!produtoSelecionado) {
                showNotification('Selecione um produto primeiro', 'error');
                return;
            }

            try {
                showLoading(true);

                const precoFinal = parseFloat(document.getElementById('precoFinal').value);
                const margemFinal = parseFloat(document.getElementById('margemFinal').value);

                if (!precoFinal || precoFinal <= 0) {
                    showNotification('Informe um preço válido', 'error');
                    return;
                }

                // Atualizar preço do produto
                await updateDoc(doc(db, "produtos", produtoSelecionado.id), {
                    precoVenda: precoFinal,
                    margemAtual: margemFinal,
                    dataUltimaAlteracaoPreco: Timestamp.now()
                });

                // Registrar no histórico
                await addDoc(collection(db, "historicoPrecos"), {
                    produtoId: produtoSelecionado.id,
                    preco: precoFinal,
                    margem: margemFinal,
                    usuario: 'usuario_atual', // Implementar autenticação
                    motivo: 'Atualização via precificação inteligente',
                    dataAlteracao: Timestamp.now()
                });

                showNotification('Precificação salva com sucesso!', 'success');
                await carregarHistoricoPrecos();

            } catch (error) {
                console.error('Erro ao salvar precificação:', error);
                showNotification('Erro ao salvar precificação: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Limpar cálculos
        function limparCalculos() {
            document.getElementById('custoMaterial').textContent = 'R$ 0,00';
            document.getElementById('custoMaoObra').textContent = 'R$ 0,00';
            document.getElementById('custosIndiretos').textContent = 'R$ 0,00';
            document.getElementById('overhead').textContent = 'R$ 0,00';
            document.getElementById('custoTotal').textContent = 'R$ 0,00';
            document.getElementById('precoSugerido').value = '';
            document.getElementById('precoFinal').value = '';
            document.getElementById('margemFinal').value = '';
            document.getElementById('precoRecomendado').textContent = '0,00';
        }

        // Funções utilitárias
        function showLoading(show) {
            document.getElementById('loading').classList.toggle('active', show);
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>
