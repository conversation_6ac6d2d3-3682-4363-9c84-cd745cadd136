
// ===================================================================
// SERVIÇO DE RASTREABILIDADE ENTRE DOCUMENTOS
// ===================================================================
// Garante que SC → CT → PC mantenham referências corretas
// ===================================================================

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    updateDoc,
    doc,
    query,
    where,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TraceabilityService {
    
    /**
     * Verifica e corrige rastreabilidade entre documentos
     */
    static async verificarRastreabilidade() {
        try {
            console.log('🔍 Iniciando verificação de rastreabilidade...');
            
            const problemas = [];
            
            // Carregar todos os documentos
            const [solicitacoes, cotacoes, pedidos] = await Promise.all([
                this.carregarDocumentos('solicitacoesCompra'),
                this.carregarDocumentos('cotacoes'),
                this.carregarDocumentos('pedidosCompra')
            ]);
            
            // Verificar SC → CT
            const problemasCotacoes = await this.verificarSolicitacoesCotacoes(solicitacoes, cotacoes);
            problemas.push(...problemasCotacoes);
            
            // Verificar CT → PC
            const problemasPedidos = await this.verificarCotacoesPedidos(cotacoes, pedidos);
            problemas.push(...problemasPedidos);
            
            // Verificar numeração sequencial
            const problemasNumeracao = await this.verificarNumeracaoSequencial(solicitacoes, cotacoes, pedidos);
            problemas.push(...problemasNumeracao);
            
            console.log(`✅ Verificação concluída: ${problemas.length} problemas encontrados`);
            return problemas;
            
        } catch (error) {
            console.error('❌ Erro na verificação de rastreabilidade:', error);
            throw error;
        }
    }
    
    /**
     * Carrega documentos de uma coleção
     */
    static async carregarDocumentos(colecao) {
        const snap = await getDocs(collection(db, colecao));
        return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }
    
    /**
     * Verifica rastreabilidade SC → CT
     */
    static async verificarSolicitacoesCotacoes(solicitacoes, cotacoes) {
        const problemas = [];
        
        for (const cotacao of cotacoes) {
            if (cotacao.solicitacaoId) {
                const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
                
                if (!solicitacao) {
                    problemas.push({
                        tipo: 'REFERENCIA_QUEBRADA',
                        documento: 'COTACAO',
                        id: cotacao.id,
                        numero: cotacao.numero,
                        problema: `Cotação ${cotacao.numero} referencia solicitação inexistente: ${cotacao.solicitacaoId}`,
                        solucao: 'Remover referência ou localizar solicitação correta'
                    });
                } else {
                    // Verificar se a solicitação conhece a cotação
                    if (!solicitacao.cotacoes || !solicitacao.cotacoes.includes(cotacao.id)) {
                        problemas.push({
                            tipo: 'REFERENCIA_UNIDIRECIONAL',
                            documento: 'SOLICITACAO',
                            id: solicitacao.id,
                            numero: solicitacao.numero,
                            problema: `Solicitação ${solicitacao.numero} não referencia cotação ${cotacao.numero}`,
                            solucao: 'Adicionar referência da cotação na solicitação'
                        });
                    }
                }
            }
        }
        
        return problemas;
    }
    
    /**
     * Verifica rastreabilidade CT → PC
     */
    static async verificarCotacoesPedidos(cotacoes, pedidos) {
        const problemas = [];
        
        for (const pedido of pedidos) {
            if (pedido.cotacaoId) {
                const cotacao = cotacoes.find(c => c.id === pedido.cotacaoId);
                
                if (!cotacao) {
                    problemas.push({
                        tipo: 'REFERENCIA_QUEBRADA',
                        documento: 'PEDIDO',
                        id: pedido.id,
                        numero: pedido.numero,
                        problema: `Pedido ${pedido.numero} referencia cotação inexistente: ${pedido.cotacaoId}`,
                        solucao: 'Remover referência ou localizar cotação correta'
                    });
                } else {
                    // Verificar se a cotação conhece o pedido
                    if (!cotacao.pedidos || !cotacao.pedidos.includes(pedido.id)) {
                        problemas.push({
                            tipo: 'REFERENCIA_UNIDIRECIONAL',
                            documento: 'COTACAO',
                            id: cotacao.id,
                            numero: cotacao.numero,
                            problema: `Cotação ${cotacao.numero} não referencia pedido ${pedido.numero}`,
                            solucao: 'Adicionar referência do pedido na cotação'
                        });
                    }
                }
            }
        }
        
        return problemas;
    }
    
    /**
     * Verifica numeração sequencial
     */
    static async verificarNumeracaoSequencial(solicitacoes, cotacoes, pedidos) {
        const problemas = [];
        
        // Verificar cada tipo de documento
        problemas.push(...this.verificarSequencia('SOLICITACAO', solicitacoes));
        problemas.push(...this.verificarSequencia('COTACAO', cotacoes));
        problemas.push(...this.verificarSequencia('PEDIDO', pedidos));
        
        return problemas;
    }
    
    /**
     * Verifica sequência de numeração de um tipo de documento
     */
    static verificarSequencia(tipo, documentos) {
        const problemas = [];
        
        // Agrupar por ano/mês
        const porAnoMes = {};
        
        documentos.forEach(doc => {
            if (!doc.numero) return;
            
            const match = doc.numero.match(/^([A-Z]+)-(\d{4})-(\d{4})$/);
            if (match) {
                const [, prefixo, anoMes, sequencia] = match;
                const key = `${prefixo}-${anoMes}`;
                
                if (!porAnoMes[key]) porAnoMes[key] = [];
                porAnoMes[key].push({
                    documento: doc,
                    sequencia: parseInt(sequencia),
                    numero: doc.numero
                });
            }
        });
        
        // Verificar sequências dentro de cada ano/mês
        Object.entries(porAnoMes).forEach(([chave, docs]) => {
            docs.sort((a, b) => a.sequencia - b.sequencia);
            
            for (let i = 0; i < docs.length; i++) {
                const esperado = i + 1;
                const atual = docs[i].sequencia;
                
                if (atual !== esperado) {
                    problemas.push({
                        tipo: 'NUMERACAO_INCORRETA',
                        documento: tipo,
                        id: docs[i].documento.id,
                        numero: docs[i].numero,
                        problema: `Sequência incorreta em ${chave}: esperado ${esperado}, encontrado ${atual}`,
                        solucao: `Renumerar para ${chave}-${esperado.toString().padStart(4, '0')}`
                    });
                }
            }
        });
        
        return problemas;
    }
    
    /**
     * Corrige problemas de rastreabilidade
     */
    static async corrigirProblemas(problemas) {
        try {
            console.log(`🔧 Iniciando correção de ${problemas.length} problemas...`);
            
            let corrigidos = 0;
            
            for (const problema of problemas) {
                try {
                    switch (problema.tipo) {
                        case 'REFERENCIA_UNIDIRECIONAL':
                            await this.corrigirReferenciaUnidirecional(problema);
                            corrigidos++;
                            break;
                            
                        case 'NUMERACAO_INCORRETA':
                            await this.corrigirNumeracao(problema);
                            corrigidos++;
                            break;
                            
                        case 'REFERENCIA_QUEBRADA':
                            console.warn(`⚠️ Referência quebrada requer intervenção manual: ${problema.numero}`);
                            break;
                    }
                } catch (error) {
                    console.error(`❌ Erro ao corrigir problema ${problema.numero}:`, error);
                }
            }
            
            console.log(`✅ Correção concluída: ${corrigidos}/${problemas.length} problemas corrigidos`);
            return corrigidos;
            
        } catch (error) {
            console.error('❌ Erro na correção de problemas:', error);
            throw error;
        }
    }
    
    /**
     * Corrige referência unidirecional
     */
    static async corrigirReferenciaUnidirecional(problema) {
        // Implementar correção de referências bidirecionais
        console.log(`🔧 Corrigindo referência unidirecional: ${problema.numero}`);
        
        // TODO: Implementar lógica específica baseada no tipo de problema
    }
    
    /**
     * Corrige numeração incorreta
     */
    static async corrigirNumeracao(problema) {
        const novoNumero = problema.solucao.split('para ')[1];
        
        await updateDoc(doc(db, this.getCollectionName(problema.documento), problema.id), {
            numero: novoNumero,
            numeroAnterior: problema.numero,
            corrigidoEm: Timestamp.now(),
            corrigidoPor: 'SISTEMA_RASTREABILIDADE'
        });
        
        console.log(`✅ Numeração corrigida: ${problema.numero} → ${novoNumero}`);
    }
    
    /**
     * Mapeia tipo de documento para nome da coleção
     */
    static getCollectionName(tipo) {
        const mapa = {
            'SOLICITACAO': 'solicitacoesCompra',
            'COTACAO': 'cotacoes',
            'PEDIDO': 'pedidosCompra'
        };
        return mapa[tipo];
    }
    
    /**
     * Gera relatório de rastreabilidade
     */
    static async gerarRelatorioRastreabilidade() {
        try {
            const problemas = await this.verificarRastreabilidade();
            
            const relatorio = {
                dataVerificacao: new Date(),
                totalProblemas: problemas.length,
                problemasPorTipo: this.agruparProblemasPorTipo(problemas),
                problemas: problemas,
                recomendacoes: this.gerarRecomendacoes(problemas)
            };
            
            return relatorio;
            
        } catch (error) {
            console.error('❌ Erro ao gerar relatório:', error);
            throw error;
        }
    }
    
    /**
     * Agrupa problemas por tipo
     */
    static agruparProblemasPorTipo(problemas) {
        const grupos = {};
        
        problemas.forEach(problema => {
            if (!grupos[problema.tipo]) grupos[problema.tipo] = 0;
            grupos[problema.tipo]++;
        });
        
        return grupos;
    }
    
    /**
     * Gera recomendações baseadas nos problemas encontrados
     */
    static gerarRecomendacoes(problemas) {
        const recomendacoes = [];
        
        const tipos = new Set(problemas.map(p => p.tipo));
        
        if (tipos.has('REFERENCIA_QUEBRADA')) {
            recomendacoes.push('Revisar e corrigir referências quebradas manualmente');
        }
        
        if (tipos.has('REFERENCIA_UNIDIRECIONAL')) {
            recomendacoes.push('Implementar referências bidirecionais entre documentos');
        }
        
        if (tipos.has('NUMERACAO_INCORRETA')) {
            recomendacoes.push('Usar apenas o serviço centralizado de numeração');
        }
        
        return recomendacoes;
    }
}

// Exportar para uso global
window.TraceabilityService = TraceabilityService;

console.log('🔗 Serviço de Rastreabilidade carregado');
