
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verificação de Rastreabilidade - WiZAR ERP</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --secondary-color: #f0f3f6;
      --success-color: #107e3e;
      --warning-color: #e9730c;
      --danger-color: #bb0000;
      --border-color: #d4d4d4;
      --text-color: #333;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
    }
    
    .container {
      width: 90%;
      max-width: 1400px;
      margin: 20px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .header {
      background-color: var(--primary-color);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .actions {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
    
    .btn-primary { background-color: var(--primary-color); color: white; }
    .btn-success { background-color: var(--success-color); color: white; }
    .btn-warning { background-color: var(--warning-color); color: white; }
    .btn-danger { background-color: var(--danger-color); color: white; }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }
    
    .stat-card {
      background: #fff;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 15px;
      text-align: center;
    }
    
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    
    .problems-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    
    .problems-table th,
    .problems-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }
    
    .problems-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
    }
    
    .problem-type {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .type-ref-quebrada { background-color: #ffeaea; color: var(--danger-color); }
    .type-ref-unidirecional { background-color: #fff3cd; color: var(--warning-color); }
    .type-numeracao { background-color: #e1ecf4; color: var(--primary-color); }
    
    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }
    
    .success-message {
      background-color: #e5f2e5;
      color: var(--success-color);
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    
    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      overflow: hidden;
      margin: 10px 0;
    }
    
    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      transition: width 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔗 Verificação de Rastreabilidade</h1>
      <button class="btn btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
    </div>
    
    <div class="actions">
      <button class="btn btn-primary" onclick="verificarRastreabilidade()">🔍 Verificar Rastreabilidade</button>
      <button class="btn btn-success" onclick="corrigirProblemas()" id="btnCorrigir" disabled>🔧 Corrigir Problemas</button>
      <button class="btn btn-warning" onclick="gerarRelatorio()">📊 Gerar Relatório</button>
      <button class="btn btn-danger" onclick="verificarNumeracao()">🔢 Verificar Numeração</button>
    </div>
    
    <div id="loading" class="loading" style="display: none;">
      <div>🔄 Processando...</div>
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
      </div>
    </div>
    
    <div id="successMessage" class="success-message" style="display: none;"></div>
    
    <div id="statsContainer" class="stats-grid" style="display: none;">
      <div class="stat-card">
        <div class="stat-number" id="totalSolicitacoes">0</div>
        <div class="stat-label">Solicitações</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="totalCotacoes">0</div>
        <div class="stat-label">Cotações</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="totalPedidos">0</div>
        <div class="stat-label">Pedidos</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="totalProblemas">0</div>
        <div class="stat-label">Problemas Encontrados</div>
      </div>
    </div>
    
    <div id="problemsContainer" style="display: none;">
      <h3>📋 Problemas Identificados</h3>
      <table class="problems-table">
        <thead>
          <tr>
            <th>Tipo</th>
            <th>Documento</th>
            <th>Número</th>
            <th>Problema</th>
            <th>Solução Sugerida</th>
          </tr>
        </thead>
        <tbody id="problemsTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { TraceabilityService } from './services/traceability-service.js';
    import { NumberGeneratorService } from './services/number-generator-service.js';

    let problemasEncontrados = [];

    window.verificarRastreabilidade = async function() {
      try {
        showLoading(true);
        updateProgress(10);
        
        console.log('🔍 Iniciando verificação de rastreabilidade...');
        
        updateProgress(30);
        const problemas = await TraceabilityService.verificarRastreabilidade();
        
        updateProgress(80);
        problemasEncontrados = problemas;
        
        updateProgress(100);
        showLoading(false);
        
        displayResults(problemas);
        
        if (problemas.length === 0) {
          showSuccess('✅ Nenhum problema de rastreabilidade encontrado! Sistema íntegro.');
        }
        
      } catch (error) {
        console.error('❌ Erro na verificação:', error);
        showLoading(false);
        alert('❌ Erro na verificação: ' + error.message);
      }
    };

    window.corrigirProblemas = async function() {
      if (problemasEncontrados.length === 0) {
        alert('❌ Nenhum problema para corrigir.');
        return;
      }

      if (confirm(`Deseja corrigir ${problemasEncontrados.length} problemas encontrados?`)) {
        try {
          showLoading(true);
          
          const corrigidos = await TraceabilityService.corrigirProblemas(problemasEncontrados);
          
          showLoading(false);
          showSuccess(`✅ ${corrigidos} problemas corrigidos com sucesso!`);
          
          // Reverificar após correção
          setTimeout(() => {
            verificarRastreabilidade();
          }, 1000);
          
        } catch (error) {
          console.error('❌ Erro na correção:', error);
          showLoading(false);
          alert('❌ Erro na correção: ' + error.message);
        }
      }
    };

    window.gerarRelatorio = async function() {
      try {
        showLoading(true);
        
        const relatorio = await TraceabilityService.gerarRelatorioRastreabilidade();
        
        showLoading(false);
        
        // Exibir relatório em nova janela
        const janela = window.open('', '_blank');
        janela.document.write(gerarHTMLRelatorio(relatorio));
        
      } catch (error) {
        console.error('❌ Erro ao gerar relatório:', error);
        showLoading(false);
        alert('❌ Erro ao gerar relatório: ' + error.message);
      }
    };

    window.verificarNumeracao = async function() {
      try {
        showLoading(true);
        
        const contadores = await NumberGeneratorService.getCountersInfo();
        
        showLoading(false);
        
        // Exibir informações dos contadores
        let info = '🔢 Informações dos Contadores:\n\n';
        
        Object.entries(contadores).forEach(([tipo, dados]) => {
          if (dados.exists !== false) {
            info += `${dados.config.description}:\n`;
            info += `  Último: ${dados.config.format.replace('{AAMM}', dados.yearMonth).replace('{NNNN}', dados.sequence.toString().padStart(4, '0'))}\n`;
            info += `  Próximo: ${dados.sequence + 1}\n`;
            info += `  Total gerado: ${dados.totalGenerated || 0}\n\n`;
          }
        });
        
        alert(info);
        
      } catch (error) {
        console.error('❌ Erro na verificação de numeração:', error);
        showLoading(false);
        alert('❌ Erro na verificação: ' + error.message);
      }
    };

    function displayResults(problemas) {
      // Exibir estatísticas
      document.getElementById('statsContainer').style.display = 'grid';
      document.getElementById('totalProblemas').textContent = problemas.length;
      
      // Exibir problemas
      if (problemas.length > 0) {
        document.getElementById('problemsContainer').style.display = 'block';
        document.getElementById('btnCorrigir').disabled = false;
        
        const tbody = document.getElementById('problemsTableBody');
        tbody.innerHTML = '';
        
        problemas.forEach(problema => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td><span class="problem-type ${getTypeClass(problema.tipo)}">${problema.tipo}</span></td>
            <td>${problema.documento}</td>
            <td>${problema.numero}</td>
            <td>${problema.problema}</td>
            <td>${problema.solucao}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        document.getElementById('problemsContainer').style.display = 'none';
        document.getElementById('btnCorrigir').disabled = true;
      }
    }

    function getTypeClass(tipo) {
      switch (tipo) {
        case 'REFERENCIA_QUEBRADA': return 'type-ref-quebrada';
        case 'REFERENCIA_UNIDIRECIONAL': return 'type-ref-unidirecional';
        case 'NUMERACAO_INCORRETA': return 'type-numeracao';
        default: return '';
      }
    }

    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
    }

    function updateProgress(percent) {
      document.getElementById('progressFill').style.width = percent + '%';
    }

    function showSuccess(message) {
      const element = document.getElementById('successMessage');
      element.textContent = message;
      element.style.display = 'block';
      setTimeout(() => {
        element.style.display = 'none';
      }, 5000);
    }

    function gerarHTMLRelatorio(relatorio) {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Relatório de Rastreabilidade</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { border-bottom: 2px solid #0854a0; padding-bottom: 10px; }
            .stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin: 20px 0; }
            .stat { background: #f5f5f5; padding: 15px; text-align: center; border-radius: 4px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>📊 Relatório de Rastreabilidade</h1>
            <p>Gerado em: ${relatorio.dataVerificacao.toLocaleString('pt-BR')}</p>
          </div>
          
          <div class="stats">
            <div class="stat">
              <strong>${relatorio.totalProblemas}</strong><br>
              Problemas Encontrados
            </div>
            ${Object.entries(relatorio.problemasPorTipo).map(([tipo, count]) => 
              `<div class="stat"><strong>${count}</strong><br>${tipo}</div>`
            ).join('')}
          </div>
          
          <h3>🔧 Recomendações:</h3>
          <ul>
            ${relatorio.recomendacoes.map(rec => `<li>${rec}</li>`).join('')}
          </ul>
          
          ${relatorio.problemas.length > 0 ? `
          <h3>📋 Detalhes dos Problemas:</h3>
          <table>
            <tr>
              <th>Tipo</th>
              <th>Documento</th>
              <th>Número</th>
              <th>Problema</th>
              <th>Solução</th>
            </tr>
            ${relatorio.problemas.map(p => `
            <tr>
              <td>${p.tipo}</td>
              <td>${p.documento}</td>
              <td>${p.numero}</td>
              <td>${p.problema}</td>
              <td>${p.solucao}</td>
            </tr>
            `).join('')}
          </table>
          ` : '<p>✅ Nenhum problema encontrado!</p>'}
        </body>
        </html>
      `;
    }
  </script>
</body>
</html>
