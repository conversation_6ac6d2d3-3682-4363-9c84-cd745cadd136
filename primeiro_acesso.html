<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Primeiro Acesso - Configuração Inicial</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0854a0;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .required::after {
            content: " *";
            color: red;
        }
        button {
            background-color: #0854a0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 30px auto 0;
            width: 200px;
        }
        button:hover {
            background-color: #0a4d8c;
        }
        .error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        .success-message {
            display: none;
            text-align: center;
            color: #28a745;
            font-size: 18px;
            margin-top: 20px;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Configuração Inicial</h1>
        <p>Bem-vindo ao sistema! Por favor, preencha os dados da sua empresa para começar.</p>
        
        <form id="configForm">
            <div class="form-group">
                <label class="required" for="nomeFantasia">Nome Fantasia</label>
                <input type="text" id="nomeFantasia" required>
                <div class="error" id="nomeFantasiaError"></div>
            </div>

            <div class="form-group">
                <label class="required" for="razaoSocial">Razão Social</label>
                <input type="text" id="razaoSocial" required>
                <div class="error" id="razaoSocialError"></div>
            </div>

            <div class="form-group">
                <label class="required" for="cnpj">CNPJ</label>
                <input type="text" id="cnpj" required>
                <div class="error" id="cnpjError"></div>
            </div>

            <div class="form-group">
                <label class="required" for="endereco">Endereço</label>
                <input type="text" id="endereco" required>
                <div class="error" id="enderecoError"></div>
            </div>

            <div class="form-group">
                <label class="required" for="telefone">Telefone</label>
                <input type="tel" id="telefone" required>
                <div class="error" id="telefoneError"></div>
            </div>

            <div class="form-group">
                <label class="required" for="email">E-mail</label>
                <input type="email" id="email" required>
                <div class="error" id="emailError"></div>
            </div>

            <button type="submit">Salvar e Continuar</button>
        </form>

        <div class="loading" id="loading">
            Salvando configurações...
        </div>

        <div class="success-message" id="successMessage">
            Configuração concluída com sucesso! Redirecionando...
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { doc, updateDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { completeFirstAccess } from './init_database.js';

        // Função para formatar CNPJ
        function formatCNPJ(cnpj) {
            return cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5");
        }

        // Função para formatar telefone
        function formatPhone(phone) {
            return phone.replace(/^(\d{2})(\d{4,5})(\d{4})/, "($1) $2-$3");
        }

        // Aplicar máscaras nos campos
        document.getElementById('cnpj').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 14) {
                e.target.value = formatCNPJ(value.padEnd(14, '0'));
            }
        });

        document.getElementById('telefone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                e.target.value = formatPhone(value.padEnd(11, '0'));
            }
        });

        // Função para validar CNPJ
        function validateCNPJ(cnpj) {
            cnpj = cnpj.replace(/[^\d]/g, '');
            if (cnpj.length !== 14) return false;
            
            // Validação básica de CNPJ
            if (/^(\d)\1{13}$/.test(cnpj)) return false;
            
            return true;
        }

        // Função para validar e-mail
        function validateEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        // Função para validar formulário
        function validateForm() {
            let isValid = true;
            const fields = {
                nomeFantasia: { required: true },
                razaoSocial: { required: true },
                cnpj: { required: true, validate: validateCNPJ },
                endereco: { required: true },
                telefone: { required: true },
                email: { required: true, validate: validateEmail }
            };

            for (const [field, rules] of Object.entries(fields)) {
                const input = document.getElementById(field);
                const error = document.getElementById(field + 'Error');
                const value = input.value.trim();

                error.style.display = 'none';
                
                if (rules.required && !value) {
                    error.textContent = 'Este campo é obrigatório';
                    error.style.display = 'block';
                    isValid = false;
                } else if (rules.validate && !rules.validate(value)) {
                    error.textContent = 'Valor inválido';
                    error.style.display = 'block';
                    isValid = false;
                }
            }

            return isValid;
        }

        // Manipular envio do formulário
        document.getElementById('configForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!validateForm()) {
                return;
            }

            const loading = document.getElementById('loading');
            const successMessage = document.getElementById('successMessage');
            const form = document.getElementById('configForm');

            loading.style.display = 'block';
            form.style.display = 'none';

            try {
                // Atualizar dados da empresa
                const empresaData = {
                    nomeFantasia: document.getElementById('nomeFantasia').value.trim(),
                    razaoSocial: document.getElementById('razaoSocial').value.trim(),
                    cnpj: document.getElementById('cnpj').value.trim(),
                    endereco: document.getElementById('endereco').value.trim(),
                    telefone: document.getElementById('telefone').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    primeiroAcesso: false
                };

                await updateDoc(doc(db, "empresa", "config"), empresaData);
                
                // Marcar primeiro acesso como concluído
                await completeFirstAccess('admin');

                loading.style.display = 'none';
                successMessage.style.display = 'block';

                // Redirecionar para a página principal após 2 segundos
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);

            } catch (error) {
                console.error('Erro ao salvar configurações:', error);
                loading.style.display = 'none';
                form.style.display = 'block';
                alert('Erro ao salvar configurações. Por favor, tente novamente.');
            }
        });
    </script>
</body>
</html> 