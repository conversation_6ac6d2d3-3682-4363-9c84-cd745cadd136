
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizador de Dados - Sistema de Recebimento</title>
    <style>
        :root {
            --primary-color: #0854a0;
            --success-color: #107e3e;
            --danger-color: #bb0000;
            --warning-color: #e9730c;
            --light-bg: #f8f9fa;
            --white: #ffffff;
            --border-color: #d4d4d4;
            --text-color: #333;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #354a5f, var(--primary-color));
            color: var(--white);
            padding: 30px;
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            background: linear-gradient(135deg, var(--primary-color), #0a4d8c);
            color: var(--white);
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn.active {
            background: linear-gradient(135deg, var(--success-color), #0d6e36);
        }

        .content {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .data-section {
            display: none;
        }

        .data-section.active {
            display: block;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--light-bg), #e9ecef);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-color);
            opacity: 0.8;
        }

        .table-container {
            background: var(--white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #354a5f, #34495e);
            color: var(--white);
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
            font-size: 0.9rem;
        }

        tr:hover {
            background: linear-gradient(135deg, #f8f9fa, #ecf0f1);
        }

        tr:nth-child(even) {
            background: rgba(8, 84, 160, 0.02);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            margin-left: 10px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .export-btn {
            background: linear-gradient(135deg, var(--success-color), #0d6e36);
            margin-left: auto;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
            
            table {
                font-size: 0.8rem;
            }
            
            th, td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Visualizador de Dados</h1>
            <p class="subtitle">Sistema de Recebimento de Materiais - Dados Salvos no Firebase</p>
        </div>

        <div class="controls">
            <button class="btn active" onclick="showSection('recebimentos')">
                📦 Recebimentos de Materiais
            </button>
            <button class="btn" onclick="showSection('movimentacoes')">
                🔄 Movimentações de Estoque
            </button>
            <button class="btn" onclick="showSection('estoques')">
                📊 Estoques Atuais
            </button>
            <button class="btn" onclick="showSection('qualidade')">
                🔬 Estoque Qualidade
            </button>
            <button class="btn" onclick="showSection('pedidos')">
                📋 Pedidos de Compra
            </button>
            <button class="btn export-btn" onclick="exportarDados()">
                💾 Exportar Dados
            </button>
        </div>

        <div class="content">
            <!-- Seção Recebimentos -->
            <div id="recebimentos" class="data-section active">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalRecebimentos">0</div>
                        <div class="stat-label">Total de Recebimentos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="recebimentosHoje">0</div>
                        <div class="stat-label">Recebimentos Hoje</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="valorTotalRecebimentos">R$ 0,00</div>
                        <div class="stat-label">Valor Total</div>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Número NF</th>
                                <th>Pedido</th>
                                <th>Fornecedor</th>
                                <th>Valor Total</th>
                                <th>Armazém</th>
                                <th>Usuário</th>
                                <th>TES</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="recebimentosTable">
                            <tr><td colspan="9" class="loading">Carregando recebimentos...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Seção Movimentações -->
            <div id="movimentacoes" class="data-section">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalMovimentacoes">0</div>
                        <div class="stat-label">Total de Movimentações</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="entradasHoje">0</div>
                        <div class="stat-label">Entradas Hoje</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="saidasHoje">0</div>
                        <div class="stat-label">Saídas Hoje</div>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Produto</th>
                                <th>Tipo</th>
                                <th>Quantidade</th>
                                <th>Armazém</th>
                                <th>Tipo Documento</th>
                                <th>TES</th>
                                <th>Saldo Anterior</th>
                                <th>Saldo Posterior</th>
                            </tr>
                        </thead>
                        <tbody id="movimentacoesTable">
                            <tr><td colspan="9" class="loading">Carregando movimentações...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Seção Estoques -->
            <div id="estoques" class="data-section">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalItensEstoque">0</div>
                        <div class="stat-label">Itens em Estoque</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="saldoTotalEstoque">0</div>
                        <div class="stat-label">Saldo Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="itensComSaldo">0</div>
                        <div class="stat-label">Itens com Saldo</div>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Produto ID</th>
                                <th>Armazém ID</th>
                                <th>Saldo</th>
                                <th>Saldo Reservado</th>
                                <th>Saldo Disponível</th>
                                <th>Última Movimentação</th>
                            </tr>
                        </thead>
                        <tbody id="estoquesTable">
                            <tr><td colspan="6" class="loading">Carregando estoques...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Seção Qualidade -->
            <div id="qualidade" class="data-section">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalQualidade">0</div>
                        <div class="stat-label">Itens na Qualidade</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pendentesQualidade">0</div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="aprovadosQualidade">0</div>
                        <div class="stat-label">Aprovados</div>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Data Entrada</th>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Quantidade</th>
                                <th>Status</th>
                                <th>NF</th>
                                <th>Lote</th>
                                <th>Motivo Inspeção</th>
                            </tr>
                        </thead>
                        <tbody id="qualidadeTable">
                            <tr><td colspan="8" class="loading">Carregando itens de qualidade...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Seção Pedidos -->
            <div id="pedidos" class="data-section">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalPedidos">0</div>
                        <div class="stat-label">Total de Pedidos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pedidosAbertos">0</div>
                        <div class="stat-label">Pedidos Abertos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="valorTotalPedidos">R$ 0,00</div>
                        <div class="stat-label">Valor Total</div>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Data</th>
                                <th>Fornecedor</th>
                                <th>Status</th>
                                <th>Valor Total</th>
                                <th>Qtd Itens</th>
                                <th>% Recebido</th>
                            </tr>
                        </thead>
                        <tbody id="pedidosTable">
                            <tr><td colspan="7" class="loading">Carregando pedidos...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            query, 
            orderBy, 
            limit,
            where,
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentData = {};

        // Função para mostrar seção
        window.showSection = function(sectionName) {
            // Remover active de todos os botões e seções
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.data-section').forEach(section => section.classList.remove('active'));
            
            // Ativar botão e seção correspondente
            event.target.classList.add('active');
            document.getElementById(sectionName).classList.add('active');
            
            // Carregar dados se necessário
            if (!currentData[sectionName]) {
                loadSectionData(sectionName);
            }
        };

        // Carregar dados iniciais
        document.addEventListener('DOMContentLoaded', function() {
            loadSectionData('recebimentos');
        });

        async function loadSectionData(section) {
            try {
                switch (section) {
                    case 'recebimentos':
                        await loadRecebimentos();
                        break;
                    case 'movimentacoes':
                        await loadMovimentacoes();
                        break;
                    case 'estoques':
                        await loadEstoques();
                        break;
                    case 'qualidade':
                        await loadQualidade();
                        break;
                    case 'pedidos':
                        await loadPedidos();
                        break;
                }
            } catch (error) {
                console.error(`Erro ao carregar ${section}:`, error);
                showError(section, error.message);
            }
        }

        async function loadRecebimentos() {
            try {
                const snapshot = await getDocs(
                    query(collection(db, "recebimentoMateriais"), 
                          orderBy("dataRecebimento", "desc"), 
                          limit(100))
                );

                const recebimentos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentData.recebimentos = recebimentos;

                // Atualizar estatísticas
                const hoje = new Date().toDateString();
                const recebimentosHoje = recebimentos.filter(r => {
                    const dataRecebimento = r.dataRecebimento?.toDate?.()?.toDateString() || '';
                    return dataRecebimento === hoje;
                });

                const valorTotal = recebimentos.reduce((sum, r) => sum + (r.valorTotalNF || 0), 0);

                document.getElementById('totalRecebimentos').textContent = recebimentos.length;
                document.getElementById('recebimentosHoje').textContent = recebimentosHoje.length;
                document.getElementById('valorTotalRecebimentos').textContent = 
                    new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(valorTotal);

                // Atualizar tabela
                const tbody = document.getElementById('recebimentosTable');
                tbody.innerHTML = recebimentos.map(r => `
                    <tr>
                        <td>${formatDate(r.dataRecebimento)}</td>
                        <td>${r.numeroNotaFiscal || r.numeroNF || '-'}</td>
                        <td>${r.numeroPedido || '-'}</td>
                        <td>${r.fornecedorNome || '-'}</td>
                        <td>${formatCurrency(r.valorTotalNF)}</td>
                        <td>${r.armazemDestino || r.armazemDestinoId || '-'}</td>
                        <td>${r.usuarioRecebimento || '-'}</td>
                        <td>${r.tes || '-'}</td>
                        <td><span class="status-badge ${r.status || 'pendente'}">${r.status || 'Concluído'}</span></td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('Erro ao carregar recebimentos:', error);
                throw error;
            }
        }

        async function loadMovimentacoes() {
            try {
                const snapshot = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), 
                          orderBy("dataHora", "desc"), 
                          limit(100))
                );

                const movimentacoes = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentData.movimentacoes = movimentacoes;

                // Estatísticas
                const hoje = new Date().toDateString();
                const entradasHoje = movimentacoes.filter(m => 
                    m.tipo === 'ENTRADA' && 
                    m.dataHora?.toDate?.()?.toDateString() === hoje
                ).length;
                
                const saidasHoje = movimentacoes.filter(m => 
                    m.tipo === 'SAIDA' && 
                    m.dataHora?.toDate?.()?.toDateString() === hoje
                ).length;

                document.getElementById('totalMovimentacoes').textContent = movimentacoes.length;
                document.getElementById('entradasHoje').textContent = entradasHoje;
                document.getElementById('saidasHoje').textContent = saidasHoje;

                // Tabela
                const tbody = document.getElementById('movimentacoesTable');
                tbody.innerHTML = movimentacoes.map(m => `
                    <tr>
                        <td>${formatDate(m.dataHora)}</td>
                        <td>${m.produtoId || '-'}</td>
                        <td><span class="status-badge ${m.tipo?.toLowerCase()}">${m.tipo || '-'}</span></td>
                        <td>${formatNumber(m.quantidade)}</td>
                        <td>${m.armazemId || '-'}</td>
                        <td>${m.tipoDocumento || '-'}</td>
                        <td>${m.tes || '-'}</td>
                        <td>${formatNumber(m.saldoAnterior)}</td>
                        <td>${formatNumber(m.saldoPosterior)}</td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('Erro ao carregar movimentações:', error);
                throw error;
            }
        }

        async function loadEstoques() {
            try {
                const snapshot = await getDocs(collection(db, "estoques"));
                const estoques = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentData.estoques = estoques;

                // Estatísticas
                const saldoTotal = estoques.reduce((sum, e) => sum + (e.saldo || 0), 0);
                const itensComSaldo = estoques.filter(e => (e.saldo || 0) > 0).length;

                document.getElementById('totalItensEstoque').textContent = estoques.length;
                document.getElementById('saldoTotalEstoque').textContent = formatNumber(saldoTotal);
                document.getElementById('itensComSaldo').textContent = itensComSaldo;

                // Tabela
                const tbody = document.getElementById('estoquesTable');
                tbody.innerHTML = estoques.map(e => `
                    <tr>
                        <td>${e.produtoId || '-'}</td>
                        <td>${e.armazemId || '-'}</td>
                        <td>${formatNumber(e.saldo)}</td>
                        <td>${formatNumber(e.saldoReservado)}</td>
                        <td>${formatNumber((e.saldo || 0) - (e.saldoReservado || 0))}</td>
                        <td>${formatDate(e.ultimaMovimentacao)}</td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('Erro ao carregar estoques:', error);
                throw error;
            }
        }

        async function loadQualidade() {
            try {
                const snapshot = await getDocs(
                    query(collection(db, "estoqueQualidade"), 
                          orderBy("dataEntrada", "desc"))
                );

                const qualidade = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentData.qualidade = qualidade;

                // Estatísticas
                const pendentes = qualidade.filter(q => q.status === 'PENDENTE').length;
                const aprovados = qualidade.filter(q => q.status === 'APROVADO').length;

                document.getElementById('totalQualidade').textContent = qualidade.length;
                document.getElementById('pendentesQualidade').textContent = pendentes;
                document.getElementById('aprovadosQualidade').textContent = aprovados;

                // Tabela
                const tbody = document.getElementById('qualidadeTable');
                tbody.innerHTML = qualidade.map(q => `
                    <tr>
                        <td>${formatDate(q.dataEntrada)}</td>
                        <td>${q.codigo || '-'}</td>
                        <td>${q.descricao || '-'}</td>
                        <td>${formatNumber(q.quantidade)}</td>
                        <td><span class="status-badge ${(q.status || 'pendente').toLowerCase()}">${q.status || 'PENDENTE'}</span></td>
                        <td>${q.numeroNF || '-'}</td>
                        <td>${q.loteInterno || q.loteFornecedor || '-'}</td>
                        <td>${q.motivoInspecao || '-'}</td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('Erro ao carregar qualidade:', error);
                throw error;
            }
        }

        async function loadPedidos() {
            try {
                const snapshot = await getDocs(
                    query(collection(db, "pedidosCompra"), 
                          orderBy("dataPedido", "desc"), 
                          limit(100))
                );

                const pedidos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                currentData.pedidos = pedidos;

                // Estatísticas
                const abertos = pedidos.filter(p => p.status !== 'FINALIZADO' && p.status !== 'CANCELADO').length;
                const valorTotal = pedidos.reduce((sum, p) => sum + (p.valorTotal || 0), 0);

                document.getElementById('totalPedidos').textContent = pedidos.length;
                document.getElementById('pedidosAbertos').textContent = abertos;
                document.getElementById('valorTotalPedidos').textContent = formatCurrency(valorTotal);

                // Tabela
                const tbody = document.getElementById('pedidosTable');
                tbody.innerHTML = pedidos.map(p => `
                    <tr>
                        <td>${p.numero || p.numeroPedido || '-'}</td>
                        <td>${formatDate(p.dataPedido)}</td>
                        <td>${p.fornecedorNome || '-'}</td>
                        <td><span class="status-badge ${(p.status || 'pendente').toLowerCase()}">${p.status || 'PENDENTE'}</span></td>
                        <td>${formatCurrency(p.valorTotal)}</td>
                        <td>${p.itens?.length || 0}</td>
                        <td>${Math.round((p.percentualRecebido || 0) * 100) / 100}%</td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('Erro ao carregar pedidos:', error);
                throw error;
            }
        }

        // Funções auxiliares
        function formatDate(timestamp) {
            if (!timestamp) return '-';
            
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toLocaleString('pt-BR');
            } catch {
                return '-';
            }
        }

        function formatCurrency(value) {
            if (!value && value !== 0) return 'R$ 0,00';
            return new Intl.NumberFormat('pt-BR', { 
                style: 'currency', 
                currency: 'BRL' 
            }).format(value);
        }

        function formatNumber(value) {
            if (!value && value !== 0) return '0';
            return new Intl.NumberFormat('pt-BR').format(value);
        }

        function showError(section, message) {
            const tableId = section + 'Table';
            const tbody = document.getElementById(tableId);
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="100%" style="text-align: center; color: var(--danger-color); padding: 40px;">
                            ❌ Erro ao carregar dados: ${message}
                        </td>
                    </tr>
                `;
            }
        }

        // Função para exportar dados
        window.exportarDados = function() {
            const activeSection = document.querySelector('.data-section.active');
            if (!activeSection) return;

            const sectionId = activeSection.id;
            const data = currentData[sectionId];
            
            if (!data || data.length === 0) {
                alert('Nenhum dado para exportar na seção atual.');
                return;
            }

            // Converter para CSV
            const headers = Object.keys(data[0]).filter(key => key !== 'id');
            let csv = headers.join(',') + '\n';
            
            data.forEach(item => {
                const row = headers.map(header => {
                    let value = item[header];
                    if (value && value.toDate) {
                        value = value.toDate().toLocaleString('pt-BR');
                    }
                    return `"${value || ''}"`;
                }).join(',');
                csv += row + '\n';
            });

            // Download
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${sectionId}_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        };
    </script>
</body>
</html>
