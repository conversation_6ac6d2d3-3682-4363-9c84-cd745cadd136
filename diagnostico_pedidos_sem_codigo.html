
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico - Pedidos sem Código/Preço</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .problema { background: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
        .corrigido { background: #d4edda; padding: 10px; margin: 10px 0; border-left: 4px solid #28a745; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-success { background: #28a745; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .status-erro { color: #dc3545; font-weight: bold; }
        .status-ok { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 Diagnóstico - Pedidos sem Código/Preço</h1>
    
    <div class="controles">
        <button class="btn btn-primary" onclick="iniciarDiagnostico()">🔍 Iniciar Diagnóstico</button>
        <button class="btn btn-warning" onclick="corrigirProblemas()">🔧 Corrigir Problemas</button>
        <button class="btn btn-success" onclick="gerarRelatorio()">📊 Gerar Relatório</button>
    </div>

    <div id="resultados"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs, updateDoc, doc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        let pedidosCompra = [];
        let produtos = [];
        let problemasEncontrados = [];

        window.iniciarDiagnostico = async function() {
            document.getElementById('resultados').innerHTML = '<p>⏳ Carregando dados...</p>';
            
            try {
                // Carregar dados
                const [pedidosSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "produtos"))
                ]);

                pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Analisar problemas
                analisarProblemas();
                
            } catch (error) {
                console.error('Erro no diagnóstico:', error);
                alert('Erro ao carregar dados: ' + error.message);
            }
        };

        function analisarProblemas() {
            problemasEncontrados = [];
            
            pedidosCompra.forEach(pedido => {
                if (!pedido.itens || !Array.isArray(pedido.itens)) return;
                
                pedido.itens.forEach((item, index) => {
                    const produto = produtos.find(p => p.id === item.produtoId);
                    
                    const problemas = [];
                    
                    // Verificar produto não encontrado
                    if (!produto) {
                        problemas.push('Produto não encontrado no cadastro');
                    }
                    
                    // Verificar código
                    if (!produto?.codigo && !item.codigo) {
                        problemas.push('Código não definido');
                    }
                    
                    // Verificar preço
                    const preco = parseFloat(item.valorUnitario || item.precoUnitario || 0);
                    if (preco === 0) {
                        problemas.push('Preço zerado ou não definido');
                    }
                    
                    // Verificar descrição
                    if (!produto?.descricao && !item.descricao) {
                        problemas.push('Descrição não definida');
                    }
                    
                    if (problemas.length > 0) {
                        problemasEncontrados.push({
                            pedidoId: pedido.id,
                            pedidoNumero: pedido.numero,
                            itemIndex: index,
                            produtoId: item.produtoId,
                            produto: produto,
                            item: item,
                            problemas: problemas
                        });
                    }
                });
            });
            
            exibirResultados();
        }

        function exibirResultados() {
            const resultados = document.getElementById('resultados');
            
            if (problemasEncontrados.length === 0) {
                resultados.innerHTML = `
                    <div class="corrigido">
                        ✅ <strong>Nenhum problema encontrado!</strong><br>
                        Todos os pedidos estão com códigos e preços corretos.
                    </div>
                `;
                return;
            }
            
            let html = `
                <h2>📋 Problemas Encontrados (${problemasEncontrados.length})</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Pedido</th>
                            <th>Produto ID</th>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Preço</th>
                            <th>Problemas</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            problemasEncontrados.forEach(problema => {
                const codigo = problema.produto?.codigo || problema.item.codigo || 'N/A';
                const descricao = problema.produto?.descricao || problema.item.descricao || 'N/A';
                const preco = parseFloat(problema.item.valorUnitario || 0).toFixed(2);
                
                html += `
                    <tr>
                        <td>PC-${problema.pedidoNumero}</td>
                        <td>${problema.produtoId?.substring(0,8)}...</td>
                        <td>${codigo}</td>
                        <td>${descricao}</td>
                        <td>R$ ${preco}</td>
                        <td>${problema.problemas.join(', ')}</td>
                        <td class="status-erro">PROBLEMA</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
                
                <div class="problema">
                    ⚠️ <strong>Resumo dos Problemas:</strong><br>
                    • ${problemasEncontrados.filter(p => p.problemas.includes('Produto não encontrado')).length} produtos não encontrados<br>
                    • ${problemasEncontrados.filter(p => p.problemas.includes('Código não definido')).length} códigos não definidos<br>
                    • ${problemasEncontrados.filter(p => p.problemas.includes('Preço zerado')).length} preços zerados<br>
                    • ${problemasEncontrados.filter(p => p.problemas.includes('Descrição não definida')).length} descrições não definidas
                </div>
            `;
            
            resultados.innerHTML = html;
        }

        window.corrigirProblemas = async function() {
            if (problemasEncontrados.length === 0) {
                alert('Nenhum problema para corrigir!');
                return;
            }
            
            if (!confirm(`Corrigir ${problemasEncontrados.length} problemas encontrados?`)) {
                return;
            }
            
            let corrigidos = 0;
            
            for (const problema of problemasEncontrados) {
                try {
                    const pedidoRef = doc(db, "pedidosCompra", problema.pedidoId);
                    const pedido = pedidosCompra.find(p => p.id === problema.pedidoId);
                    
                    if (pedido && pedido.itens && pedido.itens[problema.itemIndex]) {
                        const item = pedido.itens[problema.itemIndex];
                        
                        // Corrigir código se necessário
                        if (!item.codigo && problema.produto?.codigo) {
                            item.codigo = problema.produto.codigo;
                        }
                        
                        // Corrigir descrição se necessário
                        if (!item.descricao && problema.produto?.descricao) {
                            item.descricao = problema.produto.descricao;
                        }
                        
                        // Corrigir preço se zerado (usar preço médio do produto)
                        if (parseFloat(item.valorUnitario || 0) === 0 && problema.produto?.precoMedio) {
                            item.valorUnitario = problema.produto.precoMedio;
                        }
                        
                        // Atualizar no Firebase
                        await updateDoc(pedidoRef, {
                            itens: pedido.itens
                        });
                        
                        corrigidos++;
                    }
                    
                } catch (error) {
                    console.error('Erro ao corrigir problema:', error);
                }
            }
            
            alert(`✅ ${corrigidos} problemas corrigidos com sucesso!`);
            
            // Recarregar diagnóstico
            setTimeout(iniciarDiagnostico, 1000);
        };

        window.gerarRelatorio = function() {
            if (problemasEncontrados.length === 0) {
                alert('Nenhum problema para reportar!');
                return;
            }
            
            const relatorio = problemasEncontrados.map(p => ({
                Pedido: `PC-${p.pedidoNumero}`,
                ProdutoID: p.produtoId,
                Codigo: p.produto?.codigo || p.item.codigo || 'N/A',
                Descricao: p.produto?.descricao || p.item.descricao || 'N/A',
                Preco: `R$ ${parseFloat(p.item.valorUnitario || 0).toFixed(2)}`,
                Problemas: p.problemas.join('; ')
            }));
            
            console.table(relatorio);
            
            // Baixar como CSV
            const csv = [
                ['Pedido', 'ProdutoID', 'Codigo', 'Descricao', 'Preco', 'Problemas'],
                ...relatorio.map(r => [r.Pedido, r.ProdutoID, r.Codigo, r.Descricao, r.Preco, r.Problemas])
            ].map(row => row.join(',')).join('\n');
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `problemas_pedidos_${new Date().toISOString().slice(0,10)}.csv`;
            a.click();
        };
    </script>
</body>
</html>
