<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> de Ordens em Aberto</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
        }

        .search-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
            padding: 20px;
            background-color: var(--secondary-color);
            border-radius: 8px;
        }

        .search-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .search-group label {
            font-weight: bold;
            color: var(--text-color);
        }

        .search-group input, .search-group select {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            background-color: var(--primary-color);
            color: white;
            transition: background-color 0.2s;
        }

        .button:hover {
            background-color: var(--primary-hover);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .orders-section {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
        }

        .orders-section h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .orders-table th, .orders-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .orders-table th {
            background-color: var(--header-bg);
            color: white;
            font-weight: 500;
        }

        .orders-table tr:hover {
            background-color: var(--secondary-color);
        }

        .aggregation-group {
            background-color: var(--secondary-color);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .aggregation-group h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .aggregation-item {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .aggregation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .aggregation-title {
            font-weight: bold;
            color: var(--primary-color);
        }

        .aggregation-stats {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            background-color: var(--secondary-color);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid var(--secondary-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-aberta {
            background-color: var(--warning-color);
            color: white;
        }

        .status-em-producao {
            background-color: var(--primary-color);
            color: white;
        }

        .status-concluida {
            background-color: var(--success-color);
            color: white;
        }

        .aggregation-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .action-button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-button.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .action-button.success {
            background-color: var(--success-color);
            color: white;
        }

        .action-button:hover {
            opacity: 0.9;
        }

        .export-button {
            background-color: var(--success-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .export-button:hover {
            background-color: var(--success-hover);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Análise de Ordens em Aberto</h1>
            <button class="export-button" onclick="exportarAnalise()">
                <i class="fas fa-file-export"></i> Exportar Análise
            </button>
            <button class="button" id="btnAglutinarTodas">
                <i class="fas fa-boxes"></i> Aglutinar Todas
            </button>
        </div>

        <div class="search-panel">
            <div class="search-group">
                <label for="statusOrdem">Status da Ordem</label>
                <select id="statusOrdem">
                    <option value="todos">Todos</option>
                    <option value="aberta">Aberta</option>
                    <option value="em_producao">Em Produção</option>
                </select>
            </div>
            <div class="search-group">
                <label for="dataInicio">Data Início</label>
                <input type="date" id="dataInicio">
            </div>
            <div class="search-group">
                <label for="dataFim">Data Fim</label>
                <input type="date" id="dataFim">
            </div>
            <div class="search-group">
                <label for="buscaProduto">Buscar Produto</label>
                <input type="text" id="buscaProduto" placeholder="Código ou descrição">
            </div>
            <div class="search-group" style="align-self: end;">
                <button class="button" onclick="buscarOrdens()">
                    <i class="fas fa-search"></i> Buscar Ordens
                </button>
            </div>
        </div>

        <div class="loading">
            <div class="spinner"></div>
            <p>Analisando ordens de produção...</p>
        </div>

        <div class="main-content">
            <div class="orders-section">
                <h2>Ordens em Aberto</h2>
                <div id="ordensList">
                    <!-- Será preenchido dinamicamente -->
                </div>
            </div>

            <div class="orders-section">
                <h2>Possíveis Aglutinações</h2>
                <div id="aglutinacoesList">
                    <!-- Será preenchido dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            query,
            where,
            orderBy,
            Timestamp,
            writeBatch,
            doc,
            addDoc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let ordens = [];
        let produtos = [];
        let estruturas = [];

        // Função para carregar dados iniciais
        async function carregarDados() {
            const loading = document.querySelector('.loading');
            loading.classList.add('active');

            try {
                // Carregar produtos e estruturas primeiro
                const [produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Buscar ordens inicialmente
                await buscarOrdens();
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Por favor, tente novamente.');
            } finally {
                loading.classList.remove('active');
            }
        }

        // Função para buscar ordens
        async function buscarOrdens() {
            const loading = document.querySelector('.loading');
            loading.classList.add('active');

            try {
                const statusOrdem = document.getElementById('statusOrdem').value;
                const dataInicio = document.getElementById('dataInicio').value;
                const dataFim = document.getElementById('dataFim').value;
                const buscaProduto = document.getElementById('buscaProduto').value.toLowerCase();

                // Construir query base
                let q = collection(db, "ordensProducao");
                let constraints = [];

                // Adicionar filtros
                if (statusOrdem !== 'todos') {
                    constraints.push(where("status", "==", statusOrdem));
                }

                if (dataInicio) {
                    constraints.push(where("dataCriacao", ">=", Timestamp.fromDate(new Date(dataInicio))));
                }

                if (dataFim) {
                    constraints.push(where("dataCriacao", "<=", Timestamp.fromDate(new Date(dataFim + 'T23:59:59'))));
                }

                // Aplicar constraints e ordenar
                if (constraints.length > 0) {
                    q = query(q, ...constraints, orderBy("dataCriacao", "desc"));
                } else {
                    q = query(q, orderBy("dataCriacao", "desc"));
                }

                const ordensSnap = await getDocs(q);
                ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar por busca de produto se necessário
                if (buscaProduto) {
                    ordens = ordens.filter(ordem => {
                        const produto = produtos.find(p => p.id === ordem.produtoId);
                        return produto && (
                            produto.codigo.toLowerCase().includes(buscaProduto) ||
                            produto.descricao.toLowerCase().includes(buscaProduto)
                        );
                    });
                }

                // Analisar possíveis aglutinações
                const aglutinacoes = analisarAglutinacoes(ordens);

                // Atualizar interface
                atualizarListaOrdens(ordens);
                atualizarListaAglutinacoes(aglutinacoes);
            } catch (error) {
                console.error('Erro ao buscar ordens:', error);
                alert('Erro ao buscar ordens. Por favor, tente novamente.');
            } finally {
                loading.classList.remove('active');
            }
        }

        // Função para analisar possíveis aglutinações
        function analisarAglutinacoes(ordens) {
            const aglutinacoes = new Map();

            // Agrupar ordens por produto
            ordens.forEach(ordem => {
                const produto = produtos.find(p => p.id === ordem.produtoId);
                if (!produto) return;

                // Verificar se o produto já tem uma estrutura de aglutinação
                if (!aglutinacoes.has(produto.id)) {
                    aglutinacoes.set(produto.id, {
                        produto: produto,
                        ordens: [],
                        componentesComuns: new Map(),
                        dataInicio: ordem.dataCriacao,
                        dataFim: ordem.dataCriacao,
                        quantidadeTotal: 0
                    });
                }

                const aglutinacao = aglutinacoes.get(produto.id);
                aglutinacao.ordens.push(ordem);
                aglutinacao.quantidadeTotal += ordem.quantidade;

                // Atualizar datas
                if (ordem.dataCriacao < aglutinacao.dataInicio) {
                    aglutinacao.dataInicio = ordem.dataCriacao;
                }
                if (ordem.dataCriacao > aglutinacao.dataFim) {
                    aglutinacao.dataFim = ordem.dataCriacao;
                }

                // Analisar componentes comuns
                const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);
                if (estrutura && estrutura.componentes) {
                    estrutura.componentes.forEach(comp => {
                        if (!aglutinacao.componentesComuns.has(comp.componentId)) {
                            aglutinacao.componentesComuns.set(comp.componentId, {
                                quantidade: 0,
                                ordens: new Set()
                            });
                        }
                        const componente = aglutinacao.componentesComuns.get(comp.componentId);
                        componente.quantidade += comp.quantidade * ordem.quantidade;
                        componente.ordens.add(ordem.id);
                    });
                }
            });

            // Filtrar apenas aglutinações com mais de uma ordem
            return Array.from(aglutinacoes.values())
                .filter(ag => ag.ordens.length > 1)
                .sort((a, b) => b.ordens.length - a.ordens.length);
        }

        // Função para atualizar a lista de ordens
        function atualizarListaOrdens(ordens) {
            const container = document.getElementById('ordensList');

            if (ordens.length === 0) {
                container.innerHTML = '<p>Nenhuma ordem encontrada.</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'orders-table';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Número</th>
                        <th>Produto</th>
                        <th>Quantidade</th>
                        <th>Data Criação</th>
                        <th>Status</th>
                        <th>Prioridade</th>
                    </tr>
                </thead>
                <tbody>
                    ${ordens.map(ordem => {
                        const produto = produtos.find(p => p.id === ordem.produtoId);
                        return `
                            <tr>
                                <td>${ordem.numero}</td>
                                <td>${produto ? `${produto.codigo} - ${produto.descricao}` : 'Produto não encontrado'}</td>
                                <td>${ordem.quantidade} ${ordem.unidade}</td>
                                <td>${new Date(ordem.dataCriacao.toDate()).toLocaleDateString()}</td>
                                <td>
                                    <span class="status-badge status-${ordem.status.toLowerCase()}">
                                        ${ordem.status}
                                    </span>
                                </td>
                                <td>${ordem.prioridade || 'Normal'}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            `;

            container.innerHTML = '';
            container.appendChild(table);
        }

        // Função para atualizar a lista de aglutinações
        function atualizarListaAglutinacoes(aglutinacoes) {
            const container = document.getElementById('aglutinacoesList');

            if (aglutinacoes.length === 0) {
                container.innerHTML = '<p>Nenhuma aglutinação possível encontrada.</p>';
                return;
            }

            container.innerHTML = aglutinacoes.map(ag => `
                <div class="aggregation-group" data-produto-id="${ag.produto.id}">
                    <div class="aggregation-header">
                        <div class="aggregation-title">
                            ${ag.produto.codigo} - ${ag.produto.descricao}
                        </div>
                        <div class="aggregation-stats">
                            <span class="stat-item">
                                <i class="fas fa-boxes"></i> ${ag.ordens.length} ordens
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-calculator"></i> ${ag.quantidadeTotal} ${ag.produto.unidade}
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-calendar"></i> 
                                ${new Date(ag.dataInicio.toDate()).toLocaleDateString()} - 
                                ${new Date(ag.dataFim.toDate()).toLocaleDateString()}
                            </span>
                        </div>
                    </div>

                    <div class="aggregation-item">
                        <h4>Ordens Incluídas</h4>
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>Número</th>
                                    <th>Quantidade</th>
                                    <th>Data</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${ag.ordens.map(ordem => `
                                    <tr>
                                        <td>${ordem.numero}</td>
                                        <td>${ordem.quantidade} ${ordem.unidade}</td>
                                        <td>${new Date(ordem.dataCriacao.toDate()).toLocaleDateString()}</td>
                                        <td>
                                            <span class="status-badge status-${ordem.status.toLowerCase()}">
                                                ${ordem.status}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="aggregation-item">
                        <h4>Componentes Comuns</h4>
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>Componente</th>
                                    <th>Quantidade Total</th>
                                    <th>Ordens</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Array.from(ag.componentesComuns.entries()).map(([componentId, info]) => {
                                    const componente = produtos.find(p => p.id === componentId);
                                    return `
                                        <tr>
                                            <td>${componente ? `${componente.codigo} - ${componente.descricao}` : 'Componente não encontrado'}</td>
                                            <td>${info.quantidade} ${componente?.unidade || ''}</td>
                                            <td>${info.ordens.size} ordens</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="aggregation-actions">
                        <button class="action-button primary" onclick="criarOrdemAglutinada('${ag.produto.id}')">
                            <i class="fas fa-boxes"></i> Criar Ordem Aglutinada
                        </button>
                        <button class="action-button success" onclick="exportarAglutinacao('${ag.produto.id}')">
                            <i class="fas fa-file-export"></i> Exportar Detalhes
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Função para exportar a análise
        function exportarAnalise() {
            const analise = {
                data: new Date().toISOString(),
                totalOrdens: ordens.length,
                aglutinacoes: document.querySelectorAll('.aggregation-group').length,
                detalhes: {
                    ordens: ordens.map(ordem => ({
                        numero: ordem.numero,
                        produto: produtos.find(p => p.id === ordem.produtoId)?.codigo,
                        quantidade: ordem.quantidade,
                        data: ordem.dataCriacao.toDate().toISOString(),
                        status: ordem.status
                    })),
                    aglutinacoes: Array.from(document.querySelectorAll('.aggregation-group')).map(ag => ({
                        produto: ag.querySelector('.aggregation-title').textContent,
                        totalOrdens: parseInt(ag.querySelector('.stat-item').textContent),
                        periodo: ag.querySelectorAll('.stat-item')[2].textContent
                    }))
                }
            };

            const blob = new Blob([JSON.stringify(analise, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analise_ordens_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Função para criar ordem aglutinada
        window.criarOrdemAglutinada = async function(produtoId, reloadData = true) {
            try {
                // Encontrar a aglutinação correspondente
                const aglutinacao = document.querySelector(`.aggregation-group[data-produto-id="${produtoId}"]`);

                if (!aglutinacao) {
                    alert('Aglutinação não encontrada');
                    return;
                }

                // Preparar dados da nova ordem
                const ordensIds = Array.from(aglutinacao.querySelectorAll('.orders-table tbody tr'))
                    .map(tr => tr.cells[0].textContent);

                const ordensParaAglutinar = ordens.filter(o => ordensIds.includes(o.numero));
                const produto = produtos.find(p => p.id === produtoId);

                if (!produto) {
                    throw new Error('Produto não encontrado');
                }

                // Calcular quantidade total
                const quantidadeTotal = ordensParaAglutinar.reduce((sum, ordem) => sum + ordem.quantidade, 0);

                // Criar nova ordem aglutinada
                const novaOrdem = {
                    numero: `OP${new Date().getTime()}`,
                    produtoId: produtoId,
                    quantidade: quantidadeTotal,
                    unidade: produto.unidade,
                    status: 'aberta',
                    dataCriacao: Timestamp.now(),
                    tipo: 'aglutinada',
                    ordensOriginais: ordensParaAglutinar.map(o => ({
                        ordemId: o.id,
                        numero: o.numero,
                        quantidade: o.quantidade
                    })),
                    observacao: `Ordem aglutinada criada em ${new Date().toLocaleString()}`
                };

                // Atualizar ordens originais e criar nova ordem no Firebase
                const batch = writeBatch(db);

                // Adicionar nova ordem
                const novaOrdemRef = doc(collection(db, "ordensProducao"));
                batch.set(novaOrdemRef, novaOrdem);

                // Atualizar status das ordens originais
                ordensParaAglutinar.forEach(ordem => {
                    const ordemRef = doc(db, "ordensProducao", ordem.id);
                    batch.update(ordemRef, {
                        status: 'aglutinada',
                        ordemAglutinadaId: novaOrdemRef.id,
                        dataAglutinacao: Timestamp.now()
                    });
                });

                // Executar todas as atualizações
                await batch.commit();

                alert('Ordem aglutinada criada com sucesso!');

                // Recarregar dados
                if (reloadData) {
                    await buscarOrdens();
                }

                // Explodir componentes da nova ordem aglutinada
                const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
                if (estrutura) {
                    // Passar a nova ordem aglutinada como um array para a função de explosão
                    await explodirEConsolidarFilhas([ { ...novaOrdem, id: novaOrdemRef.id } ], estruturas, produtos);
                }

            } catch (error) {
                console.error('Erro ao criar ordem aglutinada:', error);
                alert('Erro ao criar ordem aglutinada. Por favor, tente novamente.');
            }
        };

        // Função para exportar detalhes de uma aglutinação
        window.exportarAglutinacao = function(produtoId) {
            const aglutinacao = document.querySelector(`.aggregation-group[data-produto-id="${produtoId}"]`);
            if (!aglutinacao) return;

            const detalhes = {
                produto: aglutinacao.querySelector('.aggregation-title').textContent,
                estatisticas: {
                    totalOrdens: aglutinacao.querySelector('.stat-item').textContent,
                    quantidadeTotal: aglutinacao.querySelectorAll('.stat-item')[1].textContent,
                    periodo: aglutinacao.querySelectorAll('.stat-item')[2].textContent
                },
                ordens: Array.from(aglutinacao.querySelectorAll('.orders-table tbody tr')).map(tr => ({
                    numero: tr.cells[0].textContent,
                    quantidade: tr.cells[1].textContent,
                    data: tr.cells[2].textContent,
                    status: tr.cells[3].textContent
                }))
            };

            const blob = new Blob([JSON.stringify(detalhes, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `aglutinacao_${produtoId}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        // Função para gerar número de ordem
        function generateOrderNumber() {
            const date = new Date();
            const year = date.getFullYear().toString().substr(-2);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            // Gerar um número aleatório de 4 dígitos
            const random = Math.floor(1000 + Math.random() * 9000).toString();
            return `OP${year}${month}${random}`;
        }

        // Função para explodir e consolidar necessidades de SPs (copiada de ordens_producao.html)
        async function explodirEConsolidarFilhas(opsPais, estruturas, produtos, nivel = 1) {
            // Mapa para consolidar necessidades de SPs: { produtoId|armazemId: { quantidade, dataEntrega, prioridade, centroCustoId, armazemProducaoId } }
            const necessidadesSP = {};
            for (const opPai of opsPais) {
                const estrutura = estruturas.find(e => e.produtoPaiId === opPai.produtoId);
                if (!estrutura) continue;
                for (const componente of estrutura.componentes) {
                    const produto = produtos.find(p => p.id === componente.componentId);
                    const quantidadeNecessaria = opPai.quantidade * componente.quantidade;
                    if (produto && produto.tipo === 'SP') {
                        const key = produto.id + '|' + (opPai.armazemProducaoId || '');
                        if (!necessidadesSP[key]) {
                            necessidadesSP[key] = {
                                produtoId: produto.id,
                                quantidade: 0,
                                dataEntrega: opPai.dataEntrega,
                                prioridade: opPai.prioridade,
                                centroCustoId: opPai.centroCustoId,
                                armazemProducaoId: opPai.armazemProducaoId
                            };
                        }
                        necessidadesSP[key].quantidade += quantidadeNecessaria;
                        // Ajuste de data/prioridade se necessário
                    }
                }
            }
            // Criar OPs filhas consolidadas
            const opsFilhas = [];
            for (const key in necessidadesSP) {
                const sp = necessidadesSP[key];
                const opFilha = {
                    numero: await generateOrderNumber(),
                    produtoId: sp.produtoId,
                    quantidade: sp.quantidade,
                    dataEntrega: sp.dataEntrega,
                    status: 'Pendente',
                    nivel,
                    prioridade: sp.prioridade,
                    centroCustoId: sp.centroCustoId,
                    dataCriacao: Timestamp.now(),
                    armazemProducaoId: sp.armazemProducaoId
                };
                const docRef = await addDoc(collection(db, 'ordensProducao'), opFilha);
                opFilha.id = docRef.id;
                opsFilhas.push(opFilha);
                // Debug
                console.log('Criada OP filha consolidada:', opFilha);
            }
            // Recursivamente explodir as filhas
            if (opsFilhas.length > 0) {
                const novasFilhas = await explodirEConsolidarFilhas(opsFilhas, estruturas, produtos, nivel + 1);
                return opsFilhas.concat(novasFilhas);
            }
            return opsFilhas;
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', () => {
            carregarDados();

            // Adicionar listener para o novo botão
            document.getElementById('btnAglutinarTodas').addEventListener('click', aglutinarTodasOrdens);
        });

        // Função para aglutinar todas as ordens sugeridas
        async function aglutinarTodasOrdens() {
            const confirmacao = confirm('Deseja aglutinar todas as sugestões de ordens?');
            if (!confirmacao) return;

            const loading = document.querySelector('.loading');
            loading.classList.add('active');

            try {
                const gruposAglutinacao = document.querySelectorAll('.aggregation-group');
                for (const grupo of gruposAglutinacao) {
                    const produtoId = grupo.dataset.produtoId;
                    if (produtoId) {
                        // Chamar criarOrdemAglutinada para cada grupo, sem recarregar individualmente
                        await criarOrdemAglutinada(produtoId, false); 
                    }
                }
                alert('Todas as aglutinações foram processadas.');
                // Recarregar os dados apenas uma vez ao final
                await buscarOrdens();
            } catch (error) {
                console.error('Erro ao aglutinar todas as ordens:', error);
                alert('Erro ao aglutinar todas as ordens. Por favor, tente novamente.');
            } finally {
                loading.classList.remove('active');
            }
        }
    </script>
</body>
</html> 