<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cruzamento de Dados - Componentes Ausentes</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    h1, h2 {
      color: #333;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .nao-cadastrado {
      color: red;
    }
    button {
      padding: 10px;
      margin: 5px;
      background-color: #007bff;
      color: white;
      border: none;
      cursor: pointer;
    }
    button:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <h1>Cruzamento de Dados - Componentes Ausentes</h1>
  <button onclick="cruzarDados()">Cruzar Dados</button>

  <h2>Componentes Ausentes na Tabela de Produtos</h2>
  <table id="tabela-componentes-ausentes">
    <thead>
      <tr>
        <th>Componente ID (Ausente)</th>
        <th>Produto Pai (ID)</th>
        <th>Produto Pai (Código)</th>
        <th>Produto Pai (Nome)</th>
        <th>Quantidade</th>
      </tr>
    </thead>
    <tbody></tbody>
  </table>

  <script>
    // Funções para buscar dados (substitua pelos seus endpoints de API)
    async function fetchProdutos() {
      try {
        const response = await fetch('/api/produtos');
        if (!response.ok) return [];
        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar produtos:', error.message);
        return [];
      }
    }

    async function fetchBOM() {
      try {
        const response = await fetch('/api/bom');
        if (!response.ok) return [];
        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar BOM:', error.message);
        return [];
      }
    }

    // Função para cruzar os dados e identificar componentes ausentes
    async function cruzarDados() {
      const tabela = document.querySelector('#tabela-componentes-ausentes tbody');
      tabela.innerHTML = ''; // Limpar tabela

      // Buscar dados das tabelas
      const produtos = await fetchProdutos();
      const bom = await fetchBOM();

      // Criar conjunto de IDs de produtos cadastrados para verificação rápida
      const idsProdutos = new Set(produtos.map(p => p.id));

      // Cruzar dados: verificar quais componentes da BOM não estão em produtos
      for (const item of bom) {
        if (!idsProdutos.has(item.componenteId)) {
          // Componente ausente na tabela de produtos
          const produtoPai = produtos.find(p => p.id === item.produtoId) || {};
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${item.componenteId}</td>
            <td>${item.produtoId}</td>
            <td>${produtoPai.codigo || 'Não encontrado'}</td>
            <td>${produtoPai.nome || 'Não encontrado'}</td>
            <td>${item.quantidade || 'N/A'}</td>
          `;
          tabela.appendChild(row);
        }
      }

      // Se a tabela estiver vazia, exibir mensagem
      if (tabela.children.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="5">Nenhum componente ausente encontrado.</td>';
        tabela.appendChild(row);
      }
    }
  </script>
</body>
</html>