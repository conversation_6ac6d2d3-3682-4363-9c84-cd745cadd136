
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rastreabilidade de Compras - FYRON MRP</title>
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .search-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .search-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-group {
            flex: 1;
            min-width: 200px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }

        .search-group input, .search-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e6ed;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-group input:focus, .search-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #219a52;
        }

        .trace-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .trace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .trace-title {
            font-size: 1.5rem;
            color: #2c3e50;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-criada {
            background: #e8f4fd;
            color: #0c5460;
        }

        .status-pedido-gerado {
            background: #d4edda;
            color: #155724;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .trace-timeline {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .timeline-item {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            top: 30px;
            right: -10px;
            width: 20px;
            height: 2px;
            background: #bdc3c7;
        }

        .timeline-item:last-child::after {
            display: none;
        }

        .timeline-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .timeline-icon.active {
            background: #27ae60;
        }

        .timeline-icon.inactive {
            background: #bdc3c7;
        }

        .timeline-text {
            font-size: 14px;
            color: #7f8c8d;
        }

        .details-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .detail-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .detail-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .detail-list {
            list-style: none;
        }

        .detail-list li {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-list li:last-child {
            border-bottom: none;
        }

        .history-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .history-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }

        .history-date {
            font-size: 12px;
            color: #7f8c8d;
            margin-right: 15px;
            min-width: 100px;
        }

        .history-user {
            font-weight: 600;
            color: #2c3e50;
            margin-right: 15px;
            min-width: 100px;
        }

        .history-action {
            flex: 1;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #3498db;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .details-section {
                grid-template-columns: 1fr;
            }
            
            .trace-timeline {
                flex-direction: column;
                gap: 10px;
            }
            
            .timeline-item::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Rastreabilidade de Compras</h1>
            <p>Acompanhe o fluxo completo das suas solicitações</p>
        </div>

        <div class="search-section">
            <div class="search-row">
                <div class="search-group">
                    <label for="searchType">Buscar por:</label>
                    <select id="searchType">
                        <option value="numero">Número da Solicitação</option>
                        <option value="produto">Código do Produto</option>
                        <option value="fornecedor">Fornecedor</option>
                    </select>
                </div>
                <div class="search-group">
                    <label for="searchValue">Valor:</label>
                    <input type="text" id="searchValue" placeholder="Digite o valor da busca">
                </div>
                <div class="search-group">
                    <label for="searchPeriod">Período:</label>
                    <select id="searchPeriod">
                        <option value="7">Últimos 7 dias</option>
                        <option value="30">Últimos 30 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="all">Todos</option>
                    </select>
                </div>
                <div class="search-group">
                    <button class="btn" onclick="searchTraceability()">🔍 Buscar</button>
                </div>
            </div>
        </div>

        <div id="resultsContainer">
            <div class="no-results">
                <i>📋</i>
                <h3>Nenhuma busca realizada</h3>
                <p>Use os filtros acima para encontrar a rastreabilidade desejada</p>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-firestore-compat.js"></script>
    <script src="config/firebase-config.js"></script>
    <script src="services/traceability-service-enhanced.js"></script>
    
    <script>
        // Variáveis globais
        let db;
        let traceabilityService;

        // Inicializar quando o DOM estiver pronto
        document.addEventListener('DOMContentLoaded', async function() {
            // Aguardar Firebase estar disponível
            while (!window.db) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            db = window.db;
            traceabilityService = window.TraceabilityService;
            
            console.log('🔗 Página de Rastreabilidade iniciada');
            
            // Configurar enter para buscar
            document.getElementById('searchValue').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchTraceability();
                }
            });
        });

        async function searchTraceability() {
            const searchType = document.getElementById('searchType').value;
            const searchValue = document.getElementById('searchValue').value.trim();
            const searchPeriod = document.getElementById('searchPeriod').value;
            
            if (!searchValue) {
                alert('Por favor, digite um valor para buscar');
                return;
            }

            showLoading();
            
            try {
                let results = [];
                
                if (searchType === 'numero') {
                    results = await searchByNumber(searchValue);
                } else if (searchType === 'produto') {
                    results = await searchByProduct(searchValue);
                } else if (searchType === 'fornecedor') {
                    results = await searchBySupplier(searchValue);
                }

                // Filtrar por período
                if (searchPeriod !== 'all') {
                    const days = parseInt(searchPeriod);
                    const cutoffDate = new Date();
                    cutoffDate.setDate(cutoffDate.getDate() - days);
                    
                    results = results.filter(result => {
                        const resultDate = result.dataCriacao?.toDate ? result.dataCriacao.toDate() : new Date(result.dataCriacao);
                        return resultDate >= cutoffDate;
                    });
                }

                displayResults(results);
                
            } catch (error) {
                console.error('❌ Erro na busca:', error);
                showError('Erro ao buscar rastreabilidade: ' + error.message);
            }
        }

        async function searchByNumber(numero) {
            const snapshot = await db.collection('rastreabilidade')
                .where('numeroSolicitacao', '>=', numero)
                .where('numeroSolicitacao', '<=', numero + '\uf8ff')
                .get();
            
            return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        }

        async function searchByProduct(codigo) {
            const snapshot = await db.collection('rastreabilidade').get();
            
            return snapshot.docs
                .map(doc => ({ id: doc.id, ...doc.data() }))
                .filter(item => 
                    item.itens?.some(i => 
                        i.codigo?.toLowerCase().includes(codigo.toLowerCase()) ||
                        i.descricao?.toLowerCase().includes(codigo.toLowerCase())
                    )
                );
        }

        async function searchBySupplier(fornecedor) {
            const snapshot = await db.collection('rastreabilidade').get();
            
            return snapshot.docs
                .map(doc => ({ id: doc.id, ...doc.data() }))
                .filter(item => 
                    item.cotacoes?.some(c => 
                        c.fornecedorNome?.toLowerCase().includes(fornecedor.toLowerCase())
                    ) ||
                    item.pedidosCompra?.some(p => 
                        p.fornecedorNome?.toLowerCase().includes(fornecedor.toLowerCase())
                    )
                );
        }

        function displayResults(results) {
            const container = document.getElementById('resultsContainer');
            
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="no-results">
                        <i>❌</i>
                        <h3>Nenhum resultado encontrado</h3>
                        <p>Tente ajustar os filtros de busca</p>
                    </div>
                `;
                return;
            }

            const html = results.map(result => createTraceCard(result)).join('');
            container.innerHTML = html;
        }

        function createTraceCard(trace) {
            const statusClass = trace.status?.toLowerCase().replace(' ', '-') || 'pendente';
            const statusText = trace.status || 'Pendente';
            
            return `
                <div class="trace-card">
                    <div class="trace-header">
                        <div class="trace-title">
                            📋 ${trace.numeroSolicitacao || 'N/A'}
                        </div>
                        <div class="status-badge status-${statusClass}">
                            ${statusText}
                        </div>
                    </div>

                    <div class="trace-timeline">
                        <div class="timeline-item">
                            <div class="timeline-icon active">
                                📝
                            </div>
                            <div class="timeline-text">
                                Solicitação<br>
                                <small>${formatDate(trace.dataCriacao)}</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-icon ${trace.cotacoes?.length > 0 ? 'active' : 'inactive'}">
                                💰
                            </div>
                            <div class="timeline-text">
                                Cotações<br>
                                <small>${trace.cotacoes?.length || 0} geradas</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-icon ${trace.pedidosCompra?.length > 0 ? 'active' : 'inactive'}">
                                🛒
                            </div>
                            <div class="timeline-text">
                                Pedidos<br>
                                <small>${trace.pedidosCompra?.length || 0} gerados</small>
                            </div>
                        </div>
                    </div>

                    <div class="details-section">
                        <div class="detail-card">
                            <h4>📦 Itens (${trace.itens?.length || 0})</h4>
                            <ul class="detail-list">
                                ${trace.itens?.slice(0, 3).map(item => `
                                    <li>${item.codigo} - ${item.descricao?.substring(0, 30)}...</li>
                                `).join('') || '<li>Nenhum item</li>'}
                                ${trace.itens?.length > 3 ? `<li><em>+ ${trace.itens.length - 3} itens...</em></li>` : ''}
                            </ul>
                        </div>

                        <div class="detail-card">
                            <h4>💰 Cotações (${trace.cotacoes?.length || 0})</h4>
                            <ul class="detail-list">
                                ${trace.cotacoes?.map(cotacao => `
                                    <li>${cotacao.numeroCotacao} - ${cotacao.fornecedorNome}</li>
                                `).join('') || '<li>Nenhuma cotação</li>'}
                            </ul>
                        </div>

                        <div class="detail-card">
                            <h4>🛒 Pedidos (${trace.pedidosCompra?.length || 0})</h4>
                            <ul class="detail-list">
                                ${trace.pedidosCompra?.map(pedido => `
                                    <li>${pedido.numeroPedido} - ${pedido.fornecedorNome}</li>
                                `).join('') || '<li>Nenhum pedido</li>'}
                            </ul>
                        </div>
                    </div>

                    <div class="history-section">
                        <h4>📜 Histórico de Movimentações</h4>
                        ${trace.historico?.slice(0, 5).map(h => `
                            <div class="history-item">
                                <div class="history-date">${formatDate(h.data)}</div>
                                <div class="history-user">${h.usuario}</div>
                                <div class="history-action">
                                    <strong>${h.acao}</strong>: ${h.detalhes}
                                </div>
                            </div>
                        `).join('') || '<p>Nenhum histórico disponível</p>'}
                        ${trace.historico?.length > 5 ? `<p><em>+ ${trace.historico.length - 5} movimentações anteriores...</em></p>` : ''}
                    </div>
                </div>
            `;
        }

        function showLoading() {
            document.getElementById('resultsContainer').innerHTML = `
                <div class="loading">
                    <i>⏳</i>
                    <h3>Carregando rastreabilidade...</h3>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('resultsContainer').innerHTML = `
                <div class="no-results">
                    <i>❌</i>
                    <h3>Erro</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        function formatDate(date) {
            if (!date) return 'N/A';
            
            const d = date.toDate ? date.toDate() : new Date(date);
            return d.toLocaleDateString('pt-BR') + ' ' + d.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }
    </script>
</body>
</html>
