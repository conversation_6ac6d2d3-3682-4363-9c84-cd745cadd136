<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <script>
        if (!localStorage.getItem('currentUser')) {
            window.location.href = 'login.html';
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FYRON MRP - Sistema de Gestão Empresarial</title>
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f8f9fa;
            --sidebar-bg: #1e2832;
            --sidebar-hover: #2a3441;
            --text-color: #212529;
            --text-secondary: #fff;
            --text-muted: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --totvs-blue: #0854a0;
            --totvs-gray: #f5f5f5;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--totvs-gray);
            color: var(--text-color);
            overflow-x: hidden;
            font-size: 14px;
            line-height: 1.5;
        }

        body.loading::before {
            content: 'Carregando sistema...';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
            color: var(--primary-color);
            background: white;
            padding: 20px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            z-index: 9999;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100%;
            width: 280px;
            background: linear-gradient(180deg, var(--sidebar-bg) 0%, #1a242e 100%);
            padding: 20px;
            transition: all 0.3s ease;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .logo {
            display: block !important;
            width: 100% !important;
            max-width: 280px !important;
            height: auto !important;
            margin: 0 auto 20px auto !important;
            padding: 0 10px;
        }

        /* Estilos para o logo interativo */
        .logo-placeholder {
            transition: all 0.3s ease;
        }

        .logo-placeholder:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
        }

        #logoConfigBtn {
            transition: all 0.2s ease;
        }

        #logoConfigBtn:hover {
            background: rgba(255,255,255,1) !important;
            transform: scale(1.1);
        }

        #logoMenu {
            animation: fadeIn 0.2s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        /* Responsividade do logo */
        @media (max-width: 768px) {
            #logoContainer {
                width: 100% !important;
                max-width: 250px;
            }

            #companyLogo {
                width: 100% !important;
                max-width: 250px;
                height: auto !important;
                min-height: 100px;
            }
        }

        .sidebar .user-info {
            color: var(--text-secondary);
            margin-bottom: 30px;
            text-align: center;
        }

        .sidebar .user-info .username {
            font-weight: bold;
            color: var(--primary-color);
        }

        .sidebar .logout-btn {
            background: none;
            border: none;
            color: var(--danger-color);
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }

        .sidebar .logout-btn:hover {
            color: #a30000;
        }

        .nav-list {
            list-style: none;
        }

        .nav-list li {
            margin-bottom: 5px;
        }

        .nav-list .section-title {
            color: #9191a5;
            font-size: 14px;
            padding: 10px 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-list .menu-button {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: var(--text-secondary);
            background: rgba(255, 255, 255, 0.08);
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: left;
            margin-bottom: 2px;
            position: relative;
            overflow: hidden;
        }

        .nav-list .menu-button::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .nav-list .menu-button i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        .nav-list .menu-button:hover {
            background: var(--sidebar-hover);
            color: #ffffff;
            transform: translateX(3px);
            box-shadow: 0 2px 8px rgba(8, 84, 160, 0.2);
        }

        .nav-list .menu-button:hover::before {
            transform: scaleY(1);
        }

        .nav-list .menu-button.highlight-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0a4d8c 100%);
            color: white;
            font-weight: 600;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            box-shadow: 0 2px 8px rgba(8, 84, 160, 0.3);
        }

        .nav-list .menu-button.highlight-btn::before {
            background: #ffffff;
        }

        .nav-list .menu-button.highlight-btn:hover {
            background: linear-gradient(135deg, #0a4d8c 0%, var(--primary-color) 100%);
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(8, 84, 160, 0.4);
            color: #ffffff;
        }

        .nav-list .menu-button.highlight-btn::after {
            content: "NOVO";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #ffc107, #ff9800);
            color: #000;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: bold;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .nav-list .financial {
            background: var(--success-color);
        }

        .nav-list .financial:hover {
            background: #0d6e36;
        }

        .nav-list .settings {
            background: var(--warning-color);
        }

        .nav-list .settings:hover {
            background: #d66a0b;
        }

        .main-content {
            margin-left: 280px;
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
            background: var(--totvs-gray);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #0a4d8c, var(--primary-color));
        }

        .main-content h1 {
            text-align: center;
            margin-bottom: 40px;
            color: var(--primary-color);
            font-size: 28px;
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .center-image {
            display: block !important;
            max-width: 100% !important;
            height: auto !important;
            margin: 20px auto 0 auto !important;
            border: 1px solid #d4d4d4;
            border-radius: 4px;
        }

        .version-info {
            text-align: center;
            color: #666;
            font-size: 0.8em;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .menu-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 15px;
            font-size: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(8, 84, 160, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .menu-toggle:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(8, 84, 160, 0.4);
        }

        /* Header do Main Content */
        .main-header {
            background: white;
            padding: 20px 30px;
            margin: -30px -30px 30px -30px;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .header-content-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-section h1 {
            margin: 0;
            font-size: 24px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .welcome-section p {
            margin: 5px 0 0 0;
            color: var(--text-muted);
            font-size: 14px;
        }

        .system-info {
            text-align: right;
            font-size: 13px;
            color: var(--text-muted);
        }

        .system-info .status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-top: 5px;
        }
        .export-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .export-btn:hover {
            background: var(--primary-hover);
        }

        .highlight-btn {
            background-color: #ff9800; /*  Added styling for highlighted button */
            color: white;
        }

        .highlight-btn:hover {
            background-color: #e68a00; /*  Hover effect for highlighted button */
        }

        /* Animação especial para IA Sistema Autônomo */
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 6px 20px rgba(108, 92, 231, 0.5);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                left: -280px;
                box-shadow: none;
            }

            .sidebar.active {
                left: 0;
                box-shadow: 4px 0 20px rgba(0,0,0,0.3);
            }

            .main-content {
                margin-left: 0;
                padding: 20px 15px;
            }

            .main-header {
                margin: -20px -15px 20px -15px;
                padding: 15px 20px;
            }

            .header-content-main {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .welcome-section h1 {
                font-size: 20px;
            }

            .menu-toggle {
                display: block;
            }

            .nav-list .menu-button {
                font-size: 15px;
                padding: 14px 15px;
            }

            .nav-list .menu-button i {
                margin-right: 15px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px 10px;
            }

            .main-header {
                margin: -15px -10px 15px -10px;
                padding: 12px 15px;
            }

            .welcome-section h1 {
                font-size: 18px;
            }

            .center-image {
                max-width: 90% !important;
            }
        }

        .section-toggle {
            background: none;
            color: #9191a5;
            font-size: 14px;
            border: none;
            width: 100%;
            text-align: left;
            padding: 10px 15px;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        .section-toggle:hover {
            background-color: var(--sidebar-hover);
            color: #fff;
        }
        .section-toggle:focus {
            outline: none;
        }
        .accordion-section {
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
        }
        .accordion-content {
            display: none;
            padding-left: 0;
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .accordion-content.active {
            display: block;
            opacity: 1;
            max-height: 1000px;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 1000px;
            }
        }

        #companyLogo {
            display: block !important;
            max-width: 100% !important;
            height: auto !important;
        }
        #centerImage {
            display: block !important;
            max-width: 100% !important;
            height: auto !important;
            margin: 20px auto 0 auto !important;
        }
    </style>

    <!-- Script para funções do logo (definidas antes do HTML) -->
    <script>
        // Funções do logo definidas antecipadamente
        function toggleLogoMenu() {
            const menu = document.getElementById('logoMenu');
            if (menu) {
                menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
            }
        }

        function selectLogo() {
            const input = document.getElementById('logoFileInput');
            if (input) {
                input.click();
            }
        }

        function resetLogo() {
            if (confirm('Deseja remover o logo atual e voltar ao placeholder?')) {
                localStorage.removeItem('fyronLogo');
                location.reload();
            }
        }

        function downloadLogo() {
            const savedLogo = localStorage.getItem('fyronLogo');
            if (savedLogo) {
                const link = document.createElement('a');
                link.href = savedLogo;
                link.download = 'fyron_logo.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showNotification('✅ Logo baixado com sucesso!', 'success');
            } else {
                alert('❌ Nenhum logo carregado para baixar.');
            }
            toggleLogoMenu();
        }

        function loadSelectedLogo(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Verificar se é uma imagem
                if (!file.type.startsWith('image/')) {
                    alert('❌ Por favor, selecione um arquivo de imagem válido (PNG, JPG, GIF, etc.)');
                    return;
                }

                // Verificar tamanho do arquivo (máximo 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('❌ O arquivo é muito grande. Por favor, selecione uma imagem menor que 5MB.');
                    return;
                }

                const reader = new FileReader();

                reader.onload = function(e) {
                    const logoContainer = document.getElementById('companyLogo');

                    // Limpar placeholder
                    logoContainer.innerHTML = '';
                    logoContainer.style.background = 'none';
                    logoContainer.style.boxShadow = 'none';
                    logoContainer.style.borderRadius = '0';
                    logoContainer.style.cursor = 'default';
                    logoContainer.onclick = null;

                    // Criar imagem
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = 'FYRON MRP - Sistema de Gestão Empresarial';
                    img.style.width = '280px';
                    img.style.height = 'auto';
                    img.style.maxHeight = '120px';
                    img.style.objectFit = 'contain';
                    img.className = 'logo';

                    logoContainer.appendChild(img);

                    // Salvar no localStorage para persistir
                    localStorage.setItem('fyronLogo', e.target.result);

                    // Mostrar notificação de sucesso
                    showNotification('✅ Logo carregado com sucesso!', 'success');
                    console.log('✅ Logo FYRON MRP carregado:', file.name);
                };

                reader.onerror = function() {
                    alert('❌ Erro ao carregar a imagem. Tente novamente.');
                };

                reader.readAsDataURL(file);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            `;

            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };

            notification.style.background = colors[type] || colors.info;
            if (type === 'warning') {
                notification.style.color = '#212529';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Carregar logo salvo quando a página carregar
        document.addEventListener('DOMContentLoaded', function() {
            const savedLogo = localStorage.getItem('fyronLogo');
            if (savedLogo) {
                const logoContainer = document.getElementById('companyLogo');
                if (logoContainer) {
                    // Limpar placeholder
                    logoContainer.innerHTML = '';
                    logoContainer.style.background = 'none';
                    logoContainer.style.boxShadow = 'none';
                    logoContainer.style.borderRadius = '0';
                    logoContainer.style.cursor = 'default';
                    logoContainer.onclick = null;

                    // Criar imagem
                    const img = document.createElement('img');
                    img.src = savedLogo;
                    img.alt = 'FYRON MRP - Sistema de Gestão Empresarial';
                    img.style.width = '280px';
                    img.style.height = 'auto';
                    img.style.maxHeight = '120px';
                    img.style.objectFit = 'contain';
                    img.className = 'logo';

                    logoContainer.appendChild(img);
                    console.log('✅ Logo salvo carregado com sucesso!');
                }
            }

            // Fechar menu ao clicar fora
            document.addEventListener('click', function(event) {
                const menu = document.getElementById('logoMenu');
                const button = document.getElementById('logoConfigBtn');

                if (menu && button && !menu.contains(event.target) && !button.contains(event.target)) {
                    menu.style.display = 'none';
                }
            });
        });
    </script>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Logo FYRON MRP com seletor de arquivo -->
        <div id="logoContainer" style="position: relative;">
            <div id="companyLogo" class="logo logo-placeholder" style="width: 280px; height: 120px; display: flex; align-items: center; justify-content: center; background: none; border-radius: 10px; color: white; font-family: 'Arial Black', sans-serif; text-align: center; box-shadow: none; cursor: pointer;" onclick="selectLogo()">
                <img src="assets/fyron_logo_compact.svg" alt="FYRON MRP - Sistema de Gestão Empresarial" style="width: 100%; height: auto; max-height: 120px; object-fit: contain;" class="logo">
            </div>

            <!-- Input de arquivo oculto -->
            <input type="file" id="logoFileInput" accept="image/*" style="display: none;" onchange="loadSelectedLogo(this)">

            <!-- Botão de configuração do logo -->
            <div style="position: absolute; top: 5px; right: 5px;">
                <button id="logoConfigBtn" onclick="toggleLogoMenu()" style="background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 5px rgba(0,0,0,0.2);" title="Opções do logo">
                    <i class="fas fa-cog" style="color: #666; font-size: 12px;"></i>
                </button>

                <!-- Menu de opções do logo -->
                <div id="logoMenu" style="display: none; position: absolute; top: 35px; right: 0; background: white; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); min-width: 180px; z-index: 1000;">
                    <div onclick="selectLogo()" style="padding: 12px 16px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 8px;" onmouseover="this.style.background='#f5f5f5'" onmouseout="this.style.background='white'">
                        <i class="fas fa-upload" style="color: #0854a0; width: 16px;"></i>
                        <span style="font-size: 14px;">Carregar Logo</span>
                    </div>
                    <div onclick="resetLogo()" style="padding: 12px 16px; cursor: pointer; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 8px;" onmouseover="this.style.background='#f5f5f5'" onmouseout="this.style.background='white'">
                        <i class="fas fa-trash" style="color: #dc3545; width: 16px;"></i>
                        <span style="font-size: 14px;">Remover Logo</span>
                    </div>
                    <div onclick="downloadLogo()" style="padding: 12px 16px; cursor: pointer; display: flex; align-items: center; gap: 8px;" onmouseover="this.style.background='#f5f5f5'" onmouseout="this.style.background='white'">
                        <i class="fas fa-download" style="color: #28a745; width: 16px;"></i>
                        <span style="font-size: 14px;">Baixar Logo</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="user-info">
            <div style="background: #27ae60; color: white; padding: 8px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-bottom: 10px; text-align: center;">
                <i class="fas fa-star"></i> Sistema Atualizado
            </div>
            <div style="font-size: 11px; color: #7f8c8d; margin-bottom: 15px; text-align: center;">
                Módulos Modernizados • Análise Inteligente • Gestão Avançada
            </div>
            <div>Usuário: <span class="username" id="currentUserName">-</span></div>
            <div>Nível: <span id="currentUserLevel">-</span></div>
            <button class="logout-btn" onclick="logout()">Sair</button>
        </div>

        <!-- Busca Rápida -->
        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px; border: 1px solid rgba(255,255,255,0.1);">
            <div style="color: #ffc107; font-size: 12px; font-weight: bold; margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-search"></i> BUSCA RÁPIDA
            </div>
            <input type="text" id="searchMenu" placeholder="Digite para buscar módulos..."
                   style="width: 100%; padding: 8px 12px; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: rgba(255,255,255,0.1); color: white; font-size: 12px;"
                   oninput="filtrarMenu(this.value)">
            <div style="font-size: 10px; color: #7f8c8d; margin-top: 5px;">
                Ex: "apontamentos", "estoque", "produção"
            </div>
        </div>

        <ul class="nav-list">
            <li class="accordion-section">
                <button class="section-toggle">Financeiro <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button financial" data-level="2" data-permission="contas_pagar" onclick="abrirTela('contasPagar')"><i class="fas fa-money-bill"></i> Contas a Pagar</button></li>
                    <li><button class="menu-button financial" data-level="2" data-permission="contas_receber" onclick="abrirTela('contasReceber')"><i class="fas fa-hand-holding-usd"></i> Contas a Receber</button></li>
                    <li><button class="menu-button financial" data-level="2" data-permission="condicoes_pagamento" onclick="abrirTela('condicoesPagamento')"><i class="fas fa-file-invoice"></i> Condições de Pagamento</button></li>
                    <li><button class="menu-button financial" data-level="2" data-permission="fluxo_caixa" onclick="abrirTela('fluxoCaixa')"><i class="fas fa-chart-line"></i> Fluxo de Caixa</button></li>
                    <li><button class="menu-button financial" data-level="2" data-permission="analise_credito" onclick="abrirTela('analiseCredito')"><i class="fas fa-credit-card"></i> Análise de Crédito</button></li>
                    <li><button class="menu-button financial highlight-btn" data-level="2" data-permission="faturamento" onclick="abrirTela('faturamento')"><i class="fas fa-receipt"></i> Faturamento</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Vendas <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- Gestão Unificada de Vendas -->
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="gestao_vendas" onclick="abrirTela('gestaoVendas')"><i class="fas fa-chart-line"></i> Gestão Unificada de Vendas</button></li>

                    <!-- Módulos Tradicionais -->
                    <li><button class="menu-button" data-level="2" data-permission="orcamentos" onclick="abrirTela('orcamentos')"><i class="fas fa-file-invoice-dollar"></i> Orçamentos (Legado)</button></li>
                    <li><a class="menu-button highlight-btn" data-level="2" href="orcamento-vendas-integrado.html" style="background: linear-gradient(135deg, #0854a0, #0a4d8c); border: 2px solid rgba(255,255,255,0.2);"><i class="fas fa-chart-line"></i> 🏢 Sistema de Orçamentos (Padrão TOTVS)</a></li>
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="estrutura_orcamento" onclick="abrirTela('estruturaOrcamento')"><i class="fas fa-calculator"></i> Orçamento de Estruturas</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="pedidos_venda" onclick="abrirTela('pedidosVenda')"><i class="fas fa-shopping-cart"></i> Pedidos de Venda</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="tabela_precos" onclick="abrirTela('tabelaPrecos')"><i class="fas fa-tags"></i> Tabela de Preços</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="condicoes_especiais" onclick="abrirTela('condicoesEspeciais')"><i class="fas fa-percentage"></i> Condições Especiais</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Compras <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- Workflow de Compras -->
                    <li><button class="menu-button" data-level="2" data-permission="solicitacao_compras" onclick="abrirTela('solicitacaoCompras')"><i class="fas fa-shopping-cart"></i> Solicitação de Compras</button></li>
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="cotacoes" onclick="abrirTela('cotacoes')"><i class="fas fa-file-alt"></i> Cotações (Melhorada)</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="pedidos_compra" onclick="abrirTela('pedidosCompra')"><i class="fas fa-file-signature"></i> Pedidos de Compra</button></li>

                    <!-- Gestão Unificada -->
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="gestao_compras" onclick="abrirTela('gestaoCompras')"><i class="fas fa-tasks"></i> Gestão Unificada de Compras</button></li>

                    <!-- IA Monitor de Compras -->
                    <li><a class="menu-button highlight-btn" data-level="2" href="ia_monitor_compras.html" style="background: linear-gradient(45deg, #9b59b6, #e67e22); color: white; border: none; animation: pulse 2s infinite;"><i class="fas fa-robot"></i> 🤖 IA Monitor de Compras</a></li>

                    <!-- Relatórios de Compras -->
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_necessidades_compras" onclick="abrirTela('relatorioNecessidadesCompras')"><i class="fas fa-shopping-basket"></i> Controle de Necessidades</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_necessidade_materiais" onclick="abrirTela('relatorioNecessidadeMateriais')"><i class="fas fa-boxes"></i> Produtos x OPs</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Engenharia <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_produtos" onclick="abrirTela('cadastroProduto')"><i class="fas fa-box"></i> Cadastro de Produtos</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_grupo" onclick="abrirTela('cadastroGrupo')"><i class="fas fa-layer-group"></i> Cadastro de Grupo</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_familia" onclick="abrirTela('cadastroFamilia')"><i class="fas fa-object-group"></i> Cadastro de Família</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_recursos" onclick="abrirTela('cadastroRecursos')"><i class="fas fa-tools"></i> Cadastro de Recursos</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="estrutura_produtos" onclick="abrirTela('estruturaProdutos')"><i class="fas fa-sitemap"></i> Estrutura de Produtos</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="estrutura_nova" onclick="abrirTela('estruturaNova')"><i class="fas fa-project-diagram"></i> Estrutura Drop</button></li>
                    <li><a class="menu-button" data-level="1" data-permission="relatorio_explosao_estruturas" href="relatorio_explosao_estruturas.html"><i class="fas fa-bomb"></i> Explosão Estruturas</a></li>
                    <li><a class="menu-button highlight-btn" data-level="1" data-permission="relatorio_estruturas_pendentes" href="relat_estru_pendente.html"><i class="fas fa-exclamation-triangle"></i> Estruturas Pendentes</a></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Cadastros <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button" data-level="1" data-permission="centralDocumentos" onclick="abrirTela('centralDocumentos')"><i class="fas fa-folder"></i> Central de Documentos</button></li>

                    <!-- Cadastros de Pessoas -->
                    <li><button class="menu-button highlight-btn" data-level="1" data-permission="cadastro_fornecedores" onclick="abrirTela('cadastroFornecedores')"><i class="fas fa-address-book"></i> Clientes e Fornecedores (Modernizado)</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_clientes" onclick="abrirTela('cadastroClientes')"><i class="fas fa-users"></i> Cadastro de Clientes (Legado)</button></li>

                    <!-- Outros Cadastros -->
                    <li><button class="menu-button" data-level="1" data-permission="cadastro_operacoes" onclick="abrirTela('cadastroOperacoes')"><i class="fas fa-cogs"></i> Cadastro de Operações</button></li>
                    <li><button class="menu-button" data-level="9" data-permission="cadastro_centro_custo" onclick="abrirTela('cadastroCentroCusto')"><i class="fas fa-coins"></i> Cadastro de Centro de Custo</button></li>
                    <li><button class="menu-button" data-level="9" data-permission="cadastro_setores" onclick="abrirTela('cadastroSetores')"><i class="fas fa-building"></i> Cadastro de Setores</button></li>
                    <li><button class="menu-button" data-level="9" data-permission="cadastro_usuarios" onclick="abrirTela('cadastroUsuarios')"><i class="fas fa-user-plus"></i> Cadastro de Usuários</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="produtos_fornecedores" onclick="abrirTela('produtosFornecedores')"><i class="fas fa-link"></i> Produtos x Fornecedores</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Produção <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button highlight-btn" data-level="3" data-permission="ordens_producao" onclick="abrirTela('ordensProducao')"><i class="fas fa-industry"></i> Ordens de Produção (Melhorada)</button></li>
                    <li><a class="menu-button" data-level="3" data-permission="relatorio_explosao_op" href="relatorio_explosao_estruturas.html"><i class="fas fa-bomb"></i> Explosão Produto</a></li>
                    <li><a class="menu-button" data-level="3" href="orden_producao.html"><i class="fas fa-cogs"></i> Nova Geração Avançada de OPs</a></li>
                    <li><a class="menu-button" data-level="3" href="recalcular_explosao_op.html"><i class="fas fa-sync-alt"></i> Recalcular Explosão de OP</a></li>
                    <li><button class="menu-button" data-level="9" data-permission="zerar_ordens" onclick="abrirTela('zerarOrdens')"><i class="fas fa-eraser"></i> Zerar Ordens</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="op_alteracao" onclick="abrirTela('alterarOp')"><i class="fas fa-edit"></i> Alterar OP</button></li>

                    <!-- NOVA SEÇÃO: MÓDULOS MODERNIZADOS -->
                    <li style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.1);"><div style="color: #ffc107; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🚀 MÓDULOS MODERNIZADOS</div></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="apontamentos_integrado_novo.html" style="background: linear-gradient(135deg, #0854a0, #0a4d8c); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-clipboard-check"></i> 🏭 Apontamentos (Novo)</a></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="analise_producao.html" style="background: linear-gradient(135deg, #28a745, #20c997); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-chart-line"></i> 📊 Análise de Produção</a></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="pcp_planejamento.html" style="background: linear-gradient(135deg, #9b59b6, #8e44ad); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-industry"></i> 🏗️ PCP - Planejamento</a></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="mrp_integrado_totvs.html" style="background: linear-gradient(135deg, #e67e22, #d35400); border: 1px solid rgba(255,255,255,0.2); animation: pulse 3s infinite;"><i class="fas fa-cogs"></i> 🔧 MRP Integrado (Novo)</a></li>

                    <!-- MÓDULOS LEGADOS -->
                    <li style="margin-top: 10px;"><div style="color: #6c757d; font-size: 10px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 5px;">📁 LEGADOS</div></li>
                    <li><button class="menu-button" data-level="3" data-permission="apontamentos" onclick="abrirTela('apontamentos')" style="opacity: 0.7;"><i class="fas fa-clipboard"></i> Apontamentos (Legado)</button></li>
                    <li><button class="menu-button" data-level="9" data-permission="estorno" onclick="abrirTela('estorno')"><i class="fas fa-undo"></i> Estorno de Movimentos</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Almoxarifado <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- Recebimento e Qualidade -->
                    <li><button class="menu-button" data-level="3" data-permission="recebimento_materiais" onclick="abrirTela('recebimentoMateriais')"><i class="fas fa-truck-loading"></i> Recebimento de Materiais</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="estoque_qualidade" onclick="abrirTela('inspecaoQualidade')"><i class="fas fa-clipboard-check"></i> Estoque Qualidade</button></li>

                    <!-- Movimentações de Almoxarifado -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #17a2b8; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🔄 MOVIMENTAÇÕES</div></li>
                    <li><button class="menu-button" data-level="3" data-permission="estoques" onclick="abrirTela('movimentacaoArmazem')"><i class="fas fa-exchange-alt"></i> Movimentação entre Armazéns</button></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="historico_transferencias.html" style="background: linear-gradient(135deg, #17a2b8, #138496); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-history"></i> 📋 Histórico de Transferências</a></li>
                    <li><button class="menu-button" data-level="3" data-permission="estoques" onclick="abrirTela('ajusteEstoque')"><i class="fas fa-balance-scale"></i> Ajuste de Estoque</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">📦 Estoques <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- CENTRAL UNIFICADA -->
                    <li style="margin-bottom: 15px;"><div style="color: #28a745; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🎯 SISTEMA UNIFICADO</div></li>
                    <li><a class="menu-button highlight-btn" data-level="1" href="central_estoque.html" style="background: linear-gradient(135deg, #28a745, #20c997); border: 2px solid rgba(255,255,255,0.3); font-size: 16px; padding: 15px;"><i class="fas fa-tachometer-alt"></i> 🎯 CENTRAL DE ESTOQUE</a></li>

                    <!-- MÓDULOS PRINCIPAIS -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #ffc107; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🚀 MÓDULOS PRINCIPAIS</div></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="editor_saldos_estoque.html" style="background: linear-gradient(135deg, #4f46e5, #7c3aed); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-edit"></i> 🎛️ Editor de Saldos (Principal)</a></li>
                    <li><a class="menu-button highlight-btn" data-level="3" href="gestao_estoque.html" style="background: linear-gradient(135deg, #fd7e14, #e55a00); border: 1px solid rgba(255,255,255,0.2);"><i class="fas fa-boxes"></i> 📊 Gestão Avançada</a></li>



                    <!-- MOVIMENTAÇÕES -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #17a2b8; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🔄 MOVIMENTAÇÕES</div></li>
                    <li><a class="menu-button highlight-btn" data-level="2" href="relatorio_movimentacoes.html" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; font-weight: bold;"><i class="fas fa-exchange-alt"></i> Relatório de Movimentações</a></li>

                    <!-- AJUSTES E CORREÇÕES -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #dc3545; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🔧 AJUSTES</div></li>
                    <li><a class="menu-button" data-level="9" href="listar_problemas_saldos.html"><i class="fas fa-search"></i> Análise de Problemas</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="zerar_estoques_saldos_iniciais.html"><i class="fas fa-trash-restore"></i> Ferramentas Admin</a></li>

                    <!-- CONFIGURAÇÃO INICIAL -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #28a745; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🆕 NOVOS CLIENTES</div></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="saldos_iniciais.html" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; border: none; font-weight: bold;"><i class="fas fa-plus-circle"></i> 🆕 Configuração Inicial</a></li>

                    <!-- INVENTÁRIO -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #6f42c1; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">📋 INVENTÁRIO</div></li>
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_inventario" onclick="abrirTela('relatorioInventario')"><i class="fas fa-chart-bar"></i> Relatório de Inventário</button></li>

                    <!-- ARMAZÉNS -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #fd7e14; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🏪 ARMAZÉNS</div></li>
                    <li><button class="menu-button" data-level="9" data-permission="cadastro_armazem" onclick="abrirTela('cadastroArmazem')"><i class="fas fa-warehouse"></i> Cadastro de Armazém</button></li>

                    <!-- FERRAMENTAS AVANÇADAS -->
                    <li style="margin-top: 15px; margin-bottom: 10px;"><div style="color: #6c757d; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">🛠️ FERRAMENTAS</div></li>
                    <li><a class="menu-button" data-level="9" href="listar_problemas_saldos.html"><i class="fas fa-list-alt"></i> Problemas de Saldo</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="diagnostico_funcionando.html"><i class="fas fa-stethoscope"></i> Diagnóstico do Sistema</a></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle" id="menuQualidade">🔍 Qualidade <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- FASE 1 - PROCESSOS PRINCIPAIS (PQ001-PQ007) -->

                    <!-- 🔍 INSPEÇÃO E CONTROLE -->
                    <li><button class="menu-button" id="btnInspecaoRecebimento" data-level="3" data-permission="inspecao_recebimento" onclick="abrirTela('inspecaoRecebimento')"><i class="fas fa-search"></i> PQ001 - Inspeção de Recebimento</button></li>
                    <li><button class="menu-button" id="btnInspecaoProcesso" data-level="3" data-permission="inspecao_processo" onclick="abrirTela('inspecaoProcesso')"><i class="fas fa-cogs"></i> PQ002 - Inspeção no Processo</button></li>
                    <li><button class="menu-button" id="btnLiberacaoQualidade" data-level="3" data-permission="liberacao_qualidade" onclick="abrirTela('liberacaoQualidade')"><i class="fas fa-check-circle"></i> PQ003 - Liberação de Qualidade</button></li>
                    <li><button class="menu-button" id="btnArmazemQualidade" data-level="3" data-permission="armazem_qualidade" onclick="abrirTela('armazemQualidade')"><i class="fas fa-warehouse"></i> PQ004 - Armazém da Qualidade</button></li>

                    <!-- 👥 FORNECEDORES -->
                    <li><button class="menu-button" id="btnHomologacaoFornecedores" data-level="3" data-permission="homologacao_fornecedores" onclick="abrirTela('homologacaoFornecedores')"><i class="fas fa-user-check"></i> PQ005 - Homologação de Fornecedores</button></li>
                    <li><button class="menu-button" id="btnMetricasFornecedores" data-level="3" data-permission="metricas_fornecedores" onclick="abrirTela('metricasFornecedores')"><i class="fas fa-chart-line"></i> PQ006 - Métricas para Fornecedores</button></li>

                    <!-- ❌ NÃO CONFORMIDADES -->
                    <li><button class="menu-button" id="btnReprovasDevolucoes" data-level="3" data-permission="reprovas_devolucoes" onclick="abrirTela('reprovasDevolucoes')"><i class="fas fa-times-circle"></i> PQ007 - Reprovas e Devoluções</button></li>

                    <!-- 📋 ESPECIFICAÇÕES E CONFIGURAÇÕES -->
                    <li style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.1);"><div style="color: #ffc107; font-size: 11px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 8px;">⚙️ CONFIGURAÇÕES</div></li>
                    <li><button class="menu-button highlight-btn" data-level="3" data-permission="especificacoes_produtos" onclick="abrirTela('especificacoesProdutos')"><i class="fas fa-clipboard-list"></i> 📋 Especificações de Ensaios</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="qualidade_geral" onclick="abrirTela('qualidadeGeral')"><i class="fas fa-chart-bar"></i> 📊 Gestão da Qualidade</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="controle_qualidade" onclick="abrirTela('controleQualidade')"><i class="fas fa-tasks"></i> 🔧 Controle de Qualidade</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="inspecao_qualidade" onclick="abrirTela('inspecaoQualidade')"><i class="fas fa-search-plus"></i> 🔍 Liberação de Qualidade</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">🔧 MANUTENÇÃO <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- Gestão de Ordens -->
                    <li><button class="menu-button" data-level="3" data-permission="ordens_manutencao" onclick="abrirTela('ordensManutencao')"><i class="fas fa-wrench"></i> Ordens de Manutenção</button></li>
                    <li><button class="menu-button highlight-btn" data-level="4" data-permission="controle_ordens_abertas" onclick="abrirTela('controleOrdensAbertas')"><i class="fas fa-clipboard-list"></i> Controle de Ordens Abertas</button></li>
                    <li><button class="menu-button" data-level="4" data-permission="apontamento_ordens" onclick="abrirTela('apontamentoOrdens')"><i class="fas fa-stopwatch"></i> Apontamento de Ordens</button></li>

                    <!-- Gestão de Pessoal -->
                    <li><button class="menu-button" data-level="3" data-permission="funcionarios_manutencao" onclick="abrirTela('funcionariosManutencao')"><i class="fas fa-users-cog"></i> Funcionários da Manutenção</button></li>
                    <li><a class="menu-button" data-level="3" href="cadastro_funcionarios_manutencao.html"><i class="fas fa-user-plus"></i> Cadastro de Funcionários</a></li>

                    <!-- Planejamento -->
                    <li><button class="menu-button" data-level="5" data-permission="planejamento_manutencao" onclick="abrirTela('planejamentoManutencao')"><i class="fas fa-calendar-alt"></i> Planejamento de Manutenção</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="plano_manutencao" onclick="abrirTela('planoManutencao')"><i class="fas fa-cogs"></i> Planos de Manutenção</button></li>

                    <!-- Recursos e Equipamentos -->
                    <li><button class="menu-button" data-level="6" data-permission="recursos" onclick="abrirTela('recursos')"><i class="fas fa-industry"></i> Cadastro de Recursos</button></li>
                </ul>
            </li>

            <li class="accordion-section">
                <button class="section-toggle">Relatórios <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- Gerador Inteligente -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="gerador_relatorios_inteligente.html" style="background: linear-gradient(45deg, #2c3e50, #3498db); color: white; border: none; font-weight: bold;"><i class="fas fa-magic"></i> 📊 Gerador Inteligente de Relatórios</a></li>
                    <!-- Relatórios Financeiros -->
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_financeiro" onclick="abrirTela('relatorioFinanceiro')"><i class="fas fa-chart-line"></i> Relatório Financeiro</button></li>
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_custo" onclick="abrirTela('relatorioCusto')"><i class="fas fa-dollar-sign"></i> Relatório de Custo</button></li>

                    <!-- Relatórios de Estoque e Inventário -->
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_inventario" onclick="abrirTela('relatorioInventario')"><i class="fas fa-boxes"></i> Relatório de Inventário</button></li>
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="relatorio_controle_estoque" onclick="abrirTela('relatorioControleEstoque')"><i class="fas fa-warehouse"></i> Controle de Estoque</button></li>
                    <li><a class="menu-button highlight-btn" data-level="2" href="relatorio_movimentacoes.html" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; font-weight: bold;"><i class="fas fa-exchange-alt"></i> Movimentações do Sistema</a></li>

                    <!-- Relatórios de Compras -->
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_mrp_compras" onclick="abrirTela('relatorioMrpCompras')"><i class="fas fa-chart-bar"></i> Relatório MRP e Compras</button></li>
                    <li><button class="menu-button highlight-btn" data-level="2" data-permission="relatorio_compras_gestao" onclick="abrirTela('relatorioComprasGestao')"><i class="fas fa-shopping-cart"></i> Gestão de Compras</button></li>

                    <!-- Relatórios de Produção -->
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_op_sap" onclick="abrirTela('relatorioOpSap')"><i class="fas fa-file-excel"></i> Relatório de OP (SAP)</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_ordens" onclick="abrirTela('relatorioOrdens')"><i class="fas fa-list"></i> Relatório Ordens x Setor</button></li>

                    <!-- Relatórios de Estrutura -->
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_estrutura" onclick="abrirTela('relatorioEstrutura')"><i class="fas fa-sitemap"></i> Relatório de Estrutura</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="relatorio_onde_usado" onclick="abrirTela('relatorioOndeUsado')"><i class="fas fa-search"></i> Onde é Usado</button></li>
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_substituir" onclick="abrirTela('relatorioSubstituir')"><i class="fas fa-exchange-alt"></i> Substituir Material</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="relatorio_copia_estrutura" onclick="abrirTela('relatorioCopiaEstrutura')"><i class="fas fa-copy"></i> Cópia de Estrutura</button></li>
                    <li><button class="menu-button" data-level="1" data-permission="exportar_estrutura" onclick="abrirTela('exportar_estrutura')"><i class="fas fa-file-export"></i> Exportar todas estruturas</button></li>

                    <!-- Relatórios de Qualidade -->
                    <li><button class="menu-button" data-level="3" data-permission="relatorio_inspecoes" onclick="abrirTela('relatorioInspecoes')"><i class="fas fa-clipboard-list"></i> Relatório de Inspeções</button></li>

                    <!-- Relatórios de Análise -->
                    <li><button class="menu-button" data-level="2" data-permission="relatorio_produtos_sem_pai" onclick="abrirTela('relatorioProdutosSemPai')"><i class="fas fa-exclamation-triangle"></i> Produtos Sem Pai</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Ferramentas <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <!-- IA Sistema Autônomo -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="ia_sistema_autonomo.html" style="background: linear-gradient(45deg, #6c5ce7, #a29bfe); color: white; border: none; animation: pulse 2s infinite;"><i class="fas fa-robot"></i> 🤖 IA Sistema Autônomo</a></li>

                    <!-- IA Monitor de Compras -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="ia_monitor_compras.html" style="background: linear-gradient(45deg, #9b59b6, #e67e22); color: white; border: none; animation: pulse 2s infinite;"><i class="fas fa-brain"></i> 🧠 IA Monitor de Compras</a></li>

                    <!-- OCR Sistema Inteligente -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="ocr_sistema_inteligente.html" style="background: linear-gradient(45deg, #3498db, #2ecc71); color: white; border: none;"><i class="fas fa-camera"></i> 📸 OCR Sistema Inteligente</a></li>

                    <!-- Editores HTML -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="altera_opsemestoque.html" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none;"><i class="fas fa-edit"></i> 📝 Editor de OPs sem Estoque</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="recalcular_explosao_op.html" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none;"><i class="fas fa-calculator"></i> 🔄 Recalcular Explosão de OP</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="estorno_movimento.html" style="background: linear-gradient(45deg, #9b59b6, #8e44ad); color: white; border: none;"><i class="fas fa-undo"></i> ↩️ Estorno de Movimento</a></li>

                    <!-- Editores de Compras -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="editor_solicitacoes.html" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none;"><i class="fas fa-file-alt"></i> 📋 Editor de Solicitações</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="editor_cotacoes.html" style="background: linear-gradient(45deg, #1abc9c, #16a085); color: white; border: none;"><i class="fas fa-clipboard-list"></i> 💰 Editor de Cotações</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="editor_pedidos.html" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none;"><i class="fas fa-shopping-cart"></i> 🛒 Editor de Pedidos</a></li>

                    <!-- Ferramentas de Monitoramento -->
                    <li><a class="menu-button" data-level="9" href="dashboard_fluxo_materiais.html"><i class="fas fa-chart-line"></i> Dashboard Fluxo de Materiais</a></li>
                    <li><a class="menu-button" data-level="9" href="acompanhamento_materiais_limpo.html"><i class="fas fa-eye"></i> Acompanhamento de Materiais</a></li>
                    <li><a class="menu-button" data-level="9" href="monitor_qualidade.html"><i class="fas fa-shield-alt"></i> Monitor da Qualidade</a></li>

                    <!-- Ferramentas de Gestão de Empenhos -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="controle_baixa_necessidades.html"><i class="fas fa-chart-pie"></i> Gestão de Empenhos</a></li>

                    <!-- Ferramentas de Auditoria -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="ferramenta_alterar_status.html"><i class="fas fa-tools"></i> Auditoria e Alteração de Status</a></li>
                    <li><a class="menu-button" data-level="9" href="auditoria_movimentacoes.html"><i class="fas fa-search"></i> Auditoria de Movimentações</a></li>
                    <li><a class="menu-button" data-level="9" href="relatorio_inconsistencias.html"><i class="fas fa-exclamation-triangle"></i> Relatório de Inconsistências</a></li>
                    <li><a class="menu-button" data-level="9" href="listar_problemas_saldos.html"><i class="fas fa-list-alt"></i> Lista de Problemas de Saldo</a></li>

                    <!-- Ferramentas de Correção -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="corrigir_numeracao.html"><i class="fas fa-hashtag"></i> Correção de Numeração</a></li>
                    <li><a class="menu-button" data-level="9" href="corrigir_duplicacao_item.html"><i class="fas fa-tools"></i> Correção de Duplicação de Itens</a></li>
                    <li><a class="menu-button" data-level="9" href="corrigir_saldo_estoque.html"><i class="fas fa-edit"></i> Correção de Saldo de Estoque</a></li>
                    <li><a class="menu-button" data-level="9" href="correcao_dados_funcional.html"><i class="fas fa-wrench"></i> Correção de Dados</a></li>

                    <!-- Ferramentas de Gestão de Estoque -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="zerar_estoques_saldos_iniciais.html"><i class="fas fa-trash-restore"></i> 🗑️ Zerar Estoques e Saldos Iniciais</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="corrigir_saldo_sp_prod1.html" style="background: linear-gradient(45deg, #8e44ad, #9b59b6); color: white; border: none;"><i class="fas fa-cogs"></i> 🔧 Correção Saldo SP - PROD1</a></li>

                    <!-- Ferramentas de Qualidade -->
                    <li><a class="menu-button" data-level="9" href="aprovacao_qualidade_universal.html"><i class="fas fa-check-circle"></i> Aprovação da Qualidade</a></li>

                    <!-- Ferramentas de Diagnóstico -->
                    <li><a class="menu-button highlight-btn" data-level="9" href="diagnostico_solicitacoes_cotacoes.html"><i class="fas fa-search"></i> Diagnóstico: Solicitações vs Cotações</a></li>
                    <li><a class="menu-button" data-level="9" href="diagnostico_funcionando.html"><i class="fas fa-stethoscope"></i> Diagnóstico do Sistema</a></li>

                    <!-- Relatórios Especializados -->
                    <li><a class="menu-button" data-level="9" href="relatorio_estoque_simples.html"><i class="fas fa-chart-bar"></i> Relatório de Controle de Estoque</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="relatorio_movimentacoes.html" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; font-weight: bold;"><i class="fas fa-exchange-alt"></i> 📊 Relatório de Movimentações</a></li>
                    <li><a class="menu-button highlight-btn" data-level="9" href="relatorio_completo_colecoes.html" style="background: linear-gradient(45deg, #2c3e50, #34495e); color: white; border: none; font-weight: bold;"><i class="fas fa-database"></i> 📊 Relatório Completo de Coleções</a></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Status do Sistema <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button highlight-btn" data-level="9" onclick="verificarSaudeEmpenhos()"><i class="fas fa-heartbeat"></i> Saúde dos Empenhos</button></li>
                    <li><button class="menu-button" data-level="9" onclick="verificarIntegridadeDados()"><i class="fas fa-shield-alt"></i> Integridade dos Dados</button></li>
                    <li><button class="menu-button" data-level="9" onclick="verificarPerformanceSystem()"><i class="fas fa-tachometer-alt"></i> Performance do Sistema</button></li>
                    <li><button class="menu-button" data-level="9" onclick="verificarConectividade()"><i class="fas fa-wifi"></i> Status de Conectividade</button></li>
                </ul>
            </li>
            <li class="accordion-section">
                <button class="section-toggle">Configurações <i class="fas fa-chevron-down"></i></button>
                <ul class="accordion-content">
                    <li><button class="menu-button settings" data-level="7" data-permission="config_empresa" onclick="abrirTela('configEmpresa')"><i class="fas fa-building"></i> Dados da Empresa</button></li>
                    <li><button class="menu-button settings" data-level="7" data-permission="config_parametros" onclick="abrirTela('configParametros')"><i class="fas fa-cog"></i> Parâmetros do Sistema</button></li>
                    <li><a class="menu-button settings highlight-btn" data-level="9" href="ia_sistema_autonomo.html" style="background: linear-gradient(45deg, #6c5ce7, #a29bfe); color: white; border: none;"><i class="fas fa-robot"></i> 🤖 IA Sistema Autônomo</a></li>
                    <li><button class="menu-button settings" data-level="7" data-permission="config_backup" onclick="abrirTela('configBackup')"><i class="fas fa-database"></i> Backup e Restauração</button></li>
                    <li><a class="menu-button settings" data-level="7" data-permission="restore_sistema" href="restore_sistema.html"><i class="fas fa-upload"></i> Restore do Sistema</a></li>
                    <li><button class="menu-button settings" data-level="7" data-permission="config_logs" onclick="abrirTela('configLogs')"><i class="fas fa-file-alt"></i> Logs do Sistema</button></li>
                    <li><button class="menu-button settings" data-level="7" data-permission="config_permissoes" onclick="abrirTela('permissoesUsuario')"><i class="fas fa-user-shield"></i> Permissões de Usuário</button></li>
                    <li><button class="menu-button settings" data-level="7" data-permission="config_cfops" onclick="abrirTela('importarCfops')"><i class="fas fa-list-alt"></i> Importar CFOPs</button></li>
                    <li><a class="menu-button settings" data-level="7" data-permission="importacao_tabelas" href="importacao_tabelas.html"><i class="fas fa-table"></i> Importação de Tabelas</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <button type="button" class="menu-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Header Moderno -->
        <div class="main-header">
            <div class="header-content-main">
                <div class="welcome-section">
                    <h1 id="companyTitle">FYRON MRP - Sistema de Gestão Empresarial</h1>
                    <p>Bem-vindo ao sistema integrado de gestão empresarial</p>
                </div>
                <div class="system-info">
                    <div>Versão 2.2.0 - IA Monitor de Compras</div>
                    <div>Última atualização: Junho 2025</div>
                    <div class="status">
                        <i class="fas fa-check-circle"></i>
                        Sistema Online • IA Ativa
                    </div>
                </div>
            </div>
        </div>

        <!-- Conteúdo Central -->
        <div style="text-align: center;">
            <img id="centerImage" class="center-image" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='500' height='300' viewBox='0 0 500 300'%3E%3Crect width='500' height='300' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666' font-size='18'%3EImagem Central%3C/text%3E%3C/svg%3E" alt="Imagem Central">

            <div id="importacaoRapidaContainer"></div>

            <!-- Cards de Funcionalidades -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px; margin-top: 40px; max-width: 1400px; margin-left: auto; margin-right: auto; padding: 0 20px;">
                <!-- Card Apontamentos Modernizado -->
                <div style="background: linear-gradient(145deg, #ffffff, #f8f9fa); padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; border: 1px solid rgba(8, 84, 160, 0.1); transition: all 0.3s ease; cursor: pointer;" onclick="window.open('apontamentos_integrado_novo.html', '_blank')">
                    <div style="background: linear-gradient(135deg, #0854a0, #0a4d8c); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(8, 84, 160, 0.3);">
                        <i class="fas fa-clipboard-check" style="font-size: 24px;"></i>
                    </div>
                    <h3 style="color: var(--primary-color); margin-bottom: 10px; font-size: 18px;">🏭 Apontamentos</h3>
                    <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">Interface moderna para apontamentos de produção com dashboard inteligente e navegação otimizada</p>
                    <div style="background: #e8f4fd; color: #0854a0; padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin-top: 15px; display: inline-block;">NOVO MÓDULO</div>
                </div>

                <!-- Card Análise de Produção -->
                <div style="background: linear-gradient(145deg, #ffffff, #f8f9fa); padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; border: 1px solid rgba(40, 167, 69, 0.1); transition: all 0.3s ease; cursor: pointer;" onclick="window.open('analise_producao.html', '_blank')">
                    <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                        <i class="fas fa-chart-line" style="font-size: 24px;"></i>
                    </div>
                    <h3 style="color: var(--success-color); margin-bottom: 10px; font-size: 18px;">📊 Análise de Produção</h3>
                    <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">Análises inteligentes de viabilidade, diagnósticos de impossibilidade e sugestões automáticas de PCP</p>
                    <div style="background: #d4edda; color: #28a745; padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin-top: 15px; display: inline-block;">INTELIGÊNCIA ARTIFICIAL</div>
                </div>

                <!-- Card Gestão de Estoque -->
                <div style="background: linear-gradient(145deg, #ffffff, #f8f9fa); padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; border: 1px solid rgba(253, 126, 20, 0.1); transition: all 0.3s ease; cursor: pointer;" onclick="window.open('gestao_estoque.html', '_blank')">
                    <div style="background: linear-gradient(135deg, #fd7e14, #e55a00); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);">
                        <i class="fas fa-boxes" style="font-size: 24px;"></i>
                    </div>
                    <h3 style="color: #fd7e14; margin-bottom: 10px; font-size: 18px;">📦 Gestão de Estoque</h3>
                    <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">Decodificador de produtos, correção de estoques e ferramentas avançadas de gestão de materiais</p>
                    <div style="background: #fff3cd; color: #fd7e14; padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin-top: 15px; display: inline-block;">DECODIFICAÇÃO AUTOMÁTICA</div>
                </div>

                <!-- Card Gestão de Compras -->
                <div style="background: linear-gradient(145deg, #ffffff, #f8f9fa); padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; border: 1px solid rgba(23, 162, 184, 0.1); transition: all 0.3s ease; cursor: pointer;" onclick="window.open('gestao_compras_integrada.html', '_blank')">
                    <div style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);">
                        <i class="fas fa-shopping-cart" style="font-size: 24px;"></i>
                    </div>
                    <h3 style="color: #17a2b8; margin-bottom: 10px; font-size: 18px;">🛒 Gestão de Compras</h3>
                    <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">Workflow completo de solicitações, cotações e pedidos com interface unificada e moderna</p>
                    <div style="background: #d1ecf1; color: #17a2b8; padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin-top: 15px; display: inline-block;">WORKFLOW OTIMIZADO</div>
                </div>

                <!-- Card IA Monitor de Compras -->
                <div style="background: linear-gradient(145deg, #ffffff, #f8f9fa); padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; border: 1px solid rgba(155, 89, 182, 0.1); transition: all 0.3s ease; cursor: pointer; animation: pulse 3s infinite;" onclick="window.open('ia_monitor_compras.html', '_blank')">
                    <div style="background: linear-gradient(135deg, #9b59b6, #e67e22); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);">
                        <i class="fas fa-robot" style="font-size: 24px;"></i>
                    </div>
                    <h3 style="color: #9b59b6; margin-bottom: 10px; font-size: 18px;">🤖 IA Monitor de Compras</h3>
                    <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">Sistema inteligente que monitora todo o processo de compras, detecta anomalias e previne erros automaticamente</p>
                    <div style="background: linear-gradient(135deg, #9b59b6, #e67e22); color: white; padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin-top: 15px; display: inline-block;">INTELIGÊNCIA ARTIFICIAL</div>
                </div>
            </div>

            <!-- CSS para hover dos cards e responsividade -->
            <style>
                div[onclick]:hover {
                    transform: translateY(-5px) !important;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
                }

                @media (max-width: 768px) {
                    div[style*="grid-template-columns"] {
                        grid-template-columns: 1fr !important;
                        gap: 20px !important;
                        padding: 0 10px !important;
                    }

                    div[onclick] {
                        padding: 20px !important;
                    }

                    div[onclick] h3 {
                        font-size: 16px !important;
                    }

                    div[onclick] p {
                        font-size: 13px !important;
                    }
                }

                @media (max-width: 480px) {
                    div[onclick] {
                        padding: 15px !important;
                    }

                    div[onclick] div[style*="width: 60px"] {
                        width: 50px !important;
                        height: 50px !important;
                    }

                    div[onclick] i {
                        font-size: 20px !important;
                    }
                }
            </style>

            <div class="version-info">
                FYRON MRP v2.2.0 - Sistema de Gestão Empresarial © 2025 - Todos os direitos reservados.
                <br><small>Novas funcionalidades: IA Monitor de Compras • Prevenção Inteligente de Erros • Análise Preditiva</small>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.4/xlsx.full.min.js"></script>
    <script>
        function exportToExcel() {
            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.table_to_sheet(document.querySelector(".main-content"));
            XLSX.utils.book_append_sheet(wb, ws, "");
            XLSX.writeFile(wb, "Sistema_MRP.xlsx");
        }
    </script>
    <script type="module">
        import { db, storage } from './firebase-config.js';
        import { doc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = null;
        let userPermissions = [];

        window.onload = async function() {
            document.body.classList.add('loading');
            const userSession = localStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = 'login.html';
                return;
            }

            currentUser = JSON.parse(userSession);
            document.getElementById('currentUserName').textContent = currentUser.nome;
            document.getElementById('currentUserLevel').textContent = getNivelDescricao(currentUser.nivel);

            if (currentUser.nivel < 9) {
                await loadUserPermissions();
            }

            setupMenuAccess();
            await loadCompanyData();
            document.body.classList.remove('loading');
        };

        async function loadUserPermissions() {
            try {
                const permissionsDoc = await getDoc(doc(db, "permissoes", currentUser.id));
                if (permissionsDoc.exists()) {
                    userPermissions = permissionsDoc.data().permissoes || [];
                }
            } catch (error) {
                console.error("Erro ao carregar permissões:", error);
            }
        }

        async function loadCompanyData() {
            try {
                const [empresaDoc, parametrosDoc] = await Promise.all([
                    getDoc(doc(db, "empresa", "config")),
                    getDoc(doc(db, "parametros", "sistema"))
                ]);

                // Configurar visibilidade dos menus baseado nos parâmetros
                if (parametrosDoc.exists()) {
                    const params = parametrosDoc.data();

                    // ✅ CONTROLE DA ABA QUALIDADE - SÓ EXIBE SE MÓDULO ATIVO
                    const menuQualidade = document.getElementById('menuQualidade');
                    const qualidadeSection = menuQualidade.closest('.accordion-section');

                    if (params.moduloQualidadeAtivo) {
                        // ✅ Módulo ativo - exibir aba qualidade
                        qualidadeSection.style.display = 'block';

                        // 🔍 CONFIGURAR ITENS DA FASE 1 (PQ001-PQ007)
                        document.getElementById('btnInspecaoRecebimento').style.display = params.inspecaoRecebimento ? 'block' : 'none';
                        document.getElementById('btnInspecaoProcesso').style.display = params.inspecaoProcesso ? 'block' : 'none';
                        document.getElementById('btnLiberacaoQualidade').style.display = params.liberacaoQualidade ? 'block' : 'none';
                        document.getElementById('btnArmazemQualidade').style.display = params.armazemQualidade ? 'block' : 'none';
                        document.getElementById('btnHomologacaoFornecedores').style.display = params.homologacaoFornecedores ? 'block' : 'none';
                        document.getElementById('btnMetricasFornecedores').style.display = params.metricasFornecedores ? 'block' : 'none';
                        document.getElementById('btnReprovasDevolucoes').style.display = params.reprovasDevolucoes ? 'block' : 'none';

                        // 📊 LOG DOS PROCESSOS ATIVOS
                        const processosAtivos = [];
                        if (params.inspecaoRecebimento) processosAtivos.push('PQ001');
                        if (params.inspecaoProcesso) processosAtivos.push('PQ002');
                        if (params.liberacaoQualidade) processosAtivos.push('PQ003');
                        if (params.armazemQualidade) processosAtivos.push('PQ004');
                        if (params.homologacaoFornecedores) processosAtivos.push('PQ005');
                        if (params.metricasFornecedores) processosAtivos.push('PQ006');
                        if (params.reprovasDevolucoes) processosAtivos.push('PQ007');

                        console.log('✅ Módulo de Qualidade ATIVO - Processos habilitados:', processosAtivos.join(', '));
                    } else {
                        // ❌ Módulo inativo - esconder aba qualidade completamente
                        qualidadeSection.style.display = 'none';
                        console.log('❌ Módulo de Qualidade INATIVO - Aba oculta');
                    }

                    if (params.importacaoRapidaAtiva) {
                        // Carregar o conteúdo do body da importacao_rapida.html
                        fetch('importacao_rapida.html')
                            .then(resp => resp.text())
                            .then(html => {
                                const bodyContent = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
                                document.getElementById('importacaoRapidaContainer').innerHTML = bodyContent ? bodyContent[1] : html;
                            });
                    }
                }

                const docRef = doc(db, "empresa", "config");
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();
                    // Set default logo if logoUrl is not available
                    document.getElementById('companyLogo').src = data.logoUrl || "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666' font-size='14'%3ELogo%3C/text%3E%3C/svg%3E";

                    // Set default company name if nomeFantasia is not available
                    document.getElementById('companyTitle').textContent = data.nomeFantasia || "Sistema MRP";

                    // Set default center image if centerImageUrl is not available
                    document.getElementById('centerImage').src = data.centerImageUrl || "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='500' height='300' viewBox='0 0 500 300'%3E%3Crect width='500' height='300' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666' font-size='18'%3EImagem Central%3C/text%3E%3C/svg%3E";
                } else {
                    console.warn("Dados da empresa não encontrados. Usando valores padrão.");
                }
            } catch (error) {
                console.error("Erro ao carregar dados da empresa:", error);
                // Set default values in case of error
                document.getElementById('companyLogo').src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666' font-size='14'%3ELogo%3C/text%3E%3C/svg%3E";
                document.getElementById('companyTitle').textContent = "Sistema MRP";
                document.getElementById('centerImage').src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='500' height='300' viewBox='0 0 500 300'%3E%3Crect width='500' height='300' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23666' font-size='18'%3EImagem Central%3C/text%3E%3C/svg%3E";
            }
        }

        function getNivelDescricao(nivel) {
            switch (parseInt(nivel)) {
                case 1: return "Básico";
                case 2: return "Intermediário";
                case 3: return "Avançado";
                case 7: return "Administrador";
                case 9: return "Superusuário";
                default: return nivel;
            }
        }

        function setupMenuAccess() {
            if (currentUser.nivel === 9) return;
            const buttons = document.querySelectorAll('.menu-button');
            buttons.forEach(button => {
                const requiredLevel = parseInt(button.getAttribute('data-level') || '7');
                const requiredPermission = button.getAttribute('data-permission');
                if (currentUser.nivel < requiredLevel || (requiredPermission && !userPermissions.includes(requiredPermission))) {
                    button.style.display = 'none';
                }
            });
        }

        window.logout = function() {
            localStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        };

        // Função auxiliar para obter parâmetros do sistema
        async function getParametros() {
            try {
                const parametrosDoc = await getDoc(doc(db, "parametros", "sistema"));
                if (parametrosDoc.exists()) {
                    return parametrosDoc.data();
                } else {
                    console.warn('Documento de parâmetros não encontrado, usando valores padrão');
                    return {
                        moduloQualidadeAtivo: false,
                        inspecaoRecebimento: false,
                        armazemQualidade: false
                    };
                }
            } catch (error) {
                console.error('Erro ao carregar parâmetros:', error);
                return {
                    moduloQualidadeAtivo: false,
                    inspecaoRecebimento: false,
                    armazemQualidade: false
                };
            }
        }

        window.abrirTela = async function(tela) {
            // Função centralizada para navegação entre telas do sistema
            // Organizada por módulos para facilitar manutenção

            // 🔍 VERIFICAR PARÂMETROS DE QUALIDADE PARA REDIRECIONAMENTO CONDICIONAL
            let useQualityVersion = false;
            try {
                const params = await getParametros();
                useQualityVersion = params.moduloQualidadeAtivo || false;
                console.log('🔍 Módulo de qualidade ativo:', useQualityVersion);
            } catch (error) {
                console.warn('⚠️ Erro ao verificar parâmetros de qualidade, usando versão padrão:', error);
            }

            switch (tela) {
                // === MÓDULO FINANCEIRO ===
                case 'contasPagar': window.location.href = 'contas_pagar.html'; break;
                case 'contasReceber': window.location.href = 'contas_receber.html'; break;
                case 'condicoesPagamento': window.location.href = 'cadastro_condicoes_pagamento.html'; break;
                case 'fluxoCaixa': window.location.href = 'fluxo_caixa.html'; break;
                case 'analiseCredito': window.location.href = 'analise_credito.html'; break;
                case 'faturamento': window.location.href = 'faturamento.html'; break;

                // === MÓDULO VENDAS ===
                case 'gestaoVendas': window.location.href = 'gestao_vendas_totvs.html'; break;
                case 'orcamentos': window.location.href = 'orcamentos.html'; break;
                case 'estruturaOrcamento': window.location.href = 'estrutura_orcamento.html'; break;
                case 'pedidosVenda': window.location.href = 'pedidos_venda.html'; break;
                case 'tabelaPrecos': window.location.href = 'tabela_precos.html'; break;
                case 'condicoesEspeciais': window.location.href = 'condicoes_especiais.html'; break;

                // === MÓDULO COMPRAS (COM LÓGICA DE QUALIDADE) ===
                case 'solicitacaoCompras':
                    if (useQualityVersion) {
                        console.log('🔍 Redirecionando para PCQ001 - Solicitação com Qualidade');
                        window.location.href = 'PCQ001-solicitacao-compras-qualidade.html';
                    } else {
                        console.log('📋 Redirecionando para versão padrão');
                        window.location.href = 'solicitacao_compras_melhorada.html';
                    }
                    break;
                case 'cotacoes':
                    if (useQualityVersion) {
                        console.log('🔍 Redirecionando para PCQ002 - Cotações com Qualidade');
                        window.location.href = 'PCQ002-cotacoes-qualidade.html';
                    } else {
                        console.log('📋 Redirecionando para cotações padrão');
                        window.location.href = 'cotacoes/index.html';
                    }
                    break;
                case 'pedidosCompra':
                    if (useQualityVersion) {
                        console.log('🔍 Redirecionando para PCQ003 - Pedidos com Qualidade');
                        window.location.href = 'PCQ003-pedidos-compra-qualidade.html';
                    } else {
                        window.location.href = 'pedidos_compra.html';
                    }
                    break;
                case 'recebimentoMateriais':
                    if (useQualityVersion) {
                        console.log('🔍 Redirecionando para PCQ004 - Recebimento com Qualidade');
                        window.location.href = 'PCQ004-recebimento-materiais-qualidade.html';
                    } else {
                        console.log('📦 Redirecionando para recebimento padrão');
                        window.location.href = 'recebimento_materiais_avancado.html';
                    }
                    break;
                case 'ordensProducao':
                    if (useQualityVersion) {
                        console.log('🔍 Redirecionando para PCQ005 - Ordens com Qualidade');
                        window.location.href = 'PCQ005-ordens-producao-qualidade.html';
                    } else {
                        console.log('🏭 Redirecionando para ordens padrão');
                        window.location.href = 'ordens_producao.html';
                    }
                    break;

                // Outros módulos de compras (sem versão qualidade ainda)
                case 'gestaoCompras': window.location.href = 'gestao_compras_integrada.html'; break;
                case 'relatorioNecessidadesCompras': window.location.href = 'controle_baixa_necessidades.html'; break;
                case 'relatorioNecessidadeMateriais': window.location.href = 'relatorio_necessidade_materiais.html'; break;

                // === MÓDULO ENGENHARIA ===
                case 'cadastroProduto': window.location.href = 'cadastro_produto.html'; break;
                case 'cadastroGrupo': window.location.href = 'cadastro_grupo.html'; break;
                case 'cadastroFamilia': window.location.href = 'cadastro_familia.html'; break;
                case 'cadastroRecursos': window.location.href = 'cadastro_recursos.html'; break;
                case 'cadastroArmazem': window.location.href = 'cadastro_armazem.html'; break;
                case 'estruturaProdutos': window.location.href = 'cadastro_estrutura.html'; break;
                case 'estruturaNova': window.location.href = 'estrutura_nova.html'; break;
                // === MÓDULO CADASTROS ===
                case 'centralDocumentos': window.location.href = 'central_documentos.html'; break;
                case 'cadastroClientes': window.location.href = 'cadastro_clientes.html'; break;
                case 'cadastroOperacoes': window.location.href = 'cadastro_operacoes.html'; break;
                case 'cadastroFornecedores': window.location.href = 'cadastro_fornecedores.html'; break;
                case 'cadastroCentroCusto': window.location.href = 'cadastro_centro_custo.html'; break;
                case 'cadastroSetores': window.location.href = 'cadastro_setores.html'; break;
                case 'cadastroUsuarios': window.location.href = 'cadastro_usuarios.html'; break;
                case 'produtosFornecedores': window.location.href = 'produtos_fornecedores.html'; break;
                case 'saldosIniciais': window.location.href = 'saldos_iniciais.html'; break;

                // === MÓDULO PRODUÇÃO ===
                case 'apontamentos': window.location.href = 'apontamentos.html'; break;
                case 'estoques': window.location.href = 'estoques.html'; break;
                case 'alterarOp': window.location.href = 'altera_opsemestoque.html'; break;
                case 'zerarOrdens': window.location.href = 'zerar_ordens.html'; break;
                case 'estorno': window.location.href = 'estorno_movimento.html'; break;

                // === MÓDULO ALMOXARIFADO ===
                case 'movimentacaoArmazem': window.location.href = 'movimentacao_armazem_novo.html'; break;
                case 'ajusteEstoque': window.location.href = 'ajuste_estoque.html'; break;
                // === MÓDULO QUALIDADE - FASE 1 (PQ001-PQ007) ===
                case 'inspecaoRecebimento': window.location.href = 'inspecao_recebimento.html'; break;
                case 'inspecaoProcesso': window.location.href = 'PQ002-inspecao-processo.html'; break;
                case 'liberacaoQualidade': window.location.href = 'PQ003-liberacao-qualidade.html'; break;
                case 'armazemQualidade': window.location.href = 'PQ004-armazem-qualidade.html'; break;
                case 'homologacaoFornecedores': window.location.href = 'PQ005-homologacao-fornecedores.html'; break;
                case 'metricasFornecedores': window.location.href = 'PQ006-metricas-fornecedores.html'; break;
                case 'reprovasDevolucoes': window.location.href = 'PQ007-reprovas-devolucoes.html'; break;

                // === MÓDULO QUALIDADE - CONFIGURAÇÕES E GESTÃO ===
                case 'especificacoesProdutos': window.location.href = 'especificacoes_produtos.html'; break;
                case 'qualidadeGeral': window.location.href = 'qualidade.html'; break;
                case 'controleQualidade': window.location.href = 'controle_qualidade.html'; break;
                case 'inspecaoQualidade': window.location.href = 'inspecao_qualidade.html'; break;

                // === MÓDULO MANUTENÇÃO ===
                case 'ordensManutencao': window.location.href = 'ordens_manutencao.html'; break;
                case 'controleOrdensAbertas': window.location.href = 'controle_ordens_abertas.html'; break;
                case 'funcionariosManutencao': window.location.href = 'funcionarios_manutencao.html'; break;
                case 'cadastroFuncionariosManutencao': window.location.href = 'cadastro_funcionarios_manutencao.html'; break;
                case 'apontamentoOrdens': window.location.href = 'apontamento_ordens_manutencao.html'; break;
                case 'planejamentoManutencao': window.location.href = 'planejamento_manutencao.html'; break;
                case 'planoManutencao': window.location.href = 'plano_manutencao.html'; break;
                case 'recursos': window.location.href = 'cadastro_recursos.html'; break;


                // === MÓDULO RELATÓRIOS ===
                case 'relatorioMrpCompras': window.location.href = 'relatorio_mrp_compras.html'; break;
                case 'relatorioFinanceiro': window.location.href = 'relatorio_financeiro.html'; break;
                case 'relatorioInventario': window.location.href = 'relatorio_inventario.html'; break;
                case 'relatorioControleEstoque': window.location.href = 'relatorio_estoque_simples.html'; break;
                case 'relatorioComprasGestao': window.location.href = 'relatorio_mrp_compras.html'; break;
                case 'relatorioEstrutura': window.location.href = 'relatorio_estrutura.html'; break;
                case 'relatorioOrdens': window.location.href = 'relatorio_ordens_setor.html'; break;
                case 'relatorioOpSap': window.location.href = 'relatorio_op_sap.html'; break;
                case 'relatorioOndeUsado': window.location.href = 'relatorio_onde_usado.html'; break;
                case 'relatorioSubstituir': window.location.href = 'altera_componentes.html'; break;
                case 'relatorioCopiaEstrutura': window.location.href = 'relatorio_copia_estrutura.html'; break;
                case 'exportar_estrutura': window.location.href = 'exportar-estrutura.html'; break;
                case 'relatorioCusto': window.location.href = 'gestao_custos.html'; break;
                case 'relatorioInspecoes': window.location.href = 'relatorio_inspecoes.html'; break;
                case 'relatorioProdutosSemPai': window.location.href = 'relatorio_produtos_sem_pai.html'; break;
                // === MÓDULO CONFIGURAÇÕES ===
                case 'configEmpresa': window.location.href = 'config_empresa.html'; break;
                case 'configParametros': window.location.href = 'config_parametros.html'; break;
                case 'configBackup': window.location.href = 'backup_sistema.html'; break;
                case 'configLogs': window.location.href = 'config_logs.html'; break;
                case 'permissoesUsuario': window.location.href = 'permissoes_usuario.html'; break;
                case 'importarCfops': window.location.href = 'importar_cfops.html'; break;
                default: alert('Tela não encontrada!'); break;
            }
        };

        window.toggleSidebar = function() {
            document.querySelector('.sidebar').classList.toggle('active');
        };

        // ===== FUNÇÕES DE STATUS DO SISTEMA =====

        window.verificarSaudeEmpenhos = function() {
            // Redirecionar para controle de necessidades com foco em empenhos
            window.location.href = 'controle_baixa_necessidades.html?action=checkEmpenhos';
        };

        window.verificarIntegridadeDados = function() {
            // Redirecionar para ferramenta de diagnóstico
            window.location.href = 'diagnostico_funcionando.html';
        };

        window.verificarPerformanceSystem = function() {
            // Mostrar métricas básicas de performance
            const startTime = performance.now();

            // Simular verificação de performance
            setTimeout(() => {
                const endTime = performance.now();
                const loadTime = (endTime - startTime).toFixed(2);

                const metrics = `MÉTRICAS DE PERFORMANCE DO SISTEMA\n\n` +
                    `⚡ Tempo de resposta: ${loadTime}ms\n` +
                    `📊 Memória utilizada: ${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(2) || 'N/A'} MB\n` +
                    `🔄 Conexões ativas: ${navigator.onLine ? 'Online' : 'Offline'}\n` +
                    `📱 Dispositivo: ${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}\n` +
                    `🌐 Navegador: ${navigator.userAgent.split(' ')[0]}\n\n` +
                    `✅ Sistema funcionando normalmente!`;

                alert(metrics);
            }, 100);
        };

        window.verificarConectividade = function() {
            const status = navigator.onLine ? 'ONLINE' : 'OFFLINE';
            const color = navigator.onLine ? '🟢' : '🔴';

            const connectivity = `${color} STATUS DE CONECTIVIDADE\n\n` +
                `📡 Status: ${status}\n` +
                `🌐 Protocolo: ${location.protocol}\n` +
                `🏠 Host: ${location.host}\n` +
                `📍 URL: ${location.pathname}\n\n` +
                `${navigator.onLine ? '✅ Conectado ao Firebase' : '⚠️ Modo offline - Verifique sua conexão'}`;

            alert(connectivity);
        };

        // Função para filtrar menu
        function filtrarMenu(termo) {
            const menuItems = document.querySelectorAll('.menu-button');
            const sections = document.querySelectorAll('.accordion-section');

            if (!termo.trim()) {
                // Mostrar todos os itens
                menuItems.forEach(item => {
                    item.style.display = 'flex';
                });
                sections.forEach(section => {
                    section.style.display = 'block';
                });
                return;
            }

            const termoLower = termo.toLowerCase();
            let hasVisibleItems = {};

            // Filtrar itens do menu
            menuItems.forEach(item => {
                const texto = item.textContent.toLowerCase();
                const isVisible = texto.includes(termoLower);
                item.style.display = isVisible ? 'flex' : 'none';

                // Marcar seção como tendo itens visíveis
                const section = item.closest('.accordion-section');
                if (section && isVisible) {
                    const sectionTitle = section.querySelector('.section-toggle').textContent;
                    hasVisibleItems[sectionTitle] = true;
                }
            });

            // Mostrar/ocultar seções baseado nos itens visíveis
            sections.forEach(section => {
                const sectionTitle = section.querySelector('.section-toggle').textContent;
                const hasVisible = hasVisibleItems[sectionTitle];
                section.style.display = hasVisible ? 'block' : 'none';

                // Expandir seções com resultados
                if (hasVisible) {
                    const content = section.querySelector('.accordion-content');
                    if (content) {
                        content.classList.add('active');
                    }
                }
            });
        }

        // Função para alternar sidebar no mobile
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        // Limpar busca ao clicar ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const searchInput = document.getElementById('searchMenu');
                if (searchInput) {
                    searchInput.value = '';
                    filtrarMenu('');
                }
            }
        });

        // Accordion menu script - VERSÃO CORRIGIDA
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Inicializando accordion menu...');

            const toggles = document.querySelectorAll('.section-toggle');
            console.log(`📋 Encontrados ${toggles.length} botões de seção`);

            toggles.forEach((btn, index) => {
                console.log(`🔘 Configurando botão ${index + 1}: ${btn.textContent.trim()}`);

                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log(`🖱️ Clique na seção: ${btn.textContent.trim()}`);

                    const content = btn.nextElementSibling;
                    const isCurrentlyActive = content.classList.contains('active');

                    // Fecha todas as outras seções
                    document.querySelectorAll('.accordion-content').forEach(ul => {
                        if (ul !== content) {
                            ul.classList.remove('active');
                        }
                    });

                    // Alterna a seção clicada
                    if (isCurrentlyActive) {
                        content.classList.remove('active');
                    } else {
                        content.classList.add('active');
                    }

                    // Atualiza todos os ícones
                    toggles.forEach(otherBtn => {
                        const icon = otherBtn.querySelector('i');
                        const otherContent = otherBtn.nextElementSibling;

                        if (otherContent.classList.contains('active')) {
                            icon.classList.remove('fa-chevron-down');
                            icon.classList.add('fa-chevron-up');
                        } else {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });

                    console.log(`✅ Seção ${content.classList.contains('active') ? 'expandida' : 'recolhida'}`);
                });
            });

            // Abre a primeira seção por padrão
            if (toggles.length > 0) {
                console.log('🎯 Abrindo primeira seção por padrão...');
                const firstContent = toggles[0].nextElementSibling;
                const firstIcon = toggles[0].querySelector('i');

                firstContent.classList.add('active');
                firstIcon.classList.remove('fa-chevron-down');
                firstIcon.classList.add('fa-chevron-up');

                console.log('✅ Primeira seção aberta');
            }

            console.log('🎉 Accordion menu inicializado com sucesso!');
        });

        // Função para selecionar logo
        function selectLogo() {
            document.getElementById('logoFileInput').click();
        }

        // Funções do logo já definidas no head do documento
    </script>
</body>
</html>