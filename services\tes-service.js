/**
 * SERVIÇO DE TES DINÂMICO
 * Integração entre o cadastro de TES e o sistema existente
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    doc, 
    getDoc,
    query,
    where,
    orderBy 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TESService {
    static tesCache = null;
    static lastUpdate = null;
    static CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

    /**
     * Carregar todos os TES ativos do banco de dados
     */
    static async loadTES(forceReload = false) {
        try {
            // Verificar cache
            if (!forceReload && this.tesCache && this.lastUpdate && 
                (Date.now() - this.lastUpdate) < this.CACHE_DURATION) {
                return this.tesCache;
            }

            console.log('🔄 Carregando TES do Firebase...');
            const tesSnapshot = await getDocs(
                query(
                    collection(db, "tes"), 
                    where("ativo", "==", true),
                    orderBy("codigo")
                )
            );

            this.tesCache = tesSnapshot.docs.map(doc => ({ 
                id: doc.id, 
                ...doc.data() 
            }));
            this.lastUpdate = Date.now();

            console.log(`✅ ${this.tesCache.length} TES carregados`);
            return this.tesCache;

        } catch (error) {
            console.error('❌ Erro ao carregar TES:', error);
            
            // Fallback para configuração estática se houver erro
            if (window.TESConfig) {
                console.log('🔄 Usando configuração TES estática como fallback');
                return this.convertStaticTESToFormat();
            }
            
            throw error;
        }
    }

    /**
     * Obter TES por categoria
     */
    static async getTESByCategory(categoria) {
        const allTES = await this.loadTES();
        return allTES.filter(tes => tes.categoria === categoria);
    }

    /**
     * Obter TES por tipo de movimento
     */
    static async getTESByType(tipo) {
        const allTES = await this.loadTES();
        return allTES.filter(tes => tes.tipo === tipo || tes.tipo === 'AMBOS');
    }

    /**
     * Obter configuração de um TES específico
     */
    static async getTESConfig(tesCode) {
        try {
            const allTES = await this.loadTES();
            const tes = allTES.find(t => t.codigo === tesCode);
            
            if (!tes) {
                console.warn(`⚠️ TES ${tesCode} não encontrado`);
                return null;
            }

            return {
                ...tes,
                validation: {
                    requiresNF: tes.exigeNotaFiscal,
                    requiresSupplier: tes.exigeFornecedor,
                    requiresCustomer: tes.exigeFornecedor, // Mesmo campo para cliente
                    requiresOP: tes.exigeOrdemProducao,
                    requiresJustification: tes.exigeJustificativa,
                    allowsNegativeStock: tes.permiteEstoqueNegativo
                },
                fiscal: {
                    cfop: tes.cfop,
                    icms: tes.icms || 0,
                    ipi: tes.ipi || 0,
                    pis: tes.pis || 0,
                    cofins: tes.cofins || 0
                }
            };

        } catch (error) {
            console.error(`❌ Erro ao obter configuração do TES ${tesCode}:`, error);
            return null;
        }
    }

    /**
     * Validar TES para movimentação
     */
    static async validateTES(tesCode, movementData) {
        const tesConfig = await this.getTESConfig(tesCode);
        if (!tesConfig) {
            return { valid: false, errors: ['TES não encontrada'] };
        }

        const errors = [];
        const validation = tesConfig.validation;

        // Validar nota fiscal
        if (validation.requiresNF && !movementData.numeroDocumento) {
            errors.push('Número da nota fiscal é obrigatório para esta TES');
        }

        // Validar fornecedor
        if (validation.requiresSupplier && !movementData.fornecedorId) {
            errors.push('Fornecedor é obrigatório para esta TES');
        }

        // Validar cliente
        if (validation.requiresCustomer && !movementData.clienteId) {
            errors.push('Cliente é obrigatório para esta TES');
        }

        // Validar ordem de produção
        if (validation.requiresOP && !movementData.ordemProducaoId) {
            errors.push('Ordem de produção é obrigatória para esta TES');
        }

        // Validar justificativa
        if (validation.requiresJustification && !movementData.observacoes) {
            errors.push('Justificativa é obrigatória para esta TES');
        }

        // Validar saldo negativo
        if (!validation.allowsNegativeStock && movementData.saldoFuturo < 0) {
            errors.push('Esta TES não permite saldo negativo');
        }

        return {
            valid: errors.length === 0,
            errors,
            config: tesConfig
        };
    }

    /**
     * Determinar tipo de movimentação baseado na TES
     */
    static async getMovementType(tesCode, documentType) {
        const tesConfig = await this.getTESConfig(tesCode);
        if (!tesConfig) return null;

        // Se a TES define o tipo explicitamente
        if (tesConfig.tipo !== 'AMBOS') {
            return tesConfig.tipo;
        }

        // Para TES que podem ser ambos, usar o tipo do documento
        const entradaTypes = ['COMPRA', 'PRODUCAO'];
        const saidaTypes = ['VENDA', 'CONSUMO'];

        if (entradaTypes.includes(documentType)) {
            return 'ENTRADA';
        } else if (saidaTypes.includes(documentType)) {
            return 'SAIDA';
        }

        return null;
    }

    /**
     * Calcular impostos baseado na TES
     */
    static async calculateTaxes(tesCode, baseValue) {
        const tesConfig = await this.getTESConfig(tesCode);
        if (!tesConfig || !tesConfig.fiscal) {
            return { total: 0, details: {} };
        }

        const fiscal = tesConfig.fiscal;
        
        // Validar se o CFOP existe e está ativo
        const cfopValid = await this.validateCFOP(fiscal.cfop);
        if (!cfopValid.valid) {
            throw new Error(`CFOP inválido: ${cfopValid.message}`);
        }

        const icms = (baseValue * fiscal.icms) / 100;
        const ipi = (baseValue * fiscal.ipi) / 100;
        const pis = (baseValue * fiscal.pis) / 100;
        const cofins = (baseValue * fiscal.cofins) / 100;

        return {
            total: icms + ipi + pis + cofins,
            details: {
                icms: { rate: fiscal.icms, value: icms },
                ipi: { rate: fiscal.ipi, value: ipi },
                pis: { rate: fiscal.pis, value: pis },
                cofins: { rate: fiscal.cofins, value: cofins },
                cfop: fiscal.cfop,
                cfopDescription: cfopValid.description
            }
        };
    }

    /**
     * Validar CFOP
     */
    static async validateCFOP(cfopCode) {
        try {
            if (!cfopCode) {
                return { valid: false, message: 'CFOP não informado' };
            }

            // Buscar CFOP na base de dados
            const cfopsSnapshot = await getDocs(
                query(
                    collection(db, "cfops"),
                    where("codigo", "==", cfopCode),
                    where("ativo", "==", true)
                )
            );

            if (cfopsSnapshot.empty) {
                return { valid: false, message: `CFOP ${cfopCode} não encontrado ou inativo` };
            }

            const cfopData = cfopsSnapshot.docs[0].data();
            return { 
                valid: true, 
                description: cfopData.descricao,
                tipo: cfopData.tipo
            };

        } catch (error) {
            console.error('Erro ao validar CFOP:', error);
            return { valid: false, message: 'Erro na validação do CFOP' };
        }
    }

    /**
     * Obter opções de TES para select/dropdown
     */
    static async getTESOptions(categoria = null, tipo = null) {
        try {
            let allTES = await this.loadTES();

            // Filtrar por categoria se especificada
            if (categoria) {
                allTES = allTES.filter(tes => tes.categoria === categoria);
            }

            // Filtrar por tipo se especificado
            if (tipo) {
                allTES = allTES.filter(tes => tes.tipo === tipo || tes.tipo === 'AMBOS');
            }

            return allTES.map(tes => ({
                value: tes.codigo,
                text: `${tes.codigo} - ${tes.descricao}`,
                tipo: tes.tipo,
                categoria: tes.categoria,
                atualizaEstoque: tes.atualizaEstoque
            }));

        } catch (error) {
            console.error('❌ Erro ao obter opções de TES:', error);
            return [];
        }
    }

    /**
     * Converter configuração estática para formato dinâmico (fallback)
     */
    static convertStaticTESToFormat() {
        if (!window.TESConfig || !window.TESConfig.tesOptions) {
            return [];
        }

        const converted = [];
        
        for (const [categoria, tesList] of Object.entries(window.TESConfig.tesOptions)) {
            for (const tes of tesList) {
                const validation = window.TESConfig.validations[tes.value] || {};
                const fiscal = window.TESConfig.fiscalConfig[tes.value] || {};

                converted.push({
                    codigo: tes.value,
                    categoria: categoria,
                    descricao: tes.text.replace(/^\d+\s*-\s*/, ''),
                    tipo: tes.tipo,
                    cfop: fiscal.cfop || '',
                    atualizaEstoque: tes.atualizaEstoque,
                    permiteEstoqueNegativo: validation.allowsNegativeStock || false,
                    exigeNotaFiscal: validation.requiresNF || false,
                    exigeFornecedor: validation.requiresSupplier || false,
                    exigeOrdemProducao: validation.requiresOP || false,
                    exigeJustificativa: validation.requiresJustification || false,
                    icms: fiscal.icms || 0,
                    ipi: fiscal.ipi || 0,
                    pis: fiscal.pis || 0,
                    cofins: fiscal.cofins || 0,
                    ativo: true
                });
            }
        }

        return converted;
    }

    /**
     * Limpar cache (útil após alterações)
     */
    static clearCache() {
        this.tesCache = null;
        this.lastUpdate = null;
        console.log('🗑️ Cache de TES limpo');
    }

    /**
     * Verificar se TES existe e está ativo
     */
    static async tesExists(tesCode) {
        const allTES = await this.loadTES();
        return allTES.some(tes => tes.codigo === tesCode);
    }

    /**
     * Obter estatísticas dos TES
     */
    static async getStatistics() {
        const allTES = await this.loadTES();
        
        return {
            total: allTES.length,
            ativos: allTES.filter(t => t.ativo).length,
            entrada: allTES.filter(t => t.tipo === 'ENTRADA').length,
            saida: allTES.filter(t => t.tipo === 'SAIDA').length,
            ambos: allTES.filter(t => t.tipo === 'AMBOS').length,
            porCategoria: {
                COMPRA: allTES.filter(t => t.categoria === 'COMPRA').length,
                VENDA: allTES.filter(t => t.categoria === 'VENDA').length,
                PRODUCAO: allTES.filter(t => t.categoria === 'PRODUCAO').length,
                CONSUMO: allTES.filter(t => t.categoria === 'CONSUMO').length,
                AJUSTE: allTES.filter(t => t.categoria === 'AJUSTE').length,
                TRANSFERENCIA: allTES.filter(t => t.categoria === 'TRANSFERENCIA').length
            }
        };
    }
}

// Exportar para uso global
if (typeof window !== 'undefined') {
    window.TESService = TESService;
}
