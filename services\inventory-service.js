import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    getDoc,
    getDocs,
    updateDoc,
    addDoc,
    query,
    where,
    Timestamp,
    writeBatch,
    runTransaction
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class InventoryService {
    static async checkStock(productId, quantity, warehouseId = null) {
        try {
            const stockQuery = warehouseId 
                ? query(collection(db, "estoques"), 
                    where("produtoId", "==", productId),
                    where("armazemId", "==", warehouseId))
                : query(collection(db, "estoques"), 
                    where("produtoId", "==", productId));
            
            const stockDocs = await getDocs(stockQuery);
            const totalStock = stockDocs.docs.reduce((sum, doc) => {
                const data = doc.data();
                // Corrigido: incluir saldoEmpenhado no cálculo
                const disponivel = Math.max(0,
                    (data.saldo || 0) -
                    (data.saldoReservado || 0) -
                    (data.saldoEmpenhado || 0)
                );
                return sum + disponivel;
            }, 0);

            return {
                available: totalStock >= quantity,
                currentStock: totalStock,
                needed: quantity,
                missing: Math.max(0, quantity - totalStock)
            };
        } catch (error) {
            console.error("Error checking stock:", error);
            throw error;
        }
    }

    static async reserveStock(reservaData) {
        return runTransaction(db, async (transaction) => {
            const { 
                produtoId, 
                quantidade, 
                armazemId, 
                documentoRef 
            } = reservaData;

            // Referência do estoque
            const estoqueRef = doc(collection(db, "estoques"), `${produtoId}_${armazemId}`);
            const estoqueDoc = await transaction.get(estoqueRef);

            if (!estoqueDoc.exists()) {
                throw new Error("Estoque não encontrado");
            }

            const estoqueData = estoqueDoc.data();
            const saldoDisponivel = estoqueData.saldo - (estoqueData.saldoReservado || 0);

            if (saldoDisponivel < quantidade) {
                throw new Error("Saldo insuficiente para reserva");
            }

            // Atualizar estoque com reserva
            transaction.update(estoqueRef, {
                saldoReservado: (estoqueData.saldoReservado || 0) + quantidade,
                ultimaMovimentacao: Timestamp.now()
            });

            // Registrar reserva
            const reservaRef = doc(collection(db, "reservas"));
            transaction.set(reservaRef, {
                produtoId,
                armazemId,
                quantidade,
                documentoRef,
                dataReserva: Timestamp.now()
            });

            return {
                saldoAnterior: saldoDisponivel,
                saldoReservado: (estoqueData.saldoReservado || 0) + quantidade
            };
        });
    }

    static async releaseStock(liberacaoData) {
        return runTransaction(db, async (transaction) => {
            const { 
                produtoId, 
                quantidade, 
                armazemId, 
                documentoRef 
            } = liberacaoData;

            // Referência do estoque
            const estoqueRef = doc(collection(db, "estoques"), `${produtoId}_${armazemId}`);
            const estoqueDoc = await transaction.get(estoqueRef);

            if (!estoqueDoc.exists()) {
                throw new Error("Estoque não encontrado");
            }

            const estoqueData = estoqueDoc.data();
            const saldoReservado = estoqueData.saldoReservado || 0;

            if (saldoReservado < quantidade) {
                throw new Error("Quantidade de reserva insuficiente");
            }

            // Atualizar estoque liberando reserva
            transaction.update(estoqueRef, {
                saldoReservado: saldoReservado - quantidade,
                ultimaMovimentacao: Timestamp.now()
            });

            // Registrar liberação
            const liberacaoRef = doc(collection(db, "liberacoes"));
            transaction.set(liberacaoRef, {
                produtoId,
                armazemId,
                quantidade,
                documentoRef,
                dataLiberacao: Timestamp.now()
            });

            return {
                saldoReservadoAnterior: saldoReservado,
                saldoReservadoPosterior: saldoReservado - quantidade
            };
        });
    }

    static async updateStock(productId, quantity, warehouseId, type, documentRef) {
        const batch = writeBatch(db);
        try {
            const stockRef = doc(collection(db, "estoques"), warehouseId);
            const stockDoc = await getDoc(stockRef);
            
            if (!stockDoc.exists()) {
                // Create new stock record if it doesn't exist
                batch.set(stockRef, {
                    produtoId: productId,
                    armazemId: warehouseId,
                    saldo: type === 'ENTRADA' ? quantity : -quantity,
                    saldoReservado: 0,
                    ultimaMovimentacao: Timestamp.now()
                });
            } else {
                const currentStock = stockDoc.data();
                batch.update(stockRef, {
                    saldo: type === 'ENTRADA' 
                        ? currentStock.saldo + quantity 
                        : currentStock.saldo - quantity,
                    ultimaMovimentacao: Timestamp.now()
                });
            }

            // Record stock movement
            const movementRef = doc(collection(db, "movimentacoesEstoque"));
            batch.set(movementRef, {
                produtoId: productId,
                armazemId: warehouseId,
                tipo: type,
                quantidade: quantity,
                documentoRef: documentRef,
                dataHora: Timestamp.now()
            });

            await batch.commit();
            return true;
        } catch (error) {
            console.error("Error updating stock:", error);
            throw error;
        }
    }

    static async getStockAlerts() {
        try {
            const [productsSnap, stockSnap] = await Promise.all([
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "estoques"))
            ]);

            const products = productsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            const stocks = stockSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            return products.map(product => {
                const productStock = stocks.filter(s => s.produtoId === product.id);
                const totalStock = productStock.reduce((sum, s) => sum + s.saldo, 0);
                const reservedStock = productStock.reduce((sum, s) => sum + (s.saldoReservado || 0), 0);
                const availableStock = totalStock - reservedStock;

                return {
                    product,
                    totalStock,
                    availableStock,
                    reservedStock,
                    minStock: product.estoqueMinimo || 0,
                    maxStock: product.estoqueMaximo || 0,
                    reorderPoint: product.pontoPedido || 0,
                    status: this.calculateStockStatus(availableStock, product)
                };
            }).filter(item => item.status !== 'OK');
        } catch (error) {
            console.error("Error getting stock alerts:", error);
            throw error;
        }
    }

    static calculateStockStatus(availableStock, product) {
        if (availableStock <= 0) return 'CRITICAL';
        if (availableStock <= product.estoqueMinimo) return 'LOW';
        if (availableStock <= product.pontoPedido) return 'REORDER';
        if (availableStock >= product.estoqueMaximo) return 'EXCESS';
        return 'OK';
    }

    static async validateStockMovement(movementData) {
        const errors = [];
        
        // Validar produto
        if (!movementData.produtoId) {
            errors.push('Produto não identificado');
        }

        // Validar quantidade
        if (movementData.quantidade <= 0) {
            errors.push('Quantidade inválida');
        }

        // Verificar disponibilidade de estoque para saídas
        if (movementData.tipo === 'SAIDA') {
            const stockCheck = await this.checkStock(
                movementData.produtoId, 
                movementData.quantidade, 
                movementData.armazemId
            );

            if (!stockCheck.available) {
                errors.push(`Saldo insuficiente. Disponível: ${stockCheck.currentStock}, Necessário: ${stockCheck.needed}`);
            }
        }

        return errors;
    }

    static async processStockMovement(movementData) {
        // Validar movimentação
        const validationErrors = await this.validateStockMovement(movementData);
        if (validationErrors.length > 0) {
            throw new Error(`Erro na movimentação: ${validationErrors.join(', ')}`);
        }

        // Transação atômica para garantir consistência
        return runTransaction(db, async (transaction) => {
            const { 
                produtoId, 
                quantidade, 
                tipo, 
                armazemId, 
                tipoDocumento = 'MANUAL',
                observacoes = ''
            } = movementData;

            // Referência do estoque
            const estoqueRef = doc(collection(db, "estoques"), `${produtoId}_${armazemId}`);
            const estoqueDoc = await transaction.get(estoqueRef);

            // Calcular novo saldo
            const saldoAtual = estoqueDoc.exists() ? estoqueDoc.data().saldo : 0;
            const novoSaldo = tipo === 'ENTRADA' 
                ? saldoAtual + quantidade 
                : saldoAtual - quantidade;

            // Atualizar estoque
            transaction.set(estoqueRef, {
                produtoId,
                armazemId,
                saldo: novoSaldo,
                ultimaMovimentacao: Timestamp.now()
            }, { merge: true });

            // Registrar movimentação
            const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
            transaction.set(movimentacaoRef, {
                produtoId,
                armazemId,
                tipo,
                quantidade,
                tipoDocumento,
                observacoes,
                dataHora: Timestamp.now(),
                saldoAnterior: saldoAtual,
                saldoPosterior: novoSaldo
            });

            return {
                saldoAnterior: saldoAtual,
                saldoPosterior: novoSaldo
            };
        });
    }

    static async auditStock(produtoId, armazemId) {
        const movimentacoesQuery = query(
            collection(db, "movimentacoesEstoque"),
            where("produtoId", "==", produtoId),
            where("armazemId", "==", armazemId)
        );

        const movimentacoesSnapshot = await getDocs(movimentacoesQuery);
        const movimentacoes = movimentacoesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        }));

        // Calcular saldo a partir das movimentações
        const saldoCalculado = movimentacoes.reduce((total, mov) => {
            return mov.tipo === 'ENTRADA' 
                ? total + mov.quantidade 
                : total - mov.quantidade;
        }, 0);

        // Buscar saldo atual registrado
        const estoqueRef = doc(collection(db, "estoques"), `${produtoId}_${armazemId}`);
        const estoqueDoc = await getDoc(estoqueRef);
        const saldoRegistrado = estoqueDoc.exists() ? estoqueDoc.data().saldo : 0;

        return {
            produtoId,
            armazemId,
            saldoCalculado,
            saldoRegistrado,
            diferencas: saldoCalculado !== saldoRegistrado,
            movimentacoes
        };
    }

    static async gerarRelatorioInconsistencias() {
        const estoqueSnapshot = await getDocs(collection(db, "estoques"));
        const inconsistencias = [];

        for (const estoqueDoc of estoqueSnapshot.docs) {
            const estoqueData = estoqueDoc.data();
            const auditoria = await this.auditStock(
                estoqueData.produtoId, 
                estoqueData.armazemId
            );

            if (auditoria.diferencas) {
                inconsistencias.push(auditoria);
            }
        }

        return inconsistencias;
    }

    static async updateReservedStock(produtoId, armazemId, quantidade) {
        return runTransaction(db, async (transaction) => {
            const estoqueRef = doc(collection(db, "estoques"), `${produtoId}_${armazemId}`);
            const estoqueDoc = await transaction.get(estoqueRef);

            if (!estoqueDoc.exists()) {
                throw new Error("Estoque não encontrado");
            }

            const estoqueData = estoqueDoc.data();
            const novoSaldoReservado = Math.max(0, (estoqueData.saldoReservado || 0) + quantidade);

            transaction.update(estoqueRef, {
                saldoReservado: novoSaldoReservado,
                ultimaMovimentacao: Timestamp.now()
            });

            return {
                saldoReservadoAnterior: estoqueData.saldoReservado || 0,
                saldoReservadoPosterior: novoSaldoReservado
            };
        });
    }
} 