
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔍 Verificação de Consistência - Estoques</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
    .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; }
    .header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
    .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; font-weight: bold; }
    .btn-primary { background: #3498db; color: white; }
    .btn-success { background: #27ae60; color: white; }
    .btn-warning { background: #f39c12; color: white; }
    .btn-danger { background: #e74c3c; color: white; }
    .result-box { background: white; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin: 10px 0; }
    .error { background: #ffebee; border-left: 4px solid #f44336; }
    .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
    .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
    .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
    .log { background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
    .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
    .stat-card { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 8px; text-align: center; }
    .stat-number { font-size: 2em; font-weight: bold; }
    .table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .table th { background: #f2f2f2; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔍 Verificação de Consistência - Estoques</h1>
      <p>Ferramenta completa para diagnóstico e correção de problemas</p>
    </div>

    <!-- Controles -->
    <div class="section">
      <h3>🎛️ Controles de Verificação</h3>
      <button class="btn btn-primary" onclick="verificarCompleto()">🔍 Verificação Completa</button>
      <button class="btn btn-success" onclick="verificarRapido()">⚡ Verificação Rápida</button>
      <button class="btn btn-warning" onclick="corrigirProblemas()">🔧 Corrigir Problemas</button>
      <button class="btn btn-danger" onclick="executarCorrecaoCompleta()" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">🚨 Correção Automática</button>
      <button class="btn btn-danger" onclick="limparLog()">🗑️ Limpar Log</button>
    </div>

    <!-- Estatísticas -->
    <div class="stats-grid" id="statsGrid" style="display: none;">
      <div class="stat-card">
        <div class="stat-number" id="totalRegistros">-</div>
        <div>Total Registros</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
        <div class="stat-number" id="problemasEncontrados">-</div>
        <div>Problemas Encontrados</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954);">
        <div class="stat-number" id="registrosOK">-</div>
        <div>Registros OK</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
        <div class="stat-number" id="percentualSaude">-</div>
        <div>% Saúde</div>
      </div>
    </div>

    <!-- Resultados -->
    <div class="section">
      <h3>📊 Resultados da Verificação</h3>
      <div id="resultados"></div>
    </div>

    <!-- Log -->
    <div class="section">
      <h3>📝 Log de Execução</h3>
      <div class="log" id="logArea"></div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, doc, updateDoc, addDoc, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    import { CorrecaoInconsistenciasService } from './scripts/corrigir-inconsistencias-estoque.js';

    let verificacaoData = {};
    let problemas = [];

    function log(message, type = 'info') {
      const logArea = document.getElementById('logArea');
      const timestamp = new Date().toLocaleTimeString();
      const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
      logArea.textContent += `[${timestamp}] ${icon} ${message}\n`;
      logArea.scrollTop = logArea.scrollHeight;
      console.log(`${icon} ${message}`);
    }

    window.limparLog = function() {
      document.getElementById('logArea').textContent = '';
    };

    window.verificarRapido = async function() {
      log('⚡ Iniciando verificação rápida...', 'info');
      
      try {
        await carregarDados();
        await verificarConexoes();
        await verificarEstruturasBasicas();
        
        log('✅ Verificação rápida concluída', 'success');
        atualizarEstatisticas();
        
      } catch (error) {
        log(`❌ Erro na verificação rápida: ${error.message}`, 'error');
      }
    };

    window.verificarCompleto = async function() {
      log('🔍 Iniciando verificação completa...', 'info');
      problemas = [];
      
      try {
        // 1. Carregar dados
        log('📊 Carregando dados do Firebase...', 'info');
        await carregarDados();
        
        // 2. Verificações básicas
        log('🔗 Verificando conexões e estruturas...', 'info');
        await verificarConexoes();
        await verificarEstruturasBasicas();
        
        // 3. Verificações avançadas
        log('🔍 Executando verificações avançadas...', 'info');
        await verificarConsistenciaSaldos();
        await verificarDuplicatas();
        await verificarSaldosNegativos();
        await verificarValoresFinanceiros();
        await verificarReferenciasPerdidas();
        
        // 4. Gerar relatório
        log('📋 Gerando relatório...', 'info');
        gerarRelatorio();
        
        log('✅ Verificação completa finalizada', 'success');
        
      } catch (error) {
        log(`❌ Erro na verificação completa: ${error.message}`, 'error');
      }
    };

    async function carregarDados() {
      const [produtosSnap, estoquesSnap, armazensSnap, movimentacoesSnap] = await Promise.all([
        getDocs(collection(db, "produtos")),
        getDocs(collection(db, "estoques")),
        getDocs(collection(db, "armazens")),
        getDocs(query(collection(db, "movimentacoesEstoque"), orderBy("dataHora", "desc")))
      ]);

      verificacaoData = {
        produtos: produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
        estoques: estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
        armazens: armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })),
        movimentacoes: movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      };

      log(`Dados carregados: ${verificacaoData.produtos.length} produtos, ${verificacaoData.estoques.length} estoques, ${verificacaoData.armazens.length} armazéns`, 'success');
    }

    async function verificarConexoes() {
      let problemasConexao = 0;

      // Verificar produtos órfãos em estoque
      verificacaoData.estoques.forEach(estoque => {
        const produto = verificacaoData.produtos.find(p => p.id === estoque.produtoId);
        if (!produto) {
          problemas.push({
            tipo: 'PRODUTO_ORFAO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} referencia produto inexistente: ${estoque.produtoId}`,
            sugestao: 'Remover estoque órfão ou corrigir referência'
          });
          problemasConexao++;
        }
      });

      // Verificar armazéns órfãos em estoque
      verificacaoData.estoques.forEach(estoque => {
        const armazem = verificacaoData.armazens.find(a => a.id === estoque.armazemId);
        if (!armazem) {
          problemas.push({
            tipo: 'ARMAZEM_ORFAO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} referencia armazém inexistente: ${estoque.armazemId}`,
            sugestao: 'Corrigir referência do armazém'
          });
          problemasConexao++;
        }
      });

      if (problemasConexao === 0) {
        log('✅ Todas as conexões estão consistentes', 'success');
      } else {
        log(`⚠️ ${problemasConexao} problemas de conexão encontrados`, 'warning');
      }
    }

    async function verificarEstruturasBasicas() {
      let problemasEstrutura = 0;

      verificacaoData.estoques.forEach(estoque => {
        // Verificar campos obrigatórios
        if (!estoque.produtoId) {
          problemas.push({
            tipo: 'CAMPO_OBRIGATORIO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} sem produtoId`,
            sugestao: 'Definir produtoId válido'
          });
          problemasEstrutura++;
        }

        if (!estoque.armazemId) {
          problemas.push({
            tipo: 'CAMPO_OBRIGATORIO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} sem armazemId`,
            sugestao: 'Definir armazemId válido'
          });
          problemasEstrutura++;
        }

        // Verificar estrutura de campos (saldo vs quantidade)
        if (estoque.quantidade !== undefined && estoque.saldo === undefined) {
          problemas.push({
            tipo: 'ESTRUTURA_ANTIGA',
            gravidade: 'MEDIA',
            descricao: `Estoque ${estoque.id} usa campo 'quantidade' em vez de 'saldo'`,
            sugestao: 'Migrar para campo saldo padronizado'
          });
          problemasEstrutura++;
        }
      });

      if (problemasEstrutura === 0) {
        log('✅ Estruturas básicas estão corretas', 'success');
      } else {
        log(`⚠️ ${problemasEstrutura} problemas de estrutura encontrados`, 'warning');
      }
    }

    async function verificarConsistenciaSaldos() {
      let problemasConsistencia = 0;

      for (const estoque of verificacaoData.estoques) {
        // Calcular saldo baseado em movimentações
        const movimentacoesEstoque = verificacaoData.movimentacoes.filter(m => 
          m.produtoId === estoque.produtoId && m.armazemId === estoque.armazemId
        );

        const saldoCalculado = movimentacoesEstoque.reduce((total, mov) => {
          return mov.tipo === 'ENTRADA' ? total + (mov.quantidade || 0) : total - (mov.quantidade || 0);
        }, 0);

        const saldoRegistrado = estoque.saldo || estoque.quantidade || 0;
        const diferenca = Math.abs(saldoCalculado - saldoRegistrado);

        if (diferenca > 0.001) { // Tolerância de 0.001
          problemas.push({
            tipo: 'INCONSISTENCIA_SALDO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id}: Saldo registrado (${saldoRegistrado}) ≠ Saldo calculado (${saldoCalculado})`,
            sugestao: 'Recalcular saldo baseado em movimentações',
            dados: { saldoRegistrado, saldoCalculado, diferenca }
          });
          problemasConsistencia++;
        }
      }

      if (problemasConsistencia === 0) {
        log('✅ Saldos consistentes com movimentações', 'success');
      } else {
        log(`❌ ${problemasConsistencia} inconsistências de saldo encontradas`, 'error');
      }
    }

    async function verificarDuplicatas() {
      const chaves = new Map();
      let duplicatas = 0;

      verificacaoData.estoques.forEach(estoque => {
        const chave = `${estoque.produtoId}_${estoque.armazemId}`;
        
        if (chaves.has(chave)) {
          const registroExistente = chaves.get(chave);
          problemas.push({
            tipo: 'DUPLICATA',
            gravidade: 'ALTA',
            descricao: `Estoque duplicado: ${estoque.id} e ${registroExistente.id} para produto ${estoque.produtoId} no armazém ${estoque.armazemId}`,
            sugestao: 'Consolidar registros duplicados',
            dados: { 
              registro1: registroExistente, 
              registro2: { id: estoque.id, saldo: estoque.saldo || estoque.quantidade || 0 }
            }
          });
          duplicatas++;
        } else {
          chaves.set(chave, { id: estoque.id, saldo: estoque.saldo || estoque.quantidade || 0 });
        }
      });

      if (duplicatas === 0) {
        log('✅ Nenhuma duplicata encontrada', 'success');
      } else {
        log(`❌ ${duplicatas} duplicatas encontradas`, 'error');
      }
    }

    async function verificarSaldosNegativos() {
      let saldosNegativos = 0;

      verificacaoData.estoques.forEach(estoque => {
        const saldo = estoque.saldo || estoque.quantidade || 0;
        
        if (saldo < 0) {
          problemas.push({
            tipo: 'SALDO_NEGATIVO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} com saldo negativo: ${saldo}`,
            sugestao: 'Verificar movimentações e corrigir saldo',
            dados: { saldo, produtoId: estoque.produtoId, armazemId: estoque.armazemId }
          });
          saldosNegativos++;
        }
      });

      if (saldosNegativos === 0) {
        log('✅ Nenhum saldo negativo encontrado', 'success');
      } else {
        log(`❌ ${saldosNegativos} saldos negativos encontrados`, 'error');
      }
    }

    async function verificarValoresFinanceiros() {
      let problemasFinanceiros = 0;

      verificacaoData.estoques.forEach(estoque => {
        const saldo = estoque.saldo || estoque.quantidade || 0;
        const valorTotal = estoque.valorTotal || 0;
        const custoMedio = estoque.custoMedio || 0;

        // Verificar consistência custo médio vs valor total
        if (saldo > 0 && valorTotal > 0) {
          const custoCalculado = valorTotal / saldo;
          const diferenca = Math.abs(custoCalculado - custoMedio);
          
          if (diferenca > 0.01) { // Tolerância de 1 centavo
            problemas.push({
              tipo: 'CUSTO_MEDIO_INCONSISTENTE',
              gravidade: 'MEDIA',
              descricao: `Estoque ${estoque.id}: Custo médio inconsistente (${custoMedio} vs ${custoCalculado.toFixed(2)})`,
              sugestao: 'Recalcular custo médio',
              dados: { custoMedio, custoCalculado, diferenca }
            });
            problemasFinanceiros++;
          }
        }

        // Verificar valores negativos
        if (valorTotal < 0) {
          problemas.push({
            tipo: 'VALOR_NEGATIVO',
            gravidade: 'ALTA',
            descricao: `Estoque ${estoque.id} com valor total negativo: ${valorTotal}`,
            sugestao: 'Corrigir valor total'
          });
          problemasFinanceiros++;
        }
      });

      if (problemasFinanceiros === 0) {
        log('✅ Valores financeiros consistentes', 'success');
      } else {
        log(`⚠️ ${problemasFinanceiros} problemas financeiros encontrados`, 'warning');
      }
    }

    async function verificarReferenciasPerdidas() {
      let referenciasPerdidas = 0;

      // Verificar movimentações órfãs
      verificacaoData.movimentacoes.forEach(mov => {
        const temEstoque = verificacaoData.estoques.some(e => 
          e.produtoId === mov.produtoId && e.armazemId === mov.armazemId
        );

        if (!temEstoque && mov.tipo === 'ENTRADA') {
          problemas.push({
            tipo: 'MOVIMENTACAO_ORFA',
            gravidade: 'MEDIA',
            descricao: `Movimentação ${mov.id} sem estoque correspondente`,
            sugestao: 'Criar registro de estoque ou verificar movimentação',
            dados: { produtoId: mov.produtoId, armazemId: mov.armazemId }
          });
          referenciasPerdidas++;
        }
      });

      if (referenciasPerdidas === 0) {
        log('✅ Todas as referências estão corretas', 'success');
      } else {
        log(`⚠️ ${referenciasPerdidas} referências perdidas encontradas`, 'warning');
      }
    }

    function gerarRelatorio() {
      const resultados = document.getElementById('resultados');
      
      // Agrupar problemas por tipo
      const problemasPorTipo = {};
      problemas.forEach(problema => {
        if (!problemasPorTipo[problema.tipo]) {
          problemasPorTipo[problema.tipo] = [];
        }
        problemasPorTipo[problema.tipo].push(problema);
      });

      let html = '<h4>📋 Relatório de Problemas</h4>';

      if (problemas.length === 0) {
        html += '<div class="result-box success">✅ Nenhum problema encontrado! Sistema está íntegro.</div>';
      } else {
        Object.keys(problemasPorTipo).forEach(tipo => {
          const problemasDoTipo = problemasPorTipo[tipo];
          const gravidade = problemasDoTipo[0].gravidade;
          const cssClass = gravidade === 'ALTA' ? 'error' : gravidade === 'MEDIA' ? 'warning' : 'info';
          
          html += `
            <div class="result-box ${cssClass}">
              <h5>${tipo.replace(/_/g, ' ')} (${problemasDoTipo.length})</h5>
              <ul>
          `;
          
          problemasDoTipo.slice(0, 5).forEach(problema => {
            html += `<li>${problema.descricao}<br><small><strong>Sugestão:</strong> ${problema.sugestao}</small></li>`;
          });
          
          if (problemasDoTipo.length > 5) {
            html += `<li><em>... e mais ${problemasDoTipo.length - 5} problemas similares</em></li>`;
          }
          
          html += `</ul></div>`;
        });
      }

      resultados.innerHTML = html;
    }

    function atualizarEstatisticas() {
      const totalRegistros = verificacaoData.estoques?.length || 0;
      const problemasEncontrados = problemas.length;
      const registrosOK = totalRegistros - problemasEncontrados;
      const percentualSaude = totalRegistros > 0 ? ((registrosOK / totalRegistros) * 100).toFixed(1) : 0;

      document.getElementById('totalRegistros').textContent = totalRegistros;
      document.getElementById('problemasEncontrados').textContent = problemasEncontrados;
      document.getElementById('registrosOK').textContent = registrosOK;
      document.getElementById('percentualSaude').textContent = percentualSaude + '%';

      document.getElementById('statsGrid').style.display = 'grid';
    }

    window.corrigirProblemas = async function() {
      if (problemas.length === 0) {
        alert('Nenhum problema para corrigir. Execute a verificação primeiro.');
        return;
      }

      if (!confirm(`Foram encontrados ${problemas.length} problemas. Deseja tentar corrigi-los automaticamente?`)) {
        return;
      }

      log('🔧 Iniciando correção automática...', 'info');
      let corrigidos = 0;
      let erros = 0;

      for (const problema of problemas) {
        try {
          switch (problema.tipo) {
            case 'ESTRUTURA_ANTIGA':
              await corrigirEstrutura(problema);
              corrigidos++;
              break;
            case 'SALDO_NEGATIVO':
              await corrigirSaldoNegativo(problema);
              corrigidos++;
              break;
            case 'CUSTO_MEDIO_INCONSISTENTE':
              await corrigirCustoMedio(problema);
              corrigidos++;
              break;
            default:
              log(`⚠️ Tipo de problema não suportado para correção automática: ${problema.tipo}`, 'warning');
          }
        } catch (error) {
          log(`❌ Erro ao corrigir problema ${problema.tipo}: ${error.message}`, 'error');
          erros++;
        }
      }

      log(`✅ Correção concluída: ${corrigidos} corrigidos, ${erros} erros`, 'success');
    };

    async function corrigirEstrutura(problema) {
      // Implementar correção de estrutura
      log(`🔧 Corrigindo estrutura: ${problema.descricao}`, 'info');
    }

    async function corrigirSaldoNegativo(problema) {
      // Implementar correção de saldo negativo
      log(`🔧 Corrigindo saldo negativo: ${problema.descricao}`, 'info');
    }

    async function corrigirCustoMedio(problema) {
      // Implementar correção de custo médio
      log(`🔧 Corrigindo custo médio: ${problema.descricao}`, 'info');
    }

    // Auto-executar verificação rápida ao carregar
    setTimeout(() => {
      log('🚀 Sistema de verificação carregado. Execute uma verificação para começar.', 'info');
    }, 1000);
  </script>
</body>
</html>
