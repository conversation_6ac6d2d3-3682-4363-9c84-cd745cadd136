// 📋 SISTEMA DE MENU - FYRON MRP

class MenuManager {
    constructor() {
        this.searchInput = null;
        this.menuButtons = [];
        this.accordionSections = [];
        this.init();
    }

    init() {
        this.setupElements();
        this.setupSearch();
        this.setupAccordion();
        this.setupMobileMenu();
        this.setupPermissions();
        this.loadMenuState();
        this.setupAnalytics();
    }

    // 🔧 CONFIGURAR ELEMENTOS
    setupElements() {
        this.searchInput = document.getElementById('searchMenu');
        this.menuButtons = Array.from(document.querySelectorAll('.menu-button'));
        this.accordionSections = Array.from(document.querySelectorAll('.accordion-section'));
    }

    // 🔍 CONFIGURAR BUSCA
    setupSearch() {
        if (!this.searchInput) return;

        this.searchInput.addEventListener('input', (e) => {
            this.filterMenu(e.target.value);
        });

        // Atalho de teclado para busca (Ctrl+K)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput.focus();
                this.searchInput.select();
            }
        });
    }

    // 🔍 FILTRAR MENU
    filterMenu(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        
        if (!term) {
            this.showAllMenuItems();
            return;
        }

        let hasResults = false;

        this.accordionSections.forEach(section => {
            const sectionTitle = section.querySelector('.section-toggle').textContent.toLowerCase();
            const sectionButtons = section.querySelectorAll('.menu-button');
            let sectionHasResults = false;

            sectionButtons.forEach(button => {
                const buttonText = button.textContent.toLowerCase();
                const isMatch = buttonText.includes(term) || sectionTitle.includes(term);
                
                if (isMatch) {
                    button.style.display = 'flex';
                    button.classList.add('search-highlight');
                    sectionHasResults = true;
                    hasResults = true;
                } else {
                    button.style.display = 'none';
                    button.classList.remove('search-highlight');
                }
            });

            // Mostrar/ocultar seção baseado nos resultados
            if (sectionHasResults) {
                section.style.display = 'block';
                section.querySelector('.accordion-content').classList.add('active');
            } else {
                section.style.display = 'none';
            }
        });

        // Mostrar mensagem se não houver resultados
        this.toggleNoResultsMessage(!hasResults, term);
    }

    // 👁️ MOSTRAR TODOS OS ITENS
    showAllMenuItems() {
        this.menuButtons.forEach(button => {
            button.style.display = 'flex';
            button.classList.remove('search-highlight');
        });

        this.accordionSections.forEach(section => {
            section.style.display = 'block';
        });

        this.hideNoResultsMessage();
        this.loadMenuState(); // Restaurar estado do accordion
    }

    // ❌ TOGGLE MENSAGEM SEM RESULTADOS
    toggleNoResultsMessage(show, term) {
        let message = document.getElementById('noResultsMessage');
        
        if (show && !message) {
            message = document.createElement('div');
            message.id = 'noResultsMessage';
            message.className = 'no-results-message';
            message.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #6c757d;">
                    <i class="fas fa-search" style="font-size: 2em; margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>Nenhum módulo encontrado para "<strong>${term}</strong>"</p>
                    <small>Tente termos como "estoque", "produção", "vendas"</small>
                </div>
            `;
            document.querySelector('.nav-list').appendChild(message);
        } else if (!show && message) {
            message.remove();
        }
    }

    // ❌ OCULTAR MENSAGEM SEM RESULTADOS
    hideNoResultsMessage() {
        const message = document.getElementById('noResultsMessage');
        if (message) {
            message.remove();
        }
    }

    // 📂 CONFIGURAR ACCORDION
    setupAccordion() {
        this.accordionSections.forEach(section => {
            const toggle = section.querySelector('.section-toggle');
            const content = section.querySelector('.accordion-content');
            const icon = toggle.querySelector('i');

            toggle.addEventListener('click', () => {
                const isActive = content.classList.contains('active');
                
                if (isActive) {
                    content.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    content.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }

                this.saveMenuState();
            });
        });
    }

    // 📱 CONFIGURAR MENU MOBILE
    setupMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');

        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });

            // Fechar menu ao clicar fora (mobile)
            document.addEventListener('click', (e) => {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });
        }
    }

    // 🔐 CONFIGURAR PERMISSÕES
    setupPermissions() {
        this.menuButtons.forEach(button => {
            const permission = button.getAttribute('data-permission');
            const level = parseInt(button.getAttribute('data-level')) || 1;

            if (permission && !window.authManager?.hasPermission(permission)) {
                this.hideMenuItem(button, 'Sem permissão');
            } else if (!window.authManager?.hasMinLevel(level)) {
                this.hideMenuItem(button, 'Nível insuficiente');
            }
        });
    }

    // 🙈 OCULTAR ITEM DO MENU
    hideMenuItem(button, reason) {
        button.style.display = 'none';
        button.setAttribute('data-hidden-reason', reason);
        console.log(`🔒 Item oculto: ${button.textContent.trim()} - ${reason}`);
    }

    // 💾 SALVAR ESTADO DO MENU
    saveMenuState() {
        const openSections = Array.from(document.querySelectorAll('.accordion-content.active'))
            .map(content => {
                const section = content.closest('.accordion-section');
                return section.querySelector('.section-toggle').textContent.trim();
            });
        
        localStorage.setItem('menuState', JSON.stringify(openSections));
    }

    // 📂 CARREGAR ESTADO DO MENU
    loadMenuState() {
        try {
            const savedState = JSON.parse(localStorage.getItem('menuState') || '[]');
            
            this.accordionSections.forEach(section => {
                const toggle = section.querySelector('.section-toggle');
                const content = section.querySelector('.accordion-content');
                const icon = toggle.querySelector('i');
                const sectionName = toggle.textContent.trim();

                if (savedState.includes(sectionName)) {
                    content.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    content.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        } catch (error) {
            console.warn('⚠️ Erro ao carregar estado do menu:', error);
        }
    }

    // 📊 CONFIGURAR ANALYTICS
    setupAnalytics() {
        this.menuButtons.forEach(button => {
            button.addEventListener('click', () => {
                const moduleName = this.extractModuleName(button);
                this.trackMenuClick(moduleName);
            });
        });
    }

    // 📝 EXTRAIR NOME DO MÓDULO
    extractModuleName(button) {
        const text = button.textContent.trim();
        // Remover ícones e badges
        return text.replace(/^[🎯📊🔧🏭📋🔍⚙️💰📦🏢]+\s*/, '')
                  .replace(/\s*\(.*\)$/, '')
                  .replace(/\s*NOVO$/, '')
                  .trim();
    }

    // 📈 RASTREAR CLIQUE NO MENU
    trackMenuClick(moduleName) {
        const analytics = JSON.parse(localStorage.getItem('menuAnalytics') || '{}');
        const today = new Date().toISOString().split('T')[0];
        
        if (!analytics[today]) {
            analytics[today] = {};
        }
        
        analytics[today][moduleName] = (analytics[today][moduleName] || 0) + 1;
        
        localStorage.setItem('menuAnalytics', JSON.stringify(analytics));
        
        if (window.authManager) {
            window.authManager.trackModuleUsage(moduleName);
        }
        
        console.log(`📊 Menu clicado: ${moduleName}`);
    }

    // 📊 OBTER ESTATÍSTICAS DO MENU
    getMenuStats() {
        const analytics = JSON.parse(localStorage.getItem('menuAnalytics') || '{}');
        const stats = {};
        
        Object.values(analytics).forEach(dayData => {
            Object.entries(dayData).forEach(([module, count]) => {
                stats[module] = (stats[module] || 0) + count;
            });
        });
        
        return Object.entries(stats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
    }

    // 🔄 RESETAR BUSCA
    resetSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.showAllMenuItems();
        }
    }

    // 🎨 DESTACAR RESULTADOS DA BUSCA
    highlightSearchResults(term) {
        this.menuButtons.forEach(button => {
            const text = button.textContent.toLowerCase();
            if (text.includes(term.toLowerCase()) && term.length > 0) {
                button.style.background = 'rgba(255, 193, 7, 0.2)';
                button.style.border = '1px solid #ffc107';
            } else {
                button.style.background = '';
                button.style.border = '';
            }
        });
    }
}

// 🚀 INICIALIZAR SISTEMA DE MENU
document.addEventListener('DOMContentLoaded', () => {
    const menuManager = new MenuManager();
    window.menuManager = menuManager;
    
    // Função global para filtrar menu (compatibilidade)
    window.filtrarMenu = (term) => menuManager.filterMenu(term);
});

// 🔧 FUNÇÃO PARA ABRIR TELAS (COMPATIBILIDADE)
window.abrirTela = function(tela) {
    console.log(`🔗 Abrindo tela: ${tela}`);
    
    // Mapear nomes de tela para URLs
    const telaMap = {
        'cadastroProduto': 'cadastro_produto.html',
        'ordensProducao': 'ordens_producao.html',
        'apontamentos': 'apontamentos.html',
        'estoques': 'estoques.html',
        // Adicionar mais mapeamentos conforme necessário
    };
    
    const url = telaMap[tela] || `${tela}.html`;
    
    // Rastrear uso
    if (window.authManager) {
        window.authManager.trackModuleUsage(tela);
    }
    
    // Abrir tela
    window.location.href = url;
};
