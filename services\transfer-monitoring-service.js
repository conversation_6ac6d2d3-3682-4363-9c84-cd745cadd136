
// Serviço de monitoramento contínuo para transferências
import { db } from '../firebase-config.js';
import { 
  collection, 
  query, 
  where, 
  onSnapshot,
  getDocs,
  doc,
  updateDoc,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TransferMonitoringService {
  static listeners = new Map();
  static healthCheck = {
    lastCheck: null,
    status: 'unknown',
    issues: []
  };

  /**
   * Inicia monitoramento em tempo real de transferências
   */
  static startMonitoring() {
    console.log('🔍 Iniciando monitoramento de transferências...');
    
    // Monitor de transferências pendentes
    this.monitorPendingTransfers();
    
    // Monitor de integridade
    this.startIntegrityMonitor();
    
    // Health check periódico
    this.startHealthCheck();
  }

  /**
   * Monitora transferências que ficam pendentes por muito tempo
   */
  static monitorPendingTransfers() {
    const fifteenMinutesAgo = new Date();
    fifteenMinutesAgo.setMinutes(fifteenMinutesAgo.getMinutes() - 15);

    const pendingQuery = query(
      collection(db, "transferenciasArmazem"),
      where("status", "==", "PENDENTE"),
      where("dataHora", "<=", Timestamp.fromDate(fifteenMinutesAgo))
    );

    const unsubscribe = onSnapshot(pendingQuery, (snapshot) => {
      if (!snapshot.empty) {
        console.warn(`⚠️ ${snapshot.size} transferências pendentes há mais de 15 minutos`);
        
        snapshot.forEach(doc => {
          const transfer = doc.data();
          console.warn(`📋 Transferência pendente: ${transfer.numero} - ${transfer.produtoId}`);
          
          // Tentar resolver automaticamente ou notificar administradores
          this.handleStuckTransfer(doc.id, transfer);
        });
      }
    });

    this.listeners.set('pendingTransfers', unsubscribe);
  }

  /**
   * Monitor de integridade contínuo
   */
  static startIntegrityMonitor() {
    setInterval(async () => {
      try {
        await this.performIntegrityCheck();
      } catch (error) {
        console.error('❌ Erro no monitor de integridade:', error);
      }
    }, 5 * 60 * 1000); // A cada 5 minutos
  }

  /**
   * Health check do sistema
   */
  static startHealthCheck() {
    setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('❌ Erro no health check:', error);
      }
    }, 2 * 60 * 1000); // A cada 2 minutos
  }

  /**
   * Verifica integridade do sistema
   */
  static async performIntegrityCheck() {
    const issues = [];
    
    try {
      // Verificar transferências órfãs (sem movimentações)
      const transfersSnap = await getDocs(
        query(
          collection(db, "transferenciasArmazem"),
          where("status", "==", "CONCLUIDA")
        )
      );

      for (const transferDoc of transfersSnap.docs) {
        const transfer = transferDoc.data();
        
        const movimentacoesQuery = query(
          collection(db, "movimentacoesEstoque"),
          where("transferenciaId", "==", transferDoc.id)
        );
        const movSnap = await getDocs(movimentacoesQuery);
        
        if (movSnap.empty) {
          issues.push(`Transferência ${transfer.numero} sem movimentações`);
        }
      }

      if (issues.length > 0) {
        console.warn('🔍 Problemas de integridade detectados:', issues);
        
        // Log para auditoria
        await this.logIntegrityIssues(issues);
      }

    } catch (error) {
      console.error('❌ Erro na verificação de integridade:', error);
    }
  }

  /**
   * Health check geral do sistema
   */
  static async performHealthCheck() {
    const health = {
      timestamp: new Date(),
      database: 'unknown',
      transfers: 'unknown',
      issues: []
    };

    try {
      // Testar conectividade com banco
      const testQuery = await getDocs(query(collection(db, "transferenciasArmazem"), limit(1)));
      health.database = 'healthy';

      // Verificar transferências recentes
      const recentTransfers = await getDocs(
        query(
          collection(db, "transferenciasArmazem"),
          where("dataHora", ">=", Timestamp.fromDate(new Date(Date.now() - 24 * 60 * 60 * 1000)))
        )
      );

      health.transfers = recentTransfers.size > 0 ? 'active' : 'quiet';

      // Verificar espaço de armazenamento (aproximado)
      if (recentTransfers.size > 10000) {
        health.issues.push('Volume alto de transferências - considerar arquivamento');
      }

    } catch (error) {
      health.database = 'error';
      health.issues.push(`Erro de conectividade: ${error.message}`);
    }

    this.healthCheck = {
      lastCheck: health.timestamp,
      status: health.issues.length === 0 ? 'healthy' : 'warning',
      ...health
    };

    // Log apenas se houver problemas
    if (health.issues.length > 0) {
      console.warn('⚠️ Health check detectou problemas:', health.issues);
    }
  }

  /**
   * Tenta resolver transferência travada
   */
  static async handleStuckTransfer(transferId, transferData) {
    try {
      console.log(`🔧 Tentando resolver transferência travada: ${transferId}`);
      
      // Verificar se movimentações existem
      const movQuery = query(
        collection(db, "movimentacoesEstoque"),
        where("transferenciaId", "==", transferId)
      );
      const movSnap = await getDocs(movQuery);
      
      if (movSnap.empty) {
        // Transferência sem movimentações - marcar como erro
        await updateDoc(doc(db, "transferenciasArmazem", transferId), {
          status: 'ERRO_SISTEMA',
          observacoes: (transferData.observacoes || '') + ' [ERRO: Sem movimentações - Detectado pelo monitor]',
          ultimaAtualizacao: Timestamp.now()
        });
        
        console.log(`❌ Transferência ${transferId} marcada como ERRO_SISTEMA`);
      }
      
    } catch (error) {
      console.error(`❌ Erro ao resolver transferência travada ${transferId}:`, error);
    }
  }

  /**
   * Log de problemas de integridade
   */
  static async logIntegrityIssues(issues) {
    try {
      const logEntry = {
        tipo: 'INTEGRITY_CHECK',
        timestamp: Timestamp.now(),
        issues: issues,
        sistema: 'transfer_monitoring',
        severidade: 'WARNING'
      };

      await addDoc(collection(db, "logs_sistema"), logEntry);
      
    } catch (error) {
      console.error('❌ Erro ao registrar log de integridade:', error);
    }
  }

  /**
   * Para todos os listeners
   */
  static stopMonitoring() {
    this.listeners.forEach((unsubscribe, key) => {
      unsubscribe();
      console.log(`🛑 Monitor ${key} parado`);
    });
    this.listeners.clear();
  }

  /**
   * Relatório de status do sistema
   */
  static getSystemStatus() {
    return {
      monitoring: {
        active: this.listeners.size > 0,
        activeListeners: Array.from(this.listeners.keys())
      },
      health: this.healthCheck,
      timestamp: new Date().toISOString()
    };
  }
}

// Auto-start em desenvolvimento
if (typeof window !== 'undefined') {
  window.TransferMonitoringService = TransferMonitoringService;
  
  // Iniciar monitoramento quando a página carregar
  document.addEventListener('DOMContentLoaded', () => {
    TransferMonitoringService.startMonitoring();
  });
  
  // Parar monitoramento quando sair da página
  window.addEventListener('beforeunload', () => {
    TransferMonitoringService.stopMonitoring();
  });
}

export default TransferMonitoringService;
