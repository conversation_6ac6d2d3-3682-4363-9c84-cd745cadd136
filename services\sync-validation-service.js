
// ===================================================================
// SERVIÇO DE VALIDAÇÃO E SINCRONIZAÇÃO
// ===================================================================
// Garante que SC → CT → PC mantenham dados sincronizados
// ===================================================================

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    updateDoc,
    doc,
    query,
    where,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class SyncValidationService {
    
    /**
     * Verificação completa de sincronização
     */
    static async verificarSincronizacaoCompleta() {
        try {
            console.log('🔄 Iniciando verificação completa de sincronização...');
            
            const relatorio = {
                dataVerificacao: new Date(),
                problemas: [],
                estatisticas: {},
                recomendacoes: []
            };
            
            // Carregar todos os documentos
            const [solicitacoes, cotacoes, pedidos] = await Promise.all([
                this.carregarSolicitacoes(),
                this.carregarCotacoes(),
                this.carregarPedidos()
            ]);
            
            relatorio.estatisticas = {
                totalSolicitacoes: solicitacoes.length,
                totalCotacoes: cotacoes.length,
                totalPedidos: pedidos.length
            };
            
            // Verificações específicas
            const problemasReferencias = await this.verificarReferencias(solicitacoes, cotacoes, pedidos);
            const problemasStatus = await this.verificarStatusConsistente(solicitacoes, cotacoes, pedidos);
            const problemasNumeracao = await this.verificarNumeracao(solicitacoes, cotacoes, pedidos);
            const problemasValores = await this.verificarValores(solicitacoes, cotacoes, pedidos);
            
            relatorio.problemas.push(...problemasReferencias);
            relatorio.problemas.push(...problemasStatus);
            relatorio.problemas.push(...problemasNumeracao);
            relatorio.problemas.push(...problemasValores);
            
            // Gerar recomendações
            relatorio.recomendacoes = this.gerarRecomendacoes(relatorio.problemas);
            
            console.log(`✅ Verificação concluída: ${relatorio.problemas.length} problemas encontrados`);
            return relatorio;
            
        } catch (error) {
            console.error('❌ Erro na verificação de sincronização:', error);
            throw error;
        }
    }
    
    /**
     * Carregar documentos das coleções
     */
    static async carregarSolicitacoes() {
        const snap = await getDocs(collection(db, "solicitacoesCompra"));
        return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }
    
    static async carregarCotacoes() {
        const snap = await getDocs(collection(db, "cotacoes"));
        return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }
    
    static async carregarPedidos() {
        const snap = await getDocs(collection(db, "pedidosCompra"));
        return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }
    
    /**
     * Verificar referências entre documentos
     */
    static async verificarReferencias(solicitacoes, cotacoes, pedidos) {
        const problemas = [];
        
        // Verificar SC → CT
        cotacoes.forEach(cotacao => {
            if (cotacao.solicitacaoId) {
                const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
                if (!solicitacao) {
                    problemas.push({
                        tipo: 'REFERENCIA_PERDIDA',
                        documento: 'COTACAO',
                        id: cotacao.id,
                        numero: cotacao.numero,
                        problema: `Cotação ${cotacao.numero} referencia solicitação inexistente: ${cotacao.solicitacaoId}`,
                        gravidade: 'ALTA'
                    });
                } else {
                    // Verificar se a solicitação conhece a cotação
                    if (!solicitacao.cotacaoId || solicitacao.cotacaoId !== cotacao.id) {
                        problemas.push({
                            tipo: 'REFERENCIA_UNIDIRECIONAL',
                            documento: 'SOLICITACAO',
                            id: solicitacao.id,
                            numero: solicitacao.numero,
                            problema: `Solicitação ${solicitacao.numero} não referencia cotação ${cotacao.numero}`,
                            gravidade: 'MEDIA',
                            correcao: {
                                campo: 'cotacaoId',
                                valor: cotacao.id
                            }
                        });
                    }
                }
            }
        });
        
        // Verificar CT → PC
        pedidos.forEach(pedido => {
            if (pedido.cotacaoId) {
                const cotacao = cotacoes.find(c => c.id === pedido.cotacaoId);
                if (!cotacao) {
                    problemas.push({
                        tipo: 'REFERENCIA_PERDIDA',
                        documento: 'PEDIDO',
                        id: pedido.id,
                        numero: pedido.numero,
                        problema: `Pedido ${pedido.numero} referencia cotação inexistente: ${pedido.cotacaoId}`,
                        gravidade: 'ALTA'
                    });
                }
            }
            
            if (pedido.solicitacaoId) {
                const solicitacao = solicitacoes.find(s => s.id === pedido.solicitacaoId);
                if (!solicitacao) {
                    problemas.push({
                        tipo: 'REFERENCIA_PERDIDA',
                        documento: 'PEDIDO',
                        id: pedido.id,
                        numero: pedido.numero,
                        problema: `Pedido ${pedido.numero} referencia solicitação inexistente: ${pedido.solicitacaoId}`,
                        gravidade: 'ALTA'
                    });
                }
            }
        });
        
        return problemas;
    }
    
    /**
     * Verificar consistência de status
     */
    static async verificarStatusConsistente(solicitacoes, cotacoes, pedidos) {
        const problemas = [];
        
        solicitacoes.forEach(solicitacao => {
            const cotacoesDaSolicitacao = cotacoes.filter(c => c.solicitacaoId === solicitacao.id);
            const pedidosDaSolicitacao = pedidos.filter(p => p.solicitacaoId === solicitacao.id);
            
            // Verificar status inconsistente
            if (solicitacao.status === 'APROVADA' && cotacoesDaSolicitacao.length === 0) {
                problemas.push({
                    tipo: 'STATUS_INCONSISTENTE',
                    documento: 'SOLICITACAO',
                    id: solicitacao.id,
                    numero: solicitacao.numero,
                    problema: `Solicitação aprovada sem cotação gerada`,
                    gravidade: 'MEDIA'
                });
            }
            
            if (cotacoesDaSolicitacao.some(c => c.status === 'APROVADA') && pedidosDaSolicitacao.length === 0) {
                problemas.push({
                    tipo: 'STATUS_INCONSISTENTE',
                    documento: 'SOLICITACAO',
                    id: solicitacao.id,
                    numero: solicitacao.numero,
                    problema: `Cotação aprovada sem pedido gerado`,
                    gravidade: 'ALTA'
                });
            }
        });
        
        return problemas;
    }
    
    /**
     * Verificar numeração consistente
     */
    static async verificarNumeracao(solicitacoes, cotacoes, pedidos) {
        const problemas = [];
        
        // Verificar formato de numeração
        const verificarFormato = (docs, tipo, formatoEsperado) => {
            docs.forEach(doc => {
                if (!doc.numero) {
                    problemas.push({
                        tipo: 'NUMERACAO_AUSENTE',
                        documento: tipo,
                        id: doc.id,
                        problema: `${tipo} sem número definido`,
                        gravidade: 'ALTA'
                    });
                } else {
                    const match = doc.numero.match(formatoEsperado);
                    if (!match) {
                        problemas.push({
                            tipo: 'NUMERACAO_FORMATO_INCORRETO',
                            documento: tipo,
                            id: doc.id,
                            numero: doc.numero,
                            problema: `Formato de numeração incorreto: ${doc.numero}`,
                            gravidade: 'MEDIA'
                        });
                    }
                }
            });
        };
        
        verificarFormato(solicitacoes, 'SOLICITACAO', /^SC-\d{4}-\d{4}$/);
        verificarFormato(cotacoes, 'COTACAO', /^CT-\d{4}-\d{4}$/);
        verificarFormato(pedidos, 'PEDIDO', /^PC-\d{4}-\d{4}$/);
        
        return problemas;
    }
    
    /**
     * Verificar consistência de valores
     */
    static async verificarValores(solicitacoes, cotacoes, pedidos) {
        const problemas = [];
        
        cotacoes.forEach(cotacao => {
            if (cotacao.solicitacaoId) {
                const solicitacao = solicitacoes.find(s => s.id === cotacao.solicitacaoId);
                if (solicitacao) {
                    // Verificar se os itens batem
                    const itensSolicitacao = solicitacao.itens?.length || 0;
                    const itensCotacao = cotacao.itens?.length || 0;
                    
                    if (itensSolicitacao !== itensCotacao) {
                        problemas.push({
                            tipo: 'ITENS_DIVERGENTES',
                            documento: 'COTACAO',
                            id: cotacao.id,
                            numero: cotacao.numero,
                            problema: `Quantidade de itens divergente: SC=${itensSolicitacao}, CT=${itensCotacao}`,
                            gravidade: 'MEDIA'
                        });
                    }
                }
            }
        });
        
        return problemas;
    }
    
    /**
     * Corrigir problemas automaticamente
     */
    static async corrigirProblemasAutomaticamente(problemas) {
        let corrigidos = 0;
        
        for (const problema of problemas) {
            try {
                switch (problema.tipo) {
                    case 'REFERENCIA_UNIDIRECIONAL':
                        if (problema.correcao) {
                            await updateDoc(
                                doc(db, this.getCollectionName(problema.documento), problema.id), 
                                {
                                    [problema.correcao.campo]: problema.correcao.valor,
                                    corrigidoEm: Timestamp.now(),
                                    corrigidoPor: 'SISTEMA_SINCRONIZACAO'
                                }
                            );
                            corrigidos++;
                        }
                        break;
                }
            } catch (error) {
                console.error(`Erro ao corrigir problema ${problema.id}:`, error);
            }
        }
        
        return corrigidos;
    }
    
    /**
     * Gerar recomendações
     */
    static gerarRecomendacoes(problemas) {
        const recomendacoes = [];
        
        const tiposProblemas = new Set(problemas.map(p => p.tipo));
        
        if (tiposProblemas.has('REFERENCIA_PERDIDA')) {
            recomendacoes.push({
                tipo: 'CRITICO',
                titulo: 'Referências Perdidas',
                descricao: 'Existem documentos referenciando outros que não existem. Verifique se houve exclusão indevida.',
                acao: 'Revisar e corrigir manualmente as referências'
            });
        }
        
        if (tiposProblemas.has('STATUS_INCONSISTENTE')) {
            recomendacoes.push({
                tipo: 'IMPORTANTE',
                titulo: 'Status Inconsistentes',
                descricao: 'Fluxo de aprovação interrompido. Alguns documentos aprovados não geraram documentos subsequentes.',
                acao: 'Verificar e continuar o fluxo de aprovação'
            });
        }
        
        if (tiposProblemas.has('NUMERACAO_FORMATO_INCORRETO')) {
            recomendacoes.push({
                tipo: 'MANUTENCAO',
                titulo: 'Numeração Inconsistente',
                descricao: 'Usar apenas o serviço centralizado de numeração para manter padrão.',
                acao: 'Implementar validação de formato na criação de documentos'
            });
        }
        
        return recomendacoes;
    }
    
    /**
     * Mapear tipo de documento para coleção
     */
    static getCollectionName(tipo) {
        const mapa = {
            'SOLICITACAO': 'solicitacoesCompra',
            'COTACAO': 'cotacoes',
            'PEDIDO': 'pedidosCompra'
        };
        return mapa[tipo];
    }
    
    /**
     * Gerar relatório de sincronização
     */
    static async gerarRelatorioSincronizacao() {
        try {
            const relatorio = await this.verificarSincronizacaoCompleta();
            
            return {
                ...relatorio,
                resumo: {
                    problemasCriticos: relatorio.problemas.filter(p => p.gravidade === 'ALTA').length,
                    problemasImportantes: relatorio.problemas.filter(p => p.gravidade === 'MEDIA').length,
                    problemasLeves: relatorio.problemas.filter(p => p.gravidade === 'BAIXA').length,
                    statusGeral: relatorio.problemas.length === 0 ? 'SAUDAVEL' : 
                                relatorio.problemas.filter(p => p.gravidade === 'ALTA').length > 0 ? 'CRITICO' : 'ATENCAO'
                }
            };
        } catch (error) {
            console.error('❌ Erro ao gerar relatório:', error);
            throw error;
        }
    }
}

// Exportar para uso global
window.SyncValidationService = SyncValidationService;

console.log('🔄 Serviço de Validação de Sincronização carregado');
