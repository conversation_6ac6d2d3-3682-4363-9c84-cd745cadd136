<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção - Condições de Pagamento</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .warning-box h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .warning-box ul {
            color: #856404;
            margin-left: 20px;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-box h3 {
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .info-box p {
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            display: none;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Correção de Condições de Pagamento</h1>
            <p>Ferramenta para corrigir problemas de condicaoPagamentoId undefined</p>
        </div>
        
        <div class="content">
            <div class="warning-box">
                <h3>⚠️ Atenção - Leia antes de executar</h3>
                <ul>
                    <li>Esta ferramenta corrige pedidos e contas a pagar sem condição de pagamento</li>
                    <li>Será atribuída uma condição padrão "À Vista" aos documentos</li>
                    <li>Faça backup do banco de dados antes de executar</li>
                    <li>A operação não pode ser desfeita automaticamente</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h3>📋 O que será corrigido:</h3>
                <p><strong>1. Pedidos de Compra:</strong> Pedidos sem condicaoPagamentoId receberão condição padrão</p>
                <p><strong>2. Contas a Pagar:</strong> Contas com condicaoPagamentoId undefined serão corrigidas</p>
                <p><strong>3. Validação:</strong> Sistema verificará se existe condição "À Vista" ou criará uma padrão</p>
            </div>
            
            <button id="btnAnalyze" class="btn">
                🔍 Analisar Problemas
            </button>
            
            <button id="btnFix" class="btn" disabled>
                🔧 Executar Correção
            </button>
            
            <button id="btnVerify" class="btn btn-secondary" disabled>
                ✅ Verificar Resultado
            </button>
            
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="log-container" id="logContainer">
                <div id="logContent"></div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getFirestore, 
            collection, 
            getDocs, 
            doc, 
            updateDoc, 
            query, 
            where, 
            limit,
            writeBatch,
            addDoc
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Importar configuração do Firebase
        import { firebaseConfig } from './firebase-config.js';

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        let analysisResult = null;

        // Elementos DOM
        const btnAnalyze = document.getElementById('btnAnalyze');
        const btnFix = document.getElementById('btnFix');
        const btnVerify = document.getElementById('btnVerify');
        const logContainer = document.getElementById('logContainer');
        const logContent = document.getElementById('logContent');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContent.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            if (!logContainer.style.display || logContainer.style.display === 'none') {
                logContainer.style.display = 'block';
            }
        }

        // Função para atualizar progresso
        function updateProgress(percent) {
            progressBar.style.display = 'block';
            progressFill.style.width = `${percent}%`;
        }

        // Buscar condição de pagamento padrão
        async function getDefaultPaymentCondition() {
            addLog('🔍 Buscando condição de pagamento padrão...', 'info');
            
            try {
                // Primeiro, tentar buscar por descrição "À Vista"
                const aVistaQuery = query(
                    collection(db, "condicoesPagamento"),
                    where("descricao", "==", "À Vista"),
                    limit(1)
                );
                
                const aVistaSnap = await getDocs(aVistaQuery);
                if (!aVistaSnap.empty) {
                    const condition = { id: aVistaSnap.docs[0].id, ...aVistaSnap.docs[0].data() };
                    addLog(`✅ Encontrada condição "À Vista": ${condition.descricao}`, 'success');
                    return condition;
                }
                
                // Se não encontrar "À Vista", buscar por tipo "A_VISTA"
                const tipoQuery = query(
                    collection(db, "condicoesPagamento"),
                    where("tipo", "==", "A_VISTA"),
                    limit(1)
                );
                
                const tipoSnap = await getDocs(tipoQuery);
                if (!tipoSnap.empty) {
                    const condition = { id: tipoSnap.docs[0].id, ...tipoSnap.docs[0].data() };
                    addLog(`✅ Encontrada condição tipo "A_VISTA": ${condition.descricao}`, 'success');
                    return condition;
                }
                
                // Se não encontrar nenhuma, buscar a primeira disponível
                const allQuery = query(collection(db, "condicoesPagamento"), limit(1));
                const allSnap = await getDocs(allQuery);
                
                if (!allSnap.empty) {
                    const condition = { id: allSnap.docs[0].id, ...allSnap.docs[0].data() };
                    addLog(`⚠️ Usando primeira condição disponível: ${condition.descricao}`, 'warning');
                    return condition;
                }
                
                // Se não existir nenhuma, criar uma padrão
                addLog('📝 Criando condição de pagamento padrão...', 'info');
                const newCondition = {
                    descricao: 'À Vista',
                    tipo: 'A_VISTA',
                    parcelas: 1,
                    intervalo: 0,
                    entrada: 100,
                    ativo: true,
                    dataCriacao: new Date()
                };
                
                const docRef = await addDoc(collection(db, "condicoesPagamento"), newCondition);
                addLog('✅ Condição padrão criada com sucesso', 'success');
                
                return { id: docRef.id, ...newCondition };
                
            } catch (error) {
                addLog(`❌ Erro ao buscar condição de pagamento: ${error.message}`, 'error');
                throw error;
            }
        }

        // Analisar problemas
        async function analyzeProblems() {
            try {
                btnAnalyze.disabled = true;
                btnAnalyze.textContent = '🔍 Analisando...';
                
                addLog('🚀 Iniciando análise de problemas...', 'info');
                updateProgress(10);
                
                // Buscar condição padrão
                const defaultCondition = await getDefaultPaymentCondition();
                updateProgress(30);
                
                // Analisar pedidos
                addLog('📋 Analisando pedidos de compra...', 'info');
                const pedidosSnap = await getDocs(collection(db, "pedidosCompra"));
                const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const pedidosSemCondicao = pedidos.filter(p => !p.condicaoPagamentoId);
                
                updateProgress(60);
                
                // Analisar contas a pagar
                addLog('💰 Analisando contas a pagar...', 'info');
                const contasSnap = await getDocs(collection(db, "contasAPagar"));
                const contas = contasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const contasSemCondicao = contas.filter(c => !c.condicaoPagamentoId);
                
                updateProgress(90);
                
                // Resultado da análise
                analysisResult = {
                    defaultCondition,
                    pedidos: {
                        total: pedidos.length,
                        semCondicao: pedidosSemCondicao.length,
                        lista: pedidosSemCondicao
                    },
                    contas: {
                        total: contas.length,
                        semCondicao: contasSemCondicao.length,
                        lista: contasSemCondicao
                    }
                };
                
                updateProgress(100);
                
                // Mostrar resultados
                addLog('📊 RESULTADO DA ANÁLISE:', 'info');
                addLog(`📋 Pedidos: ${pedidos.length} total, ${pedidosSemCondicao.length} sem condição`, 'info');
                addLog(`💰 Contas: ${contas.length} total, ${contasSemCondicao.length} sem condição`, 'info');
                addLog(`🎯 Condição padrão: ${defaultCondition.descricao}`, 'info');
                
                if (pedidosSemCondicao.length > 0 || contasSemCondicao.length > 0) {
                    addLog('⚠️ Problemas encontrados! Clique em "Executar Correção"', 'warning');
                    btnFix.disabled = false;
                } else {
                    addLog('✅ Nenhum problema encontrado!', 'success');
                }
                
            } catch (error) {
                addLog(`❌ Erro durante análise: ${error.message}`, 'error');
            } finally {
                btnAnalyze.disabled = false;
                btnAnalyze.textContent = '🔍 Analisar Problemas';
            }
        }

        // Executar correção
        async function executeCorrection() {
            if (!analysisResult) {
                addLog('❌ Execute a análise primeiro!', 'error');
                return;
            }
            
            try {
                btnFix.disabled = true;
                btnFix.textContent = '🔧 Corrigindo...';
                
                addLog('🚀 Iniciando correção...', 'info');
                updateProgress(0);
                
                const { defaultCondition, pedidos, contas } = analysisResult;
                let totalOperations = pedidos.semCondicao + contas.semCondicao;
                let completedOperations = 0;
                
                // Corrigir pedidos
                if (pedidos.semCondicao > 0) {
                    addLog(`🔧 Corrigindo ${pedidos.semCondicao} pedidos...`, 'info');
                    
                    const batch = writeBatch(db);
                    
                    for (const pedido of pedidos.lista) {
                        const pedidoRef = doc(db, "pedidosCompra", pedido.id);
                        batch.update(pedidoRef, {
                            condicaoPagamentoId: defaultCondition.id,
                            observacoes: (pedido.observacoes || '') + 
                                `\n[CORREÇÃO AUTOMÁTICA] Condição de pagamento atribuída: ${defaultCondition.descricao}`
                        });
                        
                        completedOperations++;
                        updateProgress((completedOperations / totalOperations) * 50);
                    }
                    
                    await batch.commit();
                    addLog(`✅ ${pedidos.semCondicao} pedidos corrigidos`, 'success');
                }
                
                // Corrigir contas a pagar
                if (contas.semCondicao > 0) {
                    addLog(`🔧 Corrigindo ${contas.semCondicao} contas...`, 'info');
                    
                    const batch = writeBatch(db);
                    
                    for (const conta of contas.lista) {
                        const contaRef = doc(db, "contasAPagar", conta.id);
                        batch.update(contaRef, {
                            condicaoPagamentoId: defaultCondition.id,
                            observacoes: (conta.observacoes || '') + 
                                `\n[CORREÇÃO AUTOMÁTICA] Condição de pagamento atribuída: ${defaultCondition.descricao}`
                        });
                        
                        completedOperations++;
                        updateProgress(50 + (completedOperations / totalOperations) * 50);
                    }
                    
                    await batch.commit();
                    addLog(`✅ ${contas.semCondicao} contas corrigidas`, 'success');
                }
                
                updateProgress(100);
                addLog('🎉 Correção concluída com sucesso!', 'success');
                btnVerify.disabled = false;
                
            } catch (error) {
                addLog(`❌ Erro durante correção: ${error.message}`, 'error');
            } finally {
                btnFix.disabled = false;
                btnFix.textContent = '🔧 Executar Correção';
            }
        }

        // Verificar resultado
        async function verifyResult() {
            try {
                btnVerify.disabled = true;
                btnVerify.textContent = '✅ Verificando...';
                
                addLog('🔍 Verificando resultado da correção...', 'info');
                
                // Re-analisar para verificar
                await analyzeProblems();
                
            } finally {
                btnVerify.disabled = false;
                btnVerify.textContent = '✅ Verificar Resultado';
            }
        }

        // Event listeners
        btnAnalyze.addEventListener('click', analyzeProblems);
        btnFix.addEventListener('click', executeCorrection);
        btnVerify.addEventListener('click', verifyResult);

        // Inicialização
        addLog('🔧 Ferramenta de correção carregada. Clique em "Analisar Problemas" para começar.', 'info');
    </script>
</body>
</html>
