# 🔄 Documentação - Flag "Aguardando" entre Sistemas

## 📋 Resumo da Funcionalidade

O sistema implementa sincronização automática do status "AGUARDANDO" entre:
- **movimentacao_armazem_novo.html** (onde é definido)
- **apontamentos_simplificado_novo.html** (onde é exibido)

## 🔧 Como Funciona

### 1. **Definição do Status (movimentacao_armazem_novo.html)**

**Localização:** Função `toggleAguardandoMaterial(produtoId)`

```javascript
// Atualizar localmente
selectedOrder.materiaisNecessarios[materialIndex].aguardandoMaterial = novoStatus;
if (novoStatus) {
    selectedOrder.materiaisNecessarios[materialIndex].dataAguardo = new Date();
    selectedOrder.materiaisNecessarios[materialIndex].usuarioAguardo = 'Sistema';
}

// Salvar no Firebase
await updateDoc(doc(db, "ordensProducao", selectedOrder.id), {
    materiaisNecessarios: selectedOrder.materiaisNecessarios,
    ultimaAtualizacao: Timestamp.now()
});
```

**Campos salvos:**
- `aguardandoMaterial`: boolean
- `dataAguardo`: timestamp
- `usuarioAguardo`: string

### 2. **Exibição do Status (apontamentos_simplificado_novo.html)**

**Localização:** Função `analisarStatusMateriais(ordemId)`

```javascript
// Verificar se está aguardando material
const aguardandoMaterial = material.aguardandoMaterial || false;

let statusMaterial = 'PENDENTE';
if (restante <= 0) {
    statusMaterial = 'COMPLETO';
} else if (totalTransferido > 0) {
    statusMaterial = 'PARCIAL';
} else {
    // Se não foi transferido nada, verificar se está aguardando
    statusMaterial = aguardandoMaterial ? 'AGUARDANDO' : 'PENDENTE';
}
```

## 🎨 Interface Visual

### 1. **Lista Principal de OPs**
```html
${statusMateriais.detalhesMateriaisStatus?.some(m => m.aguardandoMaterial) ? `
    <span class="badge badge-warning"
          style="animation: pulse-aguardando 2s infinite;"
          title="Há materiais aguardando chegada">
        <i class="fas fa-clock"></i> AGUARDANDO
    </span>
` : ''}
```

### 2. **Detalhes do Material**
```html
${material.aguardandoMaterial ? `
    <div style="background: #e7f3ff; padding: 2px 6px; border-radius: 3px; border-left: 3px solid #17a2b8;">
        <i class="fas fa-clock"></i> <strong>Aguardando chegada</strong>
        ${material.dataAguardo ? `<br><small>Desde: ${new Date(material.dataAguardo.seconds * 1000).toLocaleDateString('pt-BR')}</small>` : ''}
    </div>
` : ''}
```

### 3. **CSS para Animação**
```css
@keyframes pulse-aguardando {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.badge-aguardando {
    background: rgba(255, 126, 20, 0.1);
    color: #fd7e14;
    animation: pulse-aguardando 2s infinite;
}
```

## 📊 Status dos Materiais

| Status | Descrição | Cor | Ícone |
|--------|-----------|-----|-------|
| COMPLETO | Material totalmente transferido | Verde | ✅ |
| PARCIAL | Material parcialmente transferido | Amarelo | ⚠️ |
| AGUARDANDO | Material aguardando chegada | Azul | ⏰ |
| PENDENTE | Material não transferido | Vermelho | ❌ |

## 🔄 Fluxo de Sincronização

```mermaid
graph TD
    A[movimentacao_armazem_novo.html] --> B[Usuário clica 'Aguardar']
    B --> C[Confirma ação]
    C --> D[Atualiza materiaisNecessarios]
    D --> E[Salva no Firebase]
    E --> F[apontamentos_simplificado_novo.html]
    F --> G[Lê automaticamente]
    G --> H[Exibe flag 'Aguardando']
```

## 🧪 Como Testar

### 1. **Marcar Material como Aguardando:**
1. Abra `movimentacao_armazem_novo.html`
2. Selecione uma OP com materiais
3. Clique no botão **"Aguardar"** de um material
4. Confirme a ação

### 2. **Verificar Sincronização:**
1. Abra `apontamentos_simplificado_novo.html`
2. Localize a mesma OP
3. Verifique se aparece:
   - Badge **"AGUARDANDO"** na lista principal
   - Flag **"Aguardando chegada"** nos detalhes do material

### 3. **Ferramenta de Teste:**
Use `teste_sincronizacao_aguardando.html` para verificar automaticamente.

## 📝 Estrutura de Dados

### Firebase - Coleção: `ordensProducao`
```json
{
  "materiaisNecessarios": [
    {
      "produtoId": "string",
      "quantidade": "number",
      "aguardandoMaterial": "boolean",
      "dataAguardo": "timestamp",
      "usuarioAguardo": "string"
    }
  ]
}
```

## ✅ Melhorias Implementadas

1. **Visual Aprimorado:**
   - Badge com animação pulsante
   - Destaque visual com bordas coloridas
   - Exibição da data de aguardo

2. **Informações Adicionais:**
   - Data desde quando está aguardando
   - Usuário que marcou como aguardando
   - Tooltip explicativo

3. **Responsividade:**
   - Funciona em diferentes tamanhos de tela
   - Badges adaptáveis

## 🎯 Benefícios

1. **Visibilidade:** Fácil identificação de materiais aguardando
2. **Rastreabilidade:** Histórico de quando foi marcado
3. **Sincronização:** Automática via Firebase
4. **Usabilidade:** Interface intuitiva e visual

## 🔧 Manutenção

### Campos Importantes:
- `aguardandoMaterial`: Controla a exibição do flag
- `dataAguardo`: Para rastreabilidade temporal
- `usuarioAguardo`: Para auditoria

### Funções Principais:
- `toggleAguardandoMaterial()`: Define o status
- `analisarStatusMateriais()`: Lê e processa o status
- `getStatusMaterialClass()`: Define as cores dos badges

---
*Documentação atualizada em: 22/07/2025*
