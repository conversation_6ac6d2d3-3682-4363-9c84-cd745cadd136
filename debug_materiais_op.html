<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Materiais da OP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .highlight { background: #ffeb3b; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 Debug Materiais da OP OP25060758</h1>
    
    <button onclick="debugMateriais()">Analisar Materiais da OP</button>
    <button onclick="corrigirNecessidade()">Corrigir Campo 'necessidade'</button>
    
    <div id="resultado"></div>

    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore-compat.js"></script>
    
    <script>
        // Configuração do Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyBxRnfR0wqYysZorCcg-kJpx5ZXAuGaEIc",
            authDomain: "banco-novo.firebaseapp.com",
            projectId: "banco-novo",
            storageBucket: "banco-novo.firebasestorage.app",
            messagingSenderId: "437400408801",
            appId: "1:437400408801:web:bcdc5df6e3257528e79da0",
            measurementId: "G-VP7F3LBQB2"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        let opData = null;
        let produtos = [];

        async function debugMateriais() {
            const resultado = document.getElementById('resultado');
            resultado.innerHTML = '<div class="info">🔄 Carregando dados...</div>';

            try {
                // Buscar OP específica
                const opDoc = await db.collection('ordensProducao').doc('OP25060758').get();
                if (!opDoc.exists) {
                    resultado.innerHTML = '<div class="error">❌ OP OP25060758 não encontrada!</div>';
                    return;
                }

                opData = opDoc.data();
                console.log('📋 Dados da OP:', opData);

                // Buscar produtos
                const produtosSnap = await db.collection('produtos').get();
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Analisar materiais
                const materiaisNecessarios = opData.materiaisNecessarios || [];
                
                let html = '<h2>📋 Análise dos Materiais da OP</h2>';
                html += `<div class="info"><strong>Total de materiais:</strong> ${materiaisNecessarios.length}</div>`;

                if (materiaisNecessarios.length === 0) {
                    html += '<div class="error">❌ Nenhum material encontrado na OP!</div>';
                    resultado.innerHTML = html;
                    return;
                }

                // Tabela de análise
                html += `
                    <table>
                        <thead>
                            <tr>
                                <th>Produto ID</th>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Tipo</th>
                                <th>Quantidade</th>
                                <th>Necessidade</th>
                                <th>Saldo Reservado</th>
                                <th>Aparecerá?</th>
                                <th>Motivo</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                materiaisNecessarios.forEach(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    const codigo = produto?.codigo || 'N/A';
                    const descricao = produto?.descricao || 'N/A';
                    const tipo = produto?.tipo || 'N/A';
                    
                    const quantidade = material.quantidade || 0;
                    const necessidade = material.necessidade || 0;
                    const saldoReservado = material.saldoReservado || 0;
                    
                    // Aplicar a mesma lógica do movimentacao_armazem.html
                    const temNecessidade = necessidade > 0;
                    const ehMP = produto && produto.tipo === 'MP';
                    const aparecera = temNecessidade && ehMP;
                    
                    let motivo = '';
                    if (!ehMP) motivo = 'Não é MP';
                    else if (!temNecessidade) motivo = 'Necessidade = 0';
                    else motivo = 'OK';
                    
                    const classe = aparecera ? '' : 'highlight';
                    
                    html += `
                        <tr class="${classe}">
                            <td>${material.produtoId}</td>
                            <td>${codigo}</td>
                            <td>${descricao}</td>
                            <td>${tipo}</td>
                            <td>${quantidade}</td>
                            <td>${necessidade}</td>
                            <td>${saldoReservado}</td>
                            <td>${aparecera ? '✅ SIM' : '❌ NÃO'}</td>
                            <td>${motivo}</td>
                        </tr>
                    `;
                });

                html += '</tbody></table>';
                
                // Resumo
                const materiaisQueApareceram = materiaisNecessarios.filter(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    return (material.necessidade > 0) && produto && produto.tipo === 'MP';
                }).length;
                
                html += `
                    <div class="info">
                        <strong>📊 Resumo:</strong><br>
                        • Total de materiais: ${materiaisNecessarios.length}<br>
                        • Materiais que aparecerão: ${materiaisQueApareceram}<br>
                        • Materiais ocultos: ${materiaisNecessarios.length - materiaisQueApareceram}
                    </div>
                `;

                if (materiaisQueApareceram === 0) {
                    html += '<div class="error"><strong>🚨 PROBLEMA IDENTIFICADO:</strong> Nenhum material aparecerá na movimentação!</div>';
                }

                resultado.innerHTML = html;

            } catch (error) {
                console.error('Erro:', error);
                resultado.innerHTML = `<div class="error">❌ Erro: ${error.message}</div>`;
            }
        }

        async function corrigirNecessidade() {
            if (!opData) {
                alert('Execute a análise primeiro!');
                return;
            }

            if (!confirm('Tem certeza que deseja corrigir o campo "necessidade" baseado no campo "quantidade"?')) {
                return;
            }

            const resultado = document.getElementById('resultado');
            resultado.innerHTML = '<div class="info">🔄 Corrigindo campos...</div>';

            try {
                const materiaisCorrigidos = opData.materiaisNecessarios.map(material => {
                    const necessidadeOriginal = material.necessidade || 0;
                    const quantidade = material.quantidade || 0;
                    
                    // Se necessidade é 0 mas quantidade > 0, corrigir
                    if (necessidadeOriginal === 0 && quantidade > 0) {
                        return { ...material, necessidade: quantidade };
                    }
                    return material;
                });

                // Atualizar no banco
                await db.collection('ordensProducao').doc('OP25060758').update({
                    materiaisNecessarios: materiaisCorrigidos
                });

                resultado.innerHTML = `
                    <div class="success">
                        ✅ <strong>Correção realizada!</strong><br>
                        Os campos "necessidade" foram atualizados baseados nos valores de "quantidade".<br><br>
                        <strong>🎯 Agora teste novamente o movimentacao_armazem.html!</strong>
                    </div>
                `;

            } catch (error) {
                console.error('Erro:', error);
                resultado.innerHTML = `<div class="error">❌ Erro ao corrigir: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
