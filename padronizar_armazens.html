<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 Padronização de Armazéns</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content { padding: 30px; }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #34495e;
            color: white;
            font-weight: 600;
        }
        .table tr:hover { background: #f8f9fa; }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { display: none; text-align: center; padding: 20px; }
        .loading.show { display: block; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #27ae60;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> Padronização de Armazéns</h1>
            <p>Corrige inconsistências de nomenclatura e IDs dos armazéns</p>
        </div>

        <div class="content">
            <!-- Análise do Sistema -->
            <div class="section">
                <h3><i class="fas fa-search"></i> 1. Análise do Sistema</h3>
                <button class="btn btn-primary" onclick="analisarSistema()">
                    <i class="fas fa-chart-bar"></i> Analisar Inconsistências
                </button>
                <div id="analiseResultados"></div>
            </div>

            <!-- Mapeamento Padrão -->
            <div class="section">
                <h3><i class="fas fa-map"></i> 2. Mapeamento Padrão</h3>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Padrão de Nomenclatura:</strong><br>
                        • ALM01 - ALMOXARIFADO (Matéria Prima)<br>
                        • PROD1 - PRODUCAO (Produtos em Produção)<br>
                        • PA01 - ACABADOS (Produtos Acabados)<br>
                        • QUAL1 - QUALIDADE (Controle de Qualidade)
                    </div>
                </div>
                <button class="btn btn-warning" onclick="criarArmazensPadrao()">
                    <i class="fas fa-plus"></i> Criar Armazéns Padrão
                </button>
            </div>

            <!-- Correção de Registros -->
            <div class="section">
                <h3><i class="fas fa-tools"></i> 3. Correção de Registros</h3>
                <button class="btn btn-success" onclick="executarPadronizacao()" id="btnPadronizar" disabled>
                    <i class="fas fa-check"></i> Executar Padronização
                </button>
                <button class="btn btn-danger" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText" style="text-align: center; margin-top: 10px;"></div>
            </div>

            <!-- Resultados -->
            <div class="section" id="resultadosSection" style="display: none;">
                <h3><i class="fas fa-list"></i> Resultados</h3>
                <div id="resultadosContainer"></div>
            </div>
        </div>
    </div>

    <div class="loading" id="loading">
        <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
        <p>Processando...</p>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc,
            addDoc,
            query,
            where,
            deleteDoc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let armazens = [];
        let estoques = [];
        let produtos = [];
        let movimentacoes = [];
        let inconsistencias = [];

        // Mapeamento padrão de armazéns (compatível com cadastro_armazem.html e config_parametros.html)
        const ARMAZENS_PADRAO = {
            'ALMOXARIFADO': { 
                codigo: 'ALM01', 
                nome: 'ALMOXARIFADO', 
                tipo: 'ALMOXARIFADO', 
                propriedade: 'PROPRIO',
                endereco: 'Armazém Principal de Matéria Prima',
                ativo: true,
                descricao: 'Armazém principal para materias primas e insumos'
            },
            'PRODUCAO': { 
                codigo: 'PROD1', 
                nome: 'PRODUCAO', 
                tipo: 'PRODUCAO', 
                propriedade: 'PROPRIO',
                endereco: 'Área de Produção',
                ativo: true,
                descricao: 'Armazém para produtos em processo de fabricação'
            },
            'ACABADOS': { 
                codigo: 'PA01', 
                nome: 'ACABADOS', 
                tipo: 'EXPEDICAO', 
                propriedade: 'PROPRIO',
                endereco: 'Área de Produtos Acabados',
                ativo: true,
                descricao: 'Armazém para produtos acabados prontos para expedição'
            },
            'QUALIDADE': { 
                codigo: 'QUAL1', 
                nome: 'QUALIDADE', 
                tipo: 'QUALIDADE', 
                propriedade: 'PROPRIO',
                endereco: 'Laboratório de Controle de Qualidade',
                ativo: true,
                descricao: 'Armazém para produtos em análise de qualidade e quarentena'
            }
        };

        // Mapeamento de IDs antigos para novos
        let mapeamentoIDs = new Map();

        window.onload = async function() {
            await carregarDados();
        };

        async function carregarDados() {
            try {
                showLoading(true);

                const [armazensSnap, estoquesSnap, produtosSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "movimentacoesEstoque"))
                ]);

                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`Carregados: ${armazens.length} armazéns, ${estoques.length} estoques, ${produtos.length} produtos, ${movimentacoes.length} movimentações`);

                showAlert('Dados carregados com sucesso!', 'success');

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showAlert('Erro ao carregar dados: ' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        window.analisarSistema = function() {
            inconsistencias = [];

            // Analisar armazéns existentes
            console.log('🔍 Analisando armazéns existentes...');
            console.table(armazens.map(a => ({
                ID: a.id,
                Código: a.codigo || 'N/A',
                Nome: a.nome || 'N/A',
                Tipo: a.tipo || 'N/A'
            })));

            // Verificar duplicatas por nome
            const nomesDuplicados = {};
            armazens.forEach(armazem => {
                const nome = (armazem.nome || '').toUpperCase();
                if (!nomesDuplicados[nome]) {
                    nomesDuplicados[nome] = [];
                }
                nomesDuplicados[nome].push(armazem);
            });

            Object.entries(nomesDuplicados).forEach(([nome, armazensComMesmoNome]) => {
                if (armazensComMesmoNome.length > 1) {
                    inconsistencias.push({
                        tipo: 'DUPLICATA',
                        descricao: `Armazém duplicado: ${nome}`,
                        armazens: armazensComMesmoNome,
                        severidade: 'ALTA'
                    });
                }
            });

            // Verificar códigos inconsistentes
            armazens.forEach(armazem => {
                const nome = (armazem.nome || '').toUpperCase();
                const codigo = armazem.codigo || '';

                if (nome.includes('ALMOXARIFADO') && codigo !== 'ALM01') {
                    inconsistencias.push({
                        tipo: 'CODIGO_INCORRETO',
                        descricao: `Código incorreto para ALMOXARIFADO: ${codigo} (deveria ser ALM01)`,
                        armazem: armazem,
                        codigoCorreto: 'ALM01',
                        severidade: 'ALTA'
                    });
                }

                if (nome.includes('PRODUCAO') && codigo !== 'PROD1') {
                    inconsistencias.push({
                        tipo: 'CODIGO_INCORRETO',
                        descricao: `Código incorreto para PRODUCAO: ${codigo} (deveria ser PROD1)`,
                        armazem: armazem,
                        codigoCorreto: 'PROD1',
                        severidade: 'ALTA'
                    });
                }
            });

            // Verificar registros órfãos nos estoques
            estoques.forEach(estoque => {
                const armazemEncontrado = armazens.find(a => a.id === estoque.armazemId);
                if (!armazemEncontrado) {
                    inconsistencias.push({
                        tipo: 'ORFAO_ESTOQUE',
                        descricao: `Estoque referencia armazém inexistente: ${estoque.armazemId}`,
                        estoque: estoque,
                        severidade: 'CRITICA'
                    });
                }
            });

            // Verificar referências nos produtos
            produtos.forEach(produto => {
                if (produto.armazemPadraoId) {
                    const armazemEncontrado = armazens.find(a => a.id === produto.armazemPadraoId);
                    if (!armazemEncontrado) {
                        inconsistencias.push({
                            tipo: 'ORFAO_PRODUTO',
                            descricao: `Produto ${produto.codigo} referencia armazém inexistente: ${produto.armazemPadraoId}`,
                            produto: produto,
                            severidade: 'MEDIA'
                        });
                    }
                }
            });

            exibirAnalise();
            document.getElementById('btnPadronizar').disabled = inconsistencias.length === 0;
        };

        function exibirAnalise() {
            const container = document.getElementById('analiseResultados');

            let html = `
                <div class="alert ${inconsistencias.length === 0 ? 'alert-success' : 'alert-warning'}">
                    <i class="fas ${inconsistencias.length === 0 ? 'fa-check' : 'fa-exclamation-triangle'}"></i>
                    <strong>${inconsistencias.length === 0 ? 'Sistema Consistente!' : `${inconsistencias.length} Inconsistências Encontradas`}</strong>
                </div>
            `;

            if (inconsistencias.length > 0) {
                html += `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Tipo</th>
                                <th>Descrição</th>
                                <th>Severidade</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                inconsistencias.forEach((inc, index) => {
                    html += `
                        <tr>
                            <td>${inc.tipo.replace('_', ' ')}</td>
                            <td>${inc.descricao}</td>
                            <td><span class="badge ${inc.severidade.toLowerCase()}">${inc.severidade}</span></td>
                            <td>
                                ${inc.tipo === 'DUPLICATA' ? 'Consolidar' : 
                                  inc.tipo === 'CODIGO_INCORRETO' ? 'Corrigir Código' : 
                                  inc.tipo.includes('ORFAO') ? 'Criar/Referenciar' : 'Corrigir'}
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table>';
            }

            // Resumo dos armazéns atuais
            html += `
                <h4>📊 Armazéns Atuais</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Status</th>
                            <th>Estoques</th>
                            <th>Produtos</th>
                            <th>Movimentações</th>
                            <th>Situação</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            armazens.forEach(armazem => {
                const estoquesCount = estoques.filter(e => e.armazemId === armazem.id).length;
                const produtosCount = produtos.filter(p => p.armazemPadraoId === armazem.id).length;
                const movimentacoesCount = movimentacoes.filter(m => m.armazemId === armazem.id).length;

                // Determinar situação
                let situacao = '✅ OK';
                let corSituacao = '#27ae60';

                if (estoquesCount === 0 && movimentacoesCount === 0) {
                    situacao = '⚪ Sem Uso';
                    corSituacao = '#95a5a6';
                } else if (estoquesCount > 0 && movimentacoesCount === 0) {
                    situacao = '⚠️ Sem Movimentação';
                    corSituacao = '#f39c12';
                } else if (estoquesCount === 0 && movimentacoesCount > 0) {
                    situacao = '🔄 Só Movimentações';
                    corSituacao = '#3498db';
                }

                html += `
                    <tr>
                        <td><strong>${armazem.codigo || 'N/A'}</strong></td>
                        <td>${armazem.nome || 'N/A'}</td>
                        <td>
                            <span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                                ${armazem.tipo || 'N/A'}
                            </span>
                        </td>
                        <td>
                            <span style="background: ${armazem.ativo ? '#d4edda' : '#f8d7da'}; color: ${armazem.ativo ? '#155724' : '#721c24'}; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                                ${armazem.ativo ? '✅ Ativo' : '❌ Inativo'}
                            </span>
                        </td>
                        <td style="text-align: center;">${estoquesCount}</td>
                        <td style="text-align: center;">${produtosCount}</td>
                        <td style="text-align: center;">${movimentacoesCount}</td>
                        <td>
                            <span style="color: ${corSituacao}; font-weight: bold;">
                                ${situacao}
                            </span>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';

            // Adicionar seção de armazéns órfãos (referenciados mas não existem)
            const armazensOrfaos = new Set();
            estoques.forEach(estoque => {
                if (!armazens.find(a => a.id === estoque.armazemId)) {
                    armazensOrfaos.add(estoque.armazemId);
                }
            });

            movimentacoes.forEach(mov => {
                if (!armazens.find(a => a.id === mov.armazemId)) {
                    armazensOrfaos.add(mov.armazemId);
                }
            });

            if (armazensOrfaos.size > 0) {
                html += `
                    <h4 style="color: #e74c3c; margin-top: 30px;">🚨 Armazéns Órfãos (Referenciados mas não existem)</h4>
                    <div class="alert alert-danger">
                        <strong>IDs encontrados em estoques/movimentações mas sem cadastro:</strong>
                        <ul style="margin: 10px 0 0 20px;">
                `;

                Array.from(armazensOrfaos).forEach(armazemId => {
                    const estoquesOrfaos = estoques.filter(e => e.armazemId === armazemId).length;
                    const movOrfaos = movimentacoes.filter(m => m.armazemId === armazemId).length;
                    html += `<li><code>${armazemId}</code> - ${estoquesOrfaos} estoques, ${movOrfaos} movimentações</li>`;
                });

                html += '</ul></div>';
            }
            container.innerHTML = html;
        }

        window.criarArmazensPadrao = async function() {
            try {
                showLoading(true);

                for (const [nome, config] of Object.entries(ARMAZENS_PADRAO)) {
                    // Verificar se já existe
                    const existe = armazens.find(a => 
                        (a.codigo === config.codigo) || 
                        (a.nome && a.nome.toUpperCase().includes(nome))
                    );

                    if (!existe) {
                        console.log(`Criando armazém: ${config.codigo} - ${config.nome}`);
                        const docRef = await addDoc(collection(db, "armazens"), config);
                        armazens.push({ id: docRef.id, ...config });
                        showAlert(`Armazém ${config.codigo} criado com sucesso!`, 'success');
                    } else {
                        console.log(`Armazém já existe: ${config.codigo}`);
                    }
                }

                await carregarDados();

            } catch (error) {
                console.error('Erro ao criar armazéns:', error);
                showAlert('Erro ao criar armazéns: ' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        };

        window.executarPadronizacao = async function() {
            if (!confirm('Confirma a execução da padronização? Esta operação irá modificar registros no banco de dados.')) {
                return;
            }

            try {
                showLoading(true);
                showProgress(0, 'Iniciando padronização...');

                let processados = 0;
                const total = inconsistencias.length;

                for (const inconsistencia of inconsistencias) {
                    await corrigirInconsistencia(inconsistencia);
                    processados++;
                    showProgress((processados / total) * 100, `Processando ${processados}/${total}...`);
                }

                showProgress(100, 'Padronização concluída!');
                showAlert('Padronização executada com sucesso!', 'success');

                // Recarregar dados
                await carregarDados();
                analisarSistema();

            } catch (error) {
                console.error('Erro na padronização:', error);
                showAlert('Erro na padronização: ' + error.message, 'danger');
            } finally {
                showLoading(false);
                hideProgress();
            }
        };

        async function corrigirInconsistencia(inconsistencia) {
            switch (inconsistencia.tipo) {
                case 'DUPLICATA':
                    await consolidarDuplicatas(inconsistencia.armazens);
                    break;

                case 'CODIGO_INCORRETO':
                    await corrigirCodigo(inconsistencia.armazem, inconsistencia.codigoCorreto);
                    break;

                case 'ORFAO_ESTOQUE':
                    await criarArmazemParaOrfao(inconsistencia.estoque);
                    break;

                case 'ORFAO_PRODUTO':
                    await corrigirReferenciaOrfa(inconsistencia.produto);
                    break;
            }
        }

        async function consolidarDuplicatas(armazensComMesmoNome) {
            // Manter o primeiro, consolidar estoques e deletar outros
            const principal = armazensComMesmoNome[0];
            const secundarios = armazensComMesmoNome.slice(1);

            for (const secundario of secundarios) {
                // Transferir estoques
                const estoquesSecundario = estoques.filter(e => e.armazemId === secundario.id);
                for (const estoque of estoquesSecundario) {
                    await updateDoc(doc(db, "estoques", estoque.id), {
                        armazemId: principal.id
                    });
                }

                // Deletar armazém secundário
                await deleteDoc(doc(db, "armazens", secundario.id));

                // Mapear para futuras referências
                mapeamentoIDs.set(secundario.id, principal.id);
            }
        }

        async function corrigirCodigo(armazem, codigoCorreto) {
            await updateDoc(doc(db, "armazens", armazem.id), {
                codigo: codigoCorreto
            });
        }

        async function criarArmazemParaOrfao(estoque) {
            // Criar armazém genérico para órfãos
            const novoArmazem = {
                codigo: 'TEMP01',
                nome: 'TEMPORARIO',
                tipo: 'TEMPORARIO',
                ativo: true,
                criadoPor: 'Sistema - Padronização'
            };

            const docRef = await addDoc(collection(db, "armazens"), novoArmazem);

            // Atualizar estoque órfão
            await updateDoc(doc(db, "estoques", estoque.id), {
                armazemId: docRef.id
            });
        }

        async function corrigirReferenciaOrfa(produto) {
            // Definir armazém padrão baseado no tipo
            let armazemPadrao = armazens.find(a => a.codigo === 'ALM01');

            if (produto.tipo === 'PA') {
                armazemPadrao = armazens.find(a => a.codigo === 'PA01') || armazemPadrao;
            }

            if (armazemPadrao) {
                await updateDoc(doc(db, "produtos", produto.id), {
                    armazemPadraoId: armazemPadrao.id
                });
            }
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('analiseResultados');
            const alertClass = {
                'success': 'alert-success',
                'warning': 'alert-warning', 
                'danger': 'alert-danger',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;

            if (alertContainer) {
                alertContainer.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            }
        }

        function showLoading(show) {
            document.getElementById('loading').classList.toggle('show', show);
        }

        function showProgress(percent, text) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressBar.style.display = 'block';
            progressFill.style.width = `${percent}%`;
            progressText.textContent = text;
        }

        function hideProgress() {
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('progressText').textContent = '';
        }

        // Estilo para badges
        const style = document.createElement('style');
        style.textContent = `
            .badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 600;
            }
            .badge.critica { background: #f8d7da; color: #721c24; }
            .badge.alta { background: #fff3cd; color: #856404; }
            .badge.media { background: #d1ecf1; color: #0c5460; }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 