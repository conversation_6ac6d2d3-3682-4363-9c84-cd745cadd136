
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Relatório de Orçamentos</title>
  <style>
    @media print {
      .no-print { display: none; }
      .page-break { page-break-after: always; }
    }
    
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    
    .filters {
      margin-bottom: 20px;
      padding: 15px;
      background: #f5f5f5;
      border-radius: 5px;
    }
    
    .filter-row {
      display: flex;
      gap: 15px;
      margin-bottom: 10px;
    }
    
    .filter-group {
      flex: 1;
    }
    
    .filter-group label {
      display: block;
      margin-bottom: 5px;
    }
    
    .filter-group input, .filter-group select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .quote-card {
      border: 1px solid #ddd;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    
    .quote-header {
      border-bottom: 2px solid #333;
      margin-bottom: 15px;
      padding-bottom: 10px;
    }
    
    .quote-details {
      margin-bottom: 15px;
    }
    
    .quote-products table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 15px;
    }
    
    .quote-products th, .quote-products td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    
    .quote-total {
      text-align: right;
      font-weight: bold;
    }
    
    .btn {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    
    .btn-primary {
      background: #0854a0;
      color: white;
    }
  </style>
</head>
<body>
  <div class="no-print">
    <div class="filters">
      <div class="filter-row">
        <div class="filter-group">
          <label>Data Inicial:</label>
          <input type="date" id="startDate">
        </div>
        <div class="filter-group">
          <label>Data Final:</label>
          <input type="date" id="endDate">
        </div>
        <div class="filter-group">
          <label>Status:</label>
          <select id="statusFilter">
            <option value="">Todos</option>
            <option value="Aberto">Aberto</option>
            <option value="Aguardando Aprovação">Aguardando Aprovação</option>
            <option value="Aprovado">Aprovado</option>
            <option value="Rejeitado">Rejeitado</option>
          </select>
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-group">
          <label>Cliente:</label>
          <input type="text" id="clientFilter" placeholder="Nome do cliente...">
        </div>
        <div class="filter-group">
          <label>Número do Orçamento:</label>
          <input type="text" id="quoteNumber" placeholder="Número...">
        </div>
      </div>
    </div>
    <button class="btn btn-primary" onclick="generateReport()">Gerar Relatório</button>
    <button class="btn btn-primary" onclick="window.print()">Imprimir</button>
    <button class="btn" onclick="window.location.href='orcamentos.html'">Voltar</button>
  </div>

  <div id="reportContent"></div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, query, where, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let orcamentos = [];
    let clientes = [];
    let produtos = [];

    window.onload = async function() {
      await loadData();
    };

    async function loadData() {
      const [orcamentosSnap, clientesSnap, produtosSnap] = await Promise.all([
        getDocs(collection(db, "orcamentos")),
        getDocs(collection(db, "fornecedores")),
        getDocs(collection(db, "produtos"))
      ]);

      orcamentos = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      clientes = clientesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }

    window.generateReport = function() {
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      const status = document.getElementById('statusFilter').value;
      const clientFilter = document.getElementById('clientFilter').value.toLowerCase();
      const quoteNumber = document.getElementById('quoteNumber').value;

      let filteredQuotes = orcamentos;

      if (startDate && endDate) {
        filteredQuotes = filteredQuotes.filter(quote => {
          const quoteDate = new Date(quote.dataCriacao.seconds * 1000);
          return quoteDate >= new Date(startDate) && quoteDate <= new Date(endDate + ' 23:59:59');
        });
      }

      if (status) {
        filteredQuotes = filteredQuotes.filter(quote => quote.status === status);
      }

      if (clientFilter) {
        filteredQuotes = filteredQuotes.filter(quote => {
          const cliente = clientes.find(c => c.id === quote.clienteId);
          return cliente?.nome.toLowerCase().includes(clientFilter);
        });
      }

      if (quoteNumber) {
        filteredQuotes = filteredQuotes.filter(quote => 
          quote.numero.includes(quoteNumber)
        );
      }

      const reportContent = document.getElementById('reportContent');
      reportContent.innerHTML = '';

      filteredQuotes.forEach(quote => {
        const cliente = clientes.find(c => c.id === quote.clienteId);
        const produto = produtos.find(p => p.id === quote.produtoId);

        const quoteElement = document.createElement('div');
        quoteElement.className = 'quote-card page-break';
        quoteElement.innerHTML = `
          <div class="quote-header">
            <h2>Orçamento Nº ${quote.numero}</h2>
            <p>Data: ${new Date(quote.dataCriacao.seconds * 1000).toLocaleDateString()}</p>
          </div>
          <div class="quote-details">
            <p><strong>Cliente:</strong> ${cliente?.nome || 'N/A'}</p>
            <p><strong>Status:</strong> ${quote.status}</p>
            <p><strong>Validade:</strong> ${new Date(quote.dataValidade.seconds * 1000).toLocaleDateString()}</p>
            <p><strong>Condição de Pagamento:</strong> ${quote.condicaoPagamento}</p>
          </div>
          <div class="quote-products">
            <table>
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>Quantidade</th>
                  <th>Valor Unitário</th>
                  <th>Valor Total</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>${produto?.descricao || 'N/A'}</td>
                  <td>${quote.quantidade}</td>
                  <td>R$ ${quote.valorUnitario.toFixed(2)}</td>
                  <td>R$ ${quote.valorTotal.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="quote-total">
            <p>Total: R$ ${quote.valorTotal.toFixed(2)}</p>
          </div>
        `;
        
        reportContent.appendChild(quoteElement);
      });
    };
  </script>
</body>
</html>
