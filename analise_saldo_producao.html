
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análise de Saldo na Produção</title>
    <link rel="stylesheet" href="styles/sistema-padronizado.css">
    <style>
        .analysis-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .filters-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .material-analysis {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .balance-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .balance-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .balance-card.available {
            border-left-color: #28a745;
        }
        
        .balance-card.committed {
            border-left-color: #ffc107;
        }
        
        .balance-card.consumed {
            border-left-color: #dc3545;
        }
        
        .timeline {
            border-left: 3px solid #e9ecef;
            padding-left: 20px;
            margin: 20px 0;
        }
        
        .timeline-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -26px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
        }
        
        .timeline-item.transfer::before {
            background: #28a745;
        }
        
        .timeline-item.consumption::before {
            background: #dc3545;
        }
        
        .timeline-item.adjustment::before {
            background: #ffc107;
        }
        
        .material-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .material-info {
            flex: 1;
        }
        
        .material-actions {
            display: flex;
            gap: 10px;
        }
        
        .analysis-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .analysis-table th,
        .analysis-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .analysis-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-committed {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-consumed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .recommendation-box {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .recommendation-box h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="analysis-container">
        <h1>🔍 Análise de Saldo na Produção</h1>
        <p>Analise o histórico e origem do saldo de materiais no armazém de produção</p>
        
        <!-- Filtros -->
        <div class="filters-section">
            <h3>Filtros de Análise</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <label>Armazém de Produção:</label>
                    <select id="warehouseFilter" style="width: 100%; padding: 8px;">
                        <option value="">Selecione o armazém</option>
                    </select>
                </div>
                <div>
                    <label>Produto:</label>
                    <select id="productFilter" style="width: 100%; padding: 8px;">
                        <option value="">Todos os produtos</option>
                    </select>
                </div>
                <div>
                    <label>Período:</label>
                    <select id="periodFilter" style="width: 100%; padding: 8px;">
                        <option value="30">Últimos 30 dias</option>
                        <option value="60">Últimos 60 dias</option>
                        <option value="90">Últimos 90 dias</option>
                        <option value="180">Últimos 6 meses</option>
                    </select>
                </div>
                <div style="display: flex; align-items: end;">
                    <button onclick="analisarSaldos()" class="btn btn-primary" style="width: 100%;">
                        🔍 Analisar Saldos
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Resultados da Análise -->
        <div id="analysisResults" style="display: none;">
            <div class="material-analysis">
                <div class="balance-summary">
                    <div class="balance-card">
                        <h4>Saldo Total</h4>
                        <div id="totalBalance" style="font-size: 24px; font-weight: bold; color: #007bff;">0.000</div>
                        <small>Quantidade atual</small>
                    </div>
                    <div class="balance-card available">
                        <h4>Disponível</h4>
                        <div id="availableBalance" style="font-size: 24px; font-weight: bold; color: #28a745;">0.000</div>
                        <small>Pode ser usado</small>
                    </div>
                    <div class="balance-card committed">
                        <h4>Comprometido</h4>
                        <div id="committedBalance" style="font-size: 24px; font-weight: bold; color: #ffc107;">0.000</div>
                        <small>Reservado para OPs</small>
                    </div>
                    <div class="balance-card consumed">
                        <h4>Já Consumido</h4>
                        <div id="consumedBalance" style="font-size: 24px; font-weight: bold; color: #dc3545;">0.000</div>
                        <small>Usado em produção</small>
                    </div>
                </div>
                
                <div id="materialDetailsContainer"></div>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, query, where, orderBy, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        const firebaseConfig = {
            apiKey: "AIzaSyCqNYMbOZa-3j85X_Af6qd7Hb4o8ZxK8xg",
            authDomain: "banco-mrp.firebaseapp.com",
            projectId: "banco-mrp",
            storageBucket: "banco-mrp.appspot.com",
            messagingSenderId: "548636448558",
            appId: "1:548636448558:web:31fa3657ee0e6a8e6dc999"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        window.db = db;
        window.firestoreFunctions = { collection, getDocs, query, where, orderBy, Timestamp };
    </script>

    <script>
        let produtos = [];
        let armazens = [];
        let estoques = [];
        let transferencias = [];
        let movimentacoes = [];
        let ordensProducao = [];

        // Carregar dados iniciais
        async function carregarDados() {
            try {
                console.log('🔄 Carregando dados...');
                
                // Carregar todos os dados necessários
                const [produtosSnap, armazensSnap, estoquesSnap, transferenciasSnap, movimentacoesSnap, opsSnap] = await Promise.all([
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "produtos")),
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "armazens")),
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "estoques")),
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "transferenciasArmazem")),
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "movimentacoesEstoque")),
                    window.firestoreFunctions.getDocs(window.firestoreFunctions.collection(window.db, "ordensProducao"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('✅ Dados carregados:', {
                    produtos: produtos.length,
                    armazens: armazens.length,
                    estoques: estoques.length,
                    transferencias: transferencias.length,
                    movimentacoes: movimentacoes.length,
                    ordensProducao: ordensProducao.length
                });

                preencherFiltros();
                
            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                alert('Erro ao carregar dados: ' + error.message);
            }
        }

        // Preencher filtros
        function preencherFiltros() {
            const warehouseSelect = document.getElementById('warehouseFilter');
            const productSelect = document.getElementById('productFilter');

            // Filtrar apenas armazéns de produção
            const armazensProducao = armazens.filter(a => a.tipo === 'PRODUCAO');
            
            warehouseSelect.innerHTML = '<option value="">Selecione o armazém</option>';
            armazensProducao.forEach(armazem => {
                warehouseSelect.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.descricao}</option>`;
            });

            // Produtos que têm estoque na produção
            const produtosComEstoque = [...new Set(estoques
                .filter(e => armazensProducao.find(a => a.id === e.armazemId) && e.saldo > 0)
                .map(e => e.produtoId))];

            productSelect.innerHTML = '<option value="">Todos os produtos</option>';
            produtosComEstoque.forEach(produtoId => {
                const produto = produtos.find(p => p.id === produtoId);
                if (produto) {
                    productSelect.innerHTML += `<option value="${produtoId}">${produto.codigo} - ${produto.descricao}</option>`;
                }
            });
        }

        // Função principal de análise
        async function analisarSaldos() {
            const warehouseId = document.getElementById('warehouseFilter').value;
            const productId = document.getElementById('productFilter').value;
            const periodDays = parseInt(document.getElementById('periodFilter').value);

            if (!warehouseId) {
                alert('Por favor, selecione um armazém de produção');
                return;
            }

            try {
                console.log('🔍 Iniciando análise de saldos...');
                
                // Filtrar estoques do armazém selecionado
                let estoquesArmazem = estoques.filter(e => e.armazemId === warehouseId && e.saldo > 0);
                
                if (productId) {
                    estoquesArmazem = estoquesArmazem.filter(e => e.produtoId === productId);
                }

                if (estoquesArmazem.length === 0) {
                    alert('Nenhum estoque encontrado para os filtros selecionados');
                    return;
                }

                // Data limite para análise
                const dataLimite = new Date();
                dataLimite.setDate(dataLimite.getDate() - periodDays);

                let totalSaldo = 0;
                let totalDisponivel = 0;
                let totalComprometido = 0;
                let totalConsumido = 0;

                const detalhesContainer = document.getElementById('materialDetailsContainer');
                detalhesContainer.innerHTML = '';

                // Analisar cada material
                for (const estoque of estoquesArmazem) {
                    const produto = produtos.find(p => p.id === estoque.produtoId);
                    if (!produto) continue;

                    const analise = await analisarMaterial(estoque, produto, warehouseId, dataLimite);
                    
                    totalSaldo += analise.saldoTotal;
                    totalDisponivel += analise.saldoDisponivel;
                    totalComprometido += analise.saldoComprometido;
                    totalConsumido += analise.totalConsumido;

                    // Criar seção do material
                    const materialDiv = criarSecaoMaterial(produto, analise);
                    detalhesContainer.appendChild(materialDiv);
                }

                // Atualizar resumo
                document.getElementById('totalBalance').textContent = totalSaldo.toFixed(3);
                document.getElementById('availableBalance').textContent = totalDisponivel.toFixed(3);
                document.getElementById('committedBalance').textContent = totalComprometido.toFixed(3);
                document.getElementById('consumedBalance').textContent = totalConsumido.toFixed(3);

                // Mostrar resultados
                document.getElementById('analysisResults').style.display = 'block';

                console.log('✅ Análise concluída');

            } catch (error) {
                console.error('❌ Erro na análise:', error);
                alert('Erro na análise: ' + error.message);
            }
        }

        // Analisar material específico
        async function analisarMaterial(estoque, produto, armazemId, dataLimite) {
            const produtoId = estoque.produtoId;
            
            // Histórico de transferências para este material neste armazém
            const transferenciasMateria = transferencias.filter(t => {
                if (t.armazemDestinoId !== armazemId) return false;
                
                // Suporte a formato antigo e novo
                if (t.materiais && Array.isArray(t.materiais)) {
                    return t.materiais.some(m => m.produtoId === produtoId);
                }
                return t.produtoId === produtoId;
            });

            // Movimentações do material neste armazém
            const movimentacoesMateria = movimentacoes.filter(m => 
                m.produtoId === produtoId && 
                m.armazemId === armazemId &&
                new Date(m.dataHora?.toDate ? m.dataHora.toDate() : m.dataHora) >= dataLimite
            );

            // OPs que usam este material
            const opsComMaterial = ordensProducao.filter(op => 
                op.armazemProducaoId === armazemId &&
                op.materiaisNecessarios?.some(m => m.produtoId === produtoId)
            );

            // Calcular saldos
            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;
            // Corrigido: incluir saldoEmpenhado no cálculo
            const saldoDisponivel = Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);

            // Calcular consumo total no período
            const consumoTotal = movimentacoesMateria
                .filter(m => m.tipo === 'SAIDA' && m.tipoDocumento === 'PRODUCAO')
                .reduce((total, m) => total + (m.quantidade || 0), 0);

            // Entradas por transferência no período
            const entradasTransferencia = movimentacoesMateria
                .filter(m => m.tipo === 'ENTRADA' && m.tipoDocumento === 'TRANSFERENCIA')
                .reduce((total, m) => total + (m.quantidade || 0), 0);

            // 🆕 ANÁLISE DETALHADA POR OP
            const analisePorOP = analisarUsoMaterialPorOP(produtoId, transferenciasMateria, movimentacoesMateria, opsComMaterial);

            // Timeline de eventos
            const timeline = criarTimeline(transferenciasMateria, movimentacoesMateria, opsComMaterial);

            // Recomendações melhoradas
            const recomendacoes = gerarRecomendacoes(saldoDisponivel, saldoReservado, opsComMaterial, timeline, analisePorOP);

            return {
                saldoTotal,
                saldoDisponivel,
                saldoComprometido: saldoReservado,
                totalConsumido: consumoTotal,
                entradasTransferencia,
                transferencias: transferenciasMateria,
                movimentacoes: movimentacoesMateria,
                ordensProducao: opsComMaterial,
                analisePorOP, // 🆕 Nova análise detalhada
                timeline,
                recomendacoes
            };
        }

        // 🆕 Nova função para analisar uso do material por cada OP
        function analisarUsoMaterialPorOP(produtoId, transferencias, movimentacoes, ordensProducao) {
            const analisePorOP = [];

            // Agrupar transferências por OP
            const transferenciasPorOP = {};
            transferencias.forEach(t => {
                if (t.ordemProducaoId) {
                    if (!transferenciasPorOP[t.ordemProducaoId]) {
                        transferenciasPorOP[t.ordemProducaoId] = [];
                    }
                    transferenciasPorOP[t.ordemProducaoId].push(t);
                }
            });

            // Agrupar consumos por OP
            const consumosPorOP = {};
            movimentacoes.forEach(m => {
                if (m.tipo === 'SAIDA' && m.tipoDocumento === 'PRODUCAO' && m.numeroDocumento) {
                    const op = ordensProducao.find(o => o.numero === m.numeroDocumento);
                    if (op) {
                        if (!consumosPorOP[op.id]) {
                            consumosPorOP[op.id] = [];
                        }
                        consumosPorOP[op.id].push(m);
                    }
                }
            });

            // Analisar cada OP
            ordensProducao.forEach(op => {
                const materialNecessario = op.materiaisNecessarios?.find(m => m.produtoId === produtoId);
                if (!materialNecessario) return;

                const transferenciasOP = transferenciasPorOP[op.id] || [];
                const consumosOP = consumosPorOP[op.id] || [];

                const quantidadeTransferida = transferenciasOP.reduce((total, t) => {
                    if (t.materiais && Array.isArray(t.materiais)) {
                        const material = t.materiais.find(m => m.produtoId === produtoId);
                        return total + (material?.quantidade || 0);
                    }
                    return total + (t.quantidade || 0);
                }, 0);

                const quantidadeConsumida = consumosOP.reduce((total, c) => total + (c.quantidade || 0), 0);
                const quantidadeNecessaria = materialNecessario.quantidade || 0;
                const saldoRemanescente = quantidadeTransferida - quantidadeConsumida;

                let statusOP = 'nao_iniciada';
                if (quantidadeConsumida > 0) {
                    statusOP = quantidadeConsumida >= quantidadeNecessaria ? 'finalizada' : 'em_andamento';
                }

                analisePorOP.push({
                    op: op,
                    materialNecessario,
                    quantidadeTransferida,
                    quantidadeConsumida,
                    quantidadeNecessaria,
                    saldoRemanescente,
                    statusOP,
                    transferencias: transferenciasOP,
                    consumos: consumosOP,
                    eficiencia: quantidadeNecessaria > 0 ? ((quantidadeConsumida / quantidadeNecessaria) * 100).toFixed(1) : 0
                });
            });

            return analisePorOP.sort((a, b) => {
                // Ordenar por data de criação da OP
                const dataA = a.op.dataCriacao?.toDate ? a.op.dataCriacao.toDate() : new Date(a.op.dataCriacao);
                const dataB = b.op.dataCriacao?.toDate ? b.op.dataCriacao.toDate() : new Date(b.op.dataCriacao);
                return dataB - dataA;
            });
        }

        // Criar timeline de eventos
        function criarTimeline(transferencias, movimentacoes, ops) {
            const eventos = [];

            // Adicionar transferências
            transferencias.forEach(t => {
                eventos.push({
                    tipo: 'transfer',
                    data: t.dataHora?.toDate ? t.dataHora.toDate() : new Date(t.dataHora),
                    titulo: `Transferência ${t.numero || 'TRF-' + t.id.substring(0,6)}`,
                    descricao: `${t.motivo || 'Transferência'} - OP: ${t.ordemProducaoId || 'N/A'}`,
                    quantidade: t.quantidade || 0,
                    observacoes: t.observacoes
                });
            });

            // Adicionar movimentações importantes
            movimentacoes.forEach(m => {
                if (m.tipoDocumento === 'PRODUCAO') {
                    eventos.push({
                        tipo: 'consumption',
                        data: m.dataHora?.toDate ? m.dataHora.toDate() : new Date(m.dataHora),
                        titulo: `Consumo em Produção`,
                        descricao: `OP: ${m.numeroDocumento || 'N/A'}`,
                        quantidade: m.tipo === 'SAIDA' ? -(m.quantidade || 0) : (m.quantidade || 0),
                        observacoes: m.observacoes
                    });
                }
            });

            // Ordenar por data
            eventos.sort((a, b) => b.data - a.data);

            return eventos;
        }

        // Gerar recomendações melhoradas
        function gerarRecomendacoes(saldoDisponivel, saldoReservado, ops, timeline, analisePorOP) {
            const recomendacoes = [];

            // Análise de disponibilidade
            if (saldoDisponivel > 0 && saldoReservado === 0) {
                recomendacoes.push({
                    tipo: 'info',
                    titulo: 'Material Disponível',
                    texto: `Este material está disponível (${saldoDisponivel.toFixed(3)}) e pode ser usado em novas ordens de produção.`
                });
            }

            // Análise de comprometimento
            if (saldoReservado > 0) {
                const opsAtivas = ops.filter(op => ['PENDENTE', 'EM_PRODUCAO'].includes(op.status));
                if (opsAtivas.length > 0) {
                    recomendacoes.push({
                        tipo: 'warning',
                        titulo: 'Material Comprometido',
                        texto: `${saldoReservado.toFixed(3)} está reservado para ${opsAtivas.length} OP(s) ativa(s).`
                    });
                }
            }

            // 🆕 Análise de material sobrando de OPs anteriores
            if (analisePorOP && analisePorOP.length > 0) {
                const opsComSobra = analisePorOP.filter(a => a.saldoRemanescente > 0 && a.statusOP === 'finalizada');
                if (opsComSobra.length > 0) {
                    const totalSobra = opsComSobra.reduce((total, a) => total + a.saldoRemanescente, 0);
                    recomendacoes.push({
                        tipo: 'success',
                        titulo: 'Material Remanescente de OPs Anteriores',
                        texto: `${totalSobra.toFixed(3)} disponível de ${opsComSobra.length} OP(s) já finalizadas. Pode ser reutilizado.`
                    });
                }

                // Análise de OPs com consumo excessivo
                const opsComExcesso = analisePorOP.filter(a => a.quantidadeConsumida > a.quantidadeNecessaria);
                if (opsComExcesso.length > 0) {
                    recomendacoes.push({
                        tipo: 'warning',
                        titulo: 'Consumo Excessivo Detectado',
                        texto: `${opsComExcesso.length} OP(s) consumiram mais material que o necessário. Verifique perdas.`
                    });
                }

                // Análise de OPs em andamento
                const opsEmAndamento = analisePorOP.filter(a => a.statusOP === 'em_andamento');
                if (opsEmAndamento.length > 0) {
                    recomendacoes.push({
                        tipo: 'info',
                        titulo: 'OPs em Andamento',
                        texto: `${opsEmAndamento.length} OP(s) estão consumindo este material atualmente.`
                    });
                }
            }

            // Análise de transferências
            if (timeline.length > 0) {
                const ultimaTransferencia = timeline.find(t => t.tipo === 'transfer');
                if (ultimaTransferencia) {
                    recomendacoes.push({
                        tipo: 'success',
                        titulo: 'Última Transferência',
                        texto: `Última transferência: ${ultimaTransferencia.titulo} em ${ultimaTransferencia.data.toLocaleDateString()}`
                    });
                }
            }

            return recomendacoes;
        }

        // Criar seção do material
        function criarSecaoMaterial(produto, analise) {
            const div = document.createElement('div');
            div.className = 'material-analysis';
            
            div.innerHTML = `
                <div class="material-header">
                    <div class="material-info">
                        <h3>${produto.codigo} - ${produto.descricao}</h3>
                        <p>Unidade: ${produto.unidade || 'UN'}</p>
                    </div>
                    <div class="material-actions">
                        <span class="status-badge status-available">
                            Disponível: ${analise.saldoDisponivel.toFixed(3)}
                        </span>
                        ${analise.saldoComprometido > 0 ? `
                            <span class="status-badge status-committed">
                                Reservado: ${analise.saldoComprometido.toFixed(3)}
                            </span>
                        ` : ''}
                    </div>
                </div>

                <div class="balance-summary">
                    <div class="balance-card">
                        <h4>Saldo Atual</h4>
                        <div style="font-size: 20px; font-weight: bold;">${analise.saldoTotal.toFixed(3)}</div>
                    </div>
                    <div class="balance-card available">
                        <h4>Disponível</h4>
                        <div style="font-size: 20px; font-weight: bold;">${analise.saldoDisponivel.toFixed(3)}</div>
                    </div>
                    <div class="balance-card committed">
                        <h4>Reservado</h4>
                        <div style="font-size: 20px; font-weight: bold;">${analise.saldoComprometido.toFixed(3)}</div>
                    </div>
                    <div class="balance-card">
                        <h4>Entradas por Transferência</h4>
                        <div style="font-size: 20px; font-weight: bold;">${analise.entradasTransferencia.toFixed(3)}</div>
                    </div>
                </div>

                ${analise.recomendacoes.length > 0 ? `
                    <div class="recommendation-box">
                        <h4>💡 Recomendações</h4>
                        ${analise.recomendacoes.map(r => `
                            <p><strong>${r.titulo}:</strong> ${r.texto}</p>
                        `).join('')}
                    </div>
                ` : ''}

                ${analise.timeline.length > 0 ? `
                    <h4>📅 Histórico de Movimentações</h4>
                    <div class="timeline">
                        ${analise.timeline.slice(0, 10).map(evento => `
                            <div class="timeline-item ${evento.tipo}">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>${evento.titulo}</strong>
                                        <p style="margin: 5px 0; color: #666;">${evento.descricao}</p>
                                        ${evento.observacoes ? `<small style="color: #888;">${evento.observacoes}</small>` : ''}
                                    </div>
                                    <div style="text-align: right;">
                                        <div style="font-weight: bold; color: ${evento.quantidade >= 0 ? '#28a745' : '#dc3545'}">
                                            ${evento.quantidade >= 0 ? '+' : ''}${evento.quantidade.toFixed(3)}
                                        </div>
                                        <small>${evento.data.toLocaleDateString()} ${evento.data.toLocaleTimeString()}</small>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                ${analise.analisePorOP && analise.analisePorOP.length > 0 ? `
                    <h4>🏭 Análise Detalhada por Ordem de Produção</h4>
                    <table class="analysis-table">
                        <thead>
                            <tr>
                                <th>OP</th>
                                <th>Status</th>
                                <th>Necessário</th>
                                <th>Transferido</th>
                                <th>Consumido</th>
                                <th>Saldo Remanescente</th>
                                <th>Eficiência</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${analise.analisePorOP.map(analiseOP => {
                                const op = analiseOP.op;
                                const statusClass = analiseOP.statusOP === 'finalizada' ? 'status-consumed' : 
                                                  analiseOP.statusOP === 'em_andamento' ? 'status-committed' : 'status-available';
                                const saldoClass = analiseOP.saldoRemanescente > 0 ? 'status-available' : 
                                                 analiseOP.saldoRemanescente < 0 ? 'status-consumed' : '';
                                
                                return `
                                    <tr>
                                        <td>
                                            <strong>${op.numero || op.id.substring(0,8)}</strong>
                                            <br><small>${op.dataCriacao?.toDate ? op.dataCriacao.toDate().toLocaleDateString() : 'N/A'}</small>
                                        </td>
                                        <td>
                                            <span class="status-badge ${statusClass}">
                                                ${analiseOP.statusOP.replace('_', ' ').toUpperCase()}
                                            </span>
                                        </td>
                                        <td>${analiseOP.quantidadeNecessaria.toFixed(3)}</td>
                                        <td>${analiseOP.quantidadeTransferida.toFixed(3)}</td>
                                        <td>${analiseOP.quantidadeConsumida.toFixed(3)}</td>
                                        <td>
                                            <span class="status-badge ${saldoClass}" style="font-weight: bold;">
                                                ${analiseOP.saldoRemanescente.toFixed(3)}
                                            </span>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center;">
                                                <div style="width: 50px; height: 8px; background: #e9ecef; border-radius: 4px; margin-right: 8px;">
                                                    <div style="width: ${Math.min(100, analiseOP.eficiencia)}%; height: 100%; background: ${analiseOP.eficiencia > 100 ? '#dc3545' : analiseOP.eficiencia >= 80 ? '#28a745' : '#ffc107'}; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">${analiseOP.eficiencia}%</span>
                                            </div>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                    
                    <div style="margin-top: 15px; font-size: 12px; color: #666;">
                        <strong>Legenda:</strong>
                        <span style="margin-left: 15px;">🟢 Saldo Positivo = Material sobrando</span>
                        <span style="margin-left: 15px;">🔴 Saldo Negativo = Consumo excessivo</span>
                        <span style="margin-left: 15px;">⚪ Saldo Zero = Consumo exato</span>
                    </div>
                ` : ''}
            `;

            return div;
        }

        // Inicializar quando a página carregar
        window.addEventListener('load', carregarDados);
        window.analisarSaldos = analisarSaldos;
    </script>
</body>
</html>
