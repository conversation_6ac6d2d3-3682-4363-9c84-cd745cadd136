<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Divisão de OP para Produção Parcial</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header h1 i {
            font-size: 32px;
            color: #3498db;
        }

        .back-button {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .main-content {
            display: grid;
            gap: 30px;
            grid-template-columns: 1fr;
        }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px 30px;
            border-bottom: 3px solid #2980b9;
        }

        .card-header h2 {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-header i {
            font-size: 24px;
        }

        .card-body {
            padding: 30px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }

        .info-label {
            font-size: 14px;
            font-weight: 600;
            color: #7f8c8d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            margin: 10px 10px 10px 0;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-success:hover:not(:disabled) {
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .btn-warning:hover:not(:disabled) {
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-danger:hover:not(:disabled) {
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            margin: 20px 0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            vertical-align: middle;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transform: scale(1.01);
        }

        .table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .log-area {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.5;
            border: 1px solid #34495e;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.2);
        }

        .log-area::-webkit-scrollbar {
            width: 8px;
        }

        .log-area::-webkit-scrollbar-track {
            background: #34495e;
            border-radius: 4px;
        }

        .log-area::-webkit-scrollbar-thumb {
            background: #3498db;
            border-radius: 4px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pendente {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .status-producao {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .status-concluida {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .status-mp {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
        }

        .status-sp {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
        }

        .status-pa {
            background: linear-gradient(135deg, #1abc9c, #16a085);
            color: white;
        }

        .status-n\/a {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-left-color: #27ae60;
            color: #155724;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeeba);
            border-left-color: #f39c12;
            color: #856404;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-left-color: #e74c3c;
            color: #721c24;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border-left-color: #3498db;
            color: #0c5460;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid #3498db;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header h1 {
                font-size: 24px;
            }

            .container {
                padding: 20px 15px;
            }

            .card-body {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .table-container {
                overflow-x: auto;
            }

            .btn {
                width: 100%;
                justify-content: center;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1>
                <i class="fas fa-cut"></i>
                Divisão de OP para Produção Parcial
            </h1>
            <a href="apontamentos_simplificado.html" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Voltar aos Apontamentos
            </a>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <div class="main-content">

            <!-- OP Original Card -->
            <div class="card fade-in">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-clipboard-list"></i>
                        OP Original
                    </h2>
                </div>
                <div class="card-body">
                    <div id="opInfo" class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Status</div>
                            <div class="info-value">Aguardando seleção...</div>
                        </div>
                    </div>
                    <button class="btn" onclick="selecionarOP()">
                        <i class="fas fa-search"></i>
                        Selecionar OP
                    </button>
                </div>
            </div>

            <!-- Análise de Materiais Card -->
            <div class="card fade-in">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-boxes"></i>
                        Análise de Materiais
                    </h2>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-tag"></i> Material</th>
                                    <th><i class="fas fa-calculator"></i> Necessário</th>
                                    <th><i class="fas fa-warehouse"></i> Disponível</th>
                                    <th><i class="fas fa-industry"></i> Possível Produzir</th>
                                </tr>
                            </thead>
                            <tbody id="tbodyMateriais">
                                <tr>
                                    <td colspan="4" style="text-align: center; padding: 40px; color: #7f8c8d;">
                                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                                        Selecione uma OP para analisar os materiais...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Divisão Proposta Card -->
            <div class="card fade-in">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-cut"></i>
                        Divisão Proposta
                    </h2>
                </div>
                <div class="card-body">
                    <div id="divisaoInfo" class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Quantidade Possível</div>
                            <div class="info-value" id="qtdPossivel">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Quantidade Restante</div>
                            <div class="info-value" id="qtdRestante">-</div>
                        </div>
                    </div>
                    <button class="btn btn-success" onclick="dividirOP()" id="btnDividir" disabled>
                        <i class="fas fa-cut"></i>
                        Dividir OP
                    </button>
                </div>
            </div>

            <!-- Log de Operações Card -->
            <div class="card fade-in">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-terminal"></i>
                        Log de Operações
                    </h2>
                </div>
                <div class="card-body">
                    <div id="logArea" class="log-area"></div>
                </div>
            </div>

        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, query, where, getDocs, getDoc, doc, addDoc, updateDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let logArea = document.getElementById('logArea');
        let currentOP = null;
        let analiseProducao = null;

        // ===================================================================
        // FUNÇÃO PADRONIZADA PARA CÁLCULO DE SALDO DISPONÍVEL
        // ===================================================================

        /**
         * Calcula o saldo disponível de um estoque considerando reservas e empenhos
         * @param {Object} estoque - Objeto do estoque
         * @param {string} opId - ID da OP atual (opcional)
         * @returns {number} - Saldo disponível (nunca negativo)
         */
        function calcularSaldoDisponivel(estoque, opId = null) {
            if (!estoque) return 0;

            const saldoTotal = estoque.saldo || 0;
            const saldoReservado = estoque.saldoReservado || 0;
            const saldoEmpenhado = estoque.saldoEmpenhado || 0;

            // Se há uma OP específica e o estoque tem reserva, assumir que a reserva é para esta OP
            let saldoReservadoParaOutrasOPs = saldoReservado;

            if (opId && saldoReservado > 0) {
                // Estratégia: assumir que a reserva é para a OP atual
                // (isso resolve o problema de materiais transferidos que ficam reservados)
                saldoReservadoParaOutrasOPs = 0;
                console.log(`✅ OP ${opId} pode usar sua própria reserva de ${saldoReservado.toFixed(3)}`);
            }

            // Saldo disponível = Total - Reservado para outras OPs - Empenhado
            const saldoDisponivel = saldoTotal - saldoReservadoParaOutrasOPs - saldoEmpenhado;

            return Math.max(0, saldoDisponivel); // Nunca retornar negativo
        }

        /**
         * Versão específica para divisão de OP que considera a OP atual
         */
        function calcularSaldoDisponivelParaOP(estoque, opId) {
            return calcularSaldoDisponivel(estoque, opId);
        }

        /**
         * Função de debug para verificar saldos de materiais na divisão de OP
         */
        window.debugSaldosDivisaoOP = function() {
            if (!currentOP) {
                console.error('❌ Nenhuma OP selecionada');
                return;
            }

            console.log(`🔍 DEBUG SALDOS - OP ${currentOP.numero}`);
            console.log(`📋 Quantidade Original: ${currentOP.quantidade}`);

            if (analiseProducao) {
                console.log(`📊 Quantidade Máxima Possível: ${analiseProducao.quantidadeMaximaPossivel}`);

                analiseProducao.materiaisAnalise.forEach((analise, index) => {
                    console.log(`\n📦 Material ${index + 1}:`);
                    console.log(`   🏷️ Código: ${analise.material.codigo || analise.material.produtoId}`);
                    console.log(`   📏 Necessário: ${analise.material.quantidade}`);
                    console.log(`   ✅ Disponível: ${analise.saldoDisponivel.toFixed(3)}`);
                    console.log(`   🔢 Peças Possíveis: ${analise.pecasPossiveis}`);
                });
            } else {
                console.log('❌ Análise de produção não realizada ainda');
            }
        };

        /**
         * Função de debug para listar todas as OPs disponíveis
         */
        window.listarOPsDisponiveis = async function() {
            try {
                log('🔍 Listando todas as OPs disponíveis...');
                const todasOPs = await getDocs(collection(db, "ordensProducao"));

                console.log(`📋 Total de OPs encontradas: ${todasOPs.docs.length}`);

                todasOPs.docs.forEach((doc, index) => {
                    const op = doc.data();
                    console.log(`${index + 1}. ID: ${doc.id} | Número: ${op.numero} | Status: ${op.status} | Produto: ${op.produtoId}`);
                });

                log(`✅ Listagem concluída: ${todasOPs.docs.length} OPs encontradas`);
            } catch (error) {
                log(`❌ Erro ao listar OPs: ${error.message}`, 'error');
            }
        };

        /**
         * Função para verificar e corrigir cálculos
         */
        window.verificarCalculos = function() {
            if (!currentOP || !analiseProducao) {
                console.error('❌ OP ou análise não disponível');
                return;
            }

            console.log('🧮 VERIFICAÇÃO DE CÁLCULOS:');
            console.log(`📋 OP: ${currentOP.numero} (${currentOP.quantidade} peças)`);

            analiseProducao.materiaisAnalise.forEach((analise, index) => {
                const quantidadePorPeca = analise.material.quantidade / currentOP.quantidade;

                // Usar a mesma lógica de arredondamento inteligente
                const divisaoExata = analise.saldoDisponivel / quantidadePorPeca;
                const parteDecimal = divisaoExata - Math.floor(divisaoExata);
                const pecasCalculadas = parteDecimal >= 0.95 ? Math.ceil(divisaoExata) : Math.floor(divisaoExata);

                console.log(`\n📦 Material ${index + 1}:`);
                console.log(`   🏷️ Código: ${analise.material.codigo || analise.material.produtoId}`);
                console.log(`   📏 Total Necessário: ${analise.material.quantidade.toFixed(3)}`);
                console.log(`   📐 Por Peça: ${quantidadePorPeca.toFixed(3)}`);
                console.log(`   ✅ Disponível: ${analise.saldoDisponivel.toFixed(3)}`);
                console.log(`   🧮 Cálculo: ${analise.saldoDisponivel.toFixed(3)} ÷ ${quantidadePorPeca.toFixed(3)} = ${(analise.saldoDisponivel / quantidadePorPeca).toFixed(3)}`);
                console.log(`   🏭 Peças Sistema: ${analise.pecasPossiveis}`);
                console.log(`   🔄 Peças Recalculadas: ${pecasCalculadas}`);

                if (analise.pecasPossiveis !== pecasCalculadas) {
                    console.warn(`   ⚠️ DIVERGÊNCIA DETECTADA!`);
                }
            });
        };

        /**
         * Função para testar diferentes thresholds de arredondamento
         */
        window.testarArredondamento = function(threshold = 0.95) {
            if (!currentOP || !analiseProducao) {
                console.error('❌ OP ou análise não disponível');
                return;
            }

            console.log(`🧪 TESTE DE ARREDONDAMENTO (threshold: ${threshold}):`);

            analiseProducao.materiaisAnalise.forEach((analise, index) => {
                const quantidadePorPeca = analise.material.quantidade / currentOP.quantidade;
                const divisaoExata = analise.saldoDisponivel / quantidadePorPeca;
                const parteDecimal = divisaoExata - Math.floor(divisaoExata);

                const pecasFloor = Math.floor(divisaoExata);
                const pecasCeil = Math.ceil(divisaoExata);
                const pecasInteligente = parteDecimal >= threshold ? pecasCeil : pecasFloor;

                console.log(`\n📦 Material ${index + 1}:`);
                console.log(`   🧮 Divisão Exata: ${divisaoExata.toFixed(6)}`);
                console.log(`   📊 Parte Decimal: ${parteDecimal.toFixed(6)}`);
                console.log(`   ⬇️ Math.floor: ${pecasFloor}`);
                console.log(`   ⬆️ Math.ceil: ${pecasCeil}`);
                console.log(`   🎯 Inteligente (${threshold}): ${pecasInteligente}`);
                console.log(`   💡 Recomendação: ${parteDecimal >= 0.95 ? 'ARREDONDAR PARA CIMA' : 'MANTER FLOOR'}`);
            });
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warn' ? '⚠️' : '🔄';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        window.selecionarOP = async function() {
            try {
                // Verificar se veio da URL
                const params = new URLSearchParams(window.location.search);
                let numeroOP = params.get('op');
                
                // Se não veio da URL, pedir ao usuário
                if (!numeroOP) {
                    numeroOP = prompt('Digite o número da OP:');
                    if (!numeroOP) return;
                }

                log(`Buscando OP ${numeroOP}...`);

                // Primeiro, tentar buscar por número
                let opQuery = query(collection(db, "ordensProducao"), where("numero", "==", numeroOP));
                let opSnap = await getDocs(opQuery);

                // Se não encontrou por número, tentar buscar por ID
                if (opSnap.empty) {
                    log(`OP não encontrada por número, tentando buscar por ID...`);
                    try {
                        const opDoc = await getDoc(doc(db, "ordensProducao", numeroOP));
                        if (opDoc.exists()) {
                            // Criar um snapshot fake para manter compatibilidade
                            opSnap = {
                                empty: false,
                                docs: [{ id: opDoc.id, data: () => opDoc.data() }]
                            };
                        }
                    } catch (error) {
                        log(`Erro ao buscar por ID: ${error.message}`, 'warn');
                    }
                }

                // Se ainda não encontrou, tentar buscar por número como string
                if (opSnap.empty) {
                    log(`Tentando buscar por número como string...`);
                    opQuery = query(collection(db, "ordensProducao"), where("numero", "==", numeroOP.toString()));
                    opSnap = await getDocs(opQuery);
                }

                if (opSnap.empty) {
                    throw new Error(`OP ${numeroOP} não encontrada! Verifique se o número está correto.`);
                }

                currentOP = { id: opSnap.docs[0].id, ...opSnap.docs[0].data() };
                log(`✅ OP encontrada: ${currentOP.numero}`);

                // Analisar materiais
                await analisarMateriais();
                
            } catch (error) {
                log(`Erro ao buscar OP: ${error.message}`, 'error');
            }
        };

        async function analisarMateriais() {
            try {
                if (!currentOP || !currentOP.materiaisNecessarios) {
                    throw new Error('OP inválida ou sem materiais');
                }

                log('Analisando disponibilidade de materiais...');

                const materiaisAnalise = [];
                let quantidadeMinimaPossivel = currentOP.quantidade;

                for (const material of currentOP.materiaisNecessarios) {
                    const estoque = estoques.find(e => 
                        e.produtoId === material.produtoId && 
                        e.armazemId === currentOP.armazemProducaoId
                    );

                    // CORREÇÃO: Usar função que considera reservas da própria OP
                    const saldoDisponivel = calcularSaldoDisponivelParaOP(estoque, currentOP.id);

                    const quantidadePorPeca = material.quantidade / currentOP.quantidade;

                    // CORREÇÃO: Arredondamento inteligente para casos próximos ao inteiro
                    const divisaoExata = saldoDisponivel / quantidadePorPeca;
                    const parteDecimal = divisaoExata - Math.floor(divisaoExata);

                    // Se a parte decimal é >= 0.95, arredondar para cima
                    // Isso resolve casos como 1.999 que deveriam ser 2
                    let pecasPossiveis;
                    if (parteDecimal >= 0.95) {
                        pecasPossiveis = Math.ceil(divisaoExata);
                        log(`🔄 Arredondamento inteligente: ${divisaoExata.toFixed(3)} → ${pecasPossiveis} (decimal: ${parteDecimal.toFixed(3)})`);
                    } else {
                        pecasPossiveis = Math.floor(divisaoExata);
                    }

                    // Log detalhado para debug
                    const produto = window.produtos?.find(p => p.id === material.produtoId) || {};
                    const materialCodigo = produto.codigo || material.codigo || material.produtoId;
                    const materialDescricao = produto.descricao || 'Descrição não encontrada';

                    if (estoque) {
                        log(`📦 ${materialCodigo} - ${materialDescricao}:`);
                        log(`   💰 Saldo Total: ${(estoque.saldo || 0).toFixed(3)}`);
                        log(`   🔒 Reservado: ${(estoque.saldoReservado || 0).toFixed(3)}`);
                        log(`   ✅ Disponível: ${saldoDisponivel.toFixed(3)}`);
                        log(`   📏 Necessário Total: ${material.quantidade.toFixed(3)}`);
                        log(`   🔢 OP Quantidade: ${currentOP.quantidade}`);
                        log(`   📐 Por Peça: ${quantidadePorPeca.toFixed(3)}`);
                        log(`   🎯 Cálculo: ${saldoDisponivel.toFixed(3)} ÷ ${quantidadePorPeca.toFixed(3)} = ${(saldoDisponivel / quantidadePorPeca).toFixed(3)}`);
                        log(`   🏭 Peças Possíveis: ${pecasPossiveis}`);
                    } else {
                        log(`❌ ${materialCodigo} - ${materialDescricao}: Sem estoque no armazém de produção`);
                    }

                    materiaisAnalise.push({
                        material,
                        saldoDisponivel,
                        pecasPossiveis
                    });

                    if (pecasPossiveis < quantidadeMinimaPossivel) {
                        quantidadeMinimaPossivel = pecasPossiveis;
                    }
                }

                analiseProducao = {
                    materiaisAnalise,
                    quantidadePossivel: quantidadeMinimaPossivel,
                    quantidadeRestante: currentOP.quantidade - quantidadeMinimaPossivel
                };

                atualizarInterface();
                
            } catch (error) {
                log(`Erro na análise: ${error.message}`, 'error');
            }
        }

        async function atualizarInterface() {
            // Atualizar informações da OP
            const opInfo = document.getElementById('opInfo');
            if (currentOP) {
                const statusClass = currentOP.status === 'Concluída' ? 'status-concluida' :
                                  currentOP.status === 'Em Produção' ? 'status-producao' : 'status-pendente';

                opInfo.innerHTML = `
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-hashtag"></i> Número
                        </div>
                        <div class="info-value">${currentOP.numero}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-calculator"></i> Quantidade
                        </div>
                        <div class="info-value">${currentOP.quantidade} peças</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-info-circle"></i> Status
                        </div>
                        <div class="info-value">
                            <span class="status-badge ${statusClass}">
                                <i class="fas fa-circle"></i>
                                ${currentOP.status || 'Pendente'}
                            </span>
                        </div>
                    </div>
                `;
            }

            // Atualizar tabela de materiais
            const tbody = document.getElementById('tbodyMateriais');
            if (analiseProducao) {
                tbody.innerHTML = '';
                analiseProducao.materiaisAnalise.forEach(analise => {
                    const row = document.createElement('tr');
                    const quantidadePorPeca = analise.material.quantidade / currentOP.quantidade;

                    // Buscar informações do produto
                    const produto = window.produtos?.find(p => p.id === analise.material.produtoId) || {};
                    const codigoProduto = produto.codigo || analise.material.codigo || analise.material.produtoId;
                    const descricaoProduto = produto.descricao || 'Descrição não encontrada';
                    const tipoProduto = produto.tipo || 'N/A';

                    // Determinar cor baseada na disponibilidade
                    const percentualDisponivel = (analise.saldoDisponivel / analise.material.quantidade) * 100;
                    let statusIcon = '';
                    let statusColor = '';

                    if (analise.pecasPossiveis >= currentOP.quantidade) {
                        statusIcon = '<i class="fas fa-check-circle" style="color: #27ae60;"></i>';
                        statusColor = 'color: #27ae60; font-weight: 600;';
                    } else if (analise.pecasPossiveis > 0) {
                        statusIcon = '<i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>';
                        statusColor = 'color: #f39c12; font-weight: 600;';
                    } else {
                        statusIcon = '<i class="fas fa-times-circle" style="color: #e74c3c;"></i>';
                        statusColor = 'color: #e74c3c; font-weight: 600;';
                    }

                    row.innerHTML = `
                        <td>
                            <div style="display: flex; flex-direction: column; gap: 4px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-cube" style="color: #3498db;"></i>
                                    <span style="font-family: monospace; font-weight: 700; font-size: 14px; color: #2c3e50;">
                                        ${codigoProduto}
                                    </span>
                                    <span class="status-badge status-${tipoProduto.toLowerCase()}" style="font-size: 10px; padding: 2px 6px;">
                                        ${tipoProduto}
                                    </span>
                                </div>
                                <div style="font-size: 12px; color: #7f8c8d; margin-left: 24px;">
                                    ${descricaoProduto.length > 50 ? descricaoProduto.substring(0, 50) + '...' : descricaoProduto}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${analise.material.quantidade.toFixed(3)}</strong>
                                <br>
                                <small style="color: #7f8c8d;">
                                    <i class="fas fa-calculator"></i> ${quantidadePorPeca.toFixed(3)}/peça
                                </small>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-warehouse" style="color: #3498db;"></i>
                                <strong>${analise.saldoDisponivel.toFixed(3)}</strong>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px; ${statusColor}">
                                ${statusIcon}
                                <strong>${analise.pecasPossiveis} peças</strong>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                // Atualizar informações de divisão
                document.getElementById('qtdPossivel').textContent = analiseProducao.quantidadePossivel;
                document.getElementById('qtdRestante').textContent = analiseProducao.quantidadeRestante;

                // Habilitar/desabilitar botão de divisão
                document.getElementById('btnDividir').disabled = analiseProducao.quantidadePossivel <= 0;
            }
        }

        window.dividirOP = async function() {
            try {
                if (!currentOP || !analiseProducao) {
                    throw new Error('Selecione uma OP primeiro');
                }

                if (analiseProducao.quantidadePossivel <= 0) {
                    throw new Error('Não é possível produzir nenhuma peça');
                }

                log('Iniciando divisão da OP...');

                // 1. Criar nova OP para quantidade restante
                const novaOP = {
                    ...currentOP,
                    numero: `${currentOP.numero}-B`,
                    quantidade: analiseProducao.quantidadeRestante,
                    opOriginalId: currentOP.id,
                    status: 'Pendente',
                    dataCriacao: Timestamp.now(),
                    materiaisNecessarios: currentOP.materiaisNecessarios.map(m => ({
                        ...m,
                        quantidade: (m.quantidade / currentOP.quantidade) * analiseProducao.quantidadeRestante
                    }))
                };
                delete novaOP.id;

                const novaOPRef = await addDoc(collection(db, "ordensProducao"), novaOP);
                log(`✅ Nova OP criada: ${novaOP.numero}`);

                // 2. Atualizar OP original
                await updateDoc(doc(db, "ordensProducao", currentOP.id), {
                    quantidade: analiseProducao.quantidadePossivel,
                    opVinculadaId: novaOPRef.id,
                    materiaisNecessarios: currentOP.materiaisNecessarios.map(m => ({
                        ...m,
                        quantidade: (m.quantidade / currentOP.quantidade) * analiseProducao.quantidadePossivel
                    })),
                    ultimaAtualizacao: Timestamp.now()
                });
                log(`✅ OP original atualizada: ${currentOP.numero}`);

                // 3. Registrar vínculo
                await addDoc(collection(db, "vinculos_op"), {
                    opOriginalId: currentOP.id,
                    opOriginalNumero: currentOP.numero,
                    opNovaId: novaOPRef.id,
                    opNovaNumero: novaOP.numero,
                    tipo: 'DIVISAO_PARCIAL',
                    dataCriacao: Timestamp.now(),
                    quantidadeOriginal: currentOP.quantidade,
                    quantidadeParcial: analiseProducao.quantidadePossivel,
                    quantidadeRestante: analiseProducao.quantidadeRestante,
                    motivoDivisao: 'PRODUCAO_PARCIAL_MATERIAL'
                });

                log('✅ Divisão concluída com sucesso!', 'success');
                alert(`✅ OP dividida com sucesso!\n\nOP Parcial: ${currentOP.numero} (${analiseProducao.quantidadePossivel} pçs)\nOP Restante: ${novaOP.numero} (${analiseProducao.quantidadeRestante} pçs)`);

                // Limpar dados
                currentOP = null;
                analiseProducao = null;
                atualizarInterface();
                
            } catch (error) {
                log(`Erro ao dividir OP: ${error.message}`, 'error');
                alert('Erro ao dividir OP: ' + error.message);
            }
        };

                // Carregar dados iniciais
        window.addEventListener('load', async function() {
            try {
                log('🔄 Carregando dados...');
                
                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                window.produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Carregar estoques
                const estoquesSnap = await getDocs(collection(db, "estoques"));
                window.estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Carregar armazéns
                const armazensSnap = await getDocs(collection(db, "armazens"));
                window.armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                log('✅ Dados carregados com sucesso!');

                // Verificar se tem OP na URL e carregar automaticamente
                const params = new URLSearchParams(window.location.search);
                const numeroOP = params.get('op');
                if (numeroOP) {
                    log(`OP ${numeroOP} detectada na URL, carregando automaticamente...`);
                    setTimeout(() => selecionarOP(), 500);
                }
                
            } catch (error) {
                log(`Erro ao carregar dados: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html> 