<div class="sidebar">
  <img src="https://www.naliteck.com.br/img/logo.png" alt="Naliteck Logo" class="logo">
  <div class="user-info">
      <div>Usuário: <span class="username" id="currentUserName">-</span></div>
      <div>Nível: <span id="currentUserLevel">-</span></div>
      <button class="logout-btn" onclick="logout()">Sair</button>
  </div>

  <ul class="nav-list">
      <li class="section-title">Financeiro</li>
      <li><button class="menu-button financial" data-level="2" data-permission="contas_pagar" onclick="abrirTela('contasPagar')">Contas a Pagar</button></li>
      <li><button class="menu-button financial" data-level="2" data-permission="contas_receber" onclick="abrirTela('contasReceber')">Contas a Receber</button></li>
      <li><button class="menu-button financial" data-level="2" data-permission="condicoes_pagamento" onclick="abrirTela('condicoesPagamento')">Condições de Pagamento</button></li>
      <li><button class="menu-button financial" data-level="2" data-permission="fluxo_caixa" onclick="abrirTela('fluxoCaixa')">Fluxo de Caixa</button></li>

      <li class="section-title">Compras</li>
      <li><button class="menu-button" data-level="2" data-permission="solicitacao_compras" onclick="abrirTela('solicitacaoCompras')">Solicitação de Compras</button></li>
      <li><button class="menu-button" data-level="2" data-permission="cotacoes" onclick="abrirTela('cotacoes')">Cotações</button></li>
      <li><button class="menu-button" data-level="2" data-permission="pedidos_compra" onclick="abrirTela('pedidosCompra')">Pedidos de Compra</button></li>

      <li class="section-title">Cadastros</li>
      <li><button class="menu-button" data-level="1" data-permission="centralDocumentos" onclick="abrirTela('centralDocumentos')">Central de Documentos</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_clientes" onclick="abrirTela('cadastroClientes')">Cadastro de Clientes</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_produtos" onclick="abrirTela('cadastroProduto')">Cadastro de Produtos</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_operacoes" onclick="abrirTela('cadastroOperacoes')">Cadastro de Operações</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_recursos" onclick="abrirTela('cadastroRecursos')">Cadastro de Recursos</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_fornecedores" onclick="abrirTela('cadastroFornecedores')">Cadastro de Fornecedores</button></li>
      <li><button class="menu-button" data-level="1" data-permission="estrutura_produtos" onclick="abrirTela('estruturaProdutos')">Estrutura de Produtos</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_grupo" onclick="abrirTela('cadastroGrupo')">Cadastro de Grupo</button></li>
      <li><button class="menu-button" data-level="1" data-permission="cadastro_familia" onclick="abrirTela('cadastroFamilia')">Cadastro de Família</button></li>
      <li><button class="menu-button" data-level="7" data-permission="cadastro_usuarios" onclick="abrirTela('cadastroUsuarios')">Cadastro de Usuários</button></li>
      <li><button class="menu-button" data-level="3" data-permission="produtos_fornecedores" onclick="abrirTela('produtosFornecedores')">Produtos x Fornecedores</button></li>

      <li class="section-title">Vendas</li>
      <li><button class="menu-button" data-level="3" data-permission="pedidos_venda" onclick="abrirTela('pedidosVenda')">Pedidos de Venda</button></li>
      <li><button class="menu-button" data-level="3" data-permission="orcamento_venda" onclick="abrirTela('orcamentoVenda')">Orçamento de Venda</button></li>
      <li><button class="menu-button" data-level="3" data-permission="relatorio_vendas" onclick="abrirTela('relatorioVendas')">Relatórios de Vendas</button></li>

      <li class="section-title">Produção</li>
      <li><button class="menu-button" data-level="3" data-permission="ordens_producao" onclick="abrirTela('ordensProducao')">Ordens de Produção</button></li>
      <li><button class="menu-button" data-level="3" data-permission="op_preview" onclick="abrirTela('op_preview')">Simular Ordem de Produção</button></li>
      <li><button class="menu-button" data-level="3" data-permission="apontamentos" onclick="abrirTela('apontamentos')">Registrar Apontamentos</button></li>
      <li><button class="menu-button" data-level="3" data-permission="estoques" onclick="abrirTela('estoques')">Controle de Estoques</button></li>

      <li class="section-title">Qualidade</li>
      <li><button class="menu-button" data-level="3" data-permission="especificacoes_produtos" onclick="abrirTela('especificacoesProdutos')">Especificações de Produtos</button></li>
      <li><button class="menu-button" data-level="3" data-permission="homologacao_fornecedores" onclick="abrirTela('homologacaoFornecedores')">Homologação de Fornecedores</button></li>
      <li><button class="menu-button" data-level="3" data-permission="recebimento_materiais" onclick="abrirTela('recebimentoMateriais')">Recebimento de Materiais</button></li>
      <li><button class="menu-button" data-level="3" data-permission="estoque_qualidade" onclick="abrirTela('inspecaoQualidade')">Inspeção Qualidade</button></li>

      <li class="section-title">Relatórios</li>
      <li><button class="menu-button" data-level="2" data-permission="relatorio_mrp_compras" onclick="abrirTela('relatorioMrpCompras')">Relatório MRP e Compras</button></li>
      <li><button class="menu-button" data-level="3" data-permission="relatorio_estrutura" onclick="abrirTela('relatorioEstrutura')">Relatório de Estrutura</button></li>
      <li><button class="menu-button" data-level="3" data-permission="relatorio_op" onclick="abrirTela('relatorioOp')">Relatório de OP</button></li>
      <li><button class="menu-button" data-level="3" data-permission="relatorio_op_sap" onclick="abrirTela('relatorioOpSap')">Relatório de OP (SAP)</button></li>
      <li><button class="menu-button" data-level="3" data-permission="relatorio_ordens" onclick="abrirTela('relatorioOrdens')">Relatório de Ordens</button></li>
      <li><button class="menu-button" data-level="1" data-permission="relatorio_onde_usado" onclick="abrirTela('relatorioOndeUsado')">Onde é Usado</button></li>
      <li><button class="menu-button" data-level="1" data-permission="relatorio_copia_estrutura" onclick="abrirTela('relatorioCopiaEstrutura')">Cópia de Estrutura</button></li>
      <li><button class="menu-button" data-level="1" data-permission="exportar_estrutura" onclick="abrirTela('exportar_estrutura')">Exportar todas estruturas</button></li>
      <li><button class="menu-button" data-level="2" data-permission="relatorio_custo" onclick="abrirTela('relatorioCusto')">Relatório de Custo</button></li>

      <li class="section-title">Configurações</li>
      <li><button class="menu-button settings" data-level="7" data-permission="config_empresa" onclick="abrirTela('configEmpresa')">Dados da Empresa</button></li>
      <li><button class="menu-button settings" data-level="7" data-permission="config_parametros" onclick="abrirTela('configParametros')">Parâmetros do Sistema</button></li>
      <li><button class="menu-button settings" data-level="7" data-permission="backup_sistema" onclick="abrirTela('configBackup')">Backup e Restauração</button></li>
      <li><button class="menu-button settings" data-level="7" data-permission="config_logs" onclick="abrirTela('configLogs')">Logs do Sistema</button></li>
  </ul>
</div>