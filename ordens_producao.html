<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão de Ordens de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    /* Estilos para configurações de produção */
    .config-panel {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
    }

    .config-panel::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="config-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23config-pattern)"/></svg>');
      opacity: 0.3;
    }

    .config-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      z-index: 1;
    }

    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      position: relative;
      z-index: 1;
    }

    .config-item {
      background: rgba(255,255,255,0.15);
      padding: 20px;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
      transition: all 0.3s ease;
    }

    .config-item:hover {
      background: rgba(255,255,255,0.2);
      transform: translateY(-2px);
    }

    .config-label {
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }

    .config-status {
      font-size: 12px;
      opacity: 0.9;
      margin-top: 5px;
    }

    .config-status.active {
      color: #4CAF50;
    }

    .config-status.inactive {
      color: #FF9800;
    }

    .refresh-config-btn {
      background: rgba(255,255,255,0.2);
      border: 2px solid rgba(255,255,255,0.3);
      color: white;
      padding: 10px 20px;
      border-radius: 25px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;
      z-index: 1;
    }

    .refresh-config-btn:hover {
      background: rgba(255,255,255,0.3);
      border-color: rgba(255,255,255,0.5);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: bold;
    }

    input, select {
      width: 100%;
      padding: 12px;
      border: 2px solid #bdc3c7;
      border-radius: 8px;
      margin-bottom: 10px;
      transition: border-color 0.3s ease;
    }

    input:focus, select:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    button {
      background-color: var(--success-color);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }

    button:hover {
      background-color: var(--success-hover);
    }

    .button-group {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .secondary-button {
      background-color: var(--secondary-color);
      color: var(--text-color);
    }

    .secondary-button:hover {
      background-color: #e0e3e6;
    }

    .danger-button {
      background-color: var(--danger-color);
    }

    .danger-button:hover {
      background-color: var(--danger-hover);
    }

    .back-button {
      background-color: #6c757d;
    }

    .back-button:hover {
      background-color: #5a6268;
    }

    .warning-button {
      background-color: var(--warning-color);
    }

    .warning-button:hover {
      background-color: #d2620a;
    }

    .op-table-container {
      margin-top: 20px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
    }

    .op-table {
      width: 100%;
      border-collapse: collapse;
      background-color: #fff;
    }

    .op-table th, .op-table td {
      padding: 18px 15px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }

    .op-table td {
      font-size: 14px;
    }

    .op-table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      cursor: pointer;
      position: relative;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 3px solid #2c3e50;
    }

    .op-table th:hover {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .op-table th .sort-icon {
      display: inline-block;
      margin-left: 5px;
      font-size: 12px;
    }

    .op-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
      transition: all 0.2s ease;
    }

    .op-table .actions {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .op-table .actions button {
      padding: 6px 10px;
      font-size: 14px;
    }

    .legend {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      font-size: 14px;
    }

    .legend-color {
      width: 20px;
      height: 20px;
      margin-right: 5px;
      border-radius: 3px;
    }

    .status-pendente {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    .status-firme {
      background: #e6f3ff;
      color: #0056b3;
      border: 1px solid #b3d7ff;
      font-weight: bold;
    }
    .status-em-producao {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }
    .status-concluida {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status-cancelada {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-align: center;
      min-width: 100px;
    }

    .batch-actions {
      margin-bottom: 15px;
      display: flex;
      gap: 10px;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
      padding: 20px;
      overflow-y: auto;
    }

    .modal-content {
      background-color: white;
      margin: 2% auto;
      padding: 0;
      width: 90%;
      max-width: 1000px;
      border-radius: 8px;
      position: relative;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    }

    .modal-header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      position: sticky;
      top: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-body {
      padding: 20px;
      overflow-y: auto;
      flex: 1;
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--border-color);
      background: white;
      position: sticky;
      bottom: 0;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .tab-container {
      margin-bottom: 20px;
    }

    .tab-buttons {
      display: flex;
      border-bottom: 3px solid #ecf0f1;
      margin-bottom: 30px;
      overflow-x: auto;
    }

    .tab-button {
      padding: 15px 25px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-weight: 600;
      color: #7f8c8d;
      border-radius: 8px 8px 0 0;
      margin-right: 5px;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .tab-button.active {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      transform: translateY(-2px);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .filter-section {
      margin-bottom: 25px;
      padding: 25px;
      background: #f8f9fa;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .search-input {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .filter-options {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .filter-group {
      flex: 1;
      min-width: 200px;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      overflow: hidden;
      margin-top: 5px;
    }

    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      transition: width 0.3s ease;
    }

    .opcionais-list {
      margin: 20px 0;
    }

    .opcional-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 10px;
      background-color: #fff;
    }

    .opcional-info {
      flex: 1;
    }

    .opcional-produto {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .opcional-saldo {
      font-size: 13px;
      color: var(--text-secondary);
      margin-top: 4px;
    }

    .opcional-saldo.baixo {
      color: var(--danger-color);
    }

    .opcional-saldo.ok {
      color: var(--success-color);
    }

    .opcional-radio {
      margin-right: 15px;
    }

    .alert {
      padding: 12px 15px;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    .alert-info {
      background-color: #e8f4fd;
      border: 1px solid #b8daff;
      color: #004085;
    }

    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .production-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
      }

      .production-actions {
        margin-left: 0;
      }

      .form-row {
        flex-direction: column;
      }
      .op-table th, .op-table td {
        padding: 8px;
        font-size: 14px;
      }
      .op-table .actions {
        flex-direction: column;
        gap: 5px;
      }
      .modal-content {
        width: 95%;
        margin: 5% auto;
      }
      .filter-options {
        flex-direction: column;
      }
      .filter-group {
        min-width: 100%;
      }
      .legend {
        flex-direction: column;
        gap: 10px;
      }
      .tab-buttons {
        flex-wrap: wrap;
      }
    }

    @page {
      size: A4 portrait;
      margin: 15mm;
    }

    @media print {
      body {
        margin: 0;
        padding: 0;
      }
      .no-print {
        display: none;
      }
      .op-print-view {
        page-break-after: always;
        padding: 0 !important;
        margin: 0 !important;
        box-shadow: none !important;
      }
      .op-print-view:last-child {
        page-break-after: avoid;
      }
    }

    .op-print-view {
      display: none;
      background-color: white;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .op-print-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
      border-bottom: 1px solid #000;
      padding-bottom: 5px;
    }

    .op-print-title {
      text-align: center;
      flex-grow: 1;
      margin: 0 10px;
    }

    .op-print-title h1 {
      margin: 0;
      font-size: 18px;
    }

    .op-print-title h2 {
      margin: 3px 0;
      font-size: 16px;
    }

    .op-print-info {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 5px;
      margin-bottom: 15px;
      border: 1px solid #ccc;
      padding: 5px;
    }

    .op-print-info-item {
      border: 1px solid #ddd;
      padding: 3px;
    }

    .op-print-info-item strong {
      display: block;
      font-size: 8px;
      color: #666;
    }

    .op-print-info-item span {
      display: block;
      font-size: 12px;
      margin-top: 1px;
    }

    .op-print-section {
      margin-bottom: 15px;
    }

    .op-print-section-title {
      background-color: #f0f0f0;
      padding: 3px 8px;
      font-weight: bold;
      border: 1px solid #ccc;
      font-size: 11px;
    }

    .op-print-signatures {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
    }

    .op-print-signature {
      flex: 1;
      margin: 0 10px;
      text-align: center;
    }

    .op-print-signature-line {
      border-top: 1px solid #000;
      margin-top: 40px;
      padding-top: 5px;
    }

    /* Estilos do Banner de Produção */
    .production-banner {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;
      padding: 20px 25px;
      position: relative;
      overflow: hidden;
    }

    .production-banner::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="prod-pattern" width="15" height="15" patternUnits="userSpaceOnUse"><circle cx="7.5" cy="7.5" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23prod-pattern)"/></svg>');
      opacity: 0.3;
    }

    .production-content {
      display: flex;
      align-items: center;
      gap: 20px;
      position: relative;
      z-index: 1;
    }

    .production-icon {
      font-size: 32px;
      color: #ffd700;
    }

    .production-text h3 {
      margin: 0 0 5px 0;
      font-size: 18px;
      font-weight: 700;
    }

    .production-text p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }

    .production-actions {
      margin-left: auto;
      display: flex;
      gap: 10px;
    }

    .btn-production-demo {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
      padding: 10px 20px;
      border-radius: 25px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .btn-production-demo:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .production-label {
      background: #28a745;
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: 700;
      margin-left: 10px;
      animation: glow 2s infinite;
    }

    @keyframes glow {
      0% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
      50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8); }
      100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Banner do Sistema Real -->
    <div class="production-banner">
      <div class="production-content">
        <div class="production-icon">
          <i class="fas fa-cogs"></i>
        </div>
        <div class="production-text">
          <h3>🏭 SISTEMA DE PRODUÇÃO</h3>
          <p>Ambiente real com dados persistentes e integração completa para gestão de ordens de produção.</p>
        </div>
        <div class="production-actions">
          <button class="btn-production-demo" onclick="window.location.href='gantt_nativo.html'">
            <i class="fas fa-chart-gantt"></i>
            Ver Gantt
          </button>
        </div>
      </div>
    </div>

    <div class="header">
      <h1>
        <i class="fas fa-industry"></i>
        Gestão de Ordens de Produção <span class="production-label">REAL</span>
      </h1>
      <div class="header-actions">
        <button id="newOrderButton" class="btn btn-success"><i class="fas fa-plus"></i> Nova Ordem Manual</button>
        <button id="ganttChartButton" class="btn btn-primary" onclick="window.location.href='gantt_nativo.html'"><i class="fas fa-chart-gantt"></i> Visualizar Gantt</button>
        <button id="orderFromSalesButton" class="btn btn-info"><i class="fas fa-file-alt"></i> Ordem de Pedido</button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'"><i class="fas fa-home"></i> Voltar</button>
      </div>
    </div>

    <div class="main-content" style="padding: 30px;">
      <!-- Painel de Configurações de Produção -->
    <div class="config-panel">
      <div class="config-title">
        <i class="fas fa-cog"></i>
        Configurações de Produção Ativas
        <button class="refresh-config-btn" onclick="loadProductionConfig()">
          <i class="fas fa-sync-alt"></i> Atualizar
        </button>
        <button class="refresh-config-btn" onclick="testarValidacaoEstoque()" style="background: #28a745; margin-left: 10px;">
          <i class="fas fa-vial"></i> Testar Validação
        </button>
        <button class="refresh-config-btn" onclick="testarVerificacaoSPs()" style="background: #6f42c1; margin-left: 10px;">
          <i class="fas fa-cogs"></i> Testar SPs
        </button>
        <button class="refresh-config-btn" onclick="relatorioAntiDuplicacao()" style="background: #17a2b8; margin-left: 10px;">
          <i class="fas fa-copy"></i> Anti-Duplicação
        </button>
        <button class="refresh-config-btn" onclick="testarModalRolagem()" style="background: #fd7e14; margin-left: 10px;">
          <i class="fas fa-scroll"></i> Testar Modal
        </button>

      </div>
      <div class="config-grid">
        <div class="config-item">
          <span class="config-label">Aglutinação de OPs</span>
          <div class="config-status" id="statusAglutinacao">Carregando...</div>
        </div>
        <div class="config-item">
          <span class="config-label">Uso de Saldo de Estoque</span>
          <div class="config-status" id="statusSaldoEstoque">Carregando...</div>
        </div>
        <div class="config-item">
          <span class="config-label">OPs Firmes por Padrão</span>
          <div class="config-status" id="statusOPsFirmes">Carregando...</div>
        </div>
        <div class="config-item">
          <span class="config-label">Reserva Automática de Estoque</span>
          <div class="config-status" id="statusReservaEstoque">Carregando...</div>
        </div>
      </div>
    </div>
    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" onclick="switchTab('active')">Ordens Ativas</button>
        <button class="tab-button" onclick="switchTab('completed')">Ordens Concluídas</button>
      </div>

      <div class="filter-section">
        <div style="display: flex; align-items: center; gap: 0; margin-bottom: 10px;">
          <input type="text" class="search-input" id="generalSearch" placeholder="Buscar por número da ordem, código ou descrição do produto..." style="flex:1; border-top-right-radius: 0; border-bottom-right-radius: 0;" oninput="filterOrders()">
          <button id="btnBuscarCodigo" style="height: 38px; border-top-left-radius: 0; border-bottom-left-radius: 0; margin-left: -1px; background: var(--primary-color); color: #fff; font-weight: 500; border: 1px solid #ddd; border-left: none;" onclick="buscarPorCodigoProduto()">Buscar</button>
        </div>
        <div class="filter-options">
          <div class="filter-group">
            <label>Código do Produto:</label>
            <input type="text" id="productCodeFilter" placeholder="Digite o código..." oninput="filterOrders()">
          </div>
          <div class="filter-group">
            <label>Descrição do Produto:</label>
            <input type="text" id="productDescriptionFilter" placeholder="Digite a descrição..." oninput="filterOrders()">
          </div>
          <div class="filter-group">
            <label for="productTypeFilter">Tipo do Produto:</label>
            <select id="productTypeFilter" title="Filtrar por tipo de produto" onchange="filterOrders()">
              <option value="">Todos</option>
              <option value="PA">Produto Acabado (PA)</option>
              <option value="SP">Subproduto (SP)</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="costCenterFilter">Centro de Custo:</label>
            <select id="costCenterFilter" title="Filtrar por centro de custo" onchange="filterOrders()">
              <option value="">Todos</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="warehouseFilter">Armazém de Produção:</label>
            <select id="warehouseFilter" title="Filtrar por armazém de produção" onchange="filterOrders()">
              <option value="">Todos</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="statusFilter">Status:</label>
            <select id="statusFilter" title="Filtrar por status da ordem" onchange="filterOrders()">
              <option value="">Todos</option>
              <option value="pendente">Pendente</option>
              <option value="firme">Firme</option>
              <option value="em-producao">Em Produção</option>
              <option value="concluida">Concluída</option>
              <option value="cancelada">Cancelada</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="dateFilter">Período:</label>
            <input type="date" id="dateFilter" title="Filtrar por data de entrega" onchange="filterOrders()" placeholder="Selecione a data">
          </div>
        </div>
      </div>

      <div id="activeOrders" class="tab-content active">
        <div class="legend">
          <div class="legend-item"><span class="legend-color status-pendente"></span> Pendente</div>
          <div class="legend-item"><span class="legend-color status-firme"></span> Firme</div>
          <div class="legend-item"><span class="legend-color status-em-producao"></span> Em Produção</div>
          <div class="legend-item"><span class="legend-color status-concluida"></span> Concluída</div>
          <div class="legend-item"><span class="legend-color status-cancelada"></span> Cancelada</div>
        </div>
        <div class="batch-actions">
          <button onclick="deleteSelectedOrders()" class="danger-button"><i class="fas fa-trash"></i> Excluir Selecionadas</button>
        </div>
        <div class="op-table-container">
          <table class="op-table" id="activeOrdersTable">
            <thead>
              <tr>
                <th style="width: 5%;">
                  <label for="selectAllActive" style="display:none;">Selecionar todas as ordens ativas</label>
                  <input type="checkbox" id="selectAllActive" title="Selecionar todas as ordens ativas" onclick="toggleSelectAll('active')">
                </th>
                <th onclick="sortTable('numero', 'active')" data-sort="numero">Nº Ordem <span class="sort-icon"></span></th>
                <th onclick="sortTable('produto', 'active')" data-sort="produto">Produto <span class="sort-icon"></span></th>
                <th onclick="sortTable('quantidade', 'active')" data-sort="quantidade">Quantidade <span class="sort-icon"></span></th>
                <th onclick="sortTable('dataEntrega', 'active')" data-sort="dataEntrega">Data Entrega <span class="sort-icon"></span></th>
                <th onclick="sortTable('prioridade', 'active')" data-sort="prioridade">Prioridade <span class="sort-icon"></span></th>
                <th onclick="sortTable('status', 'active')" data-sort="status">Status <span class="sort-icon"></span></th>
                <th onclick="sortTable('centroCusto', 'active')" data-sort="centroCusto">Centro de Custo <span class="sort-icon"></span></th>
                <th onclick="sortTable('armazem', 'active')" data-sort="armazem">Armazém <span class="sort-icon"></span></th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="activeOrdersList"></tbody>
          </table>
        </div>
      </div>

      <div id="completedOrders" class="tab-content">
        <div class="legend">
          <div class="legend-item"><span class="legend-color status-pendente"></span> Pendente</div>
          <div class="legend-item"><span class="legend-color status-firme"></span> Firme</div>
          <div class="legend-item"><span class="legend-color status-em-producao"></span> Em Produção</div>
          <div class="legend-item"><span class="legend-color status-concluida"></span> Concluída</div>
          <div class="legend-item"><span class="legend-color status-cancelada"></span> Cancelada</div>
        </div>
        <div class="batch-actions">
          <button onclick="deleteSelectedOrders()" class="danger-button"><i class="fas fa-trash"></i> Excluir Selecionadas</button>
        </div>
        <div class="op-table-container">
          <table class="op-table" id="completedOrdersTable">
            <thead>
              <tr>
                <th style="width: 5%;"><input type="checkbox" id="selectAllCompleted" onclick="toggleSelectAll('completed')"></th>
                <th onclick="sortTable('numero', 'completed')" data-sort="numero">Nº Ordem <span class="sort-icon"></span></th>
                <th onclick="sortTable('produto', 'completed')" data-sort="produto">Produto <span class="sort-icon"></span></th>
                <th onclick="sortTable('quantidade', 'completed')" data-sort="quantidade">Quantidade <span class="sort-icon"></span></th>
                <th onclick="sortTable('dataEntrega', 'completed')" data-sort="dataEntrega">Data Entrega <span class="sort-icon"></span></th>
                <th onclick="sortTable('prioridade', 'completed')" data-sort="prioridade">Prioridade <span class="sort-icon"></span></th>
                <th onclick="sortTable('status', 'completed')" data-sort="status">Status <span class="sort-icon"></span></th>
                <th onclick="sortTable('centroCusto', 'completed')" data-sort="centroCusto">Centro de Custo <span class="sort-icon"></span></th>
                <th onclick="sortTable('armazem', 'completed')" data-sort="armazem">Armazém <span class="sort-icon"></span></th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="completedOrdersList"></tbody>
          </table>
        </div>
      </div>
    </div>
    </div> <!-- Fechamento do main-content -->
  </div>

  <!-- Modal Nova Ordem Manual -->
  <div id="newOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('newOrderModal')">&times;</span>
      <div class="modal-header">
        <h2>Nova Ordem de Produção</h2>
      </div>
      <div class="modal-body">
        <form id="newOrderForm" onsubmit="createManualOrder(event)">
          <div class="form-group">
            <label for="productSearch">Produto:</label>
            <input type="text" id="productSearch" placeholder="Digite para buscar o produto...">
            <select id="productSelect" required>
              <option value="">Selecione o produto...</option>
            </select>
          </div>

          <div class="form-row">
            <div class="form-col">
              <label for="quantity">Quantidade:</label>
              <input type="number" id="quantity" min="0.001" step="0.001" required>
            </div>
            <div class="form-col">
              <label for="dueDate">Data de Entrega:</label>
              <input type="date" id="dueDate" required>
            </div>
          </div>

          <div class="form-group">
            <label for="centroCusto">Centro de Custo:</label>
            <select id="centroCusto" required>
              <option value="">Selecione...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="warehouseProducao">Armazém de Produção:</label>
            <select id="warehouseProducao" required>
              <option value="">Selecione o armazém de produção...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="priority">Prioridade:</label>
            <select id="priority" required>
              <option value="normal">Normal</option>
              <option value="alta">Alta</option>
              <option value="urgente">Urgente</option>
            </select>
          </div>

          <div class="form-group">
            <label for="observations">Observações:</label>
            <textarea id="observations" rows="3"></textarea>
          </div>

          <div class="modal-footer">
            <button type="submit"><i class="fas fa-save"></i> Criar Ordem</button>
            <button type="button" onclick="closeModal('newOrderModal')" class="back-button"><i class="fas fa-times"></i> Cancelar</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal Ordem de Pedido -->
  <div id="salesOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('salesOrderModal')">&times;</span>
      <div class="modal-header">
        <h2>Criar Ordem de Pedido</h2>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>Pedido:</label>
          <select id="salesOrderSelect" onchange="loadSalesOrderDetails()">
            <option value="">Selecione o pedido...</option>
          </select>
        </div>
        <div class="form-group">
          <label for="centroCustoSales">Centro de Custo:</label>
          <select id="centroCustoSales" required>
            <option value="">Selecione...</option>
          </select>
        </div>
        <div class="form-group">
          <label for="warehouseProducaoSales">Armazém de Produção:</label>
          <select id="warehouseProducaoSales" required>
            <option value="">Selecione o armazém de produção...</option>
          </select>
        </div>
        <div id="salesOrderDetails"></div>
        <div class="modal-footer">
          <button onclick="createOrderFromSales()"><i class="fas fa-save"></i> Criar Ordem</button>
          <button onclick="closeModal('salesOrderModal')" class="back-button"><i class="fas fa-times"></i> Cancelar</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Apontamento -->
  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('appointmentModal')">&times;</span>
      <div class="modal-header">
        <h2>Apontamento de Produção</h2>
      </div>
      <div class="modal-body">
        <div id="appointmentDetails"></div>
        <form id="appointmentForm" onsubmit="submitAppointment(event)">
          <div class="form-row">
            <div class="form-col">
              <label for="producedQuantity">Quantidade Produzida:</label>
              <input type="number" id="producedQuantity" min="0.001" step="0.001" required>
            </div>
            <div class="form-col">
              <label for="scrapQuantity">Quantidade de Refugo:</label>
              <input type="number" id="scrapQuantity" min="0" step="0.001" value="0">
            </div>
          </div>

          <div class="form-group">
            <label for="appointmentObservations">Observações:</label>
            <textarea id="appointmentObservations" rows="3"></textarea>
          </div>

          <div class="modal-footer">
            <button type="submit"><i class="fas fa-check"></i> Confirmar Apontamento</button>
            <button type="button" onclick="closeModal('appointmentModal')" class="back-button"><i class="fas fa-times"></i> Cancelar</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal de Visualização -->
  <div id="viewOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('viewOrderModal')">&times;</span>
      <div class="modal-header">
        <h2>Detalhes da Ordem de Produção</h2>
      </div>
      <div class="modal-body" id="viewOrderDetails"></div>
      <div class="modal-footer">
        <button onclick="printOrder()" class="secondary-button"><i class="fas fa-print"></i> Imprimir</button>
        <button onclick="closeModal('viewOrderModal')" class="back-button"><i class="fas fa-times"></i> Fechar</button>
      </div>
    </div>
  </div>

  <!-- Modal de Seleção de Opcionais -->
  <div id="modalOpcionais" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="fecharModalOpcionais()">&times;</span>
      <h2>Selecionar Componentes Opcionais</h2>
      <div class="alert alert-info">
        Este produto possui componentes opcionais. Selecione quais deseja utilizar.
      </div>

      <div id="listaOpcionais" class="opcionais-list">
        <!-- Lista de opcionais será preenchida dinamicamente -->
      </div>

      <div class="button-group">
        <button type="button" onclick="confirmarOpcionais()" class="btn-primary">Confirmar</button>
        <button type="button" onclick="fecharModalOpcionais()" class="btn-secondary">Cancelar</button>
      </div>
    </div>
  </div>

  <!-- Botão para gerar OPs agrupadas -->
  <div style="margin-bottom: 20px;">
    <button id="btnGerarOpsAgrupadas" class="btn-warning"><i class="fas fa-layer-group"></i> Gerar OPs Agrupadas</button>
  </div>
  <!-- Modal de OPs agrupadas -->
  <div id="modalOpsAgrupadas" class="modal" style="display:none;">
    <div class="modal-content">
      <span class="close-button" onclick="document.getElementById('modalOpsAgrupadas').style.display='none'">&times;</span>
      <div class="modal-header"><h2>Gerar OPs Agrupadas</h2></div>
      <div class="modal-body">
        <div id="agrupadasTableContainer"></div>
        <div class="modal-footer">
          <button id="btnCriarOpsAgrupadas" class="btn-success"><i class="fas fa-save"></i> Criar OPs</button>
          <button onclick="document.getElementById('modalOpsAgrupadas').style.display='none'" class="back-button"><i class="fas fa-times"></i> Cancelar</button>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import {
      collection,
      addDoc,
      setDoc,
      runTransaction,
      getDoc,
      getDocs,
      query,
      where,
      doc,
      updateDoc,
      Timestamp,
      orderBy,
      serverTimestamp
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // ===================================================================
    // SERVIÇO DE EMPENHOS - INLINE
    // ===================================================================
    class EmpenhoService {
        static async transferirReservasParaEmpenhos(ordemProducaoId) {
            console.log(`🔄 Transferindo reservas para empenhos - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                const opRef = doc(db, "ordensProducao", ordemProducaoId);
                const opDoc = await transaction.get(opRef);

                if (!opDoc.exists()) {
                    throw new Error('Ordem de produção não encontrada');
                }

                const op = opDoc.data();
                const materiaisNecessarios = op.materiaisNecessarios || [];

                let transferencias = 0;
                let erros = [];

                for (const material of materiaisNecessarios) {
                    if (!material.quantidadeReservada || material.quantidadeReservada <= 0) {
                        continue;
                    }

                    try {
                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", material.produtoId),
                            where("armazemId", "==", op.armazemProducaoId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (estoqueSnapshot.empty) {
                            erros.push(`Estoque não encontrado para produto ${material.produtoId}`);
                            continue;
                        }

                        const estoqueDoc = estoqueSnapshot.docs[0];
                        const estoque = estoqueDoc.data();

                        const quantidadeTransferir = material.quantidadeReservada;
                        const novoSaldoReservado = Math.max(0, (estoque.saldoReservado || 0) - quantidadeTransferir);
                        const novoSaldoEmpenhado = (estoque.saldoEmpenhado || 0) + quantidadeTransferir;

                        transaction.update(doc(db, "estoques", estoqueDoc.id), {
                            saldoReservado: novoSaldoReservado,
                            saldoEmpenhado: novoSaldoEmpenhado,
                            ultimaMovimentacao: Timestamp.now()
                        });

                        const empenhoRef = doc(collection(db, "empenhos"));
                        transaction.set(empenhoRef, {
                            ordemProducaoId,
                            produtoId: material.produtoId,
                            armazemId: op.armazemProducaoId,
                            quantidadeEmpenhada: quantidadeTransferir,
                            quantidadeConsumida: 0,
                            status: 'ATIVO',
                            dataEmpenho: Timestamp.now(),
                            origemReserva: true
                        });

                        transferencias++;

                    } catch (error) {
                        erros.push(`Erro no material ${material.produtoId}: ${error.message}`);
                    }
                }

                transaction.update(opRef, {
                    status: 'Em Produção',
                    dataInicioProducao: Timestamp.now(),
                    empenhosAtivos: transferencias
                });

                return { transferencias, erros, ordemProducaoId };
            });
        }

        static async consumirMaterialEmpenhado(ordemProducaoId, consumos) {
            console.log(`⚡ Consumindo materiais empenhados - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                let consumosRealizados = 0;
                let erros = [];

                for (const consumo of consumos) {
                    try {
                        const empenhoQuery = query(
                            collection(db, "empenhos"),
                            where("ordemProducaoId", "==", ordemProducaoId),
                            where("produtoId", "==", consumo.produtoId),
                            where("status", "==", "ATIVO")
                        );

                        const empenhoSnapshot = await getDocs(empenhoQuery);
                        if (empenhoSnapshot.empty) {
                            erros.push(`Empenho não encontrado para produto ${consumo.produtoId}`);
                            continue;
                        }

                        const empenhoDoc = empenhoSnapshot.docs[0];
                        const empenho = empenhoDoc.data();

                        const quantidadeDisponivel = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;
                        const quantidadeConsumir = Math.min(consumo.quantidade, quantidadeDisponivel);

                        if (quantidadeConsumir <= 0) {
                            erros.push(`Sem quantidade empenhada disponível para ${consumo.produtoId}`);
                            continue;
                        }

                        const novaQuantidadeConsumida = empenho.quantidadeConsumida + quantidadeConsumir;
                        const novoStatus = novaQuantidadeConsumida >= empenho.quantidadeEmpenhada ? 'CONSUMIDO' : 'ATIVO';

                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            quantidadeConsumida: novaQuantidadeConsumida,
                            status: novoStatus,
                            ultimoConsumo: Timestamp.now()
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", consumo.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldo: estoque.saldo - quantidadeConsumir,
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeConsumir),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
                        transaction.set(movimentacaoRef, {
                            produtoId: consumo.produtoId,
                            armazemId: empenho.armazemId,
                            tipo: 'SAIDA',
                            quantidade: quantidadeConsumir,
                            tipoDocumento: 'CONSUMO_PRODUCAO',
                            numeroDocumento: ordemProducaoId,
                            observacoes: `Consumo OP ${ordemProducaoId} - Empenho`,
                            dataHora: Timestamp.now(),
                            empenhoId: empenhoDoc.id
                        });

                        consumosRealizados++;

                    } catch (error) {
                        erros.push(`Erro no consumo ${consumo.produtoId}: ${error.message}`);
                    }
                }

                return { consumosRealizados, erros };
            });
        }

        static async liberarEmpenhosRestantes(ordemProducaoId, motivo = 'OP_FINALIZADA') {
            console.log(`🔓 Liberando empenhos restantes - OP: ${ordemProducaoId}`);

            return runTransaction(db, async (transaction) => {
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("status", "==", "ATIVO")
                );

                const empenhosSnapshot = await getDocs(empenhosQuery);
                let liberacoes = 0;

                for (const empenhoDoc of empenhosSnapshot.docs) {
                    const empenho = empenhoDoc.data();
                    const quantidadeRestante = empenho.quantidadeEmpenhada - empenho.quantidadeConsumida;

                    if (quantidadeRestante > 0) {
                        transaction.update(doc(db, "empenhos", empenhoDoc.id), {
                            status: 'LIBERADO',
                            quantidadeLiberada: quantidadeRestante,
                            dataLiberacao: Timestamp.now(),
                            motivoLiberacao: motivo
                        });

                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", empenho.produtoId),
                            where("armazemId", "==", empenho.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);
                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];
                            const estoque = estoqueDoc.data();

                            transaction.update(doc(db, "estoques", estoqueDoc.id), {
                                saldoEmpenhado: Math.max(0, (estoque.saldoEmpenhado || 0) - quantidadeRestante),
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        liberacoes++;
                    }
                }

                return { liberacoes, ordemProducaoId };
            });
        }

        static async consultarEmpenhosOP(ordemProducaoId) {
            const empenhosQuery = query(
                collection(db, "empenhos"),
                where("ordemProducaoId", "==", ordemProducaoId)
            );

            const empenhosSnapshot = await getDocs(empenhosQuery);
            return empenhosSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        }

        static async inicializarCampoEmpenho() {
            console.log('🔧 Inicializando campo saldoEmpenhado nos estoques...');

            const estoquesSnapshot = await getDocs(collection(db, "estoques"));
            let atualizados = 0;

            for (const estoqueDoc of estoquesSnapshot.docs) {
                const estoque = estoqueDoc.data();

                if (estoque.saldoEmpenhado === undefined) {
                    await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                        saldoEmpenhado: 0
                    });
                    atualizados++;
                }
            }

            console.log(`✅ ${atualizados} estoques atualizados com campo saldoEmpenhado`);
            return atualizados;
        }
    }

    let produtos = [];
    let estruturas = [];
    let pedidosVenda = [];
    let ordensProducao = [];
    let estoques = [];
    let centrosCusto = [];
    let armazens = [];
    let currentAppointmentOp = null;
    let counterRef;
    let sortState = { active: { field: 'numero', direction: 'asc' }, completed: { field: 'numero', direction: 'asc' } };
    let solicitacoes = [];

    // Configurações de produção carregadas do config_parametros.html
    let productionConfig = {
      aglutinarOps: false,
      usarSaldoEstoque: true,
      opsFirmes: false,
      reservarEstoque: true,
      antiDuplicacao: true // Nova configuração
    };

    window.onload = async function() {
      if (!localStorage.getItem('currentUser')) {
        window.location.href = 'login.html';
        return;
      }
      await loadInitialData();
      await loadProductionConfig(); // Carregar configurações de produção
      setupProductSearch();
      await loadActiveOrders();
      setupSalesOrderSelect();
      setupCentroCustoSelect();
      setupWarehouseSelect();
      setupFilterSelects();

      counterRef = doc(db, "contadores", "ordens");
      const counterDoc = await getDoc(counterRef);
      if (!counterDoc.exists()) {
        await setDoc(counterRef, { valor: 0 });
      }

      // Verificar se há parâmetros de simulação
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('produtoId')) {
        await createOrderFromSimulation(urlParams);
      }

      document.getElementById('newOrderButton').addEventListener('click', openNewOrderModal);
      document.getElementById('orderFromSalesButton').addEventListener('click', openOrderFromSalesModal);
    };

    async function loadInitialData() {
      try {
        const [produtosSnap, estruturasSnap, pedidosSnap, ordensSnap, estoquesSnap, centrosCustoSnap, armazensSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "pedidosVenda")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "centrosCusto")),
          getDocs(collection(db, "armazens"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        pedidosVenda = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    // Função para carregar configurações de produção - SIMPLIFICADA
    async function loadProductionConfig() {
      try {
        // Carregar apenas de parametros/sistema (fonte única da verdade)
        const sistemaDoc = await getDoc(doc(db, "parametros", "sistema"));

        let configSistema = {};

        if (sistemaDoc.exists()) {
          configSistema = sistemaDoc.data();
        }

        // Usar configurações centralizadas
        productionConfig = {
          aglutinarOps: configSistema.aglutinarOps ?? false,
          usarSaldoEstoque: configSistema.usarSaldoEstoque ?? true,
          opsFirmes: configSistema.opsFirmes ?? false,
          reservarEstoque: configSistema.reservarEstoque ?? true,
          antiDuplicacao: configSistema.antiDuplicacao ?? true
        };

        console.log("✅ Configurações carregadas (fonte única):", productionConfig);
        updateConfigDisplay();
      } catch (error) {
        console.error("Erro ao carregar configurações de produção:", error);
        updateConfigDisplay(); // Mostrar valores padrão
      }
    }

    // Função para atualizar a exibição das configurações
    function updateConfigDisplay() {
      const statusAglutinacao = document.getElementById('statusAglutinacao');
      const statusSaldoEstoque = document.getElementById('statusSaldoEstoque');
      const statusOPsFirmes = document.getElementById('statusOPsFirmes');
      const statusReservaEstoque = document.getElementById('statusReservaEstoque');

      statusAglutinacao.textContent = productionConfig.aglutinarOps ? 'ATIVO - OPs serão aglutinadas' : 'INATIVO - OPs individuais';
      statusAglutinacao.className = `config-status ${productionConfig.aglutinarOps ? 'active' : 'inactive'}`;

      statusSaldoEstoque.textContent = productionConfig.usarSaldoEstoque ? 'ATIVO - Considera saldo disponível' : 'INATIVO - Ignora saldo';
      statusSaldoEstoque.className = `config-status ${productionConfig.usarSaldoEstoque ? 'active' : 'inactive'}`;

      statusOPsFirmes.textContent = productionConfig.opsFirmes ? 'ATIVO - OPs criadas como firmes' : 'INATIVO - OPs planejadas';
      statusOPsFirmes.className = `config-status ${productionConfig.opsFirmes ? 'active' : 'inactive'}`;

      statusReservaEstoque.textContent = productionConfig.reservarEstoque ? 'ATIVO - Reserva automática' : 'INATIVO - Sem reserva';
      statusReservaEstoque.className = `config-status ${productionConfig.reservarEstoque ? 'active' : 'inactive'}`;
    }

    // Função global para recarregar configurações
    window.loadProductionConfig = loadProductionConfig;

    // Função para criar OPs com aglutinação baseada na configuração
    async function createOrdersWithConfig(ordersData) {
      if (!productionConfig.aglutinarOps) {
        // Criar OPs individuais
        return await createIndividualOrders(ordersData);
      } else {
        // Criar OPs aglutinadas
        return await createAggregatedOrders(ordersData);
      }
    }

    // Função para criar OPs individuais
    async function createIndividualOrders(ordersData) {
      const createdOrders = [];
      for (const orderData of ordersData) {
        try {
          const op = {
            numero: await generateOrderNumber(),
            produtoId: orderData.produtoId,
            quantidade: orderData.quantidade,
            dataEntrega: orderData.dataEntrega,
            status: productionConfig.opsFirmes ? 'Firme' : 'Pendente',
            nivel: 0,
            prioridade: orderData.prioridade || 'normal',
            centroCustoId: orderData.centroCustoId,
            dataCriacao: Timestamp.now(),
            armazemProducaoId: orderData.armazemProducaoId,
            tipoOP: productionConfig.opsFirmes ? 'FIRME' : 'PLANEJADA',
            pedidosOrigem: orderData.pedidosOrigem || []
          };

          const docRef = await addDoc(collection(db, "ordensProducao"), op);
          op.id = docRef.id;
          createdOrders.push(op);

          // Explodir componentes se houver estrutura
          const estrutura = estruturas.find(e => e.produtoPaiId === orderData.produtoId);
          if (estrutura) {
            await explodeComponents(op, estrutura);
          }
        } catch (error) {
          console.error(`Erro ao criar OP individual para produto ${orderData.produtoId}:`, error);
        }
      }
      return createdOrders;
    }

    // Função para criar OPs aglutinadas
    async function createAggregatedOrders(ordersData) {
      // Agrupar por produto e armazém
      const groupedOrders = {};

      ordersData.forEach(orderData => {
        const key = `${orderData.produtoId}|${orderData.armazemProducaoId}`;
        if (!groupedOrders[key]) {
          groupedOrders[key] = {
            produtoId: orderData.produtoId,
            armazemProducaoId: orderData.armazemProducaoId,
            quantidade: 0,
            dataEntrega: orderData.dataEntrega, // Usar a primeira data
            centroCustoId: orderData.centroCustoId, // Usar o primeiro centro de custo
            prioridade: orderData.prioridade || 'normal',
            pedidosOrigem: []
          };
        }

        groupedOrders[key].quantidade += orderData.quantidade;
        if (orderData.pedidosOrigem) {
          groupedOrders[key].pedidosOrigem.push(...orderData.pedidosOrigem);
        }

        // Usar a data mais próxima
        if (orderData.dataEntrega < groupedOrders[key].dataEntrega) {
          groupedOrders[key].dataEntrega = orderData.dataEntrega;
        }
      });

      const createdOrders = [];
      for (const key in groupedOrders) {
        const groupedData = groupedOrders[key];
        try {
          const op = {
            numero: await generateOrderNumber(),
            produtoId: groupedData.produtoId,
            quantidade: groupedData.quantidade,
            dataEntrega: groupedData.dataEntrega,
            status: productionConfig.opsFirmes ? 'Firme' : 'Pendente',
            nivel: 0,
            prioridade: groupedData.prioridade,
            centroCustoId: groupedData.centroCustoId,
            dataCriacao: Timestamp.now(),
            armazemProducaoId: groupedData.armazemProducaoId,
            tipoOP: productionConfig.opsFirmes ? 'FIRME' : 'PLANEJADA',
            pedidosOrigem: groupedData.pedidosOrigem,
            aglutinada: true // Marcar como aglutinada
          };

          const docRef = await addDoc(collection(db, "ordensProducao"), op);
          op.id = docRef.id;
          createdOrders.push(op);

          // Explodir componentes se houver estrutura
          const estrutura = estruturas.find(e => e.produtoPaiId === groupedData.produtoId);
          if (estrutura) {
            await explodeComponents(op, estrutura);
          }
        } catch (error) {
          console.error(`Erro ao criar OP aglutinada para produto ${groupedData.produtoId}:`, error);
        }
      }
      return createdOrders;
    }

    function setupCentroCustoSelect() {
      const select = document.getElementById('centroCusto');
      const selectSales = document.getElementById('centroCustoSales');
      const selectFilter = document.getElementById('costCenterFilter');
      select.innerHTML = '<option value="">Selecione...</option>';
      selectSales.innerHTML = '<option value="">Selecione...</option>';
      selectFilter.innerHTML = '<option value="">Todos</option>';

      centrosCusto.forEach(cc => {
        select.innerHTML += `<option value="${cc.id}">${cc.codigo} - ${cc.descricao}</option>`;
        selectSales.innerHTML += `<option value="${cc.id}">${cc.codigo} - ${cc.descricao}</option>`;
        selectFilter.innerHTML += `<option value="${cc.id}">${cc.codigo} - ${cc.descricao}</option>`;
      });
    }

    function setupWarehouseSelect() {
      const select = document.getElementById('warehouseProducao');
      const selectSales = document.getElementById('warehouseProducaoSales');
      const selectFilter = document.getElementById('warehouseFilter');
      select.innerHTML = '<option value="">Selecione o armazém de produção...</option>';
      selectSales.innerHTML = '<option value="">Selecione o armazém de produção...</option>';
      selectFilter.innerHTML = '<option value="">Todos</option>';

      armazens
        .filter(a => a.tipo === 'PRODUCAO')
        .forEach(armazem => {
          select.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
          selectSales.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
          selectFilter.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
        });
    }

    function setupFilterSelects() {
      setupCentroCustoSelect();
      setupWarehouseSelect();
    }

    async function generateOrderNumber() {
      const date = new Date();
      const year = date.getFullYear().toString().substr(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');

      try {
        const newCounter = await runTransaction(db, async (transaction) => {
          const counterDoc = await transaction.get(counterRef);
          if (!counterDoc.exists()) {
            throw new Error("Counter document does not exist!");
          }
          const newValue = counterDoc.data().valor + 1;

          transaction.update(counterRef, { valor: newValue });
          return newValue;
        });

        const sequence = newCounter.toString().padStart(4, '0');
        return `OP${year}${month}${sequence}`;
      } catch (error) {
        console.error("Erro ao gerar número da ordem:", error);
        throw error;
      }
    }

    function setupProductSearch() {
      const productSearch = document.getElementById('productSearch');
      productSearch.addEventListener('input', () => {
        const searchText = productSearch.value.toLowerCase();
        updateProductSelect(searchText);
      });
    }

    function updateProductSelect(searchText = '') {
      const productSelect = document.getElementById('productSelect');
      productSelect.innerHTML = '<option value="">Selecione o produto...</option>';

      produtos
        .filter(p => (p.tipo === 'PA' || p.tipo === 'SP') &&
                    (!searchText || 
                     p.codigo.toLowerCase().includes(searchText) ||
                     p.descricao.toLowerCase().includes(searchText)))
        .forEach(produto => {
          productSelect.innerHTML += `
            <option value="${produto.id}">
              ${produto.codigo} - ${produto.descricao} (${produto.tipo})
            </option>`;
        });
    }

    function setupSalesOrderSelect() {
      const select = document.getElementById('salesOrderSelect');
      select.innerHTML = '<option value="">Selecione o pedido...</option>';

      const pendingOrders = pedidosVenda.filter(p => p.status === 'Pendente');
      pendingOrders.forEach(pedido => {
        const produto = produtos.find(p => p.id === pedido.produtoId);
        select.innerHTML += `
          <option value="${pedido.id}">
            ${pedido.numero} - ${produto.codigo} - ${produto.descricao}
          </option>`;
      });
    }

    window.loadSalesOrderDetails = function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      const detailsDiv = document.getElementById('salesOrderDetails');

      if (!pedidoId) {
        detailsDiv.innerHTML = '';
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);

      detailsDiv.innerHTML = `
        <div class="order-details">
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade:</strong> ${pedido.quantidade} ${produto.unidade}</p>
          <p><strong>Data de Entrega:</strong> ${new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString()}</p>
        </div>`;
    };

    window.createOrderFromSales = async function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      const centroCustoId = document.getElementById('centroCustoSales').value;
      const warehouseProducaoId = document.getElementById('warehouseProducaoSales').value;

      if (!pedidoId || !centroCustoId || !warehouseProducaoId) {
        alert('Selecione um pedido, um centro de custo e um armazém de produção.');
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === pedido.produtoId);
      const armazem = armazens.find(a => a.id === warehouseProducaoId);

      if (!estrutura) {
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }
      if (!armazem || armazem.tipo !== 'PRODUCAO') {
        alert('O armazém selecionado deve ser do tipo Produção.');
        return;
      }

      try {
        // ===== VERIFICAÇÃO ANTI-DUPLICAÇÃO INTELIGENTE =====
        console.log('🔍 Verificando duplicação antes de criar OP do pedido...');
        const opsExistentes = await verificarOPsExistentes();

        // Verificar duplicação no armazém específico
        const opExistente = await verificarOPCompativel(pedido.produtoId, pedido.quantidade, warehouseProducaoId, pedido.dataEntrega, opsExistentes);

        // Verificar duplicação em qualquer armazém
        const opExistenteQualquerArmazem = await verificarOPCompativel(pedido.produtoId, pedido.quantidade, null, pedido.dataEntrega, opsExistentes);

        if (opExistente.encontrada) {
          // Duplicação no mesmo armazém - CRÍTICA
          const produto = produtos.find(p => p.id === pedido.produtoId);
          const confirmacao = confirm(
            `🚨 DUPLICAÇÃO CRÍTICA DETECTADA!\n\n` +
            `Já existe uma OP para o produto ${produto?.codigo || pedido.produtoId} NO MESMO ARMAZÉM:\n` +
            `• OP: ${opExistente.op.numero}\n` +
            `• Quantidade atual: ${opExistente.op.quantidade}\n` +
            `• Status: ${opExistente.op.status}\n` +
            `• Armazém: ${opExistente.op.armazemProducaoId}\n\n` +
            `${opExistente.precisaAjuste ?
              `A quantidade será ajustada de ${opExistente.op.quantidade} para ${opExistente.novaQuantidade}` :
              'A quantidade atual é suficiente'}\n\n` +
            `⚠️ RECOMENDAÇÃO: Cancele e use a OP existente!\n\n` +
            `Deseja continuar mesmo assim e criar uma nova OP?`
          );

          if (!confirmacao) {
            alert('✅ Criação de OP cancelada para evitar duplicação crítica.');
            return;
          } else {
            alert('⚠️ Criando OP mesmo com duplicação crítica detectada.');
          }
        } else if (opExistenteQualquerArmazem.encontrada) {
          // Duplicação em armazém diferente - AVISO
          const produto = produtos.find(p => p.id === pedido.produtoId);
          const confirmacao = confirm(
            `⚠️ POSSÍVEL DUPLICAÇÃO DETECTADA!\n\n` +
            `Existe uma OP para o produto ${produto?.codigo || pedido.produtoId} em OUTRO ARMAZÉM:\n` +
            `• OP: ${opExistenteQualquerArmazem.op.numero}\n` +
            `• Quantidade atual: ${opExistenteQualquerArmazem.op.quantidade}\n` +
            `• Status: ${opExistenteQualquerArmazem.op.status}\n` +
            `• Armazém: ${opExistenteQualquerArmazem.op.armazemProducaoId}\n\n` +
            `Você está criando no armazém: ${warehouseProducaoId}\n\n` +
            `Deseja continuar?`
          );

          if (!confirmacao) {
            alert('❌ Criação de OP cancelada.');
            return;
          }
        }

        // ===== VALIDAÇÃO DE ESTOQUE ANTES DE CRIAR OP =====
        if (productionConfig.usarSaldoEstoque) {
          console.log('🔍 Validando estoque antes de criar OP do pedido...');

          const validacaoEstoque = await validarEstoqueParaCriacaoOP(pedido.produtoId, pedido.quantidade, warehouseProducaoId, estrutura);

          if (!validacaoEstoque.podeCrear) {
            // Separar materiais por tipo
            const materiaisMP = validacaoEstoque.materiaisInsuficientes.filter(m => !m.ehSubproduto);
            const materiaisSP = validacaoEstoque.materiaisInsuficientes.filter(m => m.ehSubproduto);

            let detalhes = `📋 PEDIDO: ${produto.codigo} - Quantidade: ${pedido.quantidade}\n\n`;

            // Mostrar matérias-primas insuficientes
            if (materiaisMP.length > 0) {
              detalhes += '📦 MATÉRIAS-PRIMAS INSUFICIENTES:\n';
              detalhes += materiaisMP.map(m =>
                `• ${m.codigo}: Necessário ${m.necessario.toFixed(3)}, Disponível ${m.disponivel.toFixed(3)} (Falta: ${m.falta.toFixed(3)})`
              ).join('\n');
            }

            // Mostrar subprodutos sem OP
            if (materiaisSP.length > 0) {
              if (materiaisMP.length > 0) detalhes += '\n\n';
              detalhes += '🏭 SUBPRODUTOS SEM OP ABERTA:\n';
              detalhes += materiaisSP.map(m =>
                `• ${m.codigo}: Necessário ${m.necessario.toFixed(3)} (SEM OP ATIVA)`
              ).join('\n');
            }

            // Mostrar OPs abertas para SPs (se houver)
            if (validacaoEstoque.verificacaoSPs?.opsAbertas?.length > 0) {
              if (materiaisMP.length > 0 || materiaisSP.length > 0) detalhes += '\n\n';
              detalhes += '✅ SUBPRODUTOS COM OP ABERTA:\n';
              validacaoEstoque.verificacaoSPs.opsAbertas.forEach(sp => {
                detalhes += `• ${sp.codigo}: ${sp.quantidadeEmProducao.toFixed(3)} em produção`;
                if (!sp.suficiente) {
                  const falta = sp.quantidadeNecessaria - sp.quantidadeEmProducao;
                  detalhes += ` (falta: ${falta.toFixed(3)})`;
                }
                detalhes += '\n';
              });
            }

            // Mostrar modal de decisão personalizado
            const decisao = await mostrarModalDecisaoOP(detalhes, materiaisSP, validacaoEstoque.verificacaoSPs);

            if (decisao === 'gerar' && materiaisSP.length > 0) {
              // Gerar OPs para subprodutos
              const opsGeradas = await gerarOPsParaSubprodutos(materiaisSP, validacaoEstoque.verificacaoSPs);
              if (opsGeradas.length > 0) {
                // Mostrar resumo detalhado
                let resumo = '✅ RESUMO DA GERAÇÃO DE OPs PARA PEDIDO:\n\n';
                resumo += '🆕 NOVAS OPs CRIADAS:\n';
                opsGeradas.forEach(op => {
                  resumo += `• OP ${op.numero} - ${op.produto}: ${op.quantidade} unidades\n`;
                });

                // Mostrar OPs existentes aproveitadas
                if (validacaoEstoque.verificacaoSPs?.opsAbertas?.length > 0) {
                  resumo += '\n♻️ OPs EXISTENTES APROVEITADAS:\n';
                  validacaoEstoque.verificacaoSPs.opsAbertas.forEach(sp => {
                    resumo += `• ${sp.codigo}: ${sp.quantidadeEmProducao.toFixed(3)} em produção\n`;
                    sp.ops.forEach(op => {
                      resumo += `  - OP ${op.numero}: ${op.quantidade.toFixed(3)}\n`;
                    });
                  });
                }

                resumo += '\n🎯 PRÓXIMO PASSO:\n';
                resumo += 'Agora você pode criar a OP para o pedido.\n';
                resumo += 'O sistema aproveitará as OPs existentes e as recém-criadas.';

                alert(resumo);
                return; // Parar aqui para que o usuário tente criar a OP novamente
              }
            } else if (decisao === 'criar') {
              // Continuar com criação
              alert('⚠️ OP será criada com pendência de material.\n\nLembre-se de providenciar o material antes de iniciar a produção.');
            } else {
              // Cancelar
              alert('❌ Criação de OP cancelada.');
              return;
            }
          }
        }

        const parentOp = {
          numero: await generateOrderNumber(),
          pedidoId: pedido.id,
          produtoId: pedido.produtoId,
          quantidade: pedido.quantidade,
          dataEntrega: pedido.dataEntrega,
          status: 'Pendente',
          nivel: 0,
          prioridade: 'normal',
          centroCustoId,
          dataCriacao: Timestamp.now(),
          armazemProducaoId: warehouseProducaoId
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        await updateDoc(doc(db, "pedidosVenda", pedidoId), {
          status: 'Em Produção'
        });

        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');
        closeModal('salesOrderModal');
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    window.createManualOrder = async function(event) {
      event.preventDefault();

      const productId = document.getElementById('productSelect').value;
      const quantity = parseFloat(document.getElementById('quantity').value);
      const dueDate = document.getElementById('dueDate').value;
      const centroCustoId = document.getElementById('centroCusto').value;
      const warehouseProducaoId = document.getElementById('warehouseProducao').value;
      const priority = document.getElementById('priority').value;
      const observations = document.getElementById('observations').value;

      if (!productId || !quantity || !dueDate || !centroCustoId || !warehouseProducaoId) {
        alert('Preencha todos os campos obrigatórios.');
        return;
      }

      const estrutura = estruturas.find(e => e.produtoPaiId === productId);
      const armazem = armazens.find(a => a.id === warehouseProducaoId);

      if (!estrutura) {
        const produto = produtos.find(p => p.id === productId);
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }
      if (!armazem || armazem.tipo !== 'PRODUCAO') {
        alert('O armazém selecionado deve ser do tipo Produção.');
        return;
      }

      try {
        // ===== VERIFICAÇÃO ANTI-DUPLICAÇÃO INTELIGENTE =====
        console.log('🔍 Verificando duplicação antes de criar OP...');
        const opsExistentes = await verificarOPsExistentes();

        // Verificar duplicação no armazém específico
        const opExistente = await verificarOPCompativel(productId, quantity, warehouseProducaoId, new Date(dueDate), opsExistentes);

        // Verificar duplicação em qualquer armazém (para alertar sobre possível duplicação)
        const opExistenteQualquerArmazem = await verificarOPCompativel(productId, quantity, null, new Date(dueDate), opsExistentes);

        if (opExistente.encontrada) {
          // Duplicação no mesmo armazém - CRÍTICA
          const produto = produtos.find(p => p.id === productId);
          const confirmacao = confirm(
            `🚨 DUPLICAÇÃO CRÍTICA DETECTADA!\n\n` +
            `Já existe uma OP para o produto ${produto?.codigo || productId} NO MESMO ARMAZÉM:\n` +
            `• OP: ${opExistente.op.numero}\n` +
            `• Quantidade atual: ${opExistente.op.quantidade}\n` +
            `• Status: ${opExistente.op.status}\n` +
            `• Armazém: ${opExistente.op.armazemProducaoId}\n\n` +
            `${opExistente.precisaAjuste ?
              `A quantidade será ajustada de ${opExistente.op.quantidade} para ${opExistente.novaQuantidade}` :
              'A quantidade atual é suficiente'}\n\n` +
            `⚠️ RECOMENDAÇÃO: Cancele e use a OP existente!\n\n` +
            `Deseja continuar mesmo assim e criar uma nova OP?`
          );

          if (!confirmacao) {
            alert('✅ Criação de OP cancelada para evitar duplicação crítica.');
            return;
          } else {
            alert('⚠️ Criando OP mesmo com duplicação crítica detectada.');
          }
        } else if (opExistenteQualquerArmazem.encontrada) {
          // Duplicação em armazém diferente - AVISO
          const produto = produtos.find(p => p.id === productId);
          const confirmacao = confirm(
            `⚠️ POSSÍVEL DUPLICAÇÃO DETECTADA!\n\n` +
            `Existe uma OP para o produto ${produto?.codigo || productId} em OUTRO ARMAZÉM:\n` +
            `• OP: ${opExistenteQualquerArmazem.op.numero}\n` +
            `• Quantidade atual: ${opExistenteQualquerArmazem.op.quantidade}\n` +
            `• Status: ${opExistenteQualquerArmazem.op.status}\n` +
            `• Armazém: ${opExistenteQualquerArmazem.op.armazemProducaoId}\n\n` +
            `Você está criando no armazém: ${warehouseProducaoId}\n\n` +
            `Deseja continuar?`
          );

          if (!confirmacao) {
            alert('❌ Criação de OP cancelada.');
            return;
          }
        }

        // ===== VALIDAÇÃO DE ESTOQUE ANTES DE CRIAR OP =====
        if (productionConfig.usarSaldoEstoque) {
          console.log('🔍 Validando estoque antes de criar OP...');

          const validacaoEstoque = await validarEstoqueParaCriacaoOP(productId, quantity, warehouseProducaoId, estrutura);

          if (!validacaoEstoque.podeCrear) {
            // Separar materiais por tipo
            const materiaisMP = validacaoEstoque.materiaisInsuficientes.filter(m => !m.ehSubproduto);
            const materiaisSP = validacaoEstoque.materiaisInsuficientes.filter(m => m.ehSubproduto);

            let detalhes = '';

            // Mostrar matérias-primas insuficientes
            if (materiaisMP.length > 0) {
              detalhes += '📦 MATÉRIAS-PRIMAS INSUFICIENTES:\n';
              detalhes += materiaisMP.map(m =>
                `• ${m.codigo}: Necessário ${m.necessario.toFixed(3)}, Disponível ${m.disponivel.toFixed(3)} (Falta: ${m.falta.toFixed(3)})`
              ).join('\n');
            }

            // Mostrar subprodutos sem OP
            if (materiaisSP.length > 0) {
              if (detalhes) detalhes += '\n\n';
              detalhes += '🏭 SUBPRODUTOS SEM OP ABERTA:\n';
              detalhes += materiaisSP.map(m =>
                `• ${m.codigo}: Necessário ${m.necessario.toFixed(3)} (SEM OP ATIVA)`
              ).join('\n');
            }

            // Mostrar OPs abertas para SPs (se houver)
            if (validacaoEstoque.verificacaoSPs?.opsAbertas?.length > 0) {
              if (detalhes) detalhes += '\n\n';
              detalhes += '✅ SUBPRODUTOS COM OP ABERTA:\n';
              validacaoEstoque.verificacaoSPs.opsAbertas.forEach(sp => {
                detalhes += `• ${sp.codigo}: ${sp.quantidadeEmProducao.toFixed(3)} em produção`;
                if (!sp.suficiente) {
                  const falta = sp.quantidadeNecessaria - sp.quantidadeEmProducao;
                  detalhes += ` (falta: ${falta.toFixed(3)})`;
                }
                detalhes += '\n';
                sp.ops.forEach(op => {
                  detalhes += `  - OP ${op.numero}: ${op.quantidade.toFixed(3)} (${op.status})\n`;
                });
              });
            }

            // Mostrar modal de decisão personalizado
            const decisao = await mostrarModalDecisaoOP(detalhes, materiaisSP, validacaoEstoque.verificacaoSPs);

            if (decisao === 'gerar' && materiaisSP.length > 0) {
              // Gerar OPs para subprodutos
              const opsGeradas = await gerarOPsParaSubprodutos(materiaisSP, validacaoEstoque.verificacaoSPs);
              if (opsGeradas.length > 0) {
                // Mostrar resumo detalhado
                let resumo = '✅ RESUMO DA GERAÇÃO DE OPs:\n\n';
                resumo += '🆕 NOVAS OPs CRIADAS:\n';
                opsGeradas.forEach(op => {
                  resumo += `• OP ${op.numero} - ${op.produto}: ${op.quantidade} unidades\n`;
                });

                // Mostrar OPs existentes aproveitadas
                if (validacaoEstoque.verificacaoSPs?.opsAbertas?.length > 0) {
                  resumo += '\n♻️ OPs EXISTENTES APROVEITADAS:\n';
                  validacaoEstoque.verificacaoSPs.opsAbertas.forEach(sp => {
                    resumo += `• ${sp.codigo}: ${sp.quantidadeEmProducao.toFixed(3)} em produção\n`;
                    sp.ops.forEach(op => {
                      resumo += `  - OP ${op.numero}: ${op.quantidade.toFixed(3)}\n`;
                    });
                  });
                }

                resumo += '\n🎯 PRÓXIMO PASSO:\n';
                resumo += 'Agora você pode criar a OP principal para o produto pai.\n';
                resumo += 'O sistema aproveitará as OPs existentes e as recém-criadas.';

                alert(resumo);
                return; // Parar aqui para que o usuário tente criar a OP novamente
              }
            } else if (decisao === 'criar') {
              // Continuar com criação
              alert('⚠️ OP será criada com pendência de material.\n\nLembre-se de providenciar o material antes de iniciar a produção.');
            } else {
              // Cancelar
              alert('❌ Criação de OP cancelada.');
              return;
            }
          } else if (validacaoEstoque.alertas.length > 0) {
            const alertasTexto = validacaoEstoque.alertas.join('\n');
            const confirmacao = confirm(`⚠️ ALERTAS DE ESTOQUE\n\n${alertasTexto}\n\nDeseja continuar com a criação da OP?`);

            if (!confirmacao) {
              return;
            }
          }
        }

        const parentOp = {
          numero: await generateOrderNumber(),
          produtoId: productId,
          produtoPaiId: productId,
          quantidade: quantity,
          dataEntrega: new Date(dueDate),
          status: productionConfig.opsFirmes ? 'Firme' : 'Pendente', // Usar configuração
          nivel: 0,
          prioridade: priority,
          centroCustoId,
          observacoes: observations,
          dataCriacao: Timestamp.now(),
          armazemProducaoId: warehouseProducaoId,
          tipoOP: productionConfig.opsFirmes ? 'FIRME' : 'PLANEJADA' // Adicionar tipo
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        await explodeComponents(parentOp, estrutura);

        // Verificar se houve reutilização de OPs
        const opsReutilizadas = await verificarOPsReutilizadas(parentOp.id);

        let mensagem = 'Ordem de produção criada com sucesso!';
        if (opsReutilizadas.length > 0) {
          mensagem += `\n\n♻️ OPs reutilizadas (evitou duplicação):\n`;
          opsReutilizadas.forEach(op => {
            mensagem += `• OP ${op.numero} - ${op.produto}\n`;
          });
        }

        alert(mensagem);
        closeModal('newOrderModal');
        document.getElementById('newOrderForm').reset();
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    async function explodeComponents(parentOp, estrutura, level = 0, visited = new Set()) {
      if (level > 10) {
        throw new Error('Estrutura muito profunda (possível ciclo)');
      }
      if (visited.has(parentOp.produtoId)) {
        throw new Error('Ciclo detectado na estrutura de produto: ' + parentOp.produtoId);
      }

      // ===== VERIFICAÇÃO ANTI-DUPLICAÇÃO =====
      console.log(`🔍 Verificando OPs existentes antes de explodir componentes (nível ${level})...`);
      const opsExistentes = await verificarOPsExistentes();
      visited.add(parentOp.produtoId);

      const childOrders = [];
      const materialNeeds = [];

      for (const componente of estrutura.componentes) {
        const produto = produtos.find(p => p.id === componente.componentId);
        const quantidadeNecessaria = parentOp.quantidade * componente.quantidade;

        if (produto.tipo === 'SP' || produto.tipo === 'PA') {
          const subEstrutura = estruturas.find(e => e.produtoPaiId === componente.componentId);
          if (subEstrutura) {
            // Verificar saldo de estoque baseado na configuração
            const saldoDisponivel = productionConfig.usarSaldoEstoque ?
              await checkInventory(componente.componentId, parentOp.armazemProducaoId) : 0;

            const quantidadeReservada = productionConfig.reservarEstoque ?
              Math.min(saldoDisponivel, quantidadeNecessaria) : 0;

            const necessidade = productionConfig.usarSaldoEstoque ?
              Math.max(0, quantidadeNecessaria - quantidadeReservada) : quantidadeNecessaria;

            materialNeeds.push({
              produtoId: componente.componentId,
              quantidade: quantidadeNecessaria,
              saldoEstoque: saldoDisponivel,
              quantidadeReservada,
              necessidade,
              tipo: produto.tipo,
              usouConfigEstoque: productionConfig.usarSaldoEstoque,
              reservouEstoque: productionConfig.reservarEstoque
            });

            // ===== VERIFICAÇÃO ANTI-DUPLICAÇÃO PARA SP/PA =====
            let childOp;
            let opExistente = { encontrada: false };

            if (productionConfig.antiDuplicacao) {
              opExistente = await verificarOPCompativel(
                componente.componentId,
                quantidadeNecessaria,
                parentOp.armazemProducaoId,
                parentOp.dataEntrega,
                opsExistentes
              );
            }

            if (opExistente.encontrada) {
              console.log(`♻️ Reutilizando OP existente: ${opExistente.op.numero} para produto ${produto.codigo}`);

              childOp = opExistente.op;

              // Se precisar ajustar quantidade
              if (opExistente.precisaAjuste) {
                console.log(`📊 Ajustando quantidade da OP ${childOp.numero}: ${childOp.quantidade} → ${opExistente.novaQuantidade}`);

                await updateDoc(doc(db, "ordensProducao", childOp.id), {
                  quantidade: opExistente.novaQuantidade,
                  observacoes: (childOp.observacoes || '') + `\n[${new Date().toLocaleString()}] Quantidade ajustada de ${childOp.quantidade} para ${opExistente.novaQuantidade} devido à nova OP pai ${parentOp.numero}`
                });

                childOp.quantidade = opExistente.novaQuantidade;
              }

              // Vincular à OP pai
              await updateDoc(doc(db, "ordensProducao", childOp.id), {
                opPaiId: parentOp.id,
                observacoes: (childOp.observacoes || '') + `\n[${new Date().toLocaleString()}] Vinculada à OP pai ${parentOp.numero}`
              });

            } else {
              // Criar nova OP filha
              console.log(`🆕 Criando nova OP filha para produto ${produto.codigo}`);

              childOp = {
                numero: await generateOrderNumber(),
                produtoId: componente.componentId,
                produtoPaiId: parentOp.produtoId,
                quantidade: quantidadeNecessaria,
                dataEntrega: parentOp.dataEntrega,
                status: 'Pendente',
                nivel: level + 1,
                prioridade: parentOp.prioridade,
                centroCustoId: parentOp.centroCustoId,
                dataCriacao: Timestamp.now(),
                armazemProducaoId: parentOp.armazemProducaoId,
                opPaiId: parentOp.id,
                observacoes: `Criada automaticamente para OP pai ${parentOp.numero}`
              };

              const childDocRef = await addDoc(collection(db, "ordensProducao"), childOp);
              childOp.id = childDocRef.id;
            }

            childOrders.push(childOp);
            await explodeComponents(childOp, subEstrutura, level + 1, new Set(visited));
          }
        } else {
          // Materiais (MP) - aplicar configurações de estoque
          const saldoDisponivel = productionConfig.usarSaldoEstoque ?
            await checkInventory(componente.componentId, parentOp.armazemProducaoId) : 0;

          const quantidadeReservada = productionConfig.reservarEstoque ?
            Math.min(saldoDisponivel, quantidadeNecessaria) : 0;

          const necessidade = productionConfig.usarSaldoEstoque ?
            Math.max(0, quantidadeNecessaria - quantidadeReservada) : quantidadeNecessaria;

          materialNeeds.push({
            produtoId: componente.componentId,
            quantidade: quantidadeNecessaria,
            saldoEstoque: saldoDisponivel,
            quantidadeReservada,
            necessidade,
            tipo: produto.tipo,
            usouConfigEstoque: productionConfig.usarSaldoEstoque,
            reservouEstoque: productionConfig.reservarEstoque
          });

          // Efetuar reserva se configurado
          if (productionConfig.reservarEstoque && quantidadeReservada > 0) {
            await updateInventoryReservation(componente.componentId, quantidadeReservada, parentOp.armazemProducaoId);
          }
        }
      }

      if (materialNeeds.length > 0) {
        await updateDoc(doc(db, "ordensProducao", parentOp.id), {
          materiaisNecessarios: materialNeeds
        });
      }

      return childOrders;
    }

    async function checkInventory(produtoId, armazemId) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      const saldoTotal = estoque ? estoque.saldo : 0;
      const saldoReservado = estoque ? (estoque.saldoReservado || 0) : 0;
      const saldoEmpenhado = estoque ? (estoque.saldoEmpenhado || 0) : 0;
      return saldoTotal - saldoReservado - saldoEmpenhado;
    }

    // ===== VERIFICAÇÃO DE OPs ABERTAS PARA SUBPRODUTOS =====
    async function verificarOPsAbertasParaSPs(estrutura, quantidadeNecessaria) {
      const opsAbertas = [];
      const spsSemOP = [];

      try {
        console.log('🔍 Verificando OPs abertas para subprodutos necessários...');

        // Buscar todas as OPs em status ativo (incluindo mais status)
        const opsQuery = query(
          collection(db, "ordensProducao"),
          where("status", "in", ["Pendente", "Aberta", "Aguardando Material", "Em Produção", "Material Transferido"])
        );

        const opsSnapshot = await getDocs(opsQuery);
        const opsExistentes = opsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 Encontradas ${opsExistentes.length} OPs ativas no sistema`);

        // Verificar cada componente da estrutura
        for (const componente of estrutura.componentes) {
          const produto = produtos.find(p => p.id === componente.componentId);

          if (!produto) continue;

          // Verificar se é subproduto (SP) - critérios corrigidos
          const ehSubproduto = produto.codigo?.includes('-SP') ||
                              produto.codigo?.includes('SP-') ||
                              produto.codigo?.includes('-ALH-') ||
                              produto.tipo === 'SP' ||
                              produto.categoria === 'SUBPRODUTO' ||
                              // Códigos numéricos específicos de SP (não MPs como 100863)
                              (produto.tipo === 'SP' && produto.codigo?.length >= 4 && /^\d+$/.test(produto.codigo));

          console.log(`🔍 Verificando ${produto.codigo}: ehSubproduto = ${ehSubproduto}, tipo = ${produto.tipo}, categoria = ${produto.categoria}`);

          if (ehSubproduto) {
            const quantidadeNecessariaSP = componente.quantidade * quantidadeNecessaria;

            // Buscar OPs existentes para este SP (busca corrigida)
            const opsDoSP = opsExistentes.filter(op => {
              // Priorizar busca por ID do produto (que é o correto)
              const matchPorProdutoId = op.produtoId === produto.id;
              const matchPorComponentId = op.produtoId === componente.componentId;
              const matchPorCodigo = op.produtoId === produto.codigo;

              const match = matchPorProdutoId || matchPorComponentId || matchPorCodigo;

              if (match) {
                console.log(`  ✅ Match encontrado: OP ${op.numero}`);
                console.log(`    - OP.produtoId: ${op.produtoId}`);
                console.log(`    - produto.id: ${produto.id}`);
                console.log(`    - componente.componentId: ${componente.componentId}`);
                console.log(`    - produto.codigo: ${produto.codigo}`);
                console.log(`    - Match por: ${matchPorProdutoId ? 'produto.id' : matchPorComponentId ? 'componentId' : 'codigo'}`);
              }

              return match;
            });

            console.log(`🔍 SP ${produto.codigo} (ID: ${componente.componentId}): Encontradas ${opsDoSP.length} OPs`);
            console.log(`   - Produto ID: ${produto.id}`);
            console.log(`   - Component ID: ${componente.componentId}`);
            console.log(`   - Produto Código: ${produto.codigo}`);

            if (opsDoSP.length > 0) {
              opsDoSP.forEach(op => {
                console.log(`  - OP ${op.numero}: ${op.quantidade} (${op.status}) - ProdutoID: ${op.produtoId}`);
              });
            } else {
              console.log(`  ❌ Nenhuma OP encontrada para ${produto.codigo}`);
              // Debug: mostrar todas as OPs para comparação
              console.log(`  🔍 Debug - Todas as OPs disponíveis:`);
              opsExistentes.forEach(op => {
                console.log(`    - OP ${op.numero}: ProdutoID=${op.produtoId}, Status=${op.status}`);
              });
            }

            if (opsDoSP.length > 0) {
              // Calcular quantidade total em produção
              const quantidadeEmProducao = opsDoSP.reduce((total, op) =>
                total + (op.quantidade || 0), 0
              );

              opsAbertas.push({
                produtoId: componente.componentId,
                codigo: produto.codigo,
                descricao: produto.descricao,
                quantidadeNecessaria: quantidadeNecessariaSP,
                quantidadeEmProducao: quantidadeEmProducao,
                ops: opsDoSP.map(op => ({
                  id: op.id,
                  numero: op.numero,
                  quantidade: op.quantidade,
                  status: op.status,
                  dataInicio: op.dataInicio
                })),
                suficiente: quantidadeEmProducao >= quantidadeNecessariaSP
              });
            } else {
              spsSemOP.push({
                produtoId: componente.componentId,
                codigo: produto.codigo,
                descricao: produto.descricao,
                quantidadeNecessaria: quantidadeNecessariaSP
              });
            }
          }
        }

        return {
          opsAbertas,
          spsSemOP,
          temSPsNecessarios: opsAbertas.length > 0 || spsSemOP.length > 0,
          todosSpsCobertos: spsSemOP.length === 0 && opsAbertas.every(sp => sp.suficiente)
        };

      } catch (error) {
        console.error('❌ Erro ao verificar OPs abertas para SPs:', error);
        return {
          opsAbertas: [],
          spsSemOP: [],
          temSPsNecessarios: false,
          todosSpsCobertos: false,
          erro: error.message
        };
      }
    }

    // ===== MODAL PROFISSIONAL PARA DECISÃO DE OP =====
    function mostrarModalDecisaoOP(detalhes, materiaisSP, verificacaoSPs) {
      return new Promise((resolve) => {
        // Criar modal
        const modal = document.createElement('div');
        modal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          backdrop-filter: blur(5px);
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border-radius: 20px;
          max-width: 1200px;
          width: 95%;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          box-shadow: 0 25px 50px rgba(0,0,0,0.25);
          border: 1px solid rgba(255,255,255,0.2);
          animation: modalSlideIn 0.3s ease-out;
          overflow: hidden;
          position: relative;
        `;

        // Adicionar animação CSS
        if (!document.getElementById('modalAnimationStyle')) {
          const style = document.createElement('style');
          style.id = 'modalAnimationStyle';
          style.textContent = `
            @keyframes modalSlideIn {
              from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
              }
              to {
                opacity: 1;
                transform: translateY(0) scale(1);
              }
            }
          `;
          document.head.appendChild(style);
        }

        // Header do Modal
        const header = document.createElement('div');
        header.style.cssText = `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 25px 30px;
          border-radius: 20px 20px 0 0;
          position: relative;
          overflow: hidden;
          flex-shrink: 0;
        `;

        const headerContent = document.createElement('div');
        headerContent.style.cssText = `
          position: relative;
          z-index: 2;
        `;

        const titulo = document.createElement('h2');
        titulo.innerHTML = '<i class="fas fa-analytics" style="margin-right: 10px;"></i>ANÁLISE DE MATERIAIS';
        titulo.style.cssText = `
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        `;

        const subtitulo = document.createElement('p');
        subtitulo.textContent = 'Verificação de disponibilidade e geração de ordens de produção';
        subtitulo.style.cssText = `
          margin: 0;
          font-size: 14px;
          opacity: 0.9;
          font-weight: 300;
        `;

        // Padrão decorativo no header
        const pattern = document.createElement('div');
        pattern.style.cssText = `
          position: absolute;
          top: 0;
          right: 0;
          width: 200px;
          height: 100%;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
          opacity: 0.3;
        `;

        headerContent.appendChild(titulo);
        headerContent.appendChild(subtitulo);
        header.appendChild(headerContent);
        header.appendChild(pattern);

        // Container principal do conteúdo
        const mainContent = document.createElement('div');
        mainContent.style.cssText = `
          padding: 30px;
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
          scrollbar-width: thin;
          scrollbar-color: #cbd5e0 #f7fafc;
          min-height: 0;
          max-height: calc(90vh - 200px);
          position: relative;
        `;

        // Estilizar scrollbar para webkit browsers
        const scrollbarStyle = document.createElement('style');
        if (!document.getElementById('modalScrollbarStyle')) {
          scrollbarStyle.id = 'modalScrollbarStyle';
          scrollbarStyle.textContent = `
            .modal-scroll-content::-webkit-scrollbar {
              width: 12px;
              background: transparent;
            }
            .modal-scroll-content::-webkit-scrollbar-track {
              background: #f8f9fa;
              border-radius: 10px;
              margin: 10px 0;
              border: 1px solid #e9ecef;
            }
            .modal-scroll-content::-webkit-scrollbar-thumb {
              background: linear-gradient(135deg, #6c757d, #495057);
              border-radius: 10px;
              border: 2px solid #f8f9fa;
              transition: all 0.3s ease;
            }
            .modal-scroll-content::-webkit-scrollbar-thumb:hover {
              background: linear-gradient(135deg, #495057, #343a40);
              transform: scale(1.1);
            }
            .modal-scroll-content::-webkit-scrollbar-corner {
              background: transparent;
            }

            /* Para Firefox */
            .modal-scroll-content {
              scrollbar-width: auto;
              scrollbar-color: #6c757d #f8f9fa;
            }
          `;
          document.head.appendChild(scrollbarStyle);
        }

        mainContent.className = 'modal-scroll-content';

        // Processar e organizar os detalhes
        const detalhesOrganizados = organizarDetalhesAnalise(detalhes);

        // Seção de Materiais-Primas Insuficientes
        if (detalhesOrganizados.materiaisPrimas.length > 0) {
          const secaoMP = criarSecaoMateriais('MATÉRIAS-PRIMAS INSUFICIENTES', detalhesOrganizados.materiaisPrimas, '#dc3545', 'fas fa-exclamation-triangle');
          mainContent.appendChild(secaoMP);
        }

        // Seção de Subprodutos sem OP
        if (detalhesOrganizados.subprodutosSemOP.length > 0) {
          const secaoSP = criarSecaoMateriais('SUBPRODUTOS SEM OP ABERTA', detalhesOrganizados.subprodutosSemOP, '#fd7e14', 'fas fa-industry');
          mainContent.appendChild(secaoSP);
        }

        // Função para organizar detalhes da análise
        function organizarDetalhesAnalise(detalhes) {
          const linhas = detalhes.split('\n');
          const resultado = {
            materiaisPrimas: [],
            subprodutosSemOP: []
          };

          let secaoAtual = null;

          linhas.forEach(linha => {
            linha = linha.trim();
            if (linha.includes('MATÉRIAS-PRIMAS INSUFICIENTES:')) {
              secaoAtual = 'materiaisPrimas';
            } else if (linha.includes('SUBPRODUTOS SEM OP ABERTA:')) {
              secaoAtual = 'subprodutosSemOP';
            } else if (linha.startsWith('•') && secaoAtual) {
              // Extrair informações do item
              const match = linha.match(/• (\w+): Necessário ([\d.]+), Disponível ([\d.]+) \(Falta: ([\d.]+)\)/);
              if (match) {
                resultado[secaoAtual].push({
                  codigo: match[1],
                  necessario: parseFloat(match[2]),
                  disponivel: parseFloat(match[3]),
                  falta: parseFloat(match[4])
                });
              }
            }
          });

          return resultado;
        }

        // Função para criar seção de materiais
        function criarSecaoMateriais(titulo, materiais, cor, icone) {
          const secao = document.createElement('div');
          secao.style.cssText = `
            margin-bottom: 25px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid ${cor}20;
          `;

          const cabecalho = document.createElement('div');
          cabecalho.style.cssText = `
            background: linear-gradient(135deg, ${cor} 0%, ${cor}dd 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
          `;
          cabecalho.innerHTML = `<i class="${icone}" style="margin-right: 10px;"></i>${titulo}`;

          const conteudo = document.createElement('div');
          conteudo.style.cssText = `
            background: white;
            padding: 0;
          `;

          materiais.forEach((material, index) => {
            const item = document.createElement('div');
            item.style.cssText = `
              padding: 15px 20px;
              border-bottom: ${index < materiais.length - 1 ? '1px solid #f0f0f0' : 'none'};
              display: flex;
              justify-content: space-between;
              align-items: center;
              transition: background-color 0.2s ease;
            `;
            item.onmouseover = () => item.style.backgroundColor = '#f8f9fa';
            item.onmouseout = () => item.style.backgroundColor = 'white';

            const info = document.createElement('div');
            info.innerHTML = `
              <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">${material.codigo}</div>
              <div style="font-size: 14px; color: #6c757d;">
                Necessário: <span style="font-weight: 500;">${material.necessario.toFixed(3)}</span> |
                Disponível: <span style="font-weight: 500;">${material.disponivel.toFixed(3)}</span>
              </div>
            `;

            const falta = document.createElement('div');
            falta.style.cssText = `
              background: ${cor}15;
              color: ${cor};
              padding: 8px 12px;
              border-radius: 20px;
              font-weight: 600;
              font-size: 14px;
              border: 1px solid ${cor}30;
            `;
            falta.textContent = `Falta: ${material.falta.toFixed(3)}`;

            item.appendChild(info);
            item.appendChild(falta);
            conteudo.appendChild(item);
          });

          secao.appendChild(cabecalho);
          secao.appendChild(conteudo);
          return secao;
        }

        // Container para opções de ação (fixo na parte inferior)
        const opcoesDiv = document.createElement('div');
        opcoesDiv.style.cssText = `
          padding: 20px 30px 30px 30px;
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border-top: 1px solid #e9ecef;
          display: flex;
          flex-direction: column;
          gap: 15px;
          flex-shrink: 0;
          border-radius: 0 0 20px 20px;
          margin-top: auto;
        `;

        // Seção de configuração para geração de OPs (só se houver SPs sem OP)
        if (materiaisSP.length > 0) {
          // Container para configurações de geração
          const configDiv = document.createElement('div');
          configDiv.style.cssText = `
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #28a745;
            position: relative;
            overflow: hidden;
          `;

          // Ícone decorativo
          const iconeDecorativo = document.createElement('div');
          iconeDecorativo.style.cssText = `
            position: absolute;
            top: -10px;
            right: -10px;
            width: 60px;
            height: 60px;
            background: #28a74520;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #28a745;
          `;
          iconeDecorativo.innerHTML = '<i class="fas fa-industry"></i>';
          configDiv.appendChild(iconeDecorativo);

          // Título da seção
          const tituloConfig = document.createElement('h3');
          tituloConfig.innerHTML = '<i class="fas fa-cogs" style="margin-right: 10px;"></i>CONFIGURAÇÃO DE GERAÇÃO DE OPs';
          tituloConfig.style.cssText = `
            margin: 0 0 20px 0;
            color: #28a745;
            font-size: 18px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          `;
          configDiv.appendChild(tituloConfig);

          // Container para as listas de OPs
          const containerListas = document.createElement('div');
          containerListas.style.cssText = `
            display: grid;
            gap: 20px;
            margin-bottom: 20px;
          `;

          // Mostrar OPs que serão criadas
          if (materiaisSP.length > 0) {
            const secaoNovasOPs = document.createElement('div');
            secaoNovasOPs.style.cssText = `
              background: white;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;

            const headerNovas = document.createElement('div');
            headerNovas.style.cssText = `
              background: linear-gradient(135deg, #007bff, #0056b3);
              color: white;
              padding: 12px 16px;
              font-weight: 600;
              font-size: 14px;
            `;
            headerNovas.innerHTML = '<i class="fas fa-plus-circle" style="margin-right: 8px;"></i>NOVAS OPs QUE SERÃO CRIADAS';

            const listaNovas = document.createElement('div');
            listaNovas.style.cssText = `padding: 0;`;

            materiaisSP.forEach((sp, index) => {
              const quantidadeComMargem = Math.ceil(sp.necessario * 1.1);
              const item = document.createElement('div');
              item.style.cssText = `
                padding: 12px 16px;
                border-bottom: ${index < materiaisSP.length - 1 ? '1px solid #f0f0f0' : 'none'};
                display: flex;
                justify-content: space-between;
                align-items: center;
              `;

              item.innerHTML = `
                <div>
                  <div style="font-weight: 600; color: #2c3e50;">${sp.codigo}</div>
                  <div style="font-size: 12px; color: #6c757d;">Necessário: ${sp.necessario.toFixed(3)}</div>
                </div>
                <div style="background: #007bff15; color: #007bff; padding: 6px 12px; border-radius: 15px; font-weight: 600; font-size: 13px;">
                  ${quantidadeComMargem} unidades
                </div>
              `;

              listaNovas.appendChild(item);
            });

            secaoNovasOPs.appendChild(headerNovas);
            secaoNovasOPs.appendChild(listaNovas);
            containerListas.appendChild(secaoNovasOPs);
          }

          // Mostrar OPs existentes que serão aproveitadas
          if (verificacaoSPs?.opsAbertas?.length > 0) {
            const secaoExistentes = document.createElement('div');
            secaoExistentes.style.cssText = `
              background: white;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;

            const headerExistentes = document.createElement('div');
            headerExistentes.style.cssText = `
              background: linear-gradient(135deg, #28a745, #20c997);
              color: white;
              padding: 12px 16px;
              font-weight: 600;
              font-size: 14px;
            `;
            headerExistentes.innerHTML = '<i class="fas fa-recycle" style="margin-right: 8px;"></i>OPs EXISTENTES QUE SERÃO APROVEITADAS';

            const listaExistentes = document.createElement('div');
            listaExistentes.style.cssText = `padding: 0;`;

            verificacaoSPs.opsAbertas.forEach((sp, index) => {
              const item = document.createElement('div');
              item.style.cssText = `
                padding: 12px 16px;
                border-bottom: ${index < verificacaoSPs.opsAbertas.length - 1 ? '1px solid #f0f0f0' : 'none'};
              `;

              const falta = sp.suficiente ? 0 : sp.quantidadeNecessaria - sp.quantidadeEmProducao;
              const statusCor = sp.suficiente ? '#28a745' : '#ffc107';
              const statusTexto = sp.suficiente ? 'Suficiente' : `Falta: ${falta.toFixed(3)}`;

              item.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                  <div>
                    <div style="font-weight: 600; color: #2c3e50;">${sp.codigo}</div>
                    <div style="font-size: 12px; color: #6c757d;">Em produção: ${sp.quantidadeEmProducao.toFixed(3)}</div>
                  </div>
                  <div style="background: ${statusCor}15; color: ${statusCor}; padding: 4px 8px; border-radius: 12px; font-weight: 600; font-size: 12px;">
                    ${statusTexto}
                  </div>
                </div>
                <div style="margin-left: 16px;">
                  ${sp.ops.map(op => `
                    <div style="font-size: 11px; color: #6c757d; margin-bottom: 2px;">
                      OP ${op.numero}: ${op.quantidade.toFixed(3)} (${op.status})
                    </div>
                  `).join('')}
                </div>
              `;

              listaExistentes.appendChild(item);
            });

            secaoExistentes.appendChild(headerExistentes);
            secaoExistentes.appendChild(listaExistentes);
            containerListas.appendChild(secaoExistentes);
          }

          configDiv.appendChild(containerListas);

          // Checkbox para confirmar geração
          const checkboxContainer = document.createElement('div');
          checkboxContainer.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e9ecef;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
          `;

          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.id = 'confirmarGeracao';
          checkbox.style.cssText = `
            width: 20px;
            height: 20px;
            cursor: pointer;
            margin-right: 15px;
            accent-color: #28a745;
          `;

          const label = document.createElement('label');
          label.htmlFor = 'confirmarGeracao';
          label.innerHTML = `
            <div style="display: flex; align-items: center;">
              <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px; font-size: 18px;"></i>
              <div>
                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">
                  Confirmo que desejo gerar as OPs listadas acima
                </div>
                <div style="font-size: 12px; color: #6c757d;">
                  Esta ação criará novas ordens de produção no sistema
                </div>
              </div>
            </div>
          `;
          label.style.cssText = `
            cursor: pointer;
            user-select: none;
            display: block;
            width: 100%;
          `;

          // Efeitos visuais do checkbox
          checkbox.addEventListener('change', function() {
            if (this.checked) {
              checkboxContainer.style.borderColor = '#28a745';
              checkboxContainer.style.backgroundColor = '#28a74508';
              checkboxContainer.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';
            } else {
              checkboxContainer.style.borderColor = '#e9ecef';
              checkboxContainer.style.backgroundColor = 'white';
              checkboxContainer.style.boxShadow = 'none';
            }
          });

          checkboxContainer.appendChild(checkbox);
          checkboxContainer.appendChild(label);
          configDiv.appendChild(checkboxContainer);

          // Observação sobre margem de segurança
          const observacao = document.createElement('div');
          observacao.style.cssText = `
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
          `;
          observacao.innerHTML = `
            <i class="fas fa-lightbulb" style="color: #856404; font-size: 18px;"></i>
            <div>
              <div style="font-weight: 600; color: #856404; margin-bottom: 4px;">Margem de Segurança</div>
              <div style="font-size: 13px; color: #856404;">
                As quantidades incluem 10% de margem de segurança para garantir disponibilidade
              </div>
            </div>
          `;
          configDiv.appendChild(observacao);

          opcoesDiv.appendChild(configDiv);

          // Botão Gerar OPs (inicialmente desabilitado)
          const btnGerar = document.createElement('button');
          btnGerar.innerHTML = `
            <i class="fas fa-industry" style="margin-right: 10px;"></i>
            GERAR OPs
            <div style="font-size: 12px; font-weight: 400; margin-top: 4px; opacity: 0.9;">
              Criar ordens de produção para subprodutos
            </div>
          `;
          btnGerar.style.cssText = `
            padding: 20px 25px;
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: not-allowed;
            transition: all 0.3s ease;
            opacity: 0.6;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
          `;

          // Função para atualizar estado do botão
          const atualizarBotao = () => {
            if (checkbox.checked) {
              btnGerar.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
              btnGerar.style.cursor = 'pointer';
              btnGerar.style.opacity = '1';
              btnGerar.style.boxShadow = '0 8px 25px rgba(40, 167, 69, 0.3)';
              btnGerar.onmouseover = () => {
                btnGerar.style.transform = 'translateY(-3px)';
                btnGerar.style.boxShadow = '0 12px 35px rgba(40, 167, 69, 0.4)';
              };
              btnGerar.onmouseout = () => {
                btnGerar.style.transform = 'translateY(0)';
                btnGerar.style.boxShadow = '0 8px 25px rgba(40, 167, 69, 0.3)';
              };
            } else {
              btnGerar.style.background = 'linear-gradient(135deg, #6c757d, #5a6268)';
              btnGerar.style.cursor = 'not-allowed';
              btnGerar.style.opacity = '0.6';
              btnGerar.style.boxShadow = 'none';
              btnGerar.onmouseover = null;
              btnGerar.onmouseout = null;
              btnGerar.style.transform = 'translateY(0)';
            }
          };

          // Event listener para checkbox
          checkbox.addEventListener('change', atualizarBotao);

          // Event listener para botão
          btnGerar.onclick = () => {
            if (checkbox.checked) {
              modal.style.animation = 'modalSlideOut 0.3s ease-in forwards';
              setTimeout(() => {
                document.body.removeChild(modal);
                resolve('gerar');
              }, 300);
            } else {
              // Animação de shake para indicar erro
              checkboxContainer.style.animation = 'shake 0.5s ease-in-out';
              setTimeout(() => {
                checkboxContainer.style.animation = '';
              }, 500);

              // Adicionar estilo de shake se não existir
              if (!document.getElementById('shakeAnimation')) {
                const shakeStyle = document.createElement('style');
                shakeStyle.id = 'shakeAnimation';
                shakeStyle.textContent = `
                  @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                  }
                `;
                document.head.appendChild(shakeStyle);
              }
            }
          };

          opcoesDiv.appendChild(btnGerar);
        }

        // Container para botões de ação
        const botoesContainer = document.createElement('div');
        botoesContainer.style.cssText = `
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin-top: 20px;
        `;

        // Botão Criar Mesmo Assim
        const btnCriar = document.createElement('button');
        btnCriar.innerHTML = `
          <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
          CRIAR MESMO ASSIM
          <div style="font-size: 12px; font-weight: 400; margin-top: 4px; opacity: 0.9;">
            OP ficará pendente de material
          </div>
        `;
        btnCriar.style.cssText = `
          padding: 20px 25px;
          background: linear-gradient(135deg, #ffc107, #fd7e14);
          color: white;
          border: none;
          border-radius: 15px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
        `;
        btnCriar.onmouseover = () => {
          btnCriar.style.transform = 'translateY(-3px)';
          btnCriar.style.boxShadow = '0 10px 30px rgba(255, 193, 7, 0.4)';
        };
        btnCriar.onmouseout = () => {
          btnCriar.style.transform = 'translateY(0)';
          btnCriar.style.boxShadow = '0 6px 20px rgba(255, 193, 7, 0.3)';
        };
        btnCriar.onclick = () => {
          modal.style.animation = 'modalSlideOut 0.3s ease-in forwards';
          setTimeout(() => {
            document.body.removeChild(modal);
            resolve('criar');
          }, 300);
        };

        // Botão Cancelar
        const btnCancelar = document.createElement('button');
        btnCancelar.innerHTML = `
          <i class="fas fa-times" style="margin-right: 8px;"></i>
          CANCELAR
          <div style="font-size: 12px; font-weight: 400; margin-top: 4px; opacity: 0.9;">
            Não criar a OP
          </div>
        `;
        btnCancelar.style.cssText = `
          padding: 20px 25px;
          background: linear-gradient(135deg, #dc3545, #c82333);
          color: white;
          border: none;
          border-radius: 15px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
        `;
        btnCancelar.onmouseover = () => {
          btnCancelar.style.transform = 'translateY(-3px)';
          btnCancelar.style.boxShadow = '0 10px 30px rgba(220, 53, 69, 0.4)';
        };
        btnCancelar.onmouseout = () => {
          btnCancelar.style.transform = 'translateY(0)';
          btnCancelar.style.boxShadow = '0 6px 20px rgba(220, 53, 69, 0.3)';
        };
        btnCancelar.onclick = () => {
          modal.style.animation = 'modalSlideOut 0.3s ease-in forwards';
          setTimeout(() => {
            document.body.removeChild(modal);
            resolve('cancelar');
          }, 300);
        };

        botoesContainer.appendChild(btnCriar);
        botoesContainer.appendChild(btnCancelar);
        opcoesDiv.appendChild(botoesContainer);

        // Adicionar animação de saída
        const styleElement = document.getElementById('modalAnimationStyle');
        if (styleElement && !styleElement.textContent.includes('modalSlideOut')) {
          styleElement.textContent += `
            @keyframes modalSlideOut {
              from {
                opacity: 1;
                transform: translateY(0) scale(1);
              }
              to {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
              }
            }
          `;
        }

        // Montar modal
        modalContent.appendChild(header);
        modalContent.appendChild(mainContent);
        modalContent.appendChild(opcoesDiv);
        modal.appendChild(modalContent);

        // Fechar com ESC
        const handleKeydown = (e) => {
          if (e.key === 'Escape') {
            modal.style.animation = 'modalSlideOut 0.3s ease-in forwards';
            setTimeout(() => {
              document.body.removeChild(modal);
              document.removeEventListener('keydown', handleKeydown);
              resolve('cancelar');
            }, 300);
          }
        };
        document.addEventListener('keydown', handleKeydown);

        // Fechar clicando fora do modal
        modal.onclick = (e) => {
          if (e.target === modal) {
            modal.style.animation = 'modalSlideOut 0.3s ease-in forwards';
            setTimeout(() => {
              document.body.removeChild(modal);
              document.removeEventListener('keydown', handleKeydown);
              resolve('cancelar');
            }, 300);
          }
        };

        // Mostrar modal
        document.body.appendChild(modal);

        // Função para verificar se há conteúdo para rolar
        const verificarRolagem = () => {
          setTimeout(() => {
            const isScrollable = mainContent.scrollHeight > mainContent.clientHeight;
            if (isScrollable) {
              console.log('Modal tem conteúdo para rolar');
            }
          }, 100);
        };

        // Focar no primeiro elemento interativo e adicionar indicadores
        setTimeout(() => {
          const firstButton = modal.querySelector('button, input[type="checkbox"]');
          if (firstButton) firstButton.focus();
          verificarRolagem();

          // Adicionar indicador de rolagem se necessário
          if (mainContent.scrollHeight > mainContent.clientHeight) {
            const scrollIndicator = document.createElement('div');
            scrollIndicator.style.cssText = `
              position: absolute;
              top: 50%;
              right: 5px;
              transform: translateY(-50%);
              background: rgba(108, 117, 125, 0.8);
              color: white;
              padding: 8px 4px;
              border-radius: 15px;
              font-size: 12px;
              z-index: 1001;
              pointer-events: none;
              transition: opacity 0.3s ease;
            `;
            scrollIndicator.innerHTML = '⇅';
            modalContent.appendChild(scrollIndicator);

            // Ocultar indicador após alguns segundos
            setTimeout(() => {
              scrollIndicator.style.opacity = '0.5';
            }, 3000);

            // Ocultar quando rolar
            mainContent.addEventListener('scroll', () => {
              scrollIndicator.style.opacity = '0.3';
            });
          }
        }, 100);
      });
    }

    // ===== FUNÇÃO PARA GERAR OPs PARA SUBPRODUTOS =====
    async function gerarOPsParaSubprodutos(materiaisSP, verificacaoSPs) {
      const opsGeradas = [];

      try {
        console.log('🏭 Iniciando geração de OPs para subprodutos...');

        for (const materialSP of materiaisSP) {
          console.log(`🔄 Gerando OP para SP: ${materialSP.codigo}`);

          // Buscar estrutura do subproduto
          const estruturaSP = estruturas.find(e => e.produtoId === materialSP.produtoId);
          if (!estruturaSP) {
            console.warn(`⚠️ Estrutura não encontrada para SP: ${materialSP.codigo}`);
            continue;
          }

          // Calcular quantidade com margem de segurança (10%)
          const quantidadeComMargem = Math.ceil(materialSP.necessario * 1.1);

          // Verificar se já existe OP insuficiente
          const spComOPInsuficiente = verificacaoSPs.opsAbertas?.find(sp =>
            sp.produtoId === materialSP.produtoId && !sp.suficiente
          );

          let quantidadeAGerar = quantidadeComMargem;

          if (spComOPInsuficiente) {
            // Gerar apenas a diferença
            quantidadeAGerar = quantidadeComMargem - spComOPInsuficiente.quantidadeEmProducao;
            console.log(`📊 SP ${materialSP.codigo}: Já tem ${spComOPInsuficiente.quantidadeEmProducao}, gerando mais ${quantidadeAGerar}`);
          }

          if (quantidadeAGerar <= 0) continue;

          // Criar OP para o subproduto
          const numeroOP = await generateOrderNumber();
          const novaOP = {
            numero: numeroOP,
            produtoId: materialSP.produtoId,
            quantidade: quantidadeAGerar,
            dataEntrega: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias
            status: 'Aguardando Material',
            nivel: 1, // Subproduto
            prioridade: 'ALTA', // Alta prioridade para não bloquear OP principal
            centroCustoId: '1010 - PRODUCAO', // Centro de custo padrão
            observacoes: `OP gerada automaticamente para suprir demanda de SP - Necessário: ${materialSP.necessario.toFixed(3)}`,
            dataCriacao: Timestamp.now(),
            armazemProducaoId: 'PROD1', // Armazém padrão
            tipoOP: 'AUTOMATICA',
            geradaAutomaticamente: true,
            motivoGeracao: 'FALTA_SUBPRODUTO',
            opPrincipalRelacionada: null // Será preenchido depois se necessário
          };

          // Salvar OP no banco
          const docRef = await addDoc(collection(db, "ordensProducao"), novaOP);
          novaOP.id = docRef.id;

          // Explodir componentes da OP do subproduto
          await explodeComponents(novaOP, estruturaSP);

          opsGeradas.push({
            id: novaOP.id,
            numero: numeroOP,
            produto: materialSP.codigo,
            quantidade: quantidadeAGerar,
            status: 'Aguardando Material'
          });

          console.log(`✅ OP ${numeroOP} criada para SP ${materialSP.codigo}: ${quantidadeAGerar} unidades`);
        }

        return opsGeradas;

      } catch (error) {
        console.error('❌ Erro ao gerar OPs para subprodutos:', error);
        alert(`❌ Erro ao gerar OPs para subprodutos: ${error.message}`);
        return [];
      }
    }

    // Função para validar estoque antes de criar OP
    async function validarEstoqueParaCriacaoOP(produtoId, quantidade, armazemId, estrutura) {
      const materiaisInsuficientes = [];
      const alertas = [];
      let podeCrear = true;

      try {
        // ===== 1. VERIFICAR OPs ABERTAS PARA SUBPRODUTOS =====
        const verificacaoSPs = await verificarOPsAbertasParaSPs(estrutura, quantidade);

        // ===== 2. VALIDAR MATERIAIS DA ESTRUTURA =====
        for (const componente of estrutura.componentes) {
          const quantidadeNecessaria = componente.quantidade * quantidade;
          const produto = produtos.find(p => p.id === componente.componentId);

          if (!produto) {
            alertas.push(`⚠️ Produto não encontrado: ${componente.componentId}`);
            continue;
          }

          // Verificar se é subproduto e se tem OP aberta - critérios corrigidos
          const ehSubproduto = produto.codigo?.includes('-SP') ||
                              produto.codigo?.includes('SP-') ||
                              produto.codigo?.includes('-ALH-') ||
                              produto.tipo === 'SP' ||
                              produto.categoria === 'SUBPRODUTO' ||
                              // Códigos numéricos específicos de SP (não MPs como 100863)
                              (produto.tipo === 'SP' && produto.codigo?.length >= 4 && /^\d+$/.test(produto.codigo));

          console.log(`🔍 Material ${produto.codigo}: ehSubproduto = ${ehSubproduto}, tipo = ${produto.tipo}`);

          if (ehSubproduto) {
            const spComOP = verificacaoSPs.opsAbertas.find(sp => sp.produtoId === componente.componentId);

            if (spComOP) {
              if (spComOP.suficiente) {
                alertas.push(`✅ SP ${produto.codigo}: ${spComOP.quantidadeEmProducao.toFixed(3)} em produção (necessário: ${quantidadeNecessaria.toFixed(3)})`);
              } else {
                const faltaSP = quantidadeNecessaria - spComOP.quantidadeEmProducao;
                alertas.push(`⚠️ SP ${produto.codigo}: ${spComOP.quantidadeEmProducao.toFixed(3)} em produção, falta: ${faltaSP.toFixed(3)}`);
              }
              continue; // Pular verificação de estoque para SPs com OP
            }
          }

          // Verificar saldo disponível
          const saldoDisponivel = await checkInventory(componente.componentId, armazemId);

          if (saldoDisponivel < quantidadeNecessaria) {
            const falta = quantidadeNecessaria - saldoDisponivel;

            materiaisInsuficientes.push({
              produtoId: componente.componentId,
              codigo: produto.codigo,
              descricao: produto.descricao,
              tipo: produto.tipo,
              ehSubproduto: ehSubproduto,
              necessario: quantidadeNecessaria,
              disponivel: Math.max(0, saldoDisponivel),
              falta: falta
            });

            podeCrear = false;
          } else if (saldoDisponivel < quantidadeNecessaria * 1.1) {
            // Alerta se estoque está baixo (menos de 10% de margem)
            alertas.push(`⚠️ Estoque baixo: ${produto.codigo} - Disponível: ${saldoDisponivel.toFixed(3)}, Necessário: ${quantidadeNecessaria.toFixed(3)}`);
          }
        }

        return {
          podeCrear,
          materiaisInsuficientes,
          alertas,
          totalMateriais: estrutura.componentes.length,
          materiaisOK: estrutura.componentes.length - materiaisInsuficientes.length,
          // Adicionar informações sobre SPs
          verificacaoSPs: verificacaoSPs
        };

      } catch (error) {
        console.error('Erro na validação de estoque:', error);
        return {
          podeCrear: false,
          materiaisInsuficientes: [],
          alertas: [`❌ Erro na validação: ${error.message}`],
          totalMateriais: 0,
          materiaisOK: 0
        };
      }
    }

    // ===== FUNÇÃO DE TESTE ESPECÍFICA PARA OP 006-ALH-200 =====
    window.testarDeteccaoOP006 = async function() {
      try {
        console.log('🔍 Testando detecção específica da OP 006-ALH-200...');

        // 1. Buscar produto 006-ALH-200
        const produto006 = produtos.find(p => p.codigo === '006-ALH-200');
        if (!produto006) {
          alert('❌ Produto 006-ALH-200 não encontrado');
          return;
        }

        console.log('✅ Produto 006-ALH-200 encontrado:', produto006);

        // 2. Buscar OPs com diferentes critérios
        const opsQuery = query(
          collection(db, "ordensProducao"),
          where("status", "in", ["Pendente", "Aberta", "Aguardando Material", "Em Produção", "Material Transferido"])
        );

        const opsSnapshot = await getDocs(opsQuery);
        const todasOPs = opsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 Total de OPs ativas: ${todasOPs.length}`);

        // 3. Buscar especificamente por 006-ALH-200
        const opsPorId = todasOPs.filter(op => op.produtoId === produto006.id);
        const opsPorCodigo = todasOPs.filter(op => op.produtoId === '006-ALH-200');
        const opsComNumero = todasOPs.filter(op => op.numero && op.numero.includes('25070893'));

        console.log('🔍 Resultados da busca:');
        console.log(`   - Por ID (${produto006.id}): ${opsPorId.length} OPs`);
        console.log(`   - Por código (006-ALH-200): ${opsPorCodigo.length} OPs`);
        console.log(`   - Por número (25070893): ${opsComNumero.length} OPs`);

        // 4. Mostrar detalhes de todas as OPs encontradas
        const todasEncontradas = [...opsPorId, ...opsPorCodigo, ...opsComNumero];
        const opsUnicas = todasEncontradas.filter((op, index, self) =>
          index === self.findIndex(o => o.id === op.id)
        );

        let relatorio = `🔍 TESTE DE DETECÇÃO OP 006-ALH-200\n\n`;
        relatorio += `📦 PRODUTO:\n`;
        relatorio += `• ID: ${produto006.id}\n`;
        relatorio += `• Código: ${produto006.codigo}\n`;
        relatorio += `• Nome: ${produto006.nome}\n\n`;

        relatorio += `📊 BUSCA:\n`;
        relatorio += `• Total de OPs ativas: ${todasOPs.length}\n`;
        relatorio += `• OPs por ID: ${opsPorId.length}\n`;
        relatorio += `• OPs por código: ${opsPorCodigo.length}\n`;
        relatorio += `• OPs com número 25070893: ${opsComNumero.length}\n`;
        relatorio += `• OPs únicas encontradas: ${opsUnicas.length}\n\n`;

        if (opsUnicas.length > 0) {
          relatorio += `✅ OPs ENCONTRADAS:\n`;
          opsUnicas.forEach(op => {
            relatorio += `• OP ${op.numero}: ${op.quantidade} (${op.status})\n`;
            relatorio += `  └─ ProdutoID: ${op.produtoId}\n`;
            relatorio += `  └─ Armazém: ${op.armazemProducaoId}\n`;
          });
        } else {
          relatorio += `❌ NENHUMA OP ENCONTRADA!\n\n`;
          relatorio += `🔍 PRIMEIRAS 5 OPs PARA DEBUG:\n`;
          todasOPs.slice(0, 5).forEach(op => {
            relatorio += `• OP ${op.numero}: ProdutoID=${op.produtoId}, Status=${op.status}\n`;
          });
        }

        alert(relatorio);

      } catch (error) {
        console.error('❌ Erro no teste:', error);
        alert(`❌ Erro no teste: ${error.message}`);
      }
    };

    // ===== FUNÇÃO DE DIAGNÓSTICO PARA PRODUTO ESPECÍFICO =====
    window.diagnosticarProduto = async function(codigoProduto = '006-ALH-200') {
      try {
        console.log(`🔍 Diagnosticando produto: ${codigoProduto}`);

        // 1. Buscar produto
        const produto = produtos.find(p => p.codigo === codigoProduto || p.id === codigoProduto);
        if (!produto) {
          alert(`❌ Produto ${codigoProduto} não encontrado`);
          return;
        }

        console.log(`✅ Produto encontrado:`, produto);

        // 2. Buscar OPs existentes para este produto
        const opsQuery = query(
          collection(db, "ordensProducao"),
          where("produtoId", "==", produto.id)
        );

        const opsSnapshot = await getDocs(opsQuery);
        const opsExistentes = opsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 OPs encontradas para ${codigoProduto}:`, opsExistentes);

        // 3. Verificar configuração anti-duplicação
        console.log(`🔧 Configuração anti-duplicação:`, productionConfig.antiDuplicacao);

        // 4. Testar função de verificação com armazém real da OP
        const armazemTeste = opsExistentes.length > 0 ? opsExistentes[0].armazemProducaoId : 'PROD-PRODUCAO';
        const testResult = await verificarOPCompativel(
          produto.id,
          1, // quantidade teste
          armazemTeste, // usar armazém da OP existente
          new Date(),
          opsExistentes
        );

        // 5. Testar também com armazém padrão
        const testResultPadrao = await verificarOPCompativel(
          produto.id,
          1, // quantidade teste
          'PROD-PRODUCAO', // armazém padrão
          new Date(),
          opsExistentes
        );

        console.log(`🧪 Resultado do teste de compatibilidade:`, testResult);

        // 5. Gerar relatório
        let relatorio = `🔍 DIAGNÓSTICO: ${codigoProduto}\n\n`;
        relatorio += `📦 PRODUTO:\n`;
        relatorio += `• ID: ${produto.id}\n`;
        relatorio += `• Código: ${produto.codigo}\n`;
        relatorio += `• Nome: ${produto.nome}\n`;
        relatorio += `• Tipo: ${produto.tipo}\n\n`;

        relatorio += `📋 OPs EXISTENTES: ${opsExistentes.length}\n`;
        opsExistentes.forEach(op => {
          relatorio += `• OP ${op.numero}: ${op.quantidade} (${op.status})\n`;
          relatorio += `  └─ Armazém: ${op.armazemProducaoId || 'NÃO DEFINIDO'}\n`;
          if (op.opPaiId) {
            relatorio += `  └─ OP Pai: ${op.opPaiId}\n`;
          }
        });

        relatorio += `\n🔧 CONFIGURAÇÕES:\n`;
        relatorio += `• Anti-duplicação: ${productionConfig.antiDuplicacao ? 'ATIVO' : 'INATIVO'}\n`;
        relatorio += `• Usar saldo estoque: ${productionConfig.usarSaldoEstoque ? 'ATIVO' : 'INATIVO'}\n`;
        relatorio += `• Reservar estoque: ${productionConfig.reservarEstoque ? 'ATIVO' : 'INATIVO'}\n\n`;

        relatorio += `🧪 TESTE DE COMPATIBILIDADE:\n`;
        relatorio += `\n📍 TESTE COM ARMAZÉM DA OP (${armazemTeste}):\n`;
        if (testResult.encontrada) {
          relatorio += `✅ OP compatível encontrada: ${testResult.op.numero}\n`;
          relatorio += `• Quantidade atual: ${testResult.op.quantidade}\n`;
          relatorio += `• Precisa ajuste: ${testResult.precisaAjuste ? 'SIM' : 'NÃO'}\n`;
          if (testResult.motivoReutilizacao) {
            relatorio += `• Motivo: ${testResult.motivoReutilizacao}\n`;
          }
        } else {
          relatorio += `❌ Nenhuma OP compatível encontrada\n`;
          if (testResult.motivo) {
            relatorio += `• Motivo: ${testResult.motivo}\n`;
          }
        }

        relatorio += `\n📍 TESTE COM ARMAZÉM PADRÃO (PROD-PRODUCAO):\n`;
        if (testResultPadrao.encontrada) {
          relatorio += `✅ OP compatível encontrada: ${testResultPadrao.op.numero}\n`;
          relatorio += `• Quantidade atual: ${testResultPadrao.op.quantidade}\n`;
          relatorio += `• Precisa ajuste: ${testResultPadrao.precisaAjuste ? 'SIM' : 'NÃO'}\n`;
          if (testResultPadrao.motivoReutilizacao) {
            relatorio += `• Motivo: ${testResultPadrao.motivoReutilizacao}\n`;
          }
        } else {
          relatorio += `❌ Nenhuma OP compatível encontrada\n`;
          if (testResultPadrao.motivo) {
            relatorio += `• Motivo: ${testResultPadrao.motivo}\n`;
          }
        }

        alert(relatorio);

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        alert(`❌ Erro no diagnóstico: ${error.message}`);
      }
    };

    // ===== FUNÇÃO DE TESTE ESPECÍFICA PARA 1360 =====
    window.testarOP1360 = async function() {
      try {
        console.log('🔍 Testando detecção de OP para produto 1360...');

        // Buscar todas as OPs ativas
        const opsQuery = query(
          collection(db, "ordensProducao"),
          where("status", "in", ["Aguardando Material", "Em Produção", "Material Transferido"])
        );

        const opsSnapshot = await getDocs(opsQuery);
        const opsExistentes = opsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 Total de OPs ativas: ${opsExistentes.length}`);

        // Buscar especificamente OPs do 1360
        const ops1360 = opsExistentes.filter(op => {
          const match = op.produtoId === '1360' ||
                       op.produtoId === 1360 ||
                       op.produto?.codigo === '1360' ||
                       op.codigo === '1360';

          if (match) {
            console.log(`✅ OP encontrada para 1360:`, {
              id: op.id,
              numero: op.numero,
              produtoId: op.produtoId,
              quantidade: op.quantidade,
              status: op.status
            });
          }

          return match;
        });

        // Mostrar todas as OPs para debug
        console.log('📋 Todas as OPs ativas:');
        opsExistentes.forEach(op => {
          console.log(`  - OP ${op.numero}: ProdutoID=${op.produtoId}, Status=${op.status}`);
        });

        let resultado = `🔍 TESTE DE OP PARA 1360:\n\n`;
        resultado += `📊 Total de OPs ativas: ${opsExistentes.length}\n`;
        resultado += `🎯 OPs encontradas para 1360: ${ops1360.length}\n\n`;

        if (ops1360.length > 0) {
          resultado += `✅ OPs DO 1360:\n`;
          ops1360.forEach(op => {
            resultado += `• OP ${op.numero}: ${op.quantidade} (${op.status})\n`;
          });
        } else {
          resultado += `❌ NENHUMA OP ENCONTRADA PARA 1360\n\n`;
          resultado += `🔍 Verificar se:\n`;
          resultado += `• ProdutoId está correto na OP\n`;
          resultado += `• Status da OP está ativo\n`;
          resultado += `• Estrutura do produto está correta\n`;
        }

        alert(resultado);

      } catch (error) {
        console.error('Erro no teste:', error);
        alert(`❌ Erro no teste: ${error.message}`);
      }
    };

    // ===== FUNÇÃO DE TESTE ESPECÍFICA PARA VERIFICAÇÃO DE SPs =====
    window.testarVerificacaoSPs = async function() {
      try {
        console.log('🔍 Testando verificação de SPs...');

        // Buscar produto que usa 006-ALH-200 como componente
        const produtoPai = produtos.find(p => p.codigo === 'C-J05-ALH-200' || p.codigo === 'CJ05-ALH-200');
        if (!produtoPai) {
          alert('❌ Produto pai não encontrado. Teste com outro produto.');
          return;
        }

        console.log('✅ Produto pai encontrado:', produtoPai);

        // Buscar estrutura
        const estrutura = estruturas.find(e => e.produtoId === produtoPai.id);
        if (!estrutura) {
          alert('❌ Estrutura não encontrada para este produto');
          return;
        }

        console.log('✅ Estrutura encontrada:', estrutura);

        // Executar verificação de SPs
        const resultado = await verificarOPsAbertasParaSPs(estrutura, 1);

        console.log('📊 Resultado da verificação:', resultado);

        // Gerar relatório
        let relatorio = `🔍 TESTE DE VERIFICAÇÃO DE SPs\n\n`;
        relatorio += `📦 PRODUTO PAI: ${produtoPai.codigo}\n`;
        relatorio += `📋 COMPONENTES NA ESTRUTURA: ${estrutura.componentes.length}\n\n`;

        relatorio += `📊 RESULTADO:\n`;
        relatorio += `• SPs com OP aberta: ${resultado.opsAbertas.length}\n`;
        relatorio += `• SPs sem OP: ${resultado.spsSemOP.length}\n`;
        relatorio += `• Tem SPs necessários: ${resultado.temSPsNecessarios}\n`;
        relatorio += `• Todos SPs cobertos: ${resultado.todosSpsCobertos}\n\n`;

        if (resultado.opsAbertas.length > 0) {
          relatorio += `✅ SPs COM OP ABERTA:\n`;
          resultado.opsAbertas.forEach(sp => {
            relatorio += `• ${sp.codigo}: ${sp.quantidadeEmProducao} em produção`;
            relatorio += ` (necessário: ${sp.quantidadeNecessaria})`;
            relatorio += ` ${sp.suficiente ? '✅' : '⚠️'}\n`;
            sp.ops.forEach(op => {
              relatorio += `  - OP ${op.numero}: ${op.quantidade} (${op.status})\n`;
            });
          });
          relatorio += '\n';
        }

        if (resultado.spsSemOP.length > 0) {
          relatorio += `❌ SPs SEM OP:\n`;
          resultado.spsSemOP.forEach(sp => {
            relatorio += `• ${sp.codigo}: necessário ${sp.quantidadeNecessaria}\n`;
          });
        }

        // Verificar especificamente o 006-ALH-200
        const sp006 = resultado.opsAbertas.find(sp => sp.codigo === '006-ALH-200') ||
                     resultado.spsSemOP.find(sp => sp.codigo === '006-ALH-200');

        if (sp006) {
          relatorio += `\n🎯 ESPECÍFICO 006-ALH-200:\n`;
          if (resultado.opsAbertas.find(sp => sp.codigo === '006-ALH-200')) {
            relatorio += `✅ DETECTADO COM OP ABERTA!\n`;
            relatorio += `• Quantidade em produção: ${sp006.quantidadeEmProducao}\n`;
            relatorio += `• Quantidade necessária: ${sp006.quantidadeNecessaria}\n`;
            relatorio += `• Suficiente: ${sp006.suficiente ? 'SIM' : 'NÃO'}\n`;
          } else {
            relatorio += `❌ DETECTADO SEM OP (ERRO!)\n`;
            relatorio += `• Quantidade necessária: ${sp006.quantidadeNecessaria}\n`;
          }
        } else {
          relatorio += `\n⚠️ 006-ALH-200 NÃO ENCONTRADO NA ESTRUTURA\n`;
        }

        alert(relatorio);

      } catch (error) {
        console.error('❌ Erro no teste:', error);
        alert(`❌ Erro no teste: ${error.message}`);
      }
    };

    // ===== FUNÇÃO DE TESTE PARA VERIFICAÇÃO DE SPs (ORIGINAL) =====
    window.testarVerificacaoSPsOriginal = async function() {
      const produtoId = prompt('🧪 TESTE DE VERIFICAÇÃO DE SPs\n\nDigite o ID do produto para testar:');
      if (!produtoId) return;

      const quantidade = parseFloat(prompt('Digite a quantidade para testar:') || '1');
      if (quantidade <= 0) return;

      try {
        // Buscar estrutura do produto
        const estrutura = estruturas.find(e => e.produtoId === produtoId);
        if (!estrutura) {
          alert('❌ Estrutura não encontrada para este produto');
          return;
        }

        console.log('🔍 Testando verificação de SPs...');
        const verificacao = await verificarOPsAbertasParaSPs(estrutura, quantidade);

        let resultado = '📊 RESULTADO DA VERIFICAÇÃO:\n\n';

        if (verificacao.opsAbertas.length > 0) {
          resultado += '✅ SUBPRODUTOS COM OP ABERTA:\n';
          verificacao.opsAbertas.forEach(sp => {
            resultado += `• ${sp.codigo}: ${sp.quantidadeEmProducao.toFixed(3)} em produção (necessário: ${sp.quantidadeNecessaria.toFixed(3)})\n`;
            sp.ops.forEach(op => {
              resultado += `  - OP ${op.numero}: ${op.quantidade.toFixed(3)} (${op.status})\n`;
            });
          });
          resultado += '\n';
        }

        if (verificacao.spsSemOP.length > 0) {
          resultado += '🏭 SUBPRODUTOS SEM OP:\n';
          verificacao.spsSemOP.forEach(sp => {
            resultado += `• ${sp.codigo}: Necessário ${sp.quantidadeNecessaria.toFixed(3)} (SEM OP ATIVA)\n`;
          });
          resultado += '\n';
        }

        if (verificacao.opsAbertas.length === 0 && verificacao.spsSemOP.length === 0) {
          resultado += '📦 Nenhum subproduto encontrado na estrutura.\n';
        }

        resultado += `\n🎯 RESUMO:\n`;
        resultado += `• Tem SPs necessários: ${verificacao.temSPsNecessarios ? 'SIM' : 'NÃO'}\n`;
        resultado += `• Todos SPs cobertos: ${verificacao.todosSpsCobertos ? 'SIM' : 'NÃO'}`;

        alert(resultado);

      } catch (error) {
        console.error('Erro no teste:', error);
        alert(`❌ Erro no teste: ${error.message}`);
      }
    };

    // Função para testar a validação de estoque
    window.testarValidacaoEstoque = async function() {
      const produtoId = prompt('🧪 TESTE DE VALIDAÇÃO DE ESTOQUE\n\nDigite o ID do produto para testar:');
      if (!produtoId) return;

      const quantidade = parseFloat(prompt('Digite a quantidade para testar:') || '0');
      if (quantidade <= 0) return;

      const armazemId = prompt('Digite o ID do armazém de produção:');
      if (!armazemId) return;

      try {
        const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
        if (!estrutura) {
          alert('❌ Estrutura não encontrada para este produto.');
          return;
        }

        const validacao = await validarEstoqueParaCriacaoOP(produtoId, quantidade, armazemId, estrutura);

        let resultado = `🧪 RESULTADO DO TESTE\n\n`;
        resultado += `📦 Produto: ${produtoId}\n`;
        resultado += `📊 Quantidade: ${quantidade}\n`;
        resultado += `🏭 Armazém: ${armazemId}\n\n`;

        resultado += `✅ Pode criar OP: ${validacao.podeCrear ? 'SIM' : 'NÃO'}\n`;
        resultado += `📋 Total de materiais: ${validacao.totalMateriais}\n`;
        resultado += `✅ Materiais OK: ${validacao.materiaisOK}\n`;
        resultado += `❌ Materiais insuficientes: ${validacao.materiaisInsuficientes.length}\n\n`;

        if (validacao.materiaisInsuficientes.length > 0) {
          resultado += `❌ MATERIAIS EM FALTA:\n`;
          validacao.materiaisInsuficientes.forEach(m => {
            resultado += `• ${m.codigo}: Falta ${m.falta.toFixed(3)} (Necessário: ${m.necessario.toFixed(3)}, Disponível: ${m.disponivel.toFixed(3)})\n`;
          });
          resultado += `\n`;
        }

        if (validacao.alertas.length > 0) {
          resultado += `⚠️ ALERTAS:\n`;
          validacao.alertas.forEach(a => resultado += `${a}\n`);
        }

        alert(resultado);
        console.log('🧪 Teste de validação:', validacao);

      } catch (error) {
        alert(`❌ Erro no teste: ${error.message}`);
        console.error('Erro no teste:', error);
      }
    };

    // ===== SISTEMA ANTI-DUPLICAÇÃO DE OPs =====

    // Função para verificar OPs existentes no sistema
    async function verificarOPsExistentes() {
      try {
        const ordensSnapshot = await getDocs(collection(db, "ordensProducao"));
        return ordensSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      } catch (error) {
        console.error('Erro ao buscar OPs existentes:', error);
        return [];
      }
    }

    // Função para verificar se existe OP compatível para reutilizar
    async function verificarOPCompativel(produtoId, quantidadeNecessaria, armazemId, dataEntrega, opsExistentes) {
      try {
        console.log(`🔍 Verificando OP compatível para produto ${produtoId}:`);
        console.log(`   - Quantidade necessária: ${quantidadeNecessaria}`);
        console.log(`   - Armazém: ${armazemId}`);
        console.log(`   - Total de OPs existentes: ${opsExistentes.length}`);

        // Buscar OPs do mesmo produto, armazém e status compatível
        const opsCompativeis = opsExistentes.filter(op => {
          const mesmoArmazem = armazemId === null || op.armazemProducaoId === armazemId; // null = qualquer armazém
          const statusCompativel = ['Pendente', 'Aberta', 'Aguardando Material', 'Em Produção'].includes(op.status);
          const naoEhFilha = !op.opPaiId; // Não considerar OPs que já são filhas
          const mesmoProduto = op.produtoId === produtoId;

          console.log(`   OP ${op.numero}: produto=${mesmoProduto}, armazem=${mesmoArmazem}(${op.armazemProducaoId}), status=${statusCompativel}(${op.status}), naoFilha=${naoEhFilha}`);

          return mesmoProduto && mesmoArmazem && statusCompativel && naoEhFilha;
        });

        console.log(`   📋 OPs compatíveis encontradas: ${opsCompativeis.length}`);

        if (opsCompativeis.length === 0) {
          console.log(`   ❌ Nenhuma OP compatível encontrada`);
          return { encontrada: false, motivo: 'Nenhuma OP compatível encontrada' };
        }

        // Ordenar por data de entrega mais próxima
        opsCompativeis.sort((a, b) => {
          const dataA = a.dataEntrega?.toDate ? a.dataEntrega.toDate() : new Date(a.dataEntrega);
          const dataB = b.dataEntrega?.toDate ? b.dataEntrega.toDate() : new Date(b.dataEntrega);
          return dataA - dataB;
        });

        const opMelhor = opsCompativeis[0];
        const quantidadeAtual = opMelhor.quantidade || 0;

        console.log(`   ✅ Melhor OP encontrada: ${opMelhor.numero} (quantidade: ${quantidadeAtual})`);

        // Verificar se precisa ajustar quantidade
        if (quantidadeAtual < quantidadeNecessaria) {
          console.log(`   📊 Quantidade insuficiente, será ajustada: ${quantidadeAtual} → ${quantidadeNecessaria}`);
          return {
            encontrada: true,
            op: opMelhor,
            precisaAjuste: true,
            novaQuantidade: quantidadeNecessaria,
            motivoAjuste: `Quantidade insuficiente: ${quantidadeAtual} < ${quantidadeNecessaria}`
          };
        } else {
          console.log(`   ✅ Quantidade suficiente, será reutilizada: ${quantidadeAtual} >= ${quantidadeNecessaria}`);
          return {
            encontrada: true,
            op: opMelhor,
            precisaAjuste: false,
            motivoReutilizacao: `Quantidade suficiente: ${quantidadeAtual} >= ${quantidadeNecessaria}`
          };
        }

      } catch (error) {
        console.error('❌ Erro ao verificar OP compatível:', error);
        return { encontrada: false, erro: error.message };
      }
    }

    // Função para mostrar relatório de duplicações evitadas
    window.relatorioAntiDuplicacao = async function() {
      try {
        const opsExistentes = await verificarOPsExistentes();

        // Agrupar por produto
        const porProduto = {};
        opsExistentes.forEach(op => {
          if (!porProduto[op.produtoId]) {
            porProduto[op.produtoId] = [];
          }
          porProduto[op.produtoId].push(op);
        });

        let relatorio = '📊 RELATÓRIO ANTI-DUPLICAÇÃO\n\n';
        let totalProdutos = 0;
        let produtosComDuplicacao = 0;

        for (const [produtoId, ops] of Object.entries(porProduto)) {
          totalProdutos++;

          if (ops.length > 1) {
            produtosComDuplicacao++;
            const produto = produtos.find(p => p.id === produtoId);

            relatorio += `🔄 ${produto?.codigo || produtoId} (${ops.length} OPs):\n`;
            ops.forEach(op => {
              relatorio += `  • OP ${op.numero}: ${op.quantidade} - ${op.status}\n`;
            });
            relatorio += '\n';
          }
        }

        relatorio += `📈 RESUMO:\n`;
        relatorio += `• Total de produtos: ${totalProdutos}\n`;
        relatorio += `• Produtos com múltiplas OPs: ${produtosComDuplicacao}\n`;
        relatorio += `• Taxa de duplicação: ${((produtosComDuplicacao / totalProdutos) * 100).toFixed(1)}%`;

        alert(relatorio);

      } catch (error) {
        alert(`❌ Erro no relatório: ${error.message}`);
      }
    };

    // Função para verificar OPs que foram reutilizadas
    async function verificarOPsReutilizadas(opPaiId) {
      try {
        const ordensSnapshot = await getDocs(
          query(
            collection(db, "ordensProducao"),
            where("opPaiId", "==", opPaiId)
          )
        );

        const opsReutilizadas = [];

        for (const doc of ordensSnapshot.docs) {
          const op = { id: doc.id, ...doc.data() };

          // Verificar se a observação indica reutilização
          if (op.observacoes && op.observacoes.includes('Vinculada à OP pai')) {
            const produto = produtos.find(p => p.id === op.produtoId);
            opsReutilizadas.push({
              numero: op.numero,
              produto: produto?.codigo || op.produtoId,
              quantidade: op.quantidade
            });
          }
        }

        return opsReutilizadas;
      } catch (error) {
        console.error('Erro ao verificar OPs reutilizadas:', error);
        return [];
      }
    }

    async function updateInventoryReservation(produtoId, quantidade, armazemId) {
      const estoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === armazemId);
      if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade;
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldoReservado: Math.max(0, novoSaldoReservado),
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
      }
    }

    async function updateInventory(produtoId, quantidade, tipo, centroCustoId, armazemDestino) {
      const produto = produtos.find(p => p.id === produtoId);
      const estoqueRef = estoques.find(e => e.produtoId === produtoId && e.armazemId === (tipo === 'entrada' ? armazemDestino : currentAppointmentOp.armazemProducaoId));

      const armazemFinal = tipo === 'entrada' ? 
        (produto.tipo === 'PA' ? 'QUA01' : currentAppointmentOp.armazemProducaoId) : 
        armazemDestino;

      if (estoqueRef) {
        const novoSaldo = tipo === 'entrada' ? 
          estoqueRef.saldo + quantidade : 
          estoqueRef.saldo - quantidade;

        await updateDoc(doc(db, "estoques", estoqueRef.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });

        estoqueRef.saldo = novoSaldo;
      } else {
        const novoEstoque = {
          produtoId,
          armazemId: armazemFinal,
          saldo: tipo === 'entrada' ? quantidade : -quantidade,
          ultimaMovimentacao: Timestamp.now()
        };

        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        novoEstoque.id = docRef.id;
        estoques.push(novoEstoque);
      }

      await addDoc(collection(db, "movimentacoesEstoque"), {
        produtoId,
        tipo: tipo.toUpperCase(),
        quantidade,
        tipoDocumento: 'PRODUCAO',
        numeroDocumento: currentAppointmentOp.numero,
        centroCustoId,
        observacoes: `${tipo === 'entrada' ? 'Produção' : 'Consumo'} OP ${currentAppointmentOp.numero}`,
        armazem: armazemFinal,
        dataHora: Timestamp.now()
      });
    }

    async function loadActiveOrders() {
      const activeList = document.getElementById('activeOrdersList');
      const completedList = document.getElementById('completedOrdersList');

      activeList.innerHTML = '';
      completedList.innerHTML = '';

      const filteredOrders = filterOrdersLogic();

      for (const op of filteredOrders) {
        const produto = produtos.find(p => p.id === op.produtoId);
        const estoque = estoques.find(e => e.produtoId === op.produtoId && e.armazemId === op.armazemProducaoId);
        const saldoEstoque = estoque ? estoque.saldo : 0;
        const centroCusto = centrosCusto.find(cc => cc.id === op.centroCustoId);
        const armazem = armazens.find(a => a.id === op.armazemProducaoId);

        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="checkbox" class="select-order" data-id="${op.id}"></td>
          <td>${op.numero}</td>
          <td>${produto ? produto.codigo + ' - ' + produto.descricao : '-'}</td>
          <td>${op.quantidade} ${produto ? produto.unidade : ''}</td>
          <td>${op.dataEntrega && typeof op.dataEntrega.seconds === 'number'
            ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()
            : ''}</td>
          <td>${op.prioridade || 'Normal'}</td>
          <td><span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span></td>
          <td>${centroCusto ? centroCusto.codigo : '-'}</td>
          <td>${armazem ? armazem.codigo : '-'}</td>
          <td class="actions">
            <button onclick="viewOrder('${op.id}')" class="secondary-button"><i class="fas fa-eye"></i> Visualizar</button>
            <button onclick="printOrder('${op.id}')" class="secondary-button"><i class="fas fa-print"></i> Imprimir</button>
            ${op.status === 'Pendente' ? `
              <button onclick="iniciarProducao('${op.id}')" class="primary-button"><i class="fas fa-play"></i> Iniciar Produção</button>
              <button onclick="alterarStatusParaFirme('${op.id}')" class="warning-button"><i class="fas fa-check"></i> Para Firme</button>
            ` : ''}
            ${op.status === 'Firme' ? `
              <button onclick="alterarStatusParaPendente('${op.id}')" class="warning-button"><i class="fas fa-edit"></i> Para Pendente</button>
            ` : ''}
            ${op.status !== 'Concluída' && op.status !== 'Cancelada' ? `
              <button onclick="openAppointmentModal('${op.id}')" class="secondary-button"><i class="fas fa-clipboard"></i> Apontar</button>
              <button onclick="cancelOrder('${op.id}')" class="danger-button"><i class="fas fa-trash"></i> Excluir</button>
            ` : ''}
          </td>
        `;

        if (op.status === 'Concluída' || op.status === 'Cancelada') {
          completedList.appendChild(row);
        } else {
          activeList.appendChild(row);
        }
      }

      updateSortIcons('active');
      updateSortIcons('completed');
    }

    function filterOrdersLogic() {
      const generalSearch = document.getElementById('generalSearch').value.toLowerCase();
      const productCode = document.getElementById('productCodeFilter').value.toLowerCase();
      const productDescription = document.getElementById('productDescriptionFilter').value.toLowerCase();
      const productType = document.getElementById('productTypeFilter').value;
      const costCenter = document.getElementById('costCenterFilter').value;
      const warehouse = document.getElementById('warehouseFilter').value;
      const status = document.getElementById('statusFilter').value;
      const date = document.getElementById('dateFilter').value;

      let filteredOrders = [...ordensProducao];

      filteredOrders = filteredOrders.filter(op => {
        const produto = produtos.find(p => p.id === op.produtoId);
        const matchesGeneralSearch = !generalSearch || 
            op.numero.toLowerCase().includes(generalSearch) ||
            (produto && produto.codigo && produto.codigo.toLowerCase().includes(generalSearch)) ||
            (produto && produto.descricao && produto.descricao.toLowerCase().includes(generalSearch));

        const matchesProductCode = !productCode || 
            (produto && produto.codigo && produto.codigo.toLowerCase().includes(productCode));

        const matchesProductDescription = !productDescription || 
            (produto && produto.descricao && produto.descricao.toLowerCase().includes(productDescription));

        const matchesProductType = !productType || 
            (produto && produto.tipo === productType);

        const matchesCostCenter = !costCenter || 
            op.centroCustoId === costCenter;

        const matchesWarehouse = !warehouse || 
            op.armazemProducaoId === warehouse;

        const matchesStatus = !status || 
            op.status.toLowerCase() === status.replace('-', ' ');

        const matchesDate = !date || 
            new Date(op.dataEntrega.seconds * 1000).toISOString().split('T')[0] === date;

        return matchesGeneralSearch && matchesProductCode && matchesProductDescription && 
               matchesProductType && matchesCostCenter && matchesWarehouse && 
               matchesStatus && matchesDate;
      });

      const tab = document.getElementById('activeOrders').classList.contains('active') ? 'active' : 'completed';
      const { field, direction } = sortState[tab];

      filteredOrders.sort((a, b) => {
        let comparison = 0;

        if (field === 'numero') {
          comparison = safeCompare(a.numero, b.numero);
        }
        if (field === 'produto') {
          const prodA = produtos.find(p => p.id === a.produtoId);
          const prodB = produtos.find(p => p.id === b.produtoId);
          const descA = prodA ? (prodA.codigo || '') + (prodA.descricao || '') : '';
          const descB = prodB ? (prodB.codigo || '') + (prodB.descricao || '') : '';
          comparison = safeCompare(descA, descB);
        }
        if (field === 'quantidade') {
          comparison = (a.quantidade || 0) - (b.quantidade || 0);
        }
        if (field === 'dataEntrega') {
          const dateA = a.dataEntrega ? a.dataEntrega.seconds || 0 : 0;
          const dateB = b.dataEntrega ? b.dataEntrega.seconds || 0 : 0;
          comparison = dateA - dateB;
        }
        if (field === 'prioridade') {
          const priorities = { urgente: 3, alta: 2, normal: 1 };
          comparison = (priorities[a.prioridade] || 0) - (priorities[b.prioridade] || 0);
        }
        if (field === 'status') {
          comparison = safeCompare(a.status, b.status);
        }
        if (field === 'centroCusto') {
          const ccA = centrosCusto.find(cc => cc.id === a.centroCustoId);
          const ccB = centrosCusto.find(cc => cc.id === b.centroCustoId);
          const codA = ccA ? ccA.codigo || '' : '';
          const codB = ccB ? ccB.codigo || '' : '';
          comparison = safeCompare(codA, codB);
        }
        if (field === 'armazem') {
          const armA = armazens.find(arm => arm.id === a.armazemProducaoId);
          const armB = armazens.find(arm => arm.id === b.armazemProducaoId);
          const codA = armA ? armA.codigo || '' : '';
          const codB = armB ? armB.codigo || '' : '';
          comparison = safeCompare(codA, codB);
        }

        return direction === 'asc' ? comparison : -comparison;
      });

      return filteredOrders;
    }

    window.sortTable = function(field, tab) {
      if (sortState[tab].field === field) {
        sortState[tab].direction = sortState[tab].direction === 'asc' ? 'desc' : 'asc';
      } else {
        sortState[tab].field = field;
        sortState[tab].direction = 'asc';
      }
      loadActiveOrders();
    };

    function updateSortIcons(tab) {
      const table = document.getElementById(`${tab}OrdersTable`);
      const headers = table.querySelectorAll('th[data-sort]');
      headers.forEach(header => {
        const sortField = header.getAttribute('data-sort');
        const icon = header.querySelector('.sort-icon');
        if (sortState[tab].field === sortField) {
          icon.innerHTML = sortState[tab].direction === 'asc' ? '↑' : '↓';
        } else {
          icon.innerHTML = '';
        }
      });
    }

    window.toggleSelectAll = function(tab) {
      const selectAllCheckbox = document.getElementById(`selectAll${tab.charAt(0).toUpperCase() + tab.slice(1)}`);
      const checkboxes = document.querySelectorAll(`#${tab}OrdersList .select-order`);
      checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
      });
    };

    window.deleteSelectedOrders = async function() {
      const selectedCheckboxes = document.querySelectorAll('.select-order:checked');
      if (selectedCheckboxes.length === 0) {
        alert('Nenhuma ordem selecionada.');
        return;
      }

      if (!confirm(`Tem certeza que deseja excluir ${selectedCheckboxes.length} ordem(s)?`)) {
        return;
      }

      try {
        for (const checkbox of selectedCheckboxes) {
          const opId = checkbox.getAttribute('data-id');
          await cancelOrder(opId);
        }
        alert('Ordens excluídas com sucesso!');
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao excluir ordens:", error);
        alert("Erro ao excluir ordens.");
      }
    };

    window.viewOrder = function(opId) {
      const op = ordensProducao.find(op => op.id === opId);
      const produto = produtos.find(p => p.id === op.produtoId);
      const centroCusto = centrosCusto.find(cc => cc.id === op.centroCustoId);
      const armazem = armazens.find(a => a.id === op.armazemProducaoId);
      const pedido = op.pedidoId ? pedidosVenda.find(p => p.id === op.pedidoId) : null;

      // Modificação na exibição dos materiais para mostrar corretamente os SPs e PAs
      let componentsHtml = '';
      if (op.materiaisNecessarios) {
        componentsHtml = '<h3>Materiais Necessários</h3><ul>';
        for (const material of op.materiaisNecessarios) {
          const materialProduto = produtos.find(p => p.id === material.produtoId);
          if (!materialProduto) continue;

          // Verificar se é SP ou PA para exibição diferenciada
          const isSPorPA = materialProduto.tipo === 'SP' || materialProduto.tipo === 'PA' || 
                          material.tipo === 'SP' || material.tipo === 'PA';
          const tipoLabel = isSPorPA ? `(${materialProduto.tipo})` : '';

          componentsHtml += `
            <li>
              ${materialProduto.codigo} - ${materialProduto.descricao} ${tipoLabel}<br>
              Necessário: ${material.quantidade} ${materialProduto.unidade} |
              Estoque: ${material.saldoEstoque} ${materialProduto.unidade} |
              Falta: ${material.necessidade} ${materialProduto.unidade}
              ${isSPorPA ? ' <span class="badge bg-info">Ordem de Produção Filha Gerada</span>' : ''}
            </li>`;
        }
        componentsHtml += '</ul>';
      }

      const progress = op.quantidadeProduzida ? 
        (op.quantidadeProduzida / op.quantidade * 100).toFixed(1) : 0;

      document.getElementById('viewOrderDetails').innerHTML = `
        <p><strong>Número da Ordem:</strong> ${op.numero}</p>
        <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
        <p><strong>Quantidade:</strong> ${op.quantidade} ${produto.unidade}</p>
        <p><strong>Data de Entrega:</strong> ${new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()}</p>
        <p><strong>Prioridade:</strong> ${op.prioridade || 'Normal'}</p>
        <p><strong>Status:</strong> <span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span></p>
        <p><strong>Centro de Custo:</strong> ${centroCusto ? centroCusto.codigo : '-'}</p>
        <p><strong>Armazém de Produção:</strong> ${armazem ? armazem.codigo : '-'}</p>
        ${op.pedidoId ? `<p><strong>Pedido:</strong> ${pedido.numero}</p>` : ''}
        ${op.produtoPaiId ? `<p><strong>Produto Pai:</strong> ${produtos.find(p => p.id === op.produtoPaiId)?.codigo || '-'}</p>` : ''}
        ${op.quantidadeProduzida ? `
          <p><strong>Progresso:</strong> ${progress}%</p>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progress}%"></div>
          </div>
        ` : ''}
        ${componentsHtml}
      `;

      document.getElementById('viewOrderModal').style.display = 'block';
    };

    window.printOrder = function(opId) {
      const op = ordensProducao.find(op => op.id === opId);
      if (!op) {
        alert('Ordem não encontrada.');
        return;
      }

      const produto = produtos.find(p => p.id === op.produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === op.produtoId);
      const now = new Date();
      const dataAtual = now.toLocaleDateString();
      const horaAtual = now.toLocaleTimeString();

      // Verifica se está bloqueada para impressão (falta de material)
      let bloqueada = false;
      let motivoBloqueio = '';
      if (op.materiaisNecessarios && op.materiaisNecessarios.some(m => m.necessidade > 0)) {
        bloqueada = true;
        motivoBloqueio = 'Saldo insuficiente de matéria prima';
      }

      // Tabela de materiais
      let materiaisHtml = '';
      if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
        materiaisHtml = op.materiaisNecessarios.map(m => {
          const matProd = produtos.find(p => p.id === m.produtoId);
          return `<tr>
            <td>${matProd?.codigo || ''}</td>
            <td>${matProd?.descricao || ''}</td>
            <td>${matProd?.tipo || ''}</td>
            <td>${m.quantidade}</td>
            <td>${matProd?.unidade || ''}</td>
          </tr>`;
        }).join('');
      }

      // Tabela de roteiro de produção (igual relatorio_ordens_setor)
      let roteiroHtml = '';
      if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
        roteiroHtml = estrutura.operacoes.sort((a, b) => a.sequencia - b.sequencia).map(opr => {
          const operacao = window.operacoes ? window.operacoes.find(o => o.id === opr.operacaoId) : { operacao: opr.operacao || '' };
          const recurso = window.recursos ? window.recursos.find(r => r.id === opr.recursoId) : { codigo: opr.recurso || '', maquina: '' };
          return `<tr>
            <td>${opr.sequencia}</td>
            <td>${operacao?.operacao || ''}</td>
            <td>${recurso?.codigo || ''}${recurso?.maquina ? ' - ' + recurso.maquina : ''}</td>
            <td>${opr.tempo || ''}</td>
            <td>${opr.descricao || ''}</td>
            <td></td>
            <td></td>
            <td></td>
          </tr>`;
        }).join('');
      }

      const printWindow = window.open('', '_blank');
      printWindow.document.write(`
        <html>
          <head>
            <title>Ordem de Produção ${op.numero}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #fff; font-size: 12px; }
              .order-page { background-color: white; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
              .order-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px; border-bottom: 1px solid #000; padding-bottom: 5px; }
              .logo { width: 100px; height: auto; }
              .order-title { text-align: center; flex-grow: 1; margin: 0 10px; }
              .order-title h1 { margin: 0; font-size: 18px; }
              .order-title h2 { margin: 3px 0; font-size: 16px; }
              .order-info { display: grid; grid-template-columns: repeat(4, 1fr); gap: 5px; margin-bottom: 15px; border: 1px solid #ccc; padding: 5px; }
              .info-item { border: 1px solid #ddd; padding: 3px; }
              .info-item strong { display: block; font-size: 8px; color: #666; }
              .info-item span { display: block; font-size: 12px; margin-top: 1px; }
              .section { margin-bottom: 15px; }
              .section-title { background-color: #f0f0f0; padding: 3px 8px; font-weight: bold; border: 1px solid #ccc; font-size: 11px; }
              table { width: 100%; border-collapse: collapse; margin-top: 3px; font-size: 11px; }
              th, td { border: 1px solid #ccc; padding: 4px; text-align: left; }
              th { background-color: #f8f9fa; font-weight: bold; }
              .signatures { margin-top: 20px; display: flex; justify-content: space-between; }
              .signature { flex: 1; margin: 0 10px; text-align: center; }
              .signature-line { width: 100%; border-top: 1px solid #000; margin-top: 20px; padding-top: 3px; font-size: 10px; }
              .status-badge { display: inline-block; padding: 1px 4px; border-radius: 2px; font-size: 10px; font-weight: bold; }
              .status-pendente { background-color: #ffc107; color: #000; }
              .status-em-producao { background-color: #17a2b8; color: #fff; }
              .status-concluida { background-color: #28a745; color: #fff; }
              .status-cancelada { background-color: #dc3545; color: #fff; }
              .order-warning { margin: 10px 0; }
              .order-warning strong { color: #e9730c; }
            </style>
          </head>
          <body>
            <div class="order-page">
              <div class="order-header">
                <img src="https://www.naliteck.com.br/img/logo.png" alt="Logo" class="logo">
                <div class="order-title">
                  <h1>ORDEM DE PRODUÇÃO</h1>
                  <h2>${op.numero}</h2>
                </div>
                <div style="text-align: right; font-size: 10px;">
                  <strong>Data: </strong>${dataAtual}<br>
                  <strong>Hora: </strong>${horaAtual}
                </div>
              </div>
              <div class="order-info">
                <div class="info-item">
                  <strong>Produto:</strong>
                  <span>${produto?.codigo || ''} - ${produto?.descricao || ''}</span>
                </div>
                <div class="info-item">
                  <strong>Tipo:</strong>
                  <span>${produto?.tipo || ''}</span>
                </div>
                <div class="info-item">
                  <strong>Quantidade:</strong>
                  <span>${op.quantidade} ${produto?.unidade || ''}</span>
                </div>
                <div class="info-item">
                  <strong>Status:</strong>
                  <span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span>
                </div>
                <div class="info-item">
                  <strong>Data de Criação:</strong>
                  <span>${op.dataCriacao ? (op.dataCriacao.seconds ? new Date(op.dataCriacao.seconds * 1000).toLocaleDateString() : '') : ''}</span>
                </div>
                <div class="info-item">
                  <strong>Data de Entrega:</strong>
                  <span>${op.dataEntrega ? (op.dataEntrega.seconds ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : '') : ''}</span>
                </div>
                <div class="info-item">
                  <strong>Prioridade:</strong>
                  <span>${op.prioridade || 'Normal'}</span>
                </div>
              </div>
              ${op.materiaisNecessarios && op.materiaisNecessarios.length > 0 ? `
                <div class="section">
                  <div class="section-title">LISTA DE MATERIAIS</div>
                  <table>
                    <thead>
                      <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Tipo</th>
                        <th>Quantidade</th>
                        <th>Unidade</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${materiaisHtml}
                    </tbody>
                  </table>
                </div>
              ` : ''}
              ${roteiroHtml ? `
                <div class="section">
                  <div class="section-title">ROTEIRO DE PRODUÇÃO</div>
                  <table>
                    <thead>
                      <tr>
                        <th>Seq.</th>
                        <th>Operação</th>
                        <th>Recurso</th>
                        <th>Tempo (min)</th>
                        <th>Descrição</th>
                        <th>Status</th>
                        <th>Data Início</th>
                        <th>Data Fim</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${roteiroHtml}
                    </tbody>
                  </table>
                </div>
              ` : ''}
              <div class="signatures">
                <div class="signature"><div class="signature-line">Produção</div></div>
                <div class="signature"><div class="signature-line">Qualidade</div></div>
                <div class="signature"><div class="signature-line">Supervisor</div></div>
              </div>
              ${bloqueada ? `<div class="order-warning"><strong>OP ${op.numero} - Bloqueada para Impressão</strong><br>${motivoBloqueio}</div>` : ''}
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    };

    window.openAppointmentModal = async function(opId) {
      currentAppointmentOp = ordensProducao.find(op => op.id === opId);
      if (!currentAppointmentOp.armazemProducaoId) {
        alert('A ordem de produção deve estar associada a um armazém do tipo Produção.');
        return;
      }
      const armazemProducao = armazens.find(a => a.id === currentAppointmentOp.armazemProducaoId);
      if (!armazemProducao || armazemProducao.tipo !== 'PRODUCAO') {
        alert('A ordem de produção deve estar associada a um armazém do tipo Produção.');
        return;
      }

      const produto = produtos.find(p => p.id === currentAppointmentOp.produtoId);

      document.getElementById('appointmentDetails').innerHTML = `
        <div class="order-info">
          <p><strong>Ordem:</strong> ${currentAppointmentOp.numero}</p>
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade Total:</strong> ${currentAppointmentOp.quantidade} ${produto.unidade}</p>
          <p><strong>Quantidade Já Produzida:</strong> ${currentAppointmentOp.quantidadeProduzida || 0} ${produto.unidade}</p>
          <p><strong>Armazém de Produção:</strong> ${armazemProducao.codigo} - ${armazemProducao.nome}</p>
        </div>`;

      document.getElementById('producedQuantity').value = '';
      document.getElementById('scrapQuantity').value = '0';
      document.getElementById('appointmentObservations').value = '';
      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.submitAppointment = async function(event) {
      event.preventDefault();
      if (!currentAppointmentOp) return;

      const producedQuantity = parseFloat(document.getElementById('producedQuantity').value);
      const scrapQuantity = parseFloat(document.getElementById('scrapQuantity').value) || 0;
      const observations = document.getElementById('appointmentObservations').value;

      if (!producedQuantity) {
        alert('Por favor, informe a quantidade produzida.');
        return;
      }

      const totalProduzido = (currentAppointmentOp.quantidadeProduzida || 0) + producedQuantity;
      if (totalProduzido > currentAppointmentOp.quantidade) {
        alert('A quantidade total produzida não pode exceder a quantidade da ordem.');
        return;
      }

      try {
        // 🔄 CONSUMIR MATERIAIS EMPENHADOS
        if (currentAppointmentOp.materiaisNecessarios) {
          const consumos = [];

          for (const material of currentAppointmentOp.materiaisNecessarios) {
            const consumoReal = (material.quantidade / currentAppointmentOp.quantidade) * producedQuantity;

            // Preparar dados para consumo de empenho
            consumos.push({
              produtoId: material.produtoId,
              quantidade: consumoReal
            });

            // Manter lógica antiga para compatibilidade (reservas)
            const reservaReal = (material.quantidadeReservada || 0) / currentAppointmentOp.quantidade * producedQuantity;

            await updateInventory(material.produtoId, consumoReal, 'saida', currentAppointmentOp.centroCustoId, currentAppointmentOp.armazemProducaoId);
            if (reservaReal > 0) {
              await updateInventoryReservation(material.produtoId, -reservaReal, currentAppointmentOp.armazemProducaoId);
            }
          }

          // ⚡ CONSUMIR EMPENHOS (se existirem)
          try {
            const resultadoConsumo = await EmpenhoService.consumirMaterialEmpenhado(currentAppointmentOp.id, consumos);
            if (resultadoConsumo.consumosRealizados > 0) {
              console.log(`✅ ${resultadoConsumo.consumosRealizados} empenho(s) consumido(s)`);
            }
            if (resultadoConsumo.erros.length > 0) {
              console.warn('⚠️ Erros no consumo de empenhos:', resultadoConsumo.erros);
            }
          } catch (empenhoError) {
            console.warn('⚠️ Erro ao consumir empenhos (continuando com apontamento):', empenhoError);
          }
        }

        const produtoApontado = produtos.find(p => p.id === currentAppointmentOp.produtoId);
        const armazemDestino = produtoApontado.tipo === 'PA' ? 'QUA01' : currentAppointmentOp.armazemProducaoId;

        await updateInventory(currentAppointmentOp.produtoId, producedQuantity, 'entrada', currentAppointmentOp.centroCustoId, armazemDestino);

        let novoStatus = 'Em Produção';
        if (totalProduzido >= currentAppointmentOp.quantidade) {
          novoStatus = 'Concluída';
        }

        const opRef = doc(db, "ordensProducao", currentAppointmentOp.id);
        await updateDoc(opRef, {
          status: novoStatus,
          quantidadeProduzida: totalProduzido,
          quantidadeRefugo: (currentAppointmentOp.quantidadeRefugo || 0) + scrapQuantity,
          ultimoApontamento: {
            quantidade: producedQuantity,
            refugo: scrapQuantity,
            observacoes: observations,
            data: Timestamp.now()
          }
        });

        // 🔓 LIBERAR EMPENHOS RESTANTES (se OP foi finalizada)
        if (novoStatus === 'Concluída') {
          try {
            const resultadoLiberacao = await EmpenhoService.liberarEmpenhosRestantes(currentAppointmentOp.id, 'OP_FINALIZADA');
            if (resultadoLiberacao.liberacoes > 0) {
              console.log(`✅ ${resultadoLiberacao.liberacoes} empenho(s) liberado(s) - OP finalizada`);
            }
          } catch (liberacaoError) {
            console.warn('⚠️ Erro ao liberar empenhos (OP finalizada):', liberacaoError);
          }

          if (currentAppointmentOp.pedidoId) {
            await updateDoc(doc(db, "pedidosVenda", currentAppointmentOp.pedidoId), {
              status: 'Concluído'
            });
          }
        }

        closeModal('appointmentModal');
        await loadActiveOrders();
        alert('Apontamento registrado com sucesso!');
      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert("Erro ao registrar apontamento.");
      }
    };

    window.cancelOrder = async function(opId) {
      if (!confirm('Tem certeza que deseja cancelar esta ordem de produção?')) {
        return;
      }

      try {
        const ordem = ordensProducao.find(op => op.id === opId);

        if (ordem.materiaisNecessarios) {
          for (const material of ordem.materiaisNecessarios) {
            if (material.quantidadeReservada > 0) {
              await updateInventoryReservation(material.produtoId, -material.quantidadeReservada, ordem.armazemProducaoId);
            }
          }
        }

        await updateDoc(doc(db, "ordensProducao", opId), {
          status: 'Cancelada',
          dataCancelamento: Timestamp.now()
        });

        // 🔓 LIBERAR EMPENHOS (se existirem)
        try {
          const resultadoLiberacao = await EmpenhoService.liberarEmpenhosRestantes(opId, 'OP_CANCELADA');
          if (resultadoLiberacao.liberacoes > 0) {
            console.log(`✅ ${resultadoLiberacao.liberacoes} empenho(s) liberado(s) - OP cancelada`);
          }
        } catch (liberacaoError) {
          console.warn('⚠️ Erro ao liberar empenhos (OP cancelada):', liberacaoError);
        }

        if (ordem.pedidoId) {
          await updateDoc(doc(db, "pedidosVenda", ordem.pedidoId), {
            status: 'Pendente'
          });
        }

        await loadActiveOrders();
        alert('Ordem cancelada com sucesso!');
      } catch (error) {
        console.error("Erro ao cancelar ordem:", error);
        alert("Erro ao cancelar ordem.");
      }
    };

    window.openNewOrderModal = function() {
      document.getElementById('newOrderForm').reset();
      document.getElementById('newOrderModal').style.display = 'block';
    };

    window.openOrderFromSalesModal = function() {
      document.getElementById('salesOrderSelect').value = '';
      document.getElementById('salesOrderDetails').innerHTML = '';
      document.getElementById('salesOrderModal').style.display = 'block';
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
      if (modalId === 'appointmentModal') {
        currentAppointmentOp = null;
      }
    };

    window.switchTab = function(tab) {
      const tabs = ['active', 'completed'];
      tabs.forEach(t => {
        document.getElementById(`${t}Orders`).classList.toggle('active', t === tab);
        document.querySelector(`button[onclick="switchTab('${t}')"]`).classList.toggle('active', t === tab);
      });
      loadActiveOrders();
    };

    window.filterOrders = function() {
      loadActiveOrders();
    };

    // Função para iniciar produção (transferir reservas para empenhos)
    window.iniciarProducao = async function(opId) {
      if (!confirm('🚀 Iniciar produção desta OP?\n\nIsso irá transferir as reservas para empenhos.')) {
        return;
      }

      try {
        console.log(`🚀 Iniciando produção da OP: ${opId}`);

        // Transferir reservas para empenhos
        const resultado = await EmpenhoService.transferirReservasParaEmpenhos(opId);

        if (resultado.erros.length > 0) {
          console.warn('⚠️ Erros durante transferência:', resultado.erros);
          alert(`⚠️ Produção iniciada com ${resultado.erros.length} erro(s):\n${resultado.erros.join('\n')}`);
        } else {
          alert(`✅ Produção iniciada com sucesso!\n${resultado.transferencias} material(is) empenhado(s).`);
        }

        await loadActiveOrders();
      } catch (error) {
        console.error("❌ Erro ao iniciar produção:", error);
        alert("❌ Erro ao iniciar produção: " + error.message);
      }
    };

    // Função para alterar status de Firme para Pendente
    window.alterarStatusParaPendente = async function(opId) {
      if (!confirm('🔄 Alterar status desta OP de "Firme" para "Pendente"?\n\nEsta ação pode ser revertida.')) {
        return;
      }

      try {
        let usuarioLogado = {};
        try {
          usuarioLogado = JSON.parse(localStorage.getItem('currentUser') || '{}');
        } catch (e) {
          console.warn('Erro ao obter usuário do localStorage:', e);
        }

        const nomeUsuario = usuarioLogado.nome || usuarioLogado.email || 'Sistema';
        console.log(`🔄 Alterando status da OP ${opId} para Pendente (usuário: ${nomeUsuario})`);

        await updateDoc(doc(db, "ordensProducao", opId), {
          status: 'Pendente',
          tipoOP: 'PLANEJADA',
          dataAlteracaoStatus: Timestamp.now(),
          usuarioAlteracaoStatus: nomeUsuario
        });

        // Atualizar o array local
        const op = ordensProducao.find(o => o.id === opId);
        if (op) {
          op.status = 'Pendente';
          op.tipoOP = 'PLANEJADA';
        }

        alert('✅ Status alterado para "Pendente" com sucesso!');
        await loadActiveOrders();
      } catch (error) {
        console.error("❌ Erro ao alterar status:", error);
        alert("❌ Erro ao alterar status: " + error.message);
      }
    };

    // Função para alterar status de Pendente para Firme
    window.alterarStatusParaFirme = async function(opId) {
      if (!confirm('✅ Alterar status desta OP de "Pendente" para "Firme"?\n\nEsta ação pode ser revertida.')) {
        return;
      }

      try {
        let usuarioLogado = {};
        try {
          usuarioLogado = JSON.parse(localStorage.getItem('currentUser') || '{}');
        } catch (e) {
          console.warn('Erro ao obter usuário do localStorage:', e);
        }

        const nomeUsuario = usuarioLogado.nome || usuarioLogado.email || 'Sistema';
        console.log(`✅ Alterando status da OP ${opId} para Firme (usuário: ${nomeUsuario})`);

        await updateDoc(doc(db, "ordensProducao", opId), {
          status: 'Firme',
          tipoOP: 'FIRME',
          dataAlteracaoStatus: Timestamp.now(),
          usuarioAlteracaoStatus: nomeUsuario
        });

        // Atualizar o array local
        const op = ordensProducao.find(o => o.id === opId);
        if (op) {
          op.status = 'Firme';
          op.tipoOP = 'FIRME';
        }

        alert('✅ Status alterado para "Firme" com sucesso!');
        await loadActiveOrders();
      } catch (error) {
        console.error("❌ Erro ao alterar status:", error);
        alert("❌ Erro ao alterar status: " + error.message);
      }
    };

    window.estornarApontamento = async function(opId) {
      if (!confirm('Tem certeza que deseja estornar o último apontamento? Esta ação não pode ser desfeita.')) {
        return;
      }

      try {
        const op = ordensProducao.find(o => o.id === opId);
        if (!op || !op.ultimoApontamento) {
          alert('Nenhum apontamento encontrado para estorno.');
          return;
        }

        const novaQuantidadeProduzida = (op.quantidadeProduzida || 0) - op.ultimoApontamento.quantidade;
        const novaQuantidadeRefugo = (op.quantidadeRefugo || 0) - (op.ultimoApontamento.refugo || 0);

        if (op.materiaisNecessarios) {
          for (const material of op.materiaisNecessarios) {
            const consumoReal = (material.quantidade / op.quantidade) * op.ultimoApontamento.quantidade;

            await updateInventory(material.produtoId, consumoReal, 'entrada', op.centroCustoId, op.armazemProducaoId);

            if (material.quantidadeReservada) {
              await updateInventoryReservation(material.produtoId, consumoReal, op.armazemProducaoId);
            }
          }
        }

        await updateInventory(op.produtoId, op.ultimoApontamento.quantidade, 'saida', op.centroCustoId, op.armazemProducaoId);

        const novoStatus = novaQuantidadeProduzida > 0 ? 'Em Produção' : 'Pendente';

        await updateDoc(doc(db, "ordensProducao", opId), {
          status: novoStatus,
          quantidadeProduzida: novaQuantidadeProduzida,
          quantidadeRefugo: novaQuantidadeRefugo,
          ultimoApontamento: null,
          dataEstorno: Timestamp.now()
        });

        alert('Apontamento estornado com sucesso!');
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao estornar apontamento:", error);
        alert("Erro ao estornar apontamento: " + error.message);
      }
    };

    window.onclick = function(event) {
      const modals = ['newOrderModal', 'salesOrderModal', 'appointmentModal', 'viewOrderModal'];
      modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target == modal) {
          closeModal(modalId);
        }
      });
    };

    async function createOrderFromSimulation(params) {
      try {
        const produtoId = params.get('produtoId');
        const quantidade = parseFloat(params.get('quantidade'));
        const dataEntrega = params.get('dataEntrega');
        const prioridade = params.get('prioridade');
        const centroCustoId = params.get('centroCustoId');
        const armazemId = params.get('armazemId');

        const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
        if (!estrutura) {
          alert('Erro: Estrutura não encontrada para o produto.');
          return;
        }

        const parentOp = {
          numero: await generateOrderNumber(),
          produtoId: produtoId,
          quantidade: quantidade,
          dataEntrega: new Date(dataEntrega),
          status: 'Pendente',
          nivel: 0,
          prioridade: prioridade,
          centroCustoId: centroCustoId,
          dataCriacao: Timestamp.now(),
          armazemProducaoId: armazemId
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');

        // Limpar os parâmetros da URL
        window.history.replaceState({}, document.title, window.location.pathname);

        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    }

    async function buscarProduto() {
      const codigo = document.getElementById('productCode').value.trim();
      if (!codigo) return;

      try {
        const produtosRef = collection(db, 'produtos');
        const q = query(produtosRef, where('codigo', '==', codigo));
        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          alert('Produto não encontrado');
          return;
        }

        const produto = { id: snapshot.docs[0].id, ...snapshot.docs[0].data() };

        // Verifica se o produto tem opcionais
        if (produto.opcionais && produto.opcionais.length > 0) {
          await mostrarSelecaoOpcionais(produto);
        }

        preencherDadosProduto(produto);
      } catch (error) {
        console.error('Erro ao buscar produto:', error);
        alert('Erro ao buscar produto');
      }
    }

    async function mostrarSelecaoOpcionais(produto) {
      const modal = document.getElementById('modalOpcionais');
      const lista = document.getElementById('listaOpcionais');
      lista.innerHTML = '';

      // Busca saldo em estoque de cada opcional
      for (const opcional of produto.opcionais) {
        const saldo = await buscarSaldoEstoque(opcional.produtoId);
        const quantidadeNecessaria = calcularQuantidadeNecessaria(opcional.produtoId);

        const item = document.createElement('div');
        item.className = 'opcional-item';
        item.innerHTML = `
          <input type="radio" 
                 name="opcional" 
                 value="${opcional.produtoId}" 
                 class="opcional-radio"
                 ${saldo < quantidadeNecessaria ? 'disabled' : ''}>
          <div class="opcional-info">
            <div class="opcional-produto">${opcional.codigo} - ${opcional.descricao}</div>
            <div class="opcional-prioridade">Prioridade: ${opcional.prioridade}</div>
            <div class="opcional-saldo ${saldo < quantidadeNecessaria ? 'baixo' : 'ok'}">
              Saldo em estoque: ${saldo} ${saldo < quantidadeNecessaria ? '(Insuficiente)' : ''}
            </div>
            ${opcional.obs ? `<div class="opcional-obs">${opcional.obs}</div>` : ''}
          </div>
        `;
        lista.appendChild(item);
      }

      modal.style.display = 'block';
    }

    async function buscarSaldoEstoque(produtoId) {
      try {
        const estoqueRef = doc(db, 'estoques', produtoId);
        const estoqueDoc = await getDoc(estoqueRef);
        return estoqueDoc.exists() ? (estoqueDoc.data().saldo || 0) : 0;
      } catch (error) {
        console.error('Erro ao buscar saldo:', error);
        return 0;
      }
    }

    function calcularQuantidadeNecessaria(produtoId) {
      // Implementar lógica para calcular quantidade necessária
      // baseado na quantidade da OP e na estrutura do produto
      return 1; // Placeholder
    }

    function confirmarOpcionais() {
      const selecionado = document.querySelector('input[name="opcional"]:checked');
      if (!selecionado) {
        alert('Selecione um componente opcional');
        return;
      }

      // Armazena o opcional selecionado para usar na criação da OP
      window.opcionalSelecionado = selecionado.value;
      fecharModalOpcionais();
    }

    function fecharModalOpcionais() {
      document.getElementById('modalOpcionais').style.display = 'none';
    }

    async function criarOP(event) {
      event.preventDefault();

      try {
        const codigo = document.getElementById('productCode').value;
        const quantidade = parseFloat(document.getElementById('quantity').value);
        const dataPrevisao = document.getElementById('dueDate').value;
        const observacoes = document.getElementById('observations').value;
        const setor = document.getElementById('sector').value;

        if (!codigo || !quantidade || !dataPrevisao || !setor) {
          alert('Preencha todos os campos obrigatórios');
          return;
        }

        // Busca o produto
        const produtosRef = collection(db, 'produtos');
        const q = query(produtosRef, where('codigo', '==', codigo));
        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          alert('Produto não encontrado');
          return;
        }

        const produto = { id: snapshot.docs[0].id, ...snapshot.docs[0].data() };

        // Prepara os dados da OP
        const opData = {
          produtoId: produto.id,
          codigo: produto.codigo,
          descricao: produto.descricao,
          quantidade,
          dataPrevisao,
          observacoes,
          setor,
          status: 'pendente',
          dataCriacao: serverTimestamp(),
          usuarioCriacao: auth.currentUser.uid,
          opcionalSelecionado: window.opcionalSelecionado || null
        };

        // Se tem opcional selecionado, busca seus dados
        if (window.opcionalSelecionado) {
          const opcionalRef = doc(db, 'produtos', window.opcionalSelecionado);
          const opcionalDoc = await getDoc(opcionalRef);
          if (opcionalDoc.exists()) {
            opData.opcionalDados = {
              id: opcionalDoc.id,
              codigo: opcionalDoc.data().codigo,
              descricao: opcionalDoc.data().descricao
            };
          }
        }

        // Cria a OP
        const opRef = await addDoc(collection(db, 'ordens_producao'), opData);

        alert('Ordem de produção criada com sucesso!');
        limparFormulario();
        window.opcionalSelecionado = null; // Limpa o opcional selecionado

      } catch (error) {
        console.error('Erro ao criar OP:', error);
        alert('Erro ao criar ordem de produção');
      }
    }

    function limparFormulario() {
      document.getElementById('productCode').value = '';
      document.getElementById('quantity').value = '';
      document.getElementById('dueDate').value = '';
      document.getElementById('observations').value = '';
      document.getElementById('sector').value = '';
      // Limpa outros campos do produto
      document.getElementById('productDescription').value = '';
      document.getElementById('productUnit').value = '';
    }

    function viewOP(opId) {
      // Fetch OP details and show print view
      const op = ordensProducao.find(op => op.id === opId);
      if (op) {
        document.getElementById('printOpNumber').textContent = `OP ${op.numero}`;
        document.getElementById('printProduct').textContent = op.produto;
        document.getElementById('printQuantity').textContent = op.quantidade;
        document.getElementById('printStartDate').textContent = op.dataInicio;
        document.getElementById('printEndDate').textContent = op.dataFim;

        // Populate components
        const componentsBody = document.getElementById('printComponents');
        componentsBody.innerHTML = '';
        op.componentes.forEach(comp => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${comp.codigo}</td>
            <td>${comp.descricao}</td>
            <td>${comp.quantidade}</td>
            <td>${comp.unidade}</td>
          `;
          componentsBody.appendChild(row);
        });

        // Populate operations
        const operationsBody = document.getElementById('printOperations');
        operationsBody.innerHTML = '';
        op.operacoes.forEach(op => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${op.sequencia}</td>
            <td>${op.operacao}</td>
            <td>${op.setor}</td>
            <td>${op.tempo}</td>
          `;
          operationsBody.appendChild(row);
        });

        // Show print view
        document.getElementById('printViewContainer').style.display = 'block';

        // Print after a short delay to ensure content is rendered
        setTimeout(() => {
          window.print();
          document.getElementById('printViewContainer').style.display = 'none';
        }, 500);
      }
    }

    window.gerarSolicitacoes = async function() {
      // Carregar solicitações existentes do banco
      const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));
      solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      try {
        await reloadData();
        // ... resto do seu código ...
        // solicitacoes.push(novaSolicitacao);
      } catch (error) {
        console.error('Erro ao gerar solicitações:', error);
        alert('Erro ao gerar solicitações: ' + error.message);
      }
    }

    // Função utilitária para comparação segura
    function safeCompare(a, b) {
      a = a || '';
      b = b || '';
      return a.toString().localeCompare(b.toString());
    }

    // Função utilitária para simular necessidades (exemplo: pedidos pendentes)
    async function obterNecessidadesAgrupadas() {
      // Exemplo: buscar pedidos pendentes e agrupar por produto
      // Substitua por sua lógica real de necessidades
      const pedidosSnap = await getDocs(collection(db, 'pedidosVenda'));
      const pedidosPendentes = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(p => p.status === 'Pendente');
      const necessidades = {};
      for (const pedido of pedidosPendentes) {
        if (!necessidades[pedido.produtoId]) necessidades[pedido.produtoId] = { produtoId: pedido.produtoId, quantidade: 0, pedidos: [] };
        necessidades[pedido.produtoId].quantidade += pedido.quantidade;
        necessidades[pedido.produtoId].pedidos.push(pedido);
      }
      return Object.values(necessidades);
    }

    // Exibir modal de OPs agrupadas
    window.showModalOpsAgrupadas = async function() {
      const necessidades = await obterNecessidadesAgrupadas();
      const produtosSnap = await getDocs(collection(db, 'produtos'));
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      let html = `<table class='op-table'><thead><tr><th>Produto</th><th>Quantidade Total</th><th>Pedidos</th></tr></thead><tbody>`;
      for (const n of necessidades) {
        const produto = produtos.find(p => p.id === n.produtoId);
        html += `<tr><td>${produto ? produto.codigo + ' - ' + produto.descricao : n.produtoId}</td><td>${n.quantidade}</td><td>${n.pedidos.map(p => p.numero).join(', ')}</td></tr>`;
      }
      html += '</tbody></table>';
      document.getElementById('agrupadasTableContainer').innerHTML = html;
      document.getElementById('modalOpsAgrupadas').style.display = 'block';
    };

    document.getElementById('btnGerarOpsAgrupadas').onclick = showModalOpsAgrupadas;

    // Função helper para buscar o parâmetro de aglutinação - SIMPLIFICADA
    async function deveAglutinarOPs() {
      const paramSnap = await getDoc(doc(db, 'parametros', 'sistema'));
      // Usar fonte única da verdade
      return paramSnap.exists() ? !!paramSnap.data().aglutinarOps : false;
    }

    // Função para explodir e consolidar necessidades de SPs (agrupando por produto E armazém)
    async function explodirEConsolidarFilhas(opsPais, estruturas, produtos, nivel = 1) {
      // Mapa para consolidar necessidades de SPs: { produtoId|armazemId: { quantidade, dataEntrega, prioridade, centroCustoId, armazemProducaoId } }
      const necessidadesSP = {};
      for (const opPai of opsPais) {
        const estrutura = estruturas.find(e => e.produtoPaiId === opPai.produtoId);
        if (!estrutura) continue;
        for (const componente of estrutura.componentes) {
          const produto = produtos.find(p => p.id === componente.componentId);
          const quantidadeNecessaria = opPai.quantidade * componente.quantidade;
          if (produto && produto.tipo === 'SP') {
            const key = produto.id + '|' + (opPai.armazemProducaoId || '');
            if (!necessidadesSP[key]) {
              necessidadesSP[key] = {
                produtoId: produto.id,
                quantidade: 0,
                dataEntrega: opPai.dataEntrega,
                prioridade: opPai.prioridade,
                centroCustoId: opPai.centroCustoId,
                armazemProducaoId: opPai.armazemProducaoId
              };
            }
            necessidadesSP[key].quantidade += quantidadeNecessaria;
            // Ajuste de data/prioridade se necessário
          }
        }
      }
      // Criar OPs filhas consolidadas
      const opsFilhas = [];
      for (const key in necessidadesSP) {
        const sp = necessidadesSP[key];
        const opFilha = {
          numero: await generateOrderNumber(),
          produtoId: sp.produtoId,
          quantidade: sp.quantidade,
          dataEntrega: sp.dataEntrega,
          status: 'Pendente',
          nivel,
          prioridade: sp.prioridade,
          centroCustoId: sp.centroCustoId,
          dataCriacao: Timestamp.now(),
          armazemProducaoId: sp.armazemProducaoId
        };
        const docRef = await addDoc(collection(db, 'ordensProducao'), opFilha);
        opFilha.id = docRef.id;
        opsFilhas.push(opFilha);
        // Debug
        console.log('Criada OP filha consolidada:', opFilha);
      }
      // Recursivamente explodir as filhas
      if (opsFilhas.length > 0) {
        const novasFilhas = await explodirEConsolidarFilhas(opsFilhas, estruturas, produtos, nivel + 1);
        return opsFilhas.concat(novasFilhas);
      }
      return opsFilhas;
    }

    // Alterar criarOpsIndividuais para explodir e consolidar filhas
    async function criarOpsIndividuais() {
      const pedidosSnap = await getDocs(collection(db, 'pedidosVenda'));
      const pedidosPendentes = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() })).filter(p => p.status === 'Pendente');
      const estruturasSnap = await getDocs(collection(db, 'estruturas'));
      const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const produtosSnap = await getDocs(collection(db, 'produtos'));
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      let erros = [];
      let criadas = 0;
      const opsPais = [];
      for (const pedido of pedidosPendentes) {
        const estrutura = estruturas.find(e => e.produtoPaiId === pedido.produtoId);
        const produto = produtos.find(p => p.id === pedido.produtoId);
        if (!estrutura) {
          erros.push(`Produto ${produto ? produto.codigo : pedido.produtoId} não possui estrutura cadastrada.`);
          continue;
        }
        try {
          const op = {
            numero: await generateOrderNumber(),
            produtoId: pedido.produtoId,
            quantidade: pedido.quantidade,
            dataEntrega: pedido.dataEntrega ? new Date(pedido.dataEntrega) : new Date(),
            status: 'Pendente',
            nivel: 0,
            prioridade: 'normal',
            dataCriacao: Timestamp.now(),
            materiaisNecessarios: estrutura.componentes.map(c => ({ ...c })),
            pedidosOrigem: [pedido.id],
            armazemProducaoId: pedido.armazemProducaoId || null // garantir armazem correto
          };
          const docRef = await addDoc(collection(db, 'ordensProducao'), op);
          op.id = docRef.id;
          opsPais.push(op);
          criadas++;
        } catch (e) {
          erros.push(`Erro ao criar OP para produto ${produto ? produto.codigo : pedido.produtoId}: ${e.message}`);
        }
      }
      // Explodir e consolidar filhas
      await explodirEConsolidarFilhas(opsPais, estruturas, produtos);
      let msg = `${criadas} OP(s) individuais criadas com sucesso.`;
      if (erros.length) msg += '\nErros:\n' + erros.join('\n');
      alert(msg);
      document.getElementById('modalOpsAgrupadas').style.display = 'none';
      await loadActiveOrders();
    }

    // Alterar geração agrupada para também explodir e consolidar filhas após criar as OPs pais
    const btnCriarOpsAgrupadas = document.getElementById('btnCriarOpsAgrupadas');
    if (btnCriarOpsAgrupadas) {
      btnCriarOpsAgrupadas.onclick = async function() {
        if (await deveAglutinarOPs()) {
          // Criar OPs agrupadas (já implementado)
          const necessidades = await obterNecessidadesAgrupadas();
          const estruturasSnap = await getDocs(collection(db, 'estruturas'));
          const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          const produtosSnap = await getDocs(collection(db, 'produtos'));
          const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          let erros = [];
          let criadas = 0;
          const opsPais = [];
          for (const n of necessidades) {
            const estrutura = estruturas.find(e => e.produtoPaiId === n.produtoId);
            const produto = produtos.find(p => p.id === n.produtoId);
            if (!estrutura) {
              erros.push(`Produto ${produto ? produto.codigo : n.produtoId} não possui estrutura cadastrada.`);
              continue;
            }
            try {
              const op = {
                numero: await generateOrderNumber(),
                produtoId: n.produtoId,
                quantidade: n.quantidade,
                dataEntrega: new Date(), // Ajuste conforme sua lógica
                status: 'Pendente',
                nivel: 0,
                prioridade: 'normal',
                dataCriacao: Timestamp.now(),
                materiaisNecessarios: estrutura.componentes.map(c => ({ ...c })),
                pedidosOrigem: n.pedidos.map(p => p.id),
                armazemProducaoId: n.armazemProducaoId || null // garantir armazem correto se disponível
              };
              const docRef = await addDoc(collection(db, 'ordensProducao'), op);
              op.id = docRef.id;
              opsPais.push(op);
              criadas++;
            } catch (e) {
              erros.push(`Erro ao criar OP para produto ${produto ? produto.codigo : n.produtoId}: ${e.message}`);
            }
          }
          // Explodir e consolidar filhas
          await explodirEConsolidarFilhas(opsPais, estruturas, produtos);
          let msg = `${criadas} OP(s) criadas com sucesso.`;
          if (erros.length) msg += '\nErros:\n' + erros.join('\n');
          alert(msg);
          document.getElementById('modalOpsAgrupadas').style.display = 'none';
          await loadActiveOrders();
        } else {
          // Criar OPs individuais
          await criarOpsIndividuais();
        }
      };
    }

    window.buscarPorCodigoProduto = function() {
      const searchInput = document.getElementById('generalSearch');
      const code = searchInput.value.trim().toLowerCase();
      if (!code) {
        filterOrders(); // mostra tudo
        return;
      }
      // Filtra apenas OPs do produto com o código digitado
      const filtered = ordensProducao.filter(op => {
        const produto = produtos.find(p => p.id === op.produtoId);
        return produto && produto.codigo.toLowerCase() === code;
      });
      renderFilteredOrders(filtered);
    };

    function renderFilteredOrders(filteredOrders) {
      const activeList = document.getElementById('activeOrdersList');
      const completedList = document.getElementById('completedOrdersList');
      activeList.innerHTML = '';
      completedList.innerHTML = '';
      for (const op of filteredOrders) {
        const produto = produtos.find(p => p.id === op.produtoId);
        const estoque = estoques.find(e => e.produtoId === op.produtoId && e.armazemId === op.armazemProducaoId);
        const saldoEstoque = estoque ? estoque.saldo : 0;
        const centroCusto = centrosCusto.find(cc => cc.id === op.centroCustoId);
        const armazem = armazens.find(a => a.id === op.armazemProducaoId);
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="checkbox" class="select-order" data-id="${op.id}"></td>
          <td>${op.numero}</td>
          <td>${produto ? produto.codigo + ' - ' + produto.descricao : '-'}</td>
          <td>${op.quantidade} ${produto ? produto.unidade : ''}</td>
          <td>${op.dataEntrega && typeof op.dataEntrega.seconds === 'number'
            ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()
            : ''}</td>
          <td>${op.prioridade || 'Normal'}</td>
          <td><span class="status-badge status-${op.status.toLowerCase()}">${op.status}</span></td>
          <td>${centroCusto ? centroCusto.codigo : '-'}</td>
          <td>${armazem ? armazem.codigo : '-'}</td>
          <td class="actions">
            <button onclick="viewOrder('${op.id}')" class="secondary-button"><i class="fas fa-eye"></i> Visualizar</button>
            <button onclick="printOrder('${op.id}')" class="secondary-button"><i class="fas fa-print"></i> Imprimir</button>
            ${op.status === 'Pendente' ? `
              <button onclick="iniciarProducao('${op.id}')" class="primary-button"><i class="fas fa-play"></i> Iniciar Produção</button>
              <button onclick="alterarStatusParaFirme('${op.id}')" class="warning-button"><i class="fas fa-check"></i> Para Firme</button>
            ` : ''}
            ${op.status === 'Firme' ? `
              <button onclick="alterarStatusParaPendente('${op.id}')" class="warning-button"><i class="fas fa-edit"></i> Para Pendente</button>
            ` : ''}
            ${op.status !== 'Concluída' && op.status !== 'Cancelada' ? `
              <button onclick="openAppointmentModal('${op.id}')" class="secondary-button"><i class="fas fa-clipboard"></i> Apontar</button>
              <button onclick="cancelOrder('${op.id}')" class="danger-button"><i class="fas fa-trash"></i> Excluir</button>
            ` : ''}
          </td>
        `;
        if (op.status === 'Concluída' || op.status === 'Cancelada') {
          completedList.appendChild(row);
        } else {
          activeList.appendChild(row);
        }
      }
    }

    // ===== FUNÇÃO DE TESTE PARA MODAL COM ROLAGEM =====
    function testarModalRolagem() {
      const detalhesSimulados = `
        MATÉRIAS-PRIMAS INSUFICIENTES:
        • Material A - Necessário: 100.000 kg | Disponível: 50.000 kg | Falta: 50.000 kg
        • Material B - Necessário: 200.000 kg | Disponível: 125.000 kg | Falta: 75.000 kg
        • Material C - Necessário: 150.000 kg | Disponível: 80.000 kg | Falta: 70.000 kg
        • Material D - Necessário: 300.000 kg | Disponível: 150.000 kg | Falta: 150.000 kg
        • Material E - Necessário: 250.000 kg | Disponível: 100.000 kg | Falta: 150.000 kg

        SUBPRODUTOS SEM OP ABERTA:
        • Produto X - Necessário: 100 unidades
        • Produto Y - Necessário: 50 unidades
        • Produto Z - Necessário: 75 unidades
        • Produto W - Necessário: 200 unidades
        • Produto V - Necessário: 125 unidades
        • Produto U - Necessário: 300 unidades
        • Produto T - Necessário: 175 unidades
        • Produto S - Necessário: 225 unidades
        • Produto R - Necessário: 150 unidades
        • Produto Q - Necessário: 275 unidades

        CONFIGURAÇÃO ADICIONAL:
        • Item Extra 1 - Teste de rolagem
        • Item Extra 2 - Teste de rolagem
        • Item Extra 3 - Teste de rolagem
        • Item Extra 4 - Teste de rolagem
        • Item Extra 5 - Teste de rolagem
        • Item Extra 6 - Teste de rolagem
        • Item Extra 7 - Teste de rolagem
        • Item Extra 8 - Teste de rolagem
        • Item Extra 9 - Teste de rolagem
        • Item Extra 10 - Teste de rolagem
      `;

      const materiaisSPSimulados = [
        { codigo: 'PROD-001', descricao: 'Produto Teste 1', quantidade: 100 },
        { codigo: 'PROD-002', descricao: 'Produto Teste 2', quantidade: 50 },
        { codigo: 'PROD-003', descricao: 'Produto Teste 3', quantidade: 75 },
        { codigo: 'PROD-004', descricao: 'Produto Teste 4', quantidade: 200 },
        { codigo: 'PROD-005', descricao: 'Produto Teste 5', quantidade: 125 },
        { codigo: 'PROD-006', descricao: 'Produto Teste 6', quantidade: 300 },
        { codigo: 'PROD-007', descricao: 'Produto Teste 7', quantidade: 175 },
        { codigo: 'PROD-008', descricao: 'Produto Teste 8', quantidade: 225 }
      ];

      const verificacaoSPsSimulada = {
        opsAbertas: [
          { codigo: 'PROD-009', quantidadeEmProducao: 150, ops: [{ numero: 'OP001', quantidade: 150 }] },
          { codigo: 'PROD-010', quantidadeEmProducao: 200, ops: [{ numero: 'OP002', quantidade: 200 }] },
          { codigo: 'PROD-011', quantidadeEmProducao: 100, ops: [{ numero: 'OP003', quantidade: 100 }] },
          { codigo: 'PROD-012', quantidadeEmProducao: 250, ops: [{ numero: 'OP004', quantidade: 250 }] }
        ]
      };

      console.log('🧪 Iniciando teste do modal com rolagem...');
      mostrarModalDecisaoOP(detalhesSimulados, materiaisSPSimulados, verificacaoSPsSimulada)
        .then(decisao => {
          console.log('✅ Teste concluído! Decisão:', decisao);
          alert(`🎉 Teste do modal concluído!\n\nDecisão do usuário: ${decisao}\n\nVerifique se a rolagem funcionou corretamente.`);
        });
    }
  </script>
</body>
</html>

