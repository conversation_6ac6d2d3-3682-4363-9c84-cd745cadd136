<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Orçamentos e Vendas</title>
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
            --orcamento-color: #28a745;
            --orcamento-hover: #218838;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: var(--text-color);
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-col {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-color);
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--orcamento-hover);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        /* Sistema de busca de clientes */
        .search-container {
            position: relative;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }

        .search-item:hover {
            background: var(--secondary-color);
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .client-info {
            background: var(--secondary-color);
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .client-info h3 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
        }

        .client-info p {
            margin: 5px 0;
            font-size: 14px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th,
        .items-table td {
            padding: 12px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .items-table th {
            background: var(--secondary-color);
            font-weight: 600;
        }

        .items-table input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .total-section {
            background: var(--secondary-color);
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .total-row:last-child {
            margin-bottom: 0;
            font-weight: bold;
            font-size: 18px;
            color: var(--primary-color);
        }

       .quote-list {
            margin-top: 20px;
        }

        .quote-item {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .quote-number {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .quote-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-ATIVO {
            background: var(--success-color);
            color: white;
        }

        .status-VENCIDO {
            background: var(--danger-color);
            color: white;
        }

        .status-CONVERTIDO {
            background: var(--warning-color);
            color: black;
        }

        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: var(--danger-color);
            background: #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .success {
            color: var(--success-color);
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .no-results {
            text-align: center;
            color: #666;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                margin: 10px;
                padding: 15px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💼 Sistema de Orçamentos e Vendas</h1>
            <p>Gestão completa de orçamentos e pedidos de venda</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('novo-orcamento')">📝 Novo Orçamento</button>
            <button class="tab" onclick="showTab('consultar-orcamentos')">📋 Consultar Orçamentos</button>
            <button class="tab" onclick="showTab('converter-pedido')">🔄 Converter para Pedido</button>
        </div>

        <!-- Novo Orçamento -->
        <div id="novo-orcamento" class="tab-content active">
            <form id="orcamentoForm">
                <div class="form-row">
                    <div class="form-col">
                        <label for="numeroOrcamento">Número do Orçamento</label>
                        <input type="text" id="numeroOrcamento" readonly style="background: #f0f0f0;">
                    </div>
                    <div class="form-col">
                        <label for="dataOrcamento">Data do Orçamento</label>
                        <input type="date" id="dataOrcamento" required>
                    </div>
                    <div class="form-col">
                        <label for="validadeOrcamento">Validade (dias)</label>
                        <input type="number" id="validadeOrcamento" value="30" min="1" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="clienteSearch">🔍 Buscar Cliente (código ou nome)</label>
                    <div class="search-container">
                        <input type="text" id="clienteSearch" placeholder="Digite o código ou nome do cliente..." autocomplete="off">
                        <div id="clienteResults" class="search-results"></div>
                    </div>
                </div>

                <div id="clienteInfo" class="client-info" style="display: none;">
                    <h3 id="clienteNome"></h3>
                    <p><strong>Código:</strong> <span id="clienteCodigo"></span></p>
                    <p><strong>CNPJ:</strong> <span id="clienteCnpj"></span></p>
                    <p><strong>Telefone:</strong> <span id="clienteTelefone"></span></p>
                    <p><strong>Email:</strong> <span id="clienteEmail"></span></p>
                </div>

                <input type="hidden" id="clienteId">

                <div class="form-group">
                    <label for="observacoes">Observações</label>
                    <textarea id="observacoes" rows="3" placeholder="Observações adicionais do orçamento..."></textarea>
                </div>

                <h3>Itens do Orçamento</h3>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Qtd</th>
                            <th>Valor Unit.</th>
                            <th>Total</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="itensTable">
                        <tr id="newItemRow">
                            <td><input type="text" id="newItemCodigo" placeholder="Código"></td>
                            <td><input type="text" id="newItemDescricao" placeholder="Descrição"></td>
                            <td><input type="number" id="newItemQtd" placeholder="Qtd" min="1" step="0.01"></td>
                            <td><input type="number" id="newItemValor" placeholder="Valor" min="0" step="0.01"></td>
                            <td><span id="newItemTotal">0,00</span></td>
                            <td><button type="button" class="btn btn-primary" onclick="addItem()">Adicionar</button></td>
                        </tr>
                    </tbody>
                </table>

                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span id="subtotal">R$ 0,00</span>
                    </div>
                    <div class="total-row">
                        <span>Desconto:</span>
                        <span><input type="number" id="desconto" value="0" min="0" max="100" step="0.01" style="width: 80px;"> %</span>
                    </div>
                    <div class="total-row">
                        <span>Total Geral:</span>
                        <span id="totalGeral">R$ 0,00</span>
                    </div>
                </div>

                <div class="actions">
                    <button type="submit" class="btn btn-success">💾 Salvar Orçamento</button>
                    <button type="button" class="btn btn-secondary" onclick="limparForm()">🔄 Limpar</button>
                </div>
            </form>
        </div>

        <!-- Consultar Orçamentos -->
        <div id="consultar-orcamentos" class="tab-content">
            <div class="form-row">
                <div class="form-col">
                    <input type="text" id="searchOrcamento" placeholder="Buscar por número, cliente...">
                </div>
                <div class="form-col">
                    <select id="statusFilter">
                        <option value="">Todos os Status</option>
                        <option value="ATIVO">Ativos</option>
                        <option value="VENCIDO">Vencidos</option>
                        <option value="CONVERTIDO">Convertidos</option>
                    </select>
                </div>
                <div class="form-col">
                    <button class="btn btn-primary" onclick="buscarOrcamentos()">🔍 Buscar</button>
                </div>
            </div>

            <div id="orcamentosList" class="quote-list">
                <div class="loading">Carregando orçamentos...</div>
            </div>
        </div>

        <!-- Converter para Pedido -->
        <div id="converter-pedido" class="tab-content">
            <div class="form-group">
                <label for="orcamentoSelect">Selecionar Orçamento</label>
                <select id="orcamentoSelect">
                    <option value="">Selecione um orçamento...</option>
                </select>
            </div>

            <div id="orcamentoDetails" style="display: none;">
                <h3>Detalhes do Orçamento</h3>
                <div id="orcamentoDetailsContent"></div>

                <div class="actions">
                    <button class="btn btn-success" onclick="converterParaPedido()">🔄 Converter para Pedido</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            addDoc, 
            getDocs, 
            doc, 
            getDoc,
            updateDoc, 
            query, 
            where, 
            orderBy, 
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let clientes = [];
        let produtos = [];
        let orcamentos = [];
        let selectedCliente = null;
        let itensOrcamento = [];
        let searchTimeout = null;

        // Inicialização
        window.onload = async function() {
            await carregarDados();
            await gerarNumeroOrcamento();
            document.getElementById('dataOrcamento').value = new Date().toISOString().split('T')[0];

            // Event listeners
            document.getElementById('clienteSearch').addEventListener('input', buscarClientes);
            document.getElementById('newItemQtd').addEventListener('input', calcularTotalItem);
            document.getElementById('newItemValor').addEventListener('input', calcularTotalItem);
            document.getElementById('desconto').addEventListener('input', calcularTotalGeral);

            // Carregar orçamentos na inicialização
            await buscarOrcamentos();
            await carregarOrcamentosParaConverter();
        };

        // Carregamento de dados
        async function carregarDados() {
            try {
                const [clientesSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "clientes")),
                    getDocs(collection(db, "produtos"))
                ]);

                clientes = clientesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', { clientes: clientes.length, produtos: produtos.length });
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showMessage('Erro ao carregar dados: ' + error.message, 'error');
            }
        }

        // Geração de número sequencial para orçamento
        async function gerarNumeroOrcamento() {
            try {
                const counterRef = doc(db, "contadores", "orcamentos");
                const counterDoc = await getDoc(counterRef);

                let nextNumber = 1;
                if (counterDoc.exists()) {
                    nextNumber = (counterDoc.data().valor || 0) + 1;
                } else {
                    await addDoc(collection(db, "contadores"), {
                        valor: 1
                    });
                }

                const numeroOrcamento = `ORC${nextNumber.toString().padStart(6, '0')}`;
                document.getElementById('numeroOrcamento').value = numeroOrcamento;

                return numeroOrcamento;
            } catch (error) {
                console.error('Erro ao gerar número do orçamento:', error);
                return `ORC${Date.now()}`;
            }
        }

        // Sistema de busca de clientes
        async function buscarClientes() {
            const searchTerm = document.getElementById('clienteSearch').value.toLowerCase();
            const resultsDiv = document.getElementById('clienteResults');

            if (searchTerm.length < 2) {
                resultsDiv.style.display = 'none';
                return;
            }

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const filteredClientes = clientes.filter(cliente => 
                    cliente.codigo?.toLowerCase().includes(searchTerm) ||
                    cliente.nome?.toLowerCase().includes(searchTerm) ||
                    cliente.cnpj?.includes(searchTerm)
                );

                resultsDiv.innerHTML = '';

                if (filteredClientes.length > 0) {
                    filteredClientes.slice(0, 10).forEach(cliente => {
                        const div = document.createElement('div');
                        div.className = 'search-item';
                        div.innerHTML = `
                            <strong>${cliente.codigo}</strong> - ${cliente.nome}
                            <br><small>${cliente.cnpj || 'CNPJ não informado'}</small>
                        `;
                        div.onclick = () => selecionarCliente(cliente);
                        resultsDiv.appendChild(div);
                    });
                    resultsDiv.style.display = 'block';
                } else {
                    resultsDiv.innerHTML = '<div class="search-item">Nenhum cliente encontrado</div>';
                    resultsDiv.style.display = 'block';
                }
            }, 300);
        }

        function selecionarCliente(cliente) {
            selectedCliente = cliente;
            document.getElementById('clienteId').value = cliente.id;
            document.getElementById('clienteSearch').value = `${cliente.codigo} - ${cliente.nome}`;
            document.getElementById('clienteResults').style.display = 'none';

            // Mostrar informações do cliente
            document.getElementById('clienteNome').textContent = cliente.nome;
            document.getElementById('clienteCodigo').textContent = cliente.codigo;
            document.getElementById('clienteCnpj').textContent = cliente.cnpj || 'Não informado';
            document.getElementById('clienteTelefone').textContent = cliente.telefone || 'Não informado';
            document.getElementById('clienteEmail').textContent = cliente.email || 'Não informado';
            document.getElementById('clienteInfo').style.display = 'block';
        }

        // Gerenciamento de itens
        window.addItem = function() {
            const codigo = document.getElementById('newItemCodigo').value;
            const descricao = document.getElementById('newItemDescricao').value;
            const qtd = parseFloat(document.getElementById('newItemQtd').value);
            const valor = parseFloat(document.getElementById('newItemValor').value);

            if (!codigo || !descricao || !qtd || !valor) {
                showMessage('Preencha todos os campos do item', 'error');
                return;
            }

            const item = {
                codigo,
                descricao,
                quantidade: qtd,
                valorUnitario: valor,
                total: qtd * valor
            };

            itensOrcamento.push(item);
            atualizarTabelaItens();
            limparCamposItem();
            calcularTotalGeral();
        };

        function atualizarTabelaItens() {
            const tbody = document.getElementById('itensTable');
            const newItemRow = document.getElementById('newItemRow');

            // Remove todos os itens exceto a linha de novo item
            tbody.querySelectorAll('tr:not(#newItemRow)').forEach(row => row.remove());

            itensOrcamento.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.codigo}</td>
                    <td>${item.descricao}</td>
                    <td>${item.quantidade}</td>
                    <td>R$ ${item.valorUnitario.toFixed(2)}</td>
                    <td>R$ ${item.total.toFixed(2)}</td>
                    <td><button type="button" class="btn btn-danger" onclick="removeItem(${index})">Remover</button></td>
                `;
                tbody.insertBefore(row, newItemRow);
            });
        }

        window.removeItem = function(index) {
            itensOrcamento.splice(index, 1);
            atualizarTabelaItens();
            calcularTotalGeral();
        };

        function limparCamposItem() {
            document.getElementById('newItemCodigo').value = '';
            document.getElementById('newItemDescricao').value = '';
            document.getElementById('newItemQtd').value = '';
            document.getElementById('newItemValor').value = '';
            document.getElementById('newItemTotal').textContent = '0,00';
        }

        function calcularTotalItem() {
            const qtd = parseFloat(document.getElementById('newItemQtd').value) || 0;
            const valor = parseFloat(document.getElementById('newItemValor').value) || 0;
            const total = qtd * valor;
            document.getElementById('newItemTotal').textContent = total.toFixed(2);
        }

        function calcularTotalGeral() {
            const subtotal = itensOrcamento.reduce((sum, item) => sum + item.total, 0);
            const desconto = parseFloat(document.getElementById('desconto').value) || 0;
            const totalGeral = subtotal - (subtotal * desconto / 100);

            document.getElementById('subtotal').textContent = `R$ ${subtotal.toFixed(2)}`;
            document.getElementById('totalGeral').textContent = `R$ ${totalGeral.toFixed(2)}`;
        }

        // Salvar orçamento
        document.getElementById('orcamentoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!selectedCliente) {
                showMessage('Selecione um cliente', 'error');
                return;
            }

            if (itensOrcamento.length === 0) {
                showMessage('Adicione pelo menos um item', 'error');
                return;
            }

            try {
                const numeroOrcamento = document.getElementById('numeroOrcamento').value;
                const desconto = parseFloat(document.getElementById('desconto').value) || 0;
                const subtotal = itensOrcamento.reduce((sum, item) => sum + item.total, 0);
                const totalGeral = subtotal - (subtotal * desconto / 100);

                const orcamento = {
                    numero: numeroOrcamento,
                    clienteId: selectedCliente.id,
                    cliente: selectedCliente,
                    dataOrcamento: new Date(document.getElementById('dataOrcamento').value),
                    validadeDias: parseInt(document.getElementById('validadeOrcamento').value),
                    observacoes: document.getElementById('observacoes').value,
                    itens: itensOrcamento,
                    subtotal,
                    desconto,
                    totalGeral,
                    status: 'ATIVO',
                    dataCriacao: Timestamp.now(),
                    criadoPor: 'Usuario' // Substituir por usuário logado
                };

                await addDoc(collection(db, "orcamentos"), orcamento);

                // Atualizar contador
                await updateDoc(doc(db, "contadores", "orcamentos"), {
                    valor: parseInt(numeroOrcamento.replace('ORC', ''))
                });

                showMessage('Orçamento salvo com sucesso!', 'success');
                limparForm();
                await gerarNumeroOrcamento();

            } catch (error) {
                console.error('Erro ao salvar orçamento:', error);
                showMessage('Erro ao salvar orçamento: ' + error.message, 'error');
            }
        });

        // Buscar orçamentos
        window.buscarOrcamentos = async function() {
            try {
                const searchTerm = document.getElementById('searchOrcamento').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;

                const orcamentosSnap = await getDocs(collection(db, "orcamentos"));
                let orcamentosList = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar por termo de busca
                if (searchTerm) {
                    orcamentosList = orcamentosList.filter(orc => 
                        orc.numero?.toLowerCase().includes(searchTerm) ||
                        orc.cliente?.nome?.toLowerCase().includes(searchTerm) ||
                        orc.cliente?.codigo?.toLowerCase().includes(searchTerm)
                    );
                }

                // Filtrar por status
                if (statusFilter) {
                    orcamentosList = orcamentosList.filter(orc => orc.status === statusFilter);
                }

                exibirOrcamentos(orcamentosList);

            } catch (error) {
                console.error('Erro ao buscar orçamentos:', error);
                showMessage('Erro ao buscar orçamentos: ' + error.message, 'error');
            }
        };

        function exibirOrcamentos(orcamentosList) {
            const listDiv = document.getElementById('orcamentosList');

            if (orcamentosList.length === 0) {
                listDiv.innerHTML = '<div class="no-results">Nenhum orçamento encontrado</div>';
                return;
            }

            listDiv.innerHTML = '';

            orcamentosList.forEach(orc => {
                const div = document.createElement('div');
                div.className = 'quote-item';
                div.innerHTML = `
                    <div class="quote-header">
                        <span class="quote-number">${orc.numero}</span>
                        <span class="quote-status status-${orc.status.toLowerCase()}">${orc.status}</span>
                    </div>
                    <p><strong>Cliente:</strong> ${orc.cliente?.nome || 'N/A'}</p>
                    <p><strong>Data:</strong> ${orc.dataOrcamento?.toDate ? orc.dataOrcamento.toDate().toLocaleDateString() : 'N/A'}</p>
                    <p><strong>Total:</strong> R$ ${orc.totalGeral?.toFixed(2) || '0,00'}</p>
                    <p><strong>Itens:</strong> ${orc.itens?.length || 0}</p>
                    <div class="actions">
                        <button class="btn btn-primary" onclick="visualizarOrcamento('${orc.id}')">👁️ Visualizar</button>
                        <button class="btn btn-success" onclick="converterOrcamento('${orc.id}')">🔄 Converter</button>
                    </div>
                `;
                listDiv.appendChild(div);
            });
        }

        // Funções auxiliares
        window.showTab = function(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        };

        window.limparForm = function() {
            document.getElementById('orcamentoForm').reset();
            selectedCliente = null;
            itensOrcamento = [];
            document.getElementById('clienteInfo').style.display = 'none';
            document.getElementById('clienteResults').style.display = 'none';
            atualizarTabelaItens();
            calcularTotalGeral();
            document.getElementById('dataOrcamento').value = new Date().toISOString().split('T')[0];
        };

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(messageDiv, container.firstChild);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Carregar orçamentos para conversão
        async function carregarOrcamentosParaConverter() {
            try {
                const orcamentosSnap = await getDocs(
                    query(collection(db, "orcamentos"), where("status", "==", "ATIVO"))
                );

                const select = document.getElementById('orcamentoSelect');
                select.innerHTML = '<option value="">Selecione um orçamento...</option>';

                orcamentosSnap.docs.forEach(doc => {
                    const orc = doc.data();
                    const option = document.createElement('option');
                    option.value = doc.id;
                    option.textContent = `${orc.numero} - ${orc.cliente?.nome || 'N/A'}`;
                    select.appendChild(option);
                });

            } catch (error) {
                console.error('Erro ao carregar orçamentos:', error);
            }
        }

        // Placeholder para outras funções
        window.visualizarOrcamento = function(id) {
            alert('Função de visualização em desenvolvimento');
        };

        window.converterOrcamento = function(id) {
            alert('Função de conversão em desenvolvimento');
        };

        window.converterParaPedido = function() {
            alert('Função de conversão para pedido em desenvolvimento');
        };

        // Fechar resultados de busca ao clicar fora
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                document.getElementById('clienteResults').style.display = 'none';
            }
        });
    </script>
</body>
</html>