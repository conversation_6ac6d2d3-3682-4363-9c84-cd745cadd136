<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Teste Firebase Simples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Teste Firebase Simples</h1>
        <p>Teste básico para verificar conexão e dados do Firebase</p>
        
        <button class="btn" onclick="testarConexao()">🔍 Testar Conexão</button>
        <button class="btn" onclick="listarOPs()">📋 Listar OPs</button>
        <button class="btn" onclick="limparLog()">🗑️ Limpar Log</button>
        
        <div id="log" class="log">Aguardando teste...</div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;

        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            log.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            log.scrollTop = log.scrollHeight;
        }

        window.testarConexao = async function() {
            addLog('🔍 Testando conexão Firebase...', 'info');
            
            try {
                if (!window.db) {
                    throw new Error('Firebase não inicializado');
                }
                
                addLog('✅ Firebase disponível', 'success');
                addLog(`📊 Projeto: ${db.app.options.projectId}`, 'info');
                addLog(`🌐 Auth Domain: ${db.app.options.authDomain}`, 'info');
                
                // Teste simples de conexão
                const testCollection = await getDocs(collection(db, "produtos"));
                addLog(`✅ Conexão OK - ${testCollection.docs.length} produtos encontrados`, 'success');
                
            } catch (error) {
                addLog(`❌ Erro: ${error.message}`, 'error');
            }
        };

        window.listarOPs = async function() {
            addLog('📋 Listando Ordens de Produção...', 'info');
            
            try {
                // Testar diferentes nomes de coleção
                const nomesColecao = ['ordensProducao', 'ordens_producao', 'ops', 'ordens'];
                
                for (const nome of nomesColecao) {
                    try {
                        addLog(`🔍 Testando coleção: ${nome}`, 'info');
                        const snapshot = await getDocs(collection(db, nome));
                        
                        if (snapshot.docs.length > 0) {
                            addLog(`✅ ${nome}: ${snapshot.docs.length} documentos encontrados`, 'success');
                            
                            // Mostrar alguns exemplos
                            const exemplos = snapshot.docs.slice(0, 3).map(doc => {
                                const data = doc.data();
                                return {
                                    id: doc.id,
                                    numero: data.numero || data.numeroOP || 'N/A',
                                    status: data.status || 'N/A',
                                    produto: data.produto || data.descricaoProduto || 'N/A'
                                };
                            });
                            
                            addLog(`📋 Exemplos: ${JSON.stringify(exemplos, null, 2)}`, 'info');
                        } else {
                            addLog(`⚠️ ${nome}: coleção vazia`, 'info');
                        }
                        
                    } catch (error) {
                        addLog(`❌ ${nome}: ${error.message}`, 'error');
                    }
                }
                
            } catch (error) {
                addLog(`❌ Erro geral: ${error.message}`, 'error');
            }
        };

        window.limparLog = function() {
            document.getElementById('log').innerHTML = 'Log limpo...\n';
        };

        // Inicialização
        addLog('🚀 Página carregada', 'success');
        addLog('💡 Clique em "Testar Conexão" para começar', 'info');
    </script>
</body>
</html>
