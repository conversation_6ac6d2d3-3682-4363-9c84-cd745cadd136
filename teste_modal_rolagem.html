<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Modal com Rolagem</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>🧪 Teste do Modal com Rolagem</h2>
        <p>Este teste simula um modal com muito conteúdo para verificar se a rolagem está funcionando corretamente.</p>
        <p><strong>Clique no botão abaixo para abrir o modal de teste:</strong></p>
    </div>

    <button class="test-button" onclick="abrirModalTeste()">
        <i class="fas fa-test-tube"></i> Abrir Modal de Teste
    </button>

    <script>
        function abrirModalTeste() {
            // Criar modal de teste
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                border-radius: 20px;
                max-width: 1200px;
                width: 95%;
                max-height: 90vh;
                display: flex;
                flex-direction: column;
                box-shadow: 0 25px 50px rgba(0,0,0,0.25);
                border: 1px solid rgba(255,255,255,0.2);
                overflow: hidden;
                position: relative;
            `;

            // Header
            const header = document.createElement('div');
            header.style.cssText = `
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 25px 30px;
                border-radius: 20px 20px 0 0;
                position: relative;
                overflow: hidden;
                flex-shrink: 0;
            `;
            header.innerHTML = `
                <h2 style="margin: 0; font-size: 24px; font-weight: 700;">
                    <i class="fas fa-scroll" style="margin-right: 10px;"></i>
                    TESTE DE ROLAGEM
                </h2>
                <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">
                    Modal com muito conteúdo para testar a rolagem vertical
                </p>
            `;

            // Conteúdo principal com rolagem
            const mainContent = document.createElement('div');
            mainContent.style.cssText = `
                padding: 30px;
                flex: 1;
                overflow-y: auto;
                overflow-x: hidden;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e0 #f7fafc;
                min-height: 0;
                position: relative;
            `;
            mainContent.className = 'modal-scroll-content';

            // Adicionar muito conteúdo para forçar rolagem
            let conteudo = '';
            for (let i = 1; i <= 50; i++) {
                conteudo += `
                    <div style="
                        background: ${i % 2 === 0 ? '#f8f9fa' : 'white'};
                        padding: 15px;
                        margin-bottom: 10px;
                        border-radius: 8px;
                        border-left: 4px solid ${i % 3 === 0 ? '#28a745' : i % 3 === 1 ? '#ffc107' : '#dc3545'};
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    ">
                        <h4 style="margin: 0 0 8px 0; color: #2c3e50;">
                            <i class="fas fa-${i % 4 === 0 ? 'cog' : i % 4 === 1 ? 'box' : i % 4 === 2 ? 'industry' : 'clipboard-list'}"></i>
                            Item ${i} - Teste de Conteúdo
                        </h4>
                        <p style="margin: 0; color: #6c757d; font-size: 14px;">
                            Este é o item número ${i} do teste de rolagem. 
                            O conteúdo é extenso para verificar se a barra de rolagem aparece corretamente 
                            dentro dos limites do modal e não fora dele.
                        </p>
                        <div style="margin-top: 8px; font-size: 12px; color: #adb5bd;">
                            Posição: ${i}/50 | Status: ${i % 2 === 0 ? 'Ativo' : 'Pendente'}
                        </div>
                    </div>
                `;
            }
            mainContent.innerHTML = conteudo;

            // Área de botões fixa
            const botoesArea = document.createElement('div');
            botoesArea.style.cssText = `
                padding: 20px 30px 30px 30px;
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                border-top: 1px solid #e9ecef;
                display: flex;
                gap: 15px;
                flex-shrink: 0;
                border-radius: 0 0 20px 20px;
                justify-content: center;
            `;

            const btnFechar = document.createElement('button');
            btnFechar.innerHTML = `
                <i class="fas fa-times" style="margin-right: 8px;"></i>
                FECHAR TESTE
            `;
            btnFechar.style.cssText = `
                padding: 15px 25px;
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            `;
            btnFechar.onmouseover = () => {
                btnFechar.style.transform = 'translateY(-2px)';
                btnFechar.style.boxShadow = '0 6px 20px rgba(220, 53, 69, 0.4)';
            };
            btnFechar.onmouseout = () => {
                btnFechar.style.transform = 'translateY(0)';
                btnFechar.style.boxShadow = '0 4px 15px rgba(220, 53, 69, 0.3)';
            };
            btnFechar.onclick = () => {
                document.body.removeChild(modal);
            };

            botoesArea.appendChild(btnFechar);

            // Montar modal
            modalContent.appendChild(header);
            modalContent.appendChild(mainContent);
            modalContent.appendChild(botoesArea);
            modal.appendChild(modalContent);

            // Adicionar CSS para scrollbar
            const scrollbarStyle = document.createElement('style');
            scrollbarStyle.textContent = `
                .modal-scroll-content::-webkit-scrollbar {
                    width: 8px;
                }
                .modal-scroll-content::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 10px;
                    margin: 5px;
                }
                .modal-scroll-content::-webkit-scrollbar-thumb {
                    background: linear-gradient(135deg, #cbd5e0, #a0aec0);
                    border-radius: 10px;
                    transition: background 0.3s ease;
                }
                .modal-scroll-content::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(135deg, #a0aec0, #718096);
                }
                .modal-scroll-content::-webkit-scrollbar-corner {
                    background: transparent;
                }
            `;
            document.head.appendChild(scrollbarStyle);

            // Fechar com ESC
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleKeydown);
                }
            };
            document.addEventListener('keydown', handleKeydown);

            // Fechar clicando fora
            modal.onclick = (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleKeydown);
                }
            };

            // Mostrar modal
            document.body.appendChild(modal);

            // Log para debug
            setTimeout(() => {
                console.log('Modal criado:');
                console.log('- Altura do conteúdo:', mainContent.scrollHeight);
                console.log('- Altura visível:', mainContent.clientHeight);
                console.log('- Tem rolagem:', mainContent.scrollHeight > mainContent.clientHeight);
            }, 100);
        }
    </script>
</body>
</html>
