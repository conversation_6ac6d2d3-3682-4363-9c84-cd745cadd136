// Serviço centralizado para transferências entre armazéns
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  runTransaction, 
  addDoc, 
  updateDoc, 
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TransferService {
  
  /**
   * Executa transferência entre armazéns com validações
   */
  static async executeTransfer(transferData) {
    try {
      const result = await runTransaction(db, async (transaction) => {
        // 1. Validar dados
        this.validateTransferData(transferData);
        
        // 2. Verificar saldo origem
        const estoqueOrigemQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", transferData.produtoId),
          where("armazemId", "==", transferData.armazemOrigemId)
        );
        const estoqueOrigemSnap = await getDocs(estoqueOrigemQuery);
        
        if (estoqueOrigemSnap.empty) {
          throw new Error('Produto não encontrado no armazém de origem');
        }
        
        const estoqueOrigem = estoqueOrigemSnap.docs[0];
        const dadosOrigem = estoqueOrigem.data();
        const saldoDisponivel = dadosOrigem.saldo - (dadosOrigem.saldoReservado || 0);
        
        if (saldoDisponivel < transferData.quantidade) {
          throw new Error(`Saldo insuficiente. Disponível: ${saldoDisponivel}, Solicitado: ${transferData.quantidade}`);
        }
        
        // 3. Buscar ou criar estoque destino
        const estoqueDestinoQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", transferData.produtoId),
          where("armazemId", "==", transferData.armazemDestinoId)
        );
        const estoqueDestinoSnap = await getDocs(estoqueDestinoQuery);
        
        let estoqueDestino;
        let dadosDestino;
        
        if (estoqueDestinoSnap.empty) {
          // Criar novo estoque no destino
          estoqueDestino = doc(collection(db, "estoques"));
          dadosDestino = {
            produtoId: transferData.produtoId,
            armazemId: transferData.armazemDestinoId,
            saldo: 0,
            saldoReservado: 0,
            dataCriacao: Timestamp.now()
          };
          transaction.set(estoqueDestino, dadosDestino);
        } else {
          estoqueDestino = estoqueDestinoSnap.docs[0].ref;
          dadosDestino = estoqueDestinoSnap.docs[0].data();
        }
        
        // 4. Gerar número da transferência
        const numeroTransferencia = await this.generateTransferNumber();
        
        // 5. Atualizar estoques
        transaction.update(estoqueOrigem.ref, {
          saldo: dadosOrigem.saldo - transferData.quantidade,
          ultimaMovimentacao: Timestamp.now()
        });
        
        transaction.update(estoqueDestino, {
          saldo: dadosDestino.saldo + transferData.quantidade,
          ultimaMovimentacao: Timestamp.now()
        });
        
        // 6. Registrar transferência
        const transferenciaRef = doc(collection(db, "transferenciasArmazem"));
        const transferenciaData = {
          numero: numeroTransferencia,
          produtoId: transferData.produtoId,
          armazemOrigemId: transferData.armazemOrigemId,
          armazemDestinoId: transferData.armazemDestinoId,
          quantidade: transferData.quantidade,
          motivo: transferData.motivo,
          ordemProducaoId: transferData.ordemProducaoId,
          solicitanteId: transferData.solicitanteId,
          dataHora: Timestamp.now(),
          status: 'CONCLUIDA',
          observacoes: transferData.observacoes
        };
        transaction.set(transferenciaRef, transferenciaData);
        
        // 7. Registrar movimentações de estoque
        // Saída do armazém origem
        const movSaidaRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movSaidaRef, {
          produtoId: transferData.produtoId,
          armazemId: transferData.armazemOrigemId,
          tipo: 'SAIDA',
          quantidade: transferData.quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência para ${transferData.armazemDestinoId} - ${transferData.motivo}`,
          dataHora: Timestamp.now(),
          transferenciaId: transferenciaRef.id
        });
        
        // Entrada no armazém destino
        const movEntradaRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movEntradaRef, {
          produtoId: transferData.produtoId,
          armazemId: transferData.armazemDestinoId,
          tipo: 'ENTRADA',
          quantidade: transferData.quantidade,
          tipoDocumento: 'TRANSFERENCIA',
          numeroDocumento: numeroTransferencia,
          observacoes: `Transferência de ${transferData.armazemOrigemId} - ${transferData.motivo}`,
          dataHora: Timestamp.now(),
          transferenciaId: transferenciaRef.id
        });
        
        // 8. Atualizar necessidades da OP se aplicável
        if (transferData.ordemProducaoId) {
          await this.updateOrderRequirements(
            transaction,
            transferData.ordemProducaoId,
            transferData.produtoId,
            transferData.quantidade
          );
        }
        
        return {
          transferenciaId: transferenciaRef.id,
          numero: numeroTransferencia,
          saldoOrigemAnterior: dadosOrigem.saldo,
          saldoOrigemPosterior: dadosOrigem.saldo - transferData.quantidade,
          saldoDestinoPosterior: dadosDestino.saldo + transferData.quantidade
        };
      });
      
      console.log('Transferência executada:', result);
      
      // Notificar sistemas relacionados
      await this.notifyTransfer(result);

      // 🆕 Sincronizar flag materialTransferido automaticamente
      if (transferData.ordemProducaoId) {
        await this.syncOrderTransferFlag(transferData.ordemProducaoId);

        // 🆕 Log de auditoria
        await this.logTransferAudit(result.transferenciaId, transferData.ordemProducaoId, 'TRANSFER_COMPLETED');
      }

      return result;
      
    } catch (error) {
      console.error('Erro na transferência:', error);
      throw new Error(`Falha na transferência: ${error.message}`);
    }
  }
  
  /**
   * Transferência automática para suprir OP
   */
  static async autoTransferForOrder(orderId, produtoId, quantidade) {
    try {
      // Buscar armazém principal (ALM01)
      const armazensSnap = await getDocs(collection(db, "armazens"));
      const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      const almoxarifado = armazens.find(a => a.codigo === 'ALM01' || a.tipo === 'ALMOXARIFADO');
      if (!almoxarifado) {
        throw new Error('Armazém principal não encontrado');
      }
      
      // Buscar ordem de produção
      const orderDoc = await getDoc(doc(db, "ordensProducao", orderId));
      if (!orderDoc.exists()) {
        throw new Error('Ordem de produção não encontrada');
      }
      
      const order = orderDoc.data();
      const armazemProducao = armazens.find(a => a.id === order.armazemProducaoId);
      
      if (!armazemProducao) {
        throw new Error('Armazém de produção não encontrado');
      }
      
      // Executar transferência
      return await this.executeTransfer({
        produtoId,
        armazemOrigemId: almoxarifado.id,
        armazemDestinoId: armazemProducao.id,
        quantidade,
        motivo: 'SUPRIMENTO_OP',
        ordemProducaoId: orderId,
        observacoes: `Transferência automática para suprir OP ${order.numero}`,
        solicitanteId: 'SISTEMA'
      });
      
    } catch (error) {
      console.error('Erro na transferência automática:', error);
      throw new Error(`Falha na transferência automática: ${error.message}`);
    }
  }
  
  /**
   * Transferência em lote para múltiplos materiais
   */
  static async batchTransfer(transfers) {
    const results = [];
    const errors = [];
    
    for (const transfer of transfers) {
      try {
        const result = await this.executeTransfer(transfer);
        results.push(result);
      } catch (error) {
        errors.push({
          transfer,
          error: error.message
        });
      }
    }
    
    return {
      sucessos: results.length,
      erros: errors.length,
      detalhes: { results, errors }
    };
  }
  
  /**
   * Valida dados da transferência
   */
  static validateTransferData(transferData) {
    const required = ['produtoId', 'armazemOrigemId', 'armazemDestinoId', 'quantidade'];
    
    for (const field of required) {
      if (!transferData[field]) {
        throw new Error(`Campo obrigatório: ${field}`);
      }
    }
    
    if (transferData.quantidade <= 0) {
      throw new Error('Quantidade deve ser maior que zero');
    }
    
    if (transferData.armazemOrigemId === transferData.armazemDestinoId) {
      throw new Error('Armazém de origem e destino não podem ser iguais');
    }
  }
  
  /**
   * Atualiza necessidades da ordem de produção
   */
  static async updateOrderRequirements(transaction, orderId, produtoId, quantidade) {
    const orderRef = doc(db, "ordensProducao", orderId);
    const orderDoc = await transaction.get(orderRef);
    
    if (orderDoc.exists()) {
      const order = orderDoc.data();
      
      if (order.materiaisNecessarios) {
        const materiaisAtualizados = order.materiaisNecessarios.map(material => {
          if (material.produtoId === produtoId) {
            return {
              ...material,
              necessidade: Math.max(0, material.necessidade - quantidade),
              saldoEstoque: (material.saldoEstoque || 0) + quantidade
            };
          }
          return material;
        });
        
        transaction.update(orderRef, {
          materiaisNecessarios: materiaisAtualizados,
          ultimaAtualizacao: Timestamp.now()
        });
      }
    }
  }
  
  /**
   * Gera número sequencial para transferência
   */
  static async generateTransferNumber() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Buscar último número
    const lastTransfer = await getDocs(
      query(
        collection(db, "transferenciasArmazem"),
        where("numero", ">=", `TRF${year}${month}`),
        where("numero", "<", `TRF${year}${month}9999`),
        orderBy("numero", "desc")
      )
    );
    
    let nextNumber = 1;
    if (!lastTransfer.empty) {
      const lastNumber = lastTransfer.docs[0].data().numero;
      const lastSequence = parseInt(lastNumber.slice(-7));
      nextNumber = lastSequence + 1;
    }
    
    return `TRF${year}${month}${String(nextNumber).padStart(4, '0')}`;
  }
  
  /**
   * Notifica sistemas sobre transferência
   */
  static async notifyTransfer(result) {
    try {
      // Atualizar cache de relatórios
      // Disparar eventos para dashboards
      // Verificar alertas de estoque
      console.log('Notificações de transferência enviadas');
    } catch (error) {
      console.warn('Erro ao enviar notificações:', error);
    }
  }
  
  /**
   * Busca histórico de transferências
   */
  static async getTransferHistory(filters = {}) {
    try {
      let transferQuery = collection(db, "transferenciasArmazem");
      
      // Aplicar filtros
      if (filters.produtoId) {
        transferQuery = query(transferQuery, where("produtoId", "==", filters.produtoId));
      }
      
      if (filters.armazemOrigemId) {
        transferQuery = query(transferQuery, where("armazemOrigemId", "==", filters.armazemOrigemId));
      }
      
      if (filters.dataInicio && filters.dataFim) {
        transferQuery = query(
          transferQuery,
          where("dataHora", ">=", filters.dataInicio),
          where("dataHora", "<=", filters.dataFim)
        );
      }
      
      // Ordenar por data
      transferQuery = query(transferQuery, orderBy("dataHora", "desc"));
      
      // Aplicar limite
      if (filters.limit) {
        transferQuery = query(transferQuery, limit(filters.limit));
      }
      
      const snapshot = await getDocs(transferQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
    } catch (error) {
      console.error('Erro ao buscar histórico:', error);
      throw new Error(`Falha ao buscar histórico: ${error.message}`);
    }
  }

  /**
   * 🆕 Sincroniza automaticamente o flag materialTransferido da OP
   * Esta função elimina a necessidade do botão "Processar Histórico"
   */
  static async syncOrderTransferFlag(orderId) {
    try {
      console.log(`🔄 [TransferService] Sincronizando flag da OP ${orderId}...`);

      // Buscar a OP
      const orderDoc = await getDoc(doc(db, "ordensProducao", orderId));
      if (!orderDoc.exists()) {
        console.warn(`⚠️ [TransferService] OP ${orderId} não encontrada`);
        return false;
      }

      const orderData = orderDoc.data();

      // Buscar todas as transferências desta OP
      const transfersQuery = query(
        collection(db, "transferenciasArmazem"),
        where("ordemProducaoId", "==", orderId),
        where("status", "==", "CONCLUIDA")
      );

      const transfersSnap = await getDocs(transfersQuery);
      const transfers = transfersSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`📦 [TransferService] Encontradas ${transfers.length} transferências para OP ${orderId}`);

      // Se não há materiais necessários, manter como não transferido
      if (!orderData.materiaisNecessarios || orderData.materiaisNecessarios.length === 0) {
        console.log(`⚠️ [TransferService] OP ${orderId} não possui materiais necessários definidos`);
        return false;
      }

      // Analisar status dos materiais
      const materialStatus = this.analyzeMaterialTransferStatus(
        orderData.materiaisNecessarios,
        transfers
      );

      const overallStatus = this.determineOverallTransferStatus(materialStatus);

      // Verificar se precisa atualizar
      const currentFlag = orderData.materialTransferido || false;
      const newFlag = overallStatus !== 'nao_transferido';

      if (currentFlag === newFlag) {
        console.log(`✅ [TransferService] Flag da OP ${orderId} já está correto: ${newFlag}`);
        return true;
      }

      // Preparar dados da transferência
      const transferData = {
        statusGeral: overallStatus,
        statusMateriais: materialStatus,
        ultimaAtualizacao: new Date().toISOString(),
        atualizacaoAutomatica: true,
        totalTransferencias: transfers.length,
        usuario: 'TransferService',
        metodo: 'sync_automatico'
      };

      // Atualizar OP automaticamente
      await updateDoc(doc(db, "ordensProducao", orderId), {
        materialTransferido: newFlag,
        dadosTransferencia: transferData,
        ultimaAtualizacaoTransferencia: Timestamp.now()
      });

      console.log(`✅ [TransferService] Flag da OP ${orderId} atualizado: ${currentFlag} → ${newFlag} (${overallStatus})`);

      // 🆕 Log de auditoria da sincronização
      await this.logTransferAudit(null, orderId, 'FLAG_SYNCED', {
        flagAnterior: currentFlag,
        flagNovo: newFlag,
        statusGeral: overallStatus,
        totalTransferencias: transfers.length,
        success: true
      });

      return true;

    } catch (error) {
      console.error(`❌ [TransferService] Erro ao sincronizar flag da OP ${orderId}:`, error);

      // 🆕 Log de auditoria do erro
      try {
        await this.logTransferAudit(null, orderId, 'FLAG_SYNC_ERROR', {
          error: error.message,
          stack: error.stack,
          success: false
        });
      } catch (logError) {
        console.error('❌ Erro ao registrar log de erro:', logError);
      }

      return false;
    }
  }

  /**
   * Analisa o status de transferência dos materiais
   */
  static analyzeMaterialTransferStatus(materialsNeeded, transfers) {
    if (!materialsNeeded || materialsNeeded.length === 0) {
      return [];
    }

    return materialsNeeded.map(material => {
      const materialTransfers = transfers.filter(t => {
        // Suporte para formato antigo e novo
        if (t.materiais && Array.isArray(t.materiais)) {
          return t.materiais.some(m => m.produtoId === material.produtoId);
        }
        return t.produtoId === material.produtoId;
      });

      const transferredQuantity = materialTransfers.reduce((total, t) => {
        if (t.materiais && Array.isArray(t.materiais)) {
          const transferredMaterial = t.materiais.find(m => m.produtoId === material.produtoId);
          return total + (transferredMaterial?.quantidade || 0);
        }
        return total + (t.quantidade || 0);
      }, 0);

      const needed = material.necessidade || 0;

      if (transferredQuantity >= needed) {
        return 'completo';
      } else if (transferredQuantity > 0) {
        return 'parcial';
      } else {
        return 'nao_transferido';
      }
    });
  }

  /**
   * Determina o status geral da transferência
   */
  static determineOverallTransferStatus(materialStatus) {
    if (!materialStatus || materialStatus.length === 0) {
      return 'nao_transferido';
    }

    const complete = materialStatus.filter(s => s === 'completo').length;
    const partial = materialStatus.filter(s => s === 'parcial').length;
    const notTransferred = materialStatus.filter(s => s === 'nao_transferido').length;

    if (complete === materialStatus.length) {
      return 'completo';
    } else if (notTransferred === materialStatus.length) {
      return 'nao_transferido';
    } else {
      return 'parcial';
    }
  }

  /**
   * 🆕 Sistema de logs de auditoria para rastreamento completo
   */
  static async logTransferAudit(transferId, orderId, action, details = {}) {
    try {
      const auditLog = {
        transferId: transferId,
        orderId: orderId,
        action: action,
        timestamp: Timestamp.now(),
        details: {
          ...details,
          userAgent: navigator.userAgent,
          url: window.location.href,
          sessionId: this.getSessionId()
        },
        usuario: 'TransferService',
        sistema: 'movimentacao_armazem'
      };

      await addDoc(collection(db, "auditoria_transferencias"), auditLog);

      console.log(`📝 [Auditoria] ${action} registrado para OP ${orderId}`);

    } catch (error) {
      console.error('❌ Erro ao registrar auditoria:', error);
      // Não falhar a operação principal por erro de log
    }
  }

  /**
   * Gera ou recupera ID da sessão para rastreamento
   */
  static getSessionId() {
    let sessionId = sessionStorage.getItem('transfer_session_id');
    if (!sessionId) {
      sessionId = `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('transfer_session_id', sessionId);
    }
    return sessionId;
  }

  /**
   * 🆕 Buscar logs de auditoria de uma OP
   */
  static async getOrderAuditLogs(orderId, limit = 50) {
    try {
      const logsQuery = query(
        collection(db, "auditoria_transferencias"),
        where("orderId", "==", orderId),
        orderBy("timestamp", "desc"),
        limit(limit)
      );

      const logsSnap = await getDocs(logsQuery);
      return logsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

    } catch (error) {
      console.error('❌ Erro ao buscar logs de auditoria:', error);
      return [];
    }
  }

  /**
   * 🆕 Estatísticas de sincronização
   */
  static async getSyncStats(days = 7) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const statsQuery = query(
        collection(db, "auditoria_transferencias"),
        where("timestamp", ">=", Timestamp.fromDate(startDate)),
        where("action", "==", "FLAG_SYNCED")
      );

      const statsSnap = await getDocs(statsQuery);
      const logs = statsSnap.docs.map(doc => doc.data());

      return {
        totalSyncs: logs.length,
        successfulSyncs: logs.filter(l => l.details.success !== false).length,
        failedSyncs: logs.filter(l => l.details.success === false).length,
        uniqueOrders: new Set(logs.map(l => l.orderId)).size,
        period: `${days} dias`
      };

    } catch (error) {
      console.error('❌ Erro ao calcular estatísticas:', error);
      return null;
    }
  }
}

// Exportar para uso global
window.TransferService = TransferService;
