/**
 * 🔧 PATCH UNIVERSAL - Correção condicaoPagamentoId undefined
 * 
 * Este patch corrige o erro:
 * "FirebaseError: Function addDoc() called with invalid data. 
 *  Unsupported field value: undefined (found in field condicaoPagamentoId)"
 * 
 * COMO USAR:
 * 1. Importe este arquivo no seu HTML
 * 2. <PERSON><PERSON> as funções antes de salvar dados no Firestore
 * 3. Use validateAndCleanData() antes de addDoc()
 */

import { 
  collection, 
  getDocs, 
  query, 
  where, 
  limit 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

/**
 * 🔍 BUSCAR CONDIÇÃO DE PAGAMENTO PADRÃO
 */
export async function getDefaultPaymentCondition(db) {
  try {
    console.log('🔍 Buscando condição de pagamento padrão...');
    
    // 1. Tentar buscar por descrição "À Vista"
    const aVistaQuery = query(
      collection(db, "condicoesPagamento"),
      where("descricao", "==", "À Vista"),
      limit(1)
    );
    
    const aVistaSnap = await getDocs(aVistaQuery);
    if (!aVistaSnap.empty) {
      const condition = { id: aVistaSnap.docs[0].id, ...aVistaSnap.docs[0].data() };
      console.log(`✅ Condição "À Vista" encontrada: ${condition.id}`);
      return condition;
    }
    
    // 2. Tentar buscar por tipo "A_VISTA"
    const tipoQuery = query(
      collection(db, "condicoesPagamento"),
      where("tipo", "==", "A_VISTA"),
      limit(1)
    );
    
    const tipoSnap = await getDocs(tipoQuery);
    if (!tipoSnap.empty) {
      const condition = { id: tipoSnap.docs[0].id, ...tipoSnap.docs[0].data() };
      console.log(`✅ Condição tipo "A_VISTA" encontrada: ${condition.id}`);
      return condition;
    }
    
    // 3. Buscar primeira condição disponível
    const allQuery = query(collection(db, "condicoesPagamento"), limit(1));
    const allSnap = await getDocs(allQuery);
    
    if (!allSnap.empty) {
      const condition = { id: allSnap.docs[0].id, ...allSnap.docs[0].data() };
      console.warn(`⚠️ Usando primeira condição disponível: ${condition.descricao}`);
      return condition;
    }
    
    throw new Error('Nenhuma condição de pagamento encontrada. Configure uma condição "À Vista" no sistema.');
    
  } catch (error) {
    console.error('❌ Erro ao buscar condição de pagamento:', error);
    throw error;
  }
}

/**
 * 🧹 LIMPAR CAMPOS UNDEFINED
 */
export function removeUndefinedFields(obj) {
  const cleanObj = { ...obj };
  
  Object.keys(cleanObj).forEach(key => {
    if (cleanObj[key] === undefined) {
      console.warn(`⚠️ Removendo campo undefined: ${key}`);
      delete cleanObj[key];
    }
  });
  
  return cleanObj;
}

/**
 * ✅ VALIDAR E CORRIGIR DADOS ANTES DE SALVAR
 */
export async function validateAndCleanData(db, data, requirePaymentCondition = true) {
  try {
    console.log('🔍 Validando dados antes de salvar...');
    
    let cleanData = removeUndefinedFields(data);
    
    // Se requer condição de pagamento e não tem
    if (requirePaymentCondition && !cleanData.condicaoPagamentoId) {
      console.warn('⚠️ condicaoPagamentoId não definido. Buscando condição padrão...');
      
      const defaultCondition = await getDefaultPaymentCondition(db);
      cleanData.condicaoPagamentoId = defaultCondition.id;
      
      console.log(`✅ Condição padrão aplicada: ${defaultCondition.id}`);
    }
    
    console.log('✅ Dados validados e limpos');
    return cleanData;
    
  } catch (error) {
    console.error('❌ Erro na validação de dados:', error);
    throw error;
  }
}

/**
 * 💰 VALIDAR ESPECIFICAMENTE PARA CONTAS A PAGAR
 */
export async function validateContaAPagar(db, contaData) {
  try {
    console.log('💰 Validando dados de conta a pagar...');
    
    // Campos obrigatórios para contas a pagar
    const requiredFields = ['valor', 'dataVencimento', 'fornecedorId'];
    
    for (const field of requiredFields) {
      if (!contaData[field]) {
        throw new Error(`Campo obrigatório não informado: ${field}`);
      }
    }
    
    // Validar e corrigir condição de pagamento
    const validatedData = await validateAndCleanData(db, contaData, true);
    
    console.log('✅ Conta a pagar validada');
    return validatedData;
    
  } catch (error) {
    console.error('❌ Erro na validação de conta a pagar:', error);
    throw error;
  }
}

/**
 * 📋 VALIDAR ESPECIFICAMENTE PARA PEDIDOS DE COMPRA
 */
export async function validatePedidoCompra(db, pedidoData) {
  try {
    console.log('📋 Validando dados de pedido de compra...');
    
    // Campos obrigatórios para pedidos
    const requiredFields = ['fornecedorId', 'itens', 'valorTotal'];
    
    for (const field of requiredFields) {
      if (!pedidoData[field]) {
        throw new Error(`Campo obrigatório não informado: ${field}`);
      }
    }
    
    // Validar e corrigir condição de pagamento
    const validatedData = await validateAndCleanData(db, pedidoData, true);
    
    console.log('✅ Pedido de compra validado');
    return validatedData;
    
  } catch (error) {
    console.error('❌ Erro na validação de pedido de compra:', error);
    throw error;
  }
}

/**
 * 🔧 FUNÇÃO DE CORREÇÃO RÁPIDA PARA USAR EM QUALQUER LUGAR
 */
export async function quickFix_condicaoPagamentoId(db, data) {
  try {
    // Se não tem condição de pagamento, buscar padrão
    if (!data.condicaoPagamentoId) {
      const defaultCondition = await getDefaultPaymentCondition(db);
      data.condicaoPagamentoId = defaultCondition.id;
    }
    
    // Remover campos undefined
    Object.keys(data).forEach(key => {
      if (data[key] === undefined) {
        delete data[key];
      }
    });
    
    return data;
    
  } catch (error) {
    console.error('❌ Erro no quick fix:', error);
    throw error;
  }
}

/**
 * 📝 EXEMPLO DE USO:
 * 
 * // ANTES (com erro):
 * await addDoc(collection(db, "contasAPagar"), contaData);
 * 
 * // DEPOIS (corrigido):
 * import { validateContaAPagar } from './patch_condicao_pagamento_universal.js';
 * 
 * const validatedData = await validateContaAPagar(db, contaData);
 * await addDoc(collection(db, "contasAPagar"), validatedData);
 * 
 * // OU uso rápido:
 * import { quickFix_condicaoPagamentoId } from './patch_condicao_pagamento_universal.js';
 * 
 * const fixedData = await quickFix_condicaoPagamentoId(db, contaData);
 * await addDoc(collection(db, "contasAPagar"), fixedData);
 */

// Exportar para uso global no window
if (typeof window !== 'undefined') {
  window.PaymentConditionPatch = {
    getDefaultPaymentCondition,
    removeUndefinedFields,
    validateAndCleanData,
    validateContaAPagar,
    validatePedidoCompra,
    quickFix_condicaoPagamentoId
  };
  
  console.log('✅ Patch de condição de pagamento carregado globalmente');
}
