firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T11:25:35.206Z', apps: 1}
recebimento_materiais_avancado.html:1097 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1173 Dados carregados: {pedidos: 39, produtos: 1671, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1183 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', atualizacoesEntrega: Array(1), historico: Array(1), ultimaLimpezaCritica: Timestamp, condicaoPagamento: '60DIAS', …}
recebimento_materiais_avancado.html:1185 📦 Exemplo de item: {unidade: 'PC', produtoId: '1sHECTgPNMjBw0ScLbbq', quantidade: 1, descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', valorUnitario: 1805.17, …}
 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', grupo: '401', tipo: 'MP', ultimoCusto: 0, cest: null, …}
 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf2: '', contaContabil: '', celular1: '', latitudeCLI: '', …}
 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
 📋 Pedidos de Compra: 39
 🏷️ Produtos: 1671
 🏢 Fornecedores: 778
 🏪 Armazéns: 9
 📋 Estrutura do pedido: (22) ['id', 'atualizacoesEntrega', 'historico', 'ultimaLimpezaCritica', 'condicaoPagamento', 'dataCriacao', 'solicitacaoId', 'limpoPor', 'valorTotal', 'ultimaAtualizacao', 'prazoEntrega', 'numero', 'alteradoPor', 'sincronizadoPor', 'cotacaoId', 'aprovadoPor', 'status', 'fornecedorId', 'dataAprovacao', 'criadoPor', 'numeroAnterior', 'itens']
 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', atualizacoesEntrega: Array(1), historico: Array(1), ultimaLimpezaCritica: Timestamp, condicaoPagamento: '60DIAS', …}
 📦 Estrutura do item: (7) ['unidade', 'produtoId', 'quantidade', 'descricao', 'valorUnitario', 'codigo', 'valorTotal']
 📦 Item exemplo: {unidade: 'PC', produtoId: '1sHECTgPNMjBw0ScLbbq', quantidade: 1, descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', valorUnitario: 1805.17, …}
 🏢 Estrutura do fornecedor: (71) ['id', 'cnpjCpf2', 'contaContabil', 'celular1', 'latitudeCLI', 'indicacao', 'autorizaXml2', 'celular2', 'ativo', 'celular3', 'ultimaCompra', 'departamento3', 'endereco', 'tipoPessoa', 'dataCadastro', 'codigo', 'categorias', 'numero', 'homePage', 'suframa', 'telefone1', 'reducao', 'inscricaoMunicipal', 'im', 'statusHomologacao', 'estado', 'categoriaPrincipal', 'telefone4', 'email3', 'codCusto', 'emailNfe', 'acrescimoCLI', 'longitudeCLI', 'codigoArea', 'codigoPais', 'email', 'autorizaXml1', 'cnpjCpf3', 'email2', 'bairro', 'temSubstituicao', 'observacoes', 'fax', 'contato3', 'cargo3', 'telefone3', 'limite', 'codigoRegiao', 'cidade', 'dataAtualizacao', 'cargo2', 'codigoClassificacao', 'pais', 'codigoVendedor', 'departamento1', 'razaoSocial', 'simplesNacional', 'contato1', 'telefone2', 'cnpjCpf', 'email1', 'departamento2', 'cotacao', 'cep', 'inscricaoEstadual', 'nascimento', 'complemento', 'nomeFantasia', 'contato2', 'tipo', 'intervista']
 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf2: '', contaContabil: '', celular1: '', latitudeCLI: '', …}
 🏷️ Estrutura do produto: (31) ['id', 'grupo', 'tipo', 'ultimoCusto', 'cest', 'tipoItem', 'descricao', 'status', 'pontoPedido', 'corredor', 'prateleira', 'loteCompra', 'unidadeSecundaria', 'ncm', 'fatorConversao', 'familia', 'centroCustoObrigatorio', 'margemLucro', 'unidade', 'armazemPadraoId', 'dataCadastro', 'rastreabilidadeLote', 'codigo', 'metodoCusteio', 'inspecaoRecebimento', 'posicao', 'precoVenda', 'estoqueMaximo', 'custoMedio', 'estoqueMinimo', 'origem']
 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', grupo: '401', tipo: 'MP', ultimoCusto: 0, cest: null, …}
 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
 📋 DEBUG populateOrderSelect - Total de pedidos: 39
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'APROVADO', temItens: 2}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'APROVADO', temItens: 5}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1414 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1441 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1344 📊 Dashboard atualizado: {pendentes: 15, atrasados: 0, parciais: 0, completos: 22}
recebimento_materiais_avancado.html:2701 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2710 Dados encontrados: {movimentacoes: 3489, estoqueQualidade: 63, recebimentosDetalhes: 16}
recebimento_materiais_avancado.html:2824 Total de registros de histórico encontrados: 99
recebimento_materiais_avancado.html:1616 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1556 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1565 🔍 DEBUG selectOrderById - currentOrder encontrado: {id: 'STenDi3OPZy4ieeJIrxk', ultimoRecebimento: {…}, criadoPor: 'Alex', dataAprovacao: Timestamp, uidAprovacao: 'sistema', …}
recebimento_materiais_avancado.html:1573 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1633 🔍 DEBUG loadSupplierInfo - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', ultimoRecebimento: {…}, criadoPor: 'Alex', dataAprovacao: Timestamp, uidAprovacao: 'sistema', …}
recebimento_materiais_avancado.html:1634 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1635 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1638 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1639 📋 Campos do currentOrder: (22) ['id', 'ultimoRecebimento', 'criadoPor', 'dataAprovacao', 'uidAprovacao', 'cotacaoNumero', 'fornecedorNome', 'observacoes', 'valorTotal', 'dataUltimaAtualizacao', 'numero', 'dataRecebimento', 'aprovadoPor', 'status', 'prazoEntrega', 'itens', 'recebidoPor', 'fornecedorId', 'condicoesPagamento', 'historico', 'dataCriacao', 'cotacaoId']
recebimento_materiais_avancado.html:1640 📄 Dados do fornecedor no pedido: {fornecedorId: 'YqfHIA1xCUe30ndFcIAj', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', fornecedorCnpj: undefined, fornecedorDocumento: undefined, fornecedorContato: undefined, …}
recebimento_materiais_avancado.html:1672 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: {id: 'YqfHIA1xCUe30ndFcIAj', observacoes: '', telefone3: '0', telefone2: '', email3: '', …}
recebimento_materiais_avancado.html:1676 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1677 📋 Campos disponíveis: (70) ['id', 'observacoes', 'telefone3', 'telefone2', 'email3', 'departamento1', 'cnpjCpf2', 'cotacao', 'departamento2', 'inscricaoMunicipal', 'email', 'ultimaCompra', 'reducao', 'codigoRegiao', 'contato2', 'fax', 'razaoSocial', 'email2', 'acrescimoCLI', 'cargo2', 'codigoPais', 'codigo', 'celular3', 'telefone1', 'codigoVendedor', 'cargo3', 'categorias', 'departamento3', 'contato3', 'complemento', 'email1', 'intervista', 'numero', 'codigoClassificacao', 'cnpjCpf', 'celular2', 'dataAtualizacao', 'autorizaXml1', 'simplesNacional', 'estado', 'contaContabil', 'im', 'emailNfe', 'codCusto', 'temSubstituicao', 'ativo', 'cidade', 'dataCadastro', 'pais', 'tipoPessoa', 'nascimento', 'endereco', 'cnpjCpf3', 'statusHomologacao', 'celular1', 'inscricaoEstadual', 'bairro', 'longitudeCLI', 'limite', 'suframa', 'tipo', 'latitudeCLI', 'codigoArea', 'nomeFantasia', 'contato1', 'telefone4', 'indicacao', 'autorizaXml2', 'cep', 'homePage']
recebimento_materiais_avancado.html:1678 📄 CNPJ/CPF campos: {cnpj: undefined, cpfCnpj: undefined, cnpjCpf: '07.686.277/0001-69', documento: undefined, cpf: undefined}
recebimento_materiais_avancado.html:1699 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:995 🔍 DEBUG extrairCnpjCpf - Tentativas: (17) [undefined, undefined, '07.686.277/0001-69', undefined, undefined, '07.686.277/0001-69', undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined]
recebimento_materiais_avancado.html:996 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1737 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1738 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1739 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1756 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1757 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1758 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1761 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: {fornecedor?.cnpj: undefined, fornecedor?.cpfCnpj: undefined, fornecedor?.cnpjCpf: '07.686.277/0001-69', fornecedor?.documento: undefined, fornecedor?.cpf: undefined, …}
recebimento_materiais_avancado.html:1586 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1895 🔍 DEBUG loadOrderItems - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', ultimoRecebimento: {…}, criadoPor: 'Alex', dataAprovacao: Timestamp, uidAprovacao: 'sistema', …}
recebimento_materiais_avancado.html:1896 🔍 DEBUG loadOrderItems - itens: (2) [{…}, {…}]
recebimento_materiais_avancado.html:1909 🔍 DEBUG loadOrderItems - Item 0: {quantidade: 2, codigo: '105239', unidade: 'PC', icms: 0, precoUnitario: 397, …}
recebimento_materiais_avancado.html:1912 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: {id: '94M0qmNlWSZt9opark0E', unidade: 'PC', familia: null, leadTime: 0, codigo: '105239', …}
recebimento_materiais_avancado.html:1919 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1931 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1909 🔍 DEBUG loadOrderItems - Item 1: {unidade: 'PC', icms: 0, ipi: 0, valorTotal: 397, codigo: '105240', …}
recebimento_materiais_avancado.html:1912 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: {id: 'UPqNBsHPmH3CAwc38Hrp', unidade: 'PC', dataCadastro: {…}, tipo: 'MP', descricao: 'CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM', …}
recebimento_materiais_avancado.html:1919 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1931 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2583 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2599 Consulta com índice falhou, usando consulta alternativa: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2599
await in loadDeliveryHistory
selectOrderById @ recebimento_materiais_avancado.html:1602
handleMouseUp_ @ unknown
await in handleMouseUp_
window.selectOrder @ recebimento_materiais_avancado.html:1629
onchange @ recebimento_materiais_avancado.html:641
handleMouseUp_ @ unknown
recebimento_materiais_avancado.html:2611 Dados encontrados: {movimentacoes: 0, estoqueQualidade: 0, recebimentosDetalhes: 0}
recebimento_materiais_avancado.html:2671 Total de registros de histórico encontrados: 0
recebimento_materiais_avancado.html:1257 TES selecionado: {codigo: '001', descricao: undefined, tipo: 'ENTRADA', atualizaEstoque: true}
recebimento_materiais_avancado.html:2134 📊 Resumo atualizado: {itemsWithQuantity: 0, totalItems: 0, totalValue: 0}
recebimento_materiais_avancado.html:2134 📊 Resumo atualizado: {itemsWithQuantity: 0, totalItems: 0, totalValue: 0}
recebimento_materiais_avancado.html:2134 📊 Resumo atualizado: {itemsWithQuantity: 0, totalItems: 0, totalValue: 0}
recebimento_materiais_avancado.html:2134 📊 Resumo atualizado: {itemsWithQuantity: 0, totalItems: 0, totalValue: 0}
recebimento_materiais_avancado.html:2144 🔄 Iniciando processamento do recebimento...
recebimento_materiais_avancado.html:2196 📦 Itens coletados para recebimento: (2) [{…}, {…}]
recebimento_materiais_avancado.html:2213 ✅ Processamento confirmado pelo usuário
recebimento_materiais_avancado.html:2233 🔄 Processando itens do recebimento...
recebimento_materiais_avancado.html:2255 ✅ Registro de recebimento criado: LdPmjJSAJpTP9R64f0xT
recebimento_materiais_avancado.html:2316 ❌ Erro ao processar item 105239: FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field produtoId in document movimentacoesEstoque/yJk31gEYFue9g67mMhHj)
processItemReceipt @ recebimento_materiais_avancado.html:2316
processReceiptItems @ recebimento_materiais_avancado.html:2259
await in processReceiptItems
window.processReceipt @ recebimento_materiais_avancado.html:2216
onclick @ recebimento_materiais_avancado.html:847
recebimento_materiais_avancado.html:2275 ❌ Erro ao processar itens: FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field produtoId in document movimentacoesEstoque/yJk31gEYFue9g67mMhHj)
processReceiptItems @ recebimento_materiais_avancado.html:2275
await in processReceiptItems
window.processReceipt @ recebimento_materiais_avancado.html:2216
onclick @ recebimento_materiais_avancado.html:847
recebimento_materiais_avancado.html:2225 ❌ Erro ao processar recebimento: FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field produtoId in document movimentacoesEstoque/yJk31gEYFue9g67mMhHj)
window.processReceipt @ recebimento_materiais_avancado.html:2225
await in window.processReceipt
onclick @ recebimento_materiais_avancado.html:847
