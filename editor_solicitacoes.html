<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor <PERSON> <PERSON><PERSON><PERSON><PERSON> - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .search-bar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }

        .search-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .table th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            padding: 15px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
        }

        .status-pendente { background: #fff3cd; color: #856404; }
        .status-aprovada { background: #d4edda; color: #155724; }
        .status-rejeitada { background: #f8d7da; color: #721c24; }
        .status-em_cotacao { background: #d1ecf1; color: #0c5460; }
        .status-excluida { background: #6c757d; color: white; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-control {
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .items-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
        }

        .item-row {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            display: grid;
            grid-template-columns: 1fr 1fr 100px 80px 120px 120px auto;
            gap: 10px;
            align-items: center;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification.success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .notification.error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .notification.warning {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .notification.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        /* Estilos para badges */
        .badge {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-success {
            color: #fff;
            background-color: #28a745;
        }

        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }

        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }

        .badge-info {
            color: #fff;
            background-color: #17a2b8;
        }

        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }

        .badge-light {
            color: #212529;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        /* Melhorar aparência dos checkboxes */
        .select-item {
            transform: scale(1.2);
            margin: 0;
        }

        #selectAll {
            transform: scale(1.3);
        }

        /* Estilo para linhas selecionadas */
        tr:has(.select-item:checked) {
            background-color: #e3f2fd !important;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .text-truncate {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .field-readonly {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .danger-zone {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .danger-zone h4 {
            color: #e53e3e;
            margin-bottom: 15px;
        }

    .view-btn {
      background-color: #17a2b8;
      color: white;
    }

    .view-btn:hover {
      background-color: #138496;
    }

    .delete-btn {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .delete-btn:hover {
      background-color: var(--danger-hover);
    }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> Editor Administrativo - Solicitações de Compra</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>ATENÇÃO:</strong> Esta é uma ferramenta administrativa. Use com cuidado. Alterações aqui afetam diretamente o banco de dados.
            </div>

            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" placeholder="Buscar por número, solicitante, departamento..." oninput="filterSolicitacoes()">
                <select id="sortOrder" class="form-control" style="width: auto;" onchange="applySorting()">
                    <option value="date_desc">Data (Mais Recente)</option>
                    <option value="date_asc">Data (Mais Antiga)</option>
                    <option value="number_desc">Número (Decrescente)</option>
                    <option value="number_asc">Número (Crescente)</option>
                    <option value="status">Status</option>
                    <option value="priority">Prioridade</option>
                </select>
                <button class="btn btn-primary" onclick="loadAllSolicitacoes()">
                    <i class="fas fa-sync"></i> Recarregar
                </button>
                <button class="btn btn-danger" onclick="deleteSelectedSolicitacoes()" id="deleteSelectedBtn" style="display: none;">
                    <i class="fas fa-trash"></i> Excluir Selecionadas
                </button>
                <div id="totalCount" style="font-weight: 600; color: #495057;"></div>
            </div></div>

            <div id="selectedCount" style="margin: 10px 0; font-weight: 600; color: #007bff; display: none;">
                <span id="selectedCountText">0 solicitações selecionadas</span>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Solicitante</th>
                            <th>Departamento</th>
                            <th>Status</th>
                            <th>Prioridade</th>
                            <th>Itens</th>
                            <th>Valor</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="solicitacoesTableBody">
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-spinner fa-spin fa-2x"></i><br><br>
                                Carregando solicitações...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Solicitação</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="solicitacaoId" name="id">

                    <!-- Seção: Identificação -->
                    <h4 style="margin-bottom: 15px; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-id-card"></i> Identificação
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="numero">Número da Solicitação</label>
                            <input type="text" id="numero" class="form-control" placeholder="Ex: SC-2024-0001">
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" class="form-control" required>
                                <option value="PENDENTE">Pendente</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="REJEITADA">Rejeitada</option>
                                <option value="EM_COTACAO">Em Cotação</option>
                                <option value="EXCLUIDA">Excluída</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tipo">Tipo</label>
                            <select id="tipo" class="form-control" required>
                                <option value="NORMAL">Normal</option>
                                <option value="URGENTE">Urgente</option>
                                <option value="EMERGENCIAL">Emergencial</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="origem">Origem</label>
                            <select id="origem" class="form-control" required>
                                <option value="MANUAL">Manual</option>
                                <option value="SISTEMA">Sistema</option>
                                <option value="IMPORTACAO">Importação</option>
                            </select>
                        </div>
                    </div>

                    <!-- Seção: Solicitante -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-user"></i> Solicitante
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="solicitante">Nome do Solicitante</label>
                            <input type="text" id="solicitante" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="solicitanteId">ID do Solicitante</label>
                            <input type="text" id="solicitanteId" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="departamento">Departamento (ID)</label>
                            <input type="text" id="departamento" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="departamentoNome">Nome do Departamento</label>
                            <input type="text" id="departamentoNome" class="form-control">
                        </div>
                    </div>

                    <!-- Seção: Datas -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-calendar"></i> Datas
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="dataCriacao">Data de Criação</label>
                            <input type="datetime-local" id="dataCriacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="ultimaAtualizacao">Última Atualização</label>
                            <input type="datetime-local" id="ultimaAtualizacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="dataNecessidade">Data Necessidade</label>
                            <input type="date" id="dataNecessidade" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="dataLimiteAprovacao">Data Limite Aprovação</label>
                            <input type="date" id="dataLimiteAprovacao" class="form-control">
                        </div>
                    </div>

                    <!-- Seção: Detalhes -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-info-circle"></i> Detalhes
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="prioridade">Prioridade</label>
                            <select id="prioridade" class="form-control" required>
                                <option value="BAIXA">Baixa</option>
                                <option value="MEDIA">Média</option>
                                <option value="ALTA">Alta</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="centroCustoId">Centro de Custo ID</label>
                            <input type="text" id="centroCustoId" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="justificativa">Justificativa</label>
                        <textarea id="justificativa" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="observacoes">Observações</label>
                        <textarea id="observacoes" class="form-control" rows="2"></textarea>
                    </div>

                    <!-- Seção: Itens -->
                    <div class="items-section">
                        <h4 style="margin-bottom: 15px; color: #495057;">
                            <i class="fas fa-list"></i> Itens da Solicitação
                            <button type="button" class="btn btn-success btn-sm" style="float: right;" onclick="addNewItem()">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </button>
                        </h4>
                        <div id="itemsContainer">
                            <!-- Itens serão adicionados dinamicamente -->
                        </div>
                    </div>

                    <!-- Seção: Campos de Sistema -->
                    <div id="systemFields" style="display: none;">
                        <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                            <i class="fas fa-cog"></i> Campos do Sistema
                            <button type="button" class="btn btn-secondary btn-sm" style="float: right;" onclick="toggleSystemFields()">
                                <i class="fas fa-eye"></i> Mostrar/Ocultar
                            </button>
                        </h4>
                        <div id="systemFieldsContent" style="display: none;">
                            <!-- Campos de aprovação, cotação, etc. -->
                        </div>
                    </div>

                    <!-- Zona de Perigo -->
                    <div class="danger-zone">
                        <h4><i class="fas fa-exclamation-triangle"></i> Zona de Perigo</h4>
                        <p style="margin-bottom: 15px;">Ações irreversíveis que afetam permanentemente os dados.</p>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteForever()">
                            <i class="fas fa-trash"></i> Excluir Permanentemente
                        </button>
                    </div>

                    <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal()" style="margin-right: 10px;">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script>
        // ===================================================================
        // EDITOR DE SOLICITAÇÕES - SISTEMA EMPRESARIAL
        // ===================================================================

        // Função global para mostrar notificações
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            if (!notification) return;

            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            notification.style.opacity = '1';

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 300);
            }, 3000);
        }
    </script>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            Timestamp,
            orderBy,
            query
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let allSolicitacoes = [];
        let filteredSolicitacoes = [];
        let currentEditId = null;

        // Carregar todas as solicitações
        window.loadAllSolicitacoes = async function() {
            try {
                showNotification('Carregando solicitações...', 'info');

                const q = query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"));
                const snapshot = await getDocs(q);

                allSolicitacoes = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                filteredSolicitacoes = [...allSolicitacoes];
                applySorting(); // Aplicar ordenação inicial
                renderSolicitacoes();
                updateTotalCount();

                showNotification(`${allSolicitacoes.length} solicitações carregadas com sucesso!`, 'success');
            } catch (error) {
                console.error('Erro ao carregar solicitações:', error);
                showNotification('Erro ao carregar solicitações: ' + error.message, 'error');
            }
        };

        // Ordenar solicitações
        window.applySorting = function() {
            const sortOrder = document.getElementById('sortOrder').value;

            filteredSolicitacoes.sort((a, b) => {
                switch (sortOrder) {
                    case 'date_asc':
                        const dateA_asc = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const dateB_asc = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return dateA_asc - dateB_asc;

                    case 'creation_desc':
                        // Nova opção: Ordem de criação (mais recentes primeiro)
                        const createA_desc = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const createB_desc = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return createB_desc - createA_desc;

                    case 'creation_asc':
                        // Nova opção: Ordem de criação (mais antigas primeiro)
                        const createA_asc = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const createB_asc = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return createA_asc - createB_asc;

                    case 'number_desc':
                        return (b.numero || 0) - (a.numero || 0);

                    case 'number_asc':
                        return (a.numero || 0) - (b.numero || 0);

                    case 'status':
                        const statusA = a.status || 'PENDENTE';
                        const statusB = b.status || 'PENDENTE';
                        return statusA.localeCompare(statusB);

                    case 'priority':
                        const priorityOrder = { 'CRITICA': 5, 'ALTA': 4, 'URGENTE': 3, 'MEDIA': 2, 'BAIXA': 1 };
                        const prioA = priorityOrder[a.prioridade] || 2;
                        const prioB = priorityOrder[b.prioridade] || 2;
                        return prioB - prioA;

                    default:
                        // Por padrão, usar ordem de criação (mais recentes primeiro)
                        const dateA_default = a.dataCriacao?.toDate ? a.dataCriacao.toDate() : new Date(a.dataCriacao || 0);
                        const dateB_default = b.dataCriacao?.toDate ? b.dataCriacao.toDate() : new Date(b.dataCriacao || 0);
                        return dateB_default - dateA_default;
                }
            });

            renderSolicitacoes();
        };

        // Gerenciar seleção múltipla
        window.toggleSelectAll = function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const itemCheckboxes = document.querySelectorAll('.select-item');

            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateSelection();
        };

        window.updateSelection = function() {
            const selectedCheckboxes = document.querySelectorAll('.select-item:checked');
            const totalCheckboxes = document.querySelectorAll('.select-item');
            const selectAllCheckbox = document.getElementById('selectAll');
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            const selectedCount = document.getElementById('selectedCount');
            const selectedCountText = document.getElementById('selectedCountText');

            // Atualizar checkbox "Selecionar Todos"
            if (selectedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedCheckboxes.length === totalCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }

            // Mostrar/ocultar botão de exclusão e contador
            if (selectedCheckboxes.length > 0) {
                deleteBtn.style.display = 'inline-block';
                selectedCount.style.display = 'block';
                selectedCountText.textContent = `${selectedCheckboxes.length} solicitação${selectedCheckboxes.length > 1 ? 'ões' : ''} selecionada${selectedCheckboxes.length > 1 ? 's' : ''}`;
            } else {
                deleteBtn.style.display = 'none';
                selectedCount.style.display = 'none';
            }
        };

        // Excluir solicitações selecionadas
        window.deleteSelectedSolicitacoes = async function() {
            const selectedCheckboxes = document.querySelectorAll('.select-item:checked');

            if (selectedCheckboxes.length === 0) {
                showNotification('Nenhuma solicitação selecionada!', 'warning');
                return;
            }

            const confirmText = prompt(`ATENÇÃO! Você está prestes a excluir PERMANENTEMENTE ${selectedCheckboxes.length} solicitação${selectedCheckboxes.length > 1 ? 'ões' : ''}.\n\nEsta ação é IRREVERSÍVEL.\n\nDigite "EXCLUIR PERMANENTEMENTE" para confirmar:`);

            if (confirmText !== 'EXCLUIR PERMANENTEMENTE') {
                showNotification('Operação cancelada.', 'warning');
                return;
            }

            try {
                showNotification('Excluindo solicitações...', 'info');                const deletePromises = Array.from(selectedCheckboxes).map(checkbox => 
                    deleteDoc(doc(db, "solicitacoesCompra", checkbox.value))
                );

                await Promise.all(deletePromises);

                showNotification(`${selectedCheckboxes.length} solicitação${selectedCheckboxes.length > 1 ? 'ões' : ''} excluída${selectedCheckboxes.length > 1 ? 's' : ''} permanentemente!`, 'success');

                // Recarregar dados
                await loadAllSolicitacoes();

            } catch (error) {
                console.error('Erro ao excluir solicitações:', error);
                showNotification('Erro ao excluir solicitações: ' + error.message, 'error');
            }
        };

        // Renderizar tabela
        function renderSolicitacoes() {
            const tbody = document.getElementById('solicitacoesTableBody');

            if (filteredSolicitacoes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-search fa-2x"></i><br><br>
                            Nenhuma solicitação encontrada
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredSolicitacoes.map(sol => {
                const dataFormatada = formatDate(sol.dataCriacao);
                const valorTotal = calculateTotal(sol.itens || []);
                const numItens = (sol.itens || []).length;

                return `
                    <tr>
                        <td>
                            <input type="checkbox" class="select-item" value="${sol.id}" onchange="updateSelection()">
                        </td>
                        <td><strong>${sol.numero || 'N/A'}</strong></td>
                        <td>${dataFormatada}</td>
                        <td class="text-truncate">${sol.solicitante || 'N/A'}</td>
                        <td class="text-truncate">${sol.departamentoNome || sol.departamento || 'N/A'}</td>
                        <td><span class="status status-${(sol.status || 'pendente').toLowerCase()}">${getStatusText(sol.status)}</span></td>
                        <td><span class="priority priority-${(sol.prioridade || 'media').toLowerCase()}">${sol.prioridade || 'MEDIA'}</span></td>
                        <td style="text-align: center;">${numItens}</td>
                        <td>R$ ${formatCurrency(valorTotal)}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="editSolicitacao('${sol.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Filtrar solicitações
        window.filterSolicitacoes = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            if (!searchTerm) {
                filteredSolicitacoes = [...allSolicitacoes];
            } else {
                filteredSolicitacoes = allSolicitacoes.filter(sol => 
                    (sol.numero || '').toLowerCase().includes(searchTerm) ||
                    (sol.solicitante || '').toLowerCase().includes(searchTerm) ||
                    (sol.departamento || '').toLowerCase().includes(searchTerm) ||
                    (sol.departamentoNome || '').toLowerCase().includes(searchTerm) ||
                    (sol.justificativa || '').toLowerCase().includes(searchTerm)
                );
            }

            applySorting(); // Manter ordenação após filtro
            updateTotalCount();
        };

        // Atualizar contador
        function updateTotalCount() {
            document.getElementById('totalCount').textContent = 
                `${filteredSolicitacoes.length} de ${allSolicitacoes.length} solicitações`;
        }        // Editar solicitação
        window.editSolicitacao = function(id) {
            const solicitacao = allSolicitacoes.find(s => s.id === id);
            if (!solicitacao) {
                showNotification('Solicitação não encontrada!', 'warning');
                return;
            }

            currentEditId = id; // Armazenar o ID da solicitação sendo editada
            populateForm(solicitacao);
            openModal();
        };

        // Preencher formulário
        function populateForm(solicitacao) {
            document.getElementById('solicitacaoId').value = solicitacao.id;
            document.getElementById('numero').value = solicitacao.numero || '';
            document.getElementById('status').value = solicitacao.status || 'PENDENTE';
            document.getElementById('tipo').value = solicitacao.tipo || 'NORMAL';
            document.getElementById('origem').value = solicitacao.origem || 'MANUAL';
            document.getElementById('solicitante').value = solicitacao.solicitante || '';
            document.getElementById('solicitanteId').value = solicitacao.solicitanteId || '';
            document.getElementById('departamento').value = solicitacao.departamento || '';
            document.getElementById('departamentoNome').value = solicitacao.departamentoNome || '';

            // Formatar e preencher campos de data/hora
            document.getElementById('dataCriacao').value = formatDateTimeInputValue(solicitacao.dataCriacao);
            document.getElementById('ultimaAtualizacao').value = formatDateTimeInputValue(solicitacao.ultimaAtualizacao);

            document.getElementById('dataNecessidade').value = formatDateInputValue(solicitacao.dataNecessidade);
            document.getElementById('dataLimiteAprovacao').value = formatDateInputValue(solicitacao.dataLimiteAprovacao);

            document.getElementById('prioridade').value = solicitacao.prioridade || 'MEDIA';
            document.getElementById('centroCustoId').value = solicitacao.centroCustoId || '';
            document.getElementById('justificativa').value = solicitacao.justificativa || '';
            document.getElementById('observacoes').value = solicitacao.observacoes || '';

            // Limpar e adicionar itens da solicitação
            const itemsContainer = document.getElementById('itemsContainer');
            itemsContainer.innerHTML = '';
            if (solicitacao.itens && solicitacao.itens.length > 0) {
                solicitacao.itens.forEach((item, index) => {
                    addItemToForm(item, index);
                });
            } else {
                addNewItem(); // Garantir que pelo menos um item esteja presente
            }
        }

        // Salvar alterações
        document.getElementById('editForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            try {
                showNotification('Salvando alterações...', 'info');

                const id = document.getElementById('solicitacaoId').value;
                const solicitacaoRef = doc(db, "solicitacoesCompra", id);

                const updatedData = {
                    numero: document.getElementById('numero').value,
                    status: document.getElementById('status').value,
                    tipo: document.getElementById('tipo').value,
                    origem: document.getElementById('origem').value,
                    solicitante: document.getElementById('solicitante').value,
                    solicitanteId: document.getElementById('solicitanteId').value,
                    departamento: document.getElementById('departamento').value,
                    departamentoNome: document.getElementById('departamentoNome').value,
                    dataNecessidade: convertDateToTimestamp(document.getElementById('dataNecessidade').value),
                    dataLimiteAprovacao: convertDateToTimestamp(document.getElementById('dataLimiteAprovacao').value),
                    prioridade: document.getElementById('prioridade').value,
                    centroCustoId: document.getElementById('centroCustoId').value,
                    justificativa: document.getElementById('justificativa').value,
                    observacoes: document.getElementById('observacoes').value,
                    ultimaAtualizacao: Timestamp.now()
                };

                // Coletar itens do formulário
                const items = [];
                const itemRows = document.querySelectorAll('.item-row');
                itemRows.forEach(row => {
                    const descricao = row.querySelector('.item-descricao').value;
                    const quantidade = parseFloat(row.querySelector('.item-quantidade').value);
                    const unidade = row.querySelector('.item-unidade').value;
                    const valorUnitario = parseFloat(row.querySelector('.item-valorUnitario').value);

                    if (descricao && !isNaN(quantidade) && quantidade > 0 && !isNaN(valorUnitario) && valorUnitario > 0) {
                        items.push({ descricao, quantidade, unidade, valorUnitario });
                    }
                });

                updatedData.itens = items;

                await updateDoc(solicitacaoRef, updatedData);

                showNotification('Solicitação atualizada com sucesso!', 'success');
                closeModal();
                await loadAllSolicitacoes(); // Recarregar dados
            } catch (error) {
                console.error('Erro ao atualizar solicitação:', error);
                showNotification('Erro ao atualizar solicitação: ' + error.message, 'error');
            }
        });

        // Exclusão permanente
        window.deleteForever = async function() {
            if (!currentEditId) {
                showNotification('Nenhuma solicitação selecionada para exclusão!', 'warning');
                return;
            }

            const confirmText = prompt('ATENÇÃO! Esta ação irá excluir PERMANENTEMENTE esta solicitação.\n\nEsta ação é IRREVERSÍVEL.\n\nDigite "EXCLUIR PERMANENTEMENTE" para confirmar:');

            if (confirmText !== 'EXCLUIR PERMANENTEMENTE') {
                showNotification('Operação cancelada.', 'warning');
                return;
            }

            try {
                showNotification('Excluindo solicitação permanentemente...', 'info');
                await deleteDoc(doc(db, "solicitacoesCompra", currentEditId));
                showNotification('Solicitação excluída permanentemente!', 'success');
                closeModal();
                await loadAllSolicitacoes(); // Recarregar dados
            } catch (error) {
                console.error('Erro ao excluir solicitação:', error);
                showNotification('Erro ao excluir solicitação: ' + error.message, 'error');
            }
        };

        // Adicionar novo item ao formulário
        window.addNewItem = function() {
            const itemsContainer = document.getElementById('itemsContainer');
            const index = itemsContainer.children.length;
            addItemToForm(null, index);
        };

        // Adicionar item ao formulário
        function addItemToForm(itemData, index) {
            const itemsContainer = document.getElementById('itemsContainer');

            const itemRow = document.createElement('div');
            itemRow.classList.add('item-row');
            itemRow.innerHTML = `
                <div class="form-group">
                    <label>Descrição</label>
                    <input type="text" class="form-control item-descricao" value="${itemData?.descricao || ''}" required>
                </div>
                <div class="form-group">
                    <label>Quantidade</label>
                    <input type="number" class="form-control item-quantidade" value="${itemData?.quantidade || ''}" min="0.01" step="0.01" required>
                </div>
                 <div class="form-group">
                    <label>Unidade</label>
                    <input type="text" class="form-control item-unidade" value="${itemData?.unidade || ''}" required>
                </div>
                <div class="form-group">
                    <label>Valor Unitário</label>
                    <input type="number" class="form-control item-valorUnitario" value="${itemData?.valorUnitario || ''}" min="0.01" step="0.01" required>
                </div>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">
                    <i class="fas fa-trash"></i> Remover
                </button>
            `;

            itemsContainer.appendChild(itemRow);
        }

        // Remover item do formulário
        window.removeItem = function(element) {
            element.closest('.item-row').remove();
        };

        // Calcular valor total
        function calculateTotal(items) {
            let total = 0;
            items.forEach(item => {
                total += item.quantidade * item.valorUnitario;
            });
            return total;
        }

        // Formatar moeda
        function formatCurrency(value) {
            return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
        }

        // Formatar data
        function formatDate(date) {
            if (!date) return 'N/A';
            const dateObj = date instanceof Date ? date : date.toDate ? date.toDate() : new Date(date);
            return dateObj.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
        }

        // Função auxiliar para formatar a data e hora para o formato de entrada datetime-local
        function formatDateTimeInputValue(date) {
            if (!date) return '';

            const dateObj = date instanceof Date ? date : date.toDate ? date.toDate() : new Date(date);

            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            const hours = String(dateObj.getHours()).padStart(2, '0');
            const minutes = String(dateObj.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // Função auxiliar para formatar a data para o formato de entrada date
        function formatDateInputValue(date) {
            if (!date) return '';
            const dateObj = date instanceof Date ? date : date.toDate ? date.toDate() : new Date(date);
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Converter data para timestamp
        function convertDateToTimestamp(dateString) {
            if (!dateString) return null;

            const dateObj = new Date(dateString);
            if (isNaN(dateObj.getTime())) {
                console.error("Data inválida detectada:", dateString);
                return null;
            }

            return Timestamp.fromDate(dateObj);
        }

         // Função para obter o texto de status com base no valor
         function getStatusText(status) {
            switch (status) {
                case 'PENDENTE':
                    return 'Pendente';
                case 'APROVADA':
                    return 'Aprovada';
                case 'REJEITADA':
                    return 'Rejeitada';
                case 'EM_COTACAO':
                    return 'Em Cotação';
                case 'EXCLUIDA':
                    return 'Excluída';
                default:
                    return 'Desconhecido';
            }
        }

        // Abrir modal
        window.openModal = function() {
            document.getElementById('editModal').style.display = 'block';
        };

        // Fechar modal
        window.closeModal = function() {
            document.getElementById('editModal').style.display = 'none';
        };

        // Toggle system fields
        window.toggleSystemFields = function() {
            const systemFieldsContent = document.getElementById('systemFieldsContent');
            systemFieldsContent.style.display = systemFieldsContent.style.display === 'none' ? 'block' : 'none';
        };

        // Carregar solicitações ao iniciar
        loadAllSolicitacoes();
    </script>
</body>
</html>