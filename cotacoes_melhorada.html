<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Cotações - Sistema ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 3px solid #ecf0f1;
            margin-bottom: 30px;
            overflow-x: auto;
        }

        .tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #7f8c8d;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateY(-2px);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.abertas {
            border-left-color: #f39c12;
        }

        .stat-card.enviadas {
            border-left-color: #17a2b8;
        }

        .stat-card.respondidas {
            border-left-color: #27ae60;
        }

        .stat-card.aprovadas {
            border-left-color: #3498db;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .filters {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-bar {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 18px;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
        }

        .status-aberta {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-enviada {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-respondida {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-aprovada {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .status-fechada {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: scale(1.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .supplier-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .supplier-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .supplier-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .supplier-card.selected {
            border-color: #27ae60;
            background: #d4edda;
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
            transform: translateY(-2px);
        }

        .supplier-card:hover {
            cursor: pointer;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .supplier-card.selected:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(39, 174, 96, 0.4);
        }

        .response-indicators {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        .response-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .response-dot.responded {
            background-color: #27ae60;
        }

        .response-dot.pending {
            background-color: #ccc;
            border: 1px solid #999;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            border: 1px solid #e9ecef;
            text-align: left;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .best-price {
            background: #d4edda !important;
            font-weight: bold;
            color: #155724;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            opacity: 1;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        /* Estilos para cotações aglutinadas */
        .quotation-row.aglutinada {
            background: linear-gradient(90deg, #e3f2fd 0%, #f8f9fa 100%);
            border-left: 4px solid #2196f3;
        }

        .quotation-row.aglutinada-filha {
            background: linear-gradient(90deg, #fff3e0 0%, #f8f9fa 100%);
            border-left: 4px solid #ff9800;
            opacity: 0.8;
        }

        .quotation-row.hidden {
            display: none;
        }

        .aglutinacao-badge {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 5px;
        }

        .aglutinacao-badge.filha {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .aglutinacao-info {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .expand-collapse-btn {
            background: none;
            border: none;
            color: #2196f3;
            cursor: pointer;
            font-size: 0.9rem;
            padding: 2px 5px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .expand-collapse-btn:hover {
            background: #e3f2fd;
            color: #1976d2;
        }

        .quotation-checkbox {
            transform: scale(1.2);
            cursor: pointer;
        }

        /* Estilos para cotações fechadas */
        .quotation-row.fechada {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            opacity: 0.7;
            border-left: 4px solid #6c757d;
        }

        .quotation-row.fechada td {
            color: #6c757d !important;
        }

        .quotation-row.fechada .status {
            background: #6c757d !important;
        }

        .fechada-badge {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 5px;
        }

        /* Estilos de Paginação */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 25px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            background: white;
            color: #495057;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .pagination button:hover:not(:disabled) {
            border-color: #3498db;
            color: #3498db;
            background: #f8f9fa;
            transform: translateY(-1px);
        }

        .pagination button.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border-color: #3498db;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f8f9fa;
            color: #6c757d;
        }

        .pagination-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 10px 0;
            font-size: 14px;
            color: #7f8c8d;
            border-top: 1px solid #e9ecef;
        }

        .items-per-page {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .items-per-page select {
            padding: 5px 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .integration-badge {
            background: #e7f3ff;
            color: #0066cc;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .workflow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .workflow-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .workflow-step.active::after {
            background: #3498db;
        }

        .workflow-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            z-index: 2;
            position: relative;
        }

        .workflow-step.active .workflow-icon {
            background: #3498db;
            color: white;
        }

        .workflow-step.completed .workflow-icon {
            background: #27ae60;
            color: white;
        }

        .workflow-label {
            font-size: 12px;
            text-align: center;
            color: #7f8c8d;
            font-weight: 600;
        }

        .workflow-step.active .workflow-label {
            color: #3498db;
        }

        .workflow-step.completed .workflow-label {
            color: #27ae60;
        }

        /* Estilos para edição de itens */
        .item-edit-row {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .item-edit-row:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .item-edit-row.new-item {
            border-color: #28a745;
            background: #f8fff9;
        }

        .item-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .item-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .edit-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .edit-tab {
            padding: 12px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .edit-tab.active {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
        }

        .edit-tab:hover:not(.active) {
            background: #e9ecef;
            color: #495057;
        }

        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .summary-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 16px;
            color: #28a745;
        }

        .field-group {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .field-group h5 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-file-invoice"></i> Gestão de Cotações</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openNewQuotationModal()">
                    <i class="fas fa-plus"></i> Nova Cotação
                </button>
                <button class="btn btn-info" onclick="importFromRequests()">
                    <i class="fas fa-file-import"></i> Importar Solicitações
                </button>
                <button class="btn btn-warning" onclick="findQuotation('CT-2506-0025')">
                    <i class="fas fa-search"></i> Encontrar CT-2506-0025
                </button>
                <a href="cadastro_fornecedores.html" class="btn btn-warning" target="_blank" title="Gerenciar Fornecedores">
                    <i class="fas fa-truck"></i> Fornecedores
                </a>
                <button class="btn btn-warning" onclick="exportReport()">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <a href="index.html" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Workflow de Cotações -->
            <div class="workflow-steps">
                <div class="workflow-step completed">
                    <div class="workflow-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="workflow-label">Solicitação</div>
                </div>
                <div class="workflow-step active">
                    <div class="workflow-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="workflow-label">Cotação</div>
                </div>
                <div class="workflow-step">
                    <div class="workflow-icon">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="workflow-label">Respostas</div>
                </div>
                <div class="workflow-step">
                    <div class="workflow-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="workflow-label">Pedido</div>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card abertas">
                    <div class="stat-number" id="statAbertas">0</div>
                    <div class="stat-label">Abertas</div>
                </div>
                <div class="stat-card enviadas">
                    <div class="stat-number" id="statEnviadas">0</div>
                    <div class="stat-label">Enviadas</div>
                </div>
                <div class="stat-card respondidas">
                    <div class="stat-number" id="statRespondidas">0</div>
                    <div class="stat-label">Respondidas</div>
                </div>
                <div class="stat-card aprovadas">
                    <div class="stat-number" id="statAprovadas">0</div>
                    <div class="stat-label">Aprovadas</div>
                </div>
            </div>

            <!-- Abas -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('cotacoes')">
                    <i class="fas fa-file-invoice"></i> Cotações
                </button>
                <button class="tab" onclick="showTab('respostas')">
                    <i class="fas fa-reply"></i> Respostas
                </button>
                <button class="tab" onclick="showTab('comparacao')">
                    <i class="fas fa-balance-scale"></i> Comparação
                </button>
                <button class="tab" onclick="showTab('integracao')">
                    <i class="fas fa-link"></i> Integração
                </button>
            </div>

            <!-- Aba Cotações -->
            <div id="cotacoes" class="tab-content active">
                <!-- Barra de Pesquisa -->
                <div class="search-bar">
                    <input type="text" class="search-input" id="searchInput" placeholder="Pesquisar por número, fornecedor, solicitação...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- Ações Rápidas -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showAllQuotations()">
                        <i class="fas fa-list"></i> Todas
                    </button>
                    <button class="btn btn-warning" onclick="showOpenQuotations()">
                        <i class="fas fa-folder-open"></i> Abertas
                    </button>
                    <button class="btn btn-info" onclick="showSentQuotations()">
                        <i class="fas fa-paper-plane"></i> Enviadas
                    </button>
                    <button class="btn btn-success" onclick="showRespondedQuotations()">
                        <i class="fas fa-check"></i> Respondidas
                    </button>
                    <button class="btn btn-secondary" onclick="showPendingApproval()">
                        <i class="fas fa-clock"></i> Aguardando Aprovação
                    </button>
                </div>

                <!-- Filtros -->
                <div class="filters">
                    <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Data Início</label>
                            <input type="date" class="form-control" id="dataInicio">
                        </div>
                        <div class="form-group">
                            <label>Data Fim</label>
                            <input type="date" class="form-control" id="dataFim">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control" id="statusFilter">
                                <option value="">Todos</option>
                                <option value="ABERTA">Aberta</option>
                                <option value="ENVIADA">Enviada</option>
                                <option value="RESPONDIDA">Respondida</option>
                                <option value="APROVADA">Aprovada</option>
                                <option value="FECHADA">Fechada</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Solicitação Origem</label>
                            <select class="form-control" id="solicitacaoFilter">
                                <option value="">Todas</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Fornecedor</label>
                            <select class="form-control" id="fornecedorFilter">
                                <option value="">Todos</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: flex-end;">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Aplicar Filtros
                        </button>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Limpar
                        </button>
                    </div>
                </div>

                <!-- Controles de Aglutinação -->
                <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h5 style="margin: 0; color: #2c3e50;">
                            <i class="fas fa-layer-group"></i> Gerenciamento de Cotações
                        </h5>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <div id="selectedQuotationsCounter" style="background: #e7f3ff; color: #0066cc; padding: 5px 12px; border-radius: 15px; font-size: 0.9rem; font-weight: bold;">
                                <i class="fas fa-check-square"></i> 0 selecionadas
                            </div>
                            <button type="button" class="btn btn-info btn-sm" onclick="toggleAglutinatedView()">
                                <i class="fas fa-eye" id="viewToggleIcon"></i>
                                <span id="viewToggleText">Mostrar Aglutinadas</span>
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="toggleClosedView()">
                                <i class="fas fa-eye-slash" id="closedToggleIcon"></i>
                                <span id="closedToggleText">Mostrar Fechadas</span>
                            </button>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-success btn-sm" onclick="aglutinarCotacoes()" id="btnAglutinar" disabled>
                            <i class="fas fa-compress-arrows-alt"></i> Aglutinar Selecionadas
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="dividirCotacao()" id="btnDividir" disabled>
                            <i class="fas fa-expand-arrows-alt"></i> Dividir Cotação
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times"></i> Limpar Seleção
                        </button>
                        <div style="margin-left: auto; font-size: 0.9rem; color: #6c757d;">
                            <i class="fas fa-info-circle"></i>
                            Selecione 2+ cotações para aglutinar ou 1 aglutinada para dividir<br>
                            <small>Cotações fechadas ficam ocultas por padrão</small>
                        </div>
                    </div>
                </div>

                <!-- Tabela de Cotações -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAllQuotations" onchange="toggleAllQuotations()" title="Selecionar todas">
                                </th>
                                <th>Número</th>
                                <th>Data / Prazo</th>
                                <th>Solicitação <span class="integration-badge">Integrada</span></th>
                                <th>Fornecedores <span class="integration-badge">Homologados</span></th>
                                <th>Respostas</th>
                                <th>Valor Estimado</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="quotationsTableBody">
                            <!-- Dados carregados dinamicamente -->
                        </tbody>
                    </table>

                    <!-- Paginação -->
                    <div class="pagination-info">
                        <div class="items-per-page">
                            <label>Itens por página:</label>
                            <select id="itemsPerPage" onchange="changeItemsPerPage()">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div id="paginationInfo">
                            Mostrando 0 de 0 cotações
                        </div>
                        <div id="hiddenQuotationsInfo" style="font-size: 0.9rem; color: #6c757d; margin-top: 5px;">
                            <!-- Info sobre cotações ocultas -->
                        </div>
                    </div>

                    <div class="pagination" id="pagination">
                        <!-- Controles de paginação serão inseridos aqui -->
                    </div>
                </div>
            </div>

            <!-- Aba Respostas -->
            <div id="respostas" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Visualize e gerencie as respostas dos fornecedores às cotações enviadas.
                </div>

                <div id="responsesContainer">
                    <!-- Respostas carregadas dinamicamente -->
                </div>
            </div>

            <!-- Aba Comparação -->
            <div id="comparacao" class="tab-content">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Compare preços e condições dos fornecedores para tomar a melhor decisão.
                </div>

                <div class="form-group" style="max-width: 300px;">
                    <label>Selecione uma cotação para comparar:</label>
                    <select class="form-control" id="quotationCompareSelect" onchange="loadComparison()">
                        <option value="">Selecione...</option>
                    </select>
                </div>

                <div id="comparisonContainer">
                    <!-- Comparação carregada dinamicamente -->
                </div>
            </div>

            <!-- Aba Integração -->
            <div id="integracao" class="tab-content">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Sistema Integrado!</strong> As cotações são automaticamente criadas a partir das solicitações aprovadas.
                </div>

                <div class="workflow-steps">
                    <div class="workflow-step completed">
                        <div class="workflow-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="workflow-label">Solicitação Criada</div>
                    </div>
                    <div class="workflow-step completed">
                        <div class="workflow-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="workflow-label">Solicitação Aprovada</div>
                    </div>
                    <div class="workflow-step active">
                        <div class="workflow-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="workflow-label">Cotação Gerada</div>
                    </div>
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="workflow-label">Enviada aos Fornecedores</div>
                    </div>
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-reply"></i>
                        </div>
                        <div class="workflow-label">Respostas Recebidas</div>
                    </div>
                    <div class="workflow-step">
                        <div class="workflow-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="workflow-label">Pedido Criado</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="integrationSolicitacoes">0</div>
                        <div class="stat-label">Solicitações Aprovadas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="integrationCotacoes">0</div>
                        <div class="stat-label">Cotações Geradas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="integrationPedidos">0</div>
                        <div class="stat-label">Pedidos Criados</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="integrationTempo">0</div>
                        <div class="stat-label">Tempo Médio (dias)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Cotação -->
    <div id="newQuotationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus"></i> Nova Cotação</h2>
                <span class="close" onclick="closeModal('newQuotationModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Integração Automática:</strong> Selecione uma solicitação aprovada para gerar automaticamente a cotação.
                </div>

                <form id="newQuotationForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label>Solicitação de Origem <span style="color: red;">*</span></label>
                            <select class="form-control" id="solicitacaoOrigem" required onchange="loadRequestItems()">
                                <option value="">Selecione uma solicitação aprovada...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Prazo para Resposta (dias)</label>
                            <input type="number" class="form-control" id="prazoResposta" value="7" min="1" max="30">
                        </div>
                        <div class="form-group">
                            <label>Data Limite</label>
                            <input type="date" class="form-control" id="dataLimite">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" id="observacoesCotacao" rows="3" placeholder="Informações adicionais para os fornecedores..."></textarea>
                    </div>

                    <h4><i class="fas fa-list"></i> Itens da Cotação</h4>
                    <div id="requestItemsContainer">
                        <p style="color: #6c757d; text-align: center; padding: 20px;">
                            Selecione uma solicitação para carregar os itens automaticamente.
                        </p>
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4><i class="fas fa-truck"></i> Fornecedores</h4>
                        <div id="supplierCounter" style="background: #e7f3ff; color: #0066cc; padding: 8px 15px; border-radius: 20px; font-weight: bold; font-size: 14px;">
                            <i class="fas fa-users"></i> 0 selecionados
                        </div>
                    </div>

                    <!-- Campo de Pesquisa -->
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db;">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <div style="flex: 1;">
                                <label style="font-weight: bold; color: #2c3e50; margin-bottom: 5px; display: block;">
                                    <i class="fas fa-search"></i> Pesquisar Fornecedores
                                </label>
                                <input type="text"
                                       id="supplierSearch"
                                       class="form-control"
                                       placeholder="Digite o código, nome ou razão social do fornecedor..."
                                       onkeyup="filterSuppliers()"
                                       style="border: 2px solid #e9ecef; border-radius: 6px; padding: 10px;">
                            </div>
                            <div>
                                <button type="button" class="btn btn-info btn-sm" onclick="clearSupplierSearch()" style="margin-top: 25px;">
                                    <i class="fas fa-times"></i> Limpar
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9rem; color: #6c757d;">
                            <i class="fas fa-info-circle"></i>
                            Pesquise por: <strong>código</strong>, <strong>nome fantasia</strong> ou <strong>razão social</strong> do fornecedor
                        </div>
                    </div>

                    <div class="supplier-grid" id="suppliersGrid">
                        <!-- Fornecedores carregados dinamicamente -->
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('newQuotationModal')">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Criar Cotação
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Editar Cotação Completo -->
    <div id="editQuotationModal" class="modal">
        <div class="modal-content" style="max-width: 1600px;">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Cotação</h2>
                <span class="close" onclick="closeModal('editQuotationModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Abas de Edição -->
                <div class="tabs" style="margin-bottom: 20px;">
                    <button type="button" class="tab active" onclick="showEditTab('dadosGerais', event)">
                        <i class="fas fa-info-circle"></i> Dados Gerais
                    </button>
                    <button type="button" class="tab" onclick="showEditTab('itens', event)">
                        <i class="fas fa-list"></i> Itens
                    </button>
                    <button type="button" class="tab" onclick="showEditTab('fornecedores', event)">
                        <i class="fas fa-truck"></i> Fornecedores
                    </button>
                    <button type="button" class="tab" onclick="showEditTab('observacoes', event)">
                        <i class="fas fa-comment"></i> Observações
                    </button>
                </div>

                <form id="editQuotationForm">
                    <!-- Aba Dados Gerais -->
                    <div id="dadosGerais" class="tab-content active">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Número da Cotação</label>
                                <input type="text" class="form-control" id="editNumero" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control" id="editStatus">
                                    <option value="ABERTA">Aberta</option>
                                    <option value="ENVIADA">Enviada</option>
                                    <option value="RESPONDIDA">Respondida</option>
                                    <option value="APROVADA">Aprovada</option>
                                    <option value="FECHADA">Fechada</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Data de Criação</label>
                                <input type="text" class="form-control" id="editDataCriacao" readonly style="background: #f8f9fa;">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Solicitação de Origem</label>
                                <select class="form-control" id="editSolicitacaoOrigem" onchange="loadEditRequestItems()">
                                    <option value="">Selecione uma solicitação...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Prazo para Resposta (dias)</label>
                                <input type="number" class="form-control" id="editPrazoResposta" min="1" max="30">
                            </div>
                            <div class="form-group">
                                <label>Data Limite</label>
                                <input type="date" class="form-control" id="editDataLimite">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Valor Total Estimado</label>
                                <input type="text" class="form-control" id="editValorTotal" readonly style="background: #f8f9fa; font-weight: bold; color: #27ae60;">
                            </div>
                            <div class="form-group">
                                <label>Criado Por</label>
                                <input type="text" class="form-control" id="editCriadoPor" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="form-group">
                                <label>Última Atualização</label>
                                <input type="text" class="form-control" id="editUltimaAtualizacao" readonly style="background: #f8f9fa;">
                            </div>
                        </div>
                    </div>

                    <!-- Aba Itens -->
                    <div id="itens" class="tab-content">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h4><i class="fas fa-list"></i> Itens da Cotação</h4>
                            <div>
                                <button type="button" class="btn btn-success btn-sm" onclick="addNewItem()">
                                    <i class="fas fa-plus"></i> Adicionar Item
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="importItemsFromRequest()">
                                    <i class="fas fa-file-import"></i> Importar da Solicitação
                                </button>
                            </div>
                        </div>

                        <div id="editItemsContainer">
                            <!-- Itens carregados dinamicamente -->
                        </div>
                    </div>

                    <!-- Aba Fornecedores -->
                    <div id="fornecedores" class="tab-content">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h4><i class="fas fa-truck"></i> Fornecedores da Cotação</h4>
                            <div>
                                <div id="editSupplierCounter" style="background: #e7f3ff; color: #0066cc; padding: 8px 15px; border-radius: 20px; font-weight: bold; font-size: 14px; display: inline-block; margin-right: 10px;">
                                    <i class="fas fa-users"></i> 0 selecionados
                                </div>
                                <button type="button" class="btn btn-success btn-sm" onclick="selectAllEditSuppliers()">
                                    <i class="fas fa-check-double"></i> Selecionar Todos
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="deselectAllEditSuppliers()">
                                    <i class="fas fa-times"></i> Limpar Seleção
                                </button>
                            </div>
                        </div>

                        <!-- Campo de Pesquisa -->
                        <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db;">
                            <div style="display: flex; gap: 15px; align-items: center;">
                                <div style="flex: 1;">
                                    <label style="font-weight: bold; color: #2c3e50; margin-bottom: 5px; display: block;">
                                        <i class="fas fa-search"></i> Pesquisar Fornecedores
                                    </label>
                                    <input type="text"
                                           id="editSupplierSearch"
                                           class="form-control"
                                           placeholder="Digite o código, nome ou razão social do fornecedor..."
                                           onkeyup="filterEditSuppliers()"
                                           style="border: 2px solid #e9ecef; border-radius: 6px; padding: 10px;">
                                </div>
                                <div>
                                    <button type="button" class="btn btn-info btn-sm" onclick="clearEditSupplierSearch()" style="margin-top: 25px;">
                                        <i class="fas fa-times"></i> Limpar
                                    </button>
                                </div>
                            </div>
                            <div style="margin-top: 10px; font-size: 0.9rem; color: #6c757d;">
                                <i class="fas fa-info-circle"></i>
                                Pesquise por: <strong>código</strong>, <strong>nome fantasia</strong> ou <strong>razão social</strong> do fornecedor
                            </div>
                        </div>

                        <div class="supplier-grid" id="editSuppliersGrid">
                            <!-- Fornecedores carregados dinamicamente -->
                        </div>
                    </div>

                    <!-- Aba Observações -->
                    <div id="observacoes" class="tab-content">
                        <h4><i class="fas fa-comment"></i> Observações e Informações Adicionais</h4>

                        <div class="form-group">
                            <label>Observações Gerais</label>
                            <textarea class="form-control" id="editObservacoes" rows="5" placeholder="Informações adicionais para os fornecedores..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>Condições Especiais</label>
                            <textarea class="form-control" id="editCondicoes" rows="3" placeholder="Condições especiais de pagamento, entrega, etc..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Local de Entrega</label>
                                <input type="text" class="form-control" id="editLocalEntrega" placeholder="Endereço de entrega...">
                            </div>
                            <div class="form-group">
                                <label>Contato Responsável</label>
                                <input type="text" class="form-control" id="editContatoResponsavel" placeholder="Nome do responsável...">
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn btn-danger" onclick="deleteQuotation()">
                            <i class="fas fa-trash"></i> Excluir Cotação
                        </button>
                        <button type="button" class="btn btn-warning" onclick="duplicateQuotation()">
                            <i class="fas fa-copy"></i> Duplicar
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('editQuotationModal')">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Detalhes da Cotação -->
    <div id="quotationDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-eye"></i> Detalhes da Cotação</h2>
                <span class="close" onclick="closeModal('quotationDetailsModal')">&times;</span>
            </div>
            <div class="modal-body" id="quotationDetailsBody">
                <!-- Conteúdo carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal Comparação de Preços -->
    <div id="comparisonModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-balance-scale"></i> Comparação de Preços</h2>
                <span class="close" onclick="closeModal('comparisonModal')">&times;</span>
            </div>
            <div class="modal-body" id="comparisonModalBody">
                <!-- Conteúdo carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Modal Gerenciar Fornecedores -->
    <div id="manageSuppliersModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-truck"></i> Gerenciar Fornecedores da Cotação</h2>
                <span class="close" onclick="closeModal('manageSuppliersModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Fornecedores Homologados e Ativos:</strong> Apenas fornecedores com status ativo e homologados aparecem na lista.
                </div>

                <div id="manageSuppliersContainer">
                    <!-- Fornecedores carregados dinamicamente -->
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('manageSuppliersModal')">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveSuppliersChanges()">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Aglutinação Avançada -->
    <div id="aglutinacaoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-compress-arrows-alt"></i> Aglutinar Cotações</h2>
                <span class="close" onclick="closeModal('aglutinacaoModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Aglutinação de Cotações:</strong> Combine múltiplas cotações em uma única cotação principal.
                </div>

                <div id="aglutinacaoPreview">
                    <!-- Preview das cotações selecionadas -->
                </div>

                <div class="form-group">
                    <label>Observações da Aglutinação</label>
                    <textarea class="form-control" id="aglutinacaoObservacoes" rows="3"
                              placeholder="Motivo da aglutinação, instruções especiais, etc..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="manterFornecedores" checked>
                            Consolidar fornecedores únicos
                        </label>
                        <small class="form-text">Remove fornecedores duplicados</small>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="consolidarItens" checked>
                            Consolidar itens iguais
                        </label>
                        <small class="form-text">Soma quantidades de itens com mesmo código</small>
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('aglutinacaoModal')">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-success" onclick="confirmarAglutinacao()">
                        <i class="fas fa-compress-arrows-alt"></i> Confirmar Aglutinação
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            addDoc,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            query,
            where,
            orderBy,
            limit,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let cotacoes = [];
        let solicitacoes = [];
        let fornecedores = [];
        let produtos = [];

        // Variáveis de paginação
        let currentPage = 1;
        let itemsPerPage = 20;
        let filteredCotacoes = [];
        let totalItems = 0;

        // Verificar autenticação
        if (!currentUser) {
            window.location.href = 'login.html';
        }

        // Funções de notificação
        function showNotification(message, type = 'success', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                    notification.style.opacity = '1';
                }, 300);
            }, duration);
        }

        // Carregar dados iniciais
        window.onload = async function() {
            await loadInitialData();
            await loadQuotations();
            updateStats();
            loadApprovedRequests();
        };

        async function loadInitialData() {
            try {
                // Carregar todos os fornecedores primeiro
                const fornecedoresSnap = await getDocs(collection(db, "fornecedores"));
                const todosFornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar fornecedores ativos e homologados (mais flexível)
                fornecedores = todosFornecedores.filter(f => {
                    const isAtivo = f.status === 'ATIVO' || f.status === 'Ativo' || f.ativo === true;
                    const isHomologado = f.homologado === true || f.status === 'HOMOLOGADO';
                    return isAtivo && isHomologado;
                });

                // Se não encontrar fornecedores com filtros rigorosos, usar todos os ativos
                if (fornecedores.length === 0) {
                    fornecedores = todosFornecedores.filter(f =>
                        f.status === 'ATIVO' || f.status === 'Ativo' || f.ativo === true
                    );
                }

                // Se ainda não encontrar, usar todos
                if (fornecedores.length === 0) {
                    fornecedores = todosFornecedores;
                    console.warn('Nenhum fornecedor ativo encontrado, carregando todos os fornecedores');
                }

                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar solicitações aprovadas
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), where("status", "==", "APROVADA"))
                );
                solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', {
                    fornecedores: fornecedores.length,
                    produtos: produtos.length,
                    solicitacoes: solicitacoes.length
                });

                console.log('Fornecedores carregados:', fornecedores.map(f => ({
                    id: f.id,
                    nome: f.nome,
                    status: f.status,
                    homologado: f.homologado,
                    ativo: f.ativo
                })));

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados iniciais', 'error');
            }
        }

        async function loadQuotations() {
            try {
                const cotacoesSnap = await getDocs(
                    query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"))
                );
                cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                renderQuotations();
                loadQuotationSelects();
            } catch (error) {
                console.error('Erro ao carregar cotações:', error);
                showNotification('Erro ao carregar cotações', 'error');
            }
        }

        // Variáveis globais para aglutinação e filtros
        let showAglutinadas = false;
        let showFechadas = true; // Mostrar fechadas por padrão para permitir reabertura
        let selectedQuotations = new Set();

        function renderQuotations(cotacoesToRender = null) {
            const tbody = document.getElementById('quotationsTableBody');
            tbody.innerHTML = '';

            // Se não foram passadas cotações específicas, usar todas
            if (!cotacoesToRender) {
                filteredCotacoes = [...cotacoes];
            } else {
                filteredCotacoes = cotacoesToRender;
            }

            // Aplicar filtros
            // Filtrar cotações aglutinadas se necessário
            if (!showAglutinadas) {
                filteredCotacoes = filteredCotacoes.filter(c => !c.aglutinada || c.aglutinada.tipo === 'principal');
            }

            // Filtrar cotações fechadas se necessário
            if (!showFechadas) {
                filteredCotacoes = filteredCotacoes.filter(c => c.status !== 'FECHADA');
            }

            totalItems = filteredCotacoes.length;

            // Calcular paginação
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const cotacoesPage = filteredCotacoes.slice(startIndex, endIndex);

            cotacoesPage.forEach(quotation => {
                const row = document.createElement('tr');
                row.className = 'quotation-row';
                row.setAttribute('data-quotation-id', quotation.id);

                // Aplicar classes de aglutinação
                if (quotation.aglutinada) {
                    if (quotation.aglutinada.tipo === 'principal') {
                        row.classList.add('aglutinada');
                    } else if (quotation.aglutinada.tipo === 'filha') {
                        row.classList.add('aglutinada-filha');
                        if (!showAglutinadas) {
                            row.classList.add('hidden');
                        }
                    }
                }

                // Aplicar classe para cotações fechadas
                if (quotation.status === 'FECHADA') {
                    row.classList.add('fechada');
                }

                const statusClass = getStatusClass(quotation.status);
                const solicitacao = solicitacoes.find(s => s.id === quotation.solicitacaoId);
                const fornecedoresCount = quotation.fornecedores ? quotation.fornecedores.length : 0;
                const respostasCount = quotation.respostas ? Object.keys(quotation.respostas).length : 0;
                const totalItens = quotation.itens ? quotation.itens.length : (solicitacao?.itens?.length || 0);
                const valorTotal = quotation.itens ?
                    quotation.itens.reduce((sum, item) => sum + (item.quantidade * (item.valorUnitario || 0)), 0) :
                    (quotation.valorEstimado || 0);

                // Informações de aglutinação e status
                let statusInfo = '';

                // Badge para cotações fechadas
                if (quotation.status === 'FECHADA') {
                    statusInfo += `<span class="fechada-badge">FECHADA</span>`;
                }

                // Informações de aglutinação
                if (quotation.aglutinada) {
                    if (quotation.aglutinada.tipo === 'principal') {
                        const filhas = cotacoes.filter(c => c.aglutinada && c.aglutinada.principalId === quotation.id);
                        statusInfo += `
                            <span class="aglutinacao-badge">PRINCIPAL</span>
                            <div class="aglutinacao-info">
                                <button class="expand-collapse-btn" onclick="toggleAglutinacaoFilhas('${quotation.id}')">
                                    <i class="fas fa-chevron-down" id="expand-icon-${quotation.id}"></i> ${filhas.length} cotações aglutinadas
                                </button>
                            </div>
                        `;
                    } else if (quotation.aglutinada.tipo === 'filha') {
                        statusInfo += `<span class="aglutinacao-badge filha">AGLUTINADA</span>`;
                    }
                }

                row.innerHTML = `
                    <td>
                        <input type="checkbox"
                               class="quotation-checkbox"
                               value="${quotation.id}"
                               onchange="toggleQuotationSelection('${quotation.id}')"
                               ${selectedQuotations.has(quotation.id) ? 'checked' : ''}>
                    </td>
                    <td>
                        <strong>${quotation.numero || 'N/A'}</strong>
                        ${statusInfo}
                    </td>
                    <td>
                        ${formatDate(quotation.dataCriacao)}
                        ${quotation.dataLimite ? `<br><small style="color: #dc3545;"><i class="fas fa-clock"></i> Limite: ${formatDate(quotation.dataLimite)}</small>` : ''}
                    </td>
                    <td>
                        ${solicitacao ? `SC-${solicitacao.numero || 'N/A'}` : 'N/A'}
                        <span class="integration-badge">Auto</span>
                        <br><small style="color: #6c757d;">${totalItens} itens</small>
                    </td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span>${fornecedoresCount} fornecedores</span>
                            <div class="response-indicators">
                                ${Array.from({length: fornecedoresCount}, (_, i) =>
                                    `<div class="response-dot ${i < respostasCount ? 'responded' : 'pending'}"></div>`
                                ).join('')}
                            </div>
                        </div>
                        <br><small style="color: #6c757d;">Homologados e Ativos</small>
                    </td>
                    <td>${respostasCount}/${fornecedoresCount}</td>
                    <td>R$ ${formatCurrency(valorTotal)}</td>
                    <td><span class="status ${statusClass}">${getStatusText(quotation.status)}</span></td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-info btn-sm" onclick="viewQuotation('${quotation.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editQuotation('${quotation.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="sendQuotation('${quotation.id}')">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                            ${quotation.respostas && Object.keys(quotation.respostas).length > 0 ? `
                                <button class="btn btn-success btn-sm" onclick="compareQuotation('${quotation.id}')" title="Comparar respostas">
                                    <i class="fas fa-balance-scale"></i>
                                </button>
                            ` : ''}
                            ${quotation.status === 'FECHADA' ? `
                                <button class="btn btn-warning btn-sm" onclick="reopenQuotation('${quotation.id}')" title="Reabrir cotação">
                                    <i class="fas fa-undo"></i>
                                </button>
                            ` : ''}
                            ${quotation.aglutinada && quotation.aglutinada.tipo === 'principal' ? `
                                <button class="btn btn-warning btn-sm" onclick="dividirCotacao('${quotation.id}')" title="Dividir aglutinação">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });

            updatePaginationInfo();
            updatePaginationControls();
            updateSelectedQuotationsCounter();
        }

        function updateStats() {
            const abertas = cotacoes.filter(c => c.status === 'ABERTA').length;
            const enviadas = cotacoes.filter(c => c.status === 'ENVIADA').length;
            const respondidas = cotacoes.filter(c => c.status === 'RESPONDIDA').length;
            const aprovadas = cotacoes.filter(c => c.status === 'APROVADA').length;

            document.getElementById('statAbertas').textContent = abertas;
            document.getElementById('statEnviadas').textContent = enviadas;
            document.getElementById('statRespondidas').textContent = respondidas;
            document.getElementById('statAprovadas').textContent = aprovadas;
        }

        function loadApprovedRequests() {
            const select = document.getElementById('solicitacaoOrigem');
            select.innerHTML = '<option value="">Selecione uma solicitação aprovada...</option>';

            solicitacoes.forEach(request => {
                const option = document.createElement('option');
                option.value = request.id;
                option.textContent = `SC-${request.numero || 'N/A'} - ${request.justificativa?.substring(0, 50) || 'Sem descrição'}...`;
                select.appendChild(option);
            });
        }

        function loadQuotationSelects() {
            const select = document.getElementById('quotationCompareSelect');
            select.innerHTML = '<option value="">Selecione...</option>';

            cotacoes.filter(c => c.respostas && Object.keys(c.respostas).length > 0).forEach(quotation => {
                const option = document.createElement('option');
                option.value = quotation.id;
                option.textContent = `Cotação ${quotation.numero || 'N/A'} - ${Object.keys(quotation.respostas).length} respostas`;
                select.appendChild(option);
            });
        }

        // Funções auxiliares
        function getStatusClass(status) {
            switch (status) {
                case 'ABERTA': return 'status-aberta';
                case 'ENVIADA': return 'status-enviada';
                case 'RESPONDIDA': return 'status-respondida';
                case 'APROVADA': return 'status-aprovada';
                case 'FECHADA': return 'status-fechada';
                default: return 'status-aberta';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'ABERTA': return 'Aberta';
                case 'ENVIADA': return 'Enviada';
                case 'RESPONDIDA': return 'Respondida';
                case 'APROVADA': return 'Aprovada';
                case 'FECHADA': return 'Fechada';
                default: return 'Aberta';
            }
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString('pt-BR');
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        // Funções de modal e ações
        window.openNewQuotationModal = function() {
            document.getElementById('newQuotationModal').style.display = 'block';
            loadSuppliers();
        };

        window.closeModal = function(modalId) {
            document.getElementById(modalId).style.display = 'none';
        };

        window.showTab = function(tabName) {
            // Remover classe active de todas as abas
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Adicionar classe active na aba selecionada
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        };

        window.importFromRequests = function() {
            showNotification('Importando solicitações aprovadas...', 'info');
            loadApprovedRequests();
            openNewQuotationModal();
        };

        window.loadRequestItems = function() {
            const solicitacaoId = document.getElementById('solicitacaoOrigem').value;
            if (!solicitacaoId) {
                document.getElementById('requestItemsContainer').innerHTML =
                    '<p style="color: #6c757d; text-align: center; padding: 20px;">Selecione uma solicitação para carregar os itens automaticamente.</p>';
                return;
            }

            const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
            if (!solicitacao || !solicitacao.itens) {
                document.getElementById('requestItemsContainer').innerHTML =
                    '<p style="color: #dc3545; text-align: center; padding: 20px;">Erro: Solicitação não encontrada ou sem itens.</p>';
                return;
            }

            let html = '<div class="alert alert-success">';
            html += '<i class="fas fa-check-circle"></i> ';
            html += `<strong>${solicitacao.itens.length} itens</strong> carregados da solicitação SC-${solicitacao.numero}`;
            html += '</div>';

            html += '<div class="table-container"><table class="table"><thead><tr>';
            html += '<th>Código</th><th>Descrição</th><th>Quantidade</th><th>Unidade</th><th>Valor Unit. Estimado</th><th>Total Estimado</th><th>Observações</th>';
            html += '</tr></thead><tbody>';

            let valorTotalEstimado = 0;
            solicitacao.itens.forEach((item, index) => {
                const produto = produtos.find(p => p.codigo === item.codigo);
                const valorUnitario = item.valorUnitario || produto?.valorUnitario || 0;
                const totalItem = (item.quantidade || 0) * valorUnitario;
                valorTotalEstimado += totalItem;

                html += `<tr>
                    <td><strong>${item.codigo || 'N/A'}</strong></td>
                    <td>${item.descricao || produto?.descricao || 'N/A'}</td>
                    <td><strong>${item.quantidade || 0}</strong></td>
                    <td>${item.unidade || produto?.unidade || 'UN'}</td>
                    <td>R$ ${formatCurrency(valorUnitario)}</td>
                    <td><strong>R$ ${formatCurrency(totalItem)}</strong></td>
                    <td>${item.observacoes || '-'}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            html += `<div style="text-align: right; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">`;
            html += `<strong style="font-size: 18px;">Valor Total Estimado: R$ ${formatCurrency(valorTotalEstimado)}</strong>`;
            html += `</div>`;

            document.getElementById('requestItemsContainer').innerHTML = html;

            // Calcular data limite baseada no prazo
            const prazo = parseInt(document.getElementById('prazoResposta').value) || 7;
            const dataLimite = new Date();
            dataLimite.setDate(dataLimite.getDate() + prazo);
            document.getElementById('dataLimite').value = dataLimite.toISOString().split('T')[0];

            // Preencher informações adicionais
            document.getElementById('observacoesCotacao').value =
                `Cotação baseada na solicitação SC-${solicitacao.numero}\n` +
                `Departamento: ${solicitacao.departamento || 'N/A'}\n` +
                `Prioridade: ${solicitacao.prioridade || 'MEDIA'}\n` +
                `Justificativa: ${solicitacao.justificativa || 'N/A'}`;
        };

        function loadSuppliers() {
            const container = document.getElementById('suppliersGrid');
            container.innerHTML = '';

            if (fornecedores.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning" style="grid-column: 1 / -1;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> Nenhum fornecedor homologado e ativo encontrado.
                        Verifique o cadastro de fornecedores.
                    </div>
                `;
                return;
            }

            // Armazenar fornecedores originais para pesquisa
            window.originalSuppliers = [...fornecedores];

            renderSuppliers(fornecedores);
        }

        function renderSuppliers(suppliersToRender) {
            const container = document.getElementById('suppliersGrid');
            container.innerHTML = '';

            if (suppliersToRender.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning" style="grid-column: 1 / -1;">
                        <i class="fas fa-search"></i>
                        <strong>Nenhum fornecedor encontrado</strong> com os critérios de pesquisa.
                        <br><br>
                        <button type="button" class="btn btn-primary btn-sm" onclick="clearSupplierSearch()">
                            <i class="fas fa-times"></i> Limpar Pesquisa
                        </button>
                    </div>
                `;
                return;
            }

            // Adicionar header informativo com controles
            const headerInfo = document.createElement('div');
            headerInfo.style.gridColumn = '1 / -1';
            headerInfo.innerHTML = `
                <div class="alert alert-info">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <i class="fas fa-info-circle"></i>
                            <strong>${suppliersToRender.length} fornecedores</strong> homologados e ativos disponíveis.
                            <br><strong>0 fornecedores selecionados</strong> para esta cotação.
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button type="button" class="btn btn-success btn-sm" onclick="selectAllSuppliers()">
                                <i class="fas fa-check-double"></i> Selecionar Todos
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="deselectAllSuppliers()">
                                <i class="fas fa-times"></i> Limpar Seleção
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(headerInfo);

            suppliersToRender.forEach((supplier, index) => {
                const card = document.createElement('div');
                card.className = 'supplier-card';
                card.setAttribute('data-supplier-id', supplier.id);
                card.setAttribute('data-supplier-name', (supplier.nome || '').toLowerCase());
                card.setAttribute('data-supplier-razao', (supplier.razaoSocial || supplier.razao_social || '').toLowerCase());
                card.setAttribute('data-supplier-codigo', (supplier.codigo || '').toLowerCase());

                const checkboxId = `supplier_${supplier.id}_${index}`;

                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;" onclick="toggleSupplierByCard('${checkboxId}')">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div>
                                    <h5 style="margin: 0; color: #2c3e50;">${supplier.nome || 'Nome não informado'}</h5>
                                    ${supplier.codigo ? `<small style="color: #6c757d; font-weight: bold;">Código: ${supplier.codigo}</small>` : ''}
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                        <i class="fas fa-check"></i> HOMOLOGADO
                                    </span>
                                    <span style="background: #cce5ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                        <i class="fas fa-circle"></i> ATIVO
                                    </span>
                                </div>
                            </div>
                            ${supplier.razaoSocial || supplier.razao_social ? `
                                <p style="margin: 5px 0; color: #495057; font-size: 14px; font-weight: 500;">
                                    <i class="fas fa-building"></i> <strong>Razão Social:</strong> ${supplier.razaoSocial || supplier.razao_social}
                                </p>
                            ` : ''}
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-envelope"></i> ${supplier.email || 'Email não informado'}
                            </p>
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-phone"></i> ${supplier.telefone || 'Telefone não informado'}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                <i class="fas fa-map-marker-alt"></i> ${supplier.cidade || 'Cidade não informada'} - ${supplier.estado || 'UF'}
                            </p>
                        </div>
                        <div style="margin-left: 15px;">
                            <input type="checkbox"
                                   id="${checkboxId}"
                                   value="${supplier.id}"
                                   style="transform: scale(1.5); cursor: pointer;"
                                   onchange="toggleSupplierCard(this)"
                                   onclick="event.stopPropagation()">
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            // Inicializar contador
            updateSupplierCounter();
        }

        window.toggleSupplierCard = function(checkbox) {
            const card = checkbox.closest('.supplier-card');
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }

            // Atualizar contador de fornecedores selecionados
            updateSelectedSuppliersCount();
            updateSupplierCounter();
        };

        window.toggleSupplierByCard = function(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                toggleSupplierCard(checkbox);
            }
        };

        function updateSelectedSuppliersCount() {
            const selectedCount = document.querySelectorAll('#suppliersGrid input[type="checkbox"]:checked').length;
            const totalCount = document.querySelectorAll('#suppliersGrid input[type="checkbox"]').length;

            // Atualizar header se existir
            const headerInfo = document.querySelector('#suppliersGrid .alert-info');
            if (headerInfo) {
                headerInfo.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <i class="fas fa-info-circle"></i>
                            <strong>${totalCount} fornecedores</strong> homologados e ativos disponíveis.
                            <br><strong>${selectedCount} fornecedores selecionados</strong> para esta cotação.
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button type="button" class="btn btn-success btn-sm" onclick="selectAllSuppliers()">
                                <i class="fas fa-check-double"></i> Selecionar Todos
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="deselectAllSuppliers()">
                                <i class="fas fa-times"></i> Limpar Seleção
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        window.selectAllSuppliers = function() {
            const checkboxes = document.querySelectorAll('#suppliersGrid input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    toggleSupplierCard(checkbox);
                }
            });
        };

        window.deselectAllSuppliers = function() {
            const checkboxes = document.querySelectorAll('#suppliersGrid input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    toggleSupplierCard(checkbox);
                }
            });
        };

        function updateSupplierCounter() {
            const selectedCount = document.querySelectorAll('#suppliersGrid input[type="checkbox"]:checked').length;
            const counter = document.getElementById('supplierCounter');

            if (counter) {
                counter.innerHTML = `<i class="fas fa-users"></i> ${selectedCount} selecionados`;

                // Mudar cor baseado na quantidade
                if (selectedCount === 0) {
                    counter.style.background = '#f8d7da';
                    counter.style.color = '#721c24';
                } else if (selectedCount <= 3) {
                    counter.style.background = '#fff3cd';
                    counter.style.color = '#856404';
                } else {
                    counter.style.background = '#d4edda';
                    counter.style.color = '#155724';
                }
            }
        }

        window.viewQuotation = function(id) {
            const quotation = cotacoes.find(c => c.id === id);
            if (!quotation) return;

            const solicitacao = solicitacoes.find(s => s.id === quotation.solicitacaoId);
            const modalBody = document.getElementById('quotationDetailsBody');

            // Calcular valores
            const itensParaCotacao = quotation.itens || solicitacao?.itens || [];
            const valorTotalEstimado = itensParaCotacao.reduce((sum, item) => {
                const produto = produtos.find(p => p.codigo === item.codigo);
                const valorUnitario = item.valorUnitario || produto?.valorUnitario || 0;
                return sum + ((item.quantidade || 0) * valorUnitario);
            }, 0);

            modalBody.innerHTML = `
                <div class="form-row">
                    <div class="form-group">
                        <label>Número:</label>
                        <div><strong>${quotation.numero || 'N/A'}</strong></div>
                    </div>
                    <div class="form-group">
                        <label>Data Criação:</label>
                        <div>${formatDate(quotation.dataCriacao)}</div>
                    </div>
                    <div class="form-group">
                        <label>Data Limite:</label>
                        <div><strong>${quotation.dataLimite ? formatDate(quotation.dataLimite) : 'Não definida'}</strong></div>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <div><span class="status ${getStatusClass(quotation.status)}">${getStatusText(quotation.status)}</span></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Solicitação Origem:</label>
                        <div>
                            ${solicitacao ? `<strong>SC-${solicitacao.numero}</strong>` : 'N/A'}
                            <span class="integration-badge">Integrada</span>
                            ${solicitacao ? `<br><small style="color: #6c757d;">Departamento: ${solicitacao.departamento || 'N/A'} | Prioridade: ${solicitacao.prioridade || 'MEDIA'}</small>` : ''}
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Valor Total Estimado:</label>
                        <div><strong style="font-size: 18px; color: #27ae60;">R$ ${formatCurrency(valorTotalEstimado)}</strong></div>
                    </div>
                    <div class="form-group">
                        <label>Prazo Resposta:</label>
                        <div>${quotation.prazoResposta || 7} dias</div>
                    </div>
                </div>

                ${quotation.observacoes ? `
                    <div class="form-group">
                        <label>Observações:</label>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                            ${quotation.observacoes.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                ` : ''}

                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4><i class="fas fa-truck"></i> Fornecedores Selecionados (${quotation.fornecedores ? quotation.fornecedores.length : 0})</h4>
                    <button class="btn btn-info btn-sm" onclick="manageSuppliersForQuotation('${quotation.id}')">
                        <i class="fas fa-cog"></i> Gerenciar Fornecedores
                    </button>
                </div>
                <div class="supplier-grid">
                    ${quotation.fornecedores ? quotation.fornecedores.map(fId => {
                        const fornecedor = fornecedores.find(f => f.id === fId);
                        const respondeu = quotation.respostas && quotation.respostas[fId];
                        return `
                            <div class="supplier-card ${respondeu ? 'selected' : ''}">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                    <h5 style="margin: 0;">${fornecedor ? fornecedor.nome : 'Fornecedor não encontrado'}</h5>
                                    <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                        <i class="fas fa-check"></i> HOMOLOGADO
                                    </span>
                                </div>
                                <p style="color: #6c757d; font-size: 14px; margin: 5px 0;">
                                    <i class="fas fa-envelope"></i> ${fornecedor ? fornecedor.email : 'Email não disponível'}
                                </p>
                                <p style="color: #6c757d; font-size: 14px; margin: 5px 0;">
                                    <i class="fas fa-phone"></i> ${fornecedor ? fornecedor.telefone : 'Telefone não disponível'}
                                </p>
                                <div style="margin-top: 10px;">
                                    ${respondeu ?
                                        '<span style="color: #27ae60; font-weight: bold;"><i class="fas fa-check-circle"></i> Respondeu</span>' :
                                        '<span style="color: #ffc107; font-weight: bold;"><i class="fas fa-clock"></i> Aguardando Resposta</span>'
                                    }
                                </div>
                            </div>
                        `;
                    }).join('') : '<p style="text-align: center; color: #6c757d; padding: 20px;">Nenhum fornecedor selecionado</p>'}
                </div>

                <h4><i class="fas fa-list"></i> Itens da Cotação (${itensParaCotacao.length})</h4>
                ${itensParaCotacao.length > 0 ? `
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Unidade</th>
                                    <th>Valor Unit. Estimado</th>
                                    <th>Total Estimado</th>
                                    <th>Observações</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itensParaCotacao.map(item => {
                                    const produto = produtos.find(p => p.codigo === item.codigo);
                                    const valorUnitario = item.valorUnitario || produto?.valorUnitario || 0;
                                    const totalItem = (item.quantidade || 0) * valorUnitario;
                                    return `
                                        <tr>
                                            <td><strong>${item.codigo || 'N/A'}</strong></td>
                                            <td>${item.descricao || produto?.descricao || 'N/A'}</td>
                                            <td><strong>${item.quantidade || 0}</strong></td>
                                            <td>${item.unidade || produto?.unidade || 'UN'}</td>
                                            <td>R$ ${formatCurrency(valorUnitario)}</td>
                                            <td><strong>R$ ${formatCurrency(totalItem)}</strong></td>
                                            <td>${item.observacoes || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                            <tfoot>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td colspan="5" style="text-align: right;">TOTAL ESTIMADO:</td>
                                    <td><strong style="font-size: 16px; color: #27ae60;">R$ ${formatCurrency(valorTotalEstimado)}</strong></td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                ` : '<p style="text-align: center; color: #6c757d; padding: 20px;">Nenhum item encontrado</p>'}

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                    <button class="btn btn-warning" onclick="editQuotation('${quotation.id}')" title="Editar todos os dados da cotação">
                        <i class="fas fa-edit"></i> Editar Completo
                    </button>
                    <button class="btn btn-info" onclick="manageSuppliersForQuotation('${quotation.id}')" title="Gerenciar apenas fornecedores">
                        <i class="fas fa-truck"></i> Gerenciar Fornecedores
                    </button>
                    <button class="btn btn-primary" onclick="sendQuotation('${quotation.id}')" title="Enviar cotação aos fornecedores">
                        <i class="fas fa-paper-plane"></i> Enviar aos Fornecedores
                    </button>
                    ${quotation.respostas && Object.keys(quotation.respostas).length > 0 ? `
                        <button class="btn btn-success" onclick="compareQuotation('${quotation.id}')" title="Comparar respostas recebidas">
                            <i class="fas fa-balance-scale"></i> Comparar Respostas
                        </button>
                    ` : ''}
                    ${quotation.status === 'FECHADA' ? `
                        <button class="btn btn-warning" onclick="reopenQuotation('${quotation.id}')" title="Reabrir cotação para nova comparação">
                            <i class="fas fa-undo"></i> Reabrir Cotação
                        </button>
                    ` : ''}
                    <button class="btn btn-secondary" onclick="closeModal('quotationDetailsModal')">
                        <i class="fas fa-times"></i> Fechar
                    </button>
                </div>
            `;

            document.getElementById('quotationDetailsModal').style.display = 'block';
        };

        window.editQuotation = async function(id) {
            const quotation = cotacoes.find(c => c.id === id);
            if (!quotation) {
                showNotification('Cotação não encontrada!', 'error');
                return;
            }

            // Fechar outros modais
            closeModal('quotationDetailsModal');
            closeModal('newQuotationModal');

            // Armazenar ID da cotação sendo editada
            window.currentEditingQuotationId = id;

            // Preencher dados gerais
            loadEditQuotationData(quotation);

            // Inicializar itens para edição
            window.currentEditingItems = [...(quotation.itens || [])];

            // Carregar dados das abas
            loadEditItems(quotation);
            await loadEditSuppliers(quotation);
            loadEditObservations(quotation);

            // Abrir modal de edição
            document.getElementById('editQuotationModal').style.display = 'block';

            // Ativar primeira aba
            showEditTab('dadosGerais', null);
        };

        function loadEditQuotationData(quotation) {
            // Dados gerais
            document.getElementById('editNumero').value = quotation.numero || 'N/A';
            document.getElementById('editStatus').value = quotation.status || 'ABERTA';
            document.getElementById('editDataCriacao').value = formatDate(quotation.dataCriacao);
            document.getElementById('editSolicitacaoOrigem').value = quotation.solicitacaoId || '';
            document.getElementById('editPrazoResposta').value = quotation.prazoResposta || 7;
            document.getElementById('editDataLimite').value = quotation.dataLimite ?
                new Date(quotation.dataLimite.toDate()).toISOString().split('T')[0] : '';

            // Calcular valor total
            const valorTotal = quotation.valorEstimado || calcularValorEstimado(quotation.itens);
            document.getElementById('editValorTotal').value = `R$ ${formatCurrency(valorTotal)}`;

            document.getElementById('editCriadoPor').value = quotation.criadoPor || 'Sistema';
            document.getElementById('editUltimaAtualizacao').value = formatDate(quotation.ultimaAtualizacao || quotation.dataCriacao);

            // Carregar solicitações no select
            loadApprovedRequestsForEdit();
        }

        function loadApprovedRequestsForEdit() {
            const select = document.getElementById('editSolicitacaoOrigem');
            select.innerHTML = '<option value="">Selecione uma solicitação...</option>';

            solicitacoes.forEach(request => {
                const option = document.createElement('option');
                option.value = request.id;
                option.textContent = `SC-${request.numero || 'N/A'} - ${request.justificativa?.substring(0, 50) || 'Sem descrição'}...`;
                select.appendChild(option);
            });
        }

        function loadEditItems(quotation) {
            const container = document.getElementById('editItemsContainer');
            const itens = quotation.itens || [];

            if (itens.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Nenhum item encontrado. Use os botões acima para adicionar itens.
                    </div>
                `;
                return;
            }

            let html = '';
            let valorTotal = 0;

            itens.forEach((item, index) => {
                const produto = produtos.find(p => p.codigo === item.codigo);
                const valorUnitario = item.valorUnitario || produto?.valorUnitario || 0;
                const totalItem = (item.quantidade || 0) * valorUnitario;
                valorTotal += totalItem;

                html += `
                    <div class="item-edit-row" data-item-index="${index}">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="item-number">${index + 1}</div>
                            <div style="flex: 1;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Código</label>
                                        <input type="text" class="form-control" value="${item.codigo || ''}"
                                               onchange="updateItemData(${index}, 'codigo', this.value)">
                                    </div>
                                    <div class="form-group">
                                        <label>Descrição</label>
                                        <input type="text" class="form-control" value="${item.descricao || produto?.descricao || ''}"
                                               onchange="updateItemData(${index}, 'descricao', this.value)">
                                    </div>
                                    <div class="form-group">
                                        <label>Quantidade</label>
                                        <input type="number" class="form-control" value="${item.quantidade || 0}" min="0" step="0.01"
                                               onchange="updateItemData(${index}, 'quantidade', this.value)">
                                    </div>
                                    <div class="form-group">
                                        <label>Unidade</label>
                                        <input type="text" class="form-control" value="${item.unidade || produto?.unidade || 'UN'}"
                                               onchange="updateItemData(${index}, 'unidade', this.value)">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Valor Unitário Estimado</label>
                                        <input type="number" class="form-control" value="${valorUnitario}" min="0" step="0.01"
                                               onchange="updateItemData(${index}, 'valorUnitario', this.value)">
                                    </div>
                                    <div class="form-group">
                                        <label>Total</label>
                                        <input type="text" class="form-control" value="R$ ${formatCurrency(totalItem)}" readonly style="background: #f8f9fa; font-weight: bold;">
                                    </div>
                                    <div class="form-group">
                                        <label>Observações</label>
                                        <input type="text" class="form-control" value="${item.observacoes || ''}"
                                               onchange="updateItemData(${index}, 'observacoes', this.value)">
                                    </div>
                                    <div class="form-group" style="display: flex; align-items: end;">
                                        <div class="item-actions">
                                            <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${index})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button type="button" class="btn btn-info btn-sm" onclick="duplicateItem(${index})">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Adicionar resumo
            html += `
                <div class="summary-card">
                    <h5><i class="fas fa-calculator"></i> Resumo Financeiro</h5>
                    <div class="summary-row">
                        <span>Total de Itens:</span>
                        <span><strong>${itens.length}</strong></span>
                    </div>
                    <div class="summary-row">
                        <span>Valor Total Estimado:</span>
                        <span><strong>R$ ${formatCurrency(valorTotal)}</strong></span>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        async function loadEditSuppliers(quotation) {
            const container = document.getElementById('editSuppliersGrid');
            container.innerHTML = '';

            // Se não há fornecedores carregados, tentar carregar novamente
            if (fornecedores.length === 0) {
                console.log('Nenhum fornecedor carregado, tentando recarregar...');
                try {
                    await loadInitialData();
                } catch (error) {
                    console.error('Erro ao recarregar fornecedores:', error);
                }
            }

            if (fornecedores.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning" style="grid-column: 1 / -1;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Nenhum fornecedor encontrado!</strong><br>
                        Verifique se existem fornecedores cadastrados em <a href="cadastro_fornecedores.html" target="_blank">Cadastro de Fornecedores</a>.
                        <br><br>
                        <button type="button" class="btn btn-primary btn-sm" onclick="reloadSuppliers()">
                            <i class="fas fa-sync"></i> Recarregar Fornecedores
                        </button>
                    </div>
                `;
                return;
            }

            console.log(`Carregando ${fornecedores.length} fornecedores no modal de edição`);

            // Armazenar fornecedores originais para pesquisa
            window.editOriginalSuppliers = [...fornecedores];

            renderEditSuppliers(fornecedores, quotation);
        }

        function renderEditSuppliers(suppliersToRender, quotation) {
            const container = document.getElementById('editSuppliersGrid');
            container.innerHTML = '';

            if (suppliersToRender.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning" style="grid-column: 1 / -1;">
                        <i class="fas fa-search"></i>
                        <strong>Nenhum fornecedor encontrado</strong> com os critérios de pesquisa.
                        <br><br>
                        <button type="button" class="btn btn-primary btn-sm" onclick="clearEditSupplierSearch()">
                            <i class="fas fa-times"></i> Limpar Pesquisa
                        </button>
                    </div>
                `;
                return;
            }

            suppliersToRender.forEach((supplier, index) => {
                const isSelected = quotation.fornecedores && quotation.fornecedores.includes(supplier.id);
                const card = document.createElement('div');
                card.className = `supplier-card ${isSelected ? 'selected' : ''}`;
                card.setAttribute('data-supplier-id', supplier.id);
                card.setAttribute('data-supplier-name', (supplier.nome || '').toLowerCase());
                card.setAttribute('data-supplier-razao', (supplier.razaoSocial || supplier.razao_social || '').toLowerCase());
                card.setAttribute('data-supplier-codigo', (supplier.codigo || '').toLowerCase());

                const checkboxId = `edit_supplier_${supplier.id}_${index}`;

                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;" onclick="toggleEditSupplierByCard('${checkboxId}')">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <div>
                                    <h5 style="margin: 0; color: #2c3e50;">${supplier.nome || 'Nome não informado'}</h5>
                                    ${supplier.codigo ? `<small style="color: #6c757d; font-weight: bold;">Código: ${supplier.codigo}</small>` : ''}
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                        <i class="fas fa-check"></i> HOMOLOGADO
                                    </span>
                                    <span style="background: #cce5ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                        <i class="fas fa-circle"></i> ATIVO
                                    </span>
                                </div>
                            </div>
                            ${supplier.razaoSocial || supplier.razao_social ? `
                                <p style="margin: 5px 0; color: #495057; font-size: 14px; font-weight: 500;">
                                    <i class="fas fa-building"></i> <strong>Razão Social:</strong> ${supplier.razaoSocial || supplier.razao_social}
                                </p>
                            ` : ''}
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-envelope"></i> ${supplier.email || 'Email não informado'}
                            </p>
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-phone"></i> ${supplier.telefone || 'Telefone não informado'}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                <i class="fas fa-map-marker-alt"></i> ${supplier.cidade || 'Cidade não informada'} - ${supplier.estado || 'UF'}
                            </p>
                        </div>
                        <div style="margin-left: 15px;">
                            <input type="checkbox"
                                   id="${checkboxId}"
                                   value="${supplier.id}"
                                   ${isSelected ? 'checked' : ''}
                                   style="transform: scale(1.5); cursor: pointer;"
                                   onchange="toggleEditSupplierCard(this)"
                                   onclick="event.stopPropagation()">
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            updateEditSupplierCounter();
        }

        function loadEditObservations(quotation) {
            document.getElementById('editObservacoes').value = quotation.observacoes || '';
            document.getElementById('editCondicoes').value = quotation.condicoes || '';
            document.getElementById('editLocalEntrega').value = quotation.localEntrega || '';
            document.getElementById('editContatoResponsavel').value = quotation.contatoResponsavel || '';
        }

        // Funções de controle das abas de edição
        window.showEditTab = function(tabName, event) {
            // Remover classe active de todas as abas
            document.querySelectorAll('#editQuotationModal .tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#editQuotationModal .tab-content').forEach(content => content.classList.remove('active'));

            // Adicionar classe active na aba selecionada
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // Fallback: encontrar o botão pela aba
                const tabButton = document.querySelector(`[onclick*="showEditTab('${tabName}')"]`);
                if (tabButton) {
                    tabButton.classList.add('active');
                }
            }
            document.getElementById(tabName).classList.add('active');
        };

        // Funções para gerenciar fornecedores na edição
        window.toggleEditSupplierCard = function(checkbox) {
            const card = checkbox.closest('.supplier-card');
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
            updateEditSupplierCounter();
        };

        window.toggleEditSupplierByCard = function(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                toggleEditSupplierCard(checkbox);
            }
        };

        window.selectAllEditSuppliers = function() {
            const checkboxes = document.querySelectorAll('#editSuppliersGrid input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    toggleEditSupplierCard(checkbox);
                }
            });
        };

        window.deselectAllEditSuppliers = function() {
            const checkboxes = document.querySelectorAll('#editSuppliersGrid input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    toggleEditSupplierCard(checkbox);
                }
            });
        };

        function updateEditSupplierCounter() {
            const selectedCount = document.querySelectorAll('#editSuppliersGrid input[type="checkbox"]:checked').length;
            const counter = document.getElementById('editSupplierCounter');

            if (counter) {
                counter.innerHTML = `<i class="fas fa-users"></i> ${selectedCount} selecionados`;

                if (selectedCount === 0) {
                    counter.style.background = '#f8d7da';
                    counter.style.color = '#721c24';
                } else if (selectedCount <= 3) {
                    counter.style.background = '#fff3cd';
                    counter.style.color = '#856404';
                } else {
                    counter.style.background = '#d4edda';
                    counter.style.color = '#155724';
                }
            }
        }

        // Funções para gerenciar itens
        window.currentEditingItems = [];

        window.updateItemData = function(index, field, value) {
            if (!window.currentEditingItems) {
                const quotation = cotacoes.find(c => c.id === window.currentEditingQuotationId);
                window.currentEditingItems = [...(quotation.itens || [])];
            }

            if (window.currentEditingItems[index]) {
                window.currentEditingItems[index][field] = value;

                // Recalcular totais se mudou quantidade ou valor unitário
                if (field === 'quantidade' || field === 'valorUnitario') {
                    refreshItemTotals();
                }
            }
        };

        window.addNewItem = function() {
            if (!window.currentEditingItems) {
                window.currentEditingItems = [];
            }

            const newItem = {
                codigo: '',
                descricao: '',
                quantidade: 1,
                unidade: 'UN',
                valorUnitario: 0,
                observacoes: ''
            };

            window.currentEditingItems.push(newItem);
            refreshEditItems();
        };

        window.removeItem = function(index) {
            if (confirm('Tem certeza que deseja remover este item?')) {
                window.currentEditingItems.splice(index, 1);
                refreshEditItems();
            }
        };

        window.duplicateItem = function(index) {
            const item = window.currentEditingItems[index];
            const duplicatedItem = { ...item };
            duplicatedItem.codigo = item.codigo + '_COPIA';
            window.currentEditingItems.splice(index + 1, 0, duplicatedItem);
            refreshEditItems();
        };

        window.importItemsFromRequest = function() {
            const solicitacaoId = document.getElementById('editSolicitacaoOrigem').value;
            if (!solicitacaoId) {
                showNotification('Selecione uma solicitação primeiro!', 'warning');
                return;
            }

            const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
            if (!solicitacao || !solicitacao.itens) {
                showNotification('Solicitação não encontrada ou sem itens!', 'error');
                return;
            }

            if (confirm(`Importar ${solicitacao.itens.length} itens da solicitação? Isso substituirá os itens atuais.`)) {
                window.currentEditingItems = [...solicitacao.itens];
                refreshEditItems();
                showNotification(`${solicitacao.itens.length} itens importados com sucesso!`, 'success');
            }
        };

        window.loadEditRequestItems = function() {
            const solicitacaoId = document.getElementById('editSolicitacaoOrigem').value;
            if (!solicitacaoId) return;

            const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
            if (solicitacao && solicitacao.itens) {
                // Atualizar valor total estimado
                const valorTotal = calcularValorEstimado(solicitacao.itens);
                document.getElementById('editValorTotal').value = `R$ ${formatCurrency(valorTotal)}`;
            }
        };

        function refreshEditItems() {
            const quotation = {
                itens: window.currentEditingItems || []
            };
            loadEditItems(quotation);
        }

        function refreshItemTotals() {
            // Atualizar totais na interface
            const rows = document.querySelectorAll('.item-edit-row');
            let valorTotal = 0;

            rows.forEach((row, index) => {
                const item = window.currentEditingItems[index];
                if (item) {
                    const quantidade = parseFloat(item.quantidade) || 0;
                    const valorUnitario = parseFloat(item.valorUnitario) || 0;
                    const total = quantidade * valorUnitario;
                    valorTotal += total;

                    const totalInput = row.querySelector('input[readonly]');
                    if (totalInput) {
                        totalInput.value = `R$ ${formatCurrency(total)}`;
                    }
                }
            });

            // Atualizar valor total geral
            document.getElementById('editValorTotal').value = `R$ ${formatCurrency(valorTotal)}`;

            // Atualizar resumo se existir
            const summaryCard = document.querySelector('.summary-card');
            if (summaryCard) {
                const totalSpan = summaryCard.querySelector('.summary-row:last-child span:last-child strong');
                if (totalSpan) {
                    totalSpan.textContent = `R$ ${formatCurrency(valorTotal)}`;
                }
            }
        }

        // Funções de ações da cotação
        window.deleteQuotation = function() {
            if (!window.currentEditingQuotationId) return;

            const quotation = cotacoes.find(c => c.id === window.currentEditingQuotationId);
            if (!quotation) return;

            if (confirm(`Tem certeza que deseja EXCLUIR a cotação ${quotation.numero}?\n\nEsta ação não pode ser desfeita!`)) {
                deleteDoc(doc(db, "cotacoes", window.currentEditingQuotationId))
                    .then(() => {
                        showNotification('Cotação excluída com sucesso!', 'success');
                        closeModal('editQuotationModal');
                        loadQuotations();
                        updateStats();
                    })
                    .catch(error => {
                        console.error('Erro ao excluir cotação:', error);
                        showNotification('Erro ao excluir cotação: ' + error.message, 'error');
                    });
            }
        };

        window.duplicateQuotation = function() {
            if (!window.currentEditingQuotationId) return;

            const quotation = cotacoes.find(c => c.id === window.currentEditingQuotationId);
            if (!quotation) return;

            if (confirm(`Duplicar a cotação ${quotation.numero}?`)) {
                const newQuotationData = {
                    ...quotation,
                    numero: null, // Será gerado automaticamente
                    dataCriacao: Timestamp.now(),
                    status: 'ABERTA',
                    respostas: null,
                    criadoPor: currentUser.uid,
                    ultimaAtualizacao: Timestamp.now()
                };

                // Remover ID
                delete newQuotationData.id;

                generateQuotationNumber().then(numero => {
                    newQuotationData.numero = numero;

                    addDoc(collection(db, "cotacoes"), newQuotationData)
                        .then(() => {
                            showNotification(`Cotação duplicada como ${numero}!`, 'success');
                            closeModal('editQuotationModal');
                            loadQuotations();
                            updateStats();
                        })
                        .catch(error => {
                            console.error('Erro ao duplicar cotação:', error);
                            showNotification('Erro ao duplicar cotação: ' + error.message, 'error');
                        });
                });
            }
        };

        // Handler para salvar edições
        document.addEventListener('DOMContentLoaded', function() {
            const editForm = document.getElementById('editQuotationForm');
            if (editForm) {
                editForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    if (!window.currentEditingQuotationId) {
                        showNotification('Erro: ID da cotação não encontrado!', 'error');
                        return;
                    }

                    try {
                        // Capturar fornecedores selecionados
                        const selectedSuppliers = Array.from(document.querySelectorAll('#editSuppliersGrid input[type="checkbox"]:checked'))
                            .map(cb => cb.value);

                        if (selectedSuppliers.length === 0) {
                            showNotification('Selecione pelo menos um fornecedor!', 'warning');
                            showEditTab('fornecedores', null);
                            return;
                        }

                        // Preparar dados para atualização
                        const updateData = {
                            status: document.getElementById('editStatus').value,
                            solicitacaoId: document.getElementById('editSolicitacaoOrigem').value,
                            prazoResposta: parseInt(document.getElementById('editPrazoResposta').value) || 7,
                            dataLimite: document.getElementById('editDataLimite').value ?
                                Timestamp.fromDate(new Date(document.getElementById('editDataLimite').value)) : null,
                            fornecedores: selectedSuppliers,
                            itens: window.currentEditingItems || [],
                            valorEstimado: calcularValorEstimado(window.currentEditingItems || []),
                            observacoes: document.getElementById('editObservacoes').value || '',
                            condicoes: document.getElementById('editCondicoes').value || '',
                            localEntrega: document.getElementById('editLocalEntrega').value || '',
                            contatoResponsavel: document.getElementById('editContatoResponsavel').value || '',
                            ultimaAtualizacao: Timestamp.now(),
                            atualizadoPor: currentUser.uid
                        };

                        console.log('Updating quotation with data:', updateData);

                        // Atualizar no Firebase
                        await updateDoc(doc(db, "cotacoes", window.currentEditingQuotationId), updateData);

                        showNotification('Cotação atualizada com sucesso!', 'success');

                        // Recarregar dados
                        await loadQuotations();
                        updateStats();

                        // Fechar modal
                        closeModal('editQuotationModal');

                        // Limpar variáveis
                        window.currentEditingQuotationId = null;
                        window.currentEditingItems = null;

                    } catch (error) {
                        console.error('Erro ao salvar alterações:', error);
                        showNotification('Erro ao salvar alterações: ' + error.message, 'error');
                    }
                });
            }
        });

        window.saveQuotation = async function(formData, isEdit = false, quotationId = null) {
            try {
                console.log('Saving quotation with data:', formData); // Debug

                // Validar dados obrigatórios
                if (!formData.solicitacaoOrigem) {
                    showNotification('Selecione uma solicitação de origem!', 'warning');
                    return;
                }

                if (!formData.fornecedores || formData.fornecedores.length === 0) {
                    showNotification('Selecione pelo menos um fornecedor!', 'warning');
                    return;
                }

                // Buscar dados da solicitação para incluir itens
                const solicitacao = solicitacoes.find(s => s.id === formData.solicitacaoOrigem);

                const quotationData = {
                    solicitacaoId: formData.solicitacaoOrigem,
                    prazoResposta: parseInt(formData.prazoResposta) || 7,
                    dataLimite: formData.dataLimite ? Timestamp.fromDate(new Date(formData.dataLimite)) : null,
                    observacoes: formData.observacoesCotacao || '',
                    fornecedores: formData.fornecedores,
                    itens: solicitacao ? solicitacao.itens : [],
                    valorEstimado: solicitacao ? calcularValorEstimado(solicitacao.itens) : 0,
                    status: 'ABERTA',
                    ultimaAtualizacao: Timestamp.now(),
                    atualizadoPor: currentUser.uid
                };

                console.log('Final quotation data:', quotationData); // Debug

                if (isEdit && quotationId) {
                    // Atualizar cotação existente
                    await updateDoc(doc(db, "cotacoes", quotationId), quotationData);
                    showNotification('Cotação atualizada com sucesso!', 'success');
                } else {
                    // Criar nova cotação
                    quotationData.numero = await generateQuotationNumber();
                    quotationData.dataCriacao = Timestamp.now();
                    quotationData.criadoPor = currentUser.uid;

                    const docRef = await addDoc(collection(db, "cotacoes"), quotationData);
                    console.log('Cotação criada com ID:', docRef.id); // Debug
                    showNotification('Cotação criada com sucesso!', 'success');
                }

                // Recarregar dados
                await loadQuotations();
                updateStats();
                closeModal('newQuotationModal');

                // Resetar formulário
                resetQuotationForm();

            } catch (error) {
                console.error('Erro ao salvar cotação:', error);
                showNotification('Erro ao salvar cotação: ' + error.message, 'error');
            }
        };

        function calcularValorEstimado(itens) {
            if (!itens || !Array.isArray(itens)) return 0;

            return itens.reduce((total, item) => {
                const produto = produtos.find(p => p.codigo === item.codigo);
                const valorUnitario = item.valorUnitario || produto?.valorUnitario || 0;
                return total + ((item.quantidade || 0) * valorUnitario);
            }, 0);
        }

        function resetQuotationForm() {
            document.getElementById('newQuotationForm').reset();
            document.getElementById('requestItemsContainer').innerHTML =
                '<p style="color: #6c757d; text-align: center; padding: 20px;">Selecione uma solicitação para carregar os itens automaticamente.</p>';

            // Limpar seleção de fornecedores
            const checkboxes = document.querySelectorAll('#suppliersGrid input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                const card = checkbox.closest('.supplier-card');
                if (card) card.classList.remove('selected');
            });

            // Resetar título e botão
            document.querySelector('#newQuotationModal .modal-header h2').innerHTML =
                '<i class="fas fa-plus"></i> Nova Cotação';
            const submitBtn = document.querySelector('#newQuotationForm button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-save"></i> Criar Cotação';

            // Remover campo hidden se existir
            const hiddenId = document.querySelector('input[name="quotationId"]');
            if (hiddenId) hiddenId.remove();

            updateSelectedSuppliersCount();
        }

        async function generateQuotationNumber(isAglutinada = false) {
            const hoje = new Date();
            const ano = hoje.getFullYear().toString().slice(-2); // Últimos 2 dígitos do ano
            const mes = (hoje.getMonth() + 1).toString().padStart(2, '0'); // Mês com 2 dígitos
            const anoMes = ano + mes; // Formato AAMM

            // Buscar cotações do mês atual (incluindo aglutinadas)
            const cotacoesDoMes = cotacoes.filter(c => {
                const dataCriacao = c.dataCriacao.toDate ? c.dataCriacao.toDate() : new Date(c.dataCriacao);
                const anoMesDoc = dataCriacao.getFullYear().toString().slice(-2) +
                                 (dataCriacao.getMonth() + 1).toString().padStart(2, '0');
                return anoMesDoc === anoMes && c.numero &&
                       (c.numero.startsWith(`CT-${anoMes}-`) || c.numero.startsWith(`CTA-${anoMes}-`));
            });

            const proximoNumero = cotacoesDoMes.length + 1;

            // Se for aglutinada, usar prefixo CTA (Cotação Aglutinada)
            const prefixo = isAglutinada ? 'CTA' : 'CT';
            return `${prefixo}-${anoMes}-${proximoNumero.toString().padStart(4, '0')}`;
        }

        // Função específica para gerar número de cotação aglutinada
        async function generateAglutinatedQuotationNumber() {
            return await generateQuotationNumber(true);
        }

        // Interceptar submit do formulário
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('newQuotationForm');
            if (form) {
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    console.log('Form submitted'); // Debug

                    // Capturar fornecedores selecionados
                    const selectedSuppliers = Array.from(document.querySelectorAll('#suppliersGrid input[type="checkbox"]:checked'))
                        .map(cb => cb.value);

                    console.log('Selected suppliers:', selectedSuppliers); // Debug
                    console.log('Total checkboxes found:', document.querySelectorAll('#suppliersGrid input[type="checkbox"]').length); // Debug
                    console.log('Checked checkboxes found:', document.querySelectorAll('#suppliersGrid input[type="checkbox"]:checked').length); // Debug

                    if (selectedSuppliers.length === 0) {
                        showNotification('Selecione pelo menos um fornecedor!', 'warning');

                        // Destacar visualmente a seção de fornecedores
                        const suppliersSection = document.getElementById('suppliersGrid');
                        if (suppliersSection) {
                            suppliersSection.style.border = '2px solid #dc3545';
                            suppliersSection.style.borderRadius = '8px';
                            setTimeout(() => {
                                suppliersSection.style.border = '';
                                suppliersSection.style.borderRadius = '';
                            }, 3000);
                        }

                        // Scroll para a seção de fornecedores
                        const supplierTitle = document.querySelector('h4');
                        if (supplierTitle) {
                            supplierTitle.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }

                        return;
                    }

                    // Capturar dados do formulário
                    const quotationData = {
                        solicitacaoOrigem: document.getElementById('solicitacaoOrigem').value,
                        prazoResposta: document.getElementById('prazoResposta').value,
                        dataLimite: document.getElementById('dataLimite').value,
                        observacoesCotacao: document.getElementById('observacoesCotacao').value,
                        fornecedores: selectedSuppliers
                    };

                    console.log('Quotation data:', quotationData); // Debug

                    // Verificar se é edição
                    const hiddenId = document.querySelector('input[name="quotationId"]');
                    const quotationId = hiddenId ? hiddenId.value : null;
                    const isEdit = !!quotationId;

                    console.log('Is edit:', isEdit, 'ID:', quotationId); // Debug

                    await saveQuotation(quotationData, isEdit, quotationId);
                });
            }
        });

        // Funções de ações rápidas
        window.showAllQuotations = function() {
            loadQuotations();
        };

        window.showOpenQuotations = function() {
            document.getElementById('statusFilter').value = 'ABERTA';
            applyFilters();
        };

        window.showSentQuotations = function() {
            document.getElementById('statusFilter').value = 'ENVIADA';
            applyFilters();
        };

        window.showRespondedQuotations = function() {
            document.getElementById('statusFilter').value = 'RESPONDIDA';
            applyFilters();
        };

        window.showPendingApproval = function() {
            document.getElementById('statusFilter').value = 'RESPONDIDA';
            applyFilters();
        };

        window.applyFilters = function() {
            const statusFilter = document.getElementById('statusFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const supplierFilter = document.getElementById('supplierFilter').value;

            let filtered = [...cotacoes];

            // Filtro por status
            if (statusFilter) {
                filtered = filtered.filter(cotacao => cotacao.status === statusFilter);
            }

            // Filtro por data
            if (startDate) {
                const start = new Date(startDate);
                filtered = filtered.filter(cotacao => {
                    if (cotacao.dataCriacao && cotacao.dataCriacao.seconds) {
                        const cotacaoDate = new Date(cotacao.dataCriacao.seconds * 1000);
                        return cotacaoDate >= start;
                    }
                    return false;
                });
            }

            if (endDate) {
                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999); // Final do dia
                filtered = filtered.filter(cotacao => {
                    if (cotacao.dataCriacao && cotacao.dataCriacao.seconds) {
                        const cotacaoDate = new Date(cotacao.dataCriacao.seconds * 1000);
                        return cotacaoDate <= end;
                    }
                    return false;
                });
            }

            // Filtro por fornecedor
            if (supplierFilter) {
                filtered = filtered.filter(cotacao =>
                    cotacao.fornecedores && cotacao.fornecedores.some(f => f.id === supplierFilter)
                );
            }

            currentPage = 1; // Reset para primeira página
            renderQuotations(filtered);
            showNotification(`${filtered.length} cotações encontradas`, 'success');
        };

        window.clearFilters = function() {
            document.getElementById('dataInicio').value = '';
            document.getElementById('dataFim').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('solicitacaoFilter').value = '';
            document.getElementById('fornecedorFilter').value = '';

            currentPage = 1; // Reset para primeira página
            renderQuotations(); // Renderizar todas as cotações
            showNotification('Filtros limpos', 'success');
        };

        window.sendQuotation = function(id) {
            const quotation = cotacoes.find(c => c.id === id);
            if (!quotation) return;

            if (!quotation.fornecedores || quotation.fornecedores.length === 0) {
                showNotification('Nenhum fornecedor selecionado para esta cotação!', 'warning');
                return;
            }

            if (confirm(`Enviar cotação ${quotation.numero} para ${quotation.fornecedores.length} fornecedores?`)) {
                // Simular envio
                showNotification(`Cotação enviada para ${quotation.fornecedores.length} fornecedores!`, 'success');

                // Atualizar status
                updateDoc(doc(db, "cotacoes", id), {
                    status: 'ENVIADA',
                    dataEnvio: Timestamp.now()
                }).then(() => {
                    loadQuotations();
                    updateStats();
                });
            }
        };

        window.compareQuotation = async function(id) {
            const quotation = cotacoes.find(c => c.id === id);
            if (!quotation) {
                showNotification('Cotação não encontrada', 'error');
                return;
            }

            if (!quotation.respostas || Object.keys(quotation.respostas).length === 0) {
                showNotification('Nenhuma resposta recebida para esta cotação', 'warning');
                return;
            }

            // Abrir modal de comparação
            await showComparisonModal(quotation);
        };

        async function showComparisonModal(quotation) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'comparisonModal';
            modal.style.display = 'block';

            const respostas = quotation.respostas || {};
            const fornecedoresIds = Object.keys(respostas);

            // Buscar dados dos fornecedores
            const fornecedoresData = {};
            for (const fornecedorId of fornecedoresIds) {
                const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                if (fornecedor) {
                    fornecedoresData[fornecedorId] = fornecedor;
                }
            }

            let comparisonHtml = `
                <div class="modal-content" style="max-width: 95%; max-height: 90vh; overflow-y: auto;">
                    <span class="close-button" onclick="closeModal('comparisonModal')">&times;</span>
                    <h2><i class="fas fa-balance-scale"></i> Comparação de Cotação ${quotation.numero}</h2>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Selecione os melhores fornecedores para cada item e gere os pedidos de compra.
                    </div>

                    <div class="table-container">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Descrição</th>
                                    <th>Qtd</th>
                                    <th>Unidade</th>
            `;

            // Cabeçalhos dos fornecedores
            fornecedoresIds.forEach(fornecedorId => {
                const fornecedor = fornecedoresData[fornecedorId];
                comparisonHtml += `<th style="text-align: center; background: #f8f9fa;">
                    ${fornecedor ? fornecedor.razaoSocial : 'Fornecedor ' + fornecedorId}
                </th>`;
            });

            comparisonHtml += `
                                    <th>Melhor Opção</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            // Linhas dos itens
            quotation.itens.forEach((item, itemIndex) => {
                comparisonHtml += `
                    <tr>
                        <td><strong>${item.codigo}</strong></td>
                        <td>${item.descricao}</td>
                        <td>${item.quantidade}</td>
                        <td>${item.unidade}</td>
                `;

                let melhorPreco = Infinity;
                let melhorFornecedor = null;

                // Colunas dos fornecedores
                fornecedoresIds.forEach(fornecedorId => {
                    const resposta = respostas[fornecedorId];
                    const itemResposta = resposta?.itens?.find(i => i.codigo === item.codigo);

                    if (itemResposta && itemResposta.preco) {
                        const preco = parseFloat(itemResposta.preco);
                        const total = preco * item.quantidade;

                        if (preco < melhorPreco) {
                            melhorPreco = preco;
                            melhorFornecedor = fornecedorId;
                        }

                        comparisonHtml += `
                            <td style="text-align: center; ${preco === melhorPreco ? 'background: #d4edda; font-weight: bold;' : ''}">
                                <div>R$ ${preco.toFixed(2)}</div>
                                <div style="font-size: 12px; color: #666;">Total: R$ ${total.toFixed(2)}</div>
                                <div style="font-size: 11px; color: #666;">Prazo: ${itemResposta.prazoEntrega || 'N/I'} dias</div>
                            </td>
                        `;
                    } else {
                        comparisonHtml += `
                            <td style="text-align: center; color: #999;">
                                <div>Não cotado</div>
                            </td>
                        `;
                    }
                });

                // Coluna de seleção do melhor
                comparisonHtml += `
                    <td style="text-align: center;">
                        <select class="form-control form-control-sm" id="winner_${itemIndex}" onchange="updateWinnerSelection()">
                            <option value="">Selecione...</option>
                `;

                fornecedoresIds.forEach(fornecedorId => {
                    const fornecedor = fornecedoresData[fornecedorId];
                    const resposta = respostas[fornecedorId];
                    const itemResposta = resposta?.itens?.find(i => i.codigo === item.codigo);

                    if (itemResposta && itemResposta.preco) {
                        const selected = fornecedorId === melhorFornecedor ? 'selected' : '';
                        comparisonHtml += `
                            <option value="${fornecedorId}" ${selected}>
                                ${fornecedor ? fornecedor.razaoSocial : 'Fornecedor ' + fornecedorId} - R$ ${parseFloat(itemResposta.preco).toFixed(2)}
                            </option>
                        `;
                    }
                });

                comparisonHtml += `
                        </select>
                    </td>
                </tr>
                `;
            });

            comparisonHtml += `
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4>Resumo da Seleção</h4>
                        <div id="selectionSummary">Selecione os fornecedores para ver o resumo</div>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="closeModal('comparisonModal')">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button class="btn btn-success" onclick="generatePurchaseOrdersFromComparison('${quotation.id}')" id="generateOrdersBtn" disabled>
                            <i class="fas fa-shopping-cart"></i> Gerar Pedidos de Compra
                        </button>
                    </div>
                </div>
            `;

            modal.innerHTML = comparisonHtml;
            document.body.appendChild(modal);

            // Atualizar resumo inicial
            updateWinnerSelection();
        }

        window.updateWinnerSelection = function() {
            const selects = document.querySelectorAll('[id^="winner_"]');
            const selections = {};
            let hasSelection = false;

            selects.forEach((select, index) => {
                if (select.value) {
                    hasSelection = true;
                    if (!selections[select.value]) {
                        selections[select.value] = [];
                    }
                    selections[select.value].push(index);
                }
            });

            // Atualizar resumo
            const summaryDiv = document.getElementById('selectionSummary');
            if (hasSelection) {
                let summaryHtml = '<div class="row">';

                Object.keys(selections).forEach(fornecedorId => {
                    const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                    const itensCount = selections[fornecedorId].length;

                    summaryHtml += `
                        <div class="col-md-4">
                            <div class="card" style="margin-bottom: 10px;">
                                <div class="card-body" style="padding: 10px;">
                                    <h6 class="card-title">${fornecedor ? fornecedor.razaoSocial : 'Fornecedor ' + fornecedorId}</h6>
                                    <p class="card-text">${itensCount} item(ns) selecionado(s)</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                summaryHtml += '</div>';
                summaryDiv.innerHTML = summaryHtml;

                document.getElementById('generateOrdersBtn').disabled = false;
            } else {
                summaryDiv.innerHTML = 'Selecione os fornecedores para ver o resumo';
                document.getElementById('generateOrdersBtn').disabled = true;
            }
        };

        window.generatePurchaseOrdersFromComparison = async function(quotationId) {
            const quotation = cotacoes.find(c => c.id === quotationId);
            if (!quotation) {
                showNotification('Cotação não encontrada', 'error');
                return;
            }

            const selects = document.querySelectorAll('[id^="winner_"]');
            const selections = {};

            // Coletar seleções
            selects.forEach((select, index) => {
                if (select.value) {
                    if (!selections[select.value]) {
                        selections[select.value] = [];
                    }
                    selections[select.value].push({
                        index: index,
                        item: quotation.itens[index]
                    });
                }
            });

            if (Object.keys(selections).length === 0) {
                showNotification('Selecione pelo menos um fornecedor', 'warning');
                return;
            }

            if (!confirm(`Gerar ${Object.keys(selections).length} pedido(s) de compra?`)) {
                return;
            }

            try {
                const pedidosCriados = [];

                for (const [fornecedorId, itens] of Object.entries(selections)) {
                    const fornecedor = fornecedores.find(f => f.id === fornecedorId);
                    if (!fornecedor) continue;

                    const resposta = quotation.respostas[fornecedorId];
                    if (!resposta) continue;

                    // Gerar número do pedido
                    const numeroPedido = await generatePurchaseOrderNumber();

                    // Preparar itens do pedido
                    const itensPedido = itens.map(itemData => {
                        const item = itemData.item;
                        const itemResposta = resposta.itens.find(i => i.codigo === item.codigo);

                        return {
                            codigo: item.codigo,
                            descricao: item.descricao,
                            quantidade: item.quantidade,
                            unidade: item.unidade,
                            precoUnitario: parseFloat(itemResposta.preco),
                            precoTotal: parseFloat(itemResposta.preco) * item.quantidade,
                            prazoEntrega: itemResposta.prazoEntrega || 0,
                            observacoes: itemResposta.observacoes || ''
                        };
                    });

                    const valorTotal = itensPedido.reduce((sum, item) => sum + item.precoTotal, 0);

                    // Criar pedido de compra
                    const pedidoData = {
                        numero: numeroPedido,
                        fornecedorId: fornecedorId,
                        fornecedor: {
                            razaoSocial: fornecedor.razaoSocial,
                            cnpj: fornecedor.cnpj,
                            email: fornecedor.email,
                            telefone: fornecedor.telefone
                        },
                        cotacaoId: quotationId,
                        cotacaoNumero: quotation.numero,
                        itens: itensPedido,
                        valorTotal: valorTotal,
                        status: 'PENDENTE',
                        dataCriacao: Timestamp.now(),
                        dataVencimento: null,
                        observacoes: `Pedido gerado automaticamente da cotação ${quotation.numero}`,
                        condicoesPagamento: resposta.condicoesPagamento || '',
                        prazoEntrega: Math.max(...itensPedido.map(i => i.prazoEntrega)),
                        usuario: currentUser.nome,
                        historico: [{
                            data: Timestamp.now(),
                            acao: 'CRIACAO',
                            usuario: currentUser.nome,
                            detalhes: `Pedido criado da cotação ${quotation.numero}`
                        }]
                    };

                    // Salvar no banco
                    const docRef = await addDoc(collection(db, "pedidosCompra"), pedidoData);
                    pedidosCriados.push({
                        id: docRef.id,
                        numero: numeroPedido,
                        fornecedor: fornecedor.razaoSocial,
                        valor: valorTotal
                    });
                }

                // Atualizar status da cotação
                await updateDoc(doc(db, "cotacoes", quotationId), {
                    status: 'FECHADA',
                    dataFechamento: Timestamp.now(),
                    pedidosGerados: pedidosCriados.map(p => ({
                        id: p.id,
                        numero: p.numero
                    })),
                    ultimaAtualizacao: Timestamp.now(),
                    historico: [
                        ...(quotation.historico || []),
                        {
                            data: Timestamp.now(),
                            acao: 'GERACAO_PEDIDOS',
                            usuario: currentUser.nome,
                            detalhes: `Gerados ${pedidosCriados.length} pedidos: ${pedidosCriados.map(p => p.numero).join(', ')}`
                        }
                    ]
                });

                showNotification(`${pedidosCriados.length} pedido(s) de compra gerado(s) com sucesso!`, 'success');

                // Fechar modal e recarregar
                closeModal('comparisonModal');
                await loadQuotations();

                // Mostrar resumo dos pedidos criados
                let resumoHtml = '<div class="alert alert-success"><h5>Pedidos Criados:</h5><ul>';
                pedidosCriados.forEach(pedido => {
                    resumoHtml += `<li><strong>${pedido.numero}</strong> - ${pedido.fornecedor} - R$ ${pedido.valor.toFixed(2)}</li>`;
                });
                resumoHtml += '</ul></div>';

                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = resumoHtml;
                document.body.appendChild(tempDiv);
                setTimeout(() => document.body.removeChild(tempDiv), 5000);

            } catch (error) {
                console.error('Erro ao gerar pedidos:', error);
                showNotification('Erro ao gerar pedidos: ' + error.message, 'error');
            }
        };

        // Função para gerar número do pedido
        async function generatePurchaseOrderNumber() {
            const now = new Date();
            const year = now.getFullYear().toString().slice(-2);
            const month = (now.getMonth() + 1).toString().padStart(2, '0');

            // Buscar último número
            const pedidosRef = collection(db, "pedidosCompra");
            const q = query(pedidosRef, orderBy("dataCriacao", "desc"), limit(1));
            const snapshot = await getDocs(q);

            let nextNumber = 1;
            if (!snapshot.empty) {
                const lastPedido = snapshot.docs[0].data();
                if (lastPedido.numero) {
                    const match = lastPedido.numero.match(/PC-\d{4}-(\d+)/);
                    if (match) {
                        nextNumber = parseInt(match[1]) + 1;
                    }
                }
            }

            return `PC-${year}${month}-${nextNumber.toString().padStart(4, '0')}`;
        }

        window.reopenQuotation = async function(quotationId) {
            const quotation = cotacoes.find(c => c.id === quotationId);
            if (!quotation) {
                showNotification('Cotação não encontrada', 'error');
                return;
            }

            if (quotation.status !== 'FECHADA') {
                showNotification('Apenas cotações fechadas podem ser reabertas', 'warning');
                return;
            }

            const confirmMsg = `Reabrir cotação ${quotation.numero}?\n\nIsso permitirá gerar novos pedidos de compra a partir desta cotação.`;
            if (!confirm(confirmMsg)) {
                return;
            }

            try {
                await updateDoc(doc(db, "cotacoes", quotationId), {
                    status: 'RESPONDIDA',
                    dataReabertura: Timestamp.now(),
                    ultimaAtualizacao: Timestamp.now(),
                    historico: [
                        ...(quotation.historico || []),
                        {
                            data: Timestamp.now(),
                            acao: 'REABERTURA',
                            usuario: currentUser.nome,
                            detalhes: 'Cotação reaberta para nova comparação/geração de pedidos'
                        }
                    ]
                });

                showNotification('Cotação reaberta com sucesso!', 'success');
                await loadQuotations();
                closeModal('quotationDetailsModal');

            } catch (error) {
                console.error('Erro ao reabrir cotação:', error);
                showNotification('Erro ao reabrir cotação: ' + error.message, 'error');
            }
        };

        window.findQuotation = function(numero) {
            console.log('🔍 Procurando cotação:', numero);
            console.log('📊 Total de cotações carregadas:', cotacoes.length);

            const cotacao = cotacoes.find(c => c.numero === numero);

            if (cotacao) {
                console.log('✅ Cotação encontrada:', cotacao);
                console.log('📋 Status:', cotacao.status);
                console.log('📅 Data criação:', cotacao.dataCriacao);
                console.log('🔄 Pedidos gerados:', cotacao.pedidosGerados);

                // Forçar exibição de fechadas
                showFechadas = true;
                document.getElementById('closedToggleIcon').className = 'fas fa-eye';
                document.getElementById('closedToggleText').textContent = 'Ocultar Fechadas';

                // Recarregar lista
                renderQuotations();

                // Destacar a cotação
                setTimeout(() => {
                    const row = document.querySelector(`tr[data-quotation-id="${cotacao.id}"]`);
                    if (row) {
                        row.style.backgroundColor = '#fff3cd';
                        row.style.border = '2px solid #ffc107';
                        row.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        showNotification(`Cotação ${numero} encontrada e destacada!`, 'success');
                    } else {
                        showNotification(`Cotação ${numero} encontrada mas não visível na lista atual`, 'warning');
                    }
                }, 500);

            } else {
                console.log('❌ Cotação não encontrada');
                showNotification(`Cotação ${numero} não encontrada`, 'error');

                // Mostrar todas as cotações para debug
                console.log('📋 Cotações disponíveis:');
                cotacoes.forEach(c => {
                    console.log(`- ${c.numero} (${c.status}) - ID: ${c.id}`);
                });
            }
        };

        window.loadComparison = function() {
            const quotationId = document.getElementById('quotationCompareSelect').value;
            if (!quotationId) {
                document.getElementById('comparisonContainer').innerHTML = '';
                return;
            }

            const quotation = cotacoes.find(c => c.id === quotationId);
            if (!quotation || !quotation.respostas) {
                document.getElementById('comparisonContainer').innerHTML =
                    '<p style="text-align: center; color: #6c757d; padding: 20px;">Nenhuma resposta encontrada para esta cotação.</p>';
                return;
            }

            // Implementar comparação detalhada
            document.getElementById('comparisonContainer').innerHTML =
                '<div class="alert alert-info"><i class="fas fa-info-circle"></i> Comparação detalhada será implementada aqui.</div>';
        };

        window.manageSuppliersForQuotation = function(quotationId) {
            const quotation = cotacoes.find(c => c.id === quotationId);
            if (!quotation) return;

            // Armazenar ID da cotação para uso posterior
            window.currentQuotationId = quotationId;

            const container = document.getElementById('manageSuppliersContainer');
            container.innerHTML = '';

            if (fornecedores.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> Nenhum fornecedor homologado e ativo encontrado.
                    </div>
                `;
                document.getElementById('manageSuppliersModal').style.display = 'block';
                return;
            }

            // Header informativo
            const headerInfo = document.createElement('div');
            headerInfo.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-info-circle"></i>
                    <strong>${fornecedores.length} fornecedores</strong> homologados e ativos disponíveis.
                    <br>Atualmente <strong>${quotation.fornecedores ? quotation.fornecedores.length : 0} fornecedores</strong> selecionados para esta cotação.
                </div>
            `;
            container.appendChild(headerInfo);

            // Grid de fornecedores
            const suppliersGrid = document.createElement('div');
            suppliersGrid.className = 'supplier-grid';
            suppliersGrid.style.maxHeight = '400px';
            suppliersGrid.style.overflowY = 'auto';

            fornecedores.forEach(supplier => {
                const isSelected = quotation.fornecedores && quotation.fornecedores.includes(supplier.id);
                const card = document.createElement('div');
                card.className = `supplier-card ${isSelected ? 'selected' : ''}`;
                card.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <h5 style="margin: 0;">${supplier.nome || 'Nome não informado'}</h5>
                                <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    <i class="fas fa-check"></i> HOMOLOGADO
                                </span>
                                <span style="background: #cce5ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    <i class="fas fa-circle"></i> ATIVO
                                </span>
                            </div>
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-envelope"></i> ${supplier.email || 'Email não informado'}
                            </p>
                            <p style="margin: 5px 0; color: #6c757d; font-size: 14px;">
                                <i class="fas fa-phone"></i> ${supplier.telefone || 'Telefone não informado'}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                <i class="fas fa-map-marker-alt"></i> ${supplier.cidade || 'Cidade não informada'} - ${supplier.estado || 'UF'}
                            </p>
                        </div>
                        <div style="margin-left: 15px;">
                            <input type="checkbox" value="${supplier.id}" ${isSelected ? 'checked' : ''}
                                   style="transform: scale(1.5);" onchange="toggleSupplierCardManage(this)">
                        </div>
                    </div>
                `;
                suppliersGrid.appendChild(card);
            });

            container.appendChild(suppliersGrid);
            document.getElementById('manageSuppliersModal').style.display = 'block';
        };

        window.toggleSupplierCardManage = function(checkbox) {
            const card = checkbox.closest('.supplier-card');
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        };

        window.saveSuppliersChanges = async function() {
            if (!window.currentQuotationId) return;

            const selectedSuppliers = Array.from(
                document.querySelectorAll('#manageSuppliersContainer input[type="checkbox"]:checked')
            ).map(cb => cb.value);

            if (selectedSuppliers.length === 0) {
                showNotification('Selecione pelo menos um fornecedor!', 'warning');
                return;
            }

            try {
                await updateDoc(doc(db, "cotacoes", window.currentQuotationId), {
                    fornecedores: selectedSuppliers,
                    ultimaAtualizacao: Timestamp.now(),
                    atualizadoPor: currentUser.uid
                });

                showNotification(`Fornecedores atualizados! ${selectedSuppliers.length} fornecedores selecionados.`, 'success');

                // Recarregar dados
                await loadQuotations();
                updateStats();

                // Fechar modal
                closeModal('manageSuppliersModal');

                // Se o modal de detalhes estiver aberto, atualizá-lo
                if (document.getElementById('quotationDetailsModal').style.display === 'block') {
                    viewQuotation(window.currentQuotationId);
                }

            } catch (error) {
                console.error('Erro ao atualizar fornecedores:', error);
                showNotification('Erro ao atualizar fornecedores: ' + error.message, 'error');
            }
        };

        window.exportReport = function() {
            showNotification('Funcionalidade de exportação será implementada', 'info');
        };

        // Funções de Paginação
        function updatePaginationInfo() {
            const start = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
            const end = Math.min(currentPage * itemsPerPage, totalItems);

            document.getElementById('paginationInfo').textContent =
                `Mostrando ${start} a ${end} de ${totalItems} cotações`;

            // Atualizar informações sobre cotações ocultas
            const hiddenInfo = document.getElementById('hiddenQuotationsInfo');
            const totalCotacoes = cotacoes.length;
            const cotacoesFechadas = cotacoes.filter(c => c.status === 'FECHADA').length;
            const cotacoesAglutinadas = cotacoes.filter(c => c.aglutinada && c.aglutinada.tipo === 'filha').length;

            let hiddenMessages = [];

            if (!showFechadas && cotacoesFechadas > 0) {
                hiddenMessages.push(`${cotacoesFechadas} fechadas ocultas`);
            }

            if (!showAglutinadas && cotacoesAglutinadas > 0) {
                hiddenMessages.push(`${cotacoesAglutinadas} aglutinadas ocultas`);
            }

            if (hiddenMessages.length > 0) {
                hiddenInfo.innerHTML = `
                    <i class="fas fa-eye-slash"></i>
                    ${hiddenMessages.join(' • ')}
                    (Total: ${totalCotacoes} cotações)
                `;
                hiddenInfo.style.display = 'block';
            } else {
                hiddenInfo.style.display = 'none';
            }
        }

        function updatePaginationControls() {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // Botão Anterior
            const prevBtn = document.createElement('button');
            prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i> Anterior';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => changePage(currentPage - 1);
            pagination.appendChild(prevBtn);

            // Números das páginas
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                const firstBtn = document.createElement('button');
                firstBtn.textContent = '1';
                firstBtn.onclick = () => changePage(1);
                pagination.appendChild(firstBtn);

                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '8px 12px';
                    ellipsis.style.color = '#6c757d';
                    pagination.appendChild(ellipsis);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => changePage(i);
                pagination.appendChild(pageBtn);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '8px 12px';
                    ellipsis.style.color = '#6c757d';
                    pagination.appendChild(ellipsis);
                }

                const lastBtn = document.createElement('button');
                lastBtn.textContent = totalPages;
                lastBtn.onclick = () => changePage(totalPages);
                pagination.appendChild(lastBtn);
            }

            // Botão Próximo
            const nextBtn = document.createElement('button');
            nextBtn.innerHTML = 'Próximo <i class="fas fa-chevron-right"></i>';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => changePage(currentPage + 1);
            pagination.appendChild(nextBtn);
        }

        window.changePage = function(page) {
            if (page >= 1 && page <= Math.ceil(totalItems / itemsPerPage)) {
                currentPage = page;
                renderQuotations(filteredCotacoes);
            }
        };

        window.changeItemsPerPage = function() {
            itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
            currentPage = 1; // Reset para primeira página
            renderQuotations(filteredCotacoes);
        };

        // Função para recarregar fornecedores
        window.reloadSuppliers = async function() {
            try {
                showNotification('Recarregando fornecedores...', 'info');
                await loadInitialData();

                // Se estamos no modal de edição, recarregar a aba de fornecedores
                if (window.currentEditingQuotationId) {
                    const quotation = cotacoes.find(c => c.id === window.currentEditingQuotationId);
                    if (quotation) {
                        await loadEditSuppliers(quotation);
                    }
                }

                showNotification(`${fornecedores.length} fornecedores carregados!`, 'success');
            } catch (error) {
                console.error('Erro ao recarregar fornecedores:', error);
                showNotification('Erro ao recarregar fornecedores: ' + error.message, 'error');
            }
        };

        // Funções de pesquisa de fornecedores
        window.filterEditSuppliers = function() {
            const searchTerm = document.getElementById('editSupplierSearch').value.toLowerCase().trim();

            if (!window.editOriginalSuppliers) {
                console.warn('Lista original de fornecedores não encontrada');
                return;
            }

            let filteredSuppliers;

            if (searchTerm === '') {
                // Se não há termo de pesquisa, mostrar todos
                filteredSuppliers = window.editOriginalSuppliers;
            } else {
                // Filtrar por código, nome ou razão social
                filteredSuppliers = window.editOriginalSuppliers.filter(supplier => {
                    const codigo = (supplier.codigo || '').toLowerCase();
                    const nome = (supplier.nome || '').toLowerCase();
                    const razaoSocial = (supplier.razaoSocial || supplier.razao_social || '').toLowerCase();

                    return codigo.includes(searchTerm) ||
                           nome.includes(searchTerm) ||
                           razaoSocial.includes(searchTerm);
                });
            }

            // Obter cotação atual para manter seleções
            const quotation = cotacoes.find(c => c.id === window.currentEditingQuotationId);

            // Re-renderizar com fornecedores filtrados
            renderEditSuppliers(filteredSuppliers, quotation || { fornecedores: [] });

            // Atualizar contador
            updateEditSupplierCounter();

            // Mostrar resultado da pesquisa
            const resultCount = filteredSuppliers.length;
            const totalCount = window.editOriginalSuppliers.length;

            if (searchTerm !== '') {
                showNotification(`${resultCount} de ${totalCount} fornecedores encontrados`, 'info', 2000);
            }
        };

        window.clearEditSupplierSearch = function() {
            document.getElementById('editSupplierSearch').value = '';
            filterEditSuppliers();
        };

        // Funções de pesquisa para modal de nova cotação
        window.filterSuppliers = function() {
            const searchTerm = document.getElementById('supplierSearch').value.toLowerCase().trim();

            if (!window.originalSuppliers) {
                console.warn('Lista original de fornecedores não encontrada');
                return;
            }

            let filteredSuppliers;

            if (searchTerm === '') {
                // Se não há termo de pesquisa, mostrar todos
                filteredSuppliers = window.originalSuppliers;
            } else {
                // Filtrar por código, nome ou razão social
                filteredSuppliers = window.originalSuppliers.filter(supplier => {
                    const codigo = (supplier.codigo || '').toLowerCase();
                    const nome = (supplier.nome || '').toLowerCase();
                    const razaoSocial = (supplier.razaoSocial || supplier.razao_social || '').toLowerCase();

                    return codigo.includes(searchTerm) ||
                           nome.includes(searchTerm) ||
                           razaoSocial.includes(searchTerm);
                });
            }

            // Re-renderizar com fornecedores filtrados
            renderSuppliers(filteredSuppliers);

            // Mostrar resultado da pesquisa
            const resultCount = filteredSuppliers.length;
            const totalCount = window.originalSuppliers.length;

            if (searchTerm !== '') {
                showNotification(`${resultCount} de ${totalCount} fornecedores encontrados`, 'info', 2000);
            }
        };

        window.clearSupplierSearch = function() {
            document.getElementById('supplierSearch').value = '';
            filterSuppliers();
        };

        // Funções de aglutinação de cotações
        window.toggleQuotationSelection = function(quotationId) {
            if (selectedQuotations.has(quotationId)) {
                selectedQuotations.delete(quotationId);
            } else {
                selectedQuotations.add(quotationId);
            }
            updateSelectedQuotationsCounter();
            updateAglutinacaoButtons();
        };

        window.toggleAllQuotations = function() {
            const checkbox = document.getElementById('selectAllQuotations');
            const quotationCheckboxes = document.querySelectorAll('.quotation-checkbox');

            if (checkbox.checked) {
                quotationCheckboxes.forEach(cb => {
                    cb.checked = true;
                    selectedQuotations.add(cb.value);
                });
            } else {
                quotationCheckboxes.forEach(cb => {
                    cb.checked = false;
                    selectedQuotations.delete(cb.value);
                });
            }

            updateSelectedQuotationsCounter();
            updateAglutinacaoButtons();
        };

        function updateSelectedQuotationsCounter() {
            const counter = document.getElementById('selectedQuotationsCounter');
            const count = selectedQuotations.size;

            counter.innerHTML = `<i class="fas fa-check-square"></i> ${count} selecionadas`;

            // Atualizar cor baseado na quantidade
            if (count === 0) {
                counter.style.background = '#e7f3ff';
                counter.style.color = '#0066cc';
            } else if (count === 1) {
                counter.style.background = '#fff3cd';
                counter.style.color = '#856404';
            } else {
                counter.style.background = '#d4edda';
                counter.style.color = '#155724';
            }
        }

        function updateAglutinacaoButtons() {
            const btnAglutinar = document.getElementById('btnAglutinar');
            const btnDividir = document.getElementById('btnDividir');
            const selectedCount = selectedQuotations.size;

            // Botão aglutinar: precisa de 2+ cotações selecionadas
            btnAglutinar.disabled = selectedCount < 2;

            // Botão dividir: precisa de 1 cotação aglutinada selecionada
            const selectedQuotation = selectedCount === 1 ?
                cotacoes.find(c => c.id === Array.from(selectedQuotations)[0]) : null;
            btnDividir.disabled = !(selectedCount === 1 && selectedQuotation?.aglutinada?.tipo === 'principal');
        }

        window.clearSelection = function() {
            selectedQuotations.clear();
            document.querySelectorAll('.quotation-checkbox').forEach(cb => cb.checked = false);
            document.getElementById('selectAllQuotations').checked = false;
            updateSelectedQuotationsCounter();
            updateAglutinacaoButtons();
        };

        window.toggleAglutinatedView = function() {
            showAglutinadas = !showAglutinadas;
            const icon = document.getElementById('viewToggleIcon');
            const text = document.getElementById('viewToggleText');

            if (showAglutinadas) {
                icon.className = 'fas fa-eye-slash';
                text.textContent = 'Ocultar Aglutinadas';
            } else {
                icon.className = 'fas fa-eye';
                text.textContent = 'Mostrar Aglutinadas';
            }

            renderQuotations();
        };

        window.toggleClosedView = function() {
            showFechadas = !showFechadas;
            const icon = document.getElementById('closedToggleIcon');
            const text = document.getElementById('closedToggleText');

            if (showFechadas) {
                icon.className = 'fas fa-eye';
                text.textContent = 'Ocultar Fechadas';
            } else {
                icon.className = 'fas fa-eye-slash';
                text.textContent = 'Mostrar Fechadas';
            }

            renderQuotations();
        };

        window.toggleAglutinacaoFilhas = function(principalId) {
            const filhas = document.querySelectorAll(`tr[data-quotation-id]`).forEach(row => {
                const quotationId = row.getAttribute('data-quotation-id');
                const quotation = cotacoes.find(c => c.id === quotationId);

                if (quotation?.aglutinada?.principalId === principalId) {
                    row.classList.toggle('hidden');
                }
            });

            const icon = document.getElementById(`expand-icon-${principalId}`);
            if (icon) {
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            }
        };

        window.aglutinarCotacoes = function() {
            if (selectedQuotations.size < 2) {
                showNotification('Selecione pelo menos 2 cotações para aglutinar', 'warning');
                return;
            }

            const selectedIds = Array.from(selectedQuotations);
            const selectedCotacoes = cotacoes.filter(c => selectedIds.includes(c.id));

            // Verificar se alguma já está aglutinada
            const jaAglutinadas = selectedCotacoes.filter(c => c.aglutinada);
            if (jaAglutinadas.length > 0) {
                showNotification('Não é possível aglutinar cotações que já estão aglutinadas', 'error');
                return;
            }

            // Mostrar preview no modal
            showAglutinacaoPreview(selectedCotacoes);
            document.getElementById('aglutinacaoModal').style.display = 'block';
        };

        function showAglutinacaoPreview(cotacoes) {
            const container = document.getElementById('aglutinacaoPreview');

            let html = `
                <h4><i class="fas fa-list"></i> Cotações Selecionadas (${cotacoes.length})</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Data</th>
                                <th>Status</th>
                                <th>Itens</th>
                                <th>Fornecedores</th>
                                <th>Valor</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalItens = 0;
            let totalFornecedores = new Set();
            let valorTotal = 0;

            cotacoes.forEach(cotacao => {
                const itensCount = cotacao.itens ? cotacao.itens.length : 0;
                const fornecedoresCount = cotacao.fornecedores ? cotacao.fornecedores.length : 0;
                const valor = cotacao.valorEstimado || 0;

                totalItens += itensCount;
                if (cotacao.fornecedores) {
                    cotacao.fornecedores.forEach(f => totalFornecedores.add(f));
                }
                valorTotal += valor;

                html += `
                    <tr>
                        <td><strong>${cotacao.numero}</strong></td>
                        <td>${formatDate(cotacao.dataCriacao)}</td>
                        <td><span class="status ${getStatusClass(cotacao.status)}">${getStatusText(cotacao.status)}</span></td>
                        <td>${itensCount}</td>
                        <td>${fornecedoresCount}</td>
                        <td>R$ ${formatCurrency(valor)}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                        <tfoot style="background: #f8f9fa; font-weight: bold;">
                            <tr>
                                <td colspan="3">TOTAIS CONSOLIDADOS:</td>
                                <td>${totalItens}</td>
                                <td>${totalFornecedores.size}</td>
                                <td>R$ ${formatCurrency(valorTotal)}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `;

            // Verificar status diferentes
            const statuses = [...new Set(cotacoes.map(c => c.status))];
            if (statuses.length > 1) {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> As cotações têm status diferentes: ${statuses.join(', ')}
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        window.confirmarAglutinacao = async function() {
            const selectedIds = Array.from(selectedQuotations);
            const selectedCotacoes = cotacoes.filter(c => selectedIds.includes(c.id));

            const observacoes = document.getElementById('aglutinacaoObservacoes').value;
            const consolidarItens = document.getElementById('consolidarItens').checked;
            const manterFornecedores = document.getElementById('manterFornecedores').checked;

            try {
                // Escolher a cotação principal (primeira selecionada ou mais antiga)
                const principal = selectedCotacoes.reduce((oldest, current) =>
                    (oldest.dataCriacao.toDate() < current.dataCriacao.toDate()) ? oldest : current
                );

                const filhas = selectedCotacoes.filter(c => c.id !== principal.id);

                // Gerar número da aglutinação usando o novo padrão
                const numeroAglutinacao = await generateAglutinatedQuotationNumber();
                const dataAglutinacao = Timestamp.now();

                // Consolidar dados
                const itensConsolidados = [];
                const fornecedoresConsolidados = new Set();
                let valorTotalConsolidado = 0;

                selectedCotacoes.forEach(cotacao => {
                    // Consolidar itens
                    if (cotacao.itens) {
                        cotacao.itens.forEach(item => {
                            if (consolidarItens) {
                                const existingItem = itensConsolidados.find(i => i.codigo === item.codigo);
                                if (existingItem) {
                                    existingItem.quantidade += item.quantidade || 0;
                                } else {
                                    itensConsolidados.push({...item});
                                }
                            } else {
                                itensConsolidados.push({...item});
                            }
                        });
                    }

                    // Consolidar fornecedores
                    if (cotacao.fornecedores) {
                        if (manterFornecedores) {
                            cotacao.fornecedores.forEach(f => fornecedoresConsolidados.add(f));
                        } else {
                            cotacao.fornecedores.forEach(f => fornecedoresConsolidados.add(f));
                        }
                    }

                    // Somar valores
                    valorTotalConsolidado += cotacao.valorEstimado || 0;
                });

                // Atualizar cotação principal
                const principalUpdate = {
                    numero: numeroAglutinacao, // Atualizar número para o novo padrão CTA
                    aglutinada: {
                        tipo: 'principal',
                        numeroOriginal: principal.numero, // Salvar número original
                        dataAglutinacao: dataAglutinacao,
                        cotacoesFilhas: filhas.map(f => ({ id: f.id, numero: f.numero })),
                        observacoes: observacoes || `Aglutinação de ${selectedCotacoes.length} cotações`
                    },
                    itens: itensConsolidados,
                    fornecedores: Array.from(fornecedoresConsolidados),
                    valorEstimado: valorTotalConsolidado,
                    ultimaAtualizacao: dataAglutinacao,
                    historico: [
                        ...(principal.historico || []),
                        {
                            data: dataAglutinacao,
                            acao: 'AGLUTINACAO_PRINCIPAL',
                            usuario: currentUser.nome,
                            detalhes: `Aglutinou ${filhas.length} cotações: ${filhas.map(f => f.numero).join(', ')} → ${numeroAglutinacao}`
                        }
                    ]
                };

                await updateDoc(doc(db, "cotacoes", principal.id), principalUpdate);

                // Atualizar cotações filhas
                for (const filha of filhas) {
                    const filhaUpdate = {
                        aglutinada: {
                            tipo: 'filha',
                            numeroAglutinacao: numeroAglutinacao,
                            principalId: principal.id,
                            dataAglutinacao: dataAglutinacao
                        },
                        status: 'AGLUTINADA',
                        statusOriginal: filha.status, // Salvar status original para restauração
                        ultimaAtualizacao: dataAglutinacao,
                        historico: [
                            ...(filha.historico || []),
                            {
                                data: dataAglutinacao,
                                acao: 'AGLUTINACAO_FILHA',
                                usuario: currentUser.nome,
                                detalhes: `Aglutinada à cotação principal ${numeroAglutinacao} (era ${principal.numero})`
                            }
                        ]
                    };

                    await updateDoc(doc(db, "cotacoes", filha.id), filhaUpdate);
                }

                showNotification(`${selectedCotacoes.length} cotações aglutinadas com sucesso!`, 'success');

                // Fechar modal e recarregar dados
                closeModal('aglutinacaoModal');
                await loadQuotations();
                clearSelection();

            } catch (error) {
                console.error('Erro ao aglutinar cotações:', error);
                showNotification('Erro ao aglutinar cotações: ' + error.message, 'error');
            }
        };

        window.dividirCotacao = async function(quotationId = null) {
            // Se não foi passado ID, usar a selecionada
            if (!quotationId && selectedQuotations.size === 1) {
                quotationId = Array.from(selectedQuotations)[0];
            }

            if (!quotationId) {
                showNotification('Selecione uma cotação aglutinada para dividir', 'warning');
                return;
            }

            const cotacao = cotacoes.find(c => c.id === quotationId);
            if (!cotacao || !cotacao.aglutinada || cotacao.aglutinada.tipo !== 'principal') {
                showNotification('Apenas cotações principais aglutinadas podem ser divididas', 'error');
                return;
            }

            const filhas = cotacoes.filter(c =>
                c.aglutinada && c.aglutinada.principalId === quotationId
            );

            if (!confirm(`Dividir aglutinação? Isso irá restaurar ${filhas.length + 1} cotações independentes.`)) {
                return;
            }

            try {
                const dataDivisao = Timestamp.now();

                // Restaurar cotação principal
                const principalUpdate = {
                    numero: cotacao.aglutinada.numeroOriginal || cotacao.numero, // Restaurar número original
                    aglutinada: null,
                    ultimaAtualizacao: dataDivisao,
                    historico: [
                        ...(cotacao.historico || []),
                        {
                            data: dataDivisao,
                            acao: 'DIVISAO_AGLUTINACAO',
                            usuario: currentUser.nome,
                            detalhes: `Dividiu aglutinação com ${filhas.length} cotações`
                        }
                    ]
                };

                // Remover campos consolidados se necessário
                delete principalUpdate.aglutinada;

                await updateDoc(doc(db, "cotacoes", quotationId), principalUpdate);

                // Restaurar cotações filhas
                for (const filha of filhas) {
                    const filhaUpdate = {
                        aglutinada: null,
                        status: filha.statusOriginal || 'ABERTA', // Restaurar status original
                        ultimaAtualizacao: dataDivisao,
                        historico: [
                            ...(filha.historico || []),
                            {
                                data: dataDivisao,
                                acao: 'DIVISAO_RESTAURACAO',
                                usuario: currentUser.nome,
                                detalhes: 'Cotação restaurada após divisão de aglutinação'
                            }
                        ]
                    };

                    delete filhaUpdate.aglutinada;

                    await updateDoc(doc(db, "cotacoes", filha.id), filhaUpdate);
                }

                showNotification(`Aglutinação dividida! ${filhas.length + 1} cotações restauradas.`, 'success');

                // Recarregar dados
                await loadQuotations();
                clearSelection();

            } catch (error) {
                console.error('Erro ao dividir cotação:', error);
                showNotification('Erro ao dividir cotação: ' + error.message, 'error');
            }
        };

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        };
    </script>
</body>
</html>
