<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversor Firebase → PHP/MySQL</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px dashed #e0e0e0;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .step:hover {
            border-color: #4facfe;
            background: #f8f9ff;
        }

        .step-number {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .step h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            display: none;
        }

        .file-input-button {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            width: 100%;
            text-align: center;
        }

        .file-input-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .file-list {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .file-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            border-radius: 4px;
        }

        .html-icon { background: #e34f26; }
        .js-icon { background: #f7df1e; }
        .css-icon { background: #1572b6; }
        .other-icon { background: #6c757d; }

        .conversion-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .conversion-status.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .conversion-status.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .preview-area {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            display: none;
        }

        .preview-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .preview-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1em;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .preview-tab.active {
            color: #4facfe;
            border-bottom-color: #4facfe;
        }

        .preview-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .download-section {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-top: 30px;
            border-radius: 15px;
        }

        .download-btn {
            background: white;
            color: #667eea;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255,255,255,0.3);
        }

        .download-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
        }

        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .config-row {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }

        .config-row label {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .config-row input, .config-row select {
            flex: 2;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
        }

        .config-row input:focus, .config-row select:focus {
            outline: none;
            border-color: #4facfe;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .processing {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Conversor Firebase → PHP/MySQL</h1>
            <p>Migre seu projeto Firebase para PHP + MySQL automaticamente</p>
        </div>

        <div class="main-content">
            <!-- Passo 1: Seleção de Arquivos -->
            <div class="step">
                <div class="step-number">1</div>
                <h3>📁 Selecionar Projeto</h3>
                <p>Selecione todos os arquivos do seu projeto (HTML, JS, CSS)</p>

                <div class="file-input-wrapper">
                    <input type="file" id="projectFiles" class="file-input" multiple accept=".html,.js,.css,.json">
                    <button class="file-input-button" onclick="document.getElementById('projectFiles').click()">
                        🔍 Selecionar Arquivos do Projeto
                    </button>
                </div>

                <div id="fileList" class="file-list" style="display: none;"></div>
            </div>

            <!-- Passo 2: Configurações -->
            <div class="step">
                <div class="step-number">2</div>
                <h3>⚙️ Configurações de Conversão</h3>

                <div class="config-section">
                    <div class="config-row">
                        <label for="dbName">Nome do Banco:</label>
                        <input type="text" id="dbName" value="meu_projeto_db" placeholder="Nome do banco MySQL">
                    </div>

                    <div class="config-row">
                        <label for="tablePrefix">Prefixo das Tabelas:</label>
                        <input type="text" id="tablePrefix" value="tb_" placeholder="Prefixo para tabelas">
                    </div>

                    <div class="config-row">
                        <label for="phpVersion">Versão PHP:</label>
                        <select id="phpVersion">
                            <option value="8.0">PHP 8.0+</option>
                            <option value="7.4">PHP 7.4</option>
                            <option value="5.6">PHP 5.6</option>
                        </select>
                    </div>

                    <div class="config-row">
                        <label for="includeAuth">Incluir Sistema de Auth:</label>
                        <select id="includeAuth">
                            <option value="true">Sim</option>
                            <option value="false">Não</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Passo 3: Conversão -->
            <div class="step">
                <div class="step-number">3</div>
                <h3>🔄 Processar Conversão</h3>
                <p>Analise e converta seus arquivos Firebase para PHP/MySQL</p>

                <button class="file-input-button" id="processBtn" onclick="processConversion()">
                    🚀 Iniciar Conversão
                </button>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div id="conversionStatus" class="conversion-status"></div>
            </div>

            <!-- Preview dos Resultados -->
            <div class="preview-area" id="previewArea">
                <h3>👀 Preview dos Arquivos Gerados</h3>

                <div class="preview-tabs">
                    <button class="preview-tab active" onclick="showPreview('php')">📄 PHP Files</button>
                    <button class="preview-tab" onclick="showPreview('sql')">🗄️ SQL Schema</button>
                    <button class="preview-tab" onclick="showPreview('config')">⚙️ Config</button>
                </div>

                <div id="previewContent" class="preview-content"></div>
            </div>

            <!-- Download -->
            <div class="download-section" id="downloadSection" style="display: none;">
                <h3>📦 Download dos Arquivos Convertidos</h3>
                <p>Seus arquivos PHP e SQL estão prontos para uso no XAMPP!</p>

                <button class="download-btn" id="downloadPhp" onclick="downloadFiles('php')">
                    📄 Download PHP Files
                </button>
                <button class="download-btn" id="downloadSql" onclick="downloadFiles('sql')">
                    🗄️ Download SQL Schema
                </button>
                <button class="download-btn" id="downloadAll" onclick="downloadFiles('all')">
                    📦 Download Completo (.zip)
                </button>
            </div>
        </div>
    </div>

    <script>
        let projectFiles = [];
        let convertedFiles = {
            php: {},
            sql: '',
            config: ''
        };

        // Manipular seleção de arquivos
        document.getElementById('projectFiles').addEventListener('change', function(e) {
            projectFiles = Array.from(e.target.files);
            displayFileList();
        });

        function displayFileList() {
            const fileListDiv = document.getElementById('fileList');
            const fileList = document.getElementById('fileList');

            if (projectFiles.length === 0) {
                fileListDiv.style.display = 'none';
                return;
            }

            fileListDiv.style.display = 'block';
            fileList.innerHTML = '';

            projectFiles.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                const icon = document.createElement('div');
                icon.className = 'file-icon';

                if (file.name.endsWith('.html')) icon.className += ' html-icon';
                else if (file.name.endsWith('.js')) icon.className += ' js-icon';
                else if (file.name.endsWith('.css')) icon.className += ' css-icon';
                else icon.className += ' other-icon';

                fileItem.innerHTML = `
                    <div class="${icon.className}"></div>
                    <span>${file.name} (${(file.size/1024).toFixed(1)} KB)</span>
                `;

                fileList.appendChild(fileItem);
            });
        }

        // Processar conversão
        async function processConversion() {
            if (projectFiles.length === 0) {
                showStatus('error', 'Por favor, selecione os arquivos do projeto primeiro!');
                return;
            }

            const processBtn = document.getElementById('processBtn');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            processBtn.disabled = true;
            processBtn.textContent = '🔄 Processando...';
            processBtn.classList.add('processing');
            progressBar.style.display = 'block';

            try {
                // Simular processamento com progresso
                for (let i = 0; i <= 100; i += 10) {
                    progressFill.style.width = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                // Processar arquivos
                await analyzeAndConvertFiles();

                showStatus('success', `✅ Conversão concluída! ${Object.keys(convertedFiles.php).length} arquivos PHP gerados.`);

                // Mostrar preview e download
                document.getElementById('previewArea').style.display = 'block';
                document.getElementById('downloadSection').style.display = 'block';

                showPreview('php');

            } catch (error) {
                showStatus('error', `❌ Erro na conversão: ${error.message}`);
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '🚀 Iniciar Conversão';
                processBtn.classList.remove('processing');
                progressBar.style.display = 'none';
            }
        }

        async function analyzeAndConvertFiles() {
            const dbName = document.getElementById('dbName').value;
            const tablePrefix = document.getElementById('tablePrefix').value;
            const phpVersion = document.getElementById('phpVersion').value;
            const includeAuth = document.getElementById('includeAuth').value === 'true';

            convertedFiles.php = {};
            convertedFiles.sql = '';
            convertedFiles.config = '';

            // Gerar arquivo de configuração
            convertedFiles.config = generateConfigFile(dbName);

            // Processar cada arquivo
            for (const file of projectFiles) {
                const content = await readFileContent(file);

                if (file.name.endsWith('.html')) {
                    convertedFiles.php[file.name.replace('.html', '.php')] = convertHTMLtoPHP(content, file.name);
                } else if (file.name.endsWith('.js')) {
                    const phpCode = convertJStoPHP(content);
                    if (phpCode) {
                        convertedFiles.php[file.name.replace('.js', '.php')] = phpCode;
                    }
                }
            }

            // Gerar SQL baseado nas operações Firebase detectadas
            convertedFiles.sql = generateSQLSchema(tablePrefix, includeAuth);

            // Adicionar arquivos de utilidade
            convertedFiles.php['database.php'] = generateDatabaseClass(dbName);
            convertedFiles.php['api.php'] = generateAPIClass();

            if (includeAuth) {
                convertedFiles.php['auth.php'] = generateAuthClass();
            }
        }

        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }

        function convertHTMLtoPHP(htmlContent, fileName) {
            let phpContent = htmlContent;

            // Substituir chamadas Firebase por PHP
            phpContent = phpContent.replace(/firebase\.(auth|database|firestore)/gi, '<?php // Firebase call converted ?>');
            phpContent = phpContent.replace(/<script.*firebase.*<\/script>/gis, '<?php require_once "database.php"; ?>');

            // Adicionar cabeçalho PHP
            phpContent = `<?php
// Arquivo convertido de ${fileName}
require_once 'database.php';
require_once 'auth.php';

session_start();
?>
${phpContent}`;

            return phpContent;
        }

        function convertJStoPHP(jsContent) {
            if (!jsContent.includes('firebase')) return null;

            let phpContent = `<?php
// Arquivo JavaScript convertido para PHP
require_once 'database.php';

class ConvertedScript {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

`;

            // Converter operações básicas do Firebase
            jsContent.split('\n').forEach(line => {
                if (line.includes('.set(')) {
                    phpContent += `    // INSERT/UPDATE: ${line.trim()}\n`;
                    phpContent += `    // $this->db->insert('table_name', $data);\n`;
                } else if (line.includes('.get(')) {
                    phpContent += `    // SELECT: ${line.trim()}\n`;
                    phpContent += `    // $result = $this->db->select('table_name', $conditions);\n`;
                } else if (line.includes('.delete(')) {
                    phpContent += `    // DELETE: ${line.trim()}\n`;
                    phpContent += `    // $this->db->delete('table_name', $id);\n`;
                }
            });

            phpContent += `
}

// Instanciar e usar a classe
$script = new ConvertedScript();
?>`;

            return phpContent;
        }

        function generateConfigFile(dbName) {
            return `<?php
// Configuração do banco de dados MySQL
define('DB_HOST', 'localhost');
define('DB_NAME', '${dbName}');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Configurações gerais
define('SITE_URL', 'http://localhost/');
define('UPLOADS_DIR', 'uploads/');

// Configurações de sessão
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Mudar para 1 em HTTPS

// Timezone
date_default_timezone_set('America/Sao_Paulo');
?>`;
        }

        function generateDatabaseClass(dbName) {
            return `<?php
class Database {
    private $pdo;
    private $host = DB_HOST;
    private $dbname = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;

    public function __construct() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4";
            $this->pdo = new PDO($dsn, $this->username, $this->password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            die("Erro de conexão: " . $e->getMessage());
        }
    }

    public function select($table, $conditions = [], $orderBy = '') {
        $sql = "SELECT * FROM {$table}";

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', array_map(fn($k) => "{$k} = :{$k}", array_keys($conditions)));
        }

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($conditions);
        return $stmt->fetchAll();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);

        return $stmt->execute($data);
    }

    public function update($table, $data, $conditions) {
        $setClause = implode(', ', array_map(fn($k) => "{$k} = :{$k}", array_keys($data)));
        $whereClause = implode(' AND ', array_map(fn($k) => "{$k} = :where_{$k}", array_keys($conditions)));

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$whereClause}";

        $params = $data;
        foreach ($conditions as $key => $value) {
            $params["where_{$key}"] = $value;
        }

        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function delete($table, $conditions) {
        $whereClause = implode(' AND ', array_map(fn($k) => "{$k} = :{$k}", array_keys($conditions)));

        $sql = "DELETE FROM {$table} WHERE {$whereClause}";
        $stmt = $this->pdo->prepare($sql);

        return $stmt->execute($conditions);
    }
}
?>`;
        }

        function generateAPIClass() {
            return `<?php
header('Content-Type: application/json');
require_once 'database.php';
require_once 'auth.php';

class API {
    private $db;
    private $auth;

    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));

        switch ($method) {
            case 'GET':
                return $this->handleGet($pathParts);
            case 'POST':
                return $this->handlePost($pathParts);
            case 'PUT':
                return $this->handlePut($pathParts);
            case 'DELETE':
                return $this->handleDelete($pathParts);
            default:
                return $this->jsonResponse(['error' => 'Método não permitido'], 405);
        }
    }

    private function handleGet($pathParts) {
        // Implementar rotas GET
        return $this->jsonResponse(['message' => 'GET endpoint']);
    }

    private function handlePost($pathParts) {
        // Implementar rotas POST
        $input = json_decode(file_get_contents('php://input'), true);
        return $this->jsonResponse(['message' => 'POST endpoint', 'data' => $input]);
    }

    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Executar API
$api = new API();
$api->handleRequest();
?>`;
        }

        function generateAuthClass() {
            return `<?php
class Auth {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    public function register($email, $password, $userData = []) {
        // Verificar se email já existe
        $existing = $this->db->select('users', ['email' => $email]);
        if (!empty($existing)) {
            return ['success' => false, 'message' => 'Email já cadastrado'];
        }

        // Hash da senha
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Dados do usuário
        $data = array_merge($userData, [
            'email' => $email,
            'password' => $hashedPassword,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        if ($this->db->insert('users', $data)) {
            return ['success' => true, 'message' => 'Usuário cadastrado com sucesso'];
        }

        return ['success' => false, 'message' => 'Erro ao cadastrar usuário'];
    }

    public function login($email, $password) {
        $user = $this->db->select('users', ['email' => $email]);

        if (empty($user)) {
            return ['success' => false, 'message' => 'Usuário não encontrado'];
        }

        $user = $user[0];

        if (password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];

            return ['success' => true, 'message' => 'Login realizado com sucesso', 'user' => $user];
        }

        return ['success' => false, 'message' => 'Senha incorreta'];
    }

    public function logout() {
        session_destroy();
        return ['success' => true, 'message' => 'Logout realizado com sucesso'];
    }

    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }

    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return $this->db->select('users', ['id' => $_SESSION['user_id']])[0] ?? null;
    }
}
?>`;
        }

        function generateSQLSchema(tablePrefix, includeAuth) {
            let sql = `-- Schema MySQL gerado automaticamente
-- Execute este arquivo no phpMyAdmin ou MySQL Workbench

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Criar banco de dados
CREATE DATABASE IF NOT EXISTS \`${document.getElementById('dbName').value}\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE \`${document.getElementById('dbName').value}\`;

`;

            if (includeAuth) {
                sql += `-- Tabela de usuários
CREATE TABLE \`${tablePrefix}users\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`email\` varchar(255) NOT NULL,
  \`password\` varchar(255) NOT NULL,
  \`name\` varchar(255) DEFAULT NULL,
  \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  \`status\` enum('active','inactive') DEFAULT 'active',
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`email\` (\`email\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

`;
            }

            sql += `-- Tabela de dados (exemplo baseado em Firebase collections)
CREATE TABLE \`${tablePrefix}data\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`user_id\` int(11) DEFAULT NULL,
  \`title\` varchar(255) DEFAULT NULL,
  \`content\` text,
  \`data_json\` json DEFAULT NULL,
  \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (\`id\`),
  KEY \`user_id\` (\`user_id\`),
  FOREIGN KEY (\`user_id\`) REFERENCES \`${tablePrefix}users\` (\`id\`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de configurações
CREATE TABLE \`${tablePrefix}settings\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`key_name\` varchar(100) NOT NULL,
  \`value\` text,
  \`type\` enum('string','json','number','boolean') DEFAULT 'string',
  \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`key_name\` (\`key_name\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;`;

            return sql;
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('conversionStatus');
            statusDiv.className = `conversion-status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }

        function showPreview(type) {
            // Remover classe active de todas as tabs
            document.querySelectorAll('.preview-tab').forEach(tab => tab.classList.remove('active'));

            // Adicionar classe active na tab clicada
            event.target.classList.add('active');

            const previewContent = document.getElementById('previewContent');

            switch(type) {
                case 'php':
                    let phpPreview = '';
                    Object.entries(convertedFiles.php).forEach(([filename, content]) => {
                        phpPreview += `// ===== ${filename} =====\n${content}\n\n`;
                    });
                    previewContent.textContent = phpPreview || 'Nenhum arquivo PHP gerado ainda.';
                    break;

                case 'sql':
                    previewContent.textContent = convertedFiles.sql || 'Schema SQL não gerado ainda.';
                    break;

                case 'config':
                    previewContent.textContent = convertedFiles.config || 'Arquivo de configuração não gerado ainda.';
                    break;
            }
        }

        function downloadFiles(type) {
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

            switch(type) {
                case 'php':
                    Object.entries(convertedFiles.php).forEach(([filename, content]) => {
                        downloadFile(filename, content, 'text/php');
                    });
                    downloadFile('config.php', convertedFiles.config, 'text/php');
                    break;

                case 'sql':
                    downloadFile(`schema_${timestamp}.sql`, convertedFiles.sql, 'text/sql');
                    break;

                case 'all':
                    downloadZip();
                    break;
            }
        }

        function downloadFile(filename, content, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function downloadZip() {
            // Simular download de ZIP (em um ambiente real, usaria JSZip)
            alert('💡 Dica: Em um ambiente real, isso geraria um arquivo ZIP com todos os arquivos. Por enquanto, baixe os arquivos individualmente.');
        }
    </script>
</body>
</html>