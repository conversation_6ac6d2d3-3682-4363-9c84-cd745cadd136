<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Visualizar Ordem de Produção</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
    .container { max-width: 800px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 24px; }
    h1 { color: #0854a0; }
    .info-box { background: #f0f3f6; border-radius: 6px; padding: 12px; margin-bottom: 18px; }
    label { font-weight: 500; color: #0854a0; }
    input, button { padding: 8px 12px; border-radius: 4px; border: 1px solid #d4d4d4; font-size: 15px; }
    button { background: #0854a0; color: #fff; border: none; cursor: pointer; margin-left: 8px; }
    button:hover { background: #0a4d8c; }
    table { width: 100%; border-collapse: collapse; margin-top: 18px; }
    th, td { border: 1px solid #d4d4d4; padding: 7px 10px; text-align: left; }
    th { background: #f0f3f6; }
    .error { color: #bb0000; margin-top: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Visualizar Ordem de Produção</h1>
    <form id="searchForm" onsubmit="return false;">
      <label for="opInput">Número ou ID da OP:</label>
      <input type="text" id="opInput" placeholder="Ex: OP25050705 ou ID Firestore" required>
      <button onclick="buscarOP()">Buscar</button>
    </form>
    <div id="result"></div>
  </div>
  <script type="module">
    import { db } from '../firebase-config.js';
    import { collection, getDocs, getDoc, doc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    window.buscarOP = async function() {
      const opInput = document.getElementById('opInput').value.trim();
      const resultDiv = document.getElementById('result');
      resultDiv.innerHTML = '';
      if (!opInput) return;
      let opDoc = null;
      // Tenta buscar por número
      const q = query(collection(db, 'ordensProducao'), where('numero', '==', opInput));
      const snap = await getDocs(q);
      if (!snap.empty) {
        opDoc = { id: snap.docs[0].id, ...snap.docs[0].data() };
      } else {
        // Tenta buscar por ID
        try {
          const docRef = doc(db, 'ordensProducao', opInput);
          const docSnap = await getDoc(docRef);
          if (docSnap.exists()) {
            opDoc = { id: docSnap.id, ...docSnap.data() };
          }
        } catch (e) {}
      }
      if (!opDoc) {
        resultDiv.innerHTML = '<div class="error">OP não encontrada.</div>';
        return;
      }
      let html = `<div class="info-box"><b>ID:</b> ${opDoc.id}<br>`;
      Object.keys(opDoc).forEach(k => {
        if (k !== 'materiaisNecessarios') {
          html += `<b>${k}:</b> ${JSON.stringify(opDoc[k])}<br>`;
        }
      });
      html += '</div>';
      if (opDoc.materiaisNecessarios && opDoc.materiaisNecessarios.length) {
        html += `<h2>Materiais Necessários</h2><table><thead><tr><th>Código</th><th>Descrição</th><th>Unidade</th><th>Necessidade</th><th>Saldo Estoque</th><th>Saldo Reservado</th></tr></thead><tbody>`;
        for (const mat of opDoc.materiaisNecessarios) {
          html += `<tr><td>${mat.codigo || ''}</td><td>${mat.descricao || ''}</td><td>${mat.unidade || ''}</td><td>${mat.necessidade ?? ''}</td><td>${mat.saldoEstoque ?? ''}</td><td>${mat.saldoReservado ?? ''}</td></tr>`;
        }
        html += '</tbody></table>';
      } else {
        html += '<div class="info-box">Sem materiais necessários registrados.</div>';
      }
      resultDiv.innerHTML = html;
    }
  </script>
</body>
</html> 