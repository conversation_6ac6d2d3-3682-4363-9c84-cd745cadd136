<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Teste Simples - Apontamentos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .ops-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .op-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        .op-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .op-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.aguardando { background: #fff3cd; color: #856404; }
        .status.producao { background: #d4edda; color: #155724; }
        .status.pausada { background: #f8d7da; color: #721c24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste Simples - Apontamentos</h1>
        <p>Versão simplificada para testar carregamento de OPs</p>
        
        <button class="btn" onclick="carregarOPs()">📋 Carregar OPs</button>
        <button class="btn" onclick="limparLog()">🗑️ Limpar Log</button>
        
        <div id="opsGrid" class="ops-grid"></div>
        
        <div id="log" class="log">Aguardando carregamento...</div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;

        let ordensProducao = [];
        let produtos = [];

        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        window.carregarOPs = async function() {
            addLog('🔄 Iniciando carregamento de OPs...');
            
            try {
                // Verificar Firebase
                if (!window.db) {
                    throw new Error('Firebase não inicializado');
                }
                
                addLog('✅ Firebase disponível');
                addLog(`📊 Projeto: ${db.app.options.projectId}`);
                
                // Carregar produtos primeiro
                addLog('📦 Carregando produtos...');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                addLog(`✅ ${produtos.length} produtos carregados`);
                
                // Carregar OPs
                addLog('📋 Carregando ordens de produção...');
                const opsSnap = await getDocs(collection(db, "ordensProducao"));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                addLog(`✅ ${ordensProducao.length} OPs carregadas`);
                
                if (ordensProducao.length > 0) {
                    addLog('📋 Primeiras OPs:');
                    ordensProducao.slice(0, 3).forEach(op => {
                        addLog(`  - ${op.numero || op.id}: ${op.produto || 'Sem produto'} (${op.status || 'Sem status'})`);
                    });
                    
                    renderizarOPs();
                } else {
                    addLog('⚠️ Nenhuma OP encontrada');
                }
                
            } catch (error) {
                addLog(`❌ Erro: ${error.message}`);
                console.error('Erro detalhado:', error);
            }
        };

        function renderizarOPs() {
            addLog('🎨 Renderizando OPs na interface...');
            
            const grid = document.getElementById('opsGrid');
            grid.innerHTML = '';
            
            if (ordensProducao.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #666;">Nenhuma OP encontrada</div>';
                return;
            }
            
            ordensProducao.forEach(op => {
                const produto = produtos.find(p => p.id === op.produtoId) || {};
                
                const card = document.createElement('div');
                card.className = 'op-card';
                
                const statusClass = (op.status || '').toLowerCase().replace(' ', '');
                
                card.innerHTML = `
                    <div class="op-header">OP: ${op.numero || op.id}</div>
                    <div class="op-info">Produto: ${produto.codigo || op.produto || 'N/A'}</div>
                    <div class="op-info">Descrição: ${produto.descricao || op.descricaoProduto || 'N/A'}</div>
                    <div class="op-info">Quantidade: ${op.quantidade || 0}</div>
                    <div class="op-info">Status: <span class="status ${statusClass}">${op.status || 'N/A'}</span></div>
                    <div class="op-info">Data: ${op.dataEntrega ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'N/A'}</div>
                `;
                
                grid.appendChild(card);
            });
            
            addLog(`✅ ${ordensProducao.length} OPs renderizadas`);
        }

        window.limparLog = function() {
            document.getElementById('log').innerHTML = 'Log limpo...\n';
        };

        // Inicialização
        addLog('🚀 Página carregada');
        addLog('💡 Clique em "Carregar OPs" para começar');
    </script>
</body>
</html>
