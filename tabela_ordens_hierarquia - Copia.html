<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Necessidades de MP por Produto Pai</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --success-color: #28a745;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #17a2b8;
      --border-color: #dee2e6;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header {
      background: linear-gradient(135deg, var(--primary-color), #0a4d8c);
      color: white;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      text-align: center;
    }

    .header h1 {
      font-size: 28px;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 16px;
      opacity: 0.9;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid var(--primary-color);
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .stat-card.pai {
      border-left-color: var(--success-color);
    }

    .stat-card.filha {
      border-left-color: var(--info-color);
    }

    .stat-card.orfao {
      border-left-color: var(--warning-color);
    }

    .stat-value {
      font-size: 32px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .stat-label {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }

    .filters {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid var(--border-color);
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      font-weight: 600;
      margin-bottom: 5px;
      color: #333;
    }

    .filter-group select,
    .filter-group input {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #0a4d8c;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table-header {
      background: var(--primary-color);
      color: white;
      padding: 15px 20px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    th {
      background: #f8f9fa;
      color: #333;
      padding: 12px 8px;
      text-align: left;
      font-weight: 600;
      border-bottom: 2px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 10;
    }

    td {
      padding: 12px 8px;
      border-bottom: 1px solid #e9ecef;
      vertical-align: middle;
    }

    tbody tr:hover {
      background-color: #f8f9fa;
    }

    .nivel-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: bold;
      text-align: center;
      min-width: 30px;
      display: inline-block;
    }

    .nivel-0 {
      background: var(--success-color);
      color: white;
    }

    .nivel-1 {
      background: var(--info-color);
      color: white;
    }

    .nivel-2 {
      background: var(--warning-color);
      color: #333;
    }

    .nivel-3 {
      background: var(--danger-color);
      color: white;
    }

    .nivel-4plus {
      background: #6f42c1;
      color: white;
    }

    .tipo-badge {
      padding: 3px 8px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .tipo-pa {
      background: #e3f2fd;
      color: #1976d2;
    }

    .tipo-sp {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .tipo-mp {
      background: #e8f5e8;
      color: #388e3c;
    }

    .status-badge {
      padding: 4px 10px;
      border-radius: 15px;
      font-size: 11px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .status-pendente {
      background: #fff3cd;
      color: #856404;
    }

    .status-producao {
      background: #d1ecf1;
      color: #0c5460;
    }

    .status-finalizada {
      background: #d4edda;
      color: #155724;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-data {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        padding: 15px;
      }

      .stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .filters-grid {
        grid-template-columns: 1fr;
      }

      table {
        font-size: 12px;
      }

      th, td {
        padding: 8px 4px;
      }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-shopping-cart"></i> Necessidades de MP por Produto Pai</h1>
      <p>Geração de listas de necessidades de matéria-prima por produto pai específico</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats">
      <div class="stat-card">
        <div class="stat-value" id="totalOrdens">-</div>
        <div class="stat-label">OPs Pendentes</div>
      </div>
      <div class="stat-card pai">
        <div class="stat-value" id="ordensPai">-</div>
        <div class="stat-label">Produtos Pai Disponíveis</div>
      </div>
      <div class="stat-card filha">
        <div class="stat-value" id="totalMPs">-</div>
        <div class="stat-label">MPs Necessárias</div>
      </div>
      <div class="stat-card orfao">
        <div class="stat-value" id="valorTotal">R$ -</div>
        <div class="stat-label">Valor Total Estimado</div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="filters">
      <div class="filters-grid">
        <div class="filter-group">
          <label for="filtroStatus">Status:</label>
          <select id="filtroStatus">
            <option value="">Todos os Status</option>
            <option value="pendente">Pendente</option>
            <option value="em produção">Em Produção</option>
            <option value="finalizada">Finalizada</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroNivel">Nível:</label>
          <select id="filtroNivel">
            <option value="">Todos os Níveis</option>
            <option value="0">Nível 0 (Pai)</option>
            <option value="1">Nível 1</option>
            <option value="2">Nível 2</option>
            <option value="3">Nível 3+</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroTipo">Tipo Produto:</label>
          <select id="filtroTipo">
            <option value="">Todos os Tipos</option>
            <option value="PA">PA - Produto Acabado</option>
            <option value="SP">SP - Semi Produto</option>
            <option value="MP">MP - Matéria Prima</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroProdutoPai">Produto Pai:</label>
          <select id="filtroProdutoPai">
            <option value="">Todos os Produtos Pai</option>
            <!-- Será preenchido dinamicamente -->
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroBusca">Buscar:</label>
          <input type="text" id="filtroBusca" placeholder="Número OP, código produto ou produto pai...">
        </div>
      </div>
      <div style="text-align: right;">
        <button class="btn btn-secondary" onclick="limparFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button class="btn btn-primary" onclick="gerarListaNecessidades()" style="background-color: #28a745;">
          <i class="fas fa-shopping-cart"></i> Gerar Lista Geral de MPs
        </button>
        <button class="btn btn-primary" onclick="gerarListaPorProdutoPai()" style="background-color: #17a2b8;">
          <i class="fas fa-filter"></i> MPs do Produto Pai Selecionado
        </button>
        <button class="btn btn-primary" onclick="recarregarDados()">
          <i class="fas fa-sync"></i> Atualizar Dados
        </button>
      </div>
    </div>

    <!-- Tabela de Ordens (Oculta) -->
    <div class="table-container" style="display: none;">
      <div class="table-header">
        <span><i class="fas fa-table"></i> Ordens de Produção</span>
        <span id="contadorOrdens">Carregando...</span>
      </div>

      <div id="loadingContainer" class="loading">
        <div class="loading-spinner"></div>
        <p>Carregando ordens de produção...</p>
      </div>

      <div id="noDataContainer" class="no-data" style="display: none;">
        <i class="fas fa-inbox"></i>
        <h3>Nenhuma ordem encontrada</h3>
        <p>Não há ordens que atendam aos filtros aplicados.</p>
      </div>

      <div style="max-height: 600px; overflow-y: auto;">
        <table id="tabelaOrdens" style="display: none;">
          <thead>
            <tr>
              <th>Número OP</th>
              <th>Nível</th>
              <th>Produto</th>
              <th>Tipo</th>
              <th>Quantidade</th>
              <th>Status</th>
              <th>OP Pai</th>
              <th>Produto Pai</th>
              <th>Produto Raiz</th>
              <th>Data Criação</th>
            </tr>
          </thead>
          <tbody id="corpoTabela">
          </tbody>
        </table>
      </div>
    </div>

    <!-- Seção de Necessidades de MP -->
    <div id="necessidadesContainer" class="table-container" style="display: none; margin-top: 20px;">
      <div class="table-header">
        <span><i class="fas fa-shopping-cart"></i> Necessidades de MP das OPs Pendentes</span>
        <span id="contadorMPs">-</span>
      </div>

      <div id="loadingNecessidades" class="loading" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Processando necessidades de MP...</p>
      </div>

      <div style="max-height: 400px; overflow-y: auto;">
        <table id="tabelaNecessidades" style="display: none;">
          <thead>
            <tr>
              <th>Código MP</th>
              <th>Descrição</th>
              <th>Quantidade Total</th>
              <th>Unidade</th>
              <th>Grupo</th>
              <th>Família</th>
              <th>Preço Unit.</th>
              <th>Valor Total</th>
              <th>OPs Relacionadas</th>
            </tr>
          </thead>
          <tbody id="corpoTabelaNecessidades">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script type="module">
    // Importações do Firebase
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
    import {
      getFirestore,
      collection,
      getDocs,
      query,
      orderBy
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // Configuração do Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyBs7Qhf2HpjZKbdyQVhLGdHjXDKHjCJ8nE",
      authDomain: "banco-mrp.firebaseapp.com",
      projectId: "banco-mrp",
      storageBucket: "banco-mrp.appspot.com",
      messagingSenderId: "1234567890",
      appId: "1:1234567890:web:abcdef123456"
    };

    // Inicializar Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Variáveis globais
    let ordensProducao = [];
    let produtos = [];
    let ordensFiltradas = [];

    // Função para carregar dados
    async function carregarDados() {
      try {
        console.log('🔄 Carregando dados...');

        // Mostrar loading
        document.getElementById('loadingContainer').style.display = 'block';
        document.getElementById('noDataContainer').style.display = 'none';
        document.getElementById('tabelaOrdens').style.display = 'none';

        // Carregar ordens de produção e produtos
        const [ordensSnap, produtosSnap] = await Promise.all([
          getDocs(query(collection(db, "ordensProducao"), orderBy("numero", "desc"))),
          getDocs(collection(db, "produtos"))
        ]);

        // Processar dados
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`📊 Carregadas ${ordensProducao.length} ordens de produção`);
        console.log(`📦 Carregados ${produtos.length} produtos`);

        // Processar hierarquia
        processarHierarquia();

        // Exibir dados
        aplicarFiltros();

        // Mostrar seção de necessidades automaticamente
        document.getElementById('necessidadesContainer').style.display = 'block';

      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);

        document.getElementById('loadingContainer').style.display = 'none';
        document.getElementById('noDataContainer').style.display = 'block';
      }
    }

    // Função para processar hierarquia das ordens
    function processarHierarquia() {
      console.log('🔍 Processando hierarquia das ordens...');

      // Enriquecer cada ordem com informações do produto e hierarquia
      ordensProducao = ordensProducao.map(ordem => {
        const produto = produtos.find(p => p.id === ordem.produtoId);

        // Encontrar OP pai se existir
        let opPai = null;
        let produtoPai = null;
        let produtoRaiz = null;

        if (ordem.produtoPaiId && ordem.produtoPaiId !== ordem.produtoId) {
          // Tentar encontrar OP pai pelo produtoPaiId
          opPai = ordensProducao.find(op => op.produtoId === ordem.produtoPaiId);

          if (!opPai) {
            // Se não encontrou OP, tentar encontrar produto pai
            produtoPai = produtos.find(p => p.id === ordem.produtoPaiId);
          } else {
            produtoPai = produtos.find(p => p.id === opPai.produtoId);
          }
        }

        // Encontrar produto raiz (OP Pai original)
        if (ordem.opRaiz) {
          // Se tem campo opRaiz, usar ele
          const opRaiz = ordensProducao.find(op => op.id === ordem.opRaiz);
          if (opRaiz) {
            produtoRaiz = produtos.find(p => p.id === opRaiz.produtoId);
          }
        } else if (ordem.nivel === 0 || !ordem.produtoPaiId || ordem.produtoPaiId === ordem.produtoId) {
          // Se é nível 0 ou não tem pai, é o próprio produto raiz
          produtoRaiz = produto;
        } else if (ordem.produtoPaiId) {
          // Se tem produtoPaiId, buscar a OP pai de nível 0
          const buscarOPRaiz = (produtoPaiId) => {
            const opPaiCandidata = ordensProducao.find(op => op.produtoId === produtoPaiId);
            if (opPaiCandidata) {
              if (opPaiCandidata.nivel === 0 || !opPaiCandidata.produtoPaiId) {
                return opPaiCandidata;
              } else if (opPaiCandidata.produtoPaiId) {
                return buscarOPRaiz(opPaiCandidata.produtoPaiId);
              }
            }
            return null;
          };

          const opRaiz = buscarOPRaiz(ordem.produtoPaiId);
          if (opRaiz) {
            produtoRaiz = produtos.find(p => p.id === opRaiz.produtoId);
          }
        }

        // Se ainda não encontrou, usar o próprio produto
        if (!produtoRaiz) {
          produtoRaiz = produto;
        }

        // Determinar nível na hierarquia
        let nivel = ordem.nivel || 0;
        if (nivel === undefined || nivel === null) {
          // Se não tem nível definido, calcular baseado na hierarquia
          if (!ordem.produtoPaiId || ordem.produtoPaiId === ordem.produtoId) {
            nivel = 0; // É uma OP pai
          } else {
            nivel = 1; // Assumir nível 1 se tem pai mas não tem nível definido
          }
        }

        return {
          ...ordem,
          produto: produto || { codigo: 'N/A', descricao: 'Produto não encontrado', tipo: 'N/A' },
          opPai: opPai,
          produtoPai: produtoPai,
          produtoRaiz: produtoRaiz || produto, // Se não encontrou raiz, usar o próprio produto
          nivel: nivel,
          ehPai: nivel === 0 || !ordem.produtoPaiId || ordem.produtoPaiId === ordem.produtoId,
          temPai: !!(ordem.produtoPaiId && ordem.produtoPaiId !== ordem.produtoId)
        };
      });

      // Preencher dropdown de produtos pai
      preencherDropdownProdutosPai();

      console.log('✅ Hierarquia processada');
    }

    // Função para preencher dropdown de produtos pai
    function preencherDropdownProdutosPai() {
      const dropdown = document.getElementById('filtroProdutoPai');
      if (!dropdown) return;

      // Coletar apenas produtos de OPs Pai VERDADEIRAS (nível 0)
      // Estas são as OPs principais que você digita o código para gerar a explosão
      const produtosPai = new Set();

      ordensProducao.forEach(ordem => {
        // Apenas OPs Pai verdadeiras (nível 0 ou sem produtoPaiId)
        const ehOPPaiVerdadeira = ordem.nivel === 0 ||
                                  !ordem.produtoPaiId ||
                                  ordem.produtoPaiId === ordem.produtoId;

        if (ehOPPaiVerdadeira && ordem.produto && ordem.produto.codigo) {
          produtosPai.add(`${ordem.produto.codigo}|${ordem.produto.descricao || ''}|${ordem.produto.id}`);
        }
      });

      // Limpar dropdown
      dropdown.innerHTML = '<option value="">Todos os Produtos Pai</option>';

      // Adicionar opções ordenadas
      Array.from(produtosPai)
        .sort()
        .forEach(produtoInfo => {
          const [codigo, descricao, produtoId] = produtoInfo.split('|');
          const option = document.createElement('option');
          option.value = produtoId; // Usar ID do produto para filtro mais preciso
          option.textContent = `${codigo} - ${descricao}`;
          dropdown.appendChild(option);
        });

      console.log(`📦 ${produtosPai.size} produtos pai únicos encontrados (apenas nível 0)`);
    }

    // Função para aplicar filtros
    window.aplicarFiltros = function() {
      const filtroStatus = document.getElementById('filtroStatus').value.toLowerCase();
      const filtroNivel = document.getElementById('filtroNivel').value;
      const filtroTipo = document.getElementById('filtroTipo').value;
      const filtroProdutoPai = document.getElementById('filtroProdutoPai').value;
      const filtroBusca = document.getElementById('filtroBusca').value.toLowerCase();

      ordensFiltradas = ordensProducao.filter(ordem => {
        // Filtro por status - SE NÃO ESPECIFICADO, MOSTRAR APENAS PENDENTES
        const statusOrdem = (ordem.status || 'pendente').toLowerCase();
        if (filtroStatus) {
          if (statusOrdem !== filtroStatus) {
            return false;
          }
        } else {
          // Se não especificou filtro, mostrar apenas PENDENTES
          if (statusOrdem !== 'pendente') {
            return false;
          }
        }

        // Filtro por nível
        if (filtroNivel !== '' && ordem.nivel.toString() !== filtroNivel) {
          if (filtroNivel === '3' && ordem.nivel < 3) {
            return false;
          }
        }

        // Filtro por tipo de produto
        if (filtroTipo && ordem.produto.tipo !== filtroTipo) {
          return false;
        }

        // Filtro por produto pai (usando ID para maior precisão)
        if (filtroProdutoPai) {
          // Verificar se a OP pertence ao produto pai selecionado
          // Isso inclui a própria OP pai e todas as suas filhas
          const pertenceAoProdutoPai =
            // É a própria OP pai
            (ordem.nivel === 0 && ordem.produtoId === filtroProdutoPai) ||
            // É filha direta do produto pai
            (ordem.produtoPaiId === filtroProdutoPai) ||
            // É filha de uma OP que tem este produto como raiz
            (ordem.produtoRaiz?.id === filtroProdutoPai);

          if (!pertenceAoProdutoPai) {
            return false;
          }
        }

        // Filtro por busca
        if (filtroBusca) {
          const buscaTexto = `${ordem.numero} ${ordem.produto.codigo} ${ordem.produto.descricao} ${ordem.produtoRaiz?.codigo || ''} ${ordem.produtoPai?.codigo || ''}`.toLowerCase();
          if (!buscaTexto.includes(filtroBusca)) {
            return false;
          }
        }

        return true;
      });

      exibirOrdens();
      atualizarEstatisticas();
    };

    // Função para exibir ordens na tabela
    function exibirOrdens() {
      const corpoTabela = document.getElementById('corpoTabela');
      const loadingContainer = document.getElementById('loadingContainer');
      const noDataContainer = document.getElementById('noDataContainer');
      const tabelaOrdens = document.getElementById('tabelaOrdens');
      const contadorOrdens = document.getElementById('contadorOrdens');

      // Esconder loading
      loadingContainer.style.display = 'none';

      if (ordensFiltradas.length === 0) {
        noDataContainer.style.display = 'block';
        tabelaOrdens.style.display = 'none';
        contadorOrdens.textContent = '0 ordens encontradas';
        return;
      }

      // Mostrar tabela
      noDataContainer.style.display = 'none';
      tabelaOrdens.style.display = 'table';
      contadorOrdens.textContent = `${ordensFiltradas.length} ordem${ordensFiltradas.length !== 1 ? 'ns' : ''} encontrada${ordensFiltradas.length !== 1 ? 's' : ''}`;

      // Gerar linhas da tabela
      corpoTabela.innerHTML = ordensFiltradas.map(ordem => {
        const nivelBadge = getNivelBadge(ordem.nivel);
        const tipoBadge = getTipoBadge(ordem.produto.tipo);
        const statusBadge = getStatusBadge(ordem.status);

        const opPaiInfo = ordem.opPai ?
          `<div><strong>${ordem.opPai.numero}</strong></div>` :
          '<span style="color: #999;">-</span>';

        const produtoPaiInfo = ordem.produtoPai ?
          `<div><strong>${ordem.produtoPai.codigo}</strong></div><div style="font-size: 11px; color: #666;">${ordem.produtoPai.descricao}</div>` :
          '<span style="color: #999;">-</span>';

        const produtoRaizInfo = ordem.produtoRaiz ?
          `<div><strong style="color: #28a745;">${ordem.produtoRaiz.codigo}</strong></div><div style="font-size: 11px; color: #666;">${ordem.produtoRaiz.descricao}</div>` :
          '<span style="color: #999;">-</span>';

        const dataCriacao = ordem.dataCriacao ?
          (ordem.dataCriacao.toDate ? ordem.dataCriacao.toDate().toLocaleDateString('pt-BR') : new Date(ordem.dataCriacao).toLocaleDateString('pt-BR')) :
          '-';

        return `
          <tr>
            <td><strong>${ordem.numero}</strong></td>
            <td>${nivelBadge}</td>
            <td>
              <div><strong>${ordem.produto.codigo}</strong></div>
              <div style="font-size: 11px; color: #666;">${ordem.produto.descricao}</div>
            </td>
            <td>${tipoBadge}</td>
            <td style="text-align: right;">
              <strong>${(ordem.quantidade || 0).toLocaleString('pt-BR')} ${ordem.produto.unidade || 'UN'}</strong>
            </td>
            <td>${statusBadge}</td>
            <td>${opPaiInfo}</td>
            <td>${produtoPaiInfo}</td>
            <td>${produtoRaizInfo}</td>
            <td>${dataCriacao}</td>
          </tr>
        `;
      }).join('');
    }

    // Funções auxiliares para badges
    function getNivelBadge(nivel) {
      const classes = {
        0: 'nivel-0',
        1: 'nivel-1',
        2: 'nivel-2',
        3: 'nivel-3'
      };
      const classe = classes[nivel] || 'nivel-4plus';
      return `<span class="nivel-badge ${classe}">${nivel}</span>`;
    }

    function getTipoBadge(tipo) {
      const classes = {
        'PA': 'tipo-pa',
        'SP': 'tipo-sp',
        'MP': 'tipo-mp'
      };
      const classe = classes[tipo] || 'tipo-pa';
      return `<span class="tipo-badge ${classe}">${tipo || 'N/A'}</span>`;
    }

    function getStatusBadge(status) {
      const statusLower = (status || '').toLowerCase();
      const classes = {
        'pendente': 'status-pendente',
        'em produção': 'status-producao',
        'finalizada': 'status-finalizada'
      };
      const classe = classes[statusLower] || 'status-pendente';
      return `<span class="status-badge ${classe}">${status || 'Pendente'}</span>`;
    }

    // Função para atualizar estatísticas
    function atualizarEstatisticas() {
      const total = ordensFiltradas.length;
      const ordensPai = ordensFiltradas.filter(o => o.ehPai).length;

      // Contar produtos pai únicos disponíveis
      const produtosPaiUnicos = new Set();
      ordensProducao.forEach(ordem => {
        if (ordem.nivel === 0 || !ordem.produtoPaiId || ordem.produtoPaiId === ordem.produtoId) {
          produtosPaiUnicos.add(ordem.produtoId);
        }
      });

      document.getElementById('totalOrdens').textContent = total;
      document.getElementById('ordensPai').textContent = produtosPaiUnicos.size;
      document.getElementById('totalMPs').textContent = '-';
      document.getElementById('valorTotal').textContent = 'R$ -';
    }

    // Função para atualizar estatísticas das necessidades
    function atualizarEstatisticasNecessidades(necessidadesMP) {
      if (!necessidadesMP || necessidadesMP.size === 0) {
        document.getElementById('totalMPs').textContent = '0';
        document.getElementById('valorTotal').textContent = 'R$ 0,00';
        return;
      }

      const totalMPs = necessidadesMP.size;
      const valorTotal = Array.from(necessidadesMP.values())
        .reduce((total, necessidade) => total + (necessidade.valorTotal || 0), 0);

      document.getElementById('totalMPs').textContent = totalMPs;
      document.getElementById('valorTotal').textContent = `R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
    }

    // Função para limpar filtros
    window.limparFiltros = function() {
      document.getElementById('filtroStatus').value = '';
      document.getElementById('filtroNivel').value = '';
      document.getElementById('filtroTipo').value = '';
      document.getElementById('filtroProdutoPai').value = '';
      document.getElementById('filtroBusca').value = '';
      aplicarFiltros();
    };

    // Função para gerar lista de necessidades de MP
    window.gerarListaNecessidades = async function() {
      console.log('🛒 Gerando lista de necessidades de MP...');

      // Mostrar loading
      document.getElementById('loadingNecessidades').style.display = 'block';
      document.getElementById('necessidadesContainer').style.display = 'block';
      document.getElementById('tabelaNecessidades').style.display = 'none';

      try {
        // Carregar estruturas se ainda não carregou
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`📋 Estruturas carregadas: ${estruturas.length}`);

        // Mapa para acumular necessidades de MP
        const necessidadesMP = new Map();

        // Processar apenas OPs pendentes filtradas
        const opsPendentes = ordensFiltradas.filter(op =>
          (op.status || 'pendente').toLowerCase() === 'pendente'
        );

        console.log(`🎯 Processando ${opsPendentes.length} OPs pendentes`);

        // Processar cada OP pendente
        for (const op of opsPendentes) {
          console.log(`\n📊 Processando OP ${op.numero} - ${op.produto.codigo}`);

          // Verificar se tem materiais necessários
          if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
            console.log(`OP ${op.numero} pulada: sem materiais necessários`);
            continue;
          }

          console.log(`OP ${op.numero}: ${op.materiaisNecessarios.length} materiais necessários`);

          // Processar cada material da OP
          for (const material of op.materiaisNecessarios) {
            const produto = produtos.find(p => p.id === material.produtoId);
            if (!produto) {
              console.warn(`Produto não encontrado: ${material.produtoId}`);
              continue;
            }

            console.log(`Material: ${produto.codigo} (${produto.tipo}) - Qtd: ${material.quantidade}`);

            // Se é MP direto, adicionar
            if (produto.tipo === 'MP') {
              adicionarMP(produto, Number(material.quantidade) || 0, op.numero, necessidadesMP);
            }
            // Se é PA ou SP, explodir estrutura
            else if (produto.tipo === 'PA' || produto.tipo === 'SP') {
              const mpsEstrutura = await explodirEstrutura(
                produto.id,
                Number(material.quantidade) || 0,
                estruturas
              );

              for (const mpEstrutura of mpsEstrutura) {
                adicionarMP(mpEstrutura.produto, mpEstrutura.quantidade, op.numero, necessidadesMP);
              }
            }
          }
        }

        // Exibir resultados
        exibirNecessidadesMP(necessidadesMP);

        // Atualizar estatísticas
        atualizarEstatisticasNecessidades(necessidadesMP);

      } catch (error) {
        console.error('❌ Erro ao gerar necessidades:', error);
        document.getElementById('loadingNecessidades').style.display = 'none';
      }
    };

    // Função auxiliar para adicionar MP ao mapa
    function adicionarMP(produto, quantidade, numeroOP, necessidadesMP) {
      const chave = produto.id;

      if (!necessidadesMP.has(chave)) {
        necessidadesMP.set(chave, {
          produto: produto,
          quantidade: 0,
          ops: new Set(),
          valorTotal: 0
        });
      }

      const necessidade = necessidadesMP.get(chave);
      necessidade.quantidade += quantidade;
      necessidade.ops.add(numeroOP);
      necessidade.valorTotal = necessidade.quantidade * (Number(produto.precoUnitario) || 0);

      console.log(`➕ MP: ${produto.codigo} - Qtd: ${quantidade} (Total: ${necessidade.quantidade})`);
    }

    // Função para explodir estrutura (simplificada)
    async function explodirEstrutura(produtoId, quantidade, estruturas, nivel = 0) {
      if (nivel > 10) {
        console.warn('Nível máximo de recursão atingido');
        return [];
      }

      const produto = produtos.find(p => p.id === produtoId);
      if (!produto) return [];

      const estruturasProduto = estruturas.filter(e => e.produtoId === produtoId);
      if (estruturasProduto.length === 0) {
        console.log(`Produto ${produto.codigo} não tem estrutura`);
        return [];
      }

      const mps = [];

      for (const estrutura of estruturasProduto) {
        const componente = produtos.find(p => p.id === estrutura.componenteId);
        if (!componente) continue;

        const qtdComponente = quantidade * (Number(estrutura.quantidade) || 0);

        if (componente.tipo === 'MP') {
          mps.push({
            produto: componente,
            quantidade: qtdComponente
          });
        } else if (componente.tipo === 'PA' || componente.tipo === 'SP') {
          const subMps = await explodirEstrutura(componente.id, qtdComponente, estruturas, nivel + 1);
          mps.push(...subMps);
        }
      }

      return mps;
    }

    // Função para exibir necessidades de MP
    function exibirNecessidadesMP(necessidadesMP, tituloCustomizado = null) {
      const corpoTabela = document.getElementById('corpoTabelaNecessidades');
      const contadorMPs = document.getElementById('contadorMPs');
      const loadingNecessidades = document.getElementById('loadingNecessidades');
      const tabelaNecessidades = document.getElementById('tabelaNecessidades');
      const headerNecessidades = document.querySelector('#necessidadesContainer .table-header span');

      // Esconder loading
      loadingNecessidades.style.display = 'none';

      // Atualizar título se fornecido
      if (tituloCustomizado && headerNecessidades) {
        headerNecessidades.innerHTML = `<i class="fas fa-shopping-cart"></i> ${tituloCustomizado}`;
      }

      if (necessidadesMP.size === 0) {
        contadorMPs.textContent = 'Nenhuma necessidade de MP encontrada';
        return;
      }

      // Mostrar tabela
      tabelaNecessidades.style.display = 'table';
      contadorMPs.textContent = `${necessidadesMP.size} materiais MP necessários`;

      // Converter para array e ordenar por código
      const necessidadesArray = Array.from(necessidadesMP.values())
        .sort((a, b) => a.produto.codigo.localeCompare(b.produto.codigo));

      // Gerar linhas da tabela
      corpoTabela.innerHTML = necessidadesArray.map(necessidade => {
        const opsTexto = Array.from(necessidade.ops).slice(0, 5).join(', ') +
                        (necessidade.ops.size > 5 ? ` (+${necessidade.ops.size - 5} mais)` : '');

        return `
          <tr>
            <td><strong>${necessidade.produto.codigo}</strong></td>
            <td>${necessidade.produto.descricao || '-'}</td>
            <td style="text-align: right;">
              <strong>${necessidade.quantidade.toLocaleString('pt-BR', {minimumFractionDigits: 2})} ${necessidade.produto.unidade || 'UN'}</strong>
            </td>
            <td>${necessidade.produto.unidade || 'UN'}</td>
            <td>${necessidade.produto.grupo || '-'}</td>
            <td>${necessidade.produto.familia || '-'}</td>
            <td style="text-align: right;">
              R$ ${(Number(necessidade.produto.precoUnitario) || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}
            </td>
            <td style="text-align: right;">
              <strong>R$ ${necessidade.valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</strong>
            </td>
            <td style="font-size: 11px;">${opsTexto}</td>
          </tr>
        `;
      }).join('');

      // Calcular totais
      const valorTotalGeral = necessidadesArray.reduce((total, n) => total + n.valorTotal, 0);
      const totalOPs = new Set();
      necessidadesArray.forEach(n => n.ops.forEach(op => totalOPs.add(op)));

      console.log(`✅ Processamento concluído:`);
      console.log(`📦 ${necessidadesMP.size} materiais MP únicos`);
      console.log(`💰 Valor total: R$ ${valorTotalGeral.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`);
      console.log(`🏭 ${totalOPs.size} OPs envolvidas`);
    }

    // Função para gerar lista de necessidades por produto pai específico
    window.gerarListaPorProdutoPai = async function() {
      const filtroProdutoPai = document.getElementById('filtroProdutoPai').value;

      if (!filtroProdutoPai) {
        alert('⚠️ Selecione um produto pai no filtro para gerar a lista específica!');
        return;
      }

      console.log(`🎯 Gerando necessidades para produto pai: ${filtroProdutoPai}`);

      // Mostrar loading
      document.getElementById('loadingNecessidades').style.display = 'block';
      document.getElementById('necessidadesContainer').style.display = 'block';
      document.getElementById('tabelaNecessidades').style.display = 'none';

      try {
        // Carregar estruturas se ainda não carregou
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`📋 Estruturas carregadas: ${estruturas.length}`);

        // Mapa para acumular necessidades de MP
        const necessidadesMP = new Map();

        // Filtrar OPs pendentes do produto pai específico
        const opsProdutoPai = ordensFiltradas.filter(op => {
          const statusOrdem = (op.status || 'pendente').toLowerCase();
          const pertenceAoProdutoPai =
            // É a própria OP pai
            (op.nivel === 0 && op.produtoId === filtroProdutoPai) ||
            // É filha direta do produto pai
            (op.produtoPaiId === filtroProdutoPai) ||
            // É filha de uma OP que tem este produto como raiz
            (op.produtoRaiz?.id === filtroProdutoPai);

          return statusOrdem === 'pendente' && pertenceAoProdutoPai;
        });

        console.log(`🎯 Processando ${opsProdutoPai.length} OPs do produto pai ${filtroProdutoPai}`);

        // Processar cada OP do produto pai
        for (const op of opsProdutoPai) {
          console.log(`\n📊 Processando OP ${op.numero} - ${op.produto.codigo}`);

          // Verificar se tem materiais necessários
          if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
            console.log(`OP ${op.numero} pulada: sem materiais necessários`);
            continue;
          }

          console.log(`OP ${op.numero}: ${op.materiaisNecessarios.length} materiais necessários`);

          // Processar cada material da OP
          for (const material of op.materiaisNecessarios) {
            const produto = produtos.find(p => p.id === material.produtoId);
            if (!produto) {
              console.warn(`Produto não encontrado: ${material.produtoId}`);
              continue;
            }

            console.log(`Material: ${produto.codigo} (${produto.tipo}) - Qtd: ${material.quantidade}`);

            // Se é MP direto, adicionar
            if (produto.tipo === 'MP') {
              adicionarMP(produto, Number(material.quantidade) || 0, op.numero, necessidadesMP);
            }
            // Se é PA ou SP, explodir estrutura
            else if (produto.tipo === 'PA' || produto.tipo === 'SP') {
              const mpsEstrutura = await explodirEstrutura(
                produto.id,
                Number(material.quantidade) || 0,
                estruturas
              );

              for (const mpEstrutura of mpsEstrutura) {
                adicionarMP(mpEstrutura.produto, mpEstrutura.quantidade, op.numero, necessidadesMP);
              }
            }
          }
        }

        // Buscar nome do produto pai para o título
        const produtoPai = produtos.find(p => p.id === filtroProdutoPai);
        const nomeProdutoPai = produtoPai ? `${produtoPai.codigo} - ${produtoPai.descricao}` : filtroProdutoPai;

        // Exibir resultados com título específico
        exibirNecessidadesMP(necessidadesMP, `Necessidades de MP para Produto Pai: ${nomeProdutoPai}`);

        // Atualizar estatísticas
        atualizarEstatisticasNecessidades(necessidadesMP);

      } catch (error) {
        console.error('❌ Erro ao gerar necessidades por produto pai:', error);
        document.getElementById('loadingNecessidades').style.display = 'none';
      }
    };

    // Função para recarregar dados
    window.recarregarDados = function() {
      carregarDados();
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 Iniciando Tabela de Hierarquia de OPs...');
      carregarDados();
    });

  </script>
</body>
</html>
