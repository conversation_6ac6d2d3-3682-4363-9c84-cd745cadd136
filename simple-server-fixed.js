const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8000;

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  let filePath = '.' + req.url;
  
  // Default to index.html
  if (filePath === './') {
    filePath = './index.html';
  }
  
  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';
  
  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 - Arquivo não encontrado</h1>', 'utf-8');
      } else {
        res.writeHead(500);
        res.end('Erro interno do servidor: ' + error.code + ' ..\n');
      }
    } else {
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(PORT, () => {
  console.log(`🚀 Servidor rodando em http://127.0.0.1:${PORT}/`);
  console.log(`📁 Servindo arquivos de: ${__dirname}`);
  console.log(`⏰ Iniciado em: ${new Date().toLocaleString('pt-BR')}`);
  console.log('📋 Páginas disponíveis:');
  console.log(`   🏠 Principal: http://127.0.0.1:${PORT}/`);
  console.log(`   📋 Necessidades: http://127.0.0.1:${PORT}/controle_baixa_necessidades.html`);
  console.log(`   👥 Fornecedores: http://127.0.0.1:${PORT}/cadastro_fornecedores.html`);
  console.log(`   🏗️ Estruturas: http://127.0.0.1:${PORT}/estrutura_nova.html`);
  console.log(`   🏭 Produção: http://127.0.0.1:${PORT}/ordens_producao.html`);
  console.log('');
  console.log('💡 Para parar o servidor, pressione Ctrl+C');
});
