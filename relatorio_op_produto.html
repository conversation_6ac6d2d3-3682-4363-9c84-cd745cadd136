<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestão de Ordens de Produção</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.23/jspdf.plugin.autotable.min.js"></script>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    textarea {
      resize: vertical;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .groups-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .groups-table th,
    .groups-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .groups-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
      cursor: pointer;
      position: relative;
    }

    .groups-table th:hover {
      background-color: #e0e0e0;
    }

    .groups-table tr:hover {
      background-color: #f8f9fa;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
    }

    .edit-btn, .delete-btn {
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      cursor: pointer;
    }

    .edit-btn {
      background-color: #ffc107;
      color: #000;
    }

    .delete-btn {
      background-color: var(--danger-color);
      color: white;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .info-text {
      font-size: 12px;
      color: var(--text-secondary);
      margin-top: 4px;
    }

    .table-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .search-container {
      margin-bottom: 20px;
    }

    .search-container input {
      width: 300px;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .sort-indicator {
      margin-left: 5px;
      font-size: 12px;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      width: 80%;
      max-width: 1200px;
      border-radius: 8px;
      position: relative;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background-color: var(--success-color);
      transition: width 0.3s ease;
    }

    .tab-container {
      margin-bottom: 20px;
    }

    .tab-buttons {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .tab-button {
      padding: 10px 20px;
      background-color: var(--secondary-color);
      border: 1px solid var(--border-color);
      border-radius: 4px;
      cursor: pointer;
      color: var(--text-color);
      font-weight: 500;
    }

    .tab-button.active {
      background-color: var(--primary-color);
      color: white;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }
    /* Adicione isso no <style> existente */
  .modal-content {
    max-height: 80vh; /* 80% da altura da tela */
    overflow-y: auto; /* Barra de rolagem vertical quando necessário */
  }

  /* Para melhorar a aparência da barra de rolagem */
  .modal-content::-webkit-scrollbar {
    width: 8px;
  }
  .modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  .modal-content::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
  }

  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Gestão de Ordens de Produção</h1>
      <div class="button-group">
        <button id="newOrderButton" class="btn-primary">Nova Ordem Manual</button>
        <button id="orderFromSalesButton" class="btn-primary">Ordem de Pedido</button>
        <button id="generalReportButton" class="btn-primary">Relatório Geral</button>
        <button onclick="window.location.href='index.html'" class="btn-secondary">Voltar</button>
      </div>
    </div>

    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" onclick="switchTab('active')">Ordens Ativas</button>
        <button class="tab-button" onclick="switchTab('completed')">Ordens Concluídas</button>
      </div>

      <div class="form-container">
  <h2 class="form-title">Filtros</h2>
  
  <!-- Filtro por Código -->
  <div style="margin-bottom: 15px;">
    <label for="productCodeFilter">Código do Produto:</label>
    <input type="text" id="productCodeFilter" placeholder="Ex: P1001" 
           style="width: 300px;" oninput="filterOrders()">
  </div>

  <!-- Filtros em Linha -->
  <div style="display: flex; gap: 20px; margin-bottom: 15px;">
    <!-- Status -->
    <div style="flex: 1;">
      <label for="statusFilter">Status:</label>
      <select id="statusFilter" onchange="filterOrders()" style="width: 100%;">
        <option value="">Todos</option>
        <option value="Pendente">Pendente</option>
        <option value="Em Produção">Em Produção</option>
        <option value="Concluída">Concluída</option>
        <option value="Cancelada">Cancelada</option>
      </select>
    </div>

    <!-- Data Inicial -->
    <div style="flex: 1;">
      <label for="startDateFilter">Data Inicial:</label>
      <input type="date" id="startDateFilter" onchange="filterOrders()" style="width: 100%;">
    </div>

    <!-- Data Final -->
    <div style="flex: 1;">
      <label for="endDateFilter">Data Final:</label>
      <input type="date" id="endDateFilter" onchange="filterOrders()" style="width: 100%;">
    </div>
  </div>
</div>

      <div id="activeOrders" class="tab-content active">
        <div class="form-container" id="activeOrdersList"></div>
      </div>

      <div id="completedOrders" class="tab-content">
        <div class="form-container" id="completedOrdersList"></div>
      </div>
    </div>
  </div>

  <!-- Modal Nova Ordem Manual -->
  <div id="newOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('newOrderModal')">×</span>
      <div class="form-container">
        <h2 class="form-title">Nova Ordem de Produção</h2>
        <form id="newOrderForm" onsubmit="createManualOrder(event)">
          <div>
            <label for="productSearch" class="required">Produto:</label>
            <input type="text" id="productSearch" placeholder="Digite para buscar o produto...">
            <select id="productSelect" required>
              <option value="">Selecione o produto...</option>
            </select>
          </div>
          <div style="display: flex; gap: 15px; margin-top: 15px;">
            <div style="flex: 1;">
              <label for="quantity" class="required">Quantidade:</label>
              <input type="number" id="quantity" min="0.001" step="0.001" required>
            </div>
            <div style="flex: 1;">
              <label for="dueDate" class="required">Data de Entrega:</label>
              <input type="date" id="dueDate" required>
            </div>
          </div>
          <div style="margin-top: 15px;">
            <label for="priority" class="required">Prioridade:</label>
            <select id="priority" required>
              <option value="normal">Normal</option>
              <option value="alta">Alta</option>
              <option value="urgente">Urgente</option>
            </select>
          </div>
          <div style="margin-top: 15px;">
            <label for="observations">Observações:</label>
            <textarea id="observations" rows="3"></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" class="btn-success">Criar Ordem</button>
            <button type="button" class="btn-secondary" onclick="closeModal('newOrderModal')">Cancelar</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal Ordem de Pedido -->
  <div id="salesOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('salesOrderModal')">×</span>
      <div class="form-container">
        <h2 class="form-title">Criar Ordem de Pedido</h2>
        <div>
          <label>Pedido:</label>
          <select id="salesOrderSelect" onchange="loadSalesOrderDetails()">
            <option value="">Selecione o pedido...</option>
          </select>
        </div>
        <div id="salesOrderDetails" style="margin-top: 15px;"></div>
        <div class="form-actions">
          <button class="btn-success" onclick="createOrderFromSales()">Criar Ordem</button>
          <button class="btn-secondary" onclick="closeModal('salesOrderModal')">Cancelar</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Apontamento -->
  <div id="appointmentModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('appointmentModal')">×</span>
      <div class="form-container">
        <h2 class="form-title">Apontamento de Produção</h2>
        <div id="appointmentDetails"></div>
        <form id="appointmentForm" onsubmit="submitAppointment(event)">
          <div style="display: flex; gap: 15px; margin-top: 15px;">
            <div style="flex: 1;">
              <label for="producedQuantity" class="required">Quantidade Produzida:</label>
              <input type="number" id="producedQuantity" min="0.001" step="0.001" required>
            </div>
            <div style="flex: 1;">
              <label for="scrapQuantity">Quantidade de Refugo:</label>
              <input type="number" id="scrapQuantity" min="0" step="0.001" value="0">
            </div>
          </div>
          <div style="margin-top: 15px;">
            <label for="appointmentObservations">Observações:</label>
            <textarea id="appointmentObservations" rows="3"></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" class="btn-success">Confirmar Apontamento</button>
            <button type="button" class="btn-secondary" onclick="closeModal('appointmentModal')">Cancelar</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal Relatório Geral de Produção -->
  <div id="generalReportModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('generalReportModal')">×</span>
      <div class="form-container">
        <h2 class="form-title">Relatório Geral de Produção</h2>
        <div style="display: flex; gap: 20px; flex-wrap: wrap; margin-bottom: 20px;">
          <div style="flex: 1; min-width: 200px;">
            <label for="genStartDate" class="required">Data Inicial</label>
            <input type="date" id="genStartDate" onchange="generateGeneralReport()">
          </div>
          <div style="flex: 1; min-width: 200px;">
            <label for="genEndDate" class="required">Data Final</label>
            <input type="date" id="genEndDate" onchange="generateGeneralReport()">
          </div>
          <div style="flex: 1; min-width: 200px;">
            <label for="genStatusFilter">Status</label>
            <select id="genStatusFilter" onchange="generateGeneralReport()">
              <option value="">Todos</option>
              <option value="Pendente">Pendente</option>
              <option value="Em Produção">Em Produção</option>
              <option value="Concluída">Concluída</option>
              <option value="Cancelada">Cancelada</option>
            </select>
          </div>
        </div>
        <div class="search-container">
          <input type="text" id="genProductSearch" placeholder="Pesquisar por produto..." oninput="generateGeneralReport()">
        </div>
      </div>
      <div id="generalReportContent" class="form-container"></div>
      <div class="form-actions">
        <button class="btn-primary" onclick="exportGeneralReportToPDF()">Exportar para PDF</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc,
      setDoc,
      runTransaction,
      getDoc,
      getDocs,
      query,
      where,
      doc, 
      updateDoc,
      Timestamp,
      orderBy 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let pedidosVenda = [];
    let ordensProducao = [];
    let estoques = [];
    let currentAppointmentOp = null;
    let counterRef;

    window.onload = async function() {
      await loadInitialData();
      setupProductSearch();
      await loadActiveOrders();
      setupSalesOrderSelect();
      
      counterRef = doc(db, "contadores", "ordens");
      const counterDoc = await getDoc(counterRef);
      if (!counterDoc.exists()) {
        await setDoc(counterRef, { valor: 0 });
      }

      document.getElementById('newOrderButton').addEventListener('click', openNewOrderModal);
      document.getElementById('orderFromSalesButton').addEventListener('click', openOrderFromSalesModal);
      document.getElementById('generalReportButton').addEventListener('click', openGeneralReportModal);
    };

    async function generateOrderNumber() {
      const date = new Date();
      const year = date.getFullYear().toString().substr(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');

      try {
        const newCounter = await runTransaction(db, async (transaction) => {
          const counterDoc = await transaction.get(counterRef);
          if (!counterDoc.exists()) {
            throw new Error("Counter document does not exist!");
          }
          const newValue = counterDoc.data().valor + 1;
          transaction.update(counterRef, { valor: newValue });
          return newValue;
        });

        const sequence = newCounter.toString().padStart(4, '0');
        return `OP${year}${month}${sequence}`;
      } catch (error) {
        console.error("Erro ao gerar número da ordem:", error);
        throw error;
      }
    }

    async function loadInitialData() {
      try {
        const [produtosSnap, estruturasSnap, pedidosSnap, ordensSnap, estoquesSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "pedidosVenda")),
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "estoques"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        pedidosVenda = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        ordensProducao = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    function setupProductSearch() {
      const productSearch = document.getElementById('productSearch');
      productSearch.addEventListener('input', () => {
        const searchText = productSearch.value.toLowerCase();
        updateProductSelect(searchText);
      });
    }

    function updateProductSelect(searchText = '') {
      const productSelect = document.getElementById('productSelect');
      productSelect.innerHTML = '<option value="">Selecione o produto...</option>';

      produtos
        .filter(p => (p.tipo === 'PA' || p.tipo === 'SP') &&
                    (!searchText || 
                     p.codigo.toLowerCase().includes(searchText) ||
                     p.descricao.toLowerCase().includes(searchText)))
        .forEach(produto => {
          productSelect.innerHTML += `
            <option value="${produto.id}">
              ${produto.codigo} - ${produto.descricao} (${produto.tipo})
            </option>`;
        });
    }

    function setupSalesOrderSelect() {
      const select = document.getElementById('salesOrderSelect');
      select.innerHTML = '<option value="">Selecione o pedido...</option>';

      const pendingOrders = pedidosVenda.filter(p => p.status === 'Pendente');
      pendingOrders.forEach(pedido => {
        const produto = produtos.find(p => p.id === pedido.produtoId);
        select.innerHTML += `
          <option value="${pedido.id}">
            ${pedido.numero} - ${produto.codigo} - ${produto.descricao}
          </option>`;
      });
    }

    window.loadSalesOrderDetails = function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      const detailsDiv = document.getElementById('salesOrderDetails');
      
      if (!pedidoId) {
        detailsDiv.innerHTML = '';
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);

      detailsDiv.innerHTML = `
        <div>
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade:</strong> ${pedido.quantidade} ${produto.unidade}</p>
          <p><strong>Data de Entrega:</strong> ${new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString()}</p>
        </div>`;
    };

    window.createOrderFromSales = async function() {
      const pedidoId = document.getElementById('salesOrderSelect').value;
      if (!pedidoId) {
        alert('Selecione um pedido.');
        return;
      }

      const pedido = pedidosVenda.find(p => p.id === pedidoId);
      const produto = produtos.find(p => p.id === pedido.produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === pedido.produtoId);

      if (!estrutura) {
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }

      try {
        const parentOp = {
          numero: await generateOrderNumber(),
          pedidoId: pedido.id,
          produtoId: pedido.produtoId,
          quantidade: pedido.quantidade,
          dataEntrega: pedido.dataEntrega,
          status: 'Pendente',
          nivel: 0,
          prioridade: 'normal',
          dataCriacao: Timestamp.now()
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        await updateDoc(doc(db, "pedidosVenda", pedidoId), {
          status: 'Em Produção'
        });

        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');
        closeModal('salesOrderModal');
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    window.createManualOrder = async function(event) {
      event.preventDefault();

      const productId = document.getElementById('productSelect').value;
      const quantity = parseFloat(document.getElementById('quantity').value);
      const dueDate = document.getElementById('dueDate').value;
      const priority = document.getElementById('priority').value;
      const observations = document.getElementById('observations').value;

      if (!productId || !quantity || !dueDate) {
        alert('Preencha todos os campos obrigatórios.');
        return;
      }

      const estrutura = estruturas.find(e => e.produtoPaiId === productId);
      if (!estrutura) {
        const produto = produtos.find(p => p.id === productId);
        alert(`O produto ${produto.codigo} não possui estrutura cadastrada.`);
        return;
      }

      try {
        const parentOp = {
          numero: await generateOrderNumber(),
          produtoId: productId,
          produtoPaiId: productId,
          quantidade: quantity,
          dataEntrega: Timestamp.fromDate(new Date(dueDate)),
          status: 'Pendente',
          nivel: 0,
          prioridade: priority,
          observacoes: observations,
          dataCriacao: Timestamp.now()
        };

        const parentDocRef = await addDoc(collection(db, "ordensProducao"), parentOp);
        parentOp.id = parentDocRef.id;

        await explodeComponents(parentOp, estrutura);

        alert('Ordem de produção criada com sucesso!');
        closeModal('newOrderModal');
        document.getElementById('newOrderForm').reset();
        await loadActiveOrders();
      } catch (error) {
        console.error("Erro ao criar ordem de produção:", error);
        alert("Erro ao criar ordem de produção.");
      }
    };

    async function explodeComponents(parentOp, estrutura, level = 0) {
      const childOrders = [];
      const materialNeeds = [];
      const spNeeds = [];

      for (const componente of estrutura.componentes) {
        const produto = produtos.find(p => p.id === componente.componentId);
        const quantidadeNecessaria = parentOp.quantidade * componente.quantidade;
        
        const saldoDisponivel = await checkInventory(componente.componentId);
        const quantidadeReservada = Math.min(saldoDisponivel, quantidadeNecessaria);
        const necessidade = Math.max(0, quantidadeNecessaria - quantidadeReservada);

        const materialNeed = {
          produtoId: componente.componentId,
          quantidade: quantidadeNecessaria,
          saldoEstoque: saldoDisponivel + quantidadeReservada,
          quantidadeReservada: quantidadeReservada,
          necessidade: necessidade
        };

        if (quantidadeReservada > 0) {
          await updateInventoryReservation(componente.componentId, quantidadeReservada);
        }

        if (produto.tipo === 'SP') {
          spNeeds.push(materialNeed);
        } else {
          materialNeeds.push(materialNeed);
        }
      }

      if (materialNeeds.length > 0 || spNeeds.length > 0) {
        await updateDoc(doc(db, "ordensProducao", parentOp.id), {
          materiaisNecessarios: [...materialNeeds, ...spNeeds]
        });
      }

      return childOrders;
    }

    async function updateInventoryReservation(produtoId, quantidade) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      if (estoque) {
        const novoSaldoReservado = (estoque.saldoReservado || 0) + quantidade;
        await updateDoc(doc(db, "estoques", estoque.id), {
          saldoReservado: Math.max(0, novoSaldoReservado),
          ultimaMovimentacao: Timestamp.now()
        });
        estoque.saldoReservado = Math.max(0, novoSaldoReservado);
      }
    }

    async function checkInventory(produtoId) {
      const estoque = estoques.find(e => e.produtoId === produtoId);
      const saldoTotal = estoque ? estoque.saldo : 0;
      const saldoReservado = estoque ? (estoque.saldoReservado || 0) : 0;
      return saldoTotal - saldoReservado;
    }

    async function updateInventory(produtoId, quantidade, tipo) {
      const estoqueRef = estoques.find(e => e.produtoId === produtoId);
      
      if (estoqueRef) {
        const novoSaldo = tipo === 'entrada' ? 
          estoqueRef.saldo + quantidade : 
          estoqueRef.saldo - quantidade;

        await updateDoc(doc(db, "estoques", estoqueRef.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: Timestamp.now()
        });

        estoqueRef.saldo = novoSaldo;
      } else {
        const novoEstoque = {
          produtoId,
          saldo: tipo === 'entrada' ? quantidade : -quantidade,
          ultimaMovimentacao: Timestamp.now()
        };

        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        novoEstoque.id = docRef.id;
        estoques.push(novoEstoque);
      }
    }

    async function loadActiveOrders() {
  await loadInitialData(); // Certifique-se de que os dados estão atualizados
  filterOrders(); // Aplica os filtros atuais
}

    window.openAppointmentModal = function(opId) {
      currentAppointmentOp = ordensProducao.find(op => op.id === opId);
      const produto = produtos.find(p => p.id === currentAppointmentOp.produtoId);
      
      document.getElementById('appointmentDetails').innerHTML = `
        <div>
          <p><strong>Ordem:</strong> ${currentAppointmentOp.numero}</p>
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade Total:</strong> ${currentAppointmentOp.quantidade} ${produto.unidade}</p>
          <p><strong>Quantidade Já Produzida:</strong> ${currentAppointmentOp.quantidadeProduzida || 0} ${produto.unidade}</p>
        </div>`;
      
      document.getElementById('producedQuantity').value = '';
      document.getElementById('scrapQuantity').value = '0';
      document.getElementById('appointmentObservations').value = '';
      document.getElementById('appointmentModal').style.display = 'block';
    };

    window.submitAppointment = async function(event) {
      event.preventDefault();
      if (!currentAppointmentOp) return;

      const producedQuantity = parseFloat(document.getElementById('producedQuantity').value);
      const scrapQuantity = parseFloat(document.getElementById('scrapQuantity').value) || 0;
      const observations = document.getElementById('appointmentObservations').value;

      if (!producedQuantity) {
        alert('Por favor, informe a quantidade produzida.');
        return;
      }

      const totalProduzido = (currentAppointmentOp.quantidadeProduzida || 0) + producedQuantity;
      if (totalProduzido > currentAppointmentOp.quantidade) {
        alert('A quantidade total produzida não pode exceder a quantidade da ordem.');
        return;
      }

      try {
        if (currentAppointmentOp.materiaisNecessarios) {
          for (const material of currentAppointmentOp.materiaisNecessarios) {
            const consumoReal = (material.quantidade / currentAppointmentOp.quantidade) * producedQuantity;
            const reservaReal = (material.quantidadeReservada || 0) / currentAppointmentOp.quantidade * producedQuantity;

            await updateInventory(material.produtoId, consumoReal, 'saida');
            if (reservaReal > 0) {
              await updateInventoryReservation(material.produtoId, -reservaReal);
            }
          }
        }

        await updateInventory(currentAppointmentOp.produtoId, producedQuantity, 'entrada');

        let novoStatus = 'Em Produção';
        if (totalProduzido >= currentAppointmentOp.quantidade) {
          novoStatus = 'Concluída';
        }

        const opRef = doc(db, "ordensProducao", currentAppointmentOp.id);
        await updateDoc(opRef, {
          status: novoStatus,
          quantidadeProduzida: totalProduzido,
          quantidadeRefugo: (currentAppointmentOp.quantidadeRefugo || 0) + scrapQuantity,
          ultimoApontamento: {
            quantidade: producedQuantity,
            refugo: scrapQuantity,
            observacoes: observations,
            data: Timestamp.now()
          }
        });

        if (novoStatus === 'Concluída' && currentAppointmentOp.pedidoId) {
          await updateDoc(doc(db, "pedidosVenda", currentAppointmentOp.pedidoId), {
            status: 'Concluído'
          });
        }

        closeModal('appointmentModal');
        await loadActiveOrders();
        alert('Apontamento registrado com sucesso!');
      } catch (error) {
        console.error("Erro ao registrar apontamento:", error);
        alert("Erro ao registrar apontamento.");
      }
    };

    window.cancelOrder = async function(opId) {
      if (!confirm('Tem certeza que deseja cancelar esta ordem de produção?')) {
        return;
      }

      try {
        const ordem = ordensProducao.find(op => op.id === opId);

        if (ordem.materiaisNecessarios) {
          for (const material of ordem.materiaisNecessarios) {
            if (material.quantidadeReservada > 0) {
              await updateInventoryReservation(material.produtoId, -material.quantidadeReservada);
            }
          }
        }

        await updateDoc(doc(db, "ordensProducao", opId), {
          status: 'Cancelada',
          dataCancelamento: Timestamp.now()
        });

        if (ordem.pedidoId) {
          await updateDoc(doc(db, "pedidosVenda", ordem.pedidoId), {
            status: 'Pendente'
          });
        }

        await loadActiveOrders();
        alert('Ordem cancelada com sucesso!');
      } catch (error) {
        console.error("Erro ao cancelar ordem:", error);
        alert("Erro ao cancelar ordem.");
      }
    };

    window.openNewOrderModal = function() {
      document.getElementById('newOrderForm').reset();
      document.getElementById('newOrderModal').style.display = 'block';
    };

    window.openOrderFromSalesModal = function() {
      document.getElementById('salesOrderSelect').value = '';
      document.getElementById('salesOrderDetails').innerHTML = '';
      document.getElementById('salesOrderModal').style.display = 'block';
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
      if (modalId === 'appointmentModal') {
        currentAppointmentOp = null;
      }
    };

    window.switchTab = function(tab) {
      const tabs = ['active', 'completed'];
      tabs.forEach(t => {
        document.getElementById(`${t}Orders`).classList.toggle('active', t === tab);
        document.querySelector(`button[onclick="switchTab('${t}')"]`).classList.toggle('active', t === tab);
      });
    };

    window.filterOrders = function() {
  const productCode = document.getElementById('productCodeFilter').value.trim().toLowerCase();
  const status = document.getElementById('statusFilter').value;
  const startDate = document.getElementById('startDateFilter').value;
  const endDate = document.getElementById('endDateFilter').value;
  
  const activeList = document.getElementById('activeOrdersList');
  const completedList = document.getElementById('completedOrdersList');
  
  activeList.innerHTML = '';
  completedList.innerHTML = '';

  let filteredOrders = ordensProducao;
  
  // Aplicar filtro por código do produto
  if (productCode) {
    filteredOrders = filteredOrders.filter(op => {
      const produto = produtos.find(p => p.id === op.produtoId);
      return produto && produto.codigo.toLowerCase().includes(productCode);
    });
  }
  
  // Aplicar filtro por status
  if (status) {
    filteredOrders = filteredOrders.filter(op => 
      op.status.toLowerCase() === status.toLowerCase()
    );
  }
  
  // Aplicar filtro por data
  if (startDate || endDate) {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate + 'T23:59:59') : null;
    
    filteredOrders = filteredOrders.filter(op => {
      const opDate = new Date(op.dataEntrega.seconds * 1000);
      
      if (start && opDate < start) return false;
      if (end && opDate > end) return false;
      return true;
    });
  }

  // Ordenar as ordens (mantendo a mesma lógica original)
  filteredOrders.sort((a, b) => {
    if (a.nivel === b.nivel) {
      return b.dataCriacao.seconds - a.dataCriacao.seconds;
    }
    return a.nivel - b.nivel;
  });

  // Exibir as ordens filtradas
  for (const op of filteredOrders) {
    const produto = produtos.find(p => p.id === op.produtoId);
    const estoque = estoques.find(e => e.produtoId === op.produtoId);
    const saldoEstoque = estoque ? estoque.saldo : 0;

    const opElement = document.createElement('div');
    opElement.style.marginBottom = '15px';
    
    let componentesHtml = '';
    if (op.materiaisNecessarios) {
      componentesHtml = '<div style="margin-top: 10px;"><strong>Materiais necessários:</strong><ul style="list-style-type: none; padding-left: 20px;">';
      for (const material of op.materiaisNecessarios) {
        const materialProduto = produtos.find(p => p.id === material.produtoId);
        const disponibilidade = (material.saldoEstoque / material.quantidade * 100).toFixed(1);
        
        componentesHtml += `
          <li style="margin: 5px 0;">
            ${materialProduto.codigo} - ${materialProduto.descricao}
            <div class="info-text">
              Necessário: ${material.quantidade} ${materialProduto.unidade}
              | Estoque: ${material.saldoEstoque} ${materialProduto.unidade}
              | Falta: ${material.necessidade} ${materialProduto.unidade}
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${Math.min(100, disponibilidade)}%"></div>
            </div>
          </li>`;
      }
      componentesHtml += '</ul></div>';
    }

    const progress = op.quantidadeProduzida ? 
      (op.quantidadeProduzida / op.quantidade * 100).toFixed(1) : 0;

    opElement.innerHTML = `
      <div style="font-weight: bold;">
        ${op.numero} - ${produto.codigo} - ${produto.descricao}
        <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; margin-left: 10px; background-color: ${
          op.status === 'Pendente' ? '#FFC107' : 
          op.status === 'Em Produção' ? '#2196F3' : 
          op.status === 'Concluída' ? '#4CAF50' : '#f44336'
        }; color: ${op.status === 'Pendente' ? '#000' : '#fff'}">${op.status}</span>
      </div>
      <div style="margin-top: 5px;">
        <div>Quantidade: ${op.quantidade} ${produto.unidade}</div>
        <div>Entrega: ${new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()}</div>
        <div>Prioridade: ${op.prioridade || 'Normal'}</div>
        ${op.pedidoId ? `<div>Pedido: ${pedidosVenda.find(p => p.id === op.pedidoId)?.numero || ''}</div>` : ''}
        ${op.produtoPaiId ? `<div>Produto Pai: ${produtos.find(p => p.id === op.produtoPaiId)?.codigo || ''}</div>` : ''}
        <div class="info-text">Saldo em Estoque: ${saldoEstoque} ${produto.unidade}</div>
        ${op.quantidadeProduzida ? `
          <div style="margin-top: 5px;">
            Progresso: ${progress}%
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${progress}%"></div>
            </div>
          </div>
        ` : ''}
        ${componentesHtml}
      </div>
      <div class="action-buttons" style="margin-top: 10px;">
        ${op.status !== 'Concluída' && op.status !== 'Cancelada' ? `
          <button class="edit-btn" onclick="openAppointmentModal('${op.id}')">Apontar Produção</button>
          <button class="delete-btn" onclick="cancelOrder('${op.id}')">Cancelar Ordem</button>
        ` : ''}
      </div>
    `;

    if (op.status === 'Concluída' || op.status === 'Cancelada') {
      completedList.appendChild(opElement);
    } else {
      activeList.appendChild(opElement);
    }
  }
};

    // Relatório Geral
    window.openGeneralReportModal = function() {
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('genStartDate').value = '';
      document.getElementById('genEndDate').value = today;
      document.getElementById('genStatusFilter').value = '';
      document.getElementById('genProductSearch').value = '';
      generateGeneralReport();
      document.getElementById('generalReportModal').style.display = 'block';
    };

    window.generateGeneralReport = async function() {
      const startDate = document.getElementById('genStartDate').value;
      const endDate = document.getElementById('genEndDate').value;
      const statusFilter = document.getElementById('genStatusFilter').value;
      const searchText = document.getElementById('genProductSearch').value.trim().toLowerCase();
      const reportContent = document.getElementById('generalReportContent');

      // Validação das datas
      if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        reportContent.innerHTML = '<p style="text-align: center; color: var(--danger-color);">A data inicial não pode ser maior que a data final.</p>';
        return;
      }

      try {
        // Montar a consulta ao Firebase
        let ordensQuery = query(collection(db, "ordensProducao"));

        // Filtro por data
        if (startDate) {
          ordensQuery = query(ordensQuery, where("dataEntrega", ">=", Timestamp.fromDate(new Date(startDate + 'T00:00:00'))));
        }
        if (endDate) {
          ordensQuery = query(ordensQuery, where("dataEntrega", "<=", Timestamp.fromDate(new Date(endDate + 'T23:59:59'))));
        }

        // Filtro por status
        if (statusFilter) {
          ordensQuery = query(ordensQuery, where("status", "==", statusFilter));
        }

        // Executar a consulta inicial
        const ordensSnap = await getDocs(ordensQuery);
        let filteredOrders = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Filtro por produto (busca local após carregar do Firebase)
        if (searchText) {
          filteredOrders = filteredOrders.filter(op => {
            const produto = produtos.find(p => p.id === op.produtoId);
            return produto && (
              produto.codigo.toLowerCase().includes(searchText) || 
              produto.descricao.toLowerCase().includes(searchText)
            );
          });
        }

        // Cálculo das métricas gerais
        const totalOrders = filteredOrders.length;
        const statusCount = { Pendente: 0, 'Em Produção': 0, Concluída: 0, Cancelada: 0 };
        let totalSolicitado = 0;
        let totalProduzido = 0;
        let totalRefugo = 0;
        let ordensAtrasadas = 0;

        filteredOrders.forEach(op => {
          statusCount[op.status]++;
          totalSolicitado += op.quantidade;
          totalProduzido += op.quantidadeProduzida || 0;
          totalRefugo += op.quantidadeRefugo || 0;

          const entrega = new Date(op.dataEntrega.seconds * 1000);
          if (op.status === 'Concluída' && entrega < new Date() && 
              (!op.ultimoApontamento || new Date(op.ultimoApontamento.data.seconds * 1000) > entrega)) {
            ordensAtrasadas++;
          }
        });

        const taxaConclusao = totalSolicitado > 0 ? (totalProduzido / totalSolicitado * 100).toFixed(1) : 0;
        const taxaRefugo = totalProduzido > 0 ? (totalRefugo / totalProduzido * 100).toFixed(1) : 0;

        // Agrupamento por produto
        const ordersByProduct = {};
        filteredOrders.forEach(op => {
          const produtoId = op.produtoId;
          if (!ordersByProduct[produtoId]) {
            const produto = produtos.find(p => p.id === produtoId);
            ordersByProduct[produtoId] = { 
              produto, 
              ordens: [], 
              totalSolicitado: 0, 
              totalProduzido: 0, 
              totalRefugo: 0 
            };
          }
          ordersByProduct[produtoId].ordens.push(op);
          ordersByProduct[produtoId].totalSolicitado += op.quantidade;
          ordersByProduct[produtoId].totalProduzido += op.quantidadeProduzida || 0;
          ordersByProduct[produtoId].totalRefugo += op.quantidadeRefugo || 0;
        });

        // Agrupamento por status
        const ordersByStatus = {};
        filteredOrders.forEach(op => {
          if (!ordersByStatus[op.status]) {
            ordersByStatus[op.status] = [];
          }
          ordersByStatus[op.status].push(op);
        });

        // Geração do HTML do relatório
        reportContent.innerHTML = `
          <h2 class="form-title">Resumo Geral</h2>
          <p><strong>Total de Ordens:</strong> ${totalOrders}</p>
          <p><strong>Ordens por Status:</strong></p>
          <ul style="list-style-type: none; padding-left: 20px;">
            <li>Pendente: ${statusCount.Pendente}</li>
            <li>Em Produção: ${statusCount['Em Produção']}</li>
            <li>Concluída: ${statusCount.Concluída}</li>
            <li>Cancelada: ${statusCount.Cancelada}</li>
          </ul>
          <p><strong>Quantidade Solicitada:</strong> ${totalSolicitado.toFixed(3)}</p>
          <p><strong>Quantidade Produzida:</strong> ${totalProduzido.toFixed(3)}</p>
          <p><strong>Taxa de Conclusão:</strong> ${taxaConclusao}%</p>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${taxaConclusao}%"></div>
          </div>
          <p><strong>Quantidade de Refugo:</strong> ${totalRefugo.toFixed(3)}</p>
          <p><strong>Taxa de Refugo:</strong> ${taxaRefugo}%</p>
          <p><strong>Ordens Concluídas com Atraso:</strong> ${ordensAtrasadas}</p>

          <h2 class="form-title" style="margin-top: 30px;">Por Produto</h2>
          <table class="groups-table">
            <thead>
              <tr>
                <th>Código</th>
                <th>Descrição</th>
                <th>Total Solicitado</th>
                <th>Total Produzido</th>
                <th>Total Refugo</th>
                <th>Progresso</th>
              </tr>
            </thead>
            <tbody>
              ${Object.values(ordersByProduct).map(data => {
                const progress = data.totalSolicitado > 0 ? (data.totalProduzido / data.totalSolicitado * 100).toFixed(1) : 0;
                return `
                  <tr>
                    <td>${data.produto.codigo}</td>
                    <td>${data.produto.descricao}</td>
                    <td>${data.totalSolicitado.toFixed(3)} ${data.produto.unidade}</td>
                    <td>${data.totalProduzido.toFixed(3)} ${data.produto.unidade}</td>
                    <td>${data.totalRefugo.toFixed(3)} ${data.produto.unidade}</td>
                    <td>
                      ${progress}%
                      <div class="progress-bar" style="width: 100px;">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                      </div>
                    </td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <h2 class="form-title" style="margin-top: 30px;">Por Status</h2>
          ${Object.keys(ordersByStatus).map(status => `
            <div style="margin-bottom: 20px;">
              <h3 style="font-size: 16px; color: var(--primary-color);">${status} (${ordersByStatus[status].length} ordens)</h3>
              <table class="groups-table">
                <thead>
                  <tr>
                    <th>Número</th>
                    <th>Produto</th>
                    <th>Quantidade</th>
                    <th>Entrega</th>
                  </tr>
                </thead>
                <tbody>
                  ${ordersByStatus[status].map(op => {
                    const produto = produtos.find(p => p.id === op.produtoId);
                    return `
                      <tr>
                        <td>${op.numero}</td>
                        <td>${produto.codigo} - ${produto.descricao}</td>
                        <td>${op.quantidade} ${produto.unidade}</td>
                        <td>${new Date(op.dataEntrega.seconds * 1000).toLocaleDateString()}</td>
                      </tr>
                    `;
                  }).join('')}
                </tbody>
              </table>
            </div>
          `).join('')}
        `;

        if (totalOrders === 0) {
          reportContent.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">Nenhuma ordem encontrada com os filtros aplicados.</p>';
        }
      } catch (error) {
        console.error("Erro ao gerar relatório:", error);
        reportContent.innerHTML = '<p style="text-align: center; color: var(--danger-color);">Erro ao carregar o relatório. Tente novamente.</p>';
      }
    };

    window.exportGeneralReportToPDF = function() {
      const { jsPDF } = window.jspdf;
      const doc = new jsPDF();
      const reportContent = document.getElementById('generalReportContent');

      doc.setFontSize(16);
      doc.text('Relatório Geral de Produção', 10, 10);

      let y = 20;
      Array.from(reportContent.children).forEach(section => {
        if (section.tagName === 'H2') {
          doc.setFontSize(12);
          doc.text(section.textContent, 10, y);
          y += 10;
        } else if (section.tagName === 'P' || section.tagName === 'UL') {
          doc.setFontSize(10);
          doc.text(section.innerText, 10, y);
          y += 15;
        } else if (section.tagName === 'TABLE') {
          doc.autoTable({
            html: section,
            startY: y,
            theme: 'striped',
            styles: { fontSize: 8 },
            headStyles: { fillColor: [53, 74, 95] },
          });
          y = doc.lastAutoTable.finalY + 10;
        } else if (section.tagName === 'DIV') {
          doc.setFontSize(10);
          doc.text(section.innerText, 10, y);
          y += 20;
        }
      });

      doc.save('relatorio_geral_producao.pdf');
    };
  </script>
</body>
</html>