<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pedidos de Compra</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #2c3e50;
      --primary-hover: #34495e;
      --secondary-color: #3498db;
      --secondary-hover: #2980b9;
      --success-color: #27ae60;
      --success-hover: #229954;
      --warning-color: #f39c12;
      --warning-hover: #e67e22;
      --danger-color: #e74c3c;
      --danger-hover: #c0392b;
      --info-color: #17a2b8;
      --info-hover: #138496;
      --light-bg: #ecf0f1;
      --border-color: #bdc3c7;
      --text-color: #2c3e50;
      --text-secondary: #7f8c8d;
      --shadow: 0 4px 15px rgba(0,0,0,0.1);
      --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
      --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --gradient-header: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: var(--gradient-primary);
      min-height: 100vh;
      padding: 20px;
      color: var(--text-color);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: var(--shadow);
      overflow: hidden;
    }

    .header {
      background: var(--gradient-header);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header h1::before {
      content: "🛒";
      font-size: 32px;
    }

    h2 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 2px solid var(--border-color);
    }

    .main-content {
      padding: 30px;
    }

    /* Filtros modernos */
    .filters {
      background: var(--light-bg);
      padding: 25px;
      border: 1px solid var(--border-color);
      border-radius: 12px;
      margin-bottom: 25px;
      box-shadow: var(--shadow);
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      margin-bottom: 8px;
      color: var(--text-color);
      font-weight: 600;
      font-size: 14px;
    }

    .filter-group input, .filter-group select {
      padding: 12px 15px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: white;
    }

    .filter-group input:focus, .filter-group select:focus {
      outline: none;
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
      transform: translateY(-1px);
    }

    .button-group {
      display: flex;
      gap: 15px;
      justify-content: flex-end;
      flex-wrap: wrap;
    }

    /* Cards de resumo modernos */
    .summary-section {
      margin: 25px 0;
      padding: 25px;
      background: var(--light-bg);
      border-radius: 12px;
      box-shadow: var(--shadow);
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 20px;
    }

    .summary-item {
      padding: 20px;
      background: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
      border-left: 4px solid var(--secondary-color);
      transition: all 0.3s ease;
    }

    .summary-item:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-hover);
    }

    .summary-item h3 {
      margin: 0 0 10px 0;
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .summary-value {
      font-size: 24px;
      font-weight: 700;
      color: var(--text-color);
    }
    /* Tabela moderna */
    .table-container {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
      margin: 25px 0;
    }

    .orders-table {
      width: 100%;
      border-collapse: collapse;
    }

    .orders-table th, .orders-table td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    .orders-table th {
      background: var(--gradient-header);
      color: white;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .orders-table tbody tr {
      transition: all 0.3s ease;
    }

    .orders-table tbody tr:hover {
      background: #f8f9fa;
      transform: scale(1.01);
    }

    /* Status badges modernos */
    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-align: center;
      min-width: 100px;
      display: inline-block;
    }

    .status-pendente {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status-aberto {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }

    .status-aprovado {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-recebido {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status-cancelado {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    /* NOVOS STATUS DO PROCESSO DE COMPRAS */
    .status-rascunho {
      background: #f8f9fa;
      color: #6c757d;
      border: 1px solid #dee2e6;
    }

    .status-aguardando_aprovacao {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status-enviado_fornecedor {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }

    .status-confirmado_fornecedor {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status-em_producao {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status-enviado {
      background: #e2e3e5;
      color: #383d41;
      border: 1px solid #d6d8db;
    }

    /* Botões modernos */
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 6px;
    }
    .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; }
    .modal-content { background-color: #fff; margin: 5% auto; padding: 20px; width: 80%; max-width: 800px; border-radius: 8px; position: relative; max-height: 90vh; overflow-y: auto; border: 1px solid var(--border-color); }
    .close-button { position: absolute; right: 15px; top: 15px; font-size: 24px; cursor: pointer; color: var(--text-secondary); }
    .close-button:hover { color: var(--danger-color); }
    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; color: var(--text-secondary); font-weight: 500; font-size: 14px; }
    .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 8px 10px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px; }
    .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1); }
    .items-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
    .items-table th, .items-table td { padding: 12px 15px; border: 1px solid var(--border-color); text-align: left; }
    .items-table th { background-color: var(--secondary-color); font-weight: 600; color: var(--text-secondary); }
    .items-table tfoot td { font-weight: bold; background-color: var(--secondary-color); }
    .form-row { display: flex; gap: 15px; margin-bottom: 15px; }
    .form-col { flex: 1; }
    .pagination { display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px; }
    .pagination button { padding: 5px 10px; background: white; border: 1px solid var(--border-color); color: var(--text-color); border-radius: 4px; }
    .pagination button.active { background: var(--primary-color); color: white; border-color: var(--primary-color); }
    .pagination button:hover:not(.active) { background: #f8f9fa; }
    .quotation-details { padding: 15px; background-color: var(--secondary-color); border-radius: 4px; margin-bottom: 15px; }

    /* Workflow de Aprovação Moderno */
    .approval-workflow {
      background: var(--light-bg);
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 25px;
      box-shadow: var(--shadow);
      border-left: 4px solid var(--warning-color);
    }

    .approval-workflow h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .approval-workflow h3::before {
      content: "✅";
      font-size: 20px;
    }

    .approval-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .approval-step {
      display: flex;
      align-items: center;
      padding: 15px;
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
      position: relative;
    }

    .approval-step:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .approval-step .status-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      color: white;
    }

    .status-pending {
      background: var(--warning-color);
    }

    .status-pending::after {
      content: "⏳";
    }

    .status-approved {
      background: var(--success-color);
    }

    .status-approved::after {
      content: "✓";
    }

    .status-rejected {
      background: var(--danger-color);
    }

    .status-rejected::after {
      content: "✗";
    }

    .approval-info {
      flex: 1;
    }

    .approval-info .role {
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 5px;
    }

    .approval-info .user {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .approval-info .date {
      font-size: 11px;
      color: var(--text-secondary);
      margin-top: 5px;
    }

    /* Alerta de orçamento */
    .budget-alert {
      background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
      font-weight: 600;
      box-shadow: var(--shadow);
      animation: slideIn 0.3s ease;
    }

    .budget-alert::before {
      content: "⚠️ ";
      font-size: 18px;
      margin-right: 10px;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .delivery-timeline {
      margin-top: 15px;
      padding: 15px;
      background-color: var(--secondary-color);
      border-radius: 4px;
    }
    .timeline-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .timeline-date {
      min-width: 100px;
      font-weight: bold;
    }
    .progress-container {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }
    .progress-bar {
      background-color: #e0e0e0;
      height: 20px;
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
      flex: 1;
    }
    .progress-bar > div {
      height: 100%;
      background-color: var(--success-color);
    }
    #atrasadoProgress > div {
      background-color: var(--danger-color);
    }
    #parcialProgress > div {
      background-color: var(--warning-color);
    }

    /* Added Legend Styles */
    .status-legend {
      display: flex;
      gap: 20px;
      margin-bottom: 15px;
      padding: 10px;
      background: var(--secondary-color);
      border-radius: 4px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }

    .status-dot.pendente { background-color: #FFC107; }
    .status-dot.aberto { background-color: #2196F3; }
    .status-dot.aprovado { background-color: #4CAF50; }
    .status-dot.recebido { background-color: #9C27B0; }
    .status-dot.cancelado { background-color: #F44336; }

    /* Status específicos para itens do pedido */
    .status-parcial {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status-entregue {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    /* Estilos para o modal de detalhes */
    .modal-content h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.1em;
    }

    .modal-content .items-table {
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-radius: 8px;
      overflow: hidden;
    }

    .modal-content .items-table th {
      background: linear-gradient(135deg, #6c757d, #495057);
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 11px;
      letter-spacing: 0.5px;
    }

    .modal-content .items-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .modal-content .btn {
      transition: all 0.2s ease;
    }

    .modal-content .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    /* Estilos para indicar atraso */
    .dias-restantes {
      font-weight: bold;
      padding: 4px 8px;
      border-radius: 4px;
      text-align: center;
      min-width: 80px;
      display: inline-block;
    }

    .dias-restantes.atrasado {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ef5350;
    }

    .dias-restantes.vence-hoje {
      background-color: #fff3e0;
      color: #ef6c00;
      border: 1px solid #ff9800;
    }

    .dias-restantes.proximo-vencimento {
      background-color: #fff8e1;
      color: #f57c00;
      border: 1px solid #ffb74d;
    }

    .dias-restantes.no-prazo {
      background-color: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #66bb6a;
    }

    /* Melhorar botões de ações */
    .actions {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .btn-action {
      min-width: 32px;
      height: 32px;
      padding: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 12px;
      position: relative;
    }

    .btn-action:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .btn-action i {
      font-size: 14px;
    }

    /* Tooltip para botões */
    .btn-action[title]:hover::after {
      content: attr(title);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: #333;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      white-space: nowrap;
      z-index: 1000;
      margin-bottom: 4px;
    }

    /* Cores específicas para botões de ação */
    .btn-action.btn-info {
      background: linear-gradient(135deg, #17a2b8, #138496);
      color: white;
    }

    .btn-action.btn-primary {
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
    }

    .btn-action.btn-secondary {
      background: linear-gradient(135deg, #6c757d, #545b62);
      color: white;
    }

    .btn-action.btn-success {
      background: linear-gradient(135deg, #28a745, #1e7e34);
      color: white;
    }

    .btn-action.btn-warning {
      background: linear-gradient(135deg, #ffc107, #d39e00);
      color: #212529;
    }

    .btn-action.btn-danger {
      background: linear-gradient(135deg, #dc3545, #bd2130);
      color: white;
    }

    /* Responsividade para ações */
    @media (max-width: 768px) {
      .actions {
        flex-direction: column;
        gap: 2px;
      }

      .btn-action {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
      }

      .btn-action i {
        margin-right: 8px;
      }
    }

    /* Animação para alertas de atraso */
    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0.5; }
    }

    .alertas-container {
      animation: none;
    }

    .alerta-badge {
      transition: all 0.3s ease;
    }

    .alerta-badge:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- ⚠️ AVISO TEMPORÁRIO -->
    <div style="background: linear-gradient(135deg, #ff9800, #f57c00); color: white; padding: 12px 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(255,152,0,0.3);">
      <div style="display: flex; align-items: center; gap: 12px;">
        <i class="fas fa-tools" style="font-size: 20px;"></i>
        <div>
          <strong>🔧 VERSÃO EM DESENVOLVIMENTO</strong>
          <p style="margin: 4px 0 0 0; font-size: 14px; opacity: 0.9;">
            Sistema funcionando com segurança básica. Melhorias avançadas serão implementadas em breve.
          </p>
        </div>
      </div>
    </div>

    <div class="header">
      <h1>Pedidos de Compra</h1>
      <div class="header-actions">
        <!-- Alertas de Atraso -->
        <div id="alertasAtraso" class="alertas-container" style="display: none; margin-right: 15px;">
          <div class="alerta-badge" style="background: #ff4444; color: white; padding: 8px 12px; border-radius: 20px; display: flex; align-items: center; gap: 8px; cursor: pointer;" onclick="showDelayedOrders()">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="contadorAtrasos">0</span>
            <span>pedidos em atraso</span>
          </div>
        </div>

        <button class="btn btn-success" onclick="openNewOrderModal()">
          <i class="fas fa-plus"></i> Novo Pedido
        </button>
        <button class="btn btn-info" onclick="openFromQuotationModal()">
          <i class="fas fa-file-invoice"></i> Pedido de Cotação
        </button>
        <a href="gestao_compras_integrada.html" class="btn btn-warning" title="Gestão Integrada - Aprovações">
          <i class="fas fa-tasks"></i> Gestão Integrada
        </a>
        <a href="dashboard_pedidos.html" class="btn btn-info" title="Dashboard de Acompanhamento" target="_blank">
          <i class="fas fa-chart-pie"></i> Dashboard
        </a>
        <a href="cadastro_centro_custo.html" class="btn btn-secondary" target="_blank" title="Gerenciar Centros de Custo">
          <i class="fas fa-building"></i> Centros de Custo
        </a>
        <button class="btn btn-primary" onclick="exportToExcel()">
          <i class="fas fa-file-excel"></i> Exportar Excel
        </button>
        <button class="btn btn-danger" onclick="verificarPedidosDuplicados()" title="Verificar e remover pedidos duplicados">
          <i class="fas fa-search"></i> Verificar Duplicados
        </button>
        <button class="btn btn-secondary" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- ⚠️ AVISO TEMPORÁRIO -->
      <div style="background: linear-gradient(135deg, #4caf50, #45a049); color: white; padding: 12px 20px; border-radius: 8px; margin-bottom: 20px;">
        <div style="display: flex; align-items: center; gap: 12px;">
          <i class="fas fa-shield-alt" style="font-size: 20px;"></i>
          <div>
            <strong>✅ SISTEMA SEGURO & OTIMIZADO</strong>
            <p style="margin: 5px 0 0 0; font-size: 14px;">Melhorias implementadas: validações rigorosas, controle de permissões, auditoria básica e pedidos aprovados ocultos por padrão.</p>
          </div>
        </div>
      </div>

      <!-- Alerta de orçamento -->
      <div id="budgetAlert" class="budget-alert">
        Este pedido excede o limite de orçamento mensal!
      </div>

      <!-- Informação sobre aprovações -->
      <div class="alert alert-info" style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #17a2b8;">
        <i class="fas fa-info-circle"></i>
        <strong>📋 Aprovações Centralizadas:</strong>
        Todas as aprovações de pedidos são realizadas exclusivamente na tela
        <a href="gestao_compras_integrada.html" style="color: #0c5460; font-weight: bold;">Gestão de Compras Integrada</a>.
        Use o botão "📋 Ir para Gestão Integrada" para aprovar pedidos.
      </div>

    <!-- Componente: Tracking de Entregas -->
    <div class="delivery-tracking" style="background-color: var(--secondary-color); padding: 15px; border-radius: 4px; margin-bottom: 15px;">
      <h3>Tracking de Entregas</h3>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
        <div class="tracking-card" style="background: white; padding: 15px; border-radius: 4px; border: 1px solid var(--border-color);">
          <h4 style="color: var(--success-color); margin: 0 0 10px 0;">No Prazo</h4>
          <div id="noPrazoCount" style="font-size: 24px; font-weight: bold;">0</div>
          <div style="font-size: 12px; color: var(--text-secondary);">pedidos</div>
        </div>
        <div class="tracking-card" style="background: white; padding: 15px; border-radius: 4px; border: 1px solid var(--border-color);">
          <h4 style="color: var(--warning-color); margin: 0 0 10px 0;">Atrasados</h4>
          <div id="atrasadoCount" style="font-size: 24px; font-weight: bold;">0</div>
          <div style="font-size: 12px; color: var(--text-secondary);">pedidos</div>
        </div>
        <div class="tracking-card" style="background: white; padding: 15px; border-radius: 4px; border: 1px solid var(--border-color);">
          <h4 style="color: var(--primary-color); margin: 0 0 10px 0;">Próximos do Vencimento</h4>
          <div id="proximosVencimentoCount" style="font-size: 24px; font-weight: bold;">0</div>
          <div style="font-size: 12px; color: var(--text-secondary);">pedidos (próximos 7 dias)</div>
        </div>
      </div>
    </div>

    <div class="filters">
      <div class="filter-row">
        <div class="filter-group">
          <label>Período</label>
          <input type="date" id="startDate">
        </div>
        <div class="filter-group">
          <label>Até</label>
          <input type="date" id="endDate">
        </div>
        <div class="filter-group">
          <label>Fornecedor</label>
          <select id="supplierFilter">
            <option value="">Todos</option>
          </select>
        </div>
        <div class="filter-group">
          <label>Número:</label>
          <input type="text" id="numeroFilter" placeholder="Número do pedido">
        </div>
        <div class="filter-group">
          <label>Observações:</label>
          <input type="text" id="observacaoFilter" placeholder="Observações">
        </div>
        <div class="filter-group">
          <label>Status</label>
          <select id="statusFilter">
            <option value="">Todos</option>
            <option value="PENDENTE">Pendente</option>
            <option value="ABERTO">Aberto</option>
            <option value="APROVADO">Aprovado</option>
            <option value="RECEBIDO">Recebido</option>
            <option value="CANCELADO">Cancelado</option>
          </select>
        </div>
        <div class="filter-group">
          <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
            <input type="checkbox" id="ocultarRecebidos" checked onchange="applyFilters()">
            <span>Ocultar pedidos recebidos</span>
          </label>
        </div>
        <div class="filter-group">
          <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
            <input type="checkbox" id="ocultarAprovados" onchange="applyFilters()">
            <span>Ocultar pedidos aprovados</span>
          </label>
        </div>
      </div>
      <div class="button-group">
        <button onclick="applyFilters()">Aplicar Filtros</button>
        <button onclick="resetFilters()">Limpar Filtros</button>
      </div>
    </div>

    <div class="summary-section">
      <div class="summary-grid">
        <div class="summary-item">
          <h3>Total de Pedidos</h3>
          <div class="summary-value" id="totalPedidos">0</div>
        </div>
        <div class="summary-item">
          <h3>Valor Total</h3>
          <div class="summary-value" id="valorTotal">R$ 0,00</div>
        </div>
        <div class="summary-item">
          <h3>Pedidos em Aberto</h3>
          <div class="summary-value" id="pedidosAbertos">0</div>
        </div>
        <div class="summary-item">
          <h3>Média por Pedido</h3>
          <div class="summary-value" id="mediaPedido">R$ 0,00</div>
        </div>
        <div class="summary-item">
          <h3>Pedidos Atrasados</h3>
          <div class="summary-value" id="pedidosAtrasados">0</div>
        </div>
        <div class="summary-item">
          <h3>Pedidos a Vencer</h3>
          <div class="summary-value" id="pedidosAVencer">0</div>
        </div>
      </div>
    </div>

      <div class="table-container">
        <table class="orders-table">
          <thead>
            <tr>
              <th>Número</th>
              <th>Data</th>
              <th>Fornecedor</th>
              <th>Valor Total</th>
              <th>Status</th>
              <th>Data Entrega Prevista</th>
              <th>Dias Restantes</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="ordersTableBody"></tbody>
        </table>
      </div>

    <div class="pagination" id="pagination"></div>

    <!-- Novo componente: Timeline de Entrega -->
    <div class="delivery-timeline">
      <h3>Timeline de Entrega</h3>
      <div id="deliveryTimeline">
        <!-- Preenchido dinamicamente -->
      </div>
    </div>

    <div class="progress-container">
      <div class="progress-bar" id="noPrazoProgress">
        <div></div>
      </div>
      <div class="progress-bar" id="atrasadoProgress">
        <div></div>
      </div>
      <div class="progress-bar" id="parcialProgress">
        <div></div>
      </div>
    </div>
    <div class="progress-container">
      <p id="noPrazoCount"></p>
      <p id="atrasadoCount"></p>
      <p id="parcialCount"></p>
    </div>

    <!-- Status Legend -->
    <div class="status-legend">
      <div class="legend-item">
        <span class="status-dot pendente"></span>
        <span>Pendente</span>
      </div>
      <div class="legend-item">
        <span class="status-dot aberto"></span>
        <span>Aberto</span>
      </div>
      <div class="legend-item">
        <span class="status-dot aprovado"></span>
        <span>Aprovado</span>
      </div>
      <div class="legend-item">
        <span class="status-dot recebido"></span>
        <span>Recebido</span>
      </div>
      <div class="legend-item">
        <span class="status-dot cancelado"></span>
        <span>Cancelado</span>
      </div>
    </div>
    </div> <!-- Fecha main-content -->
  </div>

  <!-- Modal Novo Pedido -->
  <div id="orderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('orderModal')">×</span>
      <h2>Novo Pedido de Compra</h2>
      <form id="orderForm" onsubmit="handleOrder(event)">
        <div class="form-row">
          <div class="form-col">
            <label>Fornecedor:</label>
            <select id="supplierSelect" required onchange="checkSupplierLimits()">
              <option value="">Selecione o fornecedor...</option>
            </select>
          </div>
          <div class="form-col">
            <label>Condição de Pagamento:</label>
            <select id="paymentTerms" required>
              <option value="AVISTA">À Vista</option>
              <option value="7DIAS">7 Dias</option>
              <option value="15DIAS">15 Dias</option>
              <option value="30DIAS">30 Dias</option>
              <option value="45DIAS">45 Dias</option>
              <option value="60DIAS">60 Dias</option>
            </select>
          </div>
        </div>

        <!-- Novo campo: Data de Entrega Prevista -->
        <div class="form-row">
          <div class="form-col">
            <label>Data de Entrega Prevista:</label>
            <input type="date" id="expectedDeliveryDate" required>
          </div>
          <div class="form-col">
            <label>Centro de Custo:</label>
            <select id="costCenter" required>
              <option value="">Selecione um centro de custo...</option>
              <!-- Carregado dinamicamente dos centros de custo cadastrados -->
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Itens:</label>
          <table class="items-table">
            <thead>
              <tr>
                <th>Código</th>
                <th>Descrição</th>
                <th>Quantidade</th>
                <th>Unidade</th>
                <th>Valor Unit.</th>
                <th>Total</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="itemsTableBody"></tbody>
            <tfoot>
              <tr>
                <td colspan="5" style="text-align: right;"><strong>Total Geral:</strong></td>
                <td id="totalGeral">R$ 0,00</td>
                <td></td>
              </tr>
            </tfoot>
          </table>
          <button type="button" onclick="addItem()">Adicionar Item</button>
        </div>

        <div class="form-group">
          <label for="observacoes">Observações:</label>
          <textarea id="observacoes" rows="3"></textarea>
        </div>

        <!-- Novos campos: Anexos -->
        <div class="form-group">
          <label>Anexos:</label>
          <input type="file" id="attachments" multiple>
        </div>

        <button type="submit">Criar Pedido</button>
      </form>
    </div>
  </div>

  <!-- Modal Pedido de Cotação -->
  <div id="quotationOrderModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('quotationOrderModal')">×</span>
      <h2>Criar Pedido de Cotação</h2>
      <div class="form-group">
        <label>Cotação:</label>
        <select id="quotationSelect" onchange="loadQuotationDetails()">
          <option value="">Selecione a cotação...</option>
        </select>
      </div>
      <div id="quotationDetails" class="quotation-details"></div>
      <div class="form-group">
        <label>Fornecedor:</label>
        <select id="quotationSupplierSelect" onchange="updateQuotationItems()">
          <option value="">Selecione o fornecedor...</option>
        </select>
      </div>
      <div id="quotationItemsContainer"></div>
      <button onclick="createOrderFromQuotation()">Criar Pedido</button>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, addDoc, getDocs, query, where, orderBy, Timestamp, doc, updateDoc, deleteDoc, getDoc, limit, arrayUnion } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let fornecedores = [];
    let produtos = [];
    let cotacoes = [];
    let pedidosCompra = [];
    let centrosCusto = [];
    let setores = [];
    let filteredPedidos = [];
    // ✅ AUTENTICAÇÃO MELHORADA - Versão temporária
    let currentUser = JSON.parse(localStorage.getItem('currentUser')) || {
        nome: 'Sistema',
        id: 'sistema',
        uid: 'sistema',
        nivel: 9 // Super usuário temporário
    };
    let userPermissions = [];
    const itemsPerPage = 20;
    let currentPage = 1;

    window.applyFilters = function() {
      const startDate = new Date(document.getElementById('startDate').value);
      const endDate = new Date(document.getElementById('endDate').value);
      if (!startDate || !endDate) {
        alert('Por favor selecione um período válido');
        return;
      }
      endDate.setHours(23, 59, 59);
      const fornecedorId = document.getElementById('supplierFilter').value;
      const numero = document.getElementById('numeroFilter').value;
      const observacao = document.getElementById('observacaoFilter').value;
      const status = document.getElementById('statusFilter').value;
      const ocultarRecebidos = document.getElementById('ocultarRecebidos').checked;
      const ocultarAprovados = document.getElementById('ocultarAprovados').checked;

      // NOVO: Verificar se deve ocultar cancelados (padrão: SIM)
      const ocultarCancelados = document.getElementById('ocultarCancelados') ?
        document.getElementById('ocultarCancelados').checked : true;

      filteredPedidos = pedidosCompra.filter(pedido => {
        const pedidoDate = pedido.dataCriacao && typeof pedido.dataCriacao.seconds === 'number' ? new Date(pedido.dataCriacao.seconds * 1000) : null;
        const matchesDate = pedidoDate && pedidoDate >= startDate && pedidoDate <= endDate;
        const matchesFornecedor = !fornecedorId || pedido.fornecedorId === fornecedorId;
        const matchesNumero = !numero || pedido.numero.includes(numero);
        const matchesObservacao = !observacao || (pedido.observacoes || "").includes(observacao);
        const matchesStatus = !status || pedido.status === status;

        // ✅ Ocultar pedidos recebidos se a opção estiver marcada
        const shouldHideReceived = ocultarRecebidos && pedido.status === 'RECEBIDO';

        // ✅ Ocultar pedidos aprovados se a opção estiver marcada
        const shouldHideApproved = ocultarAprovados && pedido.status === 'APROVADO';

        // 🆕 OCULTAR PEDIDOS CANCELADOS POR PADRÃO
        const shouldHideCancelled = ocultarCancelados && (
          pedido.status === 'CANCELADO' ||
          pedido.status === 'CANCELADA' ||
          pedido.status === 'REJEITADO'
        );

        return matchesDate && matchesFornecedor && matchesNumero && matchesObservacao && matchesStatus &&
               !shouldHideReceived && !shouldHideApproved && !shouldHideCancelled;
      });

      currentPage = 1;
      loadOrders();
      updateSummary();
      updatePagination();
    };

    // Função para resetar filtros
    window.resetFilters = function() {
      // Resetar campos de filtro
      document.getElementById('startDate').value = '';
      document.getElementById('endDate').value = '';
      document.getElementById('supplierFilter').value = '';
      document.getElementById('numeroFilter').value = '';
      document.getElementById('observacaoFilter').value = '';
      document.getElementById('statusFilter').value = '';
      document.getElementById('ocultarRecebidos').checked = true; // ✅ Manter ocultar recebidos como padrão
      document.getElementById('ocultarAprovados').checked = false; // ✅ Mostrar pedidos aprovados por padrão

      // Reconfigurar datas padrão
      setupDateFilters();

      // Aplicar filtros limpos
      applyFilters();
    };

    // Novas variáveis para controle de orçamento (do seu código)
    const LIMITE_ORCAMENTO_MENSAL = 100000; // R$ 100.000,00
    let orcamentoUtilizado = 0;
    let condicoesPagamento = [];


    document.addEventListener('DOMContentLoaded', async function() {
      try {
        console.log("🔐 Verificando autenticação...");

        // ✅ VERIFICAÇÃO BÁSICA DE AUTENTICAÇÃO
        if (!currentUser || !currentUser.nome) {
          console.warn('⚠️ Usuário não encontrado, redirecionando para login...');
          window.location.href = 'login.html';
          return;
        }

        console.log("✅ Usuário autenticado:", currentUser.nome);

        // ✅ VERIFICAÇÃO BÁSICA DE PERMISSÕES (PERMITIR ADMIN/SISTEMA)
        if (currentUser.nivel < 2 &&
            currentUser.id !== 'sistema' &&
            currentUser.id !== 'admin' &&
            currentUser.nome !== 'admin') {
          alert('❌ Você não tem permissão para acessar pedidos de compra.');
          window.location.href = 'index.html';
          return;
        }

        await loadUserPermissions();
        await loadInitialData();
        setupDateFilters();
        updateSupplierFilter();
        // ✅ Aplicar filtros automaticamente para ocultar pedidos recebidos e aprovados por padrão
        applyFilters();
        await checkBudgetLimits();

        // TODO: Registrar acesso ao módulo
        console.log('📝 Acesso ao módulo de pedidos:', currentUser.nome);

      } catch (error) {
        console.error("❌ Erro na inicialização:", error);
        alert('❌ Erro ao carregar a aplicação: ' + error.message);
      }
    });

    async function loadUserPermissions() {
      if (currentUser.nivel === 9) {
        userPermissions = [
          'solicitacao_compras_criar', 'cotacoes_gerenciar', 'pedidos_compra_criar',
          'pedidos_compra_receber', 'pedidos_compra_cancelar'
        ];
      } else {
        const permissionsDoc = await getDoc(doc(db, "permissoes", currentUser.id));
        userPermissions = permissionsDoc.exists() ? permissionsDoc.data().permissoes || [] : [];
      }
    }

    function setupDateFilters() {
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      document.getElementById('startDate').value = firstDayOfMonth.toISOString().split('T')[0];
      document.getElementById('endDate').value = today.toISOString().split('T')[0];
    }

    // ✅ FUNÇÃO MELHORADA PARA GERAR PRÓXIMO NÚMERO DE PEDIDO
    async function getNextOrderNumber() {
      try {
        // ✅ USAR CONTADOR CENTRALIZADO PARA CONSISTÊNCIA
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2); // Últimos 2 dígitos do ano
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0'); // Mês com 2 dígitos
        const anoMes = ano + mes; // Formato AAMM

        // Usar a coleção contadores para gerar numeração sequencial
        const counterRef = doc(db, "contadores", "pedidosCompra");
        const counterDoc = await getDoc(counterRef);

        let nextNumber = 1;

        if (counterDoc.exists()) {
          const counterData = counterDoc.data();

          // ✅ VERIFICAR SE MUDOU O MÊS/ANO (RESET AUTOMÁTICO)
          if (counterData.anoMes === anoMes) {
            // Mesmo mês, incrementar sequência
            nextNumber = counterData.valor + 1;
          } else {
            // Novo mês, resetar sequência
            nextNumber = 1;
          }
        } else {
          // Criar contador se não existir
          await setDoc(counterRef, {
            valor: 1,
            anoMes: anoMes,
            ultimaAtualizacao: Timestamp.now(),
            descricao: "Contador para numeração de pedidos de compra"
          });
        }

        // Atualizar contador
        await updateDoc(counterRef, {
          valor: nextNumber,
          anoMes: anoMes,
          ultimaAtualizacao: Timestamp.now()
        });

        // ✅ RETORNAR NÚMERO NO FORMATO PADRÃO: PC-AAMM-XXXX
        const numeroFormatado = `PC-${anoMes}-${nextNumber.toString().padStart(4, '0')}`;

        // TODO: Log da geração
        console.log('📄 Número de pedido gerado:', numeroFormatado);

        return numeroFormatado;

      } catch (error) {
        console.error('❌ Erro ao gerar número do pedido:', error);

        // ✅ FALLBACK PARA MÉTODO ANTERIOR EM CASO DE ERRO
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2);
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0');
        const anoMes = ano + mes;

        try {
          const pedidosRef = collection(db, "pedidosCompra");

          // Buscar pedidos do mês atual
          const q = query(
            pedidosRef,
            where("numero", ">=", `PC-${anoMes}-0000`),
            where("numero", "<=", `PC-${anoMes}-9999`),
            orderBy("numero", "desc"),
            limit(1)
          );

          const querySnapshot = await getDocs(q);

          if (querySnapshot.empty) {
            // Primeiro pedido do mês
            return `PC-${anoMes}-0001`;
          }

          const ultimoPedido = querySnapshot.docs[0].data();
          const ultimoNumero = ultimoPedido.numero;

          // Extrair o número sequencial
          const sequencial = parseInt(ultimoNumero.split('-')[2]) + 1;

          return `PC-${anoMes}-${sequencial.toString().padStart(4, '0')}`;

        } catch (fallbackError) {
          console.error('❌ Erro no fallback:', fallbackError);
          // Último recurso: usar timestamp
          const timestamp = Date.now().toString().slice(-4);
          return `PC-${anoMes}-${timestamp}`;
        }
      }
    }

    async function loadInitialData() {
      try {
        const [fornecedoresSnap, produtosSnap, cotacoesSnap, pedidosSnap, condicoesSnap, centrosCustoSnap, setoresSnap] = await Promise.all([
          getDocs(collection(db, "fornecedores")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "cotacoes")),
          getDocs(collection(db, "pedidosCompra")),
          getDocs(collection(db, "condicoesPagamento")),
          getDocs(collection(db, "centrosCusto")),
          getDocs(collection(db, "setores"))
        ]);

        fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // ORDENAR PEDIDOS POR DATA (MAIS NOVOS PRIMEIRO)
        pedidosCompra = pedidosSnap.docs
          .map(doc => ({ id: doc.id, ...doc.data() }))
          .sort((a, b) => {
            const dateA = a.dataCriacao?.seconds ? new Date(a.dataCriacao.seconds * 1000) : new Date(a.dataCriacao || 0);
            const dateB = b.dataCriacao?.seconds ? new Date(b.dataCriacao.seconds * 1000) : new Date(b.dataCriacao || 0);
            return dateB - dateA; // Mais novos primeiro
          });

        condicoesPagamento = condicoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setores = setoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log('Dados carregados:', {
          fornecedores: fornecedores.length,
          produtos: produtos.length,
          cotacoes: cotacoes.length,
          pedidosCompra: pedidosCompra.length,
          centrosCusto: centrosCusto.length,
          setores: setores.length
        });

        // Carregar dados nos selects
        updateCostCenterSelect();
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados iniciais.");
      }
    }

    function updateSupplierFilter() {
      const select = document.getElementById('supplierFilter');

      // Verificar se o elemento existe antes de tentar modificá-lo
      if (!select) {
        console.warn('⚠️ Elemento supplierFilter não encontrado no DOM. Função chamada muito cedo ou elemento não existe.');
        return;
      }

      select.innerHTML = '<option value="">Todos</option>';
      fornecedores.forEach(fornecedor => {
        select.innerHTML += `<option value="${fornecedor.id}">${fornecedor.codigo} - ${fornecedor.razaoSocial}</option>`;
      });
    }

    function updateCostCenterSelect() {
      const select = document.getElementById('costCenter');

      // Verificar se o elemento existe antes de tentar modificá-lo
      if (!select) {
        console.warn('⚠️ Elemento costCenter não encontrado no DOM. Função chamada muito cedo ou elemento não existe.');
        return;
      }

      select.innerHTML = '<option value="">Selecione um centro de custo...</option>';

      // Filtrar apenas centros de custo ativos e ordenar por código
      centrosCusto
        .filter(cc => cc.status === 'Ativo')
        .sort((a, b) => a.codigo.localeCompare(b.codigo))
        .forEach(cc => {
          const option = document.createElement('option');
          option.value = cc.id;
          option.textContent = `${cc.codigo} - ${cc.descricao}`;
          select.appendChild(option);
        });

      console.log(`${centrosCusto.filter(cc => cc.status === 'Ativo').length} centros de custo ativos carregados`);
    }

    function updateSummary() {
      const totalPedidos = filteredPedidos.length;
      const valorTotal = filteredPedidos.reduce((sum, pedido) => sum + (pedido.valorTotal || 0), 0);
      const pedidosAbertos = filteredPedidos.filter(p => p.status === 'ABERTO' || p.status === 'PENDENTE').length;
      const mediaPedido = totalPedidos > 0 ? valorTotal / totalPedidos : 0;

      // Verificar se os elementos existem antes de tentar modificá-los
      const totalPedidosEl = document.getElementById('totalPedidos');
      const valorTotalEl = document.getElementById('valorTotal');
      const pedidosAbertosEl = document.getElementById('pedidosAbertos');
      const mediaPedidoEl = document.getElementById('mediaPedido');

      if (totalPedidosEl) totalPedidosEl.textContent = totalPedidos;
      if (valorTotalEl) valorTotalEl.textContent = `R$ ${valorTotal.toFixed(2)}`;
      if (pedidosAbertosEl) pedidosAbertosEl.textContent = pedidosAbertos;
      if (mediaPedidoEl) mediaPedidoEl.textContent = `R$ ${mediaPedido.toFixed(2)}`;
    }

    async function loadOrders() {
      try {
        const tableBody = document.getElementById('ordersTableBody');

        // Verificar se o elemento existe antes de tentar modificá-lo
        if (!tableBody) {
          console.warn('⚠️ Elemento ordersTableBody não encontrado no DOM. Função chamada muito cedo ou elemento não existe.');
          return;
        }

        tableBody.innerHTML = '';

        let pedidosAtrasados = 0;
        let pedidosAVencer = 0;
        let noPrazo = 0;
        let atrasados = 0;
        let parciais = 0;
        const hoje = new Date();

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageItems = filteredPedidos.slice(startIndex, endIndex);

        pageItems.forEach(pedido => {
          const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
          const numero = pedido.numero || "N/D";
          const dataCriacao = pedido.dataCriacao && typeof pedido.dataCriacao.seconds === 'number' ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString() : "N/D";

          // ✅ MELHORAR TRATAMENTO DA DATA DE ENTREGA PREVISTA
          let dataEntregaPrevista = "Não definida";
          if (pedido.dataEntregaPrevista && typeof pedido.dataEntregaPrevista.seconds === 'number') {
            dataEntregaPrevista = new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString();
          } else if (pedido.status === 'ABERTO' || pedido.status === 'PENDENTE') {
            dataEntregaPrevista = '<span style="color: #f57c00; font-weight: bold;">⚠️ Definir data</span>';
          }

          const dataEntrega = pedido.dataEntrega && typeof pedido.dataEntrega.seconds === 'number' ? new Date(pedido.dataEntrega.seconds * 1000).toLocaleDateString() : "N/D";

          let diasRestantes = '';
          let diasRestantesClass = '';
          let diasRestantesTexto = '';

          // ✅ MELHORAR CÁLCULO DOS DIAS RESTANTES
          if (pedido.dataEntregaPrevista && typeof pedido.dataEntregaPrevista.seconds === 'number') {
            const dataPrevista = new Date(pedido.dataEntregaPrevista.seconds * 1000);
            // Zerar as horas para comparação apenas de datas
            dataPrevista.setHours(23, 59, 59, 999);
            const hojeZerado = new Date(hoje);
            hojeZerado.setHours(0, 0, 0, 0);

            const diffTime = dataPrevista - hojeZerado;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays < 0) {
              // Em atraso
              diasRestantesTexto = `${Math.abs(diffDays)} dias em atraso`;
              diasRestantesClass = 'atrasado';
              pedidosAtrasados++;
            } else if (diffDays === 0) {
              // Vence hoje
              diasRestantesTexto = 'Vence hoje';
              diasRestantesClass = 'vence-hoje';
              pedidosAVencer++;
            } else if (diffDays <= 7) {
              // Próximo do vencimento (7 dias ou menos)
              diasRestantesTexto = `${diffDays} dias`;
              diasRestantesClass = 'proximo-vencimento';
              pedidosAVencer++;
            } else {
              // No prazo
              diasRestantesTexto = `${diffDays} dias`;
              diasRestantesClass = 'no-prazo';
            }

            diasRestantes = `<span class="dias-restantes ${diasRestantesClass}">${diasRestantesTexto}</span>`;
          } else {
            // ✅ TRATAMENTO MELHORADO PARA PEDIDOS SEM DATA
            if (pedido.status === 'ABERTO' || pedido.status === 'PENDENTE') {
              diasRestantes = '<span class="dias-restantes" style="background: #fff3e0; color: #f57c00; border: 1px solid #ff9800;">⚠️ Definir prazo</span>';
            } else if (pedido.status === 'CANCELADO') {
              diasRestantes = '<span class="dias-restantes" style="background: #ffebee; color: #c62828; border: 1px solid #ef5350;">❌ Cancelado</span>';
            } else if (pedido.status === 'RECEBIDO') {
              diasRestantes = '<span class="dias-restantes" style="background: #e8f5e9; color: #2e7d32; border: 1px solid #66bb6a;">✅ Recebido</span>';
            } else {
              diasRestantes = '<span class="dias-restantes" style="background: #f5f5f5; color: #666;">➖ Não definido</span>';
            }
          }

          let statusClass = pedido.status.toLowerCase();

          // Contabilizar para estatísticas
          if (diasRestantesClass === 'atrasado') {
            atrasados++;
          } else if (diasRestantesClass === 'vence-hoje' || diasRestantesClass === 'proximo-vencimento') {
            // pedidosAVencer já foi incrementado acima
          } else {
            noPrazo++;
          }

          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${numero}</td>
            <td>${dataCriacao}</td>
            <td>${fornecedor ? fornecedor.razaoSocial : 'N/A'}</td>
            <td>R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</td>
            <td><span class="status-badge status-${statusClass}">${pedido.status}</span></td>
            <td>${dataEntregaPrevista}</td>
            <td>${diasRestantes}</td>
            <td>
              <div class="actions">
                <!-- Visualizar -->
                <button class="btn-action btn-info" onclick="viewOrder('${pedido.id}')" title="👁️ Ver Detalhes Completos">
                  <i class="fas fa-eye"></i>
                </button>

                <!-- Rastrear -->
                <button class="btn-action btn-primary" onclick="trackOrder('${pedido.id}')" title="🚚 Rastrear Entrega">
                  <i class="fas fa-truck"></i>
                </button>

                <!-- Imprimir Pedido -->
                <button class="btn-action btn-primary" onclick="gerarPDFPedido('${pedido.id}')" title="🖨️ Imprimir Pedido">
                  <i class="fas fa-print"></i>
                </button>

                <!-- Enviar para Fornecedor -->
                <button class="btn-action btn-info" onclick="enviarPedidoFornecedor('${pedido.id}')" title="📧 Enviar para Fornecedor">
                  <i class="fas fa-envelope"></i>
                </button>

                <!-- ✅ BOTÃO PARA DEFINIR DATA DE ENTREGA -->
                ${!pedido.dataEntregaPrevista && (pedido.status === 'ABERTO' || pedido.status === 'PENDENTE' || pedido.status === 'APROVADO') ? `
                  <button class="btn-action" style="background: #ff9800; color: white;" onclick="definirDataEntrega('${pedido.id}')" title="📅 Definir Data de Entrega">
                    <i class="fas fa-calendar-plus"></i>
                  </button>
                ` : ''}

                <!-- Ações específicas por status do processo de compras -->
                ${pedido.status === 'RASCUNHO' ? `
                  <button class="btn-action btn-success" onclick="enviarParaAprovacao('${pedido.id}')" title="📋 Enviar para Aprovação">
                    <i class="fas fa-paper-plane"></i>
                  </button>
                ` : ''}

                ${pedido.status === 'AGUARDANDO_APROVACAO' ? `
                  <button class="btn-action btn-info" onclick="redirectToApproval('${pedido.id}')" title="📋 Ir para Gestão Integrada - Aprovação">
                    <i class="fas fa-external-link-alt"></i>
                  </button>
                ` : ''}

                ${pedido.status === 'APROVADO' ? `
                  <button class="btn-action btn-warning" onclick="enviarParaFornecedor('${pedido.id}')" title="📧 Enviar para Fornecedor">
                    <i class="fas fa-envelope"></i>
                  </button>
                ` : ''}



                ${pedido.status === 'APROVADO' && userPermissions.includes('pedidos_compra_receber') ? `
                  <button class="btn-action btn-success" onclick="receiveOrder('${pedido.id}')" title="📦 Receber Mercadoria">
                    <i class="fas fa-box"></i>
                  </button>
                ` : ''}

                ${pedido.status === 'APROVADO' ? `
                  <button class="btn-action btn-warning" onclick="updateDeliveryStatus('${pedido.id}')" title="📅 Atualizar Entrega">
                    <i class="fas fa-calendar-alt"></i>
                  </button>
                ` : ''}

                ${(pedido.status === 'ABERTO' || pedido.status === 'PENDENTE') ? `
                  <button class="btn-action btn-danger" onclick="cancelOrder('${pedido.id}')" title="❌ Cancelar Pedido">
                    <i class="fas fa-times"></i>
                  </button>
                ` : ''}
              </div>
            </td>
          `;
          tableBody.appendChild(row);
        });

        // Update summary counters
        document.getElementById('pedidosAtrasados').textContent = pedidosAtrasados;
        document.getElementById('pedidosAVencer').textContent = pedidosAVencer;

        // Update tracking progress bars
        const total = noPrazo + atrasados + parciais;
        if (total > 0) {
          document.getElementById('noPrazoProgress').style.width = `${(noPrazo/total)*100}%`;
          document.getElementById('atrasadoProgress').style.width = `${(atrasados/total)*100}%`;
          document.getElementById('parcialProgress').style.width = `${(parciais/total)*100}%`;

          document.getElementById('noPrazoCount').textContent = `${noPrazo} pedidos`;
          document.getElementById('atrasadoCount').textContent = `${atrasados} pedidos`;
          document.getElementById('parcialCount').textContent = `${parciais} pedidos`;
        }

        updatePagination();
        updateSummary();

        // ✅ ATUALIZAR ALERTAS DE ATRASO
        updateDelayAlerts();
      } catch (error) {
        console.error("Erro ao carregar pedidos:", error);
        alert("Erro ao carregar pedidos.");
      }
    }

    function updatePagination() {
      const totalPages = Math.ceil(filteredPedidos.length / itemsPerPage);
      const pagination = document.getElementById('pagination');
      pagination.innerHTML = '';

      if (totalPages > 1) {
        pagination.innerHTML += `
          <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>Anterior</button>
        `;
        for (let i = 1; i <= totalPages; i++) {
          pagination.innerHTML += `
            <button onclick="changePage(${i})" class="${currentPage === i ? 'active' : ''}">${i}</button>
          `;
        }
        pagination.innerHTML += `
          <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>Próximo</button>
        `;
      }
    }

    window.changePage = function(page) {
      if (page >= 1 && page <= Math.ceil(filteredPedidos.length / itemsPerPage)) {
        currentPage = page;
        loadOrders();
      }
    };

    // ✅ FUNÇÃO PARA DEFINIR DATA DE ENTREGA
    window.definirDataEntrega = async function(orderId) {
      try {
        // ✅ VALIDAÇÃO DE PERMISSÕES
        if (currentUser.nivel < 2 &&
            currentUser.id !== 'sistema' &&
            currentUser.id !== 'admin' &&
            currentUser.nome !== 'admin') {
          alert('❌ Você não tem permissão para definir datas de entrega');
          return;
        }

        // ✅ BUSCAR DADOS DO PEDIDO
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = pedidoSnap.data();

        // ✅ VALIDAR SE PODE DEFINIR DATA
        if (pedido.dataEntregaPrevista) {
          const dataAtual = new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString();
          const confirmar = confirm(`⚠️ Este pedido já possui data de entrega definida: ${dataAtual}\n\nDeseja alterar a data?`);
          if (!confirmar) {
            return;
          }
        }

        // ✅ MODAL PARA DEFINIR DATA
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
          <div class="modal-content" style="max-width: 500px;">
            <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
            <h2>📅 Definir Data de Entrega</h2>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3>📋 Dados do Pedido</h3>
              <p><strong>Número:</strong> ${pedido.numero}</p>
              <p><strong>Status:</strong> ${pedido.status}</p>
              <p><strong>Valor:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
            </div>

            <div class="form-group">
              <label><strong>Data de Entrega Prevista:</strong></label>
              <input type="date" id="novaDataEntrega" required min="${new Date().toISOString().split('T')[0]}">
              <small style="color: #666;">A data deve ser futura</small>
            </div>

            <div class="form-group">
              <label><strong>Observações (opcional):</strong></label>
              <textarea id="observacoesData" rows="3" placeholder="Motivo da definição/alteração da data..."></textarea>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
              <button onclick="this.closest('.modal').remove()" class="btn btn-secondary">❌ Cancelar</button>
              <button onclick="salvarDataEntrega('${orderId}')" class="btn btn-primary">✅ Salvar Data</button>
            </div>
          </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        // ✅ DEFINIR DATA PADRÃO (7 dias a partir de hoje)
        const dataDefault = new Date();
        dataDefault.setDate(dataDefault.getDate() + 7);
        document.getElementById('novaDataEntrega').value = dataDefault.toISOString().split('T')[0];

      } catch (error) {
        console.error("❌ Erro ao abrir modal de data:", error);
        alert("❌ Erro ao carregar dados do pedido");
      }
    };

    // ✅ FUNÇÃO PARA SALVAR DATA DE ENTREGA
    window.salvarDataEntrega = async function(orderId) {
      try {
        const novaData = document.getElementById('novaDataEntrega').value;
        const observacoes = document.getElementById('observacoesData').value;

        if (!novaData) {
          alert('❌ Selecione uma data de entrega');
          return;
        }

        // ✅ VALIDAR SE DATA É FUTURA
        const dataEscolhida = new Date(novaData);
        const hoje = new Date();
        hoje.setHours(0, 0, 0, 0);

        if (dataEscolhida < hoje) {
          alert('❌ A data de entrega deve ser futura');
          return;
        }

        // ✅ ATUALIZAR PEDIDO
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        await updateDoc(pedidoRef, {
          dataEntregaPrevista: Timestamp.fromDate(dataEscolhida),
          ultimaAtualizacao: Timestamp.now(),
          historico: arrayUnion({
            data: Timestamp.now(),
            acao: 'DEFINICAO_DATA_ENTREGA',
            usuario: currentUser.nome,
            usuarioId: currentUser.id,
            detalhes: `Data de entrega definida para ${dataEscolhida.toLocaleDateString()}${observacoes ? '. Obs: ' + observacoes : ''}`
          })
        });

        // TODO: Registrar auditoria
        console.log('📅 Data de entrega definida:', orderId, 'para:', dataEscolhida.toLocaleDateString(), 'por:', currentUser.nome);

        alert('✅ Data de entrega definida com sucesso!');
        document.querySelector('.modal').remove();

        // ✅ RECARREGAR DADOS
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error("❌ Erro ao salvar data:", error);
        alert("❌ Erro ao salvar data de entrega: " + error.message);
      }
    };

    // Função para redirecionar para aprovação na gestão integrada
    window.redirectToApproval = function(orderId) {
      // Informar ao usuário sobre o redirecionamento
      if (confirm('📋 Redirecionamento para Gestão Integrada\n\nAs aprovações de pedidos são realizadas na tela de Gestão de Compras Integrada.\n\nDeseja ir para lá agora?')) {
        // Salvar o ID do pedido no localStorage para abrir diretamente na tela de aprovação
        localStorage.setItem('pendingApprovalOrderId', orderId);
        // Redirecionar para a gestão integrada
        window.location.href = 'gestao_compras_integrada.html?tab=pedidos&action=approve&id=' + orderId;
      }
    };

    // Função para cancelar pedido
    window.cancelOrder = async function(orderId) {
      try {
        // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
        if (currentUser.nivel < 3 &&
            currentUser.id !== 'sistema' &&
            currentUser.id !== 'admin' &&
            currentUser.nome !== 'admin') {
          alert('❌ Você não tem permissão para cancelar pedidos');
          return;
        }

        // ✅ VALIDAÇÃO DE DADOS DE ENTRADA
        if (!orderId || typeof orderId !== 'string') {
          alert('❌ ID do pedido inválido');
          return;
        }

        // ✅ VERIFICAR SE PEDIDO EXISTE E PODE SER CANCELADO
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = pedidoSnap.data();

        // ✅ VALIDAR STATUS DO PEDIDO
        if (pedido.status === 'CANCELADO') {
          alert('⚠️ Este pedido já foi cancelado');
          return;
        }

        if (pedido.status === 'RECEBIDO' || pedido.status === 'FINALIZADO') {
          alert('❌ Não é possível cancelar pedidos já recebidos ou finalizados');
          return;
        }

        // ✅ CONFIRMAÇÃO DUPLA PARA OPERAÇÃO CRÍTICA
        if (!confirm('⚠️ ATENÇÃO: Tem certeza que deseja cancelar este pedido?\n\nEsta ação não pode ser desfeita e pode impactar:\n- Orçamento do centro de custo\n- Planejamento de compras\n- Relacionamento com fornecedor')) {
          return;
        }

        // ✅ VALIDAR MOTIVO DO CANCELAMENTO
        const motivo = prompt('📝 Informe o motivo do cancelamento (obrigatório):');
        if (!motivo || motivo.trim().length < 10) {
          alert('❌ É necessário informar um motivo detalhado (mínimo 10 caracteres)');
          return;
        }

        // ✅ VALIDAÇÃO ORÇAMENTÁRIA (se aplicável)
        if (pedido.valorTotal > 10000) {
          const confirmacaoGerencial = confirm('💰 ATENÇÃO: Este pedido possui valor alto (R$ ' + pedido.valorTotal.toFixed(2) + ').\n\nConfirma o cancelamento mesmo assim?');
          if (!confirmacaoGerencial) {
            return;
          }
        }

        // ✅ ATUALIZAR COM DADOS SEGUROS
        await updateDoc(pedidoRef, {
          status: 'CANCELADO',
          motivoCancelamento: motivo.trim(),
          dataCancelamento: Timestamp.now(),
          canceladoPor: currentUser.nome,
          canceladoPorId: currentUser.id,
          ultimaAtualizacao: Timestamp.now(),
          historico: arrayUnion({
            data: Timestamp.now(),
            acao: 'CANCELAMENTO',
            usuario: currentUser.nome,
            usuarioId: currentUser.id,
            detalhes: `Pedido cancelado. Motivo: ${motivo.trim()}`
          })
        });

        // TODO: Registrar auditoria do cancelamento
        console.log('❌ Pedido cancelado:', orderId, 'por:', currentUser.nome, 'motivo:', motivo.trim());

        alert('✅ Pedido cancelado com sucesso!');

        // Recarregar dados
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error("❌ Erro ao cancelar pedido:", error);

        // TODO: Registrar erro na auditoria
        console.log('❌ Erro no cancelamento:', orderId, error.message);

        alert("❌ Erro ao cancelar pedido: " + error.message);
      }
    };

    // Função para rastrear pedido
    window.trackOrder = async function(orderId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);
        
        if (!pedidoSnap.exists()) {
          alert('Pedido não encontrado');
          return;
        }
        
        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };
        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
        
        // Criar modal de tracking
        const trackingModal = document.createElement('div');
        trackingModal.className = 'modal';
        trackingModal.innerHTML = `
          <div class="modal-content" style="max-width: 800px;">
            <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
            <h2>Tracking do Pedido ${pedido.numero}</h2>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <div>
                <h3>Informações do Pedido</h3>
                <p><strong>Fornecedor:</strong> ${fornecedor ? fornecedor.razaoSocial : 'N/A'}</p>
                <p><strong>Data do Pedido:</strong> ${pedido.dataCriacao ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</p>
                <p><strong>Valor Total:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
                <p><strong>Status:</strong> <span class="status-badge status-${pedido.status.toLowerCase()}">${pedido.status}</span></p>
              </div>
              <div>
                <h3>Prazos</h3>
                <p><strong>Data Prevista:</strong> ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString() : '<span style="color: #f57c00;">⚠️ Não definida</span>'}</p>
                <p><strong>Dias Restantes:</strong> ${calcularDiasRestantes(pedido.dataEntregaPrevista)}</p>
                <p><strong>Condição de Pagamento:</strong> ${pedido.condicaoPagamento || 'N/A'}</p>
              </div>
            </div>
            
            <h3>Timeline do Pedido</h3>
            <div class="timeline">
              ${generateTimeline(pedido)}
            </div>
            
            <h3>Itens do Pedido</h3>
            <table class="items-table">
              <thead>
                <tr>
                  <th>Código</th>
                  <th>Descrição</th>
                  <th>Quantidade</th>
                  <th>Valor Unit.</th>
                  <th>Total</th>
                  <th>Status Entrega</th>
                </tr>
              </thead>
              <tbody>
                ${pedido.itens.map(item => `
                  <tr>
                    <td>${item.codigo}</td>
                    <td>${item.descricao}</td>
                    <td>${item.quantidade} ${item.unidade}</td>
                    <td>R$ ${item.valorUnitario?.toFixed(2) || '0,00'}</td>
                    <td>R$ ${item.valorTotal?.toFixed(2) || '0,00'}</td>
                    <td><span class="status-badge status-${(item.statusEntrega || 'pendente').toLowerCase()}">${item.statusEntrega || 'Pendente'}</span></td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            ${pedido.status === 'APROVADO' ? `
              <div style="margin-top: 20px;">
                <button onclick="updateDeliveryStatus('${orderId}')" class="btn-primary">Atualizar Status de Entrega</button>
              </div>
            ` : ''}
          </div>
        `;
        
        document.body.appendChild(trackingModal);
        trackingModal.style.display = 'block';
        
      } catch (error) {
        console.error("Erro ao rastrear pedido:", error);
        alert("Erro ao carregar tracking do pedido");
      }
    };

    function calcularDiasRestantes(dataEntregaPrevista) {
      // ✅ MELHORAR TRATAMENTO DE DATAS INDEFINIDAS
      if (!dataEntregaPrevista || !dataEntregaPrevista.seconds) {
        return '📅 Data não definida';
      }

      const hoje = new Date();
      const dataEntrega = new Date(dataEntregaPrevista.seconds * 1000);

      // Zerar as horas para comparação apenas de datas
      dataEntrega.setHours(23, 59, 59, 999);
      const hojeZerado = new Date(hoje);
      hojeZerado.setHours(0, 0, 0, 0);

      const diffTime = dataEntrega - hojeZerado;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return `⚠️ ${Math.abs(diffDays)} dias em atraso`;
      } else if (diffDays === 0) {
        return '⏰ Vence hoje';
      } else if (diffDays <= 7) {
        return `⚡ ${diffDays} dias (próximo vencimento)`;
      } else {
        return `✅ ${diffDays} dias`;
      }
    }

    function generateTimeline(pedido) {
      const eventos = [];
      
      if (pedido.dataCriacao) {
        eventos.push({
          data: new Date(pedido.dataCriacao.seconds * 1000),
          evento: 'Pedido Criado',
          status: 'concluido',
          usuario: pedido.criadoPor
        });
      }
      
      if (pedido.dataAprovacao) {
        eventos.push({
          data: new Date(pedido.dataAprovacao.seconds * 1000),
          evento: 'Pedido Aprovado',
          status: 'concluido',
          usuario: pedido.aprovadoPor
        });
      }
      
      if (pedido.dataRecebimento) {
        eventos.push({
          data: new Date(pedido.dataRecebimento.seconds * 1000),
          evento: 'Pedido Recebido',
          status: 'concluido',
          usuario: pedido.recebidoPor
        });
      }
      
      // Adicionar evento futuro se necessário
      if (pedido.dataEntregaPrevista && pedido.status !== 'RECEBIDO') {
        eventos.push({
          data: new Date(pedido.dataEntregaPrevista.seconds * 1000),
          evento: 'Entrega Prevista',
          status: 'pendente'
        });
      }
      
      eventos.sort((a, b) => a.data - b.data);
      
      return eventos.map(evento => `
        <div class="timeline-item" style="display: flex; align-items: center; margin-bottom: 10px; padding: 10px; background: ${evento.status === 'concluido' ? '#e8f5e9' : '#fff3e0'}; border-radius: 4px;">
          <div class="timeline-icon" style="width: 20px; height: 20px; border-radius: 50%; background: ${evento.status === 'concluido' ? '#4caf50' : '#ff9800'}; margin-right: 15px;"></div>
          <div>
            <div style="font-weight: bold;">${evento.evento}</div>
            <div style="font-size: 12px; color: #666;">${evento.data.toLocaleDateString()} ${evento.data.toLocaleTimeString()}</div>
            ${evento.usuario ? `<div style="font-size: 12px; color: #666;">Por: ${evento.usuario}</div>` : ''}
          </div>
        </div>
      `).join('');
    }

    // Função para atualizar status de entrega
    window.updateDeliveryStatus = async function(orderId) {
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content" style="max-width: 600px;">
          <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
          <h2>Atualizar Status de Entrega</h2>
          
          <div class="form-group">
            <label>Tipo de Atualização:</label>
            <select id="tipoAtualizacao" onchange="toggleUpdateFields()">
              <option value="">Selecione...</option>
              <option value="entrega_parcial">Entrega Parcial</option>
              <option value="entrega_total">Entrega Total</option>
              <option value="atraso">Atraso na Entrega</option>
              <option value="cancelamento">Cancelar Item</option>
            </select>
          </div>
          
          <div id="updateFields" style="display: none;">
            <!-- Campos serão preenchidos dinamicamente -->
          </div>
          
          <div class="form-group">
            <label>Observações:</label>
            <textarea id="observacoesEntrega" rows="3" placeholder="Descreva a situação da entrega..."></textarea>
          </div>
          
          <div style="display: flex; gap: 10px; justify-content: flex-end;">
            <button onclick="this.closest('.modal').remove()" class="btn-secondary">Cancelar</button>
            <button onclick="salvarAtualizacaoEntrega('${orderId}')" class="btn-primary">Salvar Atualização</button>
          </div>
        </div>
      `;
      
      document.body.appendChild(modal);
      modal.style.display = 'block';
    };

    window.toggleUpdateFields = function() {
      const tipo = document.getElementById('tipoAtualizacao').value;
      const fieldsDiv = document.getElementById('updateFields');
      
      if (!tipo) {
        fieldsDiv.style.display = 'none';
        return;
      }
      
      let html = '';
      
      switch (tipo) {
        case 'entrega_parcial':
          html = `
            <div class="form-group">
              <label>Data da Entrega Parcial:</label>
              <input type="date" id="dataEntregaParcial" required>
            </div>
            <div class="form-group">
              <label>Próxima Data Prevista:</label>
              <input type="date" id="proximaDataPrevista" required>
            </div>
          `;
          break;
        case 'entrega_total':
          html = `
            <div class="form-group">
              <label>Data da Entrega:</label>
              <input type="date" id="dataEntregaTotal" required>
            </div>
          `;
          break;
        case 'atraso':
          html = `
            <div class="form-group">
              <label>Nova Data Prevista:</label>
              <input type="date" id="novaDataPrevista" required>
            </div>
            <div class="form-group">
              <label>Motivo do Atraso:</label>
              <select id="motivoAtraso" required>
                <option value="">Selecione...</option>
                <option value="fornecedor">Problema no Fornecedor</option>
                <option value="logistica">Problema na Logística</option>
                <option value="producao">Problema na Produção</option>
                <option value="documentacao">Problema na Documentação</option>
                <option value="outros">Outros</option>
              </select>
            </div>
          `;
          break;
        case 'cancelamento':
          html = `
            <div class="form-group">
              <label>Motivo do Cancelamento:</label>
              <select id="motivoCancelamento" required>
                <option value="">Selecione...</option>
                <option value="substituicao">Substituição de Item</option>
                <option value="cancelamento_projeto">Cancelamento do Projeto</option>
                <option value="fornecedor_indisponivel">Fornecedor Indisponível</option>
                <option value="outros">Outros</option>
              </select>
            </div>
          `;
          break;
      }
      
      fieldsDiv.innerHTML = html;
      fieldsDiv.style.display = 'block';
    };

    window.salvarAtualizacaoEntrega = async function(orderId) {
      try {
        const tipo = document.getElementById('tipoAtualizacao').value;
        const observacoes = document.getElementById('observacoesEntrega').value;
        
        if (!tipo) {
          alert('Selecione o tipo de atualização.');
          return;
        }
        
        const atualizacao = {
          tipo,
          data: Timestamp.now(),
          usuario: currentUser.nome,
          observacoes
        };
        
        // Adicionar campos específicos baseado no tipo
        switch (tipo) {
          case 'entrega_parcial':
            atualizacao.dataEntregaParcial = document.getElementById('dataEntregaParcial').value;
            atualizacao.proximaDataPrevista = document.getElementById('proximaDataPrevista').value;
            break;
          case 'entrega_total':
            atualizacao.dataEntregaTotal = document.getElementById('dataEntregaTotal').value;
            break;
          case 'atraso':
            atualizacao.novaDataPrevista = document.getElementById('novaDataPrevista').value;
            atualizacao.motivoAtraso = document.getElementById('motivoAtraso').value;
            break;
          case 'cancelamento':
            atualizacao.motivoCancelamento = document.getElementById('motivoCancelamento').value;
            break;
        }
        
        // Buscar pedido atual
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);
        const pedido = pedidoSnap.data();
        
        // Atualizar status do pedido se necessário
        let novoStatus = pedido.status;
        if (tipo === 'entrega_total') {
          novoStatus = 'RECEBIDO';
        } else if (tipo === 'cancelamento') {
          novoStatus = 'CANCELADO';
        }
        
        // Salvar atualização
        await updateDoc(pedidoRef, {
          status: novoStatus,
          atualizacoesEntrega: [...(pedido.atualizacoesEntrega || []), atualizacao],
          ultimaAtualizacao: Timestamp.now()
        });
        
        alert('Status de entrega atualizado com sucesso!');
        document.querySelector('.modal').remove();
        
        // Recarregar dados
        await loadInitialData();
        applyFilters();
        
      } catch (error) {
        console.error("Erro ao atualizar status:", error);
        alert("Erro ao atualizar status de entrega.");
      }
    };

    window.exportToExcel = function() {
      try {
        const dados = filteredPedidos.map(pedido => {
          const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
          
          return {
            'Número': pedido.numero || 'N/D',
            'Data': pedido.dataCriacao && typeof pedido.dataCriacao.seconds === 'number' ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/D',
            'Fornecedor': fornecedor ? fornecedor.razaoSocial : 'N/A',
            'Valor Total': pedido.valorTotal?.toFixed(2) || '0,00',
            'Status': pedido.status,
            'Data Entrega Prevista': pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString() : 'Não definida',
            'Condição Pagamento': pedido.condicaoPagamento || 'N/A',
            'Centro de Custo': pedido.centroCusto ? `${pedido.centroCusto.codigo} - ${pedido.centroCusto.descricao}` : 'N/A',
            'Criado Por': pedido.criadoPor || 'N/A',
            'Aprovado Por': pedido.aprovadoPor || 'N/A',
            'Itens': pedido.itens?.length || 0,
            'Observações': pedido.observacoes || ''
          };
        });
        
        const headers = Object.keys(dados[0] || {});
        const csvContent = [
          headers.join(';'),
          ...dados.map(row => headers.map(header => `"${row[header] || ''}"`).join(';'))
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `pedidos_compra_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        
        alert('Relatório exportado com sucesso!');
      } catch (error) {
        console.error('Erro ao exportar:', error);
        alert('Erro ao exportar relatório.');
      }
    };

    window.openNewOrderModal = function openNewOrderModal() {
      if (!userPermissions.includes('pedidos_compra_criar')) {
        alert('Você não tem permissão para criar pedidos de compra.');
        return;
      }
      document.getElementById('orderForm').reset();
      document.getElementById('itemsTableBody').innerHTML = '';
      document.getElementById('totalGeral').textContent = 'R$ 0,00';
      document.getElementById('orderModal').style.display = 'block';
      updateSupplierSelect();
      updateCostCenterSelect();
    };

    function updateSupplierSelect() {
      const select = document.getElementById('supplierSelect');

      // Verificar se o elemento existe antes de tentar modificá-lo
      if (!select) {
        console.warn('⚠️ Elemento supplierSelect não encontrado no DOM. Função chamada muito cedo ou elemento não existe.');
        return;
      }

      select.innerHTML = '<option value="">Selecione o fornecedor...</option>';
      fornecedores.forEach(fornecedor => {
        select.innerHTML += `<option value="${fornecedor.id}">${fornecedor.codigo} - ${fornecedor.razaoSocial}</option>`;
      });
    }

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
    };

    window.addItem = function() {
      const tableBody = document.getElementById('itemsTableBody');
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>
          <div style="display: flex; gap: 5px;">
            <input type="text" class="item-codigo" placeholder="Código" style="width: 100px;" onblur="searchProduct(this)" required>
            <button type="button" onclick="openProductSearch(this)">🔍</button>
          </div>
        </td>
        <td><input type="text" class="item-descricao" required readonly></td>
        <td><input type="number" class="item-quantidade" min="0.001" step="0.001" required onchange="calculateItemTotal(this)"></td>
        <td>
          <select class="item-unidade" onchange="updateConvertedQty(this)" required>
            <option value="">Selecione...</option>          </select>
        </td>
        <td><input type="number" class="item-valor" min="0.01" step="0.01" required onchange="calculateItemTotal(this)"></td>
        <td><input type="text" class="item-total" readonly></td>
        <td><button type="button" onclick="removeItem(this)">Remover</button></td>
        <td><input type="text" class="item-qtd-convertida" readonly placeholder="Qtd. em PC"></td>
      `;
      tableBody.appendChild(row);
    };

    window.calculateItemTotal = function(input) {
      const row = input.closest('tr');
      const quantidade = parseFloat(row.querySelector('.item-quantidade').value) || 0;
      const valor = parseFloat(row.querySelector('.item-valor').value) || 0;
      const total = quantidade * valor;
      row.querySelector('.item-total').value = `R$ ${total.toFixed(2)}`;
      calculateOrderTotal();
    };

    function calculateOrderTotal() {
      let total = 0;
      document.querySelectorAll('#itemsTableBody tr').forEach(row => {
        const quantidade = parseFloat(row.querySelector('.item-quantidade').value) || 0;
        const valor = parseFloat(row.querySelector('.item-valor').value) || 0;
        total += quantidade * valor;
      });
      document.getElementById('totalGeral').textContent = `R$ ${total.toFixed(2)}`;
    }

    window.searchProduct = function(input) {
      const codigo = input.value.trim().toUpperCase();
      const row = input.closest('tr');
      const descricaoInput = row.querySelector('.item-descricao');
      const unidadeSelect = row.querySelector('.item-unidade');
      const qtdConvertidaInput = row.querySelector('.item-qtd-convertida');

      if (codigo) {
        const produto = produtos.find(p => p.codigo.toUpperCase() === codigo);
        if (produto) {
          descricaoInput.value = produto.descricao;
          unidadeSelect.innerHTML = `
            <option value="${produto.unidade}">${produto.unidade} (Principal)</option>
            ${produto.unidadeSecundaria ? `<option value="${produto.unidadeSecundaria}">${produto.unidadeSecundaria} (Secundária)</option>` : ''}
          `;
          qtdConvertidaInput.dataset.produtoId = produto.id;
          updateConvertedQty(unidadeSelect);
        } else {
          alert('Produto não encontrado!');
          input.value = '';
          descricaoInput.value = '';
          unidadeSelect.innerHTML = '<option value="">Selecione...</option>';
          qtdConvertidaInput.value = '';
        }
      }
    };

    window.openProductSearch = function(button) {
      const searchText = prompt('Digite o código ou descrição do produto:');
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        const foundProducts = produtos.filter(p => 
          p.codigo.toLowerCase().includes(searchLower) || 
          p.descricao.toLowerCase().includes(searchLower)
        );

        if (foundProducts.length > 0) {
          const options = foundProducts.map((p, index) => 
            `${index + 1} - ${p.codigo} - ${p.descricao}`
          ).join('\n');

          const selection = prompt(`Selecione o produto (1-${foundProducts.length}):\n\n${options}`);
          const selectedIndex = parseInt(selection) - 1;

          if (selectedIndex >= 0 && selectedIndex < foundProducts.length) {
            const produto = foundProducts[selectedIndex];
            const row = button.closest('tr');
            row.querySelector('.item-codigo').value = produto.codigo;
            row.querySelector('.item-descricao').value = produto.descricao;
            row.querySelector('.item-unidade').value = produto.unidade;
          }
        } else {
          alert('Nenhum produto encontrado!');
        }
      }
    };

    window.removeItem = function(button) {
      button.closest('tr').remove();
      calculateOrderTotal();
    };

    // Função combinada: handleOrder (do <DOCUMENT> com suas alterações)
    window.handleOrder = async function(event) {
      event.preventDefault();
      if (!userPermissions.includes('pedidos_compra_criar')) {
        alert('Você não tem permissão para criar pedidos de compra.');
        return;
      }

      const fornecedorId = document.getElementById('supplierSelect').value;
      const expectedDeliveryDate = document.getElementById('expectedDeliveryDate').value;
      const costCenterId = document.getElementById('costCenter').value;

      if (!fornecedorId || !expectedDeliveryDate || !costCenterId) {
        alert('Preencha todos os campos obrigatórios.');
        return;
      }

      // Buscar dados do centro de custo
      const centroCusto = centrosCusto.find(cc => cc.id === costCenterId);
      if (!centroCusto) {
        alert('Centro de custo não encontrado.');
        return;
      }

      const items = [];
      let valorTotal = 0;
      document.querySelectorAll('#itemsTableBody tr').forEach(row => {
        const quantidade = parseFloat(row.querySelector('.item-quantidade').value);
        const valorUnitario = parseFloat(row.querySelector('.item-valor').value);
        const unidade = row.querySelector('.item-unidade').value;
        const qtdConvertida = parseFloat(row.querySelector('.item-qtd-convertida').value) || quantidade;
        const total = quantidade * valorUnitario;
        items.push({
          codigo: row.querySelector('.item-codigo').value,
          descricao: row.querySelector('.item-descricao').value,
          quantidade,
          unidade,
          quantidadeConvertida: qtdConvertida,
          valorUnitario,
          valorTotal: total
        });
        valorTotal += total;
      });

      if (items.length === 0) {
        alert('Adicione pelo menos um item ao pedido.');
        return;
      }

      // Verificar limite de orçamento (do seu código)
      if (valorTotal + orcamentoUtilizado > LIMITE_ORCAMENTO_MENSAL) {
        if (!confirm('Este pedido irá exceder o limite de orçamento mensal. Deseja continuar?')) {
          return;
        }
      }

      try {
        const numeroSequencial = await getNextOrderNumber();
        const pedido = {
          numero: numeroSequencial,
          fornecedorId,
          condicaoPagamento: document.getElementById('paymentTerms').value,
          dataEntregaPrevista: Timestamp.fromDate(new Date(expectedDeliveryDate)), // Corrigido para usar Timestamp
          centroCustoId: costCenterId,
          centroCusto: {
            codigo: centroCusto.codigo,
            descricao: centroCusto.descricao,
            departamento: centroCusto.departamento
          },
          itens: items,
          valorTotal,
          observacoes: document.getElementById('observacoes').value,
          status: 'RASCUNHO',
          statusAprovacao: 'PENDENTE', // Alterado para PENDENTE conforme seu código
          dataCriacao: Timestamp.now(),
          criadoPor: currentUser.nome,
          workflowAprovacao: [ // Seu workflow adicionado
            { nivel: 1, cargo: 'Supervisor', status: 'PENDENTE' },
            { nivel: 2, cargo: 'Gerente', status: 'PENDENTE' },
            { nivel: 3, cargo: 'Diretor', status: valorTotal > 50000 ? 'PENDENTE' : 'NAO_REQUERIDO' }
          ],
          processoCompra: {
            etapaAtual: 'CRIACAO',
            dataEnvioAprovacao: null,
            dataAprovacao: null,
            dataEnvioFornecedor: null,
            dataConfirmacaoFornecedor: null,
            dataPrevisaoEntrega: null,
            dataRecebimento: null
          },
          historico: [{
            data: Timestamp.now(),
            acao: 'CRIACAO',
            usuario: currentUser.nome,
            detalhes: 'Pedido criado como rascunho'
          }]
        };

        // Criar pedido de compra
        const pedidoRef = await addDoc(collection(db, "pedidosCompra"), pedido);

        // Criar conta a pagar automaticamente
        const condicaoId = document.getElementById('paymentTerms').value;
        const condicao = condicoesPagamento.find(c => c.id === condicaoId);
        const fornecedor = fornecedores.find(f => f.id === fornecedorId);

        // Validar se a condição de pagamento existe
        if (!condicao) {
          throw new Error('Condição de pagamento não encontrada. Selecione uma condição válida.');
        }

        const contaPagar = {
          fornecedorId: fornecedorId,
          tipoDocumento: 'PEDIDO',
          numeroDocumento: numeroSequencial,
          dataEmissao: Timestamp.now(),
          valorTotal: valorTotal,
          condicaoPagamentoId: condicao.id,
          centroCustoId: costCenterId,
          centroCusto: {
            codigo: centroCusto.codigo,
            descricao: centroCusto.descricao
          },
          observacoes: `Gerado automaticamente do Pedido de Compra ${numeroSequencial}`,
          pedidoCompraId: pedidoRef.id,
          dataCadastro: Timestamp.now(),
          parcelas: []
        };

        // Gerar parcelas baseado na condição de pagamento
        if (condicao.tipo === 'A_VISTA') {
          contaPagar.parcelas.push({
            numero: 1,
            descricao: 'À Vista',
            dataVencimento: Timestamp.now(),
            valor: valorTotal,
            valorPago: 0,
            status: 'PENDENTE',
            pagamentos: []
          });
        } else if (condicao.tipo === 'PARCELADO') {
          const valorParcela = valorTotal / condicao.numParcelas;
          for (let i = 1; i <= condicao.numParcelas; i++) {
            const dataVencimento = new Date();
            dataVencimento.setDate(dataVencimento.getDate() + (condicao.intervalo * (i - 1)));
            contaPagar.parcelas.push({
              numero: i,
              descricao: `Parcela ${i}/${condicao.numParcelas}`,
              dataVencimento: Timestamp.fromDate(dataVencimento),
              valor: valorParcela,
              valorPago: 0,
              status: 'PENDENTE',
              pagamentos: []
            });
          }
        }

        // Remover campos undefined do objeto contaPagar antes de salvar
        Object.keys(contaPagar).forEach(key => {
          if (contaPagar[key] === undefined) {
            delete contaPagar[key];
          }
        });
        await addDoc(collection(db, "contasAPagar"), contaPagar);

        alert('Pedido criado com sucesso e conta a pagar gerada automaticamente!');
        closeModal('orderModal');
      } catch (error) {
        console.error("Erro ao criar pedido:", error);
        alert("Erro ao criar pedido: " + error.message);
      }
    };

    window.viewOrder = async function(orderId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('Pedido não encontrado');
          return;
        }

        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };
        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
        const centroCusto = centrosCusto.find(cc => cc.id === pedido.centroCustoId);

        // Debug: verificar estrutura dos itens
        console.log('🔍 Debug - Estrutura do pedido:', pedido);
        if (pedido.itens && pedido.itens.length > 0) {
          console.log('🔍 Debug - Primeiro item:', pedido.itens[0]);
          console.log('🔍 Debug - Campos disponíveis:', Object.keys(pedido.itens[0]));
        }

        // Criar modal de detalhes
        const detailsModal = document.createElement('div');
        detailsModal.className = 'modal';
        detailsModal.innerHTML = `
          <div class="modal-content" style="max-width: 1000px; max-height: 90vh; overflow-y: auto;">
            <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
            <h2>📋 Detalhes do Pedido ${pedido.numero}</h2>

            <!-- Informações Gerais -->
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 25px; background: #f8f9fa; padding: 20px; border-radius: 8px;">
              <div>
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 5px;">
                  <i class="fas fa-info-circle"></i> Informações Básicas
                </h3>
                <p><strong>Número:</strong> ${pedido.numero}</p>
                <p><strong>Status:</strong> <span class="status-badge status-${pedido.status?.toLowerCase() || 'pendente'}">${pedido.status || 'N/A'}</span></p>
                <p><strong>Data Criação:</strong> ${pedido.dataCriacao ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'}</p>
                <p><strong>Data Entrega:</strong> ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString('pt-BR') : 'Não definida'}</p>
              </div>

              <div>
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 5px;">
                  <i class="fas fa-building"></i> Fornecedor
                </h3>
                <p><strong>Razão Social:</strong> ${fornecedor?.razaoSocial || 'N/A'}</p>
                <p><strong>CNPJ:</strong> ${fornecedor?.cnpj || 'N/A'}</p>
                <p><strong>Email:</strong> ${fornecedor?.email || 'N/A'}</p>
                <p><strong>Telefone:</strong> ${fornecedor?.telefone || 'N/A'}</p>
              </div>

              <div>
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 5px;">
                  <i class="fas fa-chart-line"></i> Valores
                </h3>
                <p><strong>Valor Total:</strong> <span style="color: #28a745; font-size: 1.2em; font-weight: bold;">R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</span></p>
                <p><strong>Centro de Custo:</strong> ${centroCusto?.nome || 'N/A'}</p>
                <p><strong>Condição Pagamento:</strong> ${pedido.condicaoPagamento || 'N/A'}</p>
                <p><strong>Observações:</strong> ${pedido.observacoes || 'Nenhuma'}</p>
              </div>
            </div>

            <!-- Barra de Progresso -->
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                <span style="font-weight: bold; color: #495057;">Progresso do Pedido</span>
                <span style="font-weight: bold; color: #6f42c1;">${calcularProgressoEntrega(pedido)}% concluído</span>
              </div>
              <div style="width: 100%; background-color: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                <div style="width: ${calcularProgressoEntrega(pedido)}%; background: linear-gradient(90deg, #6f42c1, #e83e8c); height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
              </div>
            </div>

            <!-- Itens do Pedido -->
            <div style="margin-bottom: 25px;">
              <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 5px;">
                <i class="fas fa-list"></i> Itens do Pedido (${pedido.itens?.length || 0} itens)
              </h3>
              <div style="overflow-x: auto;">
                <table class="items-table" style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #6c757d; color: white;">
                      <th style="padding: 12px; text-align: left;">Código</th>
                      <th style="padding: 12px; text-align: left;">Descrição</th>
                      <th style="padding: 12px; text-align: center;">Qtd</th>
                      <th style="padding: 12px; text-align: center;">Unidade</th>
                      <th style="padding: 12px; text-align: right;">Valor Unit.</th>
                      <th style="padding: 12px; text-align: right;">Total</th>
                      <th style="padding: 12px; text-align: center;">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${(pedido.itens || []).map(item => {
                      // Buscar valor unitário em diferentes campos possíveis
                      const valorUnitario = parseFloat(
                        item.valorUnitario ||
                        item.valor ||
                        item.preco ||
                        item.precoUnitario ||
                        item.valorUnit ||
                        item.precoUnit ||
                        0
                      );
                      const quantidade = parseFloat(item.quantidade || item.qtd || 0);
                      const valorTotal = quantidade * valorUnitario;

                      return `
                        <tr style="border-bottom: 1px solid #dee2e6;">
                          <td style="padding: 10px; font-weight: bold;">${item.codigo || 'N/A'}</td>
                          <td style="padding: 10px;">${item.descricao || 'N/A'}</td>
                          <td style="padding: 10px; text-align: center; font-weight: bold;">${quantidade.toFixed(3)}</td>
                          <td style="padding: 10px; text-align: center;">${item.unidade || 'UN'}</td>
                          <td style="padding: 10px; text-align: right; font-weight: bold; color: ${valorUnitario > 0 ? '#495057' : '#dc3545'};">
                            R$ ${valorUnitario.toFixed(2)}
                            ${valorUnitario === 0 ? ' <small style="color: #dc3545;">(⚠️ Sem preço)</small>' : ''}
                          </td>
                          <td style="padding: 10px; text-align: right; font-weight: bold; color: ${valorTotal > 0 ? '#28a745' : '#dc3545'};">
                            R$ ${valorTotal.toFixed(2)}
                          </td>
                          <td style="padding: 10px; text-align: center;">
                            <span class="status-badge ${getItemStatusClass(item.status || 'pendente')}">
                              ${getItemStatusText(item.status || 'pendente')}
                            </span>
                          </td>
                        </tr>
                      `;
                    }).join('')}
                  </tbody>
                  <tfoot>
                    <tr style="background: #f8f9fa; font-weight: bold;">
                      <td colspan="5" style="padding: 12px; text-align: right; font-size: 1.1em;">TOTAL GERAL:</td>
                      <td style="padding: 12px; text-align: right; color: #28a745; font-size: 1.2em; font-weight: bold;">
                        R$ ${calcularValorTotalItens(pedido.itens).toFixed(2)}
                      </td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            <!-- Histórico de Atualizações -->
            ${pedido.atualizacoesEntrega && pedido.atualizacoesEntrega.length > 0 ? `
              <div style="margin-bottom: 25px;">
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 5px;">
                  <i class="fas fa-history"></i> Histórico de Entregas
                </h3>
                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                  ${pedido.atualizacoesEntrega.map(atualizacao => `
                    <div style="padding: 10px; border-bottom: 1px solid #f1f3f4;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: bold; color: #495057;">${atualizacao.tipo || 'Atualização'}</span>
                        <span style="color: #6c757d; font-size: 0.9em;">${atualizacao.data ? new Date(atualizacao.data.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}</span>
                      </div>
                      <p style="margin: 5px 0; color: #6c757d;">${atualizacao.observacoes || 'Sem observações'}</p>
                    </div>
                  `).join('')}
                </div>
              </div>
            ` : ''}

            <!-- Resumo Financeiro -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 25px; background: #e9ecef; padding: 15px; border-radius: 8px;">
              <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: bold; color: #28a745;">R$ ${calcularValorTotalItens(pedido.itens).toFixed(2)}</div>
                <div style="color: #6c757d; font-size: 0.9em;">Valor Total</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: bold; color: #17a2b8;">${pedido.itens?.length || 0}</div>
                <div style="color: #6c757d; font-size: 0.9em;">Itens</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: bold; color: #ffc107;">${calcularDiasRestantes(pedido.dataEntregaPrevista)}</div>
                <div style="color: #6c757d; font-size: 0.9em;">Dias para Entrega</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: bold; color: #6f42c1;">${calcularProgressoEntrega(pedido)}%</div>
                <div style="color: #6c757d; font-size: 0.9em;">Progresso</div>
              </div>
            </div>

            <!-- Botões de Ação -->
            <div style="display: flex; gap: 10px; justify-content: space-between; margin-top: 20px; padding-top: 20px; border-top: 2px solid #dee2e6;">
              <div style="display: flex; gap: 10px;">
                ${pedido.status === 'RASCUNHO' ? `
                  <button onclick="enviarParaAprovacao('${pedido.id}'); this.closest('.modal').remove();" class="btn btn-warning">
                    <i class="fas fa-paper-plane"></i> Enviar p/ Aprovação
                  </button>
                ` : ''}
                ${pedido.status === 'APROVADO' ? `
                  <button onclick="sendToSupplier('${pedido.id}'); this.closest('.modal').remove();" class="btn btn-success">
                    <i class="fas fa-envelope"></i> Enviar p/ Fornecedor
                  </button>
                ` : ''}
                ${pedido.status === 'CONFIRMADO_FORNECEDOR' ? `
                  <button onclick="receiveOrder('${pedido.id}'); this.closest('.modal').remove();" class="btn btn-primary">
                    <i class="fas fa-truck"></i> Receber Material
                  </button>
                ` : ''}
              </div>
              <div style="display: flex; gap: 10px;">
                <button onclick="trackOrder('${pedido.id}'); this.closest('.modal').remove();" class="btn btn-info">
                  <i class="fas fa-route"></i> Rastrear
                </button>
                <button onclick="printOrder('${pedido.id}')" class="btn btn-secondary">
                  <i class="fas fa-print"></i> Imprimir
                </button>
                <button onclick="window.open('relatorio_pc.html?id=${pedido.id}', '_blank')" class="btn btn-info">
                  <i class="fas fa-file-alt"></i> Relatório
                </button>
                <button onclick="this.closest('.modal').remove()" class="btn btn-primary">
                  <i class="fas fa-times"></i> Fechar
                </button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(detailsModal);
        detailsModal.style.display = 'block';

      } catch (error) {
        console.error("Erro ao abrir detalhes:", error);
        alert("Erro ao abrir detalhes do pedido");
      }
    };

    // Funções auxiliares para formatação de status dos itens
    function getItemStatusClass(status) {
      const statusMap = {
        'pendente': 'status-pendente',
        'parcial': 'status-parcial',
        'entregue': 'status-entregue',
        'cancelado': 'status-cancelado'
      };
      return statusMap[status.toLowerCase()] || 'status-pendente';
    }

    function getItemStatusText(status) {
      const statusMap = {
        'pendente': 'Pendente',
        'parcial': 'Parcial',
        'entregue': 'Entregue',
        'cancelado': 'Cancelado'
      };
      return statusMap[status.toLowerCase()] || 'Pendente';
    }

    // Função para calcular progresso de entrega
    function calcularProgressoEntrega(pedido) {
      if (!pedido.itens || pedido.itens.length === 0) return 0;

      const totalItens = pedido.itens.length;
      const itensEntregues = pedido.itens.filter(item =>
        item.status === 'entregue' || item.status === 'recebido'
      ).length;

      return Math.round((itensEntregues / totalItens) * 100);
    }

    // Função para calcular valor total dos itens
    function calcularValorTotalItens(itens) {
      if (!itens || !Array.isArray(itens)) return 0;

      return itens.reduce((total, item) => {
        // Buscar valor unitário em diferentes campos possíveis
        const valorUnitario = parseFloat(
          item.valorUnitario ||
          item.valor ||
          item.preco ||
          item.precoUnitario ||
          item.valorUnit ||
          item.precoUnit ||
          0
        );
        const quantidade = parseFloat(item.quantidade || item.qtd || 0);
        return total + (quantidade * valorUnitario);
      }, 0);
    }



    // FUNÇÃO PARA ENVIAR PEDIDO PARA APROVAÇÃO
    window.enviarParaAprovacao = async function(orderId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = pedidoSnap.data();

        // Validar se pode ser enviado para aprovação
        if (pedido.status !== 'RASCUNHO') {
          alert('⚠️ Apenas pedidos em rascunho podem ser enviados para aprovação');
          return;
        }

        // Validações obrigatórias
        if (!pedido.itens || pedido.itens.length === 0) {
          alert('❌ Pedido deve ter pelo menos um item');
          return;
        }

        if (!pedido.fornecedorId) {
          alert('❌ Fornecedor deve ser selecionado');
          return;
        }

        const confirmacao = confirm(`📋 Enviar Pedido para Aprovação?\n\nNúmero: ${pedido.numero}\nValor: R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}\nFornecedor: ${pedido.fornecedor || 'N/A'}\n\nApós enviar, o pedido não poderá mais ser editado até ser aprovado ou rejeitado.`);

        if (!confirmacao) return;

        // Atualizar status para aguardando aprovação
        await updateDoc(pedidoRef, {
          status: 'AGUARDANDO_APROVACAO',
          statusAprovacao: 'AGUARDANDO',
          'processoCompra.etapaAtual': 'AGUARDANDO_APROVACAO',
          'processoCompra.dataEnvioAprovacao': Timestamp.now(),
          dataEnvioAprovacao: Timestamp.now(),
          ultimaAtualizacao: Timestamp.now(),
          historico: [
            ...(pedido.historico || []),
            {
              data: Timestamp.now(),
              acao: 'ENVIO_APROVACAO',
              usuario: currentUser.nome,
              detalhes: 'Pedido enviado para aprovação'
            }
          ]
        });

        alert('✅ Pedido enviado para aprovação com sucesso!');
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error('❌ Erro ao enviar para aprovação:', error);
        alert('❌ Erro ao enviar pedido para aprovação: ' + error.message);
      }
    };

    // FUNÇÃO PARA ENVIAR PEDIDO PARA FORNECEDOR (após aprovação)
    window.enviarParaFornecedor = async function(orderId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = pedidoSnap.data();

        // Validar se pode ser enviado para fornecedor
        if (pedido.status !== 'APROVADO') {
          alert('⚠️ Apenas pedidos aprovados podem ser enviados ao fornecedor');
          return;
        }

        const confirmacao = confirm(`📧 Enviar Pedido para Fornecedor?\n\nNúmero: ${pedido.numero}\nFornecedor: ${pedido.fornecedor || 'N/A'}\nValor: R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}\n\nO fornecedor receberá o pedido por email.`);

        if (!confirmacao) return;

        // Atualizar status para enviado ao fornecedor
        await updateDoc(pedidoRef, {
          status: 'ENVIADO_FORNECEDOR',
          'processoCompra.etapaAtual': 'ENVIADO_FORNECEDOR',
          'processoCompra.dataEnvioFornecedor': Timestamp.now(),
          dataEnvioFornecedor: Timestamp.now(),
          ultimaAtualizacao: Timestamp.now(),
          historico: [
            ...(pedido.historico || []),
            {
              data: Timestamp.now(),
              acao: 'ENVIO_FORNECEDOR',
              usuario: currentUser.nome,
              detalhes: 'Pedido enviado para o fornecedor'
            }
          ]
        });

        // Aqui você pode integrar com serviço de email
        // await enviarEmailFornecedor(pedido);

        alert('✅ Pedido enviado para o fornecedor com sucesso!');
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error('❌ Erro ao enviar para fornecedor:', error);
        alert('❌ Erro ao enviar pedido para fornecedor: ' + error.message);
      }
    };



    // CORREÇÃO: Função de aprovação removida - aprovações devem ser feitas na Gestão Integrada
    window.approveOrder = function(orderId) {
      alert('📋 Aprovações Centralizadas\n\nAs aprovações de pedidos foram centralizadas na tela de Gestão de Compras Integrada.\n\nClique no botão "📋 Ir para Gestão Integrada" para aprovar este pedido.');
    };

    // ✅ FUNÇÃO DE CANCELAMENTO JÁ IMPLEMENTADA ACIMA COM MELHORIAS DE SEGURANÇA

    window.receiveOrder = async function(orderId) {
      try {
        // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
        if (!userPermissions.includes('pedidos_compra_receber') &&
            currentUser.nivel < 3 &&
            currentUser.id !== 'sistema' &&
            currentUser.id !== 'admin' &&
            currentUser.nome !== 'admin') {
          alert('❌ Você não tem permissão para receber pedidos de compra');
          return;
        }

        // ✅ VALIDAÇÃO DE DADOS DE ENTRADA
        if (!orderId || typeof orderId !== 'string') {
          alert('❌ ID do pedido inválido');
          return;
        }

        // ✅ VERIFICAR SE USUÁRIO ESTÁ AUTENTICADO
        if (!currentUser || !currentUser.nome) {
          alert('❌ Usuário não autenticado');
          return;
        }

        // ✅ BUSCAR DADOS DO PEDIDO COM VALIDAÇÃO
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };

        // ✅ VALIDAR STATUS DO PEDIDO
        if (pedido.status !== 'APROVADO') {
          alert('❌ Apenas pedidos aprovados podem ser recebidos');
          return;
        }

        if (pedido.status === 'RECEBIDO' || pedido.status === 'FINALIZADO') {
          alert('⚠️ Este pedido já foi recebido');
          return;
        }

        if (pedido.status === 'CANCELADO') {
          alert('❌ Não é possível receber pedidos cancelados');
          return;
        }

        // ✅ VALIDAR DADOS DO PEDIDO
        if (!pedido.itens || pedido.itens.length === 0) {
          alert('❌ Pedido não possui itens para recebimento');
          return;
        }

        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
        if (!fornecedor) {
          alert('❌ Fornecedor do pedido não encontrado');
          return;
        }

        // ✅ CONSULTAR PARÂMETROS DO SISTEMA COM VALIDAÇÃO
        const parametrosRef = doc(db, "parametros", "sistema");
        const parametrosSnap = await getDoc(parametrosRef);
        const parametros = parametrosSnap.exists() ? parametrosSnap.data() : {};

        const moduloQualidadeAtivo = parametros.moduloQualidadeAtivo || false;
        const inspecaoRecebimento = parametros.inspecaoRecebimento || false;

        // TODO: Registrar início do processo de recebimento
        console.log('📦 Iniciando recebimento:', orderId, 'por:', currentUser.nome);

        // Mostrar modal de confirmação com informações do fluxo
        showReceivingConfirmationModal(pedido, fornecedor, moduloQualidadeAtivo, inspecaoRecebimento);

      } catch (error) {
        console.error("❌ Erro ao preparar recebimento:", error);

        // TODO: Registrar erro na auditoria
        console.log('❌ Erro no recebimento:', orderId, error.message);

        alert("❌ Erro ao carregar dados do pedido: " + error.message);
      }
    };

    // Função para mostrar modal de confirmação do recebimento
    function showReceivingConfirmationModal(pedido, fornecedor, moduloQualidadeAtivo, inspecaoRecebimento) {
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.style.display = 'block';
      
      const fluxoQualidade = moduloQualidadeAtivo && inspecaoRecebimento;
      
      modal.innerHTML = `
        <div class="modal-content" style="max-width: 600px;">
          <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
          <h2>📦 Confirmar Recebimento de Material</h2>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3>📋 Dados do Pedido</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
              <div>
                <p><strong>Número:</strong> ${pedido.numero}</p>
                <p><strong>Fornecedor:</strong> ${fornecedor?.razaoSocial || 'N/A'}</p>
                <p><strong>Data Pedido:</strong> ${pedido.dataCriacao ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</p>
              </div>
              <div>
                <p><strong>Valor Total:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
                <p><strong>Status Atual:</strong> ${pedido.status}</p>
                <p><strong>Itens:</strong> ${pedido.itens?.length || 0} item(ns)</p>
              </div>
            </div>
          </div>

          <div style="background: ${fluxoQualidade ? '#fff3e0' : '#e8f5e9'}; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid ${fluxoQualidade ? '#ff9800' : '#4caf50'};">
            <h3>🔄 Fluxo de Recebimento</h3>
            ${fluxoQualidade ? `
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                <span style="font-size: 24px;">🔍</span>
                <div>
                  <strong>Módulo de Qualidade ATIVO</strong>
                  <p style="margin: 5px 0; color: #666;">Material será direcionado para inspeção de qualidade</p>
                </div>
              </div>
              <div style="background: white; padding: 15px; border-radius: 5px;">
                <h4>📋 Processo com Inspeção:</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                  <li>✅ Recebimento com dados da NF</li>
                  <li>🔍 Inspeção de qualidade obrigatória</li>
                  <li>📊 Controle de conformidade</li>
                  <li>✅ Aprovação para entrada no estoque</li>
                </ul>
                <p style="margin-top: 10px; color: #f57c00;"><strong>⚠️ Material ficará em quarentena até aprovação</strong></p>
              </div>
            ` : `
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                <span style="font-size: 24px;">📦</span>
                <div>
                  <strong>Recebimento Direto</strong>
                  <p style="margin: 5px 0; color: #666;">Material será recebido diretamente no estoque</p>
                </div>
              </div>
              <div style="background: white; padding: 15px; border-radius: 5px;">
                <h4>📋 Processo Direto:</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                  <li>✅ Recebimento com dados da NF</li>
                  <li>📊 Validação de quantidade e preços</li>
                  <li>💰 Rateio automático de frete</li>
                  <li>📈 Entrada direta no estoque</li>
                </ul>
                <p style="margin-top: 10px; color: #388e3c;"><strong>✅ Material disponível imediatamente</strong></p>
              </div>
            `}
          </div>

          <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h4>⚙️ Configurações do Sistema</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
              <div>
                <span style="font-weight: bold;">Módulo de Qualidade:</span>
                <span style="color: ${moduloQualidadeAtivo ? '#4caf50' : '#f44336'}; font-weight: bold;">
                  ${moduloQualidadeAtivo ? '✅ ATIVO' : '❌ INATIVO'}
                </span>
              </div>
              <div>
                <span style="font-weight: bold;">Inspeção de Recebimento:</span>
                <span style="color: ${inspecaoRecebimento ? '#4caf50' : '#f44336'}; font-weight: bold;">
                  ${inspecaoRecebimento ? '✅ OBRIGATÓRIA' : '❌ OPCIONAL'}
                </span>
              </div>
            </div>
            <p style="margin-top: 10px; font-size: 12px; color: #666;">
              <strong>💡 Dica:</strong> Configurações podem ser alteradas em 
              <a href="config_parametros.html" target="_blank" style="color: #1976d2;">Configurações > Parâmetros do Sistema</a>
            </p>
          </div>

          <div style="display: flex; gap: 15px; justify-content: flex-end;">
            <button onclick="this.closest('.modal').remove()" class="btn btn-secondary">
              ❌ Cancelar
            </button>
            <button onclick="processReceivingFlow('${pedido.id}', ${fluxoQualidade})" class="btn btn-primary">
              📦 Confirmar Recebimento
            </button>
          </div>
        </div>
      `;
      
      document.body.appendChild(modal);
    }

    // Função para processar o fluxo de recebimento baseado na configuração
    window.processReceivingFlow = async function(orderId, requiresInspection) {
      try {
        // ✅ VALIDAÇÃO RIGOROSA DE PERMISSÕES
        if (!userPermissions.includes('pedidos_compra_receber') &&
            currentUser.nivel < 3 &&
            currentUser.id !== 'sistema' &&
            currentUser.id !== 'admin' &&
            currentUser.nome !== 'admin') {
          alert('❌ Você não tem permissão para processar recebimentos');
          return;
        }

        // ✅ VALIDAÇÃO DE DADOS DE ENTRADA
        if (!orderId || typeof orderId !== 'string') {
          alert('❌ ID do pedido inválido');
          return;
        }

        if (typeof requiresInspection !== 'boolean') {
          alert('❌ Parâmetro de inspeção inválido');
          return;
        }

        // Fechar modal de confirmação
        const modal = document.querySelector('.modal');
        if (modal) {
          modal.remove();
        }

        // ✅ BUSCAR DADOS DO PEDIDO COM VALIDAÇÃO
        const pedidoRef = doc(db, "pedidosCompra", orderId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('❌ Pedido não encontrado');
          return;
        }

        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };

        // ✅ VALIDAR STATUS DO PEDIDO NOVAMENTE
        if (pedido.status !== 'APROVADO') {
          alert('❌ Status do pedido foi alterado. Apenas pedidos aprovados podem ser recebidos');
          return;
        }

        // ✅ VALIDAR DADOS ESSENCIAIS
        if (!pedido.itens || pedido.itens.length === 0) {
          alert('❌ Pedido não possui itens válidos');
          return;
        }

        // ✅ PREPARAR DADOS SEGUROS PARA LOCALSTORAGE
        const pedidoData = {
          id: orderId,
          numero: pedido.numero,
          fornecedorId: pedido.fornecedorId,
          fornecedor: pedido.fornecedor,
          valorTotal: pedido.valorTotal,
          itens: pedido.itens,
          dataCriacao: pedido.dataCriacao,
          status: pedido.status,
          requiresInspection: requiresInspection,
          processedBy: currentUser.nome,
          processedById: currentUser.id,
          processedAt: new Date().toISOString()
        };

        localStorage.setItem('selectedOrderForReceiving', JSON.stringify(pedidoData));
        localStorage.setItem('receivingSource', 'pedidos_compra');

        // TODO: Registrar início do fluxo de recebimento
        console.log('🔄 Processando fluxo de recebimento:', orderId, 'inspeção:', requiresInspection, 'por:', currentUser.nome);

        if (requiresInspection) {
          // ✅ FLUXO COM INSPEÇÃO - VALIDAÇÃO ADICIONAL
          console.log('🔍 Redirecionando para o módulo de Inspeção de Qualidade...');

          const newWindow = window.open('inspecao_recebimento.html', '_blank');
          if (!newWindow) {
            alert('❌ Não foi possível abrir a tela de inspeção. Verifique se o bloqueador de pop-ups está desabilitado');
            return;
          }
          newWindow.focus();
        } else {
          // ✅ FLUXO DIRETO - RECEBIMENTO SEM INSPEÇÃO
          console.log('📦 Redirecionando para recebimento direto...');

          const newWindow = window.open('recebimento_materiais.html', '_blank');
          if (!newWindow) {
            alert('❌ Não foi possível abrir a tela de recebimento. Verifique se o bloqueador de pop-ups está desabilitado');
            return;
          }
          newWindow.focus();
        }

      } catch (error) {
        console.error("❌ Erro ao processar fluxo de recebimento:", error);

        // TODO: Registrar erro na auditoria
        console.log('❌ Erro no fluxo de recebimento:', orderId, error.message);

        alert("❌ Erro ao processar recebimento: " + error.message);
      }
    };

    function showReceivingModal(pedido, fornecedor) {
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.id = 'receivingModal';
      modal.style.display = 'block';

      const hoje = new Date().toISOString().split('T')[0];

      modal.innerHTML = `
        <div class="modal-content" style="max-width: 95%; max-height: 90vh; overflow-y: auto;">
          <span class="close-button" onclick="closeModal('receivingModal')">&times;</span>
          <h2><i class="fas fa-truck"></i> Recebimento de Material - ${pedido.numero}</h2>

          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Preencha os dados da nota fiscal e confira as quantidades recebidas.
          </div>

          <form id="receivingForm" onsubmit="processReceiving(event, '${pedido.id}')">
            <!-- Dados da Nota Fiscal -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3><i class="fas fa-file-invoice"></i> Dados da Nota Fiscal</h3>
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                <div class="form-group">
                  <label><strong>Número da NF:</strong></label>
                  <input type="text" id="numeroNF" required placeholder="Ex: 123456" style="width: 100%; padding: 8px;">
                </div>
                <div class="form-group">
                  <label><strong>Série:</strong></label>
                  <input type="text" id="serieNF" placeholder="Ex: 1" style="width: 100%; padding: 8px;">
                </div>
                <div class="form-group">
                  <label><strong>Data da NF:</strong></label>
                  <input type="date" id="dataNF" value="${hoje}" required style="width: 100%; padding: 8px;">
                </div>
              </div>

              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 15px;">
                <div class="form-group">
                  <label><strong>Valor Total NF:</strong></label>
                  <input type="number" id="valorTotalNF" step="0.01" placeholder="0.00" style="width: 100%; padding: 8px;">
                </div>
                <div class="form-group">
                  <label><strong>Tipo de Frete:</strong></label>
                  <select id="tipoFrete" onchange="toggleFreteFields()" style="width: 100%; padding: 8px;">
                    <option value="CIF">CIF (Fornecedor paga)</option>
                    <option value="FOB">FOB (Comprador paga)</option>
                  </select>
                </div>
                <div class="form-group">
                  <label><strong>Valor do Frete:</strong></label>
                  <input type="number" id="valorFrete" step="0.01" placeholder="0.00" style="width: 100%; padding: 8px;">
                </div>
              </div>

              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                <div class="form-group">
                  <label><strong>Transportadora:</strong></label>
                  <input type="text" id="transportadora" placeholder="Nome da transportadora" style="width: 100%; padding: 8px;">
                </div>
                <div class="form-group">
                  <label><strong>Chave NFe:</strong></label>
                  <input type="text" id="chaveNFe" placeholder="44 dígitos da chave de acesso" maxlength="44" style="width: 100%; padding: 8px;">
                </div>
              </div>
            </div>

            <!-- Dados do Fornecedor -->
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3><i class="fas fa-building"></i> Fornecedor</h3>
              <div style="display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 15px;">
                <div>
                  <p><strong>Razão Social:</strong> ${fornecedor?.razaoSocial || 'N/A'}</p>
                  <p><strong>CNPJ:</strong> ${fornecedor?.cnpj || 'N/A'}</p>
                </div>
                <div>
                  <p><strong>Pedido:</strong> ${pedido.numero}</p>
                  <p><strong>Data Pedido:</strong> ${pedido.dataCriacao ? new Date(pedido.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <p><strong>Valor Pedido:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
                  <p><strong>Status:</strong> ${pedido.status}</p>
                </div>
              </div>
            </div>

            <!-- Itens do Pedido -->
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3><i class="fas fa-boxes"></i> Itens para Recebimento</h3>
              <div class="table-container">
                <table class="table table-striped" id="receivingItemsTable">
                  <thead>
                    <tr>
                      <th>Código</th>
                      <th>Descrição</th>
                      <th>Qtd Pedida</th>
                      <th>Qtd Recebida</th>
                      <th>Unidade</th>
                      <th>Preço Unit.</th>
                      <th>Total Item</th>
                      <th>Observações</th>
                    </tr>
                  </thead>
                  <tbody>
      `;

      // Adicionar itens do pedido
      pedido.itens.forEach((item, index) => {
        modal.innerHTML += `
                    <tr>
                      <td><strong>${item.codigo}</strong></td>
                      <td>${item.descricao}</td>
                      <td style="text-align: center;">${item.quantidade}</td>
                      <td style="text-align: center;">
                        <input type="number"
                               id="qtdRecebida_${index}"
                               value="${item.quantidade}"
                               min="0"
                               max="${item.quantidade}"
                               step="0.001"
                               onchange="calculateItemReceiving(${index})"
                               style="width: 80px; text-align: center; padding: 4px;">
                      </td>
                      <td style="text-align: center;">${item.unidade}</td>
                      <td style="text-align: right;">R$ ${item.valorUnitario?.toFixed(2) || '0,00'}</td>
                      <td style="text-align: right;">
                        <span id="totalItem_${index}">R$ ${(item.valorUnitario * item.quantidade)?.toFixed(2) || '0,00'}</span>
                      </td>
                      <td>
                        <input type="text"
                               id="obsItem_${index}"
                               placeholder="Observações do item"
                               style="width: 100%; padding: 4px;">
                      </td>
                    </tr>
        `;
      });

      modal.innerHTML += `
                  </tbody>
                  <tfoot style="background: #f8f9fa; font-weight: bold;">
                    <tr>
                      <td colspan="6" style="text-align: right;">TOTAL RECEBIDO:</td>
                      <td style="text-align: right;" id="totalRecebido">R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            <!-- Observações Gerais -->
            <div class="form-group">
              <label><strong>Observações do Recebimento:</strong></label>
              <textarea id="observacoesRecebimento"
                        rows="3"
                        placeholder="Observações gerais sobre o recebimento..."
                        style="width: 100%; padding: 8px; resize: vertical;"></textarea>
            </div>

            <!-- Botões -->
            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button type="button" class="btn btn-secondary" onclick="closeModal('receivingModal')">
                <i class="fas fa-times"></i> Cancelar
              </button>
              <div>
                <button type="button" class="btn btn-warning" onclick="savePartialReceiving('${pedido.id}')">
                  <i class="fas fa-save"></i> Salvar Parcial
                </button>
                <button type="submit" class="btn btn-success">
                  <i class="fas fa-check"></i> Confirmar Recebimento
                </button>
              </div>
            </div>
          </form>
        </div>
      `;

      document.body.appendChild(modal);
    }

    // Função para calcular total do item no recebimento
    window.calculateItemReceiving = function(itemIndex) {
      const qtdInput = document.getElementById(`qtdRecebida_${itemIndex}`);
      const totalSpan = document.getElementById(`totalItem_${itemIndex}`);

      if (!qtdInput || !totalSpan) return;

      const qtdRecebida = parseFloat(qtdInput.value) || 0;
      const precoUnitario = parseFloat(qtdInput.dataset.precoUnitario) || 0;
      const total = qtdRecebida * precoUnitario;

      totalSpan.textContent = `R$ ${total.toFixed(2)}`;

      // Recalcular total geral
      calculateTotalReceiving();
    };

    // Função para calcular total geral do recebimento
    function calculateTotalReceiving() {
      const totalSpans = document.querySelectorAll('[id^="totalItem_"]');
      let totalGeral = 0;

      totalSpans.forEach(span => {
        const valor = parseFloat(span.textContent.replace('R$ ', '').replace(',', '.')) || 0;
        totalGeral += valor;
      });

      const totalRecebidoSpan = document.getElementById('totalRecebido');
      if (totalRecebidoSpan) {
        totalRecebidoSpan.textContent = `R$ ${totalGeral.toFixed(2)}`;
      }
    }

    // Função para alternar campos de frete
    window.toggleFreteFields = function() {
      const tipoFrete = document.getElementById('tipoFrete').value;
      const valorFreteInput = document.getElementById('valorFrete');

      if (tipoFrete === 'CIF') {
        valorFreteInput.placeholder = 'Frete já incluído no valor dos produtos';
        valorFreteInput.value = '0.00';
      } else {
        valorFreteInput.placeholder = 'Valor do frete a ser pago';
      }
    };

    // Função para processar recebimento completo
    window.processReceiving = async function(event, pedidoId) {
      event.preventDefault();

      try {
        const numeroNF = document.getElementById('numeroNF').value;
        const serieNF = document.getElementById('serieNF').value;
        const dataNF = document.getElementById('dataNF').value;
        const valorTotalNF = parseFloat(document.getElementById('valorTotalNF').value) || 0;
        const tipoFrete = document.getElementById('tipoFrete').value;
        const valorFrete = parseFloat(document.getElementById('valorFrete').value) || 0;
        const transportadora = document.getElementById('transportadora').value;
        const chaveNFe = document.getElementById('chaveNFe').value;
        const observacoesRecebimento = document.getElementById('observacoesRecebimento').value;

        if (!numeroNF || !dataNF) {
          alert('Preencha os dados obrigatórios da nota fiscal!');
          return;
        }

        // Coletar dados dos itens recebidos
        const itensRecebidos = [];
        const pedidoRef = doc(db, "pedidosCompra", pedidoId);
        const pedidoSnap = await getDoc(pedidoRef);
        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };

        let totalRecebido = 0;
        let recebimentoCompleto = true;

        pedido.itens.forEach((item, index) => {
          const qtdRecebida = parseFloat(document.getElementById(`qtdRecebida_${index}`).value) || 0;
          const obsItem = document.getElementById(`obsItem_${index}`).value;

          if (qtdRecebida > 0) {
            const valorTotalItem = qtdRecebida * (item.valorUnitario || 0);
            totalRecebido += valorTotalItem;

            itensRecebidos.push({
              codigo: item.codigo,
              descricao: item.descricao,
              quantidadePedida: item.quantidade,
              quantidadeRecebida: qtdRecebida,
              unidade: item.unidade,
              valorUnitario: item.valorUnitario || 0,
              valorTotal: valorTotalItem,
              observacoes: obsItem
            });

            // Verificar se recebimento será completo considerando recebimentos anteriores
            const quantidadeJaRecebida = item.quantidadeRecebida || 0;
            const quantidadeTotalRecebida = quantidadeJaRecebida + qtdRecebida;
            if (quantidadeTotalRecebida < item.quantidade) {
              recebimentoCompleto = false;
            }
          } else {
            // Se não recebeu nada neste item, verificar se já estava totalmente recebido
            const quantidadeJaRecebida = item.quantidadeRecebida || 0;
            if (quantidadeJaRecebida < item.quantidade) {
              recebimentoCompleto = false;
            }
          }
        });

        if (itensRecebidos.length === 0) {
          alert('Informe pelo menos um item recebido!');
          return;
        }

        // Criar registro de recebimento
        const recebimentoData = {
          pedidoId: pedidoId,
          numeroPedido: pedido.numero,
          fornecedorId: pedido.fornecedorId,
          notaFiscal: {
            numero: numeroNF,
            serie: serieNF,
            data: Timestamp.fromDate(new Date(dataNF)),
            valorTotal: valorTotalNF,
            chaveNFe: chaveNFe
          },
          frete: {
            tipo: tipoFrete,
            valor: valorFrete,
            transportadora: transportadora
          },
          itens: itensRecebidos,
          valorTotalRecebido: totalRecebido,
          tipoRecebimento: recebimentoCompleto ? 'COMPLETO' : 'PARCIAL',
          dataRecebimento: Timestamp.now(),
          recebidoPor: currentUser.nome,
          observacoes: observacoesRecebimento,
          status: 'RECEBIDO'
        };

        // Salvar recebimento
        await addDoc(collection(db, "recebimentoMateriais"), recebimentoData);

        // Atualizar quantidades recebidas nos itens do pedido
        const itensAtualizados = pedido.itens.map((item, index) => {
          const itemRecebido = itensRecebidos.find(ir => ir.codigo === item.codigo);
          if (itemRecebido) {
            return {
              ...item,
              quantidadeRecebida: (item.quantidadeRecebida || 0) + itemRecebido.quantidadeRecebida
            };
          }
          return item;
        });

        // Atualizar status do pedido
        const novoStatus = recebimentoCompleto ? 'RECEBIDO' : 'PARCIALMENTE_RECEBIDO';
        await updateDoc(pedidoRef, {
          status: novoStatus,
          dataRecebimento: Timestamp.now(),
          recebidoPor: currentUser.nome,
          itens: itensAtualizados, // Atualizar itens com quantidades recebidas
          ultimoRecebimento: {
            data: Timestamp.now(),
            numeroNF: numeroNF,
            valorRecebido: totalRecebido,
            tipo: recebimentoCompleto ? 'COMPLETO' : 'PARCIAL'
          },
          historico: [
            ...(pedido.historico || []),
            {
              data: Timestamp.now(),
              acao: recebimentoCompleto ? 'RECEBIMENTO_COMPLETO' : 'RECEBIMENTO_PARCIAL',
              usuario: currentUser.nome,
              detalhes: `NF: ${numeroNF} - Valor: R$ ${totalRecebido.toFixed(2)}`
            }
          ]
        });

        // Processar entrada no estoque (com frete rateado)
        await processarEntradaEstoque(itensRecebidos, valorFrete, tipoFrete, recebimentoData);

        alert(`Recebimento ${recebimentoCompleto ? 'completo' : 'parcial'} processado com sucesso!\nNF: ${numeroNF}\nValor: R$ ${totalRecebido.toFixed(2)}`);

        closeModal('receivingModal');
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error("Erro ao processar recebimento:", error);
        alert("Erro ao processar recebimento: " + error.message);
      }
    };

    // Função para salvar recebimento parcial
    window.savePartialReceiving = async function(pedidoId) {
      if (!confirm('Salvar como recebimento parcial? O pedido continuará aberto para próximos recebimentos.')) {
        return;
      }

      // Usar a mesma lógica do recebimento completo
      const form = document.getElementById('receivingForm');
      const event = { preventDefault: () => {} };
      await processReceiving(event, pedidoId);
    };

    // Função para processar entrada no estoque com rateio de frete
    async function processarEntradaEstoque(itensRecebidos, valorFrete, tipoFrete, recebimentoData) {
      try {
        // Verificar parâmetros do sistema
        const parametrosDoc = await getDoc(doc(db, "parametros", "sistema"));
        const params = parametrosDoc.exists() ? parametrosDoc.data() : {};
        const requireInspecao = params.inspecaoRecebimento || false;

        // Calcular valor total dos itens para rateio do frete
        const valorTotalItens = itensRecebidos.reduce((sum, item) => sum + item.valorTotal, 0);

        for (const item of itensRecebidos) {
          const produto = produtos.find(p => p.codigo === item.codigo);
          if (!produto) {
            console.warn(`Produto não encontrado: ${item.codigo}`);
            continue;
          }

          // Calcular frete rateado por item (proporcional ao valor)
          let freteRateado = 0;
          if (valorFrete > 0 && valorTotalItens > 0) {
            freteRateado = (item.valorTotal / valorTotalItens) * valorFrete;
          }

          // Custo unitário final (incluindo frete rateado)
          const custoUnitarioFinal = item.valorUnitario + (freteRateado / item.quantidadeRecebida);
          const custoTotalFinal = custoUnitarioFinal * item.quantidadeRecebida;

          if (requireInspecao) {
            // Enviar para estoque de qualidade/inspeção
            await addDoc(collection(db, "estoqueQualidade"), {
              recebimentoId: recebimentoData.id,
              pedidoId: recebimentoData.pedidoId,
              produtoId: produto.id,
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: item.quantidadeRecebida,
              unidade: item.unidade,
              custoUnitario: custoUnitarioFinal,
              custoTotal: custoTotalFinal,
              freteRateado: freteRateado,
              notaFiscal: recebimentoData.notaFiscal,
              status: 'PENDENTE_INSPECAO',
              dataEntrada: Timestamp.now(),
              origem: `Recebimento NF ${recebimentoData.notaFiscal.numero}`,
              observacoes: item.observacoes
            });
          } else {
            // Entrada direta no estoque
            await addDoc(collection(db, "movimentacaoEstoque"), {
              tipo: 'ENTRADA',
              subtipo: 'COMPRA',
              produtoId: produto.id,
              codigo: item.codigo,
              descricao: item.descricao,
              quantidade: item.quantidadeRecebida,
              unidade: item.unidade,
              custoUnitario: custoUnitarioFinal,
              custoTotal: custoTotalFinal,
              freteRateado: freteRateado,
              armazemId: produto.armazemPadrao || 'GERAL',
              notaFiscal: recebimentoData.notaFiscal,
              pedidoId: recebimentoData.pedidoId,
              recebimentoId: recebimentoData.id,
              dataMovimentacao: Timestamp.now(),
              usuario: currentUser.nome,
              origem: `Recebimento NF ${recebimentoData.notaFiscal.numero}`,
              observacoes: item.observacoes
            });

            // Atualizar saldo do produto
            const produtoRef = doc(db, "produtos", produto.id);
            const produtoSnap = await getDoc(produtoRef);
            const produtoData = produtoSnap.data();

            const saldoAtual = produtoData.saldoEstoque || 0;
            const custoMedioAtual = produtoData.custoMedio || 0;

            // Calcular novo custo médio ponderado
            const valorEstoqueAtual = saldoAtual * custoMedioAtual;
            const valorNovaEntrada = custoTotalFinal;
            const novoSaldo = saldoAtual + item.quantidadeRecebida;
            const novoCustoMedio = novoSaldo > 0 ? (valorEstoqueAtual + valorNovaEntrada) / novoSaldo : 0;

            await updateDoc(produtoRef, {
              saldoEstoque: novoSaldo,
              custoMedio: novoCustoMedio,
              ultimaEntrada: {
                data: Timestamp.now(),
                quantidade: item.quantidadeRecebida,
                custoUnitario: custoUnitarioFinal,
                notaFiscal: recebimentoData.notaFiscal.numero
              },
              ultimaAtualizacao: Timestamp.now()
            });
          }
        }

        console.log(`✅ Entrada no estoque processada: ${itensRecebidos.length} itens`);
        if (valorFrete > 0) {
          console.log(`💰 Frete rateado: R$ ${valorFrete.toFixed(2)} (${tipoFrete})`);
        }

      } catch (error) {
        console.error("Erro ao processar entrada no estoque:", error);
        throw error;
      }
    }

    // Função para fechar modal
    window.closeModal = function(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.remove();
      }
    };

    window.openFromQuotationModal = function openFromQuotationModal() {
      if (!userPermissions.includes('pedidos_compra_criar')) {
        alert('Você não tem permissão para criar pedidos de compra.');
        return;
      }
      const select = document.getElementById('quotationSelect');
      select.innerHTML = '<option value="">Selecione a cotação...</option>';
      cotacoes
        .filter(c => c.status === 'APROVADA')
        .forEach(cotacao => {
          select.innerHTML += `<option value="${cotacao.id}">${cotacao.numero}</option>`;
        });
      document.getElementById('quotationOrderModal').style.display = 'block';
    };

    window.loadQuotationDetails = async function() {
      const cotacaoId = document.getElementById('quotationSelect').value;
      const detailsContainer = document.getElementById('quotationDetails');
      const supplierSelect = document.getElementById('quotationSupplierSelect');

      if (!cotacaoId) {
        detailsContainer.innerHTML = '';
        supplierSelect.innerHTML = '<option value="">Selecione o fornecedor...</option>';
        document.getElementById('quotationItemsContainer').innerHTML = '';
        return;
      }

      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      if (cotacao) {
        supplierSelect.innerHTML = '<option value="">Selecione o fornecedor...</option>';
        const respondedSuppliers = cotacao.fornecedores.filter(f => cotacao.respostas && cotacao.respostas[f]);
        respondedSuppliers.forEach(fornecedorId => {
          const fornecedor = fornecedores.find(f => f.id === fornecedorId);
          if (fornecedor) {
            supplierSelect.innerHTML += `
              <option value="${fornecedor.id}">${fornecedor.codigo} - ${fornecedor.razaoSocial}</option>
            `;
          }
        });

        detailsContainer.innerHTML = `
          <p><strong>Número:</strong> ${cotacao.numero}</p>
          <p><strong>Data:</strong> ${cotacao.dataCriacao && typeof cotacao.dataCriacao.seconds === 'number' ? new Date(cotacao.dataCriacao.seconds * 1000).toLocaleDateString() : ''}</p>
          <p><strong>Itens:</strong> ${cotacao.itens.length}</p>
          <p><strong>Valor Total Estimado:</strong> R$ ${cotacao.valorTotal?.toFixed(2) || '0,00'}</p>
        `;
      }
    };

    window.updateQuotationItems = function() {
      const cotacaoId = document.getElementById('quotationSelect').value;
      const fornecedorId = document.getElementById('quotationSupplierSelect').value;
      const container = document.getElementById('quotationItemsContainer');

      if (!cotacaoId || !fornecedorId) {
        container.innerHTML = '';
        return;
      }

      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      const resposta = cotacao.respostas?.[fornecedorId];
      if (!resposta) {
        container.innerHTML = '<p>Fornecedor ainda não respondeu à cotação.</p>';
        return;
      }

      let subtotal = 0;
      const itemsHtml = cotacao.itens.map((item, index) => {
        const precoUnitario = resposta.precos[index] || 0;
        const ipi = resposta.ipi[index] || 0;
        const icms = resposta.icms[index] || 0;
        const totalItem = precoUnitario * item.quantidade * (1 + ipi / 100) * (1 + icms / 100);
        subtotal += totalItem;
        return `
          <tr>
            <td>${item.codigo}</td>
            <td>${item.descricao}</td>
            <td>${item.quantidadeInterna || item.quantidade}</td>
            <td>${item.unidade}</td>
            <td>R$ ${precoUnitario.toFixed(2)}</td>
            <td>${ipi}%</td>
            <td>${icms}%</td>
            <td>R$ ${totalItem.toFixed(2)}</td>
          </tr>
        `;
      }).join('');

      container.innerHTML = `
        <table class="items-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Quantidade</th>
              <th>Unidade</th>
              <th>Preço Unit.</th>
              <th>IPI</th>
              <th>ICMS</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="7" style="text-align: right;"><strong>Subtotal:</strong></td>
              <td>R$ ${subtotal.toFixed(2)}</td>
            </tr>
            <tr>
              <td colspan="7" style="text-align: right;"><strong>Frete:</strong></td>
              <td>R$ ${(resposta.frete?.valor || 0).toFixed(2)}</td>
            </tr>
            <tr>
              <td colspan="7" style="text-align: right;"><strong>Desconto:</strong></td>
              <td>R$ ${(subtotal * (resposta.desconto || 0) / 100).toFixed(2)}</td>
            </tr>
            <tr>
              <td colspan="7" style="text-align: right;"><strong>Total Geral:</strong></td>
              <td>R$ ${(subtotal + (resposta.frete?.valor || 0) - (subtotal * (resposta.desconto || 0) / 100)).toFixed(2)}</td>
            </tr>
          </tfoot>
        </table>
        <div class="form-group">
          <label>Condições Comerciais:</label>
          <p><strong>Prazo de Entrega:</strong> ${resposta.prazoEntrega} dias</p>
          <p><strong>Condição de Pagamento:</strong> ${resposta.condicaoPagamento}</p>
          <p><strong>Validade da Proposta:</strong> ${resposta.validadeProposta} dias</p>
          <p><strong>Garantia:</strong> ${resposta.garantia || 0} meses</p>
          <p><strong>Observações:</strong> ${resposta.observacoes || '-'}</p>
        </div>
      `;
    };

    window.createOrderFromQuotation = async function() {
      const cotacaoId = document.getElementById('quotationSelect').value;
      const fornecedorId = document.getElementById('quotationSupplierSelect').value;

      if (!cotacaoId || !fornecedorId) {
        alert('Selecione uma cotação e um fornecedor.');
        return;
      }

      const cotacao = cotacoes.find(c => c.id === cotacaoId);
      const resposta = cotacao.respostas?.[fornecedorId];
      const fornecedor = fornecedores.find(f => f.id === fornecedorId);

      if (!cotacao || !resposta || !fornecedor) {
        alert('Dados inválidos para criar o pedido.');
        return;
      }

      // CORREÇÃO: Verificar se a cotação já tem pedido criado
      if (cotacao.pedidoCriado) {
        alert(`⚠️ Esta cotação já possui um pedido criado.\n\nCotação: ${cotacao.numero}\nPedido: ${cotacao.pedidoId || 'ID não disponível'}\n\nVerifique na lista de pedidos ou na gestão de compras.`);
        return;
      }

      try {
        const numeroSequencial = await getNextOrderNumber();
        let valorTotal = 0;
        const items = cotacao.itens.map((item, index) => {
          const precoUnitario = resposta.precos[index] || 0;
          const ipi = resposta.ipi[index] || 0;
          const icms = resposta.icms[index] || 0;
          const quantidade = item.quantidadeInterna || item.quantidade;
          const totalItem = precoUnitario * quantidade * (1 + ipi / 100) * (1 + icms / 100);
          valorTotal += totalItem;

          return {
            codigo: item.codigo,
            descricao: item.descricao,
            quantidade: quantidade,
            unidade: item.unidade,
            quantidadeConvertida: item.quantidadeConvertida || quantidade,
            valorUnitario: precoUnitario,
            ipi: ipi,
            icms: icms,
            valorTotal: totalItem
          };
        });

        // Adicionar frete e desconto ao valor total
        valorTotal += (resposta.frete?.valor || 0);
        valorTotal -= (valorTotal * (resposta.desconto || 0) / 100);

        const pedido = {
          numero: numeroSequencial,
          cotacaoId: cotacaoId,
          respostaFornecedorId: fornecedorId,
          fornecedorId: fornecedorId,
          condicaoPagamento: resposta.condicaoPagamento,
          dataEntregaPrevista: Timestamp.fromDate(new Date(Date.now() + (resposta.prazoEntrega * 24 * 60 * 60 * 1000))),
          centroCusto: cotacao.centroCusto || 'PRODUCAO',
          itens: items,
          valorTotal: valorTotal,
          observacoes: `Pedido gerado a partir da cotação ${cotacao.numero} - ${resposta.observacoes || ''}`,
          status: 'PENDENTE',
          dataCriacao: Timestamp.now(),
          criadoPor: currentUser.nome,
          workflowAprovacao: [
            { nivel: 1, cargo: 'Supervisor', status: 'PENDENTE' },
            { nivel: 2, cargo: 'Gerente', status: 'PENDENTE' },
            { nivel: 3, cargo: 'Diretor', status: valorTotal > 50000 ? 'PENDENTE' : 'NAO_REQUERIDO' }
          ],
          historico: [{ data: Timestamp.now(), acao: 'Criado a partir da cotação', usuario: currentUser.nome }]
        };

        // CORREÇÃO: Verificar se já existe pedido para esta cotação
        const pedidosExistentes = await getDocs(query(
          collection(db, "pedidosCompra"),
          where("cotacaoId", "==", cotacaoId)
        ));

        if (!pedidosExistentes.empty) {
          console.warn('⚠️ Já existe pedido para esta cotação:', cotacaoId);
          const pedidoExistente = pedidosExistentes.docs[0];
          throw new Error(`Já existe um pedido (${pedidoExistente.data().numero}) para esta cotação. Verifique na tela de gestão de compras.`);
        }

        // Criar pedido de compra
        const pedidoRef = await addDoc(collection(db, "pedidosCompra"), pedido);
        console.log('✅ Pedido criado com sucesso:', pedidoRef.id);

        // CORREÇÃO: Marcar cotação como "pedido criado" para evitar duplicação
        await updateDoc(doc(db, "cotacoes", cotacaoId), {
          pedidoCriado: true,
          pedidoId: pedidoRef.id,
          dataCriacaoPedido: Timestamp.now(),
          statusPedido: 'CRIADO'
        });
        console.log('✅ Cotação marcada como pedido criado');

        // Criar conta a pagar automaticamente (Padrão TOTVS)
        const condicaoPagamento = condicoesPagamento.find(c => c.id === resposta.condicaoPagamento);

        // Validar se a condição de pagamento existe
        if (!condicaoPagamento) {
          throw new Error('Condição de pagamento não encontrada. Verifique a cotação.');
        }

        const parcelas = gerarParcelasPagamento(valorTotal, condicaoPagamento);

        const contaPagar = {
          fornecedorId: fornecedorId,
          tipoDocumento: 'PEDIDO_COMPRA',
          numeroDocumento: numeroSequencial,
          dataEmissao: Timestamp.now(),
          dataVencimento: parcelas[0]?.dataVencimento || Timestamp.now(),
          valorTotal: valorTotal,
          valorPendente: valorTotal,
          valorPago: 0,
          condicaoPagamentoId: resposta.condicaoPagamento,
          centroCustoId: costCenterId,
          centroCusto: {
            codigo: centroCusto.codigo,
            descricao: centroCusto.descricao
          },
          naturezaOperacao: 'COMPRA_MATERIAL',
          observacoes: `Gerado automaticamente do Pedido de Compra ${numeroSequencial}`,
          pedidoCompraId: pedidoRef.id,
          status: 'ABERTO',
          dataCadastro: Timestamp.now(),
          parcelas: parcelas,
          historico: [{
            data: Timestamp.now(),
            acao: 'CRIADO',
            usuario: currentUser.nome,
            observacao: 'Conta criada automaticamente'
          }]
        };

        // Gerar parcelas baseado na condição de pagamento
        if (resposta.condicaoPagamento === 'AVISTA') {
          contaPagar.parcelas.push({
            numero: 1,
            descricao: 'À Vista',
            dataVencimento: Timestamp.now(),
            valor: valorTotal,
            valorPago: 0,
            status: 'PENDENTE',
            pagamentos: []
          });
        } else {
          const numParcelas = parseInt(resposta.condicaoPagamento);
          const valorParcela = valorTotal / numParcelas;
          for (let i = 1; i <= numParcelas; i++) {
            const dataVencimento = new Date();
            dataVencimento.setDate(dataVencimento.getDate() + (30 * i));
            contaPagar.parcelas.push({
              numero: i,
              descricao: `Parcela ${i}/${numParcelas}`,
              dataVencimento: Timestamp.fromDate(dataVencimento),
              valor: valorParcela,
              valorPago: 0,
              status: 'PENDENTE',
              pagamentos: []
            });
          }
        }

        // Remover campos undefined do objeto contaPagar antes de salvar
        Object.keys(contaPagar).forEach(key => {
          if (contaPagar[key] === undefined) {
            delete contaPagar[key];
          }
        });
        await addDoc(collection(db, "contasAPagar"), contaPagar);

        alert('Pedido criado com sucesso e conta a pagar gerada automaticamente!');
        closeModal('quotationOrderModal');
        await loadInitialData();
        applyFilters();
      } catch (error) {
        console.error("Erro ao criar pedido:", error);
        alert("Erro ao criar pedido: " + error.message);
      }
    };

    window.updateConvertedQty = function(select) {
      const row = select.closest('tr');
      const qtdInput = row.querySelector('.item-quantidade');
      const qtdConvertidaInput = row.querySelector('.item-qtd-convertida');
      const produtoId = qtdConvertidaInput.dataset.produtoId;
      const produto = produtos.find(p => p.id === produtoId);

      if (!produto || !qtdInput.value) return;

      const qtd = parseFloat(qtdInput.value);
      let qtdConvertida = qtd;

      if (select.value === produto.unidadeSecundaria && produto.fatorConversao) {
        qtdConvertida = qtd / produto.fatorConversao;
      } else if (select.value === produto.unidade) {
        qtdConvertida = qtd;
      }

      qtdConvertidaInput.value = qtdConvertida.toFixed(3);
      calculateItemTotal(qtdInput);
    };

    // Função para gerar parcelas de pagamento (Padrão TOTVS)
    function gerarParcelasPagamento(valorTotal, condicaoPagamento) {
      if (!condicaoPagamento) {
        return [{
          numero: 1,
          valor: valorTotal,
          dataVencimento: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
          status: 'ABERTO'
        }];
      }

      const parcelas = [];
      const numeroParcelas = condicaoPagamento.parcelas || 1;
      const valorParcela = valorTotal / numeroParcelas;
      const diasEntreParcelas = condicaoPagamento.diasEntreParcelas || 30;
      const diasPrimeiraParcela = condicaoPagamento.diasPrimeiraParcela || 30;

      for (let i = 0; i < numeroParcelas; i++) {
        const diasVencimento = diasPrimeiraParcela + (i * diasEntreParcelas);
        const dataVencimento = new Date(Date.now() + diasVencimento * 24 * 60 * 60 * 1000);

        parcelas.push({
          numero: i + 1,
          valor: i === numeroParcelas - 1 ? valorTotal - (valorParcela * i) : valorParcela, // Ajuste na última parcela
          dataVencimento: Timestamp.fromDate(dataVencimento),
          status: 'ABERTO',
          valorPago: 0,
          dataPagamento: null
        });
      }

      return parcelas;
    }

    // Suas novas funções
    async function checkBudgetLimits() {
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      // First filter by date
      const q = query(
        collection(db, "pedidosCompra"),
        where("dataCriacao", ">=", Timestamp.fromDate(startOfMonth))
      );

      // Then filter by status in memory
      const querySnapshot = await getDocs(q);
      orcamentoUtilizado = querySnapshot.docs
        .filter(doc => ["ABERTO", "APROVADO", "RECEBIDO"].includes(doc.data().status))
        .reduce((total, doc) => total + (doc.data().valorTotal || 0), 0);

      updateBudgetAlert();
    }

    function updateBudgetAlert() {
      const alert = document.getElementById('budgetAlert');
      if (orcamentoUtilizado > LIMITE_ORCAMENTO_MENSAL) {
        alert.style.display = 'block';
        alert.textContent = `Atenção: Orçamento mensal excedido! (${orcamentoUtilizado.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})})`;
      } else {
        alert.style.display = 'none';
      }
    }

    async function checkSupplierLimits() {
      const fornecedorId = document.getElementById('supplierSelect').value;
      if (!fornecedorId) return;

      const fornecedor = fornecedores.find(f => f.id === fornecedorId);
      if (!fornecedor) return;

      const q = query(
        collection(db, "pedidosCompra"),
        where("fornecedorId", "==", fornecedorId),
        where("status", "in", ["ABERTO", "APROVADO"])
      );

      const querySnapshot = await getDocs(q);
      const totalEmAberto = querySnapshot.docs.reduce((total, doc) => {
        return total + (doc.data().valorTotal || 0);
      }, 0);

      if (totalEmAberto > (fornecedor.limiteCredito || 0)) {
        alert(`Atenção: Este fornecedor já possui pedidos que excedem seu limite de crédito!\nTotal em aberto: ${totalEmAberto.toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'})}`);
      }
    }

    function totaltoFixed(num) {
      return parseFloat(num).toFixed(2);
    }

    // ===== FUNCIONALIDADES DE ENVIO PARA FORNECEDOR =====

    window.enviarPedidoFornecedor = async function(pedidoId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", pedidoId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('Pedido não encontrado');
          return;
        }

        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };
        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);

        if (!fornecedor || !fornecedor.email) {
          alert('Fornecedor não possui email cadastrado!');
          return;
        }

        // Criar modal de envio
        const envioModal = document.createElement('div');
        envioModal.className = 'modal';
        envioModal.innerHTML = `
          <div class="modal-content" style="max-width: 700px;">
            <span class="close-button" onclick="this.closest('.modal').remove()">×</span>
            <h2>📧 Enviar Pedido para Fornecedor</h2>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h3>📋 Dados do Pedido</h3>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                  <p><strong>Número:</strong> ${pedido.numero}</p>
                  <p><strong>Fornecedor:</strong> ${fornecedor.razaoSocial}</p>
                  <p><strong>Email:</strong> ${fornecedor.email}</p>
                </div>
                <div>
                  <p><strong>Valor Total:</strong> R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}</p>
                  <p><strong>Data Entrega:</strong> ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString() : 'N/A'}</p>
                  <p><strong>Status:</strong> ${pedido.status}</p>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label><strong>📧 Para (Email):</strong></label>
              <input type="email" id="emailDestino" value="${fornecedor.email}" required style="width: 100%; padding: 8px;">
            </div>

            <div class="form-group">
              <label><strong>📧 Cópia (CC):</strong></label>
              <input type="email" id="emailCopia" placeholder="<EMAIL> (opcional)" style="width: 100%; padding: 8px;">
            </div>

            <div class="form-group">
              <label><strong>📝 Assunto:</strong></label>
              <input type="text" id="assuntoEmail" value="Pedido de Compra ${pedido.numero} - ${fornecedor.razaoSocial}" required style="width: 100%; padding: 8px;">
            </div>

            <div class="form-group">
              <label><strong>💬 Mensagem:</strong></label>
              <textarea id="mensagemEmail" rows="8" style="width: 100%; padding: 8px;" required>Prezados,

Segue em anexo o Pedido de Compra ${pedido.numero} para análise e confirmação.

📋 DADOS DO PEDIDO:
• Número: ${pedido.numero}
• Data de Emissão: ${new Date().toLocaleDateString()}
• Data de Entrega Solicitada: ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString() : 'A definir'}
• Valor Total: R$ ${pedido.valorTotal?.toFixed(2) || '0,00'}
• Condição de Pagamento: ${pedido.condicaoPagamento || 'Conforme negociado'}

📦 ITENS (${pedido.itens?.length || 0} itens):
${pedido.itens?.map(item => `• ${item.codigo} - ${item.descricao} - Qtd: ${item.quantidade} ${item.unidade} - R$ ${item.valorTotal?.toFixed(2) || '0,00'}`).join('\n') || 'Nenhum item'}

Por favor, confirmem o recebimento e nos informem sobre:
✅ Disponibilidade dos itens
✅ Prazo de entrega
✅ Condições comerciais

Aguardamos retorno.

Atenciosamente,
${currentUser.nome || 'Departamento de Compras'}
${currentUser.email || ''}</textarea>
            </div>

            <div class="form-group">
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="anexarPDF" checked>
                <span>📎 Anexar PDF do pedido</span>
              </label>
            </div>

            <div class="form-group">
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="marcarComoEnviado" checked>
                <span>✅ Marcar pedido como "ENVIADO" após envio</span>
              </label>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
              <button onclick="this.closest('.modal').remove()" class="btn-secondary">❌ Cancelar</button>
              <button onclick="confirmarEnvioPedido('${pedidoId}')" class="btn-primary">📧 Enviar Pedido</button>
            </div>
          </div>
        `;

        document.body.appendChild(envioModal);
        envioModal.style.display = 'block';

      } catch (error) {
        console.error("Erro ao preparar envio:", error);
        alert("Erro ao preparar envio do pedido");
      }
    };

    window.confirmarEnvioPedido = async function(pedidoId) {
      try {
        const emailDestino = document.getElementById('emailDestino').value;
        const emailCopia = document.getElementById('emailCopia').value;
        const assunto = document.getElementById('assuntoEmail').value;
        const mensagem = document.getElementById('mensagemEmail').value;
        const anexarPDF = document.getElementById('anexarPDF').checked;
        const marcarComoEnviado = document.getElementById('marcarComoEnviado').checked;

        if (!emailDestino || !assunto || !mensagem) {
          alert('Preencha todos os campos obrigatórios!');
          return;
        }

        // Validar email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailDestino)) {
          alert('Email de destino inválido!');
          return;
        }

        if (emailCopia && !emailRegex.test(emailCopia)) {
          alert('Email de cópia inválido!');
          return;
        }

        // Simular envio (aqui você integraria com um serviço de email real)
        const btnEnviar = document.querySelector('.btn-primary');
        const textoOriginal = btnEnviar.textContent;
        btnEnviar.textContent = '📤 Enviando...';
        btnEnviar.disabled = true;

        // Simular delay de envio
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Registrar envio no pedido
        const pedidoRef = doc(db, "pedidosCompra", pedidoId);
        const envioData = {
          dataEnvio: Timestamp.now(),
          emailDestino: emailDestino,
          emailCopia: emailCopia || null,
          assunto: assunto,
          mensagem: mensagem,
          anexouPDF: anexarPDF,
          enviadoPor: currentUser.nome || 'Sistema'
        };

        const updateData = {
          ultimoEnvio: envioData,
          historicoEnvios: [...(await getDoc(pedidoRef)).data().historicoEnvios || [], envioData]
        };

        if (marcarComoEnviado) {
          updateData.status = 'ENVIADO';
          updateData.dataEnvio = Timestamp.now();
        }

        await updateDoc(pedidoRef, updateData);

        // ✅ ENVIAR NOTIFICAÇÃO DE ENVIO
        if (window.NotificationService) {
          try {
            const pedidoData = (await getDoc(pedidoRef)).data();
            await window.NotificationService.notifyPedidoEnviado(
              { id: pedidoId, ...pedidoData },
              emailDestino,
              currentUser.nome || 'Sistema'
            );
          } catch (notifError) {
            console.warn('⚠️ Erro ao enviar notificação de envio:', notifError);
          }
        }

        // Registrar log de atividade
        await addDoc(collection(db, "logsAtividades"), {
          tipo: 'ENVIO_PEDIDO',
          pedidoId: pedidoId,
          usuario: currentUser.nome || 'Sistema',
          dataHora: Timestamp.now(),
          detalhes: {
            emailDestino: emailDestino,
            assunto: assunto,
            anexouPDF: anexarPDF
          }
        });

        alert(`✅ Pedido enviado com sucesso para ${emailDestino}!`);
        document.querySelector('.modal').remove();

        // Recarregar dados
        await loadInitialData();
        applyFilters();

      } catch (error) {
        console.error("Erro ao enviar pedido:", error);
        alert("❌ Erro ao enviar pedido: " + error.message);

        // Restaurar botão
        const btnEnviar = document.querySelector('.btn-primary');
        if (btnEnviar) {
          btnEnviar.textContent = '📧 Enviar Pedido';
          btnEnviar.disabled = false;
        }
      }
    };

    window.gerarPDFPedido = async function(pedidoId) {
      try {
        const pedidoRef = doc(db, "pedidosCompra", pedidoId);
        const pedidoSnap = await getDoc(pedidoRef);

        if (!pedidoSnap.exists()) {
          alert('Pedido não encontrado');
          return;
        }

        const pedido = { id: pedidoSnap.id, ...pedidoSnap.data() };
        const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);

        // Buscar dados da empresa do Firebase
        const empresaRef = doc(db, "empresa", "config");
        const empresaSnap = await getDoc(empresaRef);

        const empresaData = empresaSnap.exists() ? empresaSnap.data() : {
          razaoSocial: 'NALITECK BRASIL AUTOMAÇÃO IND EIRELI',
          endereco: 'RUA PASCHOA DE NADAI NALINI, 72',
          telefone: '( 17 )3042-1507',
          cep: '15406-232',
          cidade: 'Olimpia',
          estado: 'SP',
          cnpj: '12.811.393/0001-00',
          inscricaoEstadual: '487.029.880.116',
          logoUrl: ''
        };

        // Formatar data atual
        const agora = new Date();
        const dataFormatada = agora.toLocaleDateString('pt-BR');
        const horaFormatada = agora.toLocaleTimeString('pt-BR');

        // Calcular totais
        let subtotal = 0;
        let quantidadeTotal = 0;

        pedido.itens?.forEach(item => {
          const valorItem = (item.quantidade || 0) * (item.valorUnitario || 0);
          subtotal += valorItem;
          quantidadeTotal += (item.quantidade || 0);
        });

        // Criar conteúdo HTML exatamente como o formato da imagem mostrada
        const htmlContent = `
          <!DOCTYPE html>
          <html lang="pt-BR">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Pedido de Compra - ${pedido.numero}</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                margin: 0;
                padding: 15px;
                font-size: 10px;
                line-height: 1.2;
                color: #000;
              }
              .header-box {
                border: 2px solid #000;
                border-radius: 10px;
                padding: 8px;
                margin-bottom: 10px;
                position: relative;
                display: flex;
                align-items: center;
              }
              .logo-section {
                width: 80px;
                height: 60px;
                border: 1px solid #000;
                border-radius: 5px;
                text-align: center;
                padding: 5px;
                margin-right: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              .logo-section img {
                max-width: 70px;
                max-height: 50px;
                object-fit: contain;
              }
              .company-header {
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                margin-bottom: 5px;
              }
              .document-number {
                text-align: center;
                font-size: 11px;
                margin-bottom: 5px;
              }
              .page-info {
                position: absolute;
                top: 8px;
                right: 15px;
                font-size: 10px;
                font-weight: bold;
              }
              .document-line {
                text-align: left;
                font-size: 11px;
                margin: 5px 0;
                font-weight: bold;
              }
              .company-box {
                border: 1px solid #000;
                padding: 8px;
                margin: 10px 0;
                background-color: #f8f8f8;
              }
              .supplier-box {
                border: 1px solid #000;
                padding: 8px;
                margin: 10px 0;
                background-color: #f8f8f8;
              }
              .info-line {
                margin: 2px 0;
                font-size: 10px;
              }
              .items-header {
                border-top: 2px solid #000;
                border-bottom: 1px solid #000;
                padding: 5px 0;
                margin: 15px 0 5px 0;
                font-weight: bold;
                font-size: 10px;
              }
              .items-table {
                width: 100%;
                border-collapse: collapse;
                font-family: 'Courier New', monospace;
                font-size: 9px;
              }
              .items-table th {
                border-bottom: 1px solid #000;
                padding: 3px;
                text-align: left;
                font-weight: bold;
                background: none;
              }
              .items-table td {
                padding: 2px 3px;
                border: none;
                vertical-align: top;
              }
              .item-line {
                border-bottom: 1px dotted #ccc;
              }
              .right-align {
                text-align: right;
              }
              .center-align {
                text-align: center;
              }
              @media print {
                body { margin: 0; padding: 10px; }
                .page-break { page-break-after: always; }
              }
            </style>
          </head>
          <body>
            <!-- Cabeçalho com logo e dados da empresa -->
            <div class="header-box">
              <div class="logo-section">
                ${empresaData.logoUrl ? `<img src="${empresaData.logoUrl}" alt="Logo da Empresa">` : 'LOGO'}
              </div>
              <div style="flex: 1; text-align: center;">
                <div class="company-header">${empresaData.razaoSocial || 'NALITECK BRASIL AUTOMAÇÃO IND EIRELI'}</div>
                <div class="document-number">${pedido.numero} - ${dataFormatada}</div>
                <div class="document-number">${dataFormatada} ${horaFormatada} Pag:001</div>
              </div>
              <div class="page-info">WP-40-0380</div>
            </div>

            <!-- Linha do documento -->
            <div class="document-line">: ${pedido.numero} Data: ${dataFormatada} ${pedido.status} Pag:001</div>

            <!-- Dados da empresa -->
            <div class="company-box">
              <strong>${empresaData.razaoSocial || 'NALITECK BRASIL AUTOMAÇÃO IND EIRELI'}</strong>
              <div class="info-line">
                Ender.: ${empresaData.endereco || 'RUA PASCHOA DE NADAI NALINI, 72'}
                CEP:${empresaData.cep || '15406-232'} ${empresaData.cidade || 'Olimpia'} - ${empresaData.estado || 'SP'}
              </div>
              <div class="info-line">
                Telef.: ${empresaData.telefone || '( 17 )3042-1507'}
                Fax: ( ) -
                CNPJ: ${empresaData.cnpj || '12.811.393/0001-00'}
                IE: ${empresaData.inscricaoEstadual || '487.029.880.116'}
              </div>
            </div>

            <!-- Dados do fornecedor -->
            <div class="supplier-box">
              <div class="info-line">
                <strong>Cliente..:</strong> ${fornecedor?.razaoSocial || 'N/A'}
                <span style="margin-left: 200px;">Contato..: ${fornecedor?.contato || 'N/A'}</span>
              </div>
              <div class="info-line">
                <strong>Endereço.:</strong> ${fornecedor?.endereco || 'N/A'}
                <span style="margin-left: 150px;">${fornecedor?.cidade || ''} - ${fornecedor?.estado || ''} - ${fornecedor?.cep || ''} - Brasil</span>
              </div>
              <div class="info-line">
                <strong>Tel:</strong>${fornecedor?.telefone || 'N/A'}
                <strong>Cel:</strong>${fornecedor?.celular || 'N/A'}
                <strong>Cel 2:</strong>
                <span style="margin-left: 100px;">CNPJ:${fornecedor?.cnpj || 'N/A'} IE:${fornecedor?.inscricaoEstadual || 'N/A'}</span>
              </div>
              <div class="info-line">
                <strong>Cobrança..:</strong> -
                <span style="margin-left: 300px;">- - -</span>
              </div>
              <div class="info-line">
                <strong>Entrega..:</strong> ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString('pt-BR') : '-'}
                <span style="margin-left: 300px;">- - -</span>
              </div>
            </div>

            <!-- Informações do pedido -->
            <div style="margin: 10px 0; font-size: 10px;">
              <span>Entrega.: ${pedido.dataEntregaPrevista ? new Date(pedido.dataEntregaPrevista.seconds * 1000).toLocaleDateString('pt-BR') : ''}</span>
              <span style="margin-left: 100px;">Pedido Cliente: ${pedido.numero}</span>
              <span style="margin-left: 100px;">Tabela: CUSTO</span>
            </div>
            <div style="margin: 5px 0; font-size: 10px;">
              Cond. Pgto....: ${pedido.condicaoPagamento || '28 /42 /56 Dias'}
            </div>

            <!-- Linha separadora -->
            <div class="items-header">
              <div style="display: flex; justify-content: space-between; font-weight: bold;">
                <span>ITE</span>
                <span>QUANTIDADE UN</span>
                <span>CÓDIGO</span>
                <span>DESCRIÇÃO</span>
                <span>$UNITÁRIO</span>
                <span>$LÍQUIDO</span>
                <span>$TOTAL</span>
                <span>%ICMS</span>
              </div>
            </div>

            <!-- Itens do pedido -->
            <table class="items-table">
              <tbody>
                ${pedido.itens?.map((item, index) => {
                  const valorTotal = (item.quantidade || 0) * (item.valorUnitario || 0);
                  const quantidadeFormatada = `${(item.quantidade || 0).toFixed(3)} ${item.unidade || 'UN'}`;
                  return `
                    <tr class="item-line">
                      <td style="width: 30px;">${String(index + 1).padStart(3, '0')}</td>
                      <td style="width: 80px;">${quantidadeFormatada}</td>
                      <td style="width: 60px;">${item.codigo || 'N/A'}</td>
                      <td style="width: 250px;">${item.descricao || 'N/A'}</td>
                      <td style="width: 70px;" class="right-align">${(item.valorUnitario || 0).toFixed(4)}</td>
                      <td style="width: 70px;" class="right-align">${(item.valorUnitario || 0).toFixed(4)}</td>
                      <td style="width: 80px;" class="right-align">${valorTotal.toFixed(2)}</td>
                      <td style="width: 50px;" class="right-align">0,00</td>
                    </tr>
                  `;
                }).join('') || '<tr><td colspan="8">Nenhum item</td></tr>'}
              </tbody>
            </table>

            <div style="margin-top: 10px; font-size: 10px;">
              Qtde: ${quantidadeTotal.toFixed(3)}
            </div>

            <!-- Segunda página com totais -->
            <div class="page-break"></div>

            <div style="text-align: center; margin: 20px 0; font-weight: bold;">
              Data: ${dataFormatada}
            </div>
            <div style="text-align: center; margin: 10px 0; font-weight: bold;">
              ${pedido.status}
            </div>

            <table style="width: 50%; margin: 20px 0; font-size: 10px;">
              <tr>
                <td>ICMS. . . . :</td>
                <td class="right-align">0,00</td>
                <td style="padding-left: 50px;">IPI. . . . . :</td>
                <td class="right-align">0,00</td>
              </tr>
              <tr>
                <td>Desconto. :</td>
                <td class="right-align">${(pedido.desconto || 0).toFixed(2)}</td>
                <td style="padding-left: 50px;">Frete. . . . :</td>
                <td class="right-align">${(pedido.frete || 0).toFixed(2)}</td>
              </tr>
              <tr>
                <td></td>
                <td></td>
                <td style="padding-left: 50px;">ST. . . . . . :</td>
                <td class="right-align">0,00</td>
              </tr>
            </table>

            <div style="text-align: center; margin: 20px 0; font-weight: bold;">
              Tag: 002
            </div>

            <table style="width: 50%; margin: 20px 0; font-size: 10px;">
              <tr>
                <td>Total pedido. . . . . . :</td>
                <td class="right-align">${(pedido.valorTotal || subtotal).toFixed(2)}</td>
              </tr>
              <tr>
                <td>(-) Desconto. . . . . . :</td>
                <td class="right-align">${(pedido.valorTotal || subtotal).toFixed(2)}</td>
              </tr>
              <tr>
                <td>(+) IPI (+) Frete. . . . :</td>
                <td class="right-align">${(pedido.valorTotal || subtotal).toFixed(2)}</td>
              </tr>
              <tr>
                <td>(+) ST. . . . . . . . . . :</td>
                <td class="right-align">${(pedido.valorTotal || subtotal).toFixed(2)}</td>
              </tr>
            </table>

            <div style="text-align: center; margin-top: 30px; font-size: 9px;">
              ${dataFormatada} ${horaFormatada} WP-40-0380
            </div>
          </body>
          </html>
        `;

        // Abrir em nova janela para impressão/PDF
        const newWindow = window.open('', '_blank');
        newWindow.document.write(htmlContent);
        newWindow.document.close();

        // Aguardar carregamento e abrir diálogo de impressão
        setTimeout(() => {
          newWindow.print();
        }, 1000);

        // Registrar impressão do pedido
        await addDoc(collection(db, "logsAtividades"), {
          tipo: 'IMPRIMIR_PEDIDO',
          pedidoId: pedidoId,
          usuario: currentUser.nome || 'Sistema',
          dataHora: Timestamp.now(),
          detalhes: `Pedido ${pedido.numero} impresso`
        });

        console.log('✅ Pedido impresso com sucesso');

      } catch (error) {
        console.error("Erro ao imprimir pedido:", error);
        alert("Erro ao imprimir pedido: " + error.message);
      }
    };

    // ===== ALERTAS DE ATRASO =====

    function updateDelayAlerts() {
      try {
        const hoje = new Date();
        let pedidosAtrasados = 0;

        pedidosCompra.forEach(pedido => {
          if ((pedido.status === 'APROVADO' || pedido.status === 'ENVIADO') && pedido.dataEntregaPrevista) {
            const dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
            const diffTime = hoje - dataEntrega;
            const diasAtraso = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diasAtraso > 0) {
              pedidosAtrasados++;
            }
          }
        });

        const alertContainer = document.getElementById('alertasAtraso');
        const contador = document.getElementById('contadorAtrasos');

        if (pedidosAtrasados > 0) {
          contador.textContent = pedidosAtrasados;
          alertContainer.style.display = 'block';

          // Piscar se há muitos atrasos
          if (pedidosAtrasados > 5) {
            alertContainer.style.animation = 'blink 1s infinite';
          } else {
            alertContainer.style.animation = 'none';
          }
        } else {
          alertContainer.style.display = 'none';
        }

      } catch (error) {
        console.error('❌ Erro ao atualizar alertas de atraso:', error);
      }
    }

    window.showDelayedOrders = function() {
      // Filtrar apenas pedidos atrasados
      document.getElementById('statusFilter').value = '';
      document.getElementById('ocultarAprovados').checked = false;

      const hoje = new Date();
      filteredPedidos = pedidosCompra.filter(pedido => {
        if ((pedido.status === 'APROVADO' || pedido.status === 'ENVIADO') && pedido.dataEntregaPrevista) {
          const dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
          const diffTime = hoje - dataEntrega;
          const diasAtraso = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return diasAtraso > 0;
        }
        return false;
      });

      currentPage = 1;
      loadOrders();
      updateSummary();
      updatePagination();

      showNotification(`Exibindo ${filteredPedidos.length} pedidos em atraso`, 'warning');
    };

    // ===== INICIALIZAR SERVIÇOS =====

    // Função para inicializar serviços
    async function initializeServices() {
      try {
        // Importar e inicializar serviço de notificações
        const module = await import('./services/notification-service.js');
        const { NotificationService } = module;

        // Disponibilizar globalmente primeiro
        window.NotificationService = NotificationService;

        // Inicializar verificação automática de atrasos
        if (typeof NotificationService.initializeDelayCheck === 'function') {
          NotificationService.initializeDelayCheck();
          console.log('🔔 Serviço de notificações carregado e inicializado');
        } else {
          console.warn('⚠️ Método initializeDelayCheck não encontrado');
        }
      } catch (error) {
        console.error('❌ Erro ao carregar serviço de notificações:', error);
      }
    }

    // Inicializar serviços
    initializeServices();

    // CORREÇÃO: Função para verificar e limpar pedidos duplicados
    window.verificarPedidosDuplicados = async function() {
        try {
            console.log('🔍 Verificando pedidos duplicados...');

            // Buscar todos os pedidos
            const pedidosSnap = await getDocs(collection(db, "pedidosCompra"));
            const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // Agrupar por cotacaoId
            const pedidosPorCotacao = {};
            const pedidosDuplicados = [];

            pedidos.forEach(pedido => {
                if (pedido.cotacaoId) {
                    if (!pedidosPorCotacao[pedido.cotacaoId]) {
                        pedidosPorCotacao[pedido.cotacaoId] = [];
                    }
                    pedidosPorCotacao[pedido.cotacaoId].push(pedido);
                }
            });

            // Identificar duplicados
            Object.keys(pedidosPorCotacao).forEach(cotacaoId => {
                const pedidosDaCotacao = pedidosPorCotacao[cotacaoId];
                if (pedidosDaCotacao.length > 1) {
                    // Manter o mais antigo, marcar outros como duplicados
                    const pedidoMaisAntigo = pedidosDaCotacao.sort((a, b) => {
                        const dataA = a.dataCriacao?.seconds || 0;
                        const dataB = b.dataCriacao?.seconds || 0;
                        return dataA - dataB;
                    })[0];

                    pedidosDaCotacao.forEach(pedido => {
                        if (pedido.id !== pedidoMaisAntigo.id) {
                            pedidosDuplicados.push({
                                ...pedido,
                                cotacaoId,
                                pedidoOriginal: pedidoMaisAntigo.id
                            });
                        }
                    });
                }
            });

            console.log(`📊 Encontrados ${pedidosDuplicados.length} pedidos duplicados`);

            if (pedidosDuplicados.length > 0) {
                const detalhes = pedidosDuplicados.map(p =>
                    `• ${p.numero} (Cotação: ${p.cotacaoId})`
                ).join('\n');

                const confirmar = confirm(`🔍 PEDIDOS DUPLICADOS ENCONTRADOS\n\nForam encontrados ${pedidosDuplicados.length} pedidos duplicados:\n\n${detalhes}\n\nDeseja remover os pedidos duplicados?\n\n⚠️ Apenas os pedidos mais recentes serão removidos, mantendo o original.`);

                if (confirmar) {
                    await removerPedidosDuplicados(pedidosDuplicados);
                }
            } else {
                alert('✅ Nenhum pedido duplicado encontrado!');
            }

        } catch (error) {
            console.error('❌ Erro ao verificar duplicados:', error);
            alert('❌ Erro ao verificar pedidos duplicados: ' + error.message);
        }
    };

    // Função para remover pedidos duplicados
    async function removerPedidosDuplicados(pedidosDuplicados) {
        try {
            let removidos = 0;

            for (const pedido of pedidosDuplicados) {
                await deleteDoc(doc(db, "pedidosCompra", pedido.id));
                console.log(`🗑️ Pedido duplicado removido: ${pedido.numero}`);
                removidos++;
            }

            alert(`✅ ${removidos} pedidos duplicados foram removidos com sucesso!`);

            // Recarregar dados
            await loadData();

        } catch (error) {
            console.error('❌ Erro ao remover duplicados:', error);
            alert('❌ Erro ao remover pedidos duplicados: ' + error.message);
        }
    }

  </script>
</body>
</html>