@echo off
echo 🚀 Iniciando servidor local para evitar problemas de CORS...
echo.
echo Servidor será iniciado em: http://localhost:8080
echo.
echo Para acessar o sistema:
echo - Apontamentos: http://localhost:8080/apontamentos_integrado_novo.html
echo - Teste Firebase: http://localhost:8080/firebase-test.html
echo.
echo Pressione Ctrl+C para parar o servidor
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Usando Python para servidor HTTP...
    python -m http.server 8080
) else (
    REM Verificar se Node.js está instalado
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Usando Node.js para servidor HTTP...
        npx http-server -p 8080 -c-1
    ) else (
        echo ❌ Nem Python nem Node.js foram encontrados.
        echo Por favor, instale um deles para usar o servidor local.
        echo.
        echo Alternativa: Use um editor como VS Code com Live Server extension
        pause
    )
)
