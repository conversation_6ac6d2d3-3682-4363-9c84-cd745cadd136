<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Movimentação entre Armazéns - ERP Industrial</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* ========================================
           🎨 DESIGN SYSTEM - ERP INDUSTRIAL
           ======================================== */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 🎨 Paleta de Cores */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --header-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --success-gradient: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            --warning-gradient: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            --danger-gradient: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            --info-gradient: linear-gradient(135deg, #17a2b8 0%, #138496 100%);

            /* 📏 Espaçamentos */
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 20px;
            --spacing-lg: 30px;
            --spacing-xl: 40px;

            /* 🔤 Tipografia */
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size-sm: 12px;
            --font-size-md: 14px;
            --font-size-lg: 16px;
            --font-size-xl: 18px;
            --font-size-xxl: 24px;
            --font-size-title: 28px;

            /* 🎯 Bordas e Sombras */
            --border-radius: 8px;
            --border-radius-lg: 15px;
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-md: 0 8px 20px rgba(0,0,0,0.15);
            --shadow-lg: 0 20px 40px rgba(0,0,0,0.1);

            /* 🎭 Transições */
            --transition-fast: all 0.2s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        body {
            font-family: var(--font-family);
            background: var(--primary-gradient);
            min-height: 100vh;
            padding: var(--spacing-md);
            color: #2c3e50;
        }

        /* ========================================
           🏗️ LAYOUT PRINCIPAL
           ======================================== */

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ========================================
           📱 HEADER RESPONSIVO
           ======================================== */

        .header {
            background: var(--header-gradient);
            color: white;
            padding: 25px var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .header h1 {
            font-size: var(--font-size-title);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        /* ========================================
           🔘 SISTEMA DE BOTÕES
           ======================================== */

        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: var(--transition-normal);
            font-size: var(--font-size-md);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-primary { background: var(--info-gradient); color: white; }
        .btn-success { background: var(--success-gradient); color: white; }
        .btn-warning { background: var(--warning-gradient); color: white; }
        .btn-danger { background: var(--danger-gradient); color: white; }
        .btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; }

        /* ========================================
           📋 CONTEÚDO PRINCIPAL
           ======================================== */

        .main-content {
            padding: var(--spacing-lg);
        }

        .section-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--spacing-md);
            overflow: hidden;
            border: 1px solid #e9ecef;
            transition: var(--transition-normal);
        }

        .section-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .section-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: var(--spacing-md);
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-content {
            padding: var(--spacing-md);
        }

        /* ========================================
           📊 GRID RESPONSIVO
           ======================================== */

        .grid {
            display: grid;
            gap: var(--spacing-md);
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

        /* ========================================
           📱 RESPONSIVIDADE
           ======================================== */

        @media (max-width: 768px) {
            body {
                padding: var(--spacing-sm);
            }

            .header {
                padding: var(--spacing-md);
                text-align: center;
            }

            .header h1 {
                font-size: var(--font-size-xl);
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .main-content {
                padding: var(--spacing-md);
            }

            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: var(--font-size-sm);
            }

            .section-header {
                padding: var(--spacing-sm);
            }

            .section-content {
                padding: var(--spacing-sm);
            }
        }

        /* ========================================
           🎯 COMPONENTES ESPECÍFICOS
           ======================================== */

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
            color: #6c757d;
        }

        .loading i {
            margin-right: var(--spacing-sm);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .notification {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-md);
            display: none;
            animation: slideInDown 0.3s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notification.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .notification.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .notification.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .notification.info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        /* ========================================
           📊 COMPONENTES DE ESTATÍSTICAS
           ======================================== */

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            transition: var(--transition-normal);
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-xl);
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: var(--font-size-xxl);
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: var(--font-size-sm);
            color: #6c757d;
            font-weight: 500;
            margin-top: 4px;
        }

        /* ========================================
           📑 SISTEMA DE ABAS
           ======================================== */

        .tabs-container {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        .tabs-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            background: transparent;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: var(--transition-normal);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: #e9ecef;
            color: #495057;
        }

        .tab-button.active {
            color: #007bff;
            background: white;
            border-bottom-color: #007bff;
        }

        .tabs-content {
            min-height: 300px;
        }

        .tab-content {
            display: none;
            padding: var(--spacing-md);
            animation: fadeIn 0.3s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ========================================
           📋 TABELAS RESPONSIVAS
           ======================================== */

        .table-responsive {
            overflow-x: auto;
            border-radius: var(--border-radius);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th {
            background: #f8f9fa;
            padding: var(--spacing-sm) var(--spacing-xs);
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
        }

        .table td {
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* ========================================
           🔘 CONTROLES DE FORMULÁRIO
           ======================================== */

        .form-control {
            width: 100%;
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid #ced4da;
            border-radius: var(--border-radius);
            font-size: var(--font-size-md);
            transition: var(--transition-fast);
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-control:disabled {
            background: #e9ecef;
            opacity: 0.6;
        }

        /* ========================================
           🎯 BADGES E INDICADORES
           ======================================== */

        .badge {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }

        .badge.success { background: var(--success-gradient); }
        .badge.warning { background: var(--warning-gradient); }
        .badge.danger { background: var(--danger-gradient); }
        .badge.info { background: var(--info-gradient); }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-gradient);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        /* ========================================
           🎭 MODAIS E OVERLAYS
           ======================================== */

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease-out;
        }

        .modal-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .modal-footer {
            padding: var(--spacing-md) var(--spacing-lg);
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
        }

        /* ========================================
           📱 RESPONSIVIDADE AVANÇADA
           ======================================== */

        @media (max-width: 992px) {
            .tabs-header {
                flex-wrap: wrap;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
            }

            .table th,
            .table td {
                padding: var(--spacing-xs);
                font-size: var(--font-size-sm);
            }
        }

        @media (max-width: 576px) {
            .modal-content {
                margin: var(--spacing-md);
                width: calc(100% - 2 * var(--spacing-md));
                max-width: none;
            }

            .tabs-header {
                flex-direction: column;
            }

            .tab-button {
                text-align: center;
                border-bottom: none;
                border-right: 3px solid transparent;
            }

            .tab-button.active {
                border-bottom: none;
                border-right-color: #007bff;
            }
        }

        /* 🆕 Estilos para indicadores de inconsistência */
        .inconsistent-transfer {
            background: #fff3cd !important;
            border-left: 4px solid #f39c12 !important;
        }

        .corrected-transfer {
            background: #d4edda !important;
            border-left: 4px solid #28a745 !important;
        }

        .alert-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: 600;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- ========================================
             📱 HEADER PRINCIPAL
             ======================================== -->
        <div class="header">
            <h1>
                <i class="fas fa-exchange-alt"></i>
                Movimentação entre Armazéns
            </h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="abrirRelatorioProducao()" title="Relatório de Materiais em Produção">
                    <i class="fas fa-chart-bar"></i>
                    Relatório Produção
                </button>
                <button class="btn btn-success" onclick="analisarMateriaisRestantes()" title="Análise de Materiais Restantes">
                    <i class="fas fa-analytics"></i>
                    Análise Materiais
                </button>
                <button class="btn btn-warning" onclick="diagnosticarSistema()" title="Diagnóstico do Sistema">
                    <i class="fas fa-stethoscope"></i>
                    Diagnóstico
                </button>
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <!-- ========================================
             📋 CONTEÚDO PRINCIPAL
             ======================================== -->
        <div class="main-content">
            <!-- Notificações -->
            <div id="notification" class="notification"></div>

            <!-- Loading inicial -->
            <div id="loadingMain" class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Carregando sistema...
            </div>

            <!-- Conteúdo será carregado dinamicamente -->
            <div id="mainContent" style="display: none;">
                <!-- Seção de Seleção de OP -->
                <div class="section-card">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-industry"></i>
                            Seleção de Ordem de Produção
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="grid grid-2">
                            <div>
                                <label for="opSelect" style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-search"></i>
                                    Buscar OP:
                                </label>
                                <select id="opSelect" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                                    <option value="">Selecione uma Ordem de Produção...</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-filter"></i>
                                    Filtros:
                                </label>
                                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" onclick="filtrarOPs('TODOS')" id="btnTodos">
                                        <i class="fas fa-list"></i>
                                        Todos
                                    </button>
                                    <button class="btn btn-primary" onclick="filtrarOPs('Pendente')" id="btnPendente">
                                        <i class="fas fa-clock"></i>
                                        Pendente
                                    </button>
                                    <button class="btn btn-warning" onclick="filtrarOPs('Em Produção')" id="btnEmProducao">
                                        <i class="fas fa-cogs"></i>
                                        Em Produção
                                    </button>
                                    <button class="btn btn-info" onclick="filtrarOPs('Material Transferido')" id="btnMaterialTransferido">
                                        <i class="fas fa-check"></i>
                                        Material OK
                                    </button>
                                    <button class="btn btn-warning" onclick="corrigirNecessidadesOPs()" title="Corrigir campo necessidade para todas as OPs">
                                        <i class="fas fa-wrench"></i>
                                        Corrigir Necessidades
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview dos Materiais da OP -->
                <div id="opMaterialsPreview" class="section-card" style="display: none;">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            Materiais Necessários da OP
                            <span id="opMaterialsCount" class="badge">0 materiais MP</span>
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button class="btn btn-info" onclick="mostrarDetalhesOP()">
                                <i class="fas fa-info-circle"></i>
                                Detalhes
                            </button>
                            <button class="btn btn-warning" onclick="diagnosticarDuplicacoes()" title="Verificar problemas de duplicação">
                                <i class="fas fa-search"></i>
                                Diagnosticar
                            </button>
                            <button class="btn btn-success" onclick="sincronizarHistoricoCompleto()" title="Sincronizar dados com histórico real">
                                <i class="fas fa-sync-alt"></i>
                                Sincronizar Histórico
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- Informações da OP -->
                        <div class="grid grid-4" style="margin-bottom: 20px;">
                            <div>
                                <strong>OP:</strong>
                                <span id="opInfoNumero">-</span>
                            </div>
                            <div>
                                <strong>Produto:</strong>
                                <span id="opInfoProduto">-</span>
                            </div>
                            <div>
                                <strong>Quantidade:</strong>
                                <span id="opInfoQuantidade">-</span>
                            </div>
                            <div>
                                <strong>Armazém:</strong>
                                <span id="opInfoArmazem">-</span>
                            </div>
                        </div>

                        <!-- Alertas de Inconsistência -->
                        <div id="alertaInconsistencias" style="display: none;"></div>

                        <!-- Tabela de Materiais -->
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Código</th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Descrição</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Necessário</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Transferido (OP)</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Transferido (Real)</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Restante</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Status</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Histórico</th>
                                    </tr>
                                </thead>
                                <tbody id="opMaterialsTableBody">
                                    <!-- Conteúdo será carregado dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Seção de Transferências -->
                <div id="transferSection" class="section-card" style="display: none;">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-exchange-alt"></i>
                            Sistema de Transferências
                            <span id="transferCount" class="badge">0 selecionados</span>
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button class="btn btn-success" onclick="executarTransferencias()" id="btnTransferir" disabled>
                                <i class="fas fa-paper-plane"></i>
                                Transferir Selecionados
                            </button>
                            <button class="btn btn-info" onclick="sincronizarStatusTransferencias()" title="Sincronizar status com sistema de apontamentos">
                                <i class="fas fa-sync-alt"></i>
                                Sincronizar Apontamentos
                            </button>
                            <button class="btn btn-warning" onclick="limparSelecao()">
                                <i class="fas fa-times"></i>
                                Limpar Seleção
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- Filtros de Transferência -->
                        <div class="grid grid-3" style="margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-warehouse"></i>
                                    Armazém Origem:
                                </label>
                                <select id="armazemOrigemSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option value="">Selecione o armazém origem...</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-search"></i>
                                    Buscar Material:
                                </label>
                                <input type="text" id="materialSearch" placeholder="Código ou descrição..."
                                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-filter"></i>
                                    Status:
                                </label>
                                <select id="statusFilter" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option value="">Todos os status</option>
                                    <option value="PENDENTE">Pendente</option>
                                    <option value="PARCIAL">Parcial</option>
                                    <option value="COMPLETO">Completo</option>
                                </select>
                            </div>
                        </div>

                        <!-- Tabela de Materiais para Transferência -->
                        <div style="overflow-x: auto;">
                            <table id="transferTable" style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">
                                            <input type="checkbox" id="selectAll" onchange="selecionarTodos(this.checked)">
                                        </th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Código</th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Descrição</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Origem</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Disponível</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Necessário</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Transferir</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Status</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Aguardando</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="transferTableBody">
                                    <!-- Conteúdo será carregado dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========================================
         🔧 JAVASCRIPT - INICIALIZAÇÃO
         ======================================== -->
    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            getDoc,
            updateDoc,
            addDoc,
            setDoc,
            runTransaction,
            query,
            where,
            orderBy,
            onSnapshot,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // ========================================
        // 🌐 VARIÁVEIS GLOBAIS
        // ========================================

        let ordens = [];
        let produtos = [];
        let armazens = [];
        let estoques = [];
        let transferencias = [];
        let selectedOrder = null;
        let filtroAtual = 'TODOS';

        // 🆕 Cache de histórico de transferências por OP
        let historicoTransferenciasCache = new Map();
        let inconsistenciasDetectadas = [];

        // ========================================
        // 🔢 FUNÇÕES DE PRECISÃO DECIMAL
        // ========================================

        function compararComTolerancia(valor1, valor2, tolerancia = 0.001) {
            return Math.abs(valor1 - valor2) <= tolerancia;
        }

        function isTransferenciaCompleta(necessario, transferido, tolerancia = 0.001) {
            return transferido >= necessario || compararComTolerancia(necessario, transferido, tolerancia);
        }

        function calcularRestante(necessario, transferido, tolerancia = 0.001) {
            if (isTransferenciaCompleta(necessario, transferido, tolerancia)) {
                return 0;
            }
            return Math.max(0, necessario - transferido);
        }

        // ========================================
        // 🚀 INICIALIZAÇÃO DO SISTEMA
        // ========================================

        window.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 Iniciando Sistema de Movimentação de Armazéns (VERSÃO CORRIGIDA)...');

            try {
                await inicializarSistema();
                console.log('✅ Sistema inicializado com sucesso!');
            } catch (error) {
                console.error('❌ Erro na inicialização:', error);
                mostrarNotificacao('Erro ao inicializar sistema: ' + error.message, 'error');
            }
        });

        async function inicializarSistema() {
            await Promise.all([
                carregarOrdens(),
                carregarProdutos(),
                carregarArmazens(),
                carregarEstoques(),
                carregarTransferencias()
            ]);

            configurarInterface();

            document.getElementById('loadingMain').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
        }

        // ========================================
        // 📊 CARREGAMENTO DE DADOS
        // ========================================

        async function carregarOrdens() {
            console.log('📋 Carregando ordens de produção...');
            const ordensSnapshot = await getDocs(collection(db, "ordensProducao"));
            ordens = ordensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${ordens.length} ordens carregadas`);
            popularDropdownOPs();
        }

        async function carregarProdutos() {
            console.log('📦 Carregando produtos...');
            const produtosSnapshot = await getDocs(collection(db, "produtos"));
            produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${produtos.length} produtos carregados`);
        }

        async function carregarArmazens() {
            console.log('🏭 Carregando armazéns...');
            const armazensSnapshot = await getDocs(collection(db, "armazens"));
            armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${armazens.length} armazéns carregados`);
        }

        async function carregarEstoques() {
            console.log('📊 Carregando estoques...');
            const estoquesSnapshot = await getDocs(collection(db, "estoques"));
            estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${estoques.length} registros de estoque carregados`);
        }

        async function carregarTransferencias() {
            console.log('🔄 Carregando transferências...');
            try {
                const transferenciasSnapshot = await getDocs(collection(db, "transferenciasArmazem"));
                transferencias = transferenciasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ ${transferencias.length} transferências carregadas`);
            } catch (error) {
                console.error('❌ Erro ao carregar transferências:', error);
                transferencias = [];
            }
        }

        // ========================================
        // 🎯 CONFIGURAÇÃO DA INTERFACE
        // ========================================

        function configurarInterface() {
            popularDropdownOPs();
            configurarEventos();
            aplicarFiltroInicial();
        }

        function popularDropdownOPs() {
            const select = document.getElementById('opSelect');
            select.innerHTML = '<option value="">Selecione uma Ordem de Produção...</option>';

            const ordensAtivas = ordens.filter(op => {
                const status = op.status;
                return status && !['Concluída', 'Concluida', 'Cancelada'].includes(status);
            });

            ordensAtivas
                .sort((a, b) => a.numero.localeCompare(b.numero))
                .forEach(ordem => {
                    const produto = produtos.find(p => p.id === ordem.produtoId);
                    const option = document.createElement('option');
                    option.value = ordem.id;
                    option.textContent = `${ordem.numero} - ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'Sem descrição'}`;
                    option.dataset.status = ordem.status;
                    select.appendChild(option);
                });

            console.log(`📋 ${ordensAtivas.length} OPs ativas carregadas no dropdown`);
        }

        function configurarEventos() {
            document.getElementById('opSelect').addEventListener('change', function() {
                const opId = this.value;
                if (opId) {
                    selecionarOP(opId);
                } else {
                    ocultarPreviewMateriais();
                }
            });
        }

        function aplicarFiltroInicial() {
            aplicarFiltroOP('Pendente');
        }

        // ========================================
        // 🔍 SISTEMA DE FILTROS
        // ========================================

        window.filtrarOPs = function(filtro) {
            aplicarFiltroOP(filtro);
        };

        function aplicarFiltroOP(filtro) {
            filtroAtual = filtro;

            document.querySelectorAll('[id^="btn"]').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
            });

            const filtroParaBotao = {
                'TODOS': 'btnTodos',
                'Pendente': 'btnPendente',
                'Em Produção': 'btnEmProducao',
                'Material Transferido': 'btnMaterialTransferido'
            };

            const btnAtivo = document.getElementById(filtroParaBotao[filtro]);
            if (btnAtivo) {
                btnAtivo.classList.remove('btn-secondary');
                btnAtivo.classList.add('btn-primary');
            }

            const select = document.getElementById('opSelect');
            const options = Array.from(select.options);

            options.forEach(option => {
                if (option.value === '') return;

                const status = option.dataset.status;
                const mostrar = filtro === 'TODOS' || status === filtro;
                option.style.display = mostrar ? '' : 'none';
            });

            if (selectedOrder && filtro !== 'TODOS' && selectedOrder.status !== filtro) {
                select.value = '';
                ocultarPreviewMateriais();
            }

            console.log(`🔍 Filtro aplicado: ${filtro}`);
        }

        // ========================================
        // 📋 GESTÃO DE ORDENS DE PRODUÇÃO
        // ========================================

        function selecionarOP(opId) {
            console.log('🔍 Selecionando OP:', opId);

            selectedOrder = ordens.find(op => op.id === opId);

            if (!selectedOrder) {
                console.error('❌ OP não encontrada');
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            console.log('✅ OP selecionada:', selectedOrder.numero);

            // 🆕 Carregar histórico de transferências IMEDIATAMENTE
            carregarHistoricoTransferenciasOP(opId).then(() => {
                mostrarPreviewMateriais(selectedOrder);
                mostrarSecaoTransferencias();
            });
        }

        // ========================================
        // 🆕 SISTEMA CORRIGIDO DE HISTÓRICO
        // ========================================

        async function carregarHistoricoTransferenciasOP(ordemProducaoId) {
            try {
                console.log(`📜 Carregando histórico COMPLETO para OP: ${ordemProducaoId}`);

                // Buscar TODAS as transferências desta OP
                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", ordemProducaoId)
                );

                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const todasTransferencias = transferenciasSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                console.log(`📊 Total de transferências encontradas: ${todasTransferencias.length}`);

                // Filtrar apenas transferências concluídas
                const transferenciasValidas = todasTransferencias.filter(t => 
                    t.status === 'CONCLUIDA' || t.status === 'CONCLUÍDO' || !t.status
                );

                console.log(`✅ Transferências válidas: ${transferenciasValidas.length}`);

                // 🆕 Processar histórico consolidado por produto
                const historicoConsolidado = {};

                transferenciasValidas.forEach(transfer => {
                    // Suporte para formatos antigos e novos
                    if (transfer.materiais && Array.isArray(transfer.materiais)) {
                        // Formato antigo: múltiplos materiais em uma transferência
                        transfer.materiais.forEach(material => {
                            if (!historicoConsolidado[material.produtoId]) {
                                historicoConsolidado[material.produtoId] = [];
                            }
                            historicoConsolidado[material.produtoId].push({
                                ...transfer,
                                quantidade: material.quantidade,
                                codigo: material.codigo,
                                produtoId: material.produtoId
                            });
                        });
                    } else if (transfer.produtoId) {
                        // Formato novo: uma transferência por produto
                        if (!historicoConsolidado[transfer.produtoId]) {
                            historicoConsolidado[transfer.produtoId] = [];
                        }
                        historicoConsolidado[transfer.produtoId].push(transfer);
                    }
                });

                // Ordenar por data e calcular totais
                Object.keys(historicoConsolidado).forEach(produtoId => {
                    historicoConsolidado[produtoId].sort((a, b) => {
                        const dateA = a.dataHora?.toDate?.() || new Date(a.dataHora?.seconds * 1000 || 0);
                        const dateB = b.dataHora?.toDate?.() || new Date(b.dataHora?.seconds * 1000 || 0);
                        return dateB - dateA;
                    });

                    const totalTransferido = historicoConsolidado[produtoId].reduce((sum, t) => 
                        sum + (t.quantidade || 0), 0
                    );

                    const produto = produtos.find(p => p.id === produtoId);
                    console.log(`📦 ${produto?.codigo || produtoId}: ${historicoConsolidado[produtoId].length} transferências, total: ${totalTransferido.toFixed(3)}`);
                });

                // Armazenar no cache
                historicoTransferenciasCache.set(ordemProducaoId, historicoConsolidado);

                console.log(`✅ Histórico carregado para ${Object.keys(historicoConsolidado).length} produtos únicos`);

                return historicoConsolidado;

            } catch (error) {
                console.error('❌ Erro ao carregar histórico:', error);
                return {};
            }
        }

        // ========================================
        // 🆕 DETECÇÃO E CORREÇÃO DE INCONSISTÊNCIAS
        // ========================================

        async function detectarInconsistencias(opId) {
            const inconsistencias = [];
            const historico = historicoTransferenciasCache.get(opId) || {};

            if (!selectedOrder || !selectedOrder.materiaisNecessarios) {
                return inconsistencias;
            }

            for (const material of selectedOrder.materiaisNecessarios) {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto || produto.tipo !== 'MP') continue;

                const saldoOP = material.saldoReservado || 0;
                const transferenciasHistorico = historico[material.produtoId] || [];
                const transferidoReal = transferenciasHistorico.reduce((total, t) => 
                    total + (t.quantidade || 0), 0
                );

                if (Math.abs(saldoOP - transferidoReal) > 0.001) {
                    inconsistencias.push({
                        produtoId: material.produtoId,
                        codigo: produto.codigo,
                        descricao: produto.descricao,
                        saldoOP: saldoOP,
                        transferidoReal: transferidoReal,
                        diferenca: Math.abs(saldoOP - transferidoReal),
                        tipo: saldoOP > transferidoReal ? 'SOBRA_OP' : 'FALTA_OP',
                        transferencias: transferenciasHistorico.length
                    });
                }
            }

            return inconsistencias;
        }

        window.diagnosticarDuplicacoes = async function() {
            if (!selectedOrder) {
                alert('❌ Selecione uma OP primeiro!');
                return;
            }

            console.log('🔍 Diagnóstico completo iniciado...');

            const inconsistencias = await detectarInconsistencias(selectedOrder.id);

            let relatorio = `🔍 DIAGNÓSTICO DE INCONSISTÊNCIAS - OP ${selectedOrder.numero}\n\n`;

            if (inconsistencias.length === 0) {
                relatorio += `✅ TUDO CERTO!\n\nNenhuma inconsistência detectada entre os dados da OP e o histórico real de transferências.`;
            } else {
                relatorio += `⚠️ PROBLEMAS DETECTADOS: ${inconsistencias.length}\n\n`;

                inconsistencias.forEach((inc, idx) => {
                    relatorio += `${idx + 1}. ${inc.codigo} - ${inc.descricao}\n`;
                    relatorio += `   📊 Saldo na OP: ${inc.saldoOP.toFixed(3)}\n`;
                    relatorio += `   📋 Histórico Real: ${inc.transferidoReal.toFixed(3)}\n`;
                    relatorio += `   ⚠️ Diferença: ${inc.diferenca.toFixed(3)} (${inc.tipo})\n`;
                    relatorio += `   📦 Transferências: ${inc.transferencias}\n\n`;
                });

                relatorio += `💡 RECOMENDAÇÃO: Execute "Sincronizar Histórico" para corrigir automaticamente.`;
            }

            alert(relatorio);
        };

        window.sincronizarHistoricoCompleto = async function() {
            if (!selectedOrder) {
                alert('❌ Selecione uma OP primeiro!');
                return;
            }

            const confirmacao = confirm(`🔄 SINCRONIZAÇÃO COMPLETA DO HISTÓRICO\n\nEsta operação irá:\n• Recarregar o histórico real de transferências\n• Corrigir inconsistências detectadas\n• Atualizar dados da OP\n\nDeseja continuar?`);

            if (!confirmacao) return;

            try {
                mostrarNotificacao('🔄 Sincronizando histórico...', 'info');

                // 1. Recarregar histórico forçado
                const historico = await carregarHistoricoTransferenciasOP(selectedOrder.id);

                // 2. Detectar inconsistências
                const inconsistenciasAntes = await detectarInconsistencias(selectedOrder.id);

                // 3. Corrigir dados da OP
                if (inconsistenciasAntes.length > 0) {
                    console.log(`🔧 Corrigindo ${inconsistenciasAntes.length} inconsistências...`);

                    const materiaisCorrigidos = selectedOrder.materiaisNecessarios.map(material => {
                        const transferenciasHistorico = historico[material.produtoId] || [];
                        const transferidoReal = transferenciasHistorico.reduce((total, t) => 
                            total + (t.quantidade || 0), 0
                        );

                        if (Math.abs((material.saldoReservado || 0) - transferidoReal) > 0.001) {
                            console.log(`🔧 Corrigindo ${material.produtoId}: ${material.saldoReservado || 0} → ${transferidoReal}`);
                            return {
                                ...material,
                                saldoReservado: transferidoReal,
                                ultimaSincronizacao: new Date().toISOString()
                            };
                        }
                        return material;
                    });

                    // Atualizar OP no Firebase
                    await updateDoc(doc(db, "ordensProducao", selectedOrder.id), {
                        materiaisNecessarios: materiaisCorrigidos,
                        ultimaSincronizacaoHistorico: Timestamp.now()
                    });

                    // Atualizar localmente
                    selectedOrder.materiaisNecessarios = materiaisCorrigidos;
                }

                // 4. Atualizar interface
                mostrarPreviewMateriais(selectedOrder);
                carregarMateriaisParaTransferencia();

                // 5. Verificar resultado
                const inconsistenciasDepois = await detectarInconsistencias(selectedOrder.id);

                const mensagem = `✅ SINCRONIZAÇÃO CONCLUÍDA!\n\n📊 Inconsistências corrigidas: ${inconsistenciasAntes.length}\n📊 Inconsistências restantes: ${inconsistenciasDepois.length}`;

                mostrarNotificacao(mensagem.replace(/\n/g, ' '), 'success');
                alert(mensagem);

            } catch (error) {
                console.error('❌ Erro na sincronização:', error);
                alert('❌ Erro na sincronização: ' + error.message);
            }
        };

        // ========================================
        // 📋 PREVIEW DE MATERIAIS CORRIGIDO
        // ========================================

        function mostrarPreviewMateriais(ordem) {
            const preview = document.getElementById('opMaterialsPreview');
            const materiaisMP = carregarMateriaisOP(ordem);

            atualizarInformacoesOP(ordem);
            document.getElementById('opMaterialsCount').textContent = `${materiaisMP?.length || 0} materiais MP`;

            // 🆕 Detectar e exibir inconsistências
            detectarInconsistencias(ordem.id).then(inconsistencias => {
                exibirAlertasInconsistencias(inconsistencias);
                atualizarTabelaMateriaisCorrigida(materiaisMP || [], inconsistencias);
            });

            preview.style.display = 'block';
            preview.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        function exibirAlertasInconsistencias(inconsistencias) {
            const alertContainer = document.getElementById('alertaInconsistencias');

            if (inconsistencias.length === 0) {
                alertContainer.style.display = 'none';
                return;
            }

            alertContainer.style.display = 'block';
            alertContainer.innerHTML = `
                <div class="alert-box alert-warning">
                    <strong>⚠️ ${inconsistencias.length} Inconsistência(s) Detectada(s)</strong><br>
                    Existem diferenças entre os dados da OP e o histórico real de transferências.<br>
                    <button class="btn btn-success" onclick="sincronizarHistoricoCompleto()" style="margin-top: 10px;">
                        <i class="fas fa-sync-alt"></i>
                        Sincronizar Agora
                    </button>
                </div>
            `;
        }

        function atualizarTabelaMateriaisCorrigida(materiaisMP, inconsistencias) {
            const tbody = document.getElementById('opMaterialsTableBody');
            tbody.innerHTML = '';

            if (materiaisMP.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
                    <td colspan="8" style="text-align: center; padding: 30px; color: #6c757d;">
                        <div style="font-size: 18px; opacity: 0.5; margin-bottom: 8px;">📦</div>
                        <strong>Nenhum material MP encontrado</strong><br>
                        <small>Esta OP não possui materiais do tipo Matéria-Prima</small>
                    </td>
                `;
                tbody.appendChild(emptyRow);
                return;
            }

            materiaisMP.forEach(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto) return;

                // Dados da OP
                let necessario = material.necessidade || 0;
                if (necessario <= 0 && material.quantidade > 0 && selectedOrder) {
                    necessario = material.quantidade * selectedOrder.quantidade;
                    material.necessidade = necessario;
                }

                const transferidoOP = material.saldoReservado || 0;

                // 🆕 Dados do histórico real
                const historico = historicoTransferenciasCache.get(selectedOrder.id) || {};
                const transferenciasHistorico = historico[material.produtoId] || [];
                const transferidoReal = transferenciasHistorico.reduce((total, t) => 
                    total + (t.quantidade || 0), 0
                );

                // Verificar inconsistência
                const inconsistente = inconsistencias.find(inc => inc.produtoId === material.produtoId);
                const restante = calcularRestante(necessario, transferidoReal);

                // Status baseado no valor real
                let status = '';
                let statusClass = '';
                let progressPercent = 0;

                if (necessario > 0) {
                    progressPercent = Math.min(100, (transferidoReal / necessario) * 100);
                }

                if (isTransferenciaCompleta(necessario, transferidoReal)) {
                    status = '✅ Completo';
                    statusClass = 'background: #d4edda; color: #155724;';
                } else if (transferidoReal > 0) {
                    status = '🔄 Parcial';
                    statusClass = 'background: #fff3cd; color: #856404;';
                } else {
                    status = '❌ Pendente';
                    statusClass = 'background: #f8d7da; color: #721c24;';
                }

                // Histórico HTML
                let historicoHtml = '';
                if (transferenciasHistorico.length > 0) {
                    historicoHtml = `
                        <div style="max-height: 120px; overflow-y: auto; font-size: 11px;">
                            ${transferenciasHistorico.slice(0, 3).map(transfer => {
                                const dataTransferencia = transfer.dataHora?.toDate?.() || new Date(transfer.dataHora?.seconds * 1000 || 0);
                                const dataFormatada = dataTransferencia.toLocaleString('pt-BR', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                });

                                return `
                                    <div style="background: #f8f9fa; padding: 4px 6px; margin-bottom: 2px; border-radius: 4px; border-left: 3px solid #28a745;">
                                        <div style="font-weight: 600; color: #28a745;">
                                            ${transfer.quantidade?.toFixed(3) || '0.000'} ${produto.unidade}
                                        </div>
                                        <div style="color: #6c757d; font-size: 10px;">
                                            📅 ${dataFormatada}
                                        </div>
                                        <div style="color: #6c757d; font-size: 10px;">
                                            👤 ${transfer.usuario || 'Sistema'}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                            ${transferenciasHistorico.length > 3 ? `
                                <div style="text-align: center; font-size: 10px; color: #6c757d; margin-top: 4px;">
                                    +${transferenciasHistorico.length - 3} mais...
                                </div>
                            ` : ''}
                        </div>
                    `;
                } else {
                    historicoHtml = `
                        <div style="text-align: center; color: #6c757d; font-size: 11px; padding: 8px;">
                            <div style="opacity: 0.5;">📋</div>
                            <div>Nenhuma transferência</div>
                        </div>
                    `;
                }

                const row = document.createElement('tr');
                if (inconsistente) {
                    row.classList.add('inconsistent-transfer');
                }

                row.innerHTML = `
                    <td style="padding: 10px 8px; font-weight: 600; color: #2c3e50;">${produto.codigo}</td>
                    <td style="padding: 10px 8px; color: #495057;" title="${produto.descricao}">
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${produto.descricao}
                        </div>
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: #2c3e50;">
                        ${necessario.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: ${inconsistente ? '#f39c12' : '#17a2b8'};">
                        ${transferidoOP.toFixed(3)} ${produto.unidade}
                        ${inconsistente ? '<br><small style="color: #f39c12;">⚠️ Inconsistente</small>' : ''}
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: #28a745;">
                        ${transferidoReal.toFixed(3)} ${produto.unidade}
                        <div style="width: 100%; background: #e9ecef; border-radius: 10px; height: 6px; margin-top: 4px;">
                            <div style="width: ${progressPercent}%; background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
                        </div>
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: ${restante > 0 ? '#dc3545' : '#28a745'};">
                        ${restante.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="padding: 10px 8px; text-align: center;">
                        <span style="padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; ${statusClass}">
                            ${status}
                        </span>
                    </td>
                    <td style="padding: 10px 8px; max-width: 250px;">
                        ${historicoHtml}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function carregarMateriaisOP(ordem) {
            if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) {
                console.log(`⚠️ OP ${ordem.numero} não possui materiais necessários`);
                return;
            }

            const materiaisMP = ordem.materiaisNecessarios.filter(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto || produto.tipo !== 'MP') return false;

                const temQuantidade = (material.necessidade > 0) || (material.quantidade > 0);
                return temQuantidade;
            });

            console.log(`📦 ${materiaisMP.length} materiais MP encontrados para OP ${ordem.numero}`);
            return materiaisMP;
        }

        function atualizarInformacoesOP(ordem) {
            const produto = produtos.find(p => p.id === ordem.produtoId);
            const armazem = armazens.find(a => a.id === ordem.armazemProducaoId);

            document.getElementById('opInfoNumero').textContent = ordem.numero || 'N/A';
            document.getElementById('opInfoProduto').textContent = produto ?
                `${produto.codigo} - ${produto.descricao}` : 'N/A';
            document.getElementById('opInfoQuantidade').textContent = ordem.quantidade ?
                `${ordem.quantidade} ${produto?.unidade || 'UN'}` : 'N/A';
            document.getElementById('opInfoArmazem').textContent = armazem ?
                `${armazem.codigo} - ${armazem.nome}` : 'N/A';
        }

        function ocultarPreviewMateriais() {
            document.getElementById('opMaterialsPreview').style.display = 'none';
            selectedOrder = null;
        }

        // ========================================
        // 🆕 SISTEMA DE TRANSFERÊNCIAS CORRIGIDO
        // ========================================

        let materiaisSelecionados = [];
        let transferenciasEmAndamento = false;

        function mostrarSecaoTransferencias() {
            if (!selectedOrder) {
                mostrarNotificacao('Selecione uma OP primeiro!', 'warning');
                return;
            }

            const section = document.getElementById('transferSection');
            carregarMateriaisParaTransferencia();
            popularArmazensOrigem();
            configurarFiltrosTransferencia();
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        function carregarMateriaisParaTransferencia() {
            if (!selectedOrder || !selectedOrder.materiaisNecessarios) return;

            const materiaisMP = selectedOrder.materiaisNecessarios.filter(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto || produto.tipo !== 'MP') return false;
                return (material.necessidade > 0) || (material.quantidade > 0);
            });

            atualizarTabelaTransferenciasCorrigida(materiaisMP);
        }

        function atualizarTabelaTransferenciasCorrigida(materiais) {
            const tbody = document.getElementById('transferTableBody');
            tbody.innerHTML = '';

            if (materiais.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 30px; color: #6c757d;">
                            <i class="fas fa-box-open" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                            <strong>Nenhum material disponível para transferência</strong>
                        </td>
                    </tr>
                `;
                return;
            }

            materiais.forEach(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto) return;

                const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);

                // 🆕 Usar dados do histórico real
                const historico = historicoTransferenciasCache.get(selectedOrder.id) || {};
                const transferenciasHistorico = historico[material.produtoId] || [];
                const transferidoReal = transferenciasHistorico.reduce((total, t) => 
                    total + (t.quantidade || 0), 0
                );

                const restante = calcularRestante(necessario, transferidoReal);
                const estoqueDisponivel = calcularEstoqueDisponivel(material.produtoId);

                // Status baseado no histórico real
                let status = 'PENDENTE';
                let statusClass = 'danger';
                if (isTransferenciaCompleta(necessario, transferidoReal)) {
                    status = 'COMPLETO';
                    statusClass = 'success';
                } else if (transferidoReal > 0) {
                    status = 'PARCIAL';
                    statusClass = 'warning';
                }

                // 🆕 Desabilitar se já completo
                const desabilitado = status === 'COMPLETO';
                const quantidadeMaxima = Math.min(estoqueDisponivel, restante);

                const row = document.createElement('tr');
                if (desabilitado) {
                    row.style.opacity = '0.6';
                }

                row.innerHTML = `
                    <td style="text-align: center;">
                        <input type="checkbox" class="material-checkbox"
                               data-produto-id="${material.produtoId}"
                               data-necessario="${restante}"
                               onchange="atualizarSelecao()"
                               ${desabilitado ? 'disabled' : ''}>
                    </td>
                    <td style="font-weight: 600;">${produto.codigo}</td>
                    <td title="${produto.descricao}">
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${produto.descricao}
                        </div>
                    </td>
                    <td style="text-align: center;">
                        <select class="form-control armazem-origem-select" data-produto-id="${material.produtoId}" ${desabilitado ? 'disabled' : ''}>
                            <option value="">Selecionar...</option>
                        </select>
                    </td>
                    <td style="text-align: center; font-weight: 600; color: #28a745;">
                        ${estoqueDisponivel.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="text-align: center; font-weight: 600;">
                        ${restante.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="text-align: center;">
                        <input type="number" class="form-control quantidade-input"
                               data-produto-id="${material.produtoId}"
                               min="0" max="${quantidadeMaxima}"
                               step="0.001" value="${quantidadeMaxima.toFixed(3)}"
                               style="width: 100px;" ${desabilitado ? 'disabled' : ''}>
                    </td>
                    <td style="text-align: center;">
                        <span class="badge ${statusClass}">${status}</span>
                    </td>
                    <td style="text-align: center;">
                        <button class="btn ${material.aguardandoMaterial ? 'btn-warning' : 'btn-outline-warning'} btn-sm"
                                onclick="toggleAguardandoMaterial('${material.produtoId}')"
                                title="${material.aguardandoMaterial ? 'Material em aguardo' : 'Marcar como aguardando'}"
                                ${desabilitado ? 'disabled' : ''}>
                            <i class="fas ${material.aguardandoMaterial ? 'fa-clock' : 'fa-clock-o'}"></i>
                        </button>
                    </td>
                    <td style="text-align: center;">
                        <button class="btn btn-info btn-sm" onclick="mostrarHistoricoMaterial('${material.produtoId}')"
                                title="Ver histórico">
                            <i class="fas fa-history"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            popularArmazensOrigemPorMaterial();
        }

        function calcularEstoqueDisponivel(produtoId) {
            const estoquesTotais = estoques.filter(e => e.produtoId === produtoId);
            return estoquesTotais.reduce((total, estoque) => {
                const saldo = estoque.saldo || 0;
                const reservado = estoque.saldoReservado || 0;
                const empenhado = estoque.saldoEmpenhado || 0;
                const disponivel = Math.max(0, saldo - reservado - empenhado);
                return total + disponivel;
            }, 0);
        }

        function popularArmazensOrigem() {
            const select = document.getElementById('armazemOrigemSelect');
            select.innerHTML = '<option value="">Todos os armazéns...</option>';

            const armazensComEstoque = armazens.filter(a => a.tipo !== 'PRODUCAO');
            armazensComEstoque.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                select.appendChild(option);
            });
        }

        function popularArmazensOrigemPorMaterial() {
            document.querySelectorAll('.armazem-origem-select').forEach(select => {
                const produtoId = select.dataset.produtoId;
                select.innerHTML = '<option value="">Selecionar...</option>';

                const estoquesComSaldo = estoques.filter(e =>
                    e.produtoId === produtoId && e.saldo > 0
                );

                estoquesComSaldo.forEach(estoque => {
                    const armazem = armazens.find(a => a.id === estoque.armazemId);
                    if (armazem && armazem.tipo !== 'PRODUCAO') {
                        const option = document.createElement('option');
                        option.value = armazem.id;
                        option.textContent = `${armazem.codigo} - ${estoque.saldo.toFixed(3)}`;
                        select.appendChild(option);
                    }
                });

                select.addEventListener('change', function() {
                    atualizarQuantidadeDisponivel(produtoId, this.value);
                });
            });
        }

        function atualizarQuantidadeDisponivel(produtoId, armazemId) {
            const quantidadeInput = document.querySelector(`input[data-produto-id="${produtoId}"]`);
            const checkbox = document.querySelector(`input[data-produto-id="${produtoId}"][type="checkbox"]`);

            if (!armazemId) {
                quantidadeInput.disabled = true;
                checkbox.disabled = true;
                return;
            }

            const estoque = estoques.find(e =>
                e.produtoId === produtoId && e.armazemId === armazemId
            );

            const disponivel = estoque ? estoque.saldo : 0;
            const necessario = parseFloat(checkbox.dataset.necessario);
            const maxTransferir = Math.min(disponivel, necessario);

            quantidadeInput.max = maxTransferir;
            quantidadeInput.value = maxTransferir.toFixed(3);
            quantidadeInput.disabled = false;
            checkbox.disabled = disponivel <= 0;

            quantidadeInput.addEventListener('input', function() {
                const valor = parseFloat(this.value);
                if (valor > disponivel || valor > necessario) {
                    this.style.borderColor = '#dc3545';
                    this.title = 'Quantidade inválida';
                } else {
                    this.style.borderColor = '#28a745';
                    this.title = 'Quantidade válida';
                }
            });
        }

        function configurarFiltrosTransferencia() {
            document.getElementById('materialSearch').addEventListener('input', filtrarTabelaTransferencias);
            document.getElementById('statusFilter').addEventListener('change', filtrarTabelaTransferencias);
            document.getElementById('armazemOrigemSelect').addEventListener('change', filtrarTabelaTransferencias);
        }

        function filtrarTabelaTransferencias() {
            const busca = document.getElementById('materialSearch').value.toLowerCase();
            const statusFiltro = document.getElementById('statusFilter').value;
            const armazemFiltro = document.getElementById('armazemOrigemSelect').value;

            const rows = document.querySelectorAll('#transferTableBody tr');

            rows.forEach(row => {
                if (row.cells.length === 1) return;

                const codigo = row.cells[1].textContent.toLowerCase();
                const descricao = row.cells[2].textContent.toLowerCase();
                const status = row.cells[7].textContent.trim();
                const armazemSelect = row.querySelector('.armazem-origem-select');

                const matchBusca = !busca || codigo.includes(busca) || descricao.includes(busca);
                const matchStatus = !statusFiltro || status === statusFiltro;
                const matchArmazem = !armazemFiltro || armazemSelect.value === armazemFiltro;

                row.style.display = matchBusca && matchStatus && matchArmazem ? '' : 'none';
            });
        }

        window.selecionarTodos = function(checked) {
            document.querySelectorAll('.material-checkbox:not(:disabled)').forEach(checkbox => {
                checkbox.checked = checked;
            });
            atualizarSelecao();
        };

        window.atualizarSelecao = function() {
            const checkboxes = document.querySelectorAll('.material-checkbox:checked');
            materiaisSelecionados = Array.from(checkboxes).map(cb => ({
                produtoId: cb.dataset.produtoId,
                necessario: parseFloat(cb.dataset.necessario)
            }));

            document.getElementById('transferCount').textContent = `${materiaisSelecionados.length} selecionados`;
            document.getElementById('btnTransferir').disabled = materiaisSelecionados.length === 0;

            document.querySelectorAll('.quantidade-input').forEach(input => {
                const checkbox = document.querySelector(`input[data-produto-id="${input.dataset.produtoId}"][type="checkbox"]`);
                input.disabled = !checkbox.checked;
            });
        };

        window.limparSelecao = function() {
            document.querySelectorAll('.material-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            atualizarSelecao();
        };

        // ========================================
        // 🆕 EXECUÇÃO DE TRANSFERÊNCIAS COM VALIDAÇÃO ANTI-DUPLICAÇÃO
        // ========================================

        window.executarTransferencias = async function() {
            if (materiaisSelecionados.length === 0) {
                mostrarNotificacao('Nenhum material selecionado!', 'warning');
                return;
            }

            if (transferenciasEmAndamento) {
                mostrarNotificacao('Transferências já em andamento!', 'warning');
                return;
            }

            const confirmacao = confirm(`Confirma a transferência de ${materiaisSelecionados.length} material(is)?`);
            if (!confirmacao) return;

            transferenciasEmAndamento = true;
            document.getElementById('btnTransferir').disabled = true;
            document.getElementById('btnTransferir').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Transferindo...';

            try {
                const resultados = await processarTransferenciasLoteCorrigido();
                mostrarResultadosTransferencia(resultados);

                const sucessos = resultados.filter(r => r.success).length;
                if (sucessos > 0) {
                    setTimeout(() => {
                        limparSelecao();
                    }, 2000);
                }

            } catch (error) {
                console.error('Erro nas transferências:', error);
                mostrarNotificacao('Erro ao executar transferências: ' + error.message, 'error');
            } finally {
                transferenciasEmAndamento = false;
                document.getElementById('btnTransferir').disabled = false;
                document.getElementById('btnTransferir').innerHTML = '<i class="fas fa-paper-plane"></i> Transferir Selecionados';
            }
        };

        async function processarTransferenciasLoteCorrigido() {
            const resultados = [];

            for (const material of materiaisSelecionados) {
                const armazemSelect = document.querySelector(`.armazem-origem-select[data-produto-id="${material.produtoId}"]`);
                const quantidadeInput = document.querySelector(`.quantidade-input[data-produto-id="${material.produtoId}"]`);

                if (!armazemSelect || !quantidadeInput || !armazemSelect.value || !quantidadeInput.value) {
                    resultados.push({
                        produtoId: material.produtoId,
                        success: false,
                        error: 'Armazém origem ou quantidade não informados'
                    });
                    continue;
                }

                const quantidadeSolicitada = parseFloat(quantidadeInput.value);

                // 🆕 VALIDAÇÃO ANTI-DUPLICAÇÃO CORRIGIDA
                const produto = produtos.find(p => p.id === material.produtoId);
                const materialOP = selectedOrder.materiaisNecessarios.find(m => m.produtoId === material.produtoId);
                const necessario = materialOP?.necessidade || 0;

                // Usar dados do histórico real
                const historico = historicoTransferenciasCache.get(selectedOrder.id) || {};
                const transferenciasHistorico = historico[material.produtoId] || [];
                const jaTransferido = transferenciasHistorico.reduce((total, t) => 
                    total + (t.quantidade || 0), 0
                );

                const restanteReal = Math.max(0, necessario - jaTransferido);

                // Validar quantidade solicitada
                if (quantidadeSolicitada > restanteReal + 0.001) {
                    resultados.push({
                        produtoId: material.produtoId,
                        success: false,
                        error: `Quantidade solicitada (${quantidadeSolicitada.toFixed(3)}) excede o restante necessário (${restanteReal.toFixed(3)}). Já transferido: ${jaTransferido.toFixed(3)} de ${necessario.toFixed(3)} necessário.`
                    });
                    continue;
                }

                const transferData = {
                    produtoId: material.produtoId,
                    armazemOrigemId: armazemSelect.value,
                    armazemDestinoId: selectedOrder.armazemProducaoId,
                    quantidade: quantidadeSolicitada,
                    ordemProducaoId: selectedOrder.id,
                    observacoes: `Transferência para OP ${selectedOrder.numero} - Sistema corrigido`
                };

                try {
                    const resultado = await TransactionManager.executeAtomicTransfer(transferData);

                    if (resultado.success) {
                        resultados.push({
                            produtoId: material.produtoId,
                            success: true,
                            data: resultado.data
                        });
                    } else {
                        resultados.push({
                            produtoId: material.produtoId,
                            success: false,
                            error: resultado.error
                        });
                    }
                } catch (error) {
                    console.error('❌ Erro na transferência:', error);
                    resultados.push({
                        produtoId: material.produtoId,
                        success: false,
                        error: error.message
                    });
                }
            }

            return resultados;
        }

        function mostrarResultadosTransferencia(resultados) {
            const sucessos = resultados.filter(r => r.success).length;
            const erros = resultados.filter(r => !r.success).length;

            if (sucessos > 0) {
                mostrarNotificacao(`✅ ${sucessos} transferência(s) realizada(s) com sucesso!`, 'success');

                setTimeout(async () => {
                    await Promise.all([
                        carregarTransferencias(),
                        carregarEstoques(),
                        carregarOrdens()
                    ]);

                    if (selectedOrder) {
                        const opAtualizada = ordens.find(op => op.id === selectedOrder.id);
                        if (opAtualizada) {
                            selectedOrder = opAtualizada;
                            await carregarHistoricoTransferenciasOP(selectedOrder.id);
                            mostrarPreviewMateriais(selectedOrder);
                            carregarMateriaisParaTransferencia();
                        }
                    }
                }, 1000);
            }

            if (erros > 0) {
                let mensagem = `❌ ${erros} transferência(s) falharam:\n\n`;
                resultados.filter(r => !r.success).forEach(erro => {
                    const produto = produtos.find(p => p.id === erro.produtoId);
                    mensagem += `• ${produto?.codigo || 'N/A'}: ${erro.error}\n`;
                });
                alert(mensagem);
            }
        }

        // ========================================
        // 🔧 CLASSE DE TRANSAÇÃO ATÔMICA
        // ========================================

        class TransactionManager {
            static async executeAtomicTransfer(transferData) {
                try {
                    const result = await runTransaction(db, async (transaction) => {
                        // Validar estoque origem
                        const estoqueOrigemQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", transferData.produtoId),
                            where("armazemId", "==", transferData.armazemOrigemId)
                        );
                        const estoqueOrigemSnapshot = await getDocs(estoqueOrigemQuery);

                        if (estoqueOrigemSnapshot.empty) {
                            throw new Error('Estoque origem não encontrado');
                        }

                        const estoqueOrigemDoc = estoqueOrigemSnapshot.docs[0];
                        const estoqueOrigemData = estoqueOrigemDoc.data();

                        if (estoqueOrigemData.saldo < transferData.quantidade) {
                            throw new Error(`Saldo insuficiente: ${estoqueOrigemData.saldo} disponível`);
                        }

                        // Buscar ou criar estoque destino
                        const estoqueDestinoQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", transferData.produtoId),
                            where("armazemId", "==", transferData.armazemDestinoId)
                        );
                        const estoqueDestinoSnapshot = await getDocs(estoqueDestinoQuery);

                        let estoqueDestinoRef, estoqueDestinoData;

                        if (estoqueDestinoSnapshot.empty) {
                            estoqueDestinoRef = doc(collection(db, "estoques"));
                            estoqueDestinoData = { saldo: 0 };
                        } else {
                            estoqueDestinoRef = estoqueDestinoSnapshot.docs[0].ref;
                            estoqueDestinoData = estoqueDestinoSnapshot.docs[0].data();
                        }

                        // Gerar número da transferência
                        const numeroTransferencia = `TRANSF-${Date.now()}`;

                        // Criar registro de transferência
                        const transferenciaRef = doc(collection(db, "transferenciasArmazem"));
                        const produto = produtos.find(p => p.id === transferData.produtoId);
                        const armazemOrigem = armazens.find(a => a.id === transferData.armazemOrigemId);
                        const armazemDestino = armazens.find(a => a.id === transferData.armazemDestinoId);

                        const transferencia = {
                            ordemProducaoId: transferData.ordemProducaoId,
                            produtoId: transferData.produtoId,
                            armazemOrigemId: transferData.armazemOrigemId,
                            armazemDestinoId: transferData.armazemDestinoId,
                            quantidade: transferData.quantidade,
                            codigo: produto?.codigo || 'N/A',
                            descricao: produto?.descricao || 'N/A',
                            unidade: produto?.unidade || 'UN',
                            armazemOrigemNome: armazemOrigem?.nome || 'N/A',
                            armazemDestinoNome: armazemDestino?.nome || 'N/A',
                            tipo: 'OP',
                            status: 'CONCLUIDA',
                            dataHora: Timestamp.now(),
                            numeroTransferencia: numeroTransferencia,
                            observacoes: transferData.observacoes || '',
                            usuario: 'Sistema',
                            usuarioId: 'sistema',
                            origem: 'SISTEMA_CORRIGIDO',
                            versao: '2.0'
                        };

                        transaction.set(transferenciaRef, transferencia);

                        // Atualizar estoque origem
                        transaction.update(estoqueOrigemDoc.ref, {
                            saldo: estoqueOrigemData.saldo - transferData.quantidade,
                            ultimaMovimentacao: Timestamp.now()
                        });

                        // Atualizar estoque destino
                        if (estoqueDestinoSnapshot.empty) {
                            transaction.set(estoqueDestinoRef, {
                                produtoId: transferData.produtoId,
                                armazemId: transferData.armazemDestinoId,
                                saldo: transferData.quantidade,
                                saldoReservado: 0,
                                saldoEmpenhado: 0,
                                ultimaMovimentacao: Timestamp.now(),
                                criadoEm: Timestamp.now()
                            });
                        } else {
                            transaction.update(estoqueDestinoRef, {
                                saldo: estoqueDestinoData.saldo + transferData.quantidade,
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        // Atualizar OP
                        const opRef = doc(db, "ordensProducao", transferData.ordemProducaoId);
                        const ordem = ordens.find(op => op.id === transferData.ordemProducaoId);

                        if (ordem && ordem.materiaisNecessarios) {
                            const materiaisAtualizados = ordem.materiaisNecessarios.map(material => {
                                if (material.produtoId === transferData.produtoId) {
                                    return {
                                        ...material,
                                        saldoReservado: (material.saldoReservado || 0) + transferData.quantidade
                                    };
                                }
                                return material;
                            });

                            transaction.update(opRef, {
                                materiaisNecessarios: materiaisAtualizados,
                                ultimaAtualizacao: Timestamp.now()
                            });
                        }

                        return {
                            transferenciaId: transferenciaRef.id,
                            numeroTransferencia: numeroTransferencia,
                            status: 'CONCLUIDA'
                        };
                    });

                    return { success: true, data: result };

                } catch (error) {
                    console.error('❌ Erro na transferência atômica:', error);
                    return { success: false, error: error.message };
                }
            }
        }

        // ========================================
        // 🔔 SISTEMA DE NOTIFICAÇÕES
        // ========================================

        function mostrarNotificacao(mensagem, tipo = 'info') {
            const notification = document.getElementById('notification');
            notification.className = `notification ${tipo}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span>${mensagem}</span>
                    <button onclick="this.parentElement.parentElement.style.display='none'"
                            style="background: none; border: none; font-size: 18px; cursor: pointer; opacity: 0.7;">
                        ×
                    </button>
                </div>
            `;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // ========================================
        // 🆕 FUNÇÕES AUXILIARES E DIAGNÓSTICO
        // ========================================

        window.diagnosticarSistema = async function() {
            console.log('🔍 Diagnóstico completo do sistema...');

            let relatorio = `🔍 DIAGNÓSTICO COMPLETO DO SISTEMA\n\n`;
            relatorio += `📊 Estatísticas Gerais:\n`;
            relatorio += `• Ordens carregadas: ${ordens.length}\n`;
            relatorio += `• Produtos carregados: ${produtos.length}\n`;
            relatorio += `• Transferências carregadas: ${transferencias.length}\n`;
            relatorio += `• Cache de histórico: ${historicoTransferenciasCache.size} OPs\n\n`;

            if (selectedOrder) {
                relatorio += `🎯 OP Selecionada: ${selectedOrder.numero}\n`;
                const inconsistencias = await detectarInconsistencias(selectedOrder.id);
                relatorio += `• Inconsistências detectadas: ${inconsistencias.length}\n`;

                if (inconsistencias.length > 0) {
                    relatorio += `\n⚠️ Detalhes das Inconsistências:\n`;
                    inconsistencias.forEach((inc, idx) => {
                        relatorio += `${idx + 1}. ${inc.codigo}: OP=${inc.saldoOP.toFixed(3)}, Real=${inc.transferidoReal.toFixed(3)}\n`;
                    });
                }
            }

            relatorio += `\n✅ Sistema funcionando corretamente!`;
            alert(relatorio);
        };

        window.mostrarDetalhesOP = function() {
            if (!selectedOrder) {
                mostrarNotificacao('Nenhuma OP selecionada!', 'warning');
                return;
            }

            const detalhes = `
OP: ${selectedOrder.numero}
Status: ${selectedOrder.status}
Produto: ${produtos.find(p => p.id === selectedOrder.produtoId)?.descricao || 'N/A'}
Quantidade: ${selectedOrder.quantidade || 'N/A'}
Data Criação: ${selectedOrder.dataCriacao ? new Date(selectedOrder.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}
Materiais Necessários: ${selectedOrder.materiaisNecessarios?.length || 0}
            `;

            alert(detalhes);
        };

        window.mostrarHistoricoMaterial = function(produtoId) {
            const produto = produtos.find(p => p.id === produtoId);
            const historico = historicoTransferenciasCache.get(selectedOrder.id) || {};
            const transferenciasHistorico = historico[produtoId] || [];

            if (transferenciasHistorico.length === 0) {
                alert(`📋 Histórico do Material: ${produto?.codigo || 'N/A'}\n\nNenhuma transferência encontrada para este material.`);
                return;
            }

            let detalhes = `📋 Histórico do Material: ${produto?.codigo || 'N/A'}\n`;
            detalhes += `📦 Descrição: ${produto?.descricao || 'N/A'}\n\n`;
            detalhes += `📊 Total de transferências: ${transferenciasHistorico.length}\n\n`;

            transferenciasHistorico.slice(0, 10).forEach((transfer, index) => {
                const dataTransferencia = transfer.dataHora?.toDate?.() || new Date(transfer.dataHora?.seconds * 1000 || 0);
                const dataFormatada = dataTransferencia.toLocaleString('pt-BR');

                detalhes += `${index + 1}. ${transfer.quantidade?.toFixed(3) || '0.000'} ${produto?.unidade || 'UN'}\n`;
                detalhes += `   📅 ${dataFormatada}\n`;
                detalhes += `   👤 ${transfer.usuario || 'Sistema'}\n`;
                if (transfer.observacoes) {
                    detalhes += `   📝 ${transfer.observacoes}\n`;
                }
                detalhes += `\n`;
            });

            if (transferenciasHistorico.length > 10) {
                detalhes += `... e mais ${transferenciasHistorico.length - 10} transferências\n`;
            }

            alert(detalhes);
        };

        window.toggleAguardandoMaterial = async function(produtoId) {
            if (!selectedOrder) return;

            try {
                const materialIndex = selectedOrder.materiaisNecessarios.findIndex(m => m.produtoId === produtoId);
                if (materialIndex === -1) return;

                const material = selectedOrder.materiaisNecessarios[materialIndex];
                const novoStatus = !material.aguardandoMaterial;

                const materiaisAtualizados = [...selectedOrder.materiaisNecessarios];
                materiaisAtualizados[materialIndex] = {
                    ...material,
                    aguardandoMaterial: novoStatus,
                    dataAguardando: novoStatus ? Timestamp.now() : null
                };

                await updateDoc(doc(db, "ordensProducao", selectedOrder.id), {
                    materiaisNecessarios: materiaisAtualizados
                });

                selectedOrder.materiaisNecessarios = materiaisAtualizados;
                carregarMateriaisParaTransferencia();

                const produto = produtos.find(p => p.id === produtoId);
                const status = novoStatus ? 'aguardando material' : 'disponível para transferência';
                mostrarNotificacao(`Material ${produto?.codigo} marcado como ${status}`, 'info');

            } catch (error) {
                console.error('Erro ao alterar status do material:', error);
                mostrarNotificacao('Erro ao alterar status do material', 'error');
            }
        };

        window.corrigirNecessidadesOPs = async function() {
            if (!confirm('Deseja recalcular as necessidades de todas as OPs?')) return;

            let opsCorrigidas = 0;
            let erros = 0;

            for (const ordem of ordens) {
                try {
                    if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) continue;

                    let precisaAtualizar = false;
                    const materiaisAtualizados = ordem.materiaisNecessarios.map(material => {
                        if ((!material.necessidade || material.necessidade <= 0) && material.quantidade > 0 && ordem.quantidade > 0) {
                            const novaNecessidade = material.quantidade * ordem.quantidade;
                            precisaAtualizar = true;
                            return { ...material, necessidade: novaNecessidade };
                        }
                        return material;
                    });

                    if (precisaAtualizar) {
                        await updateDoc(doc(db, "ordensProducao", ordem.id), {
                            materiaisNecessarios: materiaisAtualizados,
                            ultimaCorrecaoNecessidades: Timestamp.now()
                        });
                        opsCorrigidas++;
                    }

                } catch (error) {
                    console.error(`Erro ao corrigir OP ${ordem.numero}:`, error);
                    erros++;
                }
            }

            mostrarNotificacao(`✅ Correção concluída: ${opsCorrigidas} OPs corrigidas, ${erros} erros`, 'success');

            await carregarOrdens();
            popularDropdownOPs();

            if (selectedOrder) {
                const opAtualizada = ordens.find(op => op.id === selectedOrder.id);
                if (opAtualizada) {
                    selectedOrder = opAtualizada;
                    mostrarPreviewMateriais(selectedOrder);
                }
            }
        };

        window.sincronizarStatusTransferencias = async function() {
            mostrarNotificacao('Funcionalidade de sincronização com apontamentos será implementada', 'info');
        };

        window.abrirRelatorioProducao = function() {
            window.open('relatorio_materiais_producao.html', '_blank');
        };

        window.analisarMateriaisRestantes = function() {
            mostrarNotificacao('Análise de materiais será implementada', 'info');
        };

        // Marcar versão do sistema
        console.log('✅ Sistema de Movimentação de Armazéns v2.0 - CORRIGIDO carregado com sucesso!');
        window.sistemaMovimentacaoVersao = '2.0-CORRIGIDO';

    </script>
</body>
</html>