firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T13:32:21.259Z', apps: 1}
recebimento_materiais_avancado.html:1097 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1173 Dados carregados: {pedidos: 39, produtos: 1672, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1183 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', dataCriacao: Timestamp, sincronizadoPor: 'Sistema - Correção de Rastreabilidade', criadoPor: 'Alex', atualizacoesEntrega: Array(1), …}
recebimento_materiais_avancado.html:1185 📦 Exemplo de item: {produtoId: '1sHECTgPNMjBw0ScLbbq', valorUnitario: 1805.17, quantidade: 1, unidade: 'PC', codigo: '110732', …}
 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', descricao: 'FG 3/4 JIC X 1/2', origem: '0', inspecaoRecebimento: 'nao', tipo: 'MP', …}
 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', codigoArea: '', cep: '15400-000', cnpjCpf3: '', email: '<EMAIL>', …}
 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
 📋 Pedidos de Compra: 39
 🏷️ Produtos: 1672
 🏢 Fornecedores: 778
 🏪 Armazéns: 9
 📋 Estrutura do pedido: (22) ['id', 'dataCriacao', 'sincronizadoPor', 'criadoPor', 'atualizacoesEntrega', 'historico', 'aprovadoPor', 'numeroAnterior', 'cotacaoId', 'ultimaAtualizacao', 'limpoPor', 'numero', 'fornecedorId', 'valorTotal', 'alteradoPor', 'condicaoPagamento', 'prazoEntrega', 'status', 'solicitacaoId', 'ultimaLimpezaCritica', 'itens', 'dataAprovacao']
 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', dataCriacao: Timestamp, sincronizadoPor: 'Sistema - Correção de Rastreabilidade', criadoPor: 'Alex', atualizacoesEntrega: Array(1), …}
 📦 Estrutura do item: (7) ['produtoId', 'valorUnitario', 'quantidade', 'unidade', 'codigo', 'descricao', 'valorTotal']
 📦 Item exemplo: {produtoId: '1sHECTgPNMjBw0ScLbbq', valorUnitario: 1805.17, quantidade: 1, unidade: 'PC', codigo: '110732', …}
 🏢 Estrutura do fornecedor: (71) ['id', 'codigoArea', 'cep', 'cnpjCpf3', 'email', 'telefone3', 'cargo3', 'emailNfe', 'email2', 'autorizaXml1', 'indicacao', 'inscricaoEstadual', 'dataCadastro', 'codigoPais', 'cotacao', 'categoriaPrincipal', 'contaContabil', 'fax', 'bairro', 'contato3', 'departamento3', 'telefone2', 'numero', 'email1', 'longitudeCLI', 'suframa', 'cidade', 'autorizaXml2', 'codigoClassificacao', 'reducao', 'simplesNacional', 'codCusto', 'latitudeCLI', 'homePage', 'observacoes', 'acrescimoCLI', 'contato2', 'pais', 'celular1', 'categorias', 'limite', 'telefone1', 'temSubstituicao', 'codigo', 'ativo', 'statusHomologacao', 'inscricaoMunicipal', 'email3', 'ultimaCompra', 'cnpjCpf2', 'cargo2', 'departamento2', 'contato1', 'im', 'tipo', 'complemento', 'tipoPessoa', 'codigoVendedor', 'codigoRegiao', 'dataAtualizacao', 'celular3', 'endereco', 'estado', 'nomeFantasia', 'intervista', 'celular2', 'nascimento', 'razaoSocial', 'telefone4', 'cnpjCpf', 'departamento1']
 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', codigoArea: '', cep: '15400-000', cnpjCpf3: '', email: '<EMAIL>', …}
 🏷️ Estrutura do produto: (31) ['id', 'descricao', 'origem', 'inspecaoRecebimento', 'tipo', 'estoqueMinimo', 'loteCompra', 'corredor', 'rastreabilidadeLote', 'dataCadastro', 'fatorConversao', 'armazemPadraoId', 'cest', 'grupo', 'custoMedio', 'ncm', 'metodoCusteio', 'tipoItem', 'prateleira', 'estoqueMaximo', 'unidadeSecundaria', 'centroCustoObrigatorio', 'familia', 'ultimoCusto', 'precoVenda', 'unidade', 'codigo', 'status', 'posicao', 'margemLucro', 'pontoPedido']
 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', descricao: 'FG 3/4 JIC X 1/2', origem: '0', inspecaoRecebimento: 'nao', tipo: 'MP', …}
 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
 📋 DEBUG populateOrderSelect - Total de pedidos: 39
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-949381 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'RECEBIDO_PARCIAL', temItens: 5}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1396 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1401 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1373 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1381 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1414 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 14 de 39 total
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (RECEBIDO_PARCIAL)
recebimento_materiais_avancado.html:1438 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1441 🎯 DEBUG populateOrderSelect - Lista atualizada com 14 pedidos
recebimento_materiais_avancado.html:1344 📊 Dashboard atualizado: {pendentes: 13, atrasados: 0, parciais: 1, completos: 23}
recebimento_materiais_avancado.html:2772 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2781 Dados encontrados: {movimentacoes: 3495, estoqueQualidade: 63, recebimentosDetalhes: 19}
recebimento_materiais_avancado.html:2895 Total de registros de histórico encontrados: 108
recebimento_materiais_avancado.html:2919 Erro ao carregar histórico: ReferenceError: codigoProduto is not defined
    at recebimento_materiais_avancado.html:3070:17
    at Array.forEach (<anonymous>)
    at renderReceiptHistory (recebimento_materiais_avancado.html:2935:26)
    at window.loadReceiptHistory (recebimento_materiais_avancado.html:2914:23)
window.loadReceiptHistory @ recebimento_materiais_avancado.html:2919
