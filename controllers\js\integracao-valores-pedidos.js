
/**
 * 💰 SERVIÇO DE INTEGRAÇÃO DE VALORES DOS PEDIDOS DE COMPRA
 * Carrega valores unitários dos pedidos aprovados para produtos e estoques
 */

import { db } from '../firebase-config.js';
import { 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  query, 
  where, 
  Timestamp,
  runTransaction
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class IntegracaoValoresPedidos {
  
  /**
   * Carregar valores de todos os pedidos aprovados
   */
  static async carregarValoresPedidosAprovados() {
    try {
      console.log('💰 Iniciando carregamento de valores dos pedidos...');
      
      // Buscar pedidos aprovados
      const pedidosQuery = query(
        collection(db, "pedidosCompra"),
        where("status", "==", "APROVADO")
      );
      
      const pedidosSnap = await getDocs(pedidosQuery);
      const resultados = {
        pedidosProcessados: 0,
        produtosAtualizados: 0,
        estoquesAtualizados: 0,
        valorTotalProcessado: 0,
        erros: []
      };
      
      for (const pedidoDoc of pedidosSnap.docs) {
        try {
          const pedido = { id: pedidoDoc.id, ...pedidoDoc.data() };
          const resultado = await this.processarPedido(pedido);
          
          resultados.pedidosProcessados++;
          resultados.produtosAtualizados += resultado.produtosAtualizados;
          resultados.estoquesAtualizados += resultado.estoquesAtualizados;
          resultados.valorTotalProcessado += resultado.valorProcessado;
          
        } catch (error) {
          console.error(`Erro ao processar pedido ${pedidoDoc.id}:`, error);
          resultados.erros.push({
            pedidoId: pedidoDoc.id,
            erro: error.message
          });
        }
      }
      
      console.log('✅ Carregamento de valores concluído:', resultados);
      return resultados;
      
    } catch (error) {
      console.error('❌ Erro no carregamento de valores:', error);
      throw error;
    }
  }
  
  /**
   * Processar um pedido específico
   */
  static async processarPedido(pedido) {
    if (!pedido.itens || pedido.itens.length === 0) {
      return { produtosAtualizados: 0, estoquesAtualizados: 0, valorProcessado: 0 };
    }
    
    const resultado = {
      produtosAtualizados: 0,
      estoquesAtualizados: 0,
      valorProcessado: 0
    };
    
    // Carregar produtos e estoques uma vez
    const [produtosSnap, estoquesSnap] = await Promise.all([
      getDocs(collection(db, "produtos")),
      getDocs(collection(db, "estoques"))
    ]);
    
    const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    for (const item of pedido.itens) {
      if (!item.valorUnitario || item.valorUnitario <= 0) continue;
      
      try {
        // Encontrar produto
        const produto = produtos.find(p => 
          p.codigo === item.codigo || 
          p.id === item.produtoId ||
          p.codigoInterno === item.codigo
        );
        
        if (!produto) {
          console.warn(`Produto não encontrado: ${item.codigo}`);
          continue;
        }
        
        // Atualizar produto
        await this.atualizarProduto(produto.id, item.valorUnitario, pedido);
        resultado.produtosAtualizados++;
        
        // Atualizar estoques relacionados
        const estoquesRelacionados = estoques.filter(e => e.produtoId === produto.id);
        for (const estoque of estoquesRelacionados) {
          await this.atualizarEstoque(estoque.id, estoque.saldo || 0, item.valorUnitario);
          resultado.estoquesAtualizados++;
        }
        
        resultado.valorProcessado += item.valorUnitario * (item.quantidade || 0);
        
      } catch (error) {
        console.error(`Erro ao processar item ${item.codigo}:`, error);
      }
    }
    
    return resultado;
  }
  
  /**
   * Atualizar produto com novo valor
   */
  static async atualizarProduto(produtoId, valorUnitario, pedido) {
    const updateData = {
      precoUnitario: valorUnitario,
      ultimaAtualizacaoPreco: Timestamp.now(),
      origemPreco: 'PEDIDO_COMPRA',
      pedidoOrigemId: pedido.id,
      pedidoOrigemNumero: pedido.numero
    };
    
    await updateDoc(doc(db, "produtos", produtoId), updateData);
  }
  
  /**
   * Atualizar estoque com valor total
   */
  static async atualizarEstoque(estoqueId, saldo, valorUnitario) {
    const valorTotal = saldo * valorUnitario;
    
    const updateData = {
      precoUnitario: valorUnitario,
      valorTotal: valorTotal,
      ultimaAtualizacaoValor: Timestamp.now()
    };
    
    await updateDoc(doc(db, "estoques", estoqueId), updateData);
  }
  
  /**
   * Carregar valores de um pedido específico
   */
  static async carregarValoresPedidoEspecifico(pedidoId) {
    try {
      const pedidoDoc = await getDoc(doc(db, "pedidosCompra", pedidoId));
      if (!pedidoDoc.exists()) {
        throw new Error(`Pedido ${pedidoId} não encontrado`);
      }
      
      const pedido = { id: pedidoDoc.id, ...pedidoDoc.data() };
      return await this.processarPedido(pedido);
      
    } catch (error) {
      console.error('Erro ao carregar valores do pedido específico:', error);
      throw error;
    }
  }
  
  /**
   * Obter relatório de produtos sem valor
   */
  static async relatorioProdutosSemValor() {
    try {
      const produtosSnap = await getDocs(collection(db, "produtos"));
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      const produtosSemValor = produtos.filter(p => 
        !p.precoUnitario || p.precoUnitario <= 0
      );
      
      console.log(`📊 Produtos sem valor: ${produtosSemValor.length} de ${produtos.length}`);
      
      return {
        total: produtos.length,
        semValor: produtosSemValor.length,
        comValor: produtos.length - produtosSemValor.length,
        percentualComValor: ((produtos.length - produtosSemValor.length) / produtos.length * 100).toFixed(1),
        listaSemValor: produtosSemValor.map(p => ({
          codigo: p.codigo,
          descricao: p.descricao,
          tipo: p.tipo
        }))
      };
      
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      throw error;
    }
  }
}

// Exportar para uso global
window.IntegracaoValoresPedidos = IntegracaoValoresPedidos;
