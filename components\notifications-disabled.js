/**
 * NOTIFICATIONS COMPONENT - DESABILITADO
 * Componente simplificado sem queries complexas
 */

export class NotificationsComponent {
  constructor(containerId, userId) {
    this.containerId = containerId;
    this.userId = userId;
    this.notifications = [];
    this.alerts = [];
    this.isVisible = false;
    
    this.initialize();
  }

  async initialize() {
    try {
      this.createNotificationButton();
      this.showDisabledMessage();
    } catch (error) {
      console.error('Erro ao inicializar notificações:', error);
    }
  }

  createNotificationButton() {
    const container = document.getElementById(this.containerId);
    if (!container) return;

    container.innerHTML = `
      <button class="notifications-toggle" onclick="window.notificationsComponent.toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
      </button>
      <div class="notifications-wrapper" id="notificationsWrapper">
        <div class="notifications-header">
          <h3>Notificações</h3>
        </div>
        <div id="notificationsList">
          <div style="padding: 20px; text-align: center; color: #666;">
            Sistema de notificações temporariamente desabilitado
          </div>
        </div>
      </div>
    `;
  }

  showDisabledMessage() {
    const list = document.getElementById('notificationsList');
    if (list) {
      list.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Sistema de notificações temporariamente desabilitado</div>';
    }
  }

  toggleNotifications() {
    const wrapper = document.getElementById('notificationsWrapper');
    if (!wrapper) return;
    
    this.isVisible = !this.isVisible;
    wrapper.classList.toggle('show', this.isVisible);
  }

  async markAsRead() { return true; }
  async markAllAsRead() { return true; }
  async resolveAlert() { return true; }
  async dismissAlert() { return true; }
}