<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Movimentação entre Armazéns - ERP Industrial</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* ========================================
           🎨 DESIGN SYSTEM - ERP INDUSTRIAL
           ======================================== */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 🎨 Paleta de Cores */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --header-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --success-gradient: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            --warning-gradient: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            --danger-gradient: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            --info-gradient: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            
            /* 📏 Espaçamentos */
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 20px;
            --spacing-lg: 30px;
            --spacing-xl: 40px;
            
            /* 🔤 Tipografia */
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size-sm: 12px;
            --font-size-md: 14px;
            --font-size-lg: 16px;
            --font-size-xl: 18px;
            --font-size-xxl: 24px;
            --font-size-title: 28px;
            
            /* 🎯 Bordas e Sombras */
            --border-radius: 8px;
            --border-radius-lg: 15px;
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-md: 0 8px 20px rgba(0,0,0,0.15);
            --shadow-lg: 0 20px 40px rgba(0,0,0,0.1);
            
            /* 🎭 Transições */
            --transition-fast: all 0.2s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s ease;
        }

        body {
            font-family: var(--font-family);
            background: var(--primary-gradient);
            min-height: 100vh;
            padding: var(--spacing-md);
            color: #2c3e50;
        }

        /* ========================================
           🏗️ LAYOUT PRINCIPAL
           ======================================== */
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ========================================
           📱 HEADER RESPONSIVO
           ======================================== */
        
        .header {
            background: var(--header-gradient);
            color: white;
            padding: 25px var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .header h1 {
            font-size: var(--font-size-title);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        /* ========================================
           🔘 SISTEMA DE BOTÕES
           ======================================== */
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: var(--transition-normal);
            font-size: var(--font-size-md);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-primary { background: var(--info-gradient); color: white; }
        .btn-success { background: var(--success-gradient); color: white; }
        .btn-warning { background: var(--warning-gradient); color: white; }
        .btn-danger { background: var(--danger-gradient); color: white; }
        .btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; }

        /* ========================================
           📋 CONTEÚDO PRINCIPAL
           ======================================== */
        
        .main-content {
            padding: var(--spacing-lg);
        }

        .section-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--spacing-md);
            overflow: hidden;
            border: 1px solid #e9ecef;
            transition: var(--transition-normal);
        }

        .section-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .section-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: var(--spacing-md);
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-content {
            padding: var(--spacing-md);
        }

        /* ========================================
           📊 GRID RESPONSIVO
           ======================================== */
        
        .grid {
            display: grid;
            gap: var(--spacing-md);
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

        /* ========================================
           📱 RESPONSIVIDADE
           ======================================== */
        
        @media (max-width: 768px) {
            body {
                padding: var(--spacing-sm);
            }
            
            .header {
                padding: var(--spacing-md);
                text-align: center;
            }
            
            .header h1 {
                font-size: var(--font-size-xl);
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .main-content {
                padding: var(--spacing-md);
            }
            
            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: var(--font-size-sm);
            }
            
            .section-header {
                padding: var(--spacing-sm);
            }
            
            .section-content {
                padding: var(--spacing-sm);
            }
        }

        /* ========================================
           🎯 COMPONENTES ESPECÍFICOS
           ======================================== */
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
            color: #6c757d;
        }

        .loading i {
            margin-right: var(--spacing-sm);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .notification {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-md);
            display: none;
            animation: slideInDown 0.3s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notification.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .notification.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .notification.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .notification.info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        /* ========================================
           📊 COMPONENTES DE ESTATÍSTICAS
           ======================================== */

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            transition: var(--transition-normal);
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-xl);
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: var(--font-size-xxl);
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: var(--font-size-sm);
            color: #6c757d;
            font-weight: 500;
            margin-top: 4px;
        }

        /* ========================================
           📑 SISTEMA DE ABAS
           ======================================== */

        .tabs-container {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        .tabs-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            background: transparent;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: var(--transition-normal);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: #e9ecef;
            color: #495057;
        }

        .tab-button.active {
            color: #007bff;
            background: white;
            border-bottom-color: #007bff;
        }

        .tabs-content {
            min-height: 300px;
        }

        .tab-content {
            display: none;
            padding: var(--spacing-md);
            animation: fadeIn 0.3s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ========================================
           📋 TABELAS RESPONSIVAS
           ======================================== */

        .table-responsive {
            overflow-x: auto;
            border-radius: var(--border-radius);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th {
            background: #f8f9fa;
            padding: var(--spacing-sm) var(--spacing-xs);
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
        }

        .table td {
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* ========================================
           🔘 CONTROLES DE FORMULÁRIO
           ======================================== */

        .form-control {
            width: 100%;
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid #ced4da;
            border-radius: var(--border-radius);
            font-size: var(--font-size-md);
            transition: var(--transition-fast);
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-control:disabled {
            background: #e9ecef;
            opacity: 0.6;
        }

        /* ========================================
           🎯 BADGES E INDICADORES
           ======================================== */

        .badge {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }

        .badge.success { background: var(--success-gradient); }
        .badge.warning { background: var(--warning-gradient); }
        .badge.danger { background: var(--danger-gradient); }
        .badge.info { background: var(--info-gradient); }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-gradient);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        /* ========================================
           🎭 MODAIS E OVERLAYS
           ======================================== */

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease-out;
        }

        .modal-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .modal-footer {
            padding: var(--spacing-md) var(--spacing-lg);
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
        }

        /* ========================================
           📱 RESPONSIVIDADE AVANÇADA
           ======================================== */

        @media (max-width: 992px) {
            .tabs-header {
                flex-wrap: wrap;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
            }

            .table th,
            .table td {
                padding: var(--spacing-xs);
                font-size: var(--font-size-sm);
            }
        }

        @media (max-width: 576px) {
            .modal-content {
                margin: var(--spacing-md);
                width: calc(100% - 2 * var(--spacing-md));
                max-width: none;
            }

            .tabs-header {
                flex-direction: column;
            }

            .tab-button {
                text-align: center;
                border-bottom: none;
                border-right: 3px solid transparent;
            }

            .tab-button.active {
                border-bottom: none;
                border-right-color: #007bff;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- ========================================
             📱 HEADER PRINCIPAL
             ======================================== -->
        <div class="header">
            <h1>
                <i class="fas fa-exchange-alt"></i>
                Movimentação entre Armazéns
            </h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="abrirRelatorioProducao()" title="Relatório de Materiais em Produção">
                    <i class="fas fa-chart-bar"></i>
                    Relatório Produção
                </button>
                <button class="btn btn-success" onclick="analisarMateriaisRestantes()" title="Análise de Materiais Restantes">
                    <i class="fas fa-analytics"></i>
                    Análise Materiais
                </button>
                <button class="btn btn-warning" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <!-- ========================================
             📋 CONTEÚDO PRINCIPAL
             ======================================== -->
        <div class="main-content">
            <!-- Notificações -->
            <div id="notification" class="notification"></div>

            <!-- Loading inicial -->
            <div id="loadingMain" class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Carregando sistema...
            </div>

            <!-- Conteúdo será carregado dinamicamente -->
            <div id="mainContent" style="display: none;">
                <!-- Seção de Seleção de OP -->
                <div class="section-card">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-industry"></i>
                            Seleção de Ordem de Produção
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="grid grid-2">
                            <div>
                                <label for="opSelect" style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-search"></i>
                                    Buscar OP:
                                </label>
                                <select id="opSelect" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                                    <option value="">Selecione uma Ordem de Produção...</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-filter"></i>
                                    Filtros:
                                </label>
                                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" onclick="filtrarOPs('TODOS')" id="btnTodos">
                                        <i class="fas fa-list"></i>
                                        Todos
                                    </button>
                                    <button class="btn btn-primary" onclick="filtrarOPs('Pendente')" id="btnPendente">
                                        <i class="fas fa-clock"></i>
                                        Pendente
                                    </button>
                                    <button class="btn btn-warning" onclick="filtrarOPs('Em Produção')" id="btnEmProducao">
                                        <i class="fas fa-cogs"></i>
                                        Em Produção
                                    </button>
                                    <button class="btn btn-info" onclick="filtrarOPs('Material Transferido')" id="btnMaterialTransferido">
                                        <i class="fas fa-check"></i>
                                        Material OK
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview dos Materiais da OP -->
                <div id="opMaterialsPreview" class="section-card" style="display: none;">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            Materiais Necessários da OP
                            <span id="opMaterialsCount" class="badge">0 materiais MP</span>
                        </div>
                        <button class="btn btn-info" onclick="mostrarDetalhesOP()">
                            <i class="fas fa-info-circle"></i>
                            Detalhes
                        </button>
                    </div>
                    <div class="section-content">
                        <!-- Informações da OP -->
                        <div class="grid grid-4" style="margin-bottom: 20px;">
                            <div>
                                <strong>OP:</strong>
                                <span id="opInfoNumero">-</span>
                            </div>
                            <div>
                                <strong>Produto:</strong>
                                <span id="opInfoProduto">-</span>
                            </div>
                            <div>
                                <strong>Quantidade:</strong>
                                <span id="opInfoQuantidade">-</span>
                            </div>
                            <div>
                                <strong>Armazém:</strong>
                                <span id="opInfoArmazem">-</span>
                            </div>
                        </div>

                        <!-- Tabela de Materiais -->
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Código</th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Descrição</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Necessário</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Transferido</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Restante</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Status</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Histórico</th>
                                    </tr>
                                </thead>
                                <tbody id="opMaterialsTableBody">
                                    <!-- Conteúdo será carregado dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Seção de Transferências -->
                <div id="transferSection" class="section-card" style="display: none;">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-exchange-alt"></i>
                            Sistema de Transferências
                            <span id="transferCount" class="badge">0 selecionados</span>
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button class="btn btn-success" onclick="executarTransferencias()" id="btnTransferir" disabled>
                                <i class="fas fa-paper-plane"></i>
                                Transferir Selecionados
                            </button>
                            <button class="btn btn-info" onclick="window.sincronizarStatusTransferencias()" title="Sincronizar status com sistema de apontamentos">
                                <i class="fas fa-sync-alt"></i>
                                Sincronizar Apontamentos
                            </button>
                            <button class="btn btn-warning" onclick="limparSelecao()">
                                <i class="fas fa-times"></i>
                                Limpar Seleção
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- Filtros de Transferência -->
                        <div class="grid grid-3" style="margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-warehouse"></i>
                                    Armazém Origem:
                                </label>
                                <select id="armazemOrigemSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option value="">Selecione o armazém origem...</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-search"></i>
                                    Buscar Material:
                                </label>
                                <input type="text" id="materialSearch" placeholder="Código ou descrição..."
                                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">
                                    <i class="fas fa-filter"></i>
                                    Status:
                                </label>
                                <select id="statusFilter" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option value="">Todos os status</option>
                                    <option value="PENDENTE">Pendente</option>
                                    <option value="PARCIAL">Parcial</option>
                                    <option value="COMPLETO">Completo</option>
                                </select>
                            </div>
                        </div>

                        <!-- Tabela de Materiais para Transferência -->
                        <div style="overflow-x: auto;">
                            <table id="transferTable" style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">
                                            <input type="checkbox" id="selectAll" onchange="selecionarTodos(this.checked)">
                                        </th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Código</th>
                                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #dee2e6;">Descrição</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Origem</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Disponível</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Necessário</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Transferir</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Status</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Aguardando</th>
                                        <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #dee2e6;">Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="transferTableBody">
                                    <!-- Conteúdo será carregado dinamicamente -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Dashboard de Análise -->
                <div id="analysisSection" class="section-card" style="display: none;">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-chart-line"></i>
                            Dashboard de Análise de Materiais
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button class="btn btn-primary" onclick="atualizarAnalise()">
                                <i class="fas fa-sync"></i>
                                Atualizar
                            </button>
                            <button class="btn btn-info" onclick="exportarAnalise()">
                                <i class="fas fa-download"></i>
                                Exportar
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- Estatísticas Resumidas -->
                        <div class="grid grid-4" style="margin-bottom: 20px;">
                            <div class="stat-card">
                                <div class="stat-icon" style="background: var(--info-gradient);">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalOPs">0</div>
                                    <div class="stat-label">OPs Ativas</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon" style="background: var(--success-gradient);">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalMateriais">0</div>
                                    <div class="stat-label">Materiais Únicos</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon" style="background: var(--warning-gradient);">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="materiaisFaltantes">0</div>
                                    <div class="stat-label">Faltantes</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon" style="background: var(--danger-gradient);">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="valorTotal">R$ 0</div>
                                    <div class="stat-label">Valor Estimado</div>
                                </div>
                            </div>
                        </div>

                        <!-- Abas de Análise -->
                        <div class="tabs-container">
                            <div class="tabs-header">
                                <button class="tab-button active" onclick="mostrarAba('faltantes')">
                                    <i class="fas fa-exclamation-circle"></i>
                                    Materiais Faltantes
                                </button>
                                <button class="tab-button" onclick="mostrarAba('todos')">
                                    <i class="fas fa-list"></i>
                                    Todos os Materiais
                                </button>
                                <button class="tab-button" onclick="mostrarAba('ops')">
                                    <i class="fas fa-industry"></i>
                                    OPs Detalhadas
                                </button>
                            </div>
                            <div class="tabs-content">
                                <div id="tabFaltantes" class="tab-content active">
                                    <!-- Conteúdo da aba Faltantes -->
                                </div>
                                <div id="tabTodos" class="tab-content">
                                    <!-- Conteúdo da aba Todos -->
                                </div>
                                <div id="tabOPs" class="tab-content">
                                    <!-- Conteúdo da aba OPs -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========================================
         🔧 JAVASCRIPT - INICIALIZAÇÃO
         ======================================== -->
    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            doc,
            getDoc,
            updateDoc,
            addDoc,
            setDoc,
            runTransaction,
            query,
            where,
            orderBy,
            onSnapshot,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // ========================================
        // 🌐 VARIÁVEIS GLOBAIS
        // ========================================
        
        let ordens = [];
        let produtos = [];
        let armazens = [];
        let estoques = [];
        let transferencias = [];
        let selectedOrder = null;

        // ========================================
        // 🔢 FUNÇÕES DE PRECISÃO DECIMAL
        // ========================================

        // Função para comparar valores com tolerância decimal
        function compararComTolerancia(valor1, valor2, tolerancia = 0.001) {
            return Math.abs(valor1 - valor2) <= tolerancia;
        }

        // Função para verificar se transferência está completa
        function isTransferenciaCompleta(necessario, transferido, tolerancia = 0.001) {
            const completa = transferido >= necessario || compararComTolerancia(necessario, transferido, tolerancia);

            // Debug para casos de precisão decimal
            if (Math.abs(necessario - transferido) < 0.01 && necessario !== transferido) {
                console.log(`🔍 Verificação de precisão decimal:`, {
                    necessario,
                    transferido,
                    diferenca: Math.abs(necessario - transferido),
                    statusAnterior: transferido >= necessario ? 'COMPLETO' : 'PARCIAL',
                    statusNovo: completa ? 'COMPLETO' : 'PARCIAL'
                });
            }

            return completa;
        }

        // Função para calcular restante com precisão decimal
        function calcularRestante(necessario, transferido, tolerancia = 0.001) {
            if (isTransferenciaCompleta(necessario, transferido, tolerancia)) {
                return 0;
            }
            return Math.max(0, necessario - transferido);
        }

        // Função de teste para demonstrar a correção
        window.testarPrecisaoDecimal = function() {
            console.log('🧪 TESTE DE PRECISÃO DECIMAL');
            console.log('================================');

            const casos = [
                { necessario: 0.657, transferido: 0.657, descricao: 'Valores iguais' },
                { necessario: 0.657, transferido: 0.6570001, descricao: 'Diferença mínima (+)' },
                { necessario: 0.657, transferido: 0.6569999, descricao: 'Diferença mínima (-)' },
                { necessario: 134.000, transferido: 134.0001, descricao: 'Valores grandes com diferença mínima' },
                { necessario: 1.0, transferido: 0.5, descricao: 'Transferência parcial real' }
            ];

            casos.forEach((caso, index) => {
                const statusAntigo = caso.transferido >= caso.necessario ? 'COMPLETO' : 'PARCIAL';
                const statusNovo = isTransferenciaCompleta(caso.necessario, caso.transferido) ? 'COMPLETO' : 'PARCIAL';
                const restante = calcularRestante(caso.necessario, caso.transferido);

                console.log(`\n${index + 1}. ${caso.descricao}:`);
                console.log(`   Necessário: ${caso.necessario}`);
                console.log(`   Transferido: ${caso.transferido}`);
                console.log(`   Status Antigo: ${statusAntigo}`);
                console.log(`   Status Novo: ${statusNovo}`);
                console.log(`   Restante: ${restante}`);
                console.log(`   Diferença: ${Math.abs(caso.necessario - caso.transferido)}`);
            });

            console.log('\n✅ Teste concluído! Agora 0.657 = 0.657 será considerado COMPLETO');
        };
        let filtroAtual = 'TODOS';

        // Controle de histórico de transferências
        let historicoTransferenciasOP = {};
        let ultimaOPCarregada = null;
        let ultimoCarregamentoHistorico = 0;

        // ========================================
        // 🚀 INICIALIZAÇÃO DO SISTEMA
        // ========================================
        
        window.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 Iniciando Sistema de Movimentação de Armazéns...');
            
            try {
                await inicializarSistema();
                console.log('✅ Sistema inicializado com sucesso!');
            } catch (error) {
                console.error('❌ Erro na inicialização:', error);
                mostrarNotificacao('Erro ao inicializar sistema: ' + error.message, 'error');
            }
        });

        async function inicializarSistema() {
            // Carregar dados essenciais
            await Promise.all([
                carregarOrdens(),
                carregarProdutos(),
                carregarArmazens(),
                carregarEstoques(),
                carregarTransferencias()
            ]);

            // Configurar interface
            configurarInterface();
            
            // Ocultar loading e mostrar conteúdo
            document.getElementById('loadingMain').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
            
            // Iniciar webhook de sincronização
            iniciarWebhookSincronizacao();
        }

        // ========================================
        // 📊 CARREGAMENTO DE DADOS
        // ========================================

        async function carregarOrdens() {
            console.log('📋 Carregando ordens de produção...');
            const ordensSnapshot = await getDocs(collection(db, "ordensProducao"));
            ordens = ordensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${ordens.length} ordens carregadas`);

            // Debug: mostrar algumas OPs para verificar estrutura
            if (ordens.length > 0) {
                console.log('📊 Primeiras 3 OPs carregadas:', ordens.slice(0, 3).map(op => ({
                    id: op.id,
                    numero: op.numero,
                    status: op.status,
                    produtoId: op.produtoId
                })));

                // Verificar status únicos
                const statusUnicos = [...new Set(ordens.map(op => op.status))];
                console.log('📋 Status únicos encontrados:', statusUnicos);
            }
        }

        async function carregarProdutos() {
            console.log('📦 Carregando produtos...');
            const produtosSnapshot = await getDocs(collection(db, "produtos"));
            produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${produtos.length} produtos carregados`);
        }

        async function carregarArmazens() {
            console.log('🏭 Carregando armazéns...');
            const armazensSnapshot = await getDocs(collection(db, "armazens"));
            armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${armazens.length} armazéns carregados`);
        }

        async function carregarEstoques() {
            console.log('📊 Carregando estoques...');
            const estoquesSnapshot = await getDocs(collection(db, "estoques"));
            estoques = estoquesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            console.log(`✅ ${estoques.length} registros de estoque carregados`);
        }

        async function carregarTransferencias() {
            console.log('🔄 Carregando transferências...');
            try {
                // Carregar da coleção principal de transferências
                const transferenciasSnapshot = await getDocs(collection(db, "transferenciasArmazem"));
                transferencias = transferenciasSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                console.log(`✅ ${transferencias.length} transferências carregadas`);
            } catch (error) {
                console.error('❌ Erro ao carregar transferências:', error);
                transferencias = [];
            }
        }

        // ========================================
        // 🎯 CONFIGURAÇÃO DA INTERFACE
        // ========================================

        function configurarInterface() {
            popularDropdownOPs();
            configurarEventos();
            aplicarFiltroInicial();
        }

        function popularDropdownOPs() {
            const select = document.getElementById('opSelect');
            select.innerHTML = '<option value="">Selecione uma Ordem de Produção...</option>';

            console.log(`📊 Total de ordens disponíveis: ${ordens.length}`);

            const ordensAtivas = ordens.filter(op =>
                ['Pendente', 'Em Produção', 'Firme', 'Aberta', 'Aguardando Material', 'Material Transferido'].includes(op.status)
            );

            console.log(`📊 Ordens após filtro de status ativo: ${ordensAtivas.length}`);

            if (ordensAtivas.length === 0 && ordens.length > 0) {
                console.warn('⚠️ Nenhuma OP ativa encontrada. Status disponíveis:',
                    [...new Set(ordens.map(op => op.status))]);

                // Mostrar todas as OPs para debug
                console.log('📋 Todas as OPs (primeiras 5):', ordens.slice(0, 5).map(op => ({
                    numero: op.numero,
                    status: op.status,
                    produtoId: op.produtoId
                })));
            }

            ordensAtivas
                .sort((a, b) => a.numero.localeCompare(b.numero))
                .forEach(ordem => {
                    const produto = produtos.find(p => p.id === ordem.produtoId);
                    const option = document.createElement('option');
                    option.value = ordem.id;
                    option.textContent = `${ordem.numero} - ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'Sem descrição'}`;
                    option.dataset.status = ordem.status;
                    select.appendChild(option);
                });

            console.log(`📋 ${ordensAtivas.length} OPs ativas carregadas no dropdown`);
        }

        function configurarEventos() {
            // Evento de seleção de OP
            document.getElementById('opSelect').addEventListener('change', function() {
                const opId = this.value;
                if (opId) {
                    selecionarOP(opId);
                } else {
                    ocultarPreviewMateriais();
                }
            });

            // Eventos de filtro
            document.querySelectorAll('[onclick^="filtrarOPs"]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const filtro = this.onclick.toString().match(/'([^']+)'/)[1];
                    aplicarFiltroOP(filtro);
                });
            });
        }

        // ========================================
        // 📜 SISTEMA DE HISTÓRICO DE TRANSFERÊNCIAS
        // ========================================

        async function carregarHistoricoTransferenciasOP(ordemProducaoId) {
            try {
                console.log(`📜 Carregando histórico de transferências para OP: ${ordemProducaoId}`);

                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("tipo", "==", "OP")
                );

                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const transferenciasOP = transferenciasSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Agrupar por produto ID
                historicoTransferenciasOP = {};
                transferenciasOP.forEach(transfer => {
                    if (!historicoTransferenciasOP[transfer.produtoId]) {
                        historicoTransferenciasOP[transfer.produtoId] = [];
                    }
                    historicoTransferenciasOP[transfer.produtoId].push(transfer);
                });

                // Ordenar cada grupo por data mais recente primeiro
                Object.keys(historicoTransferenciasOP).forEach(produtoId => {
                    historicoTransferenciasOP[produtoId].sort((a, b) => {
                        const dateA = a.dataHora?.toDate?.() || new Date(a.dataHora?.seconds * 1000 || 0);
                        const dateB = b.dataHora?.toDate?.() || new Date(b.dataHora?.seconds * 1000 || 0);
                        return dateB - dateA;
                    });
                });

                console.log(`✅ Histórico carregado: ${transferenciasOP.length} transferências para ${Object.keys(historicoTransferenciasOP).length} produtos`);

                ultimaOPCarregada = ordemProducaoId;
                ultimoCarregamentoHistorico = Date.now();

            } catch (error) {
                console.error('❌ Erro ao carregar histórico de transferências:', error);
                historicoTransferenciasOP = {};
            }
        }

        // ========================================
        // 🔍 SISTEMA DE FILTROS
        // ========================================

        function aplicarFiltroInicial() {
            aplicarFiltroOP('Pendente'); // Filtro padrão
        }

        window.filtrarOPs = function(filtro) {
            aplicarFiltroOP(filtro);
        };

        function aplicarFiltroOP(filtro) {
            filtroAtual = filtro;

            // Atualizar botões ativos
            document.querySelectorAll('[id^="btn"]').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
            });

            // Mapear filtros para IDs dos botões
            const filtroParaBotao = {
                'TODOS': 'btnTodos',
                'Pendente': 'btnPendente',
                'Em Produção': 'btnEmProducao',
                'Material Transferido': 'btnMaterialTransferido'
            };

            const btnAtivo = document.getElementById(filtroParaBotao[filtro]);
            if (btnAtivo) {
                btnAtivo.classList.remove('btn-secondary');
                btnAtivo.classList.add('btn-primary');
            }

            // Filtrar opções do dropdown
            const select = document.getElementById('opSelect');
            const options = Array.from(select.options);

            options.forEach(option => {
                if (option.value === '') return; // Manter opção vazia

                const status = option.dataset.status;
                const mostrar = filtro === 'TODOS' || status === filtro;
                option.style.display = mostrar ? '' : 'none';
            });

            // Limpar seleção se a OP atual não atende ao filtro
            if (selectedOrder && filtro !== 'TODOS' && selectedOrder.status !== filtro) {
                select.value = '';
                ocultarPreviewMateriais();
            }

            console.log(`🔍 Filtro aplicado: ${filtro}`);
        }

        // ========================================
        // 📋 GESTÃO DE ORDENS DE PRODUÇÃO
        // ========================================

        function selecionarOP(opId) {
            selectedOrder = ordens.find(op => op.id === opId);

            if (!selectedOrder) {
                mostrarNotificacao('OP não encontrada!', 'error');
                return;
            }

            console.log(`📋 OP selecionada: ${selectedOrder.numero}`);

            // Carregar histórico de transferências se necessário
            const agora = Date.now();
            const intervaloMinimo = 30000; // 30 segundos

            if (ultimaOPCarregada !== opId || (agora - ultimoCarregamentoHistorico) > intervaloMinimo) {
                carregarHistoricoTransferenciasOP(opId);
            }

            // Carregar materiais da OP
            carregarMateriaisOP(selectedOrder);

            // Mostrar preview
            mostrarPreviewMateriais(selectedOrder);

            // Mostrar seção de transferências se houver materiais
            const materiaisMP = carregarMateriaisOP(selectedOrder);
            if (materiaisMP && materiaisMP.length > 0) {
                setTimeout(() => {
                    mostrarSecaoTransferencias();
                }, 500);
            }
        }

        function carregarMateriaisOP(ordem) {
            if (!ordem.materiaisNecessarios || ordem.materiaisNecessarios.length === 0) {
                console.log(`⚠️ OP ${ordem.numero} não possui materiais necessários`);
                return;
            }

            // Filtrar apenas materiais MP com lógica corrigida
            const materiaisMP = ordem.materiaisNecessarios.filter(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto || produto.tipo !== 'MP') return false;

                // ✅ LÓGICA CORRIGIDA: necessidade > 0 OU quantidade > 0
                const temQuantidade = (material.necessidade > 0) || (material.quantidade > 0);
                return temQuantidade;
            });

            console.log(`📦 ${materiaisMP.length} materiais MP encontrados para OP ${ordem.numero}`);
            return materiaisMP;
        }

        function mostrarPreviewMateriais(ordem) {
            const preview = document.getElementById('opMaterialsPreview');
            const materiaisMP = carregarMateriaisOP(ordem);

            // Atualizar informações da OP
            atualizarInformacoesOP(ordem);

            // Atualizar contador
            document.getElementById('opMaterialsCount').textContent = `${materiaisMP?.length || 0} materiais MP`;

            // Atualizar tabela
            atualizarTabelaMateriais(materiaisMP || []);

            // Mostrar preview
            preview.style.display = 'block';
            preview.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        function atualizarInformacoesOP(ordem) {
            const produto = produtos.find(p => p.id === ordem.produtoId);
            const armazem = armazens.find(a => a.id === ordem.armazemProducaoId);

            document.getElementById('opInfoNumero').textContent = ordem.numero || 'N/A';
            document.getElementById('opInfoProduto').textContent = produto ?
                `${produto.codigo} - ${produto.descricao}` : 'N/A';
            document.getElementById('opInfoQuantidade').textContent = ordem.quantidade ?
                `${ordem.quantidade} ${produto?.unidade || 'UN'}` : 'N/A';
            document.getElementById('opInfoArmazem').textContent = armazem ?
                `${armazem.codigo} - ${armazem.nome}` : 'N/A';
        }

        function atualizarTabelaMateriais(materiaisMP) {
            const tbody = document.getElementById('opMaterialsTableBody');
            tbody.innerHTML = '';

            if (materiaisMP.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
                    <td colspan="7" style="text-align: center; padding: 30px; color: #6c757d;">
                        <div style="font-size: 18px; opacity: 0.5; margin-bottom: 8px;">📦</div>
                        <strong>Nenhum material MP encontrado</strong><br>
                        <small>Esta OP não possui materiais do tipo Matéria-Prima</small>
                    </td>
                `;
                tbody.appendChild(emptyRow);
                return;
            }

            materiaisMP.forEach(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto) return;

                // ✅ LÓGICA CORRIGIDA: Usar quantidade se necessidade for 0
                const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                const transferido = material.saldoReservado || 0;
                const restante = calcularRestante(necessario, transferido);

                // Determinar status
                let status = '';
                let statusClass = '';
                let progressPercent = 0;

                if (necessario > 0) {
                    progressPercent = Math.min(100, (transferido / necessario) * 100);
                }

                if (isTransferenciaCompleta(necessario, transferido)) {
                    status = '✅ Completo';
                    statusClass = 'background: #d4edda; color: #155724;';
                } else if (transferido > 0) {
                    status = '🔄 Parcial';
                    statusClass = 'background: #fff3cd; color: #856404;';
                } else {
                    status = '❌ Pendente';
                    statusClass = 'background: #f8d7da; color: #721c24;';
                }

                // Buscar histórico de transferências para este produto
                const historicoTransferencias = historicoTransferenciasOP[material.produtoId] || [];
                let historicoHtml = '';

                if (historicoTransferencias.length > 0) {
                    historicoHtml = `
                        <div style="max-height: 120px; overflow-y: auto; font-size: 11px;">
                            ${historicoTransferencias.slice(0, 3).map(transfer => {
                                const dataTransferencia = transfer.dataHora?.toDate?.() || new Date(transfer.dataHora?.seconds * 1000 || 0);
                                const dataFormatada = dataTransferencia.toLocaleString('pt-BR', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                });

                                return `
                                    <div style="background: #f8f9fa; padding: 4px 6px; margin-bottom: 2px; border-radius: 4px; border-left: 3px solid #28a745;">
                                        <div style="font-weight: 600; color: #28a745;">
                                            ${transfer.quantidade?.toFixed(3) || '0.000'} ${produto.unidade}
                                        </div>
                                        <div style="color: #6c757d; font-size: 10px;">
                                            📅 ${dataFormatada}
                                        </div>
                                        <div style="color: #6c757d; font-size: 10px;">
                                            👤 ${transfer.usuario || 'Sistema'}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                            ${historicoTransferencias.length > 3 ? `
                                <div style="text-align: center; font-size: 10px; color: #6c757d; margin-top: 4px;">
                                    +${historicoTransferencias.length - 3} mais...
                                </div>
                            ` : ''}
                        </div>
                    `;
                } else {
                    historicoHtml = `
                        <div style="text-align: center; color: #6c757d; font-size: 11px; padding: 8px;">
                            <div style="opacity: 0.5;">📋</div>
                            <div>Nenhuma transferência</div>
                        </div>
                    `;
                }

                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #e9ecef';
                row.innerHTML = `
                    <td style="padding: 10px 8px; font-weight: 600; color: #2c3e50;">${produto.codigo}</td>
                    <td style="padding: 10px 8px; color: #495057;" title="${produto.descricao}">
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${produto.descricao}
                        </div>
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: #2c3e50;">
                        ${necessario.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: #28a745;">
                        ${transferido.toFixed(3)} ${produto.unidade}
                        <div style="width: 100%; background: #e9ecef; border-radius: 10px; height: 6px; margin-top: 4px;">
                            <div style="width: ${progressPercent}%; background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
                        </div>
                    </td>
                    <td style="padding: 10px 8px; text-align: center; font-weight: 600; color: ${restante > 0 ? '#dc3545' : '#28a745'};">
                        ${restante.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="padding: 10px 8px; text-align: center;">
                        <span style="padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; ${statusClass}">
                            ${status}
                        </span>
                    </td>
                    <td style="padding: 10px 8px; max-width: 250px;">
                        ${historicoHtml}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function ocultarPreviewMateriais() {
            document.getElementById('opMaterialsPreview').style.display = 'none';
            selectedOrder = null;
        }

        // ========================================
        // 🔗 INTEGRAÇÃO COM APONTAMENTOS
        // ========================================

        /**
         * Função para verificar se uma OP tem materiais transferidos
         * Usada pelo apontamentos_simplificado.html para controlar flags
         */
        window.verificarMateriaisTransferidos = async function(ordemProducaoId) {
            try {
                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", ordemProducaoId),
                    where("tipo", "==", "OP"),
                    where("status", "==", "CONCLUIDA")
                );

                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const transferencias = transferenciasSnapshot.docs.map(doc => doc.data());

                // Verificar se há transferências concluídas
                const temTransferencias = transferencias.length > 0;

                // Calcular total transferido por produto
                const totaisPorProduto = {};
                transferencias.forEach(t => {
                    if (!totaisPorProduto[t.produtoId]) {
                        totaisPorProduto[t.produtoId] = 0;
                    }
                    totaisPorProduto[t.produtoId] += t.quantidade || 0;
                });

                return {
                    temTransferencias,
                    totalTransferencias: transferencias.length,
                    produtosTransferidos: Object.keys(totaisPorProduto).length,
                    totaisPorProduto,
                    ultimaTransferencia: transferencias.length > 0 ?
                        Math.max(...transferencias.map(t => t.dataHora?.seconds || 0)) * 1000 : null
                };

            } catch (error) {
                console.error('❌ Erro ao verificar materiais transferidos:', error);
                return {
                    temTransferencias: false,
                    totalTransferencias: 0,
                    produtosTransferidos: 0,
                    totaisPorProduto: {},
                    ultimaTransferencia: null
                };
            }
        };

        /**
         * Função para obter status detalhado de transferências de uma OP
         * Usada para exibir informações detalhadas no apontamentos
         */
        window.obterStatusTransferenciasOP = async function(ordemProducaoId) {
            try {
                // Buscar OP
                const ordem = ordens.find(op => op.id === ordemProducaoId);
                if (!ordem) {
                    throw new Error('OP não encontrada');
                }

                // Buscar transferências
                const resultadoTransferencias = await window.verificarMateriaisTransferidos(ordemProducaoId);

                // Analisar materiais necessários vs transferidos
                const materiaisMP = ordem.materiaisNecessarios?.filter(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    return produto && produto.tipo === 'MP';
                }) || [];

                const statusMateriais = materiaisMP.map(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                    const transferido = resultadoTransferencias.totaisPorProduto[material.produtoId] || 0;
                    const restante = calcularRestante(necessario, transferido);

                    let status = 'PENDENTE';
                    if (isTransferenciaCompleta(necessario, transferido)) {
                        status = 'COMPLETO';
                    } else if (transferido > 0) {
                        status = 'PARCIAL';
                    }

                    return {
                        produtoId: material.produtoId,
                        codigo: produto?.codigo || 'N/A',
                        descricao: produto?.descricao || 'N/A',
                        necessario,
                        transferido,
                        restante,
                        status,
                        percentual: necessario > 0 ? (transferido / necessario) * 100 : 0
                    };
                });

                const materiaisCompletos = statusMateriais.filter(m => m.status === 'COMPLETO').length;
                const materiaisParciais = statusMateriais.filter(m => m.status === 'PARCIAL').length;
                const materiaisPendentes = statusMateriais.filter(m => m.status === 'PENDENTE').length;

                let statusGeral = 'PENDENTE';
                if (materiaisCompletos === statusMateriais.length && statusMateriais.length > 0) {
                    statusGeral = 'COMPLETO';
                } else if (materiaisCompletos > 0 || materiaisParciais > 0) {
                    statusGeral = 'PARCIAL';
                }

                return {
                    statusGeral,
                    materiaisCompletos,
                    materiaisParciais,
                    materiaisPendentes,
                    totalMateriais: statusMateriais.length,
                    statusMateriais,
                    ...resultadoTransferencias
                };

            } catch (error) {
                console.error('❌ Erro ao obter status de transferências:', error);
                return {
                    statusGeral: 'ERRO',
                    materiaisCompletos: 0,
                    materiaisParciais: 0,
                    materiaisPendentes: 0,
                    totalMateriais: 0,
                    statusMateriais: [],
                    temTransferencias: false,
                    totalTransferencias: 0,
                    produtosTransferidos: 0,
                    totaisPorProduto: {},
                    ultimaTransferencia: null
                };
            }
        };

        /**
         * Função para notificar o sistema de apontamentos sobre mudanças
         * Chama a função do apontamentos_simplificado.html se estiver disponível
         */
        async function notificarApontamentosSobreTransferencia(opId, produtoId, quantidade) {
            try {
                // Verificar se a função do apontamentos está disponível
                if (typeof window.atualizarFlagOPAposTransferencia === 'function') {
                    console.log(`🔄 Notificando apontamentos sobre transferência: OP ${opId}, Produto ${produtoId}, Qtd ${quantidade}`);

                    const sucesso = await window.atualizarFlagOPAposTransferencia(opId, produtoId, quantidade);

                    if (sucesso) {
                        console.log('✅ Flag de material transferido atualizado no apontamentos');
                        return true;
                    } else {
                        console.warn('⚠️ Não foi possível atualizar flag no apontamentos');
                        return false;
                    }
                } else {
                    console.log('ℹ️ Sistema de apontamentos não está carregado - flag será atualizado quando acessar apontamentos');
                    return true; // Não é erro, apenas não está carregado
                }
            } catch (error) {
                console.error('❌ Erro ao notificar apontamentos:', error);
                return false;
            }
        }

        /**
         * Função para verificar e atualizar status de múltiplas OPs
         * Útil para sincronização em lote
         */
        window.sincronizarStatusTransferencias = async function(opsIds = null) {
            try {
                console.log('🔄 Iniciando sincronização de status de transferências...');

                // Se não especificado, usar todas as OPs carregadas
                const opsParaSincronizar = opsIds || ordens.map(op => op.id);

                let sucessos = 0;
                let erros = 0;

                for (const opId of opsParaSincronizar) {
                    try {
                        const sucesso = await notificarApontamentosSobreTransferencia(opId);
                        if (sucesso) {
                            sucessos++;
                        } else {
                            erros++;
                        }
                    } catch (error) {
                        console.error(`❌ Erro ao sincronizar OP ${opId}:`, error);
                        erros++;
                    }
                }

                console.log(`✅ Sincronização concluída: ${sucessos} sucessos, ${erros} erros`);

                if (sucessos > 0) {
                    mostrarNotificacao(`✅ ${sucessos} OPs sincronizadas com apontamentos`, 'success');
                }

                return { sucessos, erros };

            } catch (error) {
                console.error('❌ Erro na sincronização:', error);
                mostrarNotificacao('❌ Erro na sincronização com apontamentos', 'error');
                return { sucessos: 0, erros: 1 };
            }
        };

        // ========================================
        // 🔔 SISTEMA DE NOTIFICAÇÕES
        // ========================================

        function mostrarNotificacao(mensagem, tipo = 'info') {
            const notification = document.getElementById('notification');
            notification.className = `notification ${tipo}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span>${mensagem}</span>
                    <button onclick="this.parentElement.parentElement.style.display='none'"
                            style="background: none; border: none; font-size: 18px; cursor: pointer; opacity: 0.7;">
                        ×
                    </button>
                </div>
            `;
            notification.style.display = 'block';

            // Auto-hide após 5 segundos
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // ========================================
        // 🔄 WEBHOOK DE SINCRONIZAÇÃO
        // ========================================

        function iniciarWebhookSincronizacao() {
            console.log('🚀 Iniciando webhook de sincronização automática...');

            // Monitorar mudanças nas ordens de produção
            onSnapshot(collection(db, "ordensProducao"), (snapshot) => {
                console.log('📡 Webhook: Mudanças detectadas nas OPs');
                carregarOrdens().then(() => {
                    popularDropdownOPs();
                    if (selectedOrder) {
                        // Recarregar OP selecionada se ainda existir
                        const opAtualizada = ordens.find(op => op.id === selectedOrder.id);
                        if (opAtualizada) {
                            selectedOrder = opAtualizada;
                            mostrarPreviewMateriais(selectedOrder);
                        } else {
                            ocultarPreviewMateriais();
                        }
                    }
                });
            });

            // Monitorar mudanças nas transferências
            onSnapshot(collection(db, "transferencias_producao"), (snapshot) => {
                console.log('📡 Webhook: Mudanças detectadas nas transferências');
                carregarTransferencias().then(() => {
                    if (selectedOrder) {
                        mostrarPreviewMateriais(selectedOrder);
                    }
                });
            });

            console.log('✅ Webhook de sincronização ativo!');
        }

        // ========================================
        // 🎯 FUNÇÕES AUXILIARES
        // ========================================

        window.abrirRelatorioProducao = function() {
            window.open('relatorio_materiais_producao.html', '_blank');
        };

        window.analisarMateriaisRestantes = function() {
            mostrarDashboardAnalise();
        };

        // ========================================
        // 📊 DASHBOARD DE ANÁLISE
        // ========================================

        function mostrarDashboardAnalise() {
            const section = document.getElementById('analysisSection');
            carregarDadosAnalise();
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        function carregarDadosAnalise() {
            console.log('📊 Carregando dados para análise...');

            // Filtrar OPs ativas
            const opsAtivas = ordens.filter(op =>
                ['Pendente', 'Em Produção', 'Firme', 'Aberta', 'Aguardando Material', 'Material Transferido'].includes(op.status)
            );

            // Consolidar materiais necessários
            const materiaisConsolidados = consolidarMateriaisNecessarios(opsAtivas);

            // Atualizar estatísticas
            atualizarEstatisticasAnalise(opsAtivas, materiaisConsolidados);

            // Carregar abas
            carregarAbaFaltantes(materiaisConsolidados);
            carregarAbaTodos(materiaisConsolidados);
            carregarAbaOPs(opsAtivas);
        }

        function consolidarMateriaisNecessarios(ops) {
            const materiaisMap = new Map();

            ops.forEach(op => {
                if (!op.materiaisNecessarios) return;

                op.materiaisNecessarios.forEach(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    if (!produto || produto.tipo !== 'MP') return;

                    const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                    if (necessario <= 0) return;

                    const key = material.produtoId;
                    if (!materiaisMap.has(key)) {
                        materiaisMap.set(key, {
                            produtoId: material.produtoId,
                            produto: produto,
                            quantidadeTotal: 0,
                            transferidoTotal: 0,
                            opsQueUsam: [],
                            prioridadeMaxima: 'BAIXA'
                        });
                    }

                    const materialConsolidado = materiaisMap.get(key);
                    materialConsolidado.quantidadeTotal += necessario;
                    materialConsolidado.transferidoTotal += (material.saldoReservado || 0);
                    materialConsolidado.opsQueUsam.push({
                        opId: op.id,
                        opNumero: op.numero,
                        quantidade: necessario,
                        transferido: material.saldoReservado || 0,
                        prioridade: op.prioridade || 'NORMAL'
                    });

                    // Atualizar prioridade máxima
                    const prioridades = ['BAIXA', 'NORMAL', 'ALTA', 'URGENTE'];
                    const prioridadeAtual = prioridades.indexOf(materialConsolidado.prioridadeMaxima);
                    const prioridadeOP = prioridades.indexOf(op.prioridade || 'NORMAL');
                    if (prioridadeOP > prioridadeAtual) {
                        materialConsolidado.prioridadeMaxima = op.prioridade || 'NORMAL';
                    }
                });
            });

            return Array.from(materiaisMap.values());
        }

        function atualizarEstatisticasAnalise(ops, materiais) {
            const materiaisFaltantes = materiais.filter(m =>
                m.quantidadeTotal > m.transferidoTotal
            );

            const valorTotal = materiais.reduce((total, material) => {
                const restante = material.quantidadeTotal - material.transferidoTotal;
                const valorUnitario = material.produto.valorUnitario || 0;
                return total + (restante * valorUnitario);
            }, 0);

            document.getElementById('totalOPs').textContent = ops.length;
            document.getElementById('totalMateriais').textContent = materiais.length;
            document.getElementById('materiaisFaltantes').textContent = materiaisFaltantes.length;
            document.getElementById('valorTotal').textContent = `R$ ${valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        }

        function carregarAbaFaltantes(materiais) {
            const tab = document.getElementById('tabFaltantes');
            const materiaisFaltantes = materiais.filter(m =>
                m.quantidadeTotal > m.transferidoTotal
            ).sort((a, b) => {
                // Ordenar por prioridade e depois por quantidade restante
                const prioridades = ['BAIXA', 'NORMAL', 'ALTA', 'URGENTE'];
                const prioridadeA = prioridades.indexOf(a.prioridadeMaxima);
                const prioridadeB = prioridades.indexOf(b.prioridadeMaxima);

                if (prioridadeA !== prioridadeB) {
                    return prioridadeB - prioridadeA; // Maior prioridade primeiro
                }

                const restanteA = a.quantidadeTotal - a.transferidoTotal;
                const restanteB = b.quantidadeTotal - b.transferidoTotal;
                return restanteB - restanteA; // Maior quantidade primeiro
            });

            let html = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #dc3545; margin-bottom: 10px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        Materiais Faltantes (${materiaisFaltantes.length})
                    </h4>
                    <p style="color: #6c757d;">Materiais que ainda precisam ser transferidos para produção.</p>
                </div>
            `;

            if (materiaisFaltantes.length === 0) {
                html += `
                    <div style="text-align: center; padding: 40px; color: #28a745;">
                        <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <h5>Todos os materiais foram transferidos!</h5>
                        <p>Não há materiais pendentes de transferência.</p>
                    </div>
                `;
            } else {
                html += '<div class="table-responsive">';
                html += '<table class="table">';
                html += `
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Necessário</th>
                            <th>Transferido</th>
                            <th>Restante</th>
                            <th>Prioridade</th>
                            <th>OPs</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                `;

                materiaisFaltantes.forEach(material => {
                    const restante = material.quantidadeTotal - material.transferidoTotal;
                    const progresso = (material.transferidoTotal / material.quantidadeTotal) * 100;

                    let prioridadeClass = 'secondary';
                    switch (material.prioridadeMaxima) {
                        case 'URGENTE': prioridadeClass = 'danger'; break;
                        case 'ALTA': prioridadeClass = 'warning'; break;
                        case 'NORMAL': prioridadeClass = 'info'; break;
                    }

                    html += `
                        <tr>
                            <td style="font-weight: 600;">${material.produto.codigo}</td>
                            <td title="${material.produto.descricao}">
                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                    ${material.produto.descricao}
                                </div>
                            </td>
                            <td style="text-align: center;">
                                ${material.quantidadeTotal.toFixed(3)} ${material.produto.unidade}
                            </td>
                            <td style="text-align: center;">
                                ${material.transferidoTotal.toFixed(3)} ${material.produto.unidade}
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progresso}%;"></div>
                                </div>
                            </td>
                            <td style="text-align: center; font-weight: 600; color: #dc3545;">
                                ${restante.toFixed(3)} ${material.produto.unidade}
                            </td>
                            <td style="text-align: center;">
                                <span class="badge ${prioridadeClass}">${material.prioridadeMaxima}</span>
                            </td>
                            <td style="text-align: center;">
                                <span class="badge info">${material.opsQueUsam.length} OP(s)</span>
                            </td>
                            <td style="text-align: center;">
                                <button class="btn btn-primary btn-sm" onclick="transferirMaterial('${material.produtoId}')"
                                        title="Transferir material">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
            }

            tab.innerHTML = html;
        }

        function carregarAbaTodos(materiais) {
            const tab = document.getElementById('tabTodos');

            let html = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #007bff; margin-bottom: 10px;">
                        <i class="fas fa-list"></i>
                        Todos os Materiais (${materiais.length})
                    </h4>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <input type="text" id="searchTodos" placeholder="Buscar material..."
                               style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 6px;"
                               onkeyup="filtrarTabelaTodos()">
                        <select id="prioridadeTodos" onchange="filtrarTabelaTodos()"
                                style="padding: 8px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="">Todas as prioridades</option>
                            <option value="URGENTE">Urgente</option>
                            <option value="ALTA">Alta</option>
                            <option value="NORMAL">Normal</option>
                            <option value="BAIXA">Baixa</option>
                        </select>
                    </div>
                </div>
            `;

            html += '<div class="table-responsive">';
            html += '<table class="table" id="tableTodos">';
            html += `
                <thead>
                    <tr>
                        <th onclick="ordenarTabelaTodos('codigo')" style="cursor: pointer;">
                            Código <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="ordenarTabelaTodos('descricao')" style="cursor: pointer;">
                            Descrição <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="ordenarTabelaTodos('necessario')" style="cursor: pointer;">
                            Necessário <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="ordenarTabelaTodos('transferido')" style="cursor: pointer;">
                            Transferido <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="ordenarTabelaTodos('restante')" style="cursor: pointer;">
                            Restante <i class="fas fa-sort"></i>
                        </th>
                        <th>Progresso</th>
                        <th>Prioridade</th>
                        <th>OPs</th>
                    </tr>
                </thead>
                <tbody>
            `;

            materiais.forEach(material => {
                const restante = material.quantidadeTotal - material.transferidoTotal;
                const progresso = material.quantidadeTotal > 0 ? (material.transferidoTotal / material.quantidadeTotal) * 100 : 0;

                let prioridadeClass = 'secondary';
                switch (material.prioridadeMaxima) {
                    case 'URGENTE': prioridadeClass = 'danger'; break;
                    case 'ALTA': prioridadeClass = 'warning'; break;
                    case 'NORMAL': prioridadeClass = 'info'; break;
                }

                html += `
                    <tr data-codigo="${material.produto.codigo}"
                        data-descricao="${material.produto.descricao}"
                        data-prioridade="${material.prioridadeMaxima}"
                        data-necessario="${material.quantidadeTotal}"
                        data-transferido="${material.transferidoTotal}"
                        data-restante="${restante}">
                        <td style="font-weight: 600;">${material.produto.codigo}</td>
                        <td title="${material.produto.descricao}">
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                ${material.produto.descricao}
                            </div>
                        </td>
                        <td style="text-align: center;">
                            ${material.quantidadeTotal.toFixed(3)} ${material.produto.unidade}
                        </td>
                        <td style="text-align: center;">
                            ${material.transferidoTotal.toFixed(3)} ${material.produto.unidade}
                        </td>
                        <td style="text-align: center; font-weight: 600; color: ${restante > 0 ? '#dc3545' : '#28a745'};">
                            ${restante.toFixed(3)} ${material.produto.unidade}
                        </td>
                        <td style="text-align: center;">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progresso}%;"></div>
                            </div>
                            <small>${progresso.toFixed(1)}%</small>
                        </td>
                        <td style="text-align: center;">
                            <span class="badge ${prioridadeClass}">${material.prioridadeMaxima}</span>
                        </td>
                        <td style="text-align: center;">
                            <button class="btn btn-info btn-sm" onclick="mostrarDetalhesOPs('${material.produtoId}')"
                                    title="Ver OPs que usam este material">
                                ${material.opsQueUsam.length} OP(s)
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            tab.innerHTML = html;
        }

        function carregarAbaOPs(ops) {
            const tab = document.getElementById('tabOPs');

            let html = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #28a745; margin-bottom: 10px;">
                        <i class="fas fa-industry"></i>
                        Ordens de Produção Detalhadas (${ops.length})
                    </h4>
                    <input type="text" id="searchOPs" placeholder="Buscar OP..."
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 6px; margin-bottom: 15px;"
                           onkeyup="filtrarTabelaOPs()">
                </div>
            `;

            html += '<div class="table-responsive">';
            html += '<table class="table" id="tabelaOPs">';
            html += `
                <thead>
                    <tr>
                        <th>OP</th>
                        <th>Produto</th>
                        <th>Status</th>
                        <th>Materiais MP</th>
                        <th>Progresso</th>
                        <th>Prioridade</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
            `;

            ops.forEach(op => {
                const produto = produtos.find(p => p.id === op.produtoId);
                const materiaisMP = op.materiaisNecessarios ?
                    op.materiaisNecessarios.filter(m => {
                        const prod = produtos.find(p => p.id === m.produtoId);
                        return prod && prod.tipo === 'MP';
                    }) : [];

                let totalNecessario = 0;
                let totalTransferido = 0;

                materiaisMP.forEach(material => {
                    const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                    totalNecessario += necessario;
                    totalTransferido += (material.saldoReservado || 0);
                });

                const progresso = totalNecessario > 0 ? (totalTransferido / totalNecessario) * 100 : 100;

                let statusClass = 'secondary';
                switch (op.status) {
                    case 'Pendente': statusClass = 'warning'; break;
                    case 'Em Produção': statusClass = 'info'; break;
                    case 'Firme': statusClass = 'success'; break;
                    case 'Aberta': statusClass = 'info'; break;
                    case 'Aguardando Material': statusClass = 'warning'; break;
                    case 'Material Transferido': statusClass = 'success'; break;
                }

                let prioridadeClass = 'secondary';
                switch (op.prioridade) {
                    case 'URGENTE': prioridadeClass = 'danger'; break;
                    case 'ALTA': prioridadeClass = 'warning'; break;
                    case 'NORMAL': prioridadeClass = 'info'; break;
                }

                html += `
                    <tr data-op="${op.numero}" data-produto="${produto?.descricao || ''}">
                        <td style="font-weight: 600;">${op.numero}</td>
                        <td title="${produto?.descricao || 'N/A'}">
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}
                            </div>
                        </td>
                        <td style="text-align: center;">
                            <span class="badge ${statusClass}">${op.status}</span>
                        </td>
                        <td style="text-align: center;">
                            <span class="badge info">${materiaisMP.length} material(is)</span>
                        </td>
                        <td style="text-align: center;">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progresso}%;"></div>
                            </div>
                            <small>${progresso.toFixed(1)}%</small>
                        </td>
                        <td style="text-align: center;">
                            <span class="badge ${prioridadeClass}">${op.prioridade || 'NORMAL'}</span>
                        </td>
                        <td style="text-align: center;">
                            <button class="btn btn-primary btn-sm" onclick="selecionarOPAnalise('${op.id}')"
                                    title="Selecionar esta OP">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            tab.innerHTML = html;
        }

        // ========================================
        // 🎯 FUNÇÕES DE INTERAÇÃO DAS ABAS
        // ========================================

        window.mostrarAba = function(aba) {
            // Atualizar botões
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[onclick="mostrarAba('${aba}')"]`).classList.add('active');

            // Atualizar conteúdo
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            const tabContent = document.getElementById(`tab${aba.charAt(0).toUpperCase() + aba.slice(1)}`);
            if (tabContent) {
                tabContent.classList.add('active');
            }
        };

        window.transferirMaterial = function(produtoId) {
            // Buscar OP que usa este material
            const opComMaterial = ordens.find(op =>
                op.materiaisNecessarios &&
                op.materiaisNecessarios.some(m => m.produtoId === produtoId)
            );

            if (opComMaterial) {
                // Selecionar a OP
                document.getElementById('opSelect').value = opComMaterial.id;
                selecionarOP(opComMaterial.id);

                // Mostrar seção de transferências
                setTimeout(() => {
                    mostrarSecaoTransferencias();

                    // Marcar o material específico
                    setTimeout(() => {
                        const checkbox = document.querySelector(`input[data-produto-id="${produtoId}"][type="checkbox"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                            atualizarSelecao();
                            checkbox.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }, 500);
                }, 500);
            } else {
                mostrarNotificacao('OP não encontrada para este material!', 'warning');
            }
        };

        window.mostrarDetalhesOPs = function(produtoId) {
            const produto = produtos.find(p => p.id === produtoId);
            const opsQueUsam = ordens.filter(op =>
                op.materiaisNecessarios &&
                op.materiaisNecessarios.some(m => m.produtoId === produtoId)
            );

            let detalhes = `Detalhes do Material: ${produto?.codigo || produtoId}\n`;
            detalhes += `Descrição: ${produto?.descricao || 'N/A'}\n\n`;
            detalhes += `OPs que usam este material (${opsQueUsam.length}):\n\n`;

            opsQueUsam.forEach(op => {
                const material = op.materiaisNecessarios.find(m => m.produtoId === produtoId);
                const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                const transferido = material.saldoReservado || 0;

                detalhes += `• OP ${op.numero} (${op.status})\n`;
                detalhes += `  Necessário: ${necessario.toFixed(3)} ${produto?.unidade || 'UN'}\n`;
                detalhes += `  Transferido: ${transferido.toFixed(3)} ${produto?.unidade || 'UN'}\n`;
                detalhes += `  Restante: ${(necessario - transferido).toFixed(3)} ${produto?.unidade || 'UN'}\n\n`;
            });

            alert(detalhes);
        };

        window.selecionarOPAnalise = function(opId) {
            document.getElementById('opSelect').value = opId;
            selecionarOP(opId);

            // Fechar dashboard e mostrar OP selecionada
            document.getElementById('analysisSection').style.display = 'none';

            setTimeout(() => {
                document.getElementById('opMaterialsPreview').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 300);
        };

        window.filtrarTabelaTodos = function() {
            const busca = document.getElementById('searchTodos').value.toLowerCase();
            const prioridade = document.getElementById('prioridadeTodos').value;

            const rows = document.querySelectorAll('#tableTodos tbody tr');
            rows.forEach(row => {
                const codigo = row.dataset.codigo.toLowerCase();
                const descricao = row.dataset.descricao.toLowerCase();
                const prioridadeRow = row.dataset.prioridade;

                const matchBusca = !busca || codigo.includes(busca) || descricao.includes(busca);
                const matchPrioridade = !prioridade || prioridadeRow === prioridade;

                row.style.display = matchBusca && matchPrioridade ? '' : 'none';
            });
        };

        window.filtrarTabelaOPs = function() {
            const busca = document.getElementById('searchOPs').value.toLowerCase();

            const rows = document.querySelectorAll('#tabelaOPs tbody tr');
            rows.forEach(row => {
                const op = row.dataset.op.toLowerCase();
                const produto = row.dataset.produto.toLowerCase();

                const match = !busca || op.includes(busca) || produto.includes(busca);
                row.style.display = match ? '' : 'none';
            });
        };

        window.atualizarAnalise = function() {
            mostrarNotificacao('Atualizando análise...', 'info');
            carregarDadosAnalise();
            mostrarNotificacao('Análise atualizada com sucesso!', 'success');
        };

        window.exportarAnalise = function() {
            mostrarNotificacao('Funcionalidade de exportação será implementada em breve', 'info');
        };

        // ========================================
        // 🎯 FUNCIONALIDADES AUXILIARES AVANÇADAS
        // ========================================

        // Função para ordenar tabelas
        window.ordenarTabelaTodos = function(campo) {
            const tabela = document.getElementById('tableTodos');
            const tbody = tabela.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determinar direção da ordenação
            const isAsc = tabela.dataset.sortDirection !== 'asc';
            tabela.dataset.sortDirection = isAsc ? 'asc' : 'desc';

            rows.sort((a, b) => {
                let valueA, valueB;

                switch (campo) {
                    case 'codigo':
                        valueA = a.dataset.codigo;
                        valueB = b.dataset.codigo;
                        break;
                    case 'descricao':
                        valueA = a.dataset.descricao;
                        valueB = b.dataset.descricao;
                        break;
                    case 'necessario':
                        valueA = parseFloat(a.dataset.necessario);
                        valueB = parseFloat(b.dataset.necessario);
                        break;
                    case 'transferido':
                        valueA = parseFloat(a.dataset.transferido);
                        valueB = parseFloat(b.dataset.transferido);
                        break;
                    case 'restante':
                        valueA = parseFloat(a.dataset.restante);
                        valueB = parseFloat(b.dataset.restante);
                        break;
                    default:
                        return 0;
                }

                if (typeof valueA === 'string') {
                    return isAsc ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
                } else {
                    return isAsc ? valueA - valueB : valueB - valueA;
                }
            });

            // Reordenar linhas
            rows.forEach(row => tbody.appendChild(row));

            // Atualizar indicadores visuais
            tabela.querySelectorAll('th i').forEach(icon => {
                icon.className = 'fas fa-sort';
            });

            const header = tabela.querySelector(`th[onclick="ordenarTabelaTodos('${campo}')"] i`);
            if (header) {
                header.className = isAsc ? 'fas fa-sort-up' : 'fas fa-sort-down';
            }
        };

        // Função para detectar mudanças em tempo real
        function configurarDeteccaoMudancas() {
            // Detectar quando usuário sai da página
            window.addEventListener('beforeunload', function(e) {
                if (transferenciasEmAndamento) {
                    e.preventDefault();
                    e.returnValue = 'Transferências em andamento. Tem certeza que deseja sair?';
                }
            });

            // Detectar inatividade
            let inactivityTimer;
            function resetInactivityTimer() {
                clearTimeout(inactivityTimer);
                inactivityTimer = setTimeout(() => {
                    console.log('💤 Usuário inativo - pausando sincronização automática');
                    // Implementar lógica de pausa se necessário
                }, 300000); // 5 minutos
            }

            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
                document.addEventListener(event, resetInactivityTimer, true);
            });

            resetInactivityTimer();
        }

        // Função para logs de auditoria
        function logAuditoria(acao, dados) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                acao: acao,
                dados: dados,
                usuario: 'sistema', // Implementar autenticação real
                ip: 'localhost', // Implementar detecção de IP real
                userAgent: navigator.userAgent
            };

            console.log('📝 Log de Auditoria:', logEntry);

            // Salvar no localStorage para persistência local
            const logs = JSON.parse(localStorage.getItem('auditoria_movimentacao') || '[]');
            logs.push(logEntry);

            // Manter apenas os últimos 100 logs
            if (logs.length > 100) {
                logs.splice(0, logs.length - 100);
            }

            localStorage.setItem('auditoria_movimentacao', JSON.stringify(logs));
        }

        // Função para performance monitoring
        function monitorarPerformance() {
            const startTime = performance.now();

            return {
                finalizar: function(operacao) {
                    const endTime = performance.now();
                    const duracao = endTime - startTime;

                    console.log(`⏱️ Performance - ${operacao}: ${duracao.toFixed(2)}ms`);

                    if (duracao > 1000) {
                        console.warn(`⚠️ Operação lenta detectada: ${operacao} (${duracao.toFixed(2)}ms)`);
                    }

                    return duracao;
                }
            };
        }

        // Função para validação de integridade dos dados
        function validarIntegridadeDados() {
            const monitor = monitorarPerformance();

            let problemas = [];

            // Validar ordens
            ordens.forEach(ordem => {
                if (!ordem.numero || !ordem.produtoId) {
                    problemas.push(`OP ${ordem.id}: dados obrigatórios ausentes`);
                }

                if (ordem.materiaisNecessarios) {
                    ordem.materiaisNecessarios.forEach(material => {
                        if (!material.produtoId) {
                            problemas.push(`OP ${ordem.numero}: material sem produtoId`);
                        }
                    });
                }
            });

            // Validar produtos
            const produtoIds = new Set(produtos.map(p => p.id));
            ordens.forEach(ordem => {
                if (ordem.produtoId && !produtoIds.has(ordem.produtoId)) {
                    problemas.push(`OP ${ordem.numero}: produto ${ordem.produtoId} não encontrado`);
                }
            });

            // Validar estoques
            estoques.forEach(estoque => {
                if (!estoque.produtoId || !estoque.armazemId) {
                    problemas.push(`Estoque ${estoque.id}: dados obrigatórios ausentes`);
                }

                if (estoque.saldo < 0) {
                    problemas.push(`Estoque ${estoque.id}: saldo negativo (${estoque.saldo})`);
                }
            });

            monitor.finalizar('Validação de Integridade');

            if (problemas.length > 0) {
                console.warn('⚠️ Problemas de integridade encontrados:', problemas);
                logAuditoria('INTEGRIDADE_PROBLEMAS', { problemas });
            } else {
                console.log('✅ Integridade dos dados validada com sucesso');
            }

            return problemas;
        }

        // Função para backup automático
        function criarBackupLocal() {
            const backup = {
                timestamp: new Date().toISOString(),
                versao: '1.0.0',
                dados: {
                    ordens: ordens.length,
                    produtos: produtos.length,
                    armazens: armazens.length,
                    estoques: estoques.length,
                    transferencias: transferencias.length
                },
                configuracoes: {
                    filtroAtual: filtroAtual,
                    selectedOrderId: selectedOrder?.id
                }
            };

            localStorage.setItem('backup_movimentacao', JSON.stringify(backup));
            console.log('💾 Backup local criado:', backup);
        }

        // Função para restaurar estado da sessão
        function restaurarEstadoSessao() {
            try {
                const backup = JSON.parse(localStorage.getItem('backup_movimentacao') || '{}');

                if (backup.configuracoes) {
                    // Restaurar filtro
                    if (backup.configuracoes.filtroAtual) {
                        setTimeout(() => {
                            aplicarFiltroOP(backup.configuracoes.filtroAtual);
                        }, 1000);
                    }

                    // Restaurar OP selecionada
                    if (backup.configuracoes.selectedOrderId) {
                        setTimeout(() => {
                            const select = document.getElementById('opSelect');
                            select.value = backup.configuracoes.selectedOrderId;
                            selecionarOP(backup.configuracoes.selectedOrderId);
                        }, 1500);
                    }
                }

                console.log('🔄 Estado da sessão restaurado');
            } catch (error) {
                console.warn('⚠️ Erro ao restaurar estado da sessão:', error);
            }
        }

        // ========================================
        // 🚀 INICIALIZAÇÃO FINAL E CONFIGURAÇÕES
        // ========================================

        // Executar configurações adicionais após inicialização
        window.addEventListener('load', function() {
            setTimeout(() => {
                configurarDeteccaoMudancas();
                validarIntegridadeDados();
                restaurarEstadoSessao();

                // Criar backup inicial
                criarBackupLocal();

                // Configurar backup automático a cada 5 minutos
                setInterval(criarBackupLocal, 300000);

                // Configurar validação automática a cada 10 minutos
                setInterval(validarIntegridadeDados, 600000);

                console.log('🎉 Sistema de Movimentação de Armazéns totalmente inicializado!');
                mostrarNotificacao('Sistema carregado com sucesso! Pronto para uso.', 'success');

                logAuditoria('SISTEMA_INICIALIZADO', {
                    ordens: ordens.length,
                    produtos: produtos.length,
                    armazens: armazens.length
                });

            }, 2000);
        });

        // Salvar estado antes de sair
        window.addEventListener('beforeunload', function() {
            criarBackupLocal();
            logAuditoria('SISTEMA_FINALIZADO', {
                sessaoDuracao: Date.now() - window.sessionStartTime
            });
        });

        // Marcar início da sessão
        window.sessionStartTime = Date.now();

        window.mostrarDetalhesOP = function() {
            if (!selectedOrder) {
                mostrarNotificacao('Nenhuma OP selecionada!', 'warning');
                return;
            }

            const detalhes = `
OP: ${selectedOrder.numero}
Status: ${selectedOrder.status}
Produto: ${produtos.find(p => p.id === selectedOrder.produtoId)?.descricao || 'N/A'}
Quantidade: ${selectedOrder.quantidade || 'N/A'}
Data Criação: ${selectedOrder.dataCriacao ? new Date(selectedOrder.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}
Materiais Necessários: ${selectedOrder.materiaisNecessarios?.length || 0}
            `;

            alert(detalhes);
        };

        // ========================================
        // 🔒 CLASSES DE VALIDAÇÃO
        // ========================================

        class QuantityValidator {
            static validateQuantity(quantidade, disponivel) {
                if (quantidade <= 0) {
                    return { valid: false, message: 'Quantidade deve ser maior que zero' };
                }

                if (quantidade > disponivel) {
                    return { valid: false, message: 'Quantidade maior que disponível' };
                }

                return { valid: true, message: 'Quantidade válida' };
            }

            static validatePositiveNumber(value) {
                const num = parseFloat(value);
                return !isNaN(num) && num > 0;
            }

            static formatQuantity(value, decimals = 3) {
                return parseFloat(value).toFixed(decimals);
            }
        }

        class TransactionManager {
            static async executeAtomicTransfer(transferData) {
                console.log('🔄 Iniciando transferência atômica:', transferData);

                try {
                    // Validar dados antes da transação
                    const validation = await this.validateTransferData(transferData);
                    if (!validation.valid) {
                        throw new Error(validation.message);
                    }
                    console.log('✅ Validação passou:', validation.message);

                    const result = await runTransaction(db, async (transaction) => {
                        // Executar transferência
                        const transferResult = await this.processTransfer(transaction, transferData);

                        console.log('✅ Transferência processada:', transferResult);
                        return transferResult;
                    });

                    console.log('✅ Transferência atômica concluída:', result);
                    return { success: true, data: result };
                } catch (error) {
                    console.error('❌ Erro na transferência atômica:', error);
                    console.error('❌ Stack trace:', error.stack);
                    return { success: false, error: error.message };
                }
            }

            static async validateTransferData(data) {
                // Validações básicas
                if (!data.produtoId || !data.armazemOrigemId || !data.armazemDestinoId) {
                    return { valid: false, message: 'Dados obrigatórios não informados' };
                }

                if (data.quantidade <= 0) {
                    return { valid: false, message: 'Quantidade deve ser maior que zero' };
                }

                // Validar estoque disponível
                const estoqueOrigem = estoques.find(e =>
                    e.produtoId === data.produtoId && e.armazemId === data.armazemOrigemId
                );

                if (!estoqueOrigem || estoqueOrigem.saldo < data.quantidade) {
                    return { valid: false, message: 'Estoque insuficiente no armazém origem' };
                }

                return { valid: true, message: 'Dados válidos' };
            }

            static async processTransfer(transaction, data) {
                try {
                    console.log('🔧 Processando transferência:', data);

                    // ===== FASE 1: TODAS AS LEITURAS PRIMEIRO =====

                    // 1.1. Buscar estoque origem no Firestore
                    const estoqueOrigemQuery = query(
                        collection(db, "estoques"),
                        where("produtoId", "==", data.produtoId),
                        where("armazemId", "==", data.armazemOrigemId)
                    );
                    const estoqueOrigemSnapshot = await getDocs(estoqueOrigemQuery);

                    // 1.2. Buscar estoque destino no Firestore
                    const estoqueDestinoQuery = query(
                        collection(db, "estoques"),
                        where("produtoId", "==", data.produtoId),
                        where("armazemId", "==", data.armazemDestinoId)
                    );
                    const estoqueDestinoSnapshot = await getDocs(estoqueDestinoQuery);

                    console.log('📊 Estoque origem encontrado:', !estoqueOrigemSnapshot.empty);
                    console.log('📊 Estoque destino encontrado:', !estoqueDestinoSnapshot.empty);

                    // Obter referências dos documentos
                    let estoqueOrigemRef, estoqueOrigemData;
                    let estoqueDestinoRef, estoqueDestinoData;

                    if (!estoqueOrigemSnapshot.empty) {
                        estoqueOrigemRef = estoqueOrigemSnapshot.docs[0].ref;
                        estoqueOrigemData = estoqueOrigemSnapshot.docs[0].data();
                    } else {
                        throw new Error(`Estoque origem não encontrado: produto ${data.produtoId} no armazém ${data.armazemOrigemId}`);
                    }

                    if (!estoqueDestinoSnapshot.empty) {
                        estoqueDestinoRef = estoqueDestinoSnapshot.docs[0].ref;
                        estoqueDestinoData = estoqueDestinoSnapshot.docs[0].data();
                    } else {
                        // Criar referência para novo estoque destino
                        estoqueDestinoRef = doc(collection(db, "estoques"));
                        estoqueDestinoData = null;
                    }

                    // ===== FASE 2: TODAS AS ESCRITAS DEPOIS =====

                    // 2.1. Criar registro de transferência
                    const numeroTransferencia = `TRANSF-${Date.now()}`;
                    const transferenciaRef = doc(collection(db, "transferenciasArmazem"));

                    // Buscar dados do produto para completar o registro
                    const produto = produtos.find(p => p.id === data.produtoId);
                    const armazemOrigem = armazens.find(a => a.id === data.armazemOrigemId);
                    const armazemDestino = armazens.find(a => a.id === data.armazemDestinoId);

                    const transferencia = {
                        // Dados básicos da transferência
                        ordemProducaoId: data.ordemProducaoId,
                        produtoId: data.produtoId,
                        armazemOrigemId: data.armazemOrigemId,
                        armazemDestinoId: data.armazemDestinoId,
                        quantidade: data.quantidade,

                        // Dados do produto
                        codigo: produto?.codigo || 'N/A',
                        descricao: produto?.descricao || 'N/A',
                        unidade: produto?.unidade || 'UN',

                        // Dados dos armazéns
                        armazemOrigemNome: armazemOrigem?.nome || 'N/A',
                        armazemDestinoNome: armazemDestino?.nome || 'N/A',

                        // Metadados da transferência
                        tipo: 'OP',
                        status: 'CONCLUIDA',
                        dataHora: Timestamp.now(),
                        numeroTransferencia: numeroTransferencia,
                        observacoes: data.observacoes || `Transferência para OP ${selectedOrder?.numero || 'N/A'}`,

                        // Dados do usuário (implementar autenticação real)
                        usuario: 'Sistema',
                        usuarioId: 'sistema',

                        // Compatibilidade com sistema antigo
                        origem: 'SISTEMA_NOVO',
                        integradoComMovimentacoes: true
                    };

                    transaction.set(transferenciaRef, transferencia);
                    console.log('📝 Transferência registrada:', numeroTransferencia);

                    // 2.2. Atualizar estoque origem (reduzir)
                    const novoSaldoOrigem = estoqueOrigemData.saldo - data.quantidade;

                    transaction.update(estoqueOrigemRef, {
                        saldo: novoSaldoOrigem,
                        ultimaMovimentacao: Timestamp.now()
                    });
                    console.log(`📉 Estoque origem atualizado: ${estoqueOrigemData.saldo} - ${data.quantidade} = ${novoSaldoOrigem}`);

                    // 2.3. Atualizar estoque destino (aumentar)
                    if (estoqueDestinoData) {
                        // Documento existe - atualizar
                        const novoSaldoDestino = estoqueDestinoData.saldo + data.quantidade;

                        transaction.update(estoqueDestinoRef, {
                            saldo: novoSaldoDestino,
                            ultimaMovimentacao: Timestamp.now()
                        });
                        console.log(`📈 Estoque destino atualizado: ${estoqueDestinoData.saldo} + ${data.quantidade} = ${novoSaldoDestino}`);
                    } else {
                        // Documento não existe - criar novo
                        transaction.set(estoqueDestinoRef, {
                            produtoId: data.produtoId,
                            armazemId: data.armazemDestinoId,
                            saldo: data.quantidade,
                            saldoReservado: 0,
                            saldoEmpenhado: 0,
                            ultimaMovimentacao: Timestamp.now(),
                            criadoEm: Timestamp.now()
                        });
                        console.log(`🆕 Novo estoque destino criado: ${data.quantidade}`);
                    }

                    // 4. Atualizar saldoReservado na OP
                    const opRef = doc(db, "ordensProducao", data.ordemProducaoId);
                    const ordem = ordens.find(op => op.id === data.ordemProducaoId);

                    if (ordem && ordem.materiaisNecessarios) {
                        const materiaisAtualizados = ordem.materiaisNecessarios.map(material => {
                            if (material.produtoId === data.produtoId) {
                                return {
                                    ...material,
                                    saldoReservado: (material.saldoReservado || 0) + data.quantidade
                                };
                            }
                            return material;
                        });

                        transaction.update(opRef, {
                            materiaisNecessarios: materiaisAtualizados,
                            ultimaAtualizacao: Timestamp.now()
                        });
                    }

                    return {
                        transferenciaId: transferenciaRef.id,
                        numeroTransferencia: numeroTransferencia,
                        status: 'CONCLUIDA',
                        timestamp: Timestamp.now()
                    };

                } catch (error) {
                    console.error('❌ Erro no processTransfer:', error);
                    throw error;
                }
            }
        }

        // ========================================
        // 🔄 SISTEMA DE TRANSFERÊNCIAS
        // ========================================

        let materiaisSelecionados = [];
        let transferenciasEmAndamento = false;

        function mostrarSecaoTransferencias() {
            if (!selectedOrder) {
                mostrarNotificacao('Selecione uma OP primeiro!', 'warning');
                return;
            }

            const section = document.getElementById('transferSection');
            carregarMateriaisParaTransferencia();
            popularArmazensOrigem();
            configurarFiltrosTransferencia();
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        function carregarMateriaisParaTransferencia() {
            if (!selectedOrder || !selectedOrder.materiaisNecessarios) return;

            const materiaisMP = selectedOrder.materiaisNecessarios.filter(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto || produto.tipo !== 'MP') return false;

                const temQuantidade = (material.necessidade > 0) || (material.quantidade > 0);
                return temQuantidade;
            });

            atualizarTabelaTransferencias(materiaisMP);
        }

        function atualizarTabelaTransferencias(materiais) {
            const tbody = document.getElementById('transferTableBody');
            tbody.innerHTML = '';

            if (materiais.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 30px; color: #6c757d;">
                            <i class="fas fa-box-open" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                            <strong>Nenhum material disponível para transferência</strong>
                        </td>
                    </tr>
                `;
                return;
            }

            materiais.forEach(material => {
                const produto = produtos.find(p => p.id === material.produtoId);
                if (!produto) return;

                const necessario = material.necessidade > 0 ? material.necessidade : (material.quantidade || 0);
                const transferido = material.saldoReservado || 0;
                const restante = calcularRestante(necessario, transferido);

                // Buscar estoque disponível
                const estoqueDisponivel = calcularEstoqueDisponivel(material.produtoId);

                // Determinar status
                let status = 'PENDENTE';
                let statusClass = 'danger';
                if (isTransferenciaCompleta(necessario, transferido)) {
                    status = 'COMPLETO';
                    statusClass = 'success';
                } else if (transferido > 0) {
                    status = 'PARCIAL';
                    statusClass = 'warning';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="text-align: center;">
                        <input type="checkbox" class="material-checkbox"
                               data-produto-id="${material.produtoId}"
                               data-necessario="${restante}"
                               onchange="atualizarSelecao()">
                    </td>
                    <td style="font-weight: 600;">${produto.codigo}</td>
                    <td title="${produto.descricao}">
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${produto.descricao}
                        </div>
                    </td>
                    <td style="text-align: center;">
                        <select class="form-control armazem-origem-select" data-produto-id="${material.produtoId}">
                            <option value="">Selecionar...</option>
                        </select>
                    </td>
                    <td style="text-align: center; font-weight: 600; color: #28a745;">
                        ${estoqueDisponivel.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="text-align: center; font-weight: 600;">
                        ${restante.toFixed(3)} ${produto.unidade}
                    </td>
                    <td style="text-align: center;">
                        <input type="number" class="form-control quantidade-input"
                               data-produto-id="${material.produtoId}"
                               min="0" max="${Math.min(estoqueDisponivel, restante)}"
                               step="0.001" value="${Math.min(estoqueDisponivel, restante).toFixed(3)}"
                               style="width: 100px;" disabled>
                    </td>
                    <td style="text-align: center;">
                        <span class="badge ${statusClass}">${status}</span>
                    </td>
                    <td style="text-align: center;">
                        <button class="btn ${material.aguardandoMaterial ? 'btn-warning' : 'btn-outline-warning'} btn-sm"
                                onclick="toggleAguardandoMaterial('${material.produtoId}')"
                                title="${material.aguardandoMaterial ? 'Material em aguardo - Clique para remover' : 'Marcar como aguardando material'}">
                            <i class="fas ${material.aguardandoMaterial ? 'fa-clock' : 'fa-clock-o'}"></i>
                            ${material.aguardandoMaterial ? 'Aguardando' : 'Aguardar'}
                        </button>
                    </td>
                    <td style="text-align: center;">
                        <button class="btn btn-info btn-sm" onclick="mostrarHistoricoMaterial('${material.produtoId}')"
                                title="Ver histórico">
                            <i class="fas fa-history"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Popular dropdowns de armazém origem
            popularArmazensOrigemPorMaterial();
        }

        function calcularEstoqueDisponivel(produtoId) {
            const estoquesTotais = estoques.filter(e => e.produtoId === produtoId);
            return estoquesTotais.reduce((total, estoque) => {
                const saldo = estoque.saldo || 0;
                const reservado = estoque.saldoReservado || 0;
                const empenhado = estoque.saldoEmpenhado || 0;
                // Calcular saldo disponível real (descontando reservas e empenhos)
                const disponivel = Math.max(0, saldo - reservado - empenhado);
                return total + disponivel;
            }, 0);
        }

        function popularArmazensOrigem() {
            const select = document.getElementById('armazemOrigemSelect');
            select.innerHTML = '<option value="">Todos os armazéns...</option>';

            const armazensComEstoque = armazens.filter(a => a.tipo !== 'PRODUCAO');
            armazensComEstoque.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                select.appendChild(option);
            });
        }

        function popularArmazensOrigemPorMaterial() {
            document.querySelectorAll('.armazem-origem-select').forEach(select => {
                const produtoId = select.dataset.produtoId;
                select.innerHTML = '<option value="">Selecionar...</option>';

                // Buscar armazéns que têm estoque deste produto
                const estoquesComSaldo = estoques.filter(e =>
                    e.produtoId === produtoId && e.saldo > 0
                );

                estoquesComSaldo.forEach(estoque => {
                    const armazem = armazens.find(a => a.id === estoque.armazemId);
                    if (armazem && armazem.tipo !== 'PRODUCAO') {
                        const option = document.createElement('option');
                        option.value = armazem.id;
                        option.textContent = `${armazem.codigo} - ${estoque.saldo.toFixed(3)}`;
                        select.appendChild(option);
                    }
                });

                // Evento de mudança para atualizar quantidade disponível
                select.addEventListener('change', function() {
                    atualizarQuantidadeDisponivel(produtoId, this.value);
                });
            });
        }

        function atualizarQuantidadeDisponivel(produtoId, armazemId) {
            const quantidadeInput = document.querySelector(`input[data-produto-id="${produtoId}"]`);
            const checkbox = document.querySelector(`input[data-produto-id="${produtoId}"][type="checkbox"]`);

            if (!armazemId) {
                quantidadeInput.disabled = true;
                checkbox.disabled = true;
                return;
            }

            const estoque = estoques.find(e =>
                e.produtoId === produtoId && e.armazemId === armazemId
            );

            const disponivel = estoque ? estoque.saldo : 0;
            const necessario = parseFloat(checkbox.dataset.necessario);
            const maxTransferir = Math.min(disponivel, necessario);

            quantidadeInput.max = maxTransferir;
            quantidadeInput.value = maxTransferir.toFixed(3);
            quantidadeInput.disabled = false;
            checkbox.disabled = disponivel <= 0;

            // Validar quantidade
            quantidadeInput.addEventListener('input', function() {
                const valor = parseFloat(this.value);
                const validation = QuantityValidator.validateQuantity(valor, disponivel);

                if (!validation.valid) {
                    this.style.borderColor = '#dc3545';
                    this.title = validation.message;
                } else {
                    this.style.borderColor = '#28a745';
                    this.title = 'Quantidade válida';
                }
            });
        }

        function configurarFiltrosTransferencia() {
            // Filtro de busca
            document.getElementById('materialSearch').addEventListener('input', function() {
                filtrarTabelaTransferencias();
            });

            // Filtro de status
            document.getElementById('statusFilter').addEventListener('change', function() {
                filtrarTabelaTransferencias();
            });

            // Filtro de armazém origem
            document.getElementById('armazemOrigemSelect').addEventListener('change', function() {
                filtrarTabelaTransferencias();
            });
        }

        function filtrarTabelaTransferencias() {
            const busca = document.getElementById('materialSearch').value.toLowerCase();
            const statusFiltro = document.getElementById('statusFilter').value;
            const armazemFiltro = document.getElementById('armazemOrigemSelect').value;

            const rows = document.querySelectorAll('#transferTableBody tr');

            rows.forEach(row => {
                if (row.cells.length === 1) return; // Pular linha vazia

                const codigo = row.cells[1].textContent.toLowerCase();
                const descricao = row.cells[2].textContent.toLowerCase();
                const status = row.cells[7].textContent.trim();
                const armazemSelect = row.querySelector('.armazem-origem-select');

                const matchBusca = !busca || codigo.includes(busca) || descricao.includes(busca);
                const matchStatus = !statusFiltro || status === statusFiltro;
                const matchArmazem = !armazemFiltro || armazemSelect.value === armazemFiltro;

                row.style.display = matchBusca && matchStatus && matchArmazem ? '' : 'none';
            });
        }

        window.selecionarTodos = function(checked) {
            document.querySelectorAll('.material-checkbox:not(:disabled)').forEach(checkbox => {
                checkbox.checked = checked;
            });
            atualizarSelecao();
        };

        window.atualizarSelecao = function() {
            const checkboxes = document.querySelectorAll('.material-checkbox:checked');
            materiaisSelecionados = Array.from(checkboxes).map(cb => ({
                produtoId: cb.dataset.produtoId,
                necessario: parseFloat(cb.dataset.necessario)
            }));

            document.getElementById('transferCount').textContent = `${materiaisSelecionados.length} selecionados`;
            document.getElementById('btnTransferir').disabled = materiaisSelecionados.length === 0;

            // Habilitar/desabilitar inputs de quantidade
            document.querySelectorAll('.quantidade-input').forEach(input => {
                const checkbox = document.querySelector(`input[data-produto-id="${input.dataset.produtoId}"][type="checkbox"]`);
                input.disabled = !checkbox.checked;
            });
        };

        window.limparSelecao = function() {
            document.querySelectorAll('.material-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            atualizarSelecao();
        };

        window.executarTransferencias = async function() {
            if (materiaisSelecionados.length === 0) {
                mostrarNotificacao('Nenhum material selecionado!', 'warning');
                return;
            }

            if (transferenciasEmAndamento) {
                mostrarNotificacao('Transferências já em andamento!', 'warning');
                return;
            }

            const confirmacao = confirm(`Confirma a transferência de ${materiaisSelecionados.length} material(is)?`);
            if (!confirmacao) return;

            transferenciasEmAndamento = true;
            document.getElementById('btnTransferir').disabled = true;
            document.getElementById('btnTransferir').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Transferindo...';

            try {
                const resultados = await processarTransferenciasLote();
                mostrarResultadosTransferencia(resultados);
            } catch (error) {
                console.error('Erro nas transferências:', error);
                mostrarNotificacao('Erro ao executar transferências: ' + error.message, 'error');
            } finally {
                transferenciasEmAndamento = false;
                document.getElementById('btnTransferir').disabled = false;
                document.getElementById('btnTransferir').innerHTML = '<i class="fas fa-paper-plane"></i> Transferir Selecionados';
            }
        };

        async function processarTransferenciasLote() {
            const resultados = [];

            for (const material of materiaisSelecionados) {
                const checkbox = document.querySelector(`input[data-produto-id="${material.produtoId}"][type="checkbox"]`);
                const armazemSelect = document.querySelector(`.armazem-origem-select[data-produto-id="${material.produtoId}"]`);
                const quantidadeInput = document.querySelector(`.quantidade-input[data-produto-id="${material.produtoId}"]`);

                if (!armazemSelect.value || !quantidadeInput.value) {
                    resultados.push({
                        produtoId: material.produtoId,
                        success: false,
                        error: 'Armazém origem ou quantidade não informados'
                    });
                    continue;
                }

                const transferData = {
                    produtoId: material.produtoId,
                    armazemOrigemId: armazemSelect.value,
                    armazemDestinoId: selectedOrder.armazemProducaoId,
                    quantidade: parseFloat(quantidadeInput.value),
                    ordemProducaoId: selectedOrder.id,
                    observacoes: `Transferência para OP ${selectedOrder.numero}`
                };

                console.log('🔄 Executando transferência:', transferData);
                const resultado = await TransactionManager.executeAtomicTransfer(transferData);
                console.log('📊 Resultado da transferência:', resultado);

                // 🔄 Notificar apontamentos imediatamente após transferência bem-sucedida
                if (resultado.success) {
                    try {
                        await notificarApontamentosSobreTransferencia(
                            transferData.ordemProducaoId,
                            transferData.produtoId,
                            transferData.quantidade
                        );
                    } catch (error) {
                        console.warn('⚠️ Erro ao notificar apontamentos (transferência foi bem-sucedida):', error);
                    }
                }

                resultados.push({
                    produtoId: material.produtoId,
                    ...resultado
                });
            }

            return resultados;
        }

        function mostrarResultadosTransferencia(resultados) {
            const sucessos = resultados.filter(r => r.success).length;
            const erros = resultados.filter(r => !r.success).length;

            let mensagem = `Transferências concluídas: ${sucessos} sucesso(s), ${erros} erro(s)`;

            if (erros > 0) {
                mensagem += '\n\nErros encontrados:\n';
                resultados.filter(r => !r.success).forEach(r => {
                    const produto = produtos.find(p => p.id === r.produtoId);
                    mensagem += `• ${produto?.codigo || r.produtoId}: ${r.error}\n`;
                });
            }

            if (sucessos > 0) {
                mostrarNotificacao(`${sucessos} transferência(s) realizada(s) com sucesso!`, 'success');

                // Recarregar dados e atualizar interface
                setTimeout(async () => {
                    await Promise.all([
                        carregarTransferencias(),
                        carregarEstoques(),
                        carregarOrdens()
                    ]);

                    if (selectedOrder) {
                        // Recarregar OP atualizada
                        const opAtualizada = ordens.find(op => op.id === selectedOrder.id);
                        if (opAtualizada) {
                            selectedOrder = opAtualizada;

                            // Recarregar histórico de transferências
                            await carregarHistoricoTransferenciasOP(selectedOrder.id);

                            // 🔄 INTEGRAÇÃO COM APONTAMENTOS: Atualizar flag automaticamente
                            try {
                                if (window.atualizarFlagOPAposTransferencia) {
                                    console.log('🔄 Atualizando flag no apontamentos...');
                                    await window.atualizarFlagOPAposTransferencia(selectedOrder.id);
                                } else {
                                    console.log('⚠️ Função de atualização de flag não encontrada (apontamentos não carregado)');
                                }
                            } catch (error) {
                                console.error('❌ Erro ao atualizar flag no apontamentos:', error);
                            }

                            mostrarPreviewMateriais(selectedOrder);
                            carregarMateriaisParaTransferencia();
                        }
                    }

                    // Limpar seleção
                    limparSelecao();

                    console.log('✅ Dados atualizados após transferência');
                }, 1000);
            }

            if (erros > 0) {
                alert(mensagem);
            }
        }

        window.mostrarHistoricoMaterial = function(produtoId) {
            const produto = produtos.find(p => p.id === produtoId);
            const transferenciasHistorico = transferencias.filter(t =>
                t.produtoId === produtoId && t.ordemProducaoId === selectedOrder.id
            );

            let historico = `Histórico de Transferências - ${produto?.codigo || produtoId}\n\n`;

            if (transferenciasHistorico.length === 0) {
                historico += 'Nenhuma transferência encontrada.';
            } else {
                transferenciasHistorico.forEach(t => {
                    const data = t.dataTransferencia ? new Date(t.dataTransferencia.seconds * 1000).toLocaleDateString() : 'N/A';
                    historico += `• ${data}: ${t.quantidade} ${produto?.unidade || 'UN'} - ${t.status}\n`;
                });
            }

            alert(historico);
        };

        // ========================================
        // ⏰ CONTROLE DE AGUARDANDO MATERIAL
        // ========================================

        window.toggleAguardandoMaterial = async function(produtoId) {
            try {
                if (!selectedOrder) {
                    alert('❌ Nenhuma OP selecionada!');
                    return;
                }

                console.log(`🔄 Toggle aguardando material - OP: ${selectedOrder.numero}, Produto: ${produtoId}`);

                // Encontrar o material na OP
                const materialIndex = selectedOrder.materiaisNecessarios.findIndex(m => m.produtoId === produtoId);
                if (materialIndex === -1) {
                    alert('❌ Material não encontrado na OP!');
                    return;
                }

                const material = selectedOrder.materiaisNecessarios[materialIndex];
                const novoStatus = !material.aguardandoMaterial;

                // Buscar produto para exibição
                const produto = produtos.find(p => p.id === produtoId);
                const nomeProduto = produto ? `${produto.codigo} - ${produto.descricao}` : produtoId;

                // Confirmar ação
                const acao = novoStatus ? 'MARCAR COMO AGUARDANDO' : 'REMOVER AGUARDO';
                const confirmacao = confirm(`${acao} MATERIAL\n\nOP: ${selectedOrder.numero}\nMaterial: ${nomeProduto}\n\n${novoStatus ? '⏰ Este material será marcado como "aguardando chegada"' : '✅ O aguardo será removido'}\n\nConfirma a ação?`);

                if (!confirmacao) return;

                // Atualizar localmente
                selectedOrder.materiaisNecessarios[materialIndex].aguardandoMaterial = novoStatus;
                if (novoStatus) {
                    selectedOrder.materiaisNecessarios[materialIndex].dataAguardo = new Date();
                    selectedOrder.materiaisNecessarios[materialIndex].usuarioAguardo = 'Sistema'; // Implementar usuário real
                } else {
                    delete selectedOrder.materiaisNecessarios[materialIndex].dataAguardo;
                    delete selectedOrder.materiaisNecessarios[materialIndex].usuarioAguardo;
                }

                // Atualizar no Firebase
                await updateDoc(doc(db, "ordensProducao", selectedOrder.id), {
                    materiaisNecessarios: selectedOrder.materiaisNecessarios,
                    ultimaAtualizacao: Timestamp.now()
                });

                // Atualizar interface
                carregarMateriaisParaTransferencia();

                // Mostrar confirmação
                const mensagem = novoStatus
                    ? `⏰ MATERIAL MARCADO COMO AGUARDANDO!\n\nOP: ${selectedOrder.numero}\nMaterial: ${nomeProduto}\n\n📅 Data: ${new Date().toLocaleString('pt-BR')}`
                    : `✅ AGUARDO REMOVIDO!\n\nOP: ${selectedOrder.numero}\nMaterial: ${nomeProduto}`;

                alert(mensagem);

                console.log(`✅ Status aguardando atualizado: ${novoStatus} para produto ${produtoId}`);

            } catch (error) {
                console.error('❌ Erro ao atualizar status aguardando:', error);
                alert('❌ Erro ao atualizar status: ' + error.message);
            }
        };

    </script>
</body>
</html>
