<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Correção de Sincronização - Processo de Compras</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .action-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .action-card p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(255,193,7,0.4);
        }

        .results-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .results-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .problem-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #dc3545;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .problem-item.warning {
            border-left-color: #ffc107;
        }

        .problem-item.success {
            border-left-color: #28a745;
        }

        .problem-item h4 {
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .problem-item p {
            color: #6c757d;
            margin-bottom: 10px;
        }

        .problem-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .problem-actions .btn {
            width: auto;
            padding: 8px 15px;
            font-size: 0.9em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #007bff;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 1.2em;
            color: #007bff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: white;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='index.html'" title="Voltar ao Menu">
        ←
    </button>

    <div class="container">
        <div class="header">
            <h1>🔧 Correção de Sincronização</h1>
            <p>Ferramenta para diagnosticar e corrigir problemas no fluxo de compras</p>
        </div>

        <div class="main-content">
            <!-- Estatísticas -->
            <div class="stats-grid" id="statsGrid" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="totalSolicitacoes">0</div>
                    <div class="stat-label">Solicitações</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalCotacoes">0</div>
                    <div class="stat-label">Cotações</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalPedidos">0</div>
                    <div class="stat-label">Pedidos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="problemasEncontrados">0</div>
                    <div class="stat-label">Problemas</div>
                </div>
            </div>

            <!-- Ações Principais -->
            <div class="action-grid">
                <div class="action-card">
                    <h3>🔍 Diagnóstico Completo</h3>
                    <p>Analisa todo o fluxo de compras e identifica problemas de sincronização entre solicitações, cotações e pedidos.</p>
                    <button class="btn" onclick="executarDiagnosticoCompleto()">
                        Executar Diagnóstico
                    </button>
                </div>

                <div class="action-card">
                    <h3>🔗 Corrigir Vínculos</h3>
                    <p>Reconecta solicitações órfãs com suas cotações correspondentes e corrige referências quebradas.</p>
                    <button class="btn btn-warning" onclick="corrigirVinculos()">
                        Corrigir Vínculos
                    </button>
                </div>

                <div class="action-card">
                    <h3>📊 Atualizar Status</h3>
                    <p>Sincroniza os status entre módulos e corrige inconsistências de estado no fluxo de compras.</p>
                    <button class="btn btn-success" onclick="atualizarStatus()">
                        Atualizar Status
                    </button>
                </div>

                <div class="action-card">
                    <h3>🗑️ Limpar Órfãos</h3>
                    <p>Remove registros órfãos que não possuem vínculos válidos e estão causando inconsistências.</p>
                    <button class="btn btn-danger" onclick="limparOrfaos()">
                        Limpar Órfãos
                    </button>
                </div>

                <div class="action-card">
                    <h3>🔄 Recriar Cotações</h3>
                    <p>Cria cotações automaticamente para solicitações aprovadas que não possuem cotação.</p>
                    <button class="btn" onclick="recriarCotacoes()">
                        Recriar Cotações
                    </button>
                </div>

                <div class="action-card">
                    <h3>📋 Relatório Detalhado</h3>
                    <p>Gera um relatório completo com todos os problemas encontrados e ações recomendadas.</p>
                    <button class="btn" onclick="gerarRelatorio()">
                        Gerar Relatório
                    </button>
                </div>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p id="loadingText">Processando...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <!-- Resultados -->
            <div class="results-section" id="resultsSection" style="display: none;">
                <h3>📋 Resultados do Diagnóstico</h3>
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        // ===================================================================
        // CORREÇÃO SINCRONIZAÇÃO COMPRAS - IMPORTAÇÃO CENTRALIZADA DO FIREBASE
        // ===================================================================
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            doc, 
            updateDoc, 
            addDoc, 
            deleteDoc,
            query, 
            where, 
            orderBy,
            Timestamp,
            runTransaction,
            writeBatch
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let solicitacoes = [];
        let cotacoes = [];
        let pedidos = [];
        let problemas = [];
        let currentUser = { nome: 'Sistema de Correção' };

        // ===================================================================
        // FUNÇÕES PRINCIPAIS
        // ===================================================================

        window.executarDiagnosticoCompleto = async function() {
            try {
                showLoading('Iniciando diagnóstico completo...');
                updateProgress(10);

                // Carregar dados
                await carregarDados();
                updateProgress(30);

                // Analisar problemas
                await analisarProblemas();
                updateProgress(60);

                // Gerar relatório
                await gerarRelatorioProblemas();
                updateProgress(100);

                hideLoading();
                showResults();
                showStats();

            } catch (error) {
                console.error('Erro no diagnóstico:', error);
                hideLoading();
                showAlert('Erro no diagnóstico: ' + error.message, 'danger');
            }
        };

        async function carregarDados() {
            showLoading('Carregando dados do Firebase...');

            const [solicitacoesSnap, cotacoesSnap, pedidosSnap] = await Promise.all([
                getDocs(query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"))),
                getDocs(query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"))),
                getDocs(query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc")))
            ]);

            solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            cotacoes = cotacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            console.log('Dados carregados:', {
                solicitacoes: solicitacoes.length,
                cotacoes: cotacoes.length,
                pedidos: pedidos.length
            });
        }

        async function analisarProblemas() {
            showLoading('Analisando problemas de sincronização...');
            problemas = [];

            // 1. Solicitações aprovadas sem cotação
            const solicitacoesSemCotacao = solicitacoes.filter(sol => {
                const statusValidos = ['APROVADA', 'EM_COTACAO', 'COTADO'];
                if (!statusValidos.includes(sol.status)) return false;
                return !cotacoes.some(cot => cot.solicitacaoId === sol.id);
            });

            solicitacoesSemCotacao.forEach(sol => {
                problemas.push({
                    tipo: 'SOLICITACAO_SEM_COTACAO',
                    severidade: 'ALTA',
                    titulo: `Solicitação ${sol.numero || sol.id} sem cotação`,
                    descricao: `Solicitação aprovada não possui cotação vinculada`,
                    dados: sol,
                    acoes: ['recriar_cotacao', 'atualizar_status']
                });
            });

            // 2. Cotações órfãs
            const cotacoesOrfas = cotacoes.filter(cot => {
                if (!cot.solicitacaoId) return true;
                return !solicitacoes.some(sol => sol.id === cot.solicitacaoId);
            });

            cotacoesOrfas.forEach(cot => {
                problemas.push({
                    tipo: 'COTACAO_ORFA',
                    severidade: 'MEDIA',
                    titulo: `Cotação ${cot.numero || cot.id} órfã`,
                    descricao: `Cotação sem solicitação de origem válida`,
                    dados: cot,
                    acoes: ['vincular_solicitacao', 'excluir_cotacao']
                });
            });

            // 3. Pedidos órfãos
            const pedidosOrfaos = pedidos.filter(ped => {
                if (!ped.cotacaoId) return true;
                return !cotacoes.some(cot => cot.id === ped.cotacaoId);
            });

            pedidosOrfaos.forEach(ped => {
                problemas.push({
                    tipo: 'PEDIDO_ORFAO',
                    severidade: 'ALTA',
                    titulo: `Pedido ${ped.numero || ped.id} órfão`,
                    descricao: `Pedido sem cotação de origem válida`,
                    dados: ped,
                    acoes: ['vincular_cotacao', 'excluir_pedido']
                });
            });

            // 4. Status inconsistentes
            solicitacoes.forEach(sol => {
                const cotacaoVinculada = cotacoes.find(cot => cot.solicitacaoId === sol.id);
                if (cotacaoVinculada && sol.status === 'APROVADA') {
                    problemas.push({
                        tipo: 'STATUS_INCONSISTENTE',
                        severidade: 'BAIXA',
                        titulo: `Status inconsistente: ${sol.numero || sol.id}`,
                        descricao: `Solicitação com status APROVADA mas já possui cotação`,
                        dados: { solicitacao: sol, cotacao: cotacaoVinculada },
                        acoes: ['atualizar_status_solicitacao']
                    });
                }
            });

            console.log('Problemas encontrados:', problemas.length);
        }

        // ===================================================================
        // FUNÇÕES DE CORREÇÃO
        // ===================================================================

        window.corrigirVinculos = async function() {
            if (!confirm('Deseja corrigir os vínculos quebrados? Esta ação pode modificar dados.')) {
                return;
            }

            try {
                showLoading('Corrigindo vínculos...');
                let corrigidos = 0;

                // Corrigir cotações órfãs tentando encontrar solicitação correspondente
                const cotacoesOrfas = problemas.filter(p => p.tipo === 'COTACAO_ORFA');
                
                for (const problema of cotacoesOrfas) {
                    const cotacao = problema.dados;
                    
                    // Tentar encontrar solicitação por número ou itens similares
                    const solicitacaoCorrespondente = solicitacoes.find(sol => {
                        if (cotacao.numero && sol.numero && cotacao.numero.includes(sol.numero)) {
                            return true;
                        }
                        // Verificar itens similares
                        if (cotacao.itens && sol.itens) {
                            const itensSimilares = cotacao.itens.some(itemCot => 
                                sol.itens.some(itemSol => 
                                    itemCot.produtoId === itemSol.produtoId ||
                                    itemCot.codigo === itemSol.codigo
                                )
                            );
                            return itensSimilares;
                        }
                        return false;
                    });

                    if (solicitacaoCorrespondente) {
                        await updateDoc(doc(db, "cotacoes", cotacao.id), {
                            solicitacaoId: solicitacaoCorrespondente.id,
                            corrigidoEm: Timestamp.now(),
                            corrigidoPor: currentUser.nome
                        });
                        corrigidos++;
                    }
                }

                showAlert(`${corrigidos} vínculos corrigidos com sucesso!`, 'success');
                await executarDiagnosticoCompleto(); // Reexecutar diagnóstico

            } catch (error) {
                console.error('Erro ao corrigir vínculos:', error);
                showAlert('Erro ao corrigir vínculos: ' + error.message, 'danger');
            } finally {
                hideLoading();
            }
        };

        window.atualizarStatus = async function() {
            if (!confirm('Deseja atualizar os status inconsistentes?')) {
                return;
            }

            try {
                showLoading('Atualizando status...');
                let atualizados = 0;

                const statusInconsistentes = problemas.filter(p => p.tipo === 'STATUS_INCONSISTENTE');
                
                for (const problema of statusInconsistentes) {
                    const { solicitacao, cotacao } = problema.dados;
                    
                    // Atualizar status da solicitação para EM_COTACAO
                    await updateDoc(doc(db, "solicitacoesCompra", solicitacao.id), {
                        status: 'EM_COTACAO',
                        statusAtualizadoEm: Timestamp.now(),
                        statusAtualizadoPor: currentUser.nome
                    });
                    atualizados++;
                }

                showAlert(`${atualizados} status atualizados com sucesso!`, 'success');
                await executarDiagnosticoCompleto();

            } catch (error) {
                console.error('Erro ao atualizar status:', error);
                showAlert('Erro ao atualizar status: ' + error.message, 'danger');
            } finally {
                hideLoading();
            }
        };

        window.recriarCotacoes = async function() {
            const solicitacoesSemCotacao = problemas.filter(p => p.tipo === 'SOLICITACAO_SEM_COTACAO');
            
            if (solicitacoesSemCotacao.length === 0) {
                showAlert('Não há solicitações sem cotação para recriar.', 'info');
                return;
            }

            if (!confirm(`Deseja criar cotações para ${solicitacoesSemCotacao.length} solicitações?`)) {
                return;
            }

            try {
                showLoading('Recriando cotações...');
                let criadas = 0;

                for (const problema of solicitacoesSemCotacao) {
                    const solicitacao = problema.dados;
                    
                    // Gerar número da cotação
                    const numeroCotacao = await gerarNumeroCotacao();
                    
                    // Criar cotação
                    // Transferir datas da solicitação para a cotação
                    let dataLimite = null;
                    if (solicitacao.dataLimiteAprovacao) {
                        dataLimite = solicitacao.dataLimiteAprovacao;
                    } else if (solicitacao.dataNecessidade) {
                        dataLimite = solicitacao.dataNecessidade;
                    }

                    const cotacaoData = {
                        numero: numeroCotacao,
                        solicitacaoId: solicitacao.id,
                        dataLimite: dataLimite,
                        itens: solicitacao.itens || [],
                        status: 'ABERTA',
                        dataCriacao: Timestamp.now(),
                        criadoPor: currentUser.nome,
                        observacoes: 'Cotação criada automaticamente pelo sistema de correção'
                    };

                    await addDoc(collection(db, "cotacoes"), cotacaoData);
                    
                    // Atualizar status da solicitação
                    await updateDoc(doc(db, "solicitacoesCompra", solicitacao.id), {
                        status: 'EM_COTACAO',
                        statusAtualizadoEm: Timestamp.now()
                    });
                    
                    criadas++;
                }

                showAlert(`${criadas} cotações criadas com sucesso!`, 'success');
                await executarDiagnosticoCompleto();

            } catch (error) {
                console.error('Erro ao recriar cotações:', error);
                showAlert('Erro ao recriar cotações: ' + error.message, 'danger');
            } finally {
                hideLoading();
            }
        };

        async function gerarNumeroCotacao() {
            const cotacoesCount = cotacoes.length + 1;
            return `CT${cotacoesCount.toString().padStart(6, '0')}`;
        }

        // ===================================================================
        // FUNÇÕES DE INTERFACE
        // ===================================================================

        function showLoading(text) {
            document.getElementById('loading').classList.add('show');
            document.getElementById('loadingText').textContent = text;
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function showStats() {
            document.getElementById('totalSolicitacoes').textContent = solicitacoes.length;
            document.getElementById('totalCotacoes').textContent = cotacoes.length;
            document.getElementById('totalPedidos').textContent = pedidos.length;
            document.getElementById('problemasEncontrados').textContent = problemas.length;
            document.getElementById('statsGrid').style.display = 'grid';
        }

        function showResults() {
            document.getElementById('resultsSection').style.display = 'block';
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.querySelector('.main-content').insertBefore(alertDiv, document.querySelector('.action-grid'));
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        async function gerarRelatorioProblemas() {
            const resultsContent = document.getElementById('resultsContent');
            
            if (problemas.length === 0) {
                resultsContent.innerHTML = `
                    <div class="alert alert-success">
                        <strong>🎉 Nenhum problema encontrado!</strong><br>
                        O fluxo de compras está sincronizado corretamente.
                    </div>
                `;
                return;
            }

            let html = '';
            
            problemas.forEach(problema => {
                const severidadeClass = problema.severidade === 'ALTA' ? '' : 
                                      problema.severidade === 'MEDIA' ? 'warning' : 'success';
                
                html += `
                    <div class="problem-item ${severidadeClass}">
                        <h4>${problema.titulo}</h4>
                        <p><strong>Severidade:</strong> ${problema.severidade}</p>
                        <p>${problema.descricao}</p>
                        <div class="problem-actions">
                            ${problema.acoes.map(acao => 
                                `<button class="btn" onclick="executarAcao('${acao}', '${problema.dados.id}')">${formatarAcao(acao)}</button>`
                            ).join('')}
                        </div>
                    </div>
                `;
            });

            resultsContent.innerHTML = html;
        }

        function formatarAcao(acao) {
            const acoes = {
                'recriar_cotacao': 'Recriar Cotação',
                'atualizar_status': 'Atualizar Status',
                'vincular_solicitacao': 'Vincular Solicitação',
                'excluir_cotacao': 'Excluir Cotação',
                'vincular_cotacao': 'Vincular Cotação',
                'excluir_pedido': 'Excluir Pedido',
                'atualizar_status_solicitacao': 'Atualizar Status'
            };
            return acoes[acao] || acao;
        }

        window.executarAcao = function(acao, id) {
            console.log('Executando ação:', acao, 'para ID:', id);
            showAlert('Funcionalidade em desenvolvimento: ' + formatarAcao(acao), 'info');
        };

        window.limparOrfaos = async function() {
            const orfaos = problemas.filter(p =>
                p.tipo === 'COTACAO_ORFA' || p.tipo === 'PEDIDO_ORFAO'
            );

            if (orfaos.length === 0) {
                showAlert('Não há registros órfãos para limpar.', 'info');
                return;
            }

            if (!confirm(`ATENÇÃO: Esta ação irá excluir ${orfaos.length} registros órfãos permanentemente. Continuar?`)) {
                return;
            }

            try {
                showLoading('Limpando registros órfãos...');
                let excluidos = 0;

                for (const problema of orfaos) {
                    const collection_name = problema.tipo === 'COTACAO_ORFA' ? 'cotacoes' : 'pedidosCompra';
                    await deleteDoc(doc(db, collection_name, problema.dados.id));
                    excluidos++;
                }

                showAlert(`${excluidos} registros órfãos excluídos com sucesso!`, 'success');
                await executarDiagnosticoCompleto();

            } catch (error) {
                console.error('Erro ao limpar órfãos:', error);
                showAlert('Erro ao limpar órfãos: ' + error.message, 'danger');
            } finally {
                hideLoading();
            }
        };

        window.gerarRelatorio = function() {
            const relatorio = {
                dataGeracao: new Date().toLocaleString('pt-BR'),
                totalSolicitacoes: solicitacoes.length,
                totalCotacoes: cotacoes.length,
                totalPedidos: pedidos.length,
                problemasEncontrados: problemas.length,
                problemasPorTipo: {}
            };

            // Agrupar problemas por tipo
            problemas.forEach(p => {
                if (!relatorio.problemasPorTipo[p.tipo]) {
                    relatorio.problemasPorTipo[p.tipo] = 0;
                }
                relatorio.problemasPorTipo[p.tipo]++;
            });

            // Gerar JSON para download
            const dataStr = JSON.stringify(relatorio, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `relatorio_sincronizacao_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('Relatório gerado e baixado com sucesso!', 'success');
        };

        // Inicialização
        console.log('🔧 Sistema de Correção de Sincronização carregado');
    </script>
</body>
</html>
