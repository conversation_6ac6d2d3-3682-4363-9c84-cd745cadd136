firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T18:02:38.536Z', apps: 1}
main.js:29 Application initialized successfully
movimentacao_armazem.html:797 Uncaught ReferenceError: toggleMovementType is not defined
    at HTMLSelectElement.onchange (movimentacao_armazem.html:797:88)
onchange @ movimentacao_armazem.html:797
Análise do Erro e Solução
O erro Uncaught ReferenceError: toggleMovementType is not defined ocorre porque a função toggleMovementType está sendo chamada no evento onchange do elemento HTML, mas a função não está definida no escopo global.

Problemas Identificados:
A função toggleMovementType está sendo chamada no HTML (onchange="toggleMovementType()"), mas não está definida como uma função global (window.toggleMovementType).

Apesar de haver uma implementação da função no código (linha 2671), ela está dentro de um bloco de script do Firebase que pode não estar sendo executado corretamente.

Soluções:
Opção 1: Definir a função globalmente
Adicione esta linha no início do seu JavaScript para garantir que a função seja acessível globalmente:

javascript
// No início do seu arquivo JavaScript ou antes de qualquer chamada à função
window.toggleMovementType = function() {
    try {
        const movementType = document.getElementById('movementType').value;
        const opSection = document.getElementById('opSection');
        const freeSection = document.getElementById('freeSection');
        const returnSection = document.getElementById('returnSection');
        const opFields = document.getElementById('opFields');
        const freeFields = document.getElementById('freeFields');
        const returnFields = document.getElementById('returnFields');

        // Esconder todas as seções
        opSection.style.display = 'none';
        freeSection.style.display = 'none';
        returnSection.style.display = 'none';
        opFields.style.display = 'none';
        freeFields.style.display = 'none';
        returnFields.style.display = 'none';

        // Mostrar seção apropriada
        if (movementType === 'OP') {
            opSection.style.display = 'block';
            opFields.style.display = 'block';
        } else if (movementType === 'LIVRE') {
            freeSection.style.display = 'block';
            freeFields.style.display = 'block';
        } else if (movementType === 'RETORNO') {
            returnSection.style.display = 'block';
            returnFields.style.display = 'block';
        }
    } catch (error) {
        console.error('Erro em toggleMovementType:', error);
    }
};
Opção 2: Modificar a chamada no HTML
Se preferir manter a função no módulo do Firebase, modifique o HTML para:

html
<select id="movementType" onchange="window.toggleMovementType && window.toggleMovementType()" required>
Opção 3: Usar event listeners no JavaScript
A melhor prática seria remover o onchange do HTML e adicionar o event listener no JavaScript:

javascript
document.addEventListener('DOMContentLoaded', function() {
    const movementTypeSelect = document.getElementById('movementType');
    if (movementTypeSelect) {
        movementTypeSelect.addEventListener('change', function() {
            // Implementação da função aqui
        });
    }
});
Recomendação:
Eu recomendo a Opção 3 (usar event listeners no JavaScript) pois:

Separa melhor o HTML do JavaScript

Evita problemas de escopo

Facilita a manutenção

Permite melhor tratamento de erros

Se precisar de ajuda para implementar qualquer uma dessas soluções, posso fornecer mais detalhes sobre cada abordagem.