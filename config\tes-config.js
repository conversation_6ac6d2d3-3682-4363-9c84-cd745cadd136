/**
 * CONFIGURAÇÃO TES - TIPOS DE ENTRADA E SAÍDA
 * Sistema completo de TES para movimentações de estoque
 */

export const TESConfig = {
  // Configurações de TES por tipo de documento
  tesOptions: {
    'COMPRA': [
      { value: '001', text: '001 - Compra para estoque', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '002', text: '002 - Compra para consumo', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '003', text: '003 - Devolução de venda', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '004', text: '004 - Compra para ativo imobilizado', tipo: 'ENTRADA', atualizaEstoque: false },
      { value: '005', text: '005 - Compra para uso e consumo', tipo: 'ENTRADA', atualizaEstoque: true }
    ],
    'VENDA': [
      { value: '500', text: '500 - Venda de produto', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '501', text: '501 - Remessa para beneficiamento', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '502', text: '502 - Devolução de compra', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '503', text: '503 - Venda de ativo imobilizado', tipo: 'SAIDA', atualizaEstoque: false },
      { value: '504', text: '504 - Remessa para demonstração', tipo: 'SAIDA', atualizaEstoque: false }
    ],
    'PRODUCAO': [
      { value: '200', text: '200 - Entrada por produção', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '201', text: '201 - Consumo de componentes', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '202', text: '202 - Baixa por perda', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '203', text: '203 - Retorno de produção', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '204', text: '204 - Consumo para manutenção', tipo: 'SAIDA', atualizaEstoque: true }
    ],
    'CONSUMO': [
      { value: '400', text: '400 - Requisição para consumo', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '401', text: '401 - Requisição para manutenção', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '402', text: '402 - Requisição para projeto', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '403', text: '403 - Consumo administrativo', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '404', text: '404 - Perda por deterioração', tipo: 'SAIDA', atualizaEstoque: true }
    ],
    'AJUSTE': [
      { value: '900', text: '900 - Ajuste de inventário entrada', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '901', text: '901 - Ajuste de inventário saída', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '902', text: '902 - Ajuste por divergência', tipo: 'AMBOS', atualizaEstoque: true },
      { value: '903', text: '903 - Correção de saldo', tipo: 'AMBOS', atualizaEstoque: true },
      { value: '904', text: '904 - Acerto de inventário', tipo: 'AMBOS', atualizaEstoque: true }
    ],
    'TRANSFERENCIA': [
      { value: '100', text: '100 - Transferência entre armazéns', tipo: 'AMBOS', atualizaEstoque: true },
      { value: '101', text: '101 - Transferência para filial', tipo: 'SAIDA', atualizaEstoque: true },
      { value: '102', text: '102 - Recebimento de filial', tipo: 'ENTRADA', atualizaEstoque: true },
      { value: '103', text: '103 - Transferência para terceiros', tipo: 'SAIDA', atualizaEstoque: false },
      { value: '104', text: '104 - Retorno de terceiros', tipo: 'ENTRADA', atualizaEstoque: true }
    ]
  },

  // Validações por TES
  validations: {
    '001': { requiresNF: true, requiresSupplier: true, allowsNegativeStock: false },
    '002': { requiresNF: true, requiresSupplier: true, allowsNegativeStock: false },
    '500': { requiresNF: true, requiresCustomer: true, allowsNegativeStock: false },
    '501': { requiresNF: true, requiresCustomer: true, allowsNegativeStock: false },
    '200': { requiresOP: true, requiresFormula: true, allowsNegativeStock: false },
    '201': { requiresOP: true, requiresFormula: true, allowsNegativeStock: false },
    '900': { requiresJustification: true, requiresApproval: true, allowsNegativeStock: true },
    '901': { requiresJustification: true, requiresApproval: true, allowsNegativeStock: false }
  },

  // Configurações fiscais por TES
  fiscalConfig: {
    '001': { cfop: '1102', icms: 18, ipi: 0, pis: 1.65, cofins: 7.6 },
    '002': { cfop: '1556', icms: 18, ipi: 0, pis: 1.65, cofins: 7.6 },
    '500': { cfop: '5102', icms: 18, ipi: 0, pis: 1.65, cofins: 7.6 },
    '501': { cfop: '5124', icms: 18, ipi: 0, pis: 1.65, cofins: 7.6 }
  },

  /**
   * Obter opções de TES por tipo de documento
   */
  getTESOptions(documentType) {
    return this.tesOptions[documentType] || [];
  },

  /**
   * Obter configuração de uma TES específica
   */
  getTESConfig(tesCode) {
    for (const [docType, tesList] of Object.entries(this.tesOptions)) {
      const tes = tesList.find(t => t.value === tesCode);
      if (tes) {
        return {
          ...tes,
          validation: this.validations[tesCode] || {},
          fiscal: this.fiscalConfig[tesCode] || {}
        };
      }
    }
    return null;
  },

  /**
   * Validar TES para movimentação
   */
  validateTES(tesCode, movementData) {
    const tesConfig = this.getTESConfig(tesCode);
    if (!tesConfig) {
      return { valid: false, errors: ['TES não encontrada'] };
    }

    const errors = [];
    const validation = tesConfig.validation;

    // Validar nota fiscal
    if (validation.requiresNF && !movementData.numeroDocumento) {
      errors.push('Número da nota fiscal é obrigatório para esta TES');
    }

    // Validar fornecedor
    if (validation.requiresSupplier && !movementData.fornecedorId) {
      errors.push('Fornecedor é obrigatório para esta TES');
    }

    // Validar cliente
    if (validation.requiresCustomer && !movementData.clienteId) {
      errors.push('Cliente é obrigatório para esta TES');
    }

    // Validar ordem de produção
    if (validation.requiresOP && !movementData.ordemProducaoId) {
      errors.push('Ordem de produção é obrigatória para esta TES');
    }

    // Validar justificativa
    if (validation.requiresJustification && !movementData.observacoes) {
      errors.push('Justificativa é obrigatória para esta TES');
    }

    // Validar saldo negativo
    if (!validation.allowsNegativeStock && movementData.saldoFuturo < 0) {
      errors.push('Esta TES não permite saldo negativo');
    }

    return {
      valid: errors.length === 0,
      errors,
      config: tesConfig
    };
  },

  /**
   * Determinar tipo de movimentação baseado na TES
   */
  getMovementType(tesCode, documentType) {
    const tesConfig = this.getTESConfig(tesCode);
    if (!tesConfig) return null;

    // Se a TES define o tipo explicitamente
    if (tesConfig.tipo !== 'AMBOS') {
      return tesConfig.tipo;
    }

    // Para TES que podem ser ambos, usar o tipo do documento
    const entradaTypes = ['COMPRA', 'PRODUCAO'];
    const saidaTypes = ['VENDA', 'CONSUMO'];

    if (entradaTypes.includes(documentType)) {
      return 'ENTRADA';
    } else if (saidaTypes.includes(documentType)) {
      return 'SAIDA';
    }

    return null;
  },

  /**
   * Calcular impostos baseado na TES
   */
  calculateTaxes(tesCode, baseValue) {
    const fiscal = this.fiscalConfig[tesCode];
    if (!fiscal) return { total: 0, details: {} };

    const icms = (baseValue * fiscal.icms) / 100;
    const ipi = (baseValue * fiscal.ipi) / 100;
    const pis = (baseValue * fiscal.pis) / 100;
    const cofins = (baseValue * fiscal.cofins) / 100;

    return {
      total: icms + ipi + pis + cofins,
      details: {
        icms: { rate: fiscal.icms, value: icms },
        ipi: { rate: fiscal.ipi, value: ipi },
        pis: { rate: fiscal.pis, value: pis },
        cofins: { rate: fiscal.cofins, value: cofins },
        cfop: fiscal.cfop
      }
    };
  }
};

// Exportar para uso global
if (typeof window !== 'undefined') {
  window.TESConfig = TESConfig;
}