<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Relatório de Integração MRP e Compras</title>
    <!-- Adicionando jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
      @media print {
        body {
          margin: 0;
          padding: 0;
        }
        .no-print {
          display: none;
        }
        .report-page {
          page-break-after: always;
        }
      }

      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #333;
      }

      .logo {
        max-width: 150px;
        height: auto;
      }

      .filters {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
      }

      .filter-row {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
      }

      .filter-group {
        flex: 1;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }

      select,
      input[type='date'],
      input[type='checkbox'] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      button {
        padding: 10px 20px;
        background-color: #4caf50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }

      button:hover {
        background-color: #45a049;
      }
      .back-button {
        background-color: #6c757d;
      }
      .pdf-button {
        background-color: #007bff;
      }

      .section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .section-title {
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #eee;
      }

      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      .data-table th,
      .data-table td {
        padding: 10px;
        border: 1px solid #ddd;
        text-align: left;
      }

      .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
      }

      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
      }

      .status-pendente {
        background-color: #ffc107;
        color: #000;
      }
      .status-em-producao {
        background-color: #17a2b8;
        color: #fff;
      }
      .status-concluida {
        background-color: #28a745;
        color: #fff;
      }
      .status-aprovada {
        background-color: #28a745;
        color: #fff;
      }
      .status-rejeitada {
        background-color: #dc3545;
        color: #fff;
      }

      .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .summary-card {
        padding: 20px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .summary-card h3 {
        margin: 0 0 10px 0;
        color: #333;
      }

      .summary-card .value {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img
          src="https://www.naliteck.com.br/img/logo.png"
          alt="Logo"
          class="logo"
        />
        <div class="report-info">
          <div>Data: <span id="reportDate"></span></div>
          <div>Hora: <span id="reportTime"></span></div>
        </div>
      </div>

      <div class="no-print">
        <div class="filters">
          <div class="filter-row">
            <div class="filter-group">
              <label>Período:</label>
              <input type="date" id="startDate" />
            </div>
            <div class="filter-group">
              <label>Até:</label>
              <input type="date" id="endDate" />
            </div>
            <div class="filter-group">
              <label>Status:</label>
              <select id="statusFilter" onchange="generateReport()">
                <option value="">Todos Ativos</option>
                <option value="Pendente">Pendente</option>
                <option value="Em Produção">Em Produção</option>
                <option value="Concluída">Concluída</option>
              </select>
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-group">
              <label>Seções do Relatório em PDF:</label>
              <div class="checkbox-group">
                <label
                  ><input type="checkbox" id="includeSummary" checked /> Resumo
                  do Período</label
                >
                <label
                  ><input type="checkbox" id="includeOrders" checked /> Ordens
                  de Produção</label
                >
                <label
                  ><input type="checkbox" id="includeRequests" checked />
                  Solicitações de Compra</label
                >
                <label
                  ><input type="checkbox" id="includeMaterials" checked />
                  Materiais Críticos</label
                >
              </div>
            </div>
          </div>
        </div>
        <button onclick="generateReport()">Gerar Relatório</button>
        <button onclick="window.print()">Imprimir</button>
        <button onclick="generatePDF()" class="pdf-button">Gerar PDF</button>
        <button onclick="window.location.href='index.html'" class="back-button">
          Voltar
        </button>
      </div>

      <div id="reportContent">
        <!-- O conteúdo do relatório será gerado aqui -->
      </div>
    </div>

    <script type="module">
      import { db } from './firebase-config.js';
      import {
        collection,
        onSnapshot,
        Timestamp,
      } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

      const { jsPDF } = window.jspdf;
      let produtos = [];
      let ordensProducao = [];
      let solicitacoes = [];
      let estoques = [];

      window.onload = function () {
        setupRealTimeListeners();
        updateDateTime();
        setDefaultDates();
      };

      function setupRealTimeListeners() {
        try {
          onSnapshot(collection(db, 'produtos'), (snapshot) => {
            produtos = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));
            generateReport();
          });

          onSnapshot(collection(db, 'ordensProducao'), (snapshot) => {
            ordensProducao = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));
            generateReport();
          });

          onSnapshot(collection(db, 'solicitacoesCompra'), (snapshot) => {
            solicitacoes = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));
            generateReport();
          });

          onSnapshot(collection(db, 'estoques'), (snapshot) => {
            estoques = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));
            generateReport();
          });
        } catch (error) {
          console.error('Erro ao configurar listeners:', error);
          alert('Erro ao carregar dados iniciais.');
        }
      }

      function updateDateTime() {
        document.getElementById('reportDate').textContent =
          new Date().toLocaleDateString();
        document.getElementById('reportTime').textContent =
          new Date().toLocaleTimeString();
      }

      function setDefaultDates() {
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        document.getElementById('startDate').value = thirtyDaysAgo
          .toISOString()
          .split('T')[0];
        document.getElementById('endDate').value = today
          .toISOString()
          .split('T')[0];
      }

      window.generateReport = function () {
        const startDate = new Date(document.getElementById('startDate').value);
        const endDate = new Date(document.getElementById('endDate').value);
        endDate.setHours(23, 59, 59);
        const statusFilter = document.getElementById('statusFilter').value;

        let filteredOrdens = filterOrders(startDate, endDate, statusFilter);
        let filteredSolicitacoes = filterRequests(startDate, endDate);

        const reportContent = document.getElementById('reportContent');
        reportContent.innerHTML = '';

        if (document.getElementById('includeSummary').checked) {
          reportContent.appendChild(
            createSummarySection(filteredOrdens, filteredSolicitacoes)
          );
        }
        if (document.getElementById('includeOrders').checked) {
          reportContent.appendChild(createOrdersSection(filteredOrdens));
        }
        if (document.getElementById('includeRequests').checked) {
          reportContent.appendChild(
            createRequestsSection(filteredSolicitacoes)
          );
        }
        if (document.getElementById('includeMaterials').checked) {
          reportContent.appendChild(createMaterialsSection(filteredOrdens));
        }
      };

      function filterOrders(startDate, endDate, statusFilter) {
        let filteredOrdens = ordensProducao;
        if (startDate && endDate) {
          filteredOrdens = filteredOrdens.filter((ordem) => {
            const orderDate = new Date(ordem.dataCriacao.seconds * 1000);
            return orderDate >= startDate && orderDate <= endDate;
          });
        }
        if (statusFilter) {
          filteredOrdens = filteredOrdens.filter(
            (ordem) => ordem.status === statusFilter
          );
        } else {
          filteredOrdens = filteredOrdens.filter(
            (ordem) => ordem.status !== 'Concluída'
          );
        }
        return filteredOrdens;
      }

      function filterRequests(startDate, endDate) {
        let filteredSolicitacoes = solicitacoes;
        if (startDate && endDate) {
          filteredSolicitacoes = filteredSolicitacoes.filter((solicitacao) => {
            const solicitacaoDate = new Date(
              solicitacao.dataCriacao.seconds * 1000
            );
            return solicitacaoDate >= startDate && solicitacaoDate <= endDate;
          });
        }
        return filteredSolicitacoes;
      }

      function createSummarySection(ordens, solicitacoes) {
        const section = document.createElement('div');
        section.className = 'section';
        section.innerHTML = `
        <h2 class="section-title">Resumo do Período</h2>
        <div class="summary-cards">
          <div class="summary-card">
            <h3>Ordens de Produção</h3>
            <div class="value">${ordens.length}</div>
          </div>
          <div class="summary-card">
            <h3>Solicitações de Compra</h3>
            <div class="value">${solicitacoes.length}</div>
          </div>
          <div class="summary-card">
            <h3>Materiais em Falta</h3>
            <div class="value">${countMaterialsInNeed(ordens)}</div>
          </div>
        </div>
      `;
        return section;
      }

      function createOrdersSection(ordens) {
        const section = document.createElement('div');
        section.className = 'section';
        section.innerHTML = `
        <h2 class="section-title">Ordens de Produção</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Número</th>
              <th>Produto</th>
              <th>Quantidade</th>
              <th>Data Criação</th>
              <th>Status</th>
              <th>Materiais em Falta</th>
            </tr>
          </thead>
          <tbody>${generateOrdersTableContent(ordens)}</tbody>
        </table>
      `;
        return section;
      }

      function createRequestsSection(solicitacoes) {
        const section = document.createElement('div');
        section.className = 'section';
        section.innerHTML = `
        <h2 class="section-title">Solicitações de Compra</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Número</th>
              <th>Data</th>
              <th>Solicitante</th>
              <th>Itens</th>
              <th>Status</th>
              <th>Ordem Relacionada</th>
            </tr>
          </thead>
          <tbody>${generateRequestsTableContent(solicitacoes)}</tbody>
        </table>
      `;
        return section;
      }

      function createMaterialsSection(ordens) {
        const section = document.createElement('div');
        section.className = 'section';
        section.innerHTML = `
        <h2 class="section-title">Materiais Críticos</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Estoque Atual</th>
              <th>Necessidade Total</th>
              <th>Déficit</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>${generateMaterialsTableContent(ordens)}</tbody>
        </table>
      `;
        return section;
      }

      function countMaterialsInNeed(ordens) {
        const materiaisNecessarios = new Set();
        ordens.forEach((ordem) => {
          if (ordem.materiaisNecessarios) {
            ordem.materiaisNecessarios.forEach((material) => {
              const estoque = estoques.find(
                (e) => e.produtoId === material.produtoId
              ) || { saldo: 0 };
              if (material.quantidade > estoque.saldo) {
                materiaisNecessarios.add(material.produtoId);
              }
            });
          }
        });
        return materiaisNecessarios.size;
      }

      function generateOrdersTableContent(ordens) {
        return ordens
          .map((ordem) => {
            const produto = produtos.find((p) => p.id === ordem.produtoId);
            const materiaisEmFalta = ordem.materiaisNecessarios
              ? ordem.materiaisNecessarios.filter((m) => {
                  const estoque = estoques.find(
                    (e) => e.produtoId === m.produtoId
                  ) || { saldo: 0 };
                  return m.quantidade > estoque.saldo;
                }).length
              : 0;
            return `
          <tr>
            <td>${ordem.numero}</td>
            <td>${
              produto ? `${produto.codigo} - ${produto.descricao}` : 'N/A'
            }</td>
            <td>${ordem.quantidade} ${produto ? produto.unidade : ''}</td>
            <td>${new Date(
              ordem.dataCriacao.seconds * 1000
            ).toLocaleDateString()}</td>
            <td><span class="status-badge status-${ordem.status.toLowerCase()}">${
              ordem.status
            }</span></td>
            <td>${materiaisEmFalta} item(s)</td>
          </tr>
        `;
          })
          .join('');
      }

      function generateRequestsTableContent(solicitacoes) {
        return solicitacoes
          .map((solicitacao) => {
            return `
          <tr>
            <td>${solicitacao.numero}</td>
            <td>${new Date(
              solicitacao.dataCriacao.seconds * 1000
            ).toLocaleDateString()}</td>
            <td>${solicitacao.solicitante}</td>
            <td>${solicitacao.itens.length} itens</td>
            <td><span class="status-badge status-${solicitacao.status.toLowerCase()}">${
              solicitacao.status
            }</span></td>
            <td>${solicitacao.ordemProducao || 'N/A'}</td>
          </tr>
        `;
          })
          .join('');
      }

      function generateMaterialsTableContent(ordens) {
        const materiaisNecessarios = new Map();
        ordens.forEach((ordem) => {
          if (!ordem.materiaisNecessarios) return;
          ordem.materiaisNecessarios.forEach((material) => {
            const estoque = estoques.find(
              (e) => e.produtoId === material.produtoId
            ) || { saldo: 0 };
            const atual = materiaisNecessarios.get(material.produtoId) || {
              quantidade: 0,
              saldoEstoque: estoque.saldo,
              necessidade: 0,
            };
            atual.quantidade += material.quantidade;
            atual.necessidade += Math.max(
              0,
              material.quantidade - estoque.saldo
            );
            materiaisNecessarios.set(material.produtoId, atual);
          });
        });

        return Array.from(materiaisNecessarios.entries())
          .map(([produtoId, dados]) => {
            const produto = produtos.find((p) => p.id === produtoId);
            if (!produto) return '';
            const deficit = dados.necessidade;
            let statusClass = 'pendente';
            if (deficit <= 0) statusClass = 'concluida';
            else if (deficit > dados.quantidade * 0.5)
              statusClass = 'rejeitada';
            return `
          <tr>
            <td>${produto.codigo}</td>
            <td>${produto.descricao}</td>
            <td>${dados.saldoEstoque} ${produto.unidade}</td>
            <td>${dados.quantidade} ${produto.unidade}</td>
            <td>${deficit} ${produto.unidade}</td>
            <td><span class="status-badge status-${statusClass}">
              ${
                deficit <= 0
                  ? 'OK'
                  : deficit > dados.quantidade * 0.5
                  ? 'Crítico'
                  : 'Atenção'
              }
            </span></td>
          </tr>
        `;
          })
          .join('');
      }

      window.generatePDF = function () {
        const doc = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4',
        });
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const statusFilter = document.getElementById('statusFilter').value;
        let filteredOrdens = filterOrders(
          new Date(startDate),
          new Date(endDate + ' 23:59:59'),
          statusFilter
        );
        let filteredSolicitacoes = filterRequests(
          new Date(startDate),
          new Date(endDate + ' 23:59:59')
        );

        let y = 20;
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 10;

        // Cabeçalho
        doc.setFontSize(16);
        doc.text('Relatório de Integração MRP e Compras', pageWidth / 2, y, {
          align: 'center',
        });
        y += 10;
        doc.setFontSize(10);
        doc.text(
          `Data: ${new Date().toLocaleDateString()} | Hora: ${new Date().toLocaleTimeString()}`,
          pageWidth / 2,
          y,
          { align: 'center' }
        );
        doc.text(`Período: ${startDate} a ${endDate}`, pageWidth / 2, y + 5, {
          align: 'center',
        });
        y += 15;

        // Resumo do Período
        if (document.getElementById('includeSummary').checked) {
          doc.setFontSize(14);
          doc.text('Resumo do Período', margin, y);
          y += 10;
          doc.setFontSize(10);
          doc.text(`Ordens de Produção: ${filteredOrdens.length}`, margin, y);
          doc.text(
            `Solicitações de Compra: ${filteredSolicitacoes.length}`,
            margin + 60,
            y
          );
          doc.text(
            `Materiais em Falta: ${countMaterialsInNeed(filteredOrdens)}`,
            margin + 120,
            y
          );
          y += 20;
        }

        // Ordens de Produção
        if (document.getElementById('includeOrders').checked) {
          if (y > 250) {
            doc.addPage();
            y = 20;
          }
          doc.setFontSize(14);
          doc.text('Ordens de Produção', margin, y);
          y += 10;
          const ordersData = filteredOrdens.map((ordem) => {
            const produto = produtos.find((p) => p.id === ordem.produtoId);
            const materiaisEmFalta = ordem.materiaisNecessarios
              ? ordem.materiaisNecessarios.filter(
                  (m) =>
                    (
                      estoques.find((e) => e.produtoId === m.produtoId) || {
                        saldo: 0,
                      }
                    ).saldo < m.quantidade
                ).length
              : 0;
            return [
              ordem.numero,
              produto ? `${produto.codigo} - ${produto.descricao}` : 'N/A',
              `${ordem.quantidade} ${produto ? produto.unidade : ''}`,
              new Date(ordem.dataCriacao.seconds * 1000).toLocaleDateString(),
              ordem.status,
              `${materiaisEmFalta} item(s)`,
            ];
          });
          doc.autoTable({
            startY: y,
            head: [
              [
                'Número',
                'Produto',
                'Quantidade',
                'Data Criação',
                'Status',
                'Materiais em Falta',
              ],
            ],
            body: ordersData,
            theme: 'grid',
            styles: { fontSize: 8, cellPadding: 2 },
            columnStyles: { 1: { cellWidth: 60 } },
          });
          y = doc.lastAutoTable.finalY + 10;
        }

        // Solicitações de Compra
        if (document.getElementById('includeRequests').checked) {
          if (y > 250) {
            doc.addPage();
            y = 20;
          }
          doc.setFontSize(14);
          doc.text('Solicitações de Compra', margin, y);
          y += 10;
          const requestsData = filteredSolicitacoes.map((solicitacao) => [
            solicitacao.numero,
            new Date(
              solicitacao.dataCriacao.seconds * 1000
            ).toLocaleDateString(),
            solicitacao.solicitante,
            `${solicitacao.itens.length} itens`,
            solicitacao.status,
            solicitacao.ordemProducao || 'N/A',
          ]);
          doc.autoTable({
            startY: y,
            head: [
              [
                'Número',
                'Data',
                'Solicitante',
                'Itens',
                'Status',
                'Ordem Relacionada',
              ],
            ],
            body: requestsData,
            theme: 'grid',
            styles: { fontSize: 8, cellPadding: 2 },
          });
          y = doc.lastAutoTable.finalY + 10;
        }

        // Materiais Críticos
        if (document.getElementById('includeMaterials').checked) {
          if (y > 250) {
            doc.addPage();
            y = 20;
          }
          doc.setFontSize(14);
          doc.text('Materiais Críticos', margin, y);
          y += 10;
          const materiaisNecessarios = new Map();
          filteredOrdens.forEach((ordem) => {
            if (!ordem.materiaisNecessarios) return;
            ordem.materiaisNecessarios.forEach((material) => {
              const estoque = estoques.find(
                (e) => e.produtoId === material.produtoId
              ) || { saldo: 0 };
              const atual = materiaisNecessarios.get(material.produtoId) || {
                quantidade: 0,
                saldoEstoque: estoque.saldo,
                necessidade: 0,
              };
              atual.quantidade += material.quantidade;
              atual.necessidade += Math.max(
                0,
                material.quantidade - estoque.saldo
              );
              materiaisNecessarios.set(material.produtoId, atual);
            });
          });
          const materialsData = Array.from(materiaisNecessarios.entries())
            .map(([produtoId, dados]) => {
              const produto = produtos.find((p) => p.id === produtoId);
              if (!produto) return [];
              const deficit = dados.necessidade;
              const status =
                deficit <= 0
                  ? 'OK'
                  : deficit > dados.quantidade * 0.5
                  ? 'Crítico'
                  : 'Atenção';
              return [
                produto.codigo,
                produto.descricao,
                `${dados.saldoEstoque} ${produto.unidade}`,
                `${dados.quantidade} ${produto.unidade}`,
                `${deficit} ${produto.unidade}`,
                status,
              ];
            })
            .filter((row) => row.length > 0);
          doc.autoTable({
            startY: y,
            head: [
              [
                'Código',
                'Descrição',
                'Estoque Atual',
                'Necessidade Total',
                'Déficit',
                'Status',
              ],
            ],
            body: materialsData,
            theme: 'grid',
            styles: { fontSize: 8, cellPadding: 2 },
            columnStyles: { 1: { cellWidth: 60 } },
          });
        }

        doc.save(`Relatorio_MRP_Compras_${startDate}_a_${endDate}.pdf`);
      };
    </script>
  </body>
</html>
