<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico de Pedidos de Compra</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script type="module" src="firebase-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .content {
            padding: 30px;
        }

        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .results-section {
            margin-top: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-aprovado { background: #d4edda; color: #155724; }
        .status-enviado { background: #cce5ff; color: #004085; }
        .status-recebido-parcial { background: #fff3cd; color: #856404; }
        .status-recebido { background: #d1ecf1; color: #0c5460; }

        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid;
        }

        .alert-info {
            background: #e7f3ff;
            border-color: #3498db;
            color: #2c3e50;
        }

        .alert-warning {
            background: #fff8e1;
            border-color: #f39c12;
            color: #2c3e50;
        }

        .alert-danger {
            background: #ffeaea;
            border-color: #e74c3c;
            color: #2c3e50;
        }

        .alert-success {
            background: #eafaf1;
            border-color: #27ae60;
            color: #2c3e50;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid;
        }

        .metric-card.primary { border-color: #3498db; }
        .metric-card.success { border-color: #27ae60; }
        .metric-card.warning { border-color: #f39c12; }
        .metric-card.danger { border-color: #e74c3c; }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 14px;
        }

        .comparison-table {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-column {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .comparison-column h4 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .diff-highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #ffc107;
        }

        .error-highlight {
            background: #f8d7da;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #dc3545;
        }

        .success-highlight {
            background: #d4edda;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-search-plus"></i>
                Diagnóstico de Pedidos de Compra
            </h1>
            <div>
                <button class="btn btn-primary" onclick="exportarDados()">
                    <i class="fas fa-download"></i> Exportar Dados
                </button>
            </div>
        </div>

        <div class="content">
            <!-- Seção de Busca -->
            <div class="search-section">
                <h3><i class="fas fa-filter"></i> Filtros de Análise</h3>
                <div class="grid">
                    <div class="form-group">
                        <label for="pedidoSelect">Selecionar Pedido Específico:</label>
                        <select id="pedidoSelect" class="form-control">
                            <option value="">Carregando pedidos...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="statusFilter">Filtrar por Status:</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">Todos os Status</option>
                            <option value="APROVADO">APROVADO</option>
                            <option value="ENVIADO">ENVIADO</option>
                            <option value="RECEBIDO_PARCIAL">RECEBIDO PARCIAL</option>
                            <option value="RECEBIDO">RECEBIDO</option>
                        </select>
                    </div>
                </div>
                <div class="grid">
                    <button class="btn btn-primary" onclick="analisarPedido()">
                        <i class="fas fa-search"></i> Analisar Pedido Selecionado
                    </button>
                    <button class="btn btn-success" onclick="analisarTodos()">
                        <i class="fas fa-chart-line"></i> Análise Geral
                    </button>
                    <button class="btn btn-warning" onclick="verificarInconsistencias()">
                        <i class="fas fa-exclamation-triangle"></i> Verificar Inconsistências
                    </button>
                    <button class="btn btn-danger" onclick="limparResultados()">
                        <i class="fas fa-trash"></i> Limpar Resultados
                    </button>
                </div>
            </div>

            <!-- Métricas Gerais -->
            <div id="metricas" class="grid" style="display: none;">
                <div class="metric-card primary">
                    <div class="metric-value" id="totalPedidos">0</div>
                    <div class="metric-label">Total de Pedidos</div>
                </div>
                <div class="metric-card success">
                    <div class="metric-value" id="pedidosRecebidos">0</div>
                    <div class="metric-label">Pedidos Recebidos</div>
                </div>
                <div class="metric-card warning">
                    <div class="metric-value" id="pedidosParciais">0</div>
                    <div class="metric-label">Recebimentos Parciais</div>
                </div>
                <div class="metric-card danger">
                    <div class="metric-value" id="inconsistencias">0</div>
                    <div class="metric-label">Inconsistências</div>
                </div>
            </div>

            <!-- Seção de Resultados -->
            <div id="resultados" class="results-section" style="display: none;">
                <!-- Tabs -->
                <div class="tabs">
                    <button class="tab active" onclick="mostrarTab('dados-pedido')">
                        <i class="fas fa-file-invoice"></i> Dados do Pedido
                    </button>
                    <button class="tab" onclick="mostrarTab('movimentacoes')">
                        <i class="fas fa-exchange-alt"></i> Movimentações
                    </button>
                    <button class="tab" onclick="mostrarTab('comparacao')">
                        <i class="fas fa-balance-scale"></i> Comparação
                    </button>
                    <button class="tab" onclick="mostrarTab('json-raw')">
                        <i class="fas fa-code"></i> JSON Raw
                    </button>
                </div>

                <!-- Conteúdo das Tabs -->
                <div id="dados-pedido" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-info-circle"></i> Informações do Pedido
                        </div>
                        <div class="card-body" id="infoPedido">
                            <!-- Será preenchido dinamicamente -->
                        </div>
                    </div>
                </div>

                <div id="movimentacoes" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-history"></i> Histórico de Movimentações
                        </div>
                        <div class="card-body" id="historicoMovimentacoes">
                            <!-- Será preenchido dinamicamente -->
                        </div>
                    </div>
                </div>

                <div id="comparacao" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-not-equal"></i> Análise de Inconsistências
                        </div>
                        <div class="card-body" id="analiseInconsistencias">
                            <!-- Será preenchido dinamicamente -->
                        </div>
                    </div>
                </div>

                <div id="json-raw" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-database"></i> Dados Brutos (JSON)
                        </div>
                        <div class="card-body">
                            <div id="jsonViewer" class="json-viewer">
                                <!-- Será preenchido dinamicamente -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Carregando dados...</p>
            </div>
        </div>
    </div>

    <script type="module">
        // Importar Firebase
        import { db, collection, getDocs, doc, updateDoc } from './firebase-config.js';

        // Variáveis globais
        let pedidosCompra = [];
        let movimentacoes = [];
        let recebimentos = [];
        let produtos = [];
        let fornecedores = [];
        let currentAnalysis = null;

        // Inicialização
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔍 Iniciando diagnóstico de pedidos de compra...');

            try {
                await carregarDados();

                // Expor funções globalmente após carregamento
                exposeFunctionsGlobally();

            } catch (error) {
                console.error('❌ Erro na inicialização:', error);
                mostrarAlerta('Erro na inicialização: ' + error.message, 'danger');
            }
        });

        // ===================================================================
        // CARREGAMENTO DE DADOS
        // ===================================================================

        async function carregarDados() {
            mostrarLoading(true);

            try {
                console.log('📥 Carregando dados do Firebase...');

                // Carregar dados em paralelo
                const [
                    pedidosSnap,
                    movimentacoesSnap,
                    recebimentosSnap,
                    produtosSnap,
                    fornecedoresSnap
                ] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "recebimentosMateriais")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                // Processar dados
                pedidosCompra = pedidosSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                movimentacoes = movimentacoesSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                recebimentos = recebimentosSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                produtos = produtosSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                fornecedores = fornecedoresSnap.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                console.log('✅ Dados carregados:', {
                    pedidos: pedidosCompra.length,
                    movimentacoes: movimentacoes.length,
                    recebimentos: recebimentos.length,
                    produtos: produtos.length,
                    fornecedores: fornecedores.length
                });

                // Atualizar interface
                popularSelectPedidos();
                atualizarMetricas();

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                mostrarAlerta('Erro ao carregar dados: ' + error.message, 'danger');
            } finally {
                mostrarLoading(false);
            }
        }

        function popularSelectPedidos() {
            const select = document.getElementById('pedidoSelect');
            select.innerHTML = '<option value="">Selecione um pedido...</option>';

            // Ordenar pedidos por data (mais recentes primeiro)
            const pedidosOrdenados = [...pedidosCompra].sort((a, b) => {
                const dataA = a.dataCriacao?.seconds || 0;
                const dataB = b.dataCriacao?.seconds || 0;
                return dataB - dataA;
            });

            pedidosOrdenados.forEach(pedido => {
                const option = document.createElement('option');
                option.value = pedido.id;

                const numero = pedido.numero || pedido.id.substring(0, 8);
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const nomeForncedor = fornecedor?.nome || 'Fornecedor não encontrado';
                const status = pedido.status || 'SEM STATUS';
                const valor = pedido.valorTotal || 0;

                option.textContent = `${numero} - ${nomeForncedor} - ${status} - R$ ${valor.toFixed(2)}`;
                select.appendChild(option);
            });
        }

        function atualizarMetricas() {
            const total = pedidosCompra.length;
            const recebidos = pedidosCompra.filter(p => p.status === 'RECEBIDO').length;
            const parciais = pedidosCompra.filter(p => p.status === 'RECEBIDO_PARCIAL').length;

            // Calcular inconsistências
            let inconsistencias = 0;
            pedidosCompra.forEach(pedido => {
                if (verificarInconsistenciasPedido(pedido).length > 0) {
                    inconsistencias++;
                }
            });

            // Atualizar métricas com verificação
            const metricas = [
                { id: 'totalPedidos', valor: total },
                { id: 'pedidosRecebidos', valor: recebidos },
                { id: 'pedidosParciais', valor: parciais },
                { id: 'inconsistencias', valor: inconsistencias }
            ];

            metricas.forEach(metrica => {
                const elemento = document.getElementById(metrica.id);
                if (elemento) {
                    elemento.textContent = metrica.valor;
                } else {
                    console.warn(`⚠️ Elemento ${metrica.id} não encontrado`);
                }
            });

            const metricasContainer = document.getElementById('metricas');
            if (metricasContainer) {
                metricasContainer.style.display = 'grid';
            }
        }

        // ===================================================================
        // ANÁLISE DE PEDIDOS
        // ===================================================================

        async function analisarPedido() {
            const pedidoId = document.getElementById('pedidoSelect').value;

            if (!pedidoId) {
                mostrarAlerta('Selecione um pedido para análise', 'warning');
                return;
            }

            mostrarLoading(true);

            try {
                const pedido = pedidosCompra.find(p => p.id === pedidoId);
                if (!pedido) {
                    throw new Error('Pedido não encontrado');
                }

                console.log('🔍 Analisando pedido:', pedido.numero || pedidoId);

                // Buscar movimentações relacionadas
                const movimentacoesPedido = movimentacoes.filter(m =>
                    m.pedidoId === pedidoId ||
                    m.documento?.includes(pedido.numero) ||
                    m.numeroDocumento === pedido.numero
                );

                // Buscar recebimentos relacionados
                const recebimentosPedido = recebimentos.filter(r =>
                    r.pedidoId === pedidoId ||
                    r.numeroPedido === pedido.numero
                );

                // Criar análise completa
                currentAnalysis = {
                    pedido: pedido,
                    movimentacoes: movimentacoesPedido,
                    recebimentos: recebimentosPedido,
                    inconsistencias: verificarInconsistenciasPedido(pedido),
                    timestamp: new Date()
                };

                // Mostrar resultados
                mostrarResultadosAnalise();
                document.getElementById('resultados').style.display = 'block';

            } catch (error) {
                console.error('❌ Erro na análise:', error);
                mostrarAlerta('Erro na análise: ' + error.message, 'danger');
            } finally {
                mostrarLoading(false);
            }
        }

        function verificarInconsistenciasPedido(pedido) {
            const inconsistencias = [];

            // Verificar se tem itens
            if (!pedido.itens || !Array.isArray(pedido.itens) || pedido.itens.length === 0) {
                inconsistencias.push({
                    tipo: 'ESTRUTURA',
                    descricao: 'Pedido sem itens ou estrutura de itens inválida',
                    severidade: 'ALTA'
                });
            } else {
                // Verificar cada item
                pedido.itens.forEach((item, index) => {
                    // Verificar quantidades
                    const qtdPedida = parseFloat(item.quantidade || 0);
                    const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);

                    if (qtdRecebida > qtdPedida) {
                        inconsistencias.push({
                            tipo: 'QUANTIDADE',
                            descricao: `Item ${index + 1}: Quantidade recebida (${qtdRecebida}) maior que pedida (${qtdPedida})`,
                            severidade: 'MÉDIA',
                            item: item
                        });
                    }

                    // Verificar produto
                    if (item.produtoId) {
                        const produto = produtos.find(p => p.id === item.produtoId);
                        if (!produto) {
                            inconsistencias.push({
                                tipo: 'PRODUTO',
                                descricao: `Item ${index + 1}: Produto não encontrado (ID: ${item.produtoId})`,
                                severidade: 'ALTA',
                                item: item
                            });
                        }
                    }
                });

                // Verificar status vs quantidades
                const todosRecebidos = pedido.itens.every(item => {
                    const qtdPedida = parseFloat(item.quantidade || 0);
                    const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                    return qtdRecebida >= qtdPedida;
                });

                const algumRecebido = pedido.itens.some(item => {
                    const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                    return qtdRecebida > 0;
                });

                if (todosRecebidos && pedido.status !== 'RECEBIDO') {
                    inconsistencias.push({
                        tipo: 'STATUS',
                        descricao: `Status deveria ser RECEBIDO (todos os itens foram recebidos) mas está como ${pedido.status}`,
                        severidade: 'ALTA'
                    });
                }

                if (algumRecebido && !todosRecebidos && pedido.status !== 'RECEBIDO_PARCIAL') {
                    inconsistencias.push({
                        tipo: 'STATUS',
                        descricao: `Status deveria ser RECEBIDO_PARCIAL (recebimento parcial) mas está como ${pedido.status}`,
                        severidade: 'MÉDIA'
                    });
                }
            }

            return inconsistencias;
        }

        // ===================================================================
        // EXIBIÇÃO DE RESULTADOS
        // ===================================================================

        function mostrarResultadosAnalise() {
            if (!currentAnalysis) return;

            console.log('📋 Mostrando resultados da análise...');

            // Verificar se elementos das abas existem
            const elementosNecessarios = [
                'infoPedido',
                'historicoMovimentacoes',
                'analiseInconsistencias',
                'jsonViewer'
            ];

            const elementosFaltando = elementosNecessarios.filter(id => !document.getElementById(id));

            if (elementosFaltando.length > 0) {
                console.error('❌ Elementos não encontrados:', elementosFaltando);
                mostrarAlerta('Erro: Elementos da interface não encontrados: ' + elementosFaltando.join(', '), 'danger');
                return;
            }

            // Tab 1: Dados do Pedido
            mostrarDadosPedido();

            // Tab 2: Movimentações
            mostrarMovimentacoes();

            // Tab 3: Comparação/Inconsistências
            mostrarComparacao();

            // Tab 4: JSON Raw
            mostrarJsonRaw();

            console.log('✅ Resultados exibidos com sucesso');
        }

        function mostrarDadosPedido() {
            const pedido = currentAnalysis.pedido;
            const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);

            let html = `
                <div class="alert alert-info">
                    <strong>📋 Pedido:</strong> ${pedido.numero || pedido.id}<br>
                    <strong>🏢 Fornecedor:</strong> ${fornecedor?.nome || 'Não encontrado'}<br>
                    <strong>📊 Status:</strong> <span class="status-badge status-${pedido.status?.toLowerCase().replace('_', '-')}">${pedido.status || 'SEM STATUS'}</span><br>
                    <strong>💰 Valor Total:</strong> R$ ${(pedido.valorTotal || 0).toFixed(2)}<br>
                    <strong>📅 Data Criação:</strong> ${formatarData(pedido.dataCriacao)}<br>
                    <strong>🔢 Total de Itens:</strong> ${pedido.itens?.length || 0}
                </div>
            `;

            if (pedido.itens && pedido.itens.length > 0) {
                html += `
                    <h4>📦 Itens do Pedido</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Qtd Pedida</th>
                                <th>Qtd Recebida</th>
                                <th>Saldo</th>
                                <th>Valor Unit.</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                pedido.itens.forEach((item, index) => {
                    const produto = produtos.find(p => p.id === item.produtoId);
                    const qtdPedida = parseFloat(item.quantidade || 0);
                    const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                    const saldo = qtdPedida - qtdRecebida;
                    const valorUnit = parseFloat(item.valorUnitario || item.preco || 0);

                    let statusItem = 'PENDENTE';
                    let classStatus = 'status-aprovado';

                    if (qtdRecebida >= qtdPedida) {
                        statusItem = 'COMPLETO';
                        classStatus = 'status-recebido';
                    } else if (qtdRecebida > 0) {
                        statusItem = 'PARCIAL';
                        classStatus = 'status-recebido-parcial';
                    }

                    html += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.codigo || produto?.codigo || 'N/A'}</td>
                            <td>${item.descricao || produto?.descricao || 'N/A'}</td>
                            <td>${qtdPedida.toFixed(3)}</td>
                            <td>${qtdRecebida.toFixed(3)}</td>
                            <td>${saldo.toFixed(3)}</td>
                            <td>R$ ${valorUnit.toFixed(2)}</td>
                            <td><span class="status-badge ${classStatus}">${statusItem}</span></td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            }

            const infoPedidoElement = document.getElementById('infoPedido');
            if (infoPedidoElement) {
                infoPedidoElement.innerHTML = html;
            } else {
                console.error('❌ Elemento infoPedido não encontrado');
            }
        }

        function mostrarMovimentacoes() {
            let html = '';

            if (currentAnalysis.movimentacoes.length === 0 && currentAnalysis.recebimentos.length === 0) {
                html = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Nenhuma movimentação encontrada!</strong><br>
                        Isso pode indicar que:
                        <ul>
                            <li>O pedido ainda não foi recebido</li>
                            <li>As movimentações não estão sendo salvas corretamente</li>
                            <li>Há problema na vinculação entre pedido e movimentações</li>
                        </ul>
                    </div>
                `;
            } else {
                // Mostrar recebimentos
                if (currentAnalysis.recebimentos.length > 0) {
                    html += `
                        <h4>📥 Recebimentos Registrados (${currentAnalysis.recebimentos.length})</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Documento</th>
                                    <th>Itens</th>
                                    <th>Valor Total</th>
                                    <th>Usuário</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    currentAnalysis.recebimentos.forEach(recebimento => {
                        html += `
                            <tr>
                                <td>${formatarData(recebimento.dataRecebimento)}</td>
                                <td>${recebimento.numeroDocumento || 'N/A'}</td>
                                <td>${recebimento.itens?.length || 0}</td>
                                <td>R$ ${(recebimento.valorTotal || 0).toFixed(2)}</td>
                                <td>${recebimento.usuario || 'N/A'}</td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;
                }

                // Mostrar movimentações de estoque
                if (currentAnalysis.movimentacoes.length > 0) {
                    html += `
                        <h4>📦 Movimentações de Estoque (${currentAnalysis.movimentacoes.length})</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Tipo</th>
                                    <th>Produto</th>
                                    <th>Quantidade</th>
                                    <th>Armazém</th>
                                    <th>Documento</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    currentAnalysis.movimentacoes.forEach(mov => {
                        const produto = produtos.find(p => p.id === mov.produtoId);
                        html += `
                            <tr>
                                <td>${formatarData(mov.dataMovimentacao)}</td>
                                <td>${mov.tipo || 'N/A'}</td>
                                <td>${produto?.codigo || mov.codigo || 'N/A'}</td>
                                <td>${(mov.quantidade || 0).toFixed(3)}</td>
                                <td>${mov.armazemNome || mov.armazem || 'N/A'}</td>
                                <td>${mov.documento || mov.numeroDocumento || 'N/A'}</td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;
                }
            }

            const historicoElement = document.getElementById('historicoMovimentacoes');
            if (historicoElement) {
                historicoElement.innerHTML = html;
            } else {
                console.error('❌ Elemento historicoMovimentacoes não encontrado');
            }
        }

        function mostrarComparacao() {
            let html = '';

            if (currentAnalysis.inconsistencias.length === 0) {
                html = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>✅ Nenhuma inconsistência encontrada!</strong><br>
                        O pedido está com dados consistentes.
                    </div>
                `;
            } else {
                html = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <strong>⚠️ ${currentAnalysis.inconsistencias.length} inconsistência(s) encontrada(s):</strong>
                    </div>
                `;

                currentAnalysis.inconsistencias.forEach((inc, index) => {
                    const corSeveridade = inc.severidade === 'ALTA' ? 'danger' :
                                         inc.severidade === 'MÉDIA' ? 'warning' : 'info';

                    html += `
                        <div class="alert alert-${corSeveridade}">
                            <strong>${index + 1}. ${inc.tipo}:</strong> ${inc.descricao}<br>
                            <small><strong>Severidade:</strong> ${inc.severidade}</small>
                        </div>
                    `;
                });
            }

            // Análise de diferenças entre pedido e recebimentos
            html += `
                <h4>🔍 Análise Detalhada</h4>
                <div class="comparison-table">
                    <div class="comparison-column">
                        <h4>📋 Dados do Pedido</h4>
                        <pre>${JSON.stringify({
                            id: currentAnalysis.pedido.id,
                            numero: currentAnalysis.pedido.numero,
                            status: currentAnalysis.pedido.status,
                            valorTotal: currentAnalysis.pedido.valorTotal,
                            totalItens: currentAnalysis.pedido.itens?.length || 0,
                            dataCriacao: formatarData(currentAnalysis.pedido.dataCriacao)
                        }, null, 2)}</pre>
                    </div>
                    <div class="comparison-column">
                        <h4>📥 Dados dos Recebimentos</h4>
                        <pre>${JSON.stringify({
                            totalRecebimentos: currentAnalysis.recebimentos.length,
                            totalMovimentacoes: currentAnalysis.movimentacoes.length,
                            valorTotalRecebido: currentAnalysis.recebimentos.reduce((sum, r) => sum + (r.valorTotal || 0), 0),
                            ultimoRecebimento: currentAnalysis.recebimentos.length > 0 ?
                                formatarData(currentAnalysis.recebimentos[currentAnalysis.recebimentos.length - 1].dataRecebimento) : 'Nenhum'
                        }, null, 2)}</pre>
                    </div>
                </div>
            `;

            const analiseElement = document.getElementById('analiseInconsistencias');
            if (analiseElement) {
                analiseElement.innerHTML = html;
            } else {
                console.error('❌ Elemento analiseInconsistencias não encontrado');
            }
        }

        function mostrarJsonRaw() {
            const dados = {
                pedido: currentAnalysis.pedido,
                movimentacoes: currentAnalysis.movimentacoes,
                recebimentos: currentAnalysis.recebimentos,
                inconsistencias: currentAnalysis.inconsistencias,
                analise: {
                    timestamp: currentAnalysis.timestamp,
                    versao: '1.0.0'
                }
            };

            const jsonElement = document.getElementById('jsonViewer');
            if (jsonElement) {
                jsonElement.textContent = JSON.stringify(dados, null, 2);
            } else {
                console.error('❌ Elemento jsonViewer não encontrado');
            }
        }

        // ===================================================================
        // FUNÇÕES DE ANÁLISE GERAL
        // ===================================================================

        async function analisarTodos() {
            mostrarLoading(true);

            try {
                console.log('📊 Iniciando análise geral...');

                const relatorio = {
                    totalPedidos: pedidosCompra.length,
                    pedidosPorStatus: {},
                    inconsistenciasGerais: [],
                    estatisticas: {
                        valorTotalPedidos: 0,
                        valorTotalRecebido: 0,
                        pedidosSemMovimentacao: 0,
                        pedidosComInconsistencia: 0
                    }
                };

                // Analisar cada pedido
                pedidosCompra.forEach(pedido => {
                    // Contar por status
                    const status = pedido.status || 'SEM_STATUS';
                    relatorio.pedidosPorStatus[status] = (relatorio.pedidosPorStatus[status] || 0) + 1;

                    // Somar valores
                    relatorio.estatisticas.valorTotalPedidos += pedido.valorTotal || 0;

                    // Verificar movimentações
                    const temMovimentacao = movimentacoes.some(m =>
                        m.pedidoId === pedido.id ||
                        m.documento?.includes(pedido.numero) ||
                        m.numeroDocumento === pedido.numero
                    );

                    if (!temMovimentacao && (pedido.status === 'RECEBIDO' || pedido.status === 'RECEBIDO_PARCIAL')) {
                        relatorio.estatisticas.pedidosSemMovimentacao++;
                    }

                    // Verificar inconsistências
                    const inconsistencias = verificarInconsistenciasPedido(pedido);
                    if (inconsistencias.length > 0) {
                        relatorio.estatisticas.pedidosComInconsistencia++;
                        relatorio.inconsistenciasGerais.push({
                            pedidoId: pedido.id,
                            numero: pedido.numero,
                            inconsistencias: inconsistencias
                        });
                    }
                });

                // Calcular valor recebido
                recebimentos.forEach(recebimento => {
                    relatorio.estatisticas.valorTotalRecebido += recebimento.valorTotal || 0;
                });

                // Mostrar relatório
                mostrarRelatorioGeral(relatorio);

            } catch (error) {
                console.error('❌ Erro na análise geral:', error);
                mostrarAlerta('Erro na análise geral: ' + error.message, 'danger');
            } finally {
                mostrarLoading(false);
            }
        }

        function mostrarRelatorioGeral(relatorio) {
            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar"></i> Relatório Geral de Análise
                    </div>
                    <div class="card-body">
                        <div class="grid">
                            <div class="metric-card primary">
                                <div class="metric-value">${relatorio.totalPedidos}</div>
                                <div class="metric-label">Total de Pedidos</div>
                            </div>
                            <div class="metric-card success">
                                <div class="metric-value">R$ ${relatorio.estatisticas.valorTotalPedidos.toFixed(2)}</div>
                                <div class="metric-label">Valor Total Pedidos</div>
                            </div>
                            <div class="metric-card warning">
                                <div class="metric-value">R$ ${relatorio.estatisticas.valorTotalRecebido.toFixed(2)}</div>
                                <div class="metric-label">Valor Total Recebido</div>
                            </div>
                            <div class="metric-card danger">
                                <div class="metric-value">${relatorio.estatisticas.pedidosComInconsistencia}</div>
                                <div class="metric-label">Pedidos com Problemas</div>
                            </div>
                        </div>

                        <h4>📊 Distribuição por Status</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Quantidade</th>
                                    <th>Percentual</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            Object.entries(relatorio.pedidosPorStatus).forEach(([status, quantidade]) => {
                const percentual = ((quantidade / relatorio.totalPedidos) * 100).toFixed(1);
                html += `
                    <tr>
                        <td><span class="status-badge status-${status.toLowerCase().replace('_', '-')}">${status}</span></td>
                        <td>${quantidade}</td>
                        <td>${percentual}%</td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
            `;

            if (relatorio.inconsistenciasGerais.length > 0) {
                html += `
                    <h4>⚠️ Pedidos com Inconsistências</h4>
                    <div class="alert alert-warning">
                        <strong>${relatorio.inconsistenciasGerais.length} pedido(s) com problemas encontrado(s)</strong>
                    </div>
                `;

                relatorio.inconsistenciasGerais.forEach(item => {
                    html += `
                        <div class="alert alert-danger">
                            <strong>Pedido ${item.numero || item.pedidoId}:</strong>
                            <ul>
                    `;

                    item.inconsistencias.forEach(inc => {
                        html += `<li><strong>${inc.tipo}:</strong> ${inc.descricao}</li>`;
                    });

                    html += `
                            </ul>
                        </div>
                    `;
                });
            }

            if (relatorio.estatisticas.pedidosSemMovimentacao > 0) {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> ${relatorio.estatisticas.pedidosSemMovimentacao} pedido(s) marcado(s) como recebido(s) mas sem movimentações de estoque registradas.
                    </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            document.getElementById('resultados').innerHTML = html;
            document.getElementById('resultados').style.display = 'block';
        }

        async function verificarInconsistencias() {
            const statusFilter = document.getElementById('statusFilter').value;

            mostrarLoading(true);

            try {
                let pedidosFiltrados = pedidosCompra;

                if (statusFilter) {
                    pedidosFiltrados = pedidosCompra.filter(p => p.status === statusFilter);
                }

                const problemasEncontrados = [];

                pedidosFiltrados.forEach(pedido => {
                    const inconsistencias = verificarInconsistenciasPedido(pedido);
                    if (inconsistencias.length > 0) {
                        problemasEncontrados.push({
                            pedido: pedido,
                            inconsistencias: inconsistencias
                        });
                    }
                });

                mostrarProblemasEncontrados(problemasEncontrados);

            } catch (error) {
                console.error('❌ Erro na verificação:', error);
                mostrarAlerta('Erro na verificação: ' + error.message, 'danger');
            } finally {
                mostrarLoading(false);
            }
        }

        function mostrarProblemasEncontrados(problemas) {
            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bug"></i> Verificação de Inconsistências
                    </div>
                    <div class="card-body">
            `;

            if (problemas.length === 0) {
                html += `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>✅ Nenhuma inconsistência encontrada!</strong><br>
                        Todos os pedidos analisados estão consistentes.
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>⚠️ ${problemas.length} pedido(s) com inconsistências encontrado(s)</strong>
                    </div>
                `;

                problemas.forEach((problema, index) => {
                    const pedido = problema.pedido;
                    const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);

                    html += `
                        <div class="card" style="margin-bottom: 20px;">
                            <div class="card-header">
                                <strong>${index + 1}. Pedido ${pedido.numero || pedido.id}</strong> -
                                ${fornecedor?.nome || 'Fornecedor não encontrado'}
                            </div>
                            <div class="card-body">
                    `;

                    problema.inconsistencias.forEach(inc => {
                        const corSeveridade = inc.severidade === 'ALTA' ? 'danger' :
                                             inc.severidade === 'MÉDIA' ? 'warning' : 'info';

                        html += `
                            <div class="alert alert-${corSeveridade}">
                                <strong>${inc.tipo}:</strong> ${inc.descricao}<br>
                                <small><strong>Severidade:</strong> ${inc.severidade}</small>
                            </div>
                        `;
                    });

                    html += `
                                <button class="btn btn-primary btn-sm" onclick="analisarPedidoEspecifico('${pedido.id}')">
                                    <i class="fas fa-search"></i> Analisar Detalhadamente
                                </button>
                            </div>
                        </div>
                    `;
                });
            }

            html += `
                    </div>
                </div>
            `;

            document.getElementById('resultados').innerHTML = html;
            document.getElementById('resultados').style.display = 'block';
        }

        function analisarPedidoEspecifico(pedidoId) {
            document.getElementById('pedidoSelect').value = pedidoId;
            analisarPedido();
        }

        // ===================================================================
        // FUNÇÕES UTILITÁRIAS
        // ===================================================================

        function mostrarTab(tabId) {
            // Esconder todas as tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Mostrar tab selecionada
            document.getElementById(tabId).classList.add('active');
            event.target.classList.add('active');
        }

        function formatarData(timestamp) {
            if (!timestamp) return 'N/A';

            let data;
            if (timestamp.seconds) {
                data = new Date(timestamp.seconds * 1000);
            } else if (timestamp.toDate) {
                data = timestamp.toDate();
            } else {
                data = new Date(timestamp);
            }

            return data.toLocaleString('pt-BR');
        }

        function mostrarLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function mostrarAlerta(mensagem, tipo = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${tipo}`;
            alertDiv.innerHTML = `
                <i class="fas fa-${tipo === 'danger' ? 'exclamation-circle' :
                                   tipo === 'warning' ? 'exclamation-triangle' :
                                   tipo === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${mensagem}
            `;

            // Inserir no topo do conteúdo
            const content = document.querySelector('.content');
            content.insertBefore(alertDiv, content.firstChild);

            // Remover após 5 segundos
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        function limparResultados() {
            document.getElementById('resultados').style.display = 'none';
            document.getElementById('pedidoSelect').value = '';
            document.getElementById('statusFilter').value = '';
            currentAnalysis = null;

            // Limpar alertas
            document.querySelectorAll('.alert').forEach(alert => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            });

            mostrarAlerta('Resultados limpos com sucesso!', 'success');
        }

        async function exportarDados() {
            if (!currentAnalysis) {
                mostrarAlerta('Nenhuma análise para exportar. Execute uma análise primeiro.', 'warning');
                return;
            }

            try {
                const dados = {
                    timestamp: new Date().toISOString(),
                    versao: '1.0.0',
                    analise: currentAnalysis,
                    resumo: {
                        pedidoAnalisado: currentAnalysis.pedido.numero || currentAnalysis.pedido.id,
                        totalMovimentacoes: currentAnalysis.movimentacoes.length,
                        totalRecebimentos: currentAnalysis.recebimentos.length,
                        totalInconsistencias: currentAnalysis.inconsistencias.length
                    }
                };

                const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `diagnostico_pedido_${currentAnalysis.pedido.numero || currentAnalysis.pedido.id}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                URL.revokeObjectURL(url);

                mostrarAlerta('Dados exportados com sucesso!', 'success');

            } catch (error) {
                console.error('❌ Erro na exportação:', error);
                mostrarAlerta('Erro ao exportar dados: ' + error.message, 'danger');
            }
        }

        // ===================================================================
        // FUNÇÕES GLOBAIS PARA ACESSO VIA HTML E CONSOLE
        // ===================================================================

        function exposeFunctionsGlobally() {
            console.log('🌐 Expondo funções globalmente...');

            // Expor funções principais para uso nos botões HTML
            window.analisarPedido = analisarPedido;
            window.analisarTodos = analisarTodos;
            window.verificarInconsistencias = verificarInconsistencias;
            window.limparResultados = limparResultados;
            window.exportarDados = exportarDados;
            window.mostrarTab = mostrarTab;
            window.analisarPedidoEspecifico = analisarPedidoEspecifico;

            // Expor dados para debug via console
            window.diagnosticoDebug = {
                pedidosCompra,
                movimentacoes,
                recebimentos,
                produtos,
                fornecedores,
                currentAnalysis,
                carregarDados,
                // Funções também
                analisarPedido,
                analisarTodos,
                verificarInconsistencias
            };

            console.log('✅ Funções expostas globalmente');
        }
    </script>
</body>
</html>
