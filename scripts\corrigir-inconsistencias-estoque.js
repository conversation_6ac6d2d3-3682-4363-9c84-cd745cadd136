
/**
 * SCRIPT DE CORREÇÃO AUTOMÁTICA DE INCONSISTÊNCIAS DE ESTOQUE
 * Baseado no relatório de verificação gerado
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    doc, 
    updateDoc, 
    deleteDoc,
    runTransaction,
    query,
    where,
    Timestamp,
    addDoc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

class CorrecaoInconsistenciasService {
    
    /**
     * 🔧 EXECUTAR TODAS AS CORREÇÕES AUTOMATICAMENTE
     */
    static async executarCorrecaoCompleta() {
        console.log('🚀 Iniciando correção automática de inconsistências...');
        
        const resultados = {
            conexoes: await this.corrigirProblemasConexao(),
            saldos: await this.corrigirInconsistenciasSaldo(),
            saldosNegativos: await this.corrigirSaldosNegativos(),
            referenciasPerdidas: await this.corrigirReferenciasPerdidas(),
            timestamp: new Date()
        };

        console.log('✅ Correção completa finalizada:', resultados);
        return resultados;
    }

    /**
     * 🔗 CORRIGIR PROBLEMAS DE CONEXÃO (19 encontrados)
     */
    static async corrigirProblemasConexao() {
        console.log('🔗 Corrigindo problemas de conexão...');
        
        try {
            const [estoquesSnap, produtosSnap, armazensSnap] = await Promise.all([
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "armazens"))
            ]);

            const produtos = new Map();
            const armazens = new Map();

            produtosSnap.docs.forEach(doc => produtos.set(doc.id, doc.data()));
            armazensSnap.docs.forEach(doc => armazens.set(doc.id, doc.data()));

            let corrigidos = 0;
            let removidos = 0;

            for (const estoqueDoc of estoquesSnap.docs) {
                const estoque = estoqueDoc.data();
                let precisaCorrigir = false;
                let precisaRemover = false;

                // Verificar se produto existe
                if (!produtos.has(estoque.produtoId)) {
                    console.log(`❌ Produto órfão encontrado: ${estoque.produtoId}`);
                    precisaRemover = true;
                }

                // Verificar se armazém existe
                if (!armazens.has(estoque.armazemId)) {
                    console.log(`❌ Armazém órfão encontrado: ${estoque.armazemId}`);
                    
                    // Tentar corrigir para armazém padrão
                    const armazemPadrao = Array.from(armazens.keys())[0];
                    if (armazemPadrao) {
                        await updateDoc(estoqueDoc.ref, {
                            armazemId: armazemPadrao,
                            observacoes: `Corrigido automaticamente - armazém original: ${estoque.armazemId}`
                        });
                        precisaCorrigir = true;
                    } else {
                        precisaRemover = true;
                    }
                }

                if (precisaRemover) {
                    await deleteDoc(estoqueDoc.ref);
                    removidos++;
                    console.log(`🗑️ Removido estoque órfão: ${estoqueDoc.id}`);
                } else if (precisaCorrigir) {
                    corrigidos++;
                }
            }

            return { corrigidos, removidos };

        } catch (error) {
            console.error('Erro na correção de conexões:', error);
            return { erro: error.message };
        }
    }

    /**
     * 📊 CORRIGIR INCONSISTÊNCIAS DE SALDO (99 encontradas)
     */
    static async corrigirInconsistenciasSaldo() {
        console.log('📊 Corrigindo inconsistências de saldo...');
        
        try {
            const [estoquesSnap, movimentacoesSnap] = await Promise.all([
                getDocs(collection(db, "estoques")),
                getDocs(collection(db, "movimentacoesEstoque"))
            ]);

            const movimentacoes = movimentacoesSnap.docs.map(doc => ({ 
                id: doc.id, 
                ...doc.data() 
            }));

            let corrigidos = 0;

            for (const estoqueDoc of estoquesSnap.docs) {
                const estoque = estoqueDoc.data();
                
                // Calcular saldo baseado em movimentações
                const movimentacoesEstoque = movimentacoes.filter(m => 
                    m.produtoId === estoque.produtoId && m.armazemId === estoque.armazemId
                );

                const saldoCalculado = movimentacoesEstoque.reduce((total, mov) => {
                    const quantidade = mov.quantidade || 0;
                    return mov.tipo === 'ENTRADA' ? total + quantidade : total - quantidade;
                }, 0);

                const saldoRegistrado = estoque.saldo || 0;
                const diferenca = Math.abs(saldoCalculado - saldoRegistrado);

                if (diferenca > 0.001) {
                    console.log(`🔄 Corrigindo saldo: ${estoque.produtoId} - ${saldoRegistrado} → ${saldoCalculado}`);
                    
                    await updateDoc(estoqueDoc.ref, {
                        saldo: saldoCalculado,
                        saldoAnterior: saldoRegistrado,
                        corrigidoEm: Timestamp.now(),
                        observacoes: `Saldo recalculado automaticamente - diferença: ${diferenca.toFixed(3)}`
                    });

                    // Registrar movimentação de ajuste se necessário
                    if (Math.abs(diferenca) > 0.01) {
                        await addDoc(collection(db, "movimentacoesEstoque"), {
                            produtoId: estoque.produtoId,
                            armazemId: estoque.armazemId,
                            tipo: saldoCalculado > saldoRegistrado ? 'ENTRADA' : 'SAIDA',
                            quantidade: Math.abs(diferenca),
                            tipoDocumento: 'AJUSTE_AUTOMATICO',
                            observacoes: `Ajuste automático - correção de inconsistência`,
                            dataHora: Timestamp.now(),
                            tes: '902', // TES de ajuste por divergência
                            saldoAnterior: saldoRegistrado,
                            saldoPosterior: saldoCalculado
                        });
                    }

                    corrigidos++;
                }
            }

            return { corrigidos };

        } catch (error) {
            console.error('Erro na correção de saldos:', error);
            return { erro: error.message };
        }
    }

    /**
     * ⚠️ CORRIGIR SALDOS NEGATIVOS (3 encontrados)
     */
    static async corrigirSaldosNegativos() {
        console.log('⚠️ Corrigindo saldos negativos...');
        
        try {
            const estoquesQuery = query(
                collection(db, "estoques"),
                where("saldo", "<", 0)
            );

            const estoquesSnap = await getDocs(estoquesQuery);
            let corrigidos = 0;

            for (const estoqueDoc of estoquesSnap.docs) {
                const estoque = estoqueDoc.data();
                const saldoNegativo = estoque.saldo;

                console.log(`⚠️ Saldo negativo encontrado: ${estoque.produtoId} = ${saldoNegativo}`);

                // Opção 1: Zerar saldo e registrar ajuste
                await updateDoc(estoqueDoc.ref, {
                    saldo: 0,
                    saldoAnterior: saldoNegativo,
                    corrigidoEm: Timestamp.now(),
                    observacoes: `Saldo negativo corrigido automaticamente: ${saldoNegativo} → 0`
                });

                // Registrar movimentação de ajuste
                await addDoc(collection(db, "movimentacoesEstoque"), {
                    produtoId: estoque.produtoId,
                    armazemId: estoque.armazemId,
                    tipo: 'ENTRADA',
                    quantidade: Math.abs(saldoNegativo),
                    tipoDocumento: 'AJUSTE_SALDO_NEGATIVO',
                    observacoes: `Ajuste automático - correção de saldo negativo: ${saldoNegativo}`,
                    dataHora: Timestamp.now(),
                    tes: '900', // TES de ajuste de inventário entrada
                    saldoAnterior: saldoNegativo,
                    saldoPosterior: 0
                });

                corrigidos++;
            }

            return { corrigidos };

        } catch (error) {
            console.error('Erro na correção de saldos negativos:', error);
            return { erro: error.message };
        }
    }

    /**
     * 🔍 CORRIGIR REFERÊNCIAS PERDIDAS (8 encontradas)
     */
    static async corrigirReferenciasPerdidas() {
        console.log('🔍 Corrigindo referências perdidas...');
        
        try {
            const [movimentacoesSnap, estoquesSnap] = await Promise.all([
                getDocs(collection(db, "movimentacoesEstoque")),
                getDocs(collection(db, "estoques"))
            ]);

            const estoques = new Map();
            estoquesSnap.docs.forEach(doc => {
                const estoque = doc.data();
                const chave = `${estoque.produtoId}_${estoque.armazemId}`;
                estoques.set(chave, { id: doc.id, ...estoque });
            });

            let corrigidos = 0;

            for (const movDoc of movimentacoesSnap.docs) {
                const mov = movDoc.data();
                const chave = `${mov.produtoId}_${mov.armazemId}`;

                // Verificar se existe estoque para esta movimentação
                if (!estoques.has(chave) && mov.tipo === 'ENTRADA') {
                    console.log(`🔧 Criando estoque órfão para: ${chave}`);

                    // Criar registro de estoque
                    await addDoc(collection(db, "estoques"), {
                        produtoId: mov.produtoId,
                        armazemId: mov.armazemId,
                        saldo: mov.quantidade || 0,
                        saldoReservado: 0,
                        saldoEmpenhado: 0,
                        valorTotal: (mov.quantidade || 0) * (mov.valorUnitario || 0),
                        custoMedio: mov.valorUnitario || 0,
                        ultimaMovimentacao: mov.dataHora || Timestamp.now(),
                        criadoPor: 'CORRECAO_AUTOMATICA',
                        observacoes: 'Estoque criado automaticamente para movimentação órfã'
                    });

                    corrigidos++;
                }
            }

            return { corrigidos };

        } catch (error) {
            console.error('Erro na correção de referências perdidas:', error);
            return { erro: error.message };
        }
    }

    /**
     * 📋 GERAR RELATÓRIO DE CORREÇÃO
     */
    static async gerarRelatorioCorrecao(resultados) {
        const relatorio = {
            timestamp: new Date(),
            resumo: {
                problemasConexao: resultados.conexoes?.corrigidos || 0,
                registrosRemovidos: resultados.conexoes?.removidos || 0,
                saldosCorrigidos: resultados.saldos?.corrigidos || 0,
                saldosNegativosCorrigidos: resultados.saldosNegativos?.corrigidos || 0,
                referenciasCorrigidas: resultados.referenciasPerdidas?.corrigidos || 0
            },
            detalhes: resultados
        };

        console.log('📋 RELATÓRIO DE CORREÇÃO:');
        console.log('========================');
        console.log(`✅ Problemas de conexão corrigidos: ${relatorio.resumo.problemasConexao}`);
        console.log(`🗑️ Registros órfãos removidos: ${relatorio.resumo.registrosRemovidos}`);
        console.log(`📊 Saldos recalculados: ${relatorio.resumo.saldosCorrigidos}`);
        console.log(`⚠️ Saldos negativos corrigidos: ${relatorio.resumo.saldosNegativosCorrigidos}`);
        console.log(`🔗 Referências perdidas corrigidas: ${relatorio.resumo.referenciasCorrigidas}`);

        return relatorio;
    }
}

// Exportar para uso global
window.CorrecaoInconsistenciasService = CorrecaoInconsistenciasService;

// Executar correção completa
window.executarCorrecaoCompleta = async function() {
    try {
        const resultados = await CorrecaoInconsistenciasService.executarCorrecaoCompleta();
        const relatorio = await CorrecaoInconsistenciasService.gerarRelatorioCorrecao(resultados);
        
        alert(`Correção concluída!\n\nProblemas corrigidos:\n- Conexões: ${relatorio.resumo.problemasConexao}\n- Saldos: ${relatorio.resumo.saldosCorrigidos}\n- Saldos negativos: ${relatorio.resumo.saldosNegativosCorrigidos}\n- Referências: ${relatorio.resumo.referenciasCorrigidas}`);
        
        // Recarregar verificação
        if (typeof verificarCompleto === 'function') {
            verificarCompleto();
        }
        
    } catch (error) {
        console.error('Erro na correção:', error);
        alert('Erro durante a correção: ' + error.message);
    }
};

export { CorrecaoInconsistenciasService };
