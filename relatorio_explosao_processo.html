<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Explos<PERSON> do Processo (Roteiro de Produção)</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
    .container { max-width: 1000px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 24px; }
    h1 { color: #0854a0; }
    .input-row { display: flex; gap: 10px; margin-bottom: 20px; }
    input[type=text] { flex: 1; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    button { padding: 8px 18px; background: #0854a0; color: #fff; border: none; border-radius: 4px; font-weight: bold; cursor: pointer; }
    button:hover { background: #0a4d8c; }
    table { border-collapse: collapse; width: 100%; margin-top: 20px; }
    th, td { border: 1px solid #ccc; padding: 6px 10px; font-size: 14px; }
    th { background: #f0f0f0; }
    .nivel-0 { font-weight: bold; background: #e8f4fd; }
    .nivel-1 { background: #f9f9f9; }
    .nivel-2 { background: #f7f7f7; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Explosão do Processo (Roteiro de Produção)</h1>
    <div class="input-row">
      <input type="text" id="codigoProduto" placeholder="Digite o código do produto final...">
      <button onclick="gerarExplosao()">Gerar Explosão</button>
    </div>
    <div id="resultado"></div>
  </div>
  <script type="module">
    import { db } from '/js/firebase-config.js';
    import { getDocs, collection } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function gerarExplosao() {
      const codigo = document.getElementById('codigoProduto').value.trim();
      const resultadoDiv = document.getElementById('resultado');
      resultadoDiv.innerHTML = 'Buscando dados...';
      if (!codigo) {
        resultadoDiv.innerHTML = '<span style="color:red">Digite o código do produto.</span>';
        return;
      }
      // Carregar dados
      const [produtosSnap, estruturasSnap] = await Promise.all([
        getDocs(collection(db, 'produtos')),
        getDocs(collection(db, 'estruturas'))
      ]);
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const produtoRaiz = produtos.find(p => p.codigo === codigo);
      if (!produtoRaiz) {
        resultadoDiv.innerHTML = '<span style="color:red">Produto não encontrado.</span>';
        return;
      }
      // Explodir processo
      let linhas = [];
      function explodir(produtoId, nivel = 0) {
        const produto = produtos.find(p => p.id === produtoId);
        const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
        if (estrutura && estrutura.operacoes && estrutura.operacoes.length > 0) {
          for (const oper of estrutura.operacoes) {
            linhas.push({
              nivel,
              produto: produto?.descricao || '',
              codigo: produto?.codigo || produtoId,
              seq: oper.sequencia,
              operacao: oper.operacao || '',
              tempo: oper.tempo || 0
            });
          }
        }
        if (estrutura && estrutura.componentes && estrutura.componentes.length > 0) {
          for (const comp of estrutura.componentes) {
            explodir(comp.componentId, nivel + 1);
          }
        }
      }
      explodir(produtoRaiz.id, 0);
      if (linhas.length === 0) {
        resultadoDiv.innerHTML = '<span style="color:orange">Nenhuma operação encontrada para este produto e seus componentes.</span>';
        return;
      }
      let tabela = `<table><thead><tr><th>Nível</th><th>Produto</th><th>Código</th><th>Seq. Op</th><th>Operação</th><th>Tempo (min)</th></tr></thead><tbody>`;
      for (const l of linhas) {
        tabela += `<tr class="nivel-${l.nivel}"><td>${l.nivel}</td><td>${l.produto}</td><td>${l.codigo}</td><td>${l.seq}</td><td>${l.operacao}</td><td>${l.tempo}</td></tr>`;
      }
      tabela += '</tbody></table>';
      resultadoDiv.innerHTML = tabela;
    }
    window.gerarExplosao = gerarExplosao;
  </script>
</body>
</html> 