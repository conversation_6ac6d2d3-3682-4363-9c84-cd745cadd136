<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Alterar Ordem de Produção</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }
    .form-container {
      width: 80%;
      max-width: 900px;
      margin: 50px auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    h1 {
      text-align: center;
      color: #333;
    }
    label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    input, select {
      width: 100%;
      padding: 8px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 15px;
    }
    button:hover {
      background-color: #45a049;
    }
    .back-button {
      background-color: #008CBA;
    }
    .back-button:hover {
      background-color: #007bb5;
    }
    .component-list {
      margin-top: 20px;
    }
    .component-item {
      display: flex;
      gap: 10px;
      align-items: center;
      margin-bottom: 10px;
    }
    .remove-btn {
      background-color: #f44336;
    }
    .remove-btn:hover {
      background-color: #d32f2f;
    }
  </style>
</head>
<body>
  <div class="form-container">
    <h1>Alterar Ordem de Produção</h1>
    <form id="editOpForm">
      <label for="opSelect">Selecionar OP:</label>
      <select id="opSelect" onchange="loadOpData()">
        <option value="">Selecione uma OP...</option>
      </select>

      <label for="produto">Produto:</label>
      <select id="produto"></select>
      
      <label for="quantidade">Quantidade:</label>
      <input type="number" id="quantidade" min="0.001" step="0.001" required>
      
      <label for="dataEntrega">Data de Entrega:</label>
      <input type="date" id="dataEntrega" required>
      
      <label for="prioridade">Prioridade:</label>
      <select id="prioridade" required>
        <option value="normal">Normal</option>
        <option value="alta">Alta</option>
        <option value="urgente">Urgente</option>
      </select>
      
      <label for="observacoes">Observações:</label>
      <textarea id="observacoes" rows="3"></textarea>
      
      <h2>Componentes</h2>
      <div id="componentesList" class="component-list"></div>
      <button type="button" onclick="addComponent()">Adicionar Componente</button>
      
      <button type="submit">Salvar Alterações</button>
      <button type="button" onclick="window.location.href='index.html'" class="back-button">Voltar</button>
    </form>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      doc, 
      updateDoc, 
      addDoc // Adicionado para corrigir o erro
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensProducao = [];
    let produtos = {};
    let estoques = [];

    window.onload = async function() {
      await loadProducts();
      await loadOrders();
      await loadEstoques();
    };

    // Carrega os produtos do Firestore
    async function loadProducts() {
      try {
        const querySnapshot = await getDocs(collection(db, "produtos"));
        const produtoSelect = document.getElementById('produto');
        querySnapshot.forEach(doc => {
          produtos[doc.id] = doc.data().descricao;
          produtoSelect.innerHTML += `<option value="${doc.id}">${doc.data().descricao}</option>`;
        });
      } catch (error) {
        console.error("Erro ao carregar produtos:", error);
      }
    }

    // Carrega as ordens de produção do Firestore
    async function loadOrders() {
      try {
        const querySnapshot = await getDocs(collection(db, "ordensProducao"));
        ordensProducao = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        const opSelect = document.getElementById('opSelect');
        ordensProducao.forEach(op => {
          const descricaoProduto = produtos[op.produtoId] || op.produtoId;
          opSelect.innerHTML += `<option value="${op.id}">${op.numero} - ${descricaoProduto}</option>`;
        });
      } catch (error) {
        console.error("Erro ao carregar OPs:", error);
        alert("Erro ao carregar OPs.");
      }
    }

    // Carrega os estoques do Firestore
    async function loadEstoques() {
      try {
        const querySnapshot = await getDocs(collection(db, "estoques"));
        estoques = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar estoques:", error);
      }
    }

    // Carrega os dados da OP selecionada
    window.loadOpData = function() {
      const opId = document.getElementById('opSelect').value;
      if (!opId) return;

      const op = ordensProducao.find(o => o.id === opId);
      if (!op) return;

      document.getElementById('produto').value = op.produtoId;
      document.getElementById('quantidade').value = op.quantidade;
      document.getElementById('dataEntrega').value = new Date(op.dataEntrega.seconds * 1000).toISOString().split('T')[0];
      document.getElementById('prioridade').value = op.prioridade;
      document.getElementById('observacoes').value = op.observacoes || '';
      
      const componentesList = document.getElementById('componentesList');
      componentesList.innerHTML = '';
      if (op.materiaisNecessarios) {
        for (const comp of op.materiaisNecessarios) {
          componentesList.innerHTML += `
            <div class="component-item">
              <select>
                ${Object.entries(produtos).map(([id, descricao]) => `<option value="${id}" ${comp.produtoId === id ? 'selected' : ''}>${descricao}</option>`).join('')}
              </select>
              <input type="number" value="${comp.quantidade}" min="0.001" step="0.001">
              <button type="button" class="remove-btn" onclick="removeComponent(this)">Remover</button>
            </div>
          `;
        }
      }
    };

    // Adiciona um novo componente à lista
    window.addComponent = function() {
      const componentesList = document.getElementById('componentesList');
      componentesList.innerHTML += `
        <div class="component-item">
          <select>
            ${Object.entries(produtos).map(([id, descricao]) => `<option value="${id}">${descricao}</option>`).join('')}
          </select>
          <input type="number" value="1" min="0.001" step="0.001">
          <button type="button" class="remove-btn" onclick="removeComponent(this)">Remover</button>
        </div>
      `;
    };

    // Remove um componente da lista
    window.removeComponent = function(button) {
      button.parentElement.remove();
    };

    // Salva as alterações da OP
    document.getElementById('editOpForm').addEventListener('submit', async function(event) {
      event.preventDefault();

      const opId = document.getElementById('opSelect').value;
      const produtoId = document.getElementById('produto').value;
      const quantidade = parseFloat(document.getElementById('quantidade').value);
      const dataEntrega = document.getElementById('dataEntrega').value;
      const prioridade = document.getElementById('prioridade').value;
      const observacoes = document.getElementById('observacoes').value;

      const componentes = [];
      const componentIds = new Set();
      let hasInvalidComponent = false;

      document.querySelectorAll('#componentesList .component-item').forEach(item => {
        const componentId = item.querySelector('select').value;
        const quantidadeComponente = parseFloat(item.querySelector('input[type="number"]').value);

        if (!componentId || !quantidadeComponente) {
          hasInvalidComponent = true;
          return;
        }

        if (componentIds.has(componentId)) {
          alert('Não é permitido usar o mesmo componente mais de uma vez na ordem.');
          hasInvalidComponent = true;
          return;
        }

        componentIds.add(componentId);
        componentes.push({
          produtoId: componentId,
          quantidade: quantidadeComponente
        });
      });

      if (hasInvalidComponent) {
        alert('Por favor, preencha todos os campos dos componentes corretamente.');
        return;
      }

      try {
        const opRef = doc(db, "ordensProducao", opId);
        const op = ordensProducao.find(o => o.id === opId);

        // Reverter o empenho anterior
        if (op.materiaisNecessarios) {
          for (const material of op.materiaisNecessarios) {
            await updateInventory(material.produtoId, material.quantidade, 'entrada');
          }
        }

        // Atualizar a ordem de produção
        await updateDoc(opRef, {
          produtoId,
          quantidade,
          dataEntrega: new Date(dataEntrega),
          prioridade,
          observacoes,
          materiaisNecessarios: componentes
        });

        // Criar novo empenho
        for (const componente of componentes) {
          await updateInventory(componente.produtoId, componente.quantidade, 'saida');
        }

        alert('Ordem de produção atualizada com sucesso!');
        window.location.href = 'ordens_producao.html';
      } catch (error) {
        console.error("Erro ao atualizar ordem de produção:", error);
        alert("Erro ao atualizar ordem de produção.");
      }
    });

    // Atualiza o estoque no Firestore
    async function updateInventory(produtoId, quantidade, tipo) {
      const estoqueRef = estoques.find(e => e.produtoId === produtoId);
      
      if (estoqueRef) {
        const novoSaldo = tipo === 'entrada' ? 
          estoqueRef.saldo + quantidade : 
          estoqueRef.saldo - quantidade;

        await updateDoc(doc(db, "estoques", estoqueRef.id), {
          saldo: novoSaldo,
          ultimaMovimentacao: new Date()
        });

        estoqueRef.saldo = novoSaldo;
      } else {
        const novoEstoque = {
          produtoId,
          saldo: tipo === 'entrada' ? quantidade : -quantidade,
          ultimaMovimentacao: new Date()
        };

        // Adiciona um novo documento à coleção "estoques"
        const docRef = await addDoc(collection(db, "estoques"), novoEstoque);
        novoEstoque.id = docRef.id;
        estoques.push(novoEstoque);
      }
    }
  </script>
</body>
</html>