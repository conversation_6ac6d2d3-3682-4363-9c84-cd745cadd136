<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Apontamentos - Versão Moderna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.pendentes {
            border-left-color: #f39c12;
        }

        .stat-card.em-producao {
            border-left-color: #3498db;
        }

        .stat-card.concluidas {
            border-left-color: #27ae60;
        }

        .stat-card.total {
            border-left-color: #9b59b6;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .filters {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            display: inline-block;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        /* Estilo específico para aguardando material */
        .badge-aguardando {
            background: rgba(255, 126, 20, 0.1);
            color: #fd7e14;
            animation: pulse-aguardando 2s infinite;
        }

        @keyframes pulse-aguardando {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .table {
                min-width: 800px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-clipboard-check"></i>
                Sistema de Apontamentos
            </h1>
            <div class="header-actions">
                <button class="btn btn-info" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    Atualizar
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i>
                    Voltar
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card pendentes">
                    <div class="stat-number" id="statPendentes">0</div>
                    <div class="stat-label">OPs Pendentes</div>
                </div>
                <div class="stat-card em-producao">
                    <div class="stat-number" id="statEmProducao">0</div>
                    <div class="stat-label">Em Produção</div>
                </div>
                <div class="stat-card concluidas">
                    <div class="stat-number" id="statConcluidas">0</div>
                    <div class="stat-label">Concluídas</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number" id="statTotal">0</div>
                    <div class="stat-label">Total de OPs</div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label" for="filtroOP">Buscar OP</label>
                        <input type="text" id="filtroOP" class="form-input" placeholder="Digite o número da OP...">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="filtroStatus">Status</label>
                        <select id="filtroStatus" class="form-input">
                            <option value="">Todos os status</option>
                            <option value="Pendente">Pendente</option>
                            <option value="Em Produção">Em Produção</option>
                            <option value="Concluída">Concluída</option>
                            <option value="Pausada">Pausada</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="filtroMaterial">Material Transferido</label>
                        <select id="filtroMaterial" class="form-input">
                            <option value="">Todos</option>
                            <option value="true">Com material</option>
                            <option value="false">Sem material</option>
                            <option value="parcial">Parcial</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="mostrarFinalizadas" style="margin-right: 8px;">
                            Mostrar OPs Concluídas/Canceladas
                        </label>
                        <small style="color: #6c757d; font-size: 0.8rem; display: block; margin-top: 4px;">
                            Por padrão, OPs concluídas e canceladas ficam ocultas
                        </small>
                    </div>
                </div>
            </div>

            <!-- Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>OP</th>
                            <th>Produto</th>
                            <th>Quantidade</th>
                            <th>Status</th>
                            <th>Materiais</th>
                            <th>Entrega</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- Dados carregados dinamicamente -->
                    </tbody>
                </table>

                <!-- Contador de OPs ocultas -->
                <div id="contadorOcultas" style="text-align: center; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; display: none;">
                    <small style="color: #6c757d;">
                        <i class="fas fa-eye-slash"></i>
                        <span id="numeroOcultas">0</span> OP(s) concluída(s)/cancelada(s) oculta(s).
                        <a href="#" onclick="document.getElementById('mostrarFinalizadas').checked = true; aplicarFiltros(); return false;" style="color: #007bff;">
                            Clique aqui para exibir todas
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Área oculta para impressão -->
    <div id="printArea" style="display:none"></div>

    <!-- Scripts -->
    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            query,
            where,
            orderBy,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // ========================================
        // 📊 VARIÁVEIS GLOBAIS
        // ========================================

        let ordens = [];
        let produtos = [];
        let estoques = [];
        let transferencias = [];
        let apontamentos = [];
        let opsOcultas = 0;

        // ========================================
        // 🚀 INICIALIZAÇÃO DO SISTEMA
        // ========================================

        window.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('🎯 Iniciando Sistema de Apontamentos...');

                await carregarDados();
                atualizarDashboard();
                renderizarTabela();
                configurarEventListeners();

                console.log('✅ Sistema carregado com sucesso!');
            } catch (error) {
                console.error('❌ Erro ao inicializar sistema:', error);
            }
        });

        // ========================================
        // 📊 CARREGAMENTO DE DADOS
        // ========================================

        async function carregarDados() {
            try {
                console.log('📊 Carregando dados...');

                // Carregar ordens de produção
                const ordensSnap = await getDocs(
                    query(collection(db, "ordensProducao"), orderBy("numero", "desc"))
                );
                ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar transferências
                const transferenciasSnap = await getDocs(collection(db, "transferenciasEstoque"));
                transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ Dados carregados: ${ordens.length} OPs, ${produtos.length} produtos`);
            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                throw error;
            }
        }

        // ========================================
        // 📊 DASHBOARD E ESTATÍSTICAS
        // ========================================

        function atualizarDashboard() {
            const stats = {
                pendentes: ordens.filter(op => op.status === 'Pendente').length,
                emProducao: ordens.filter(op => op.status === 'Em Produção').length,
                concluidas: ordens.filter(op => op.status === 'Concluída').length,
                total: ordens.length
            };

            document.getElementById('statPendentes').textContent = stats.pendentes;
            document.getElementById('statEmProducao').textContent = stats.emProducao;
            document.getElementById('statConcluidas').textContent = stats.concluidas;
            document.getElementById('statTotal').textContent = stats.total;
        }

        // ========================================
        // 🔍 ANÁLISE DE STATUS DOS MATERIAIS
        // ========================================

        async function analisarStatusMateriais(ordemId) {
            try {
                const ordem = ordens.find(o => o.id === ordemId);
                if (!ordem || !ordem.materiaisNecessarios) {
                    return { status: 'PENDENTE', detalhes: 'Sem materiais definidos' };
                }

                const materiaisMP = ordem.materiaisNecessarios.filter(m => {
                    const produto = produtos.find(p => p.id === m.produtoId);
                    return produto && produto.tipo === 'Matéria Prima';
                });

                // Não retornar PENDENTE imediatamente se não há transferências
                // Primeiro verificar se há materiais aguardando

                let materiaisCompletos = 0;
                let materiaisParciais = 0;
                let materiaisPendentes = 0;
                let materiaisAguardando = 0;
                let detalhesMateriaisStatus = [];

                for (const material of materiaisMP) {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    const transferenciasOP = transferencias.filter(t =>
                        t.ordemProducaoId === ordemId &&
                        t.produtoId === material.produtoId
                    );

                    const totalTransferido = transferenciasOP.reduce((sum, t) => sum + (t.quantidade || 0), 0);
                    const restante = Math.max(0, material.quantidade - totalTransferido);

                    // Verificar se está aguardando material
                    const aguardandoMaterial = material.aguardandoMaterial || false;

                    let statusMaterial = 'PENDENTE';

                    // PRIORIDADE 1: Se está aguardando material, sempre mostrar AGUARDANDO
                    if (aguardandoMaterial && restante > 0) {
                        statusMaterial = 'AGUARDANDO';
                        materiaisAguardando++;
                    }
                    // PRIORIDADE 2: Se transferência completa
                    else if (restante <= 0) {
                        statusMaterial = 'COMPLETO';
                        materiaisCompletos++;
                    }
                    // PRIORIDADE 3: Se transferência parcial
                    else if (totalTransferido > 0) {
                        statusMaterial = 'PARCIAL';
                        materiaisParciais++;
                    }
                    // PRIORIDADE 4: Pendente (sem transferência e sem aguardo)
                    else {
                        statusMaterial = 'PENDENTE';
                        materiaisPendentes++;
                    }

                    detalhesMateriaisStatus.push({
                        produtoId: material.produtoId,
                        produtoCodigo: produto?.codigo || 'N/A',
                        produtoDescricao: produto?.descricao || 'N/A',
                        quantidadeNecessaria: material.quantidade,
                        quantidadeTransferida: totalTransferido,
                        quantidadeRestante: restante,
                        status: statusMaterial,
                        aguardandoMaterial: aguardandoMaterial,
                        dataAguardo: material.dataAguardo,
                        usuarioAguardo: material.usuarioAguardo
                    });
                }

                // Determinar status geral
                let statusGeral = 'PENDENTE';
                if (materiaisCompletos === materiaisMP.length) {
                    statusGeral = 'COMPLETO';
                } else if (materiaisCompletos > 0 || materiaisParciais > 0) {
                    statusGeral = 'PARCIAL';
                } else if (materiaisAguardando > 0) {
                    statusGeral = 'AGUARDANDO';
                }

                return {
                    status: statusGeral,
                    detalhes: `${materiaisCompletos} completos, ${materiaisParciais} parciais, ${materiaisAguardando} aguardando, ${materiaisPendentes} pendentes`,
                    materiaisCompletos,
                    materiaisParciais,
                    materiaisPendentes,
                    materiaisAguardando,
                    totalMateriais: materiaisMP.length,
                    detalhesMateriaisStatus,
                    totalTransferencias: transferencias.filter(t => t.ordemProducaoId === ordemId).length
                };

            } catch (error) {
                console.error('❌ Erro ao analisar status dos materiais:', error);
                return { status: 'ERRO', detalhes: 'Erro na análise' };
            }
        }

        // ========================================
        // 🎨 RENDERIZAÇÃO DA TABELA
        // ========================================

        async function renderizarTabela() {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';

            const ordensFiltradas = await aplicarFiltros();

            for (const ordem of ordensFiltradas) {
                const produto = produtos.find(p => p.id === ordem.produtoId);
                const statusMateriais = await analisarStatusMateriais(ordem.id);

                const row = document.createElement('tr');
                row.innerHTML = criarLinhaTabela(ordem, produto, statusMateriais);
                tbody.appendChild(row);
            }

            // Atualizar contador de OPs ocultas
            const contadorOcultas = document.getElementById('contadorOcultas');
            const numeroOcultas = document.getElementById('numeroOcultas');

            if (opsOcultas > 0) {
                numeroOcultas.textContent = opsOcultas;
                contadorOcultas.style.display = 'block';
            } else {
                contadorOcultas.style.display = 'none';
            }
        }

        function criarLinhaTabela(ordem, produto, statusMateriais) {
            const dataEntrega = ordem.dataEntrega ?
                new Date(ordem.dataEntrega.seconds * 1000).toLocaleDateString('pt-BR') :
                'Não definida';

            return `
                <td><strong>${ordem.numero}</strong></td>
                <td>
                    <div><strong>${produto?.codigo || 'N/A'}</strong></div>
                    <div style="font-size: 0.8rem; color: #666;">${produto?.descricao || 'N/A'}</div>
                </td>
                <td>${ordem.quantidade || 0}</td>
                <td>
                    <span class="badge ${getStatusClass(ordem.status)}">${ordem.status}</span>
                    ${statusMateriais.detalhesMateriaisStatus?.some(m => m.aguardandoMaterial) && ordem.status !== 'Concluída' ? `
                        <span class="badge badge-warning"
                              style="font-size: 0.65rem; padding: 2px 6px; animation: pulse-aguardando 2s infinite;"
                              title="Há materiais aguardando chegada">
                            <i class="fas fa-clock"></i> AGUARDANDO
                        </span>
                    ` : ''}
                </td>
                <td>
                    <span class="badge ${getStatusMaterialClass(statusMateriais.status)}">${statusMateriais.status}</span>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 2px;">${statusMateriais.detalhes}</div>
                </td>
                <td>${dataEntrega}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="abrirMovimentacao('${ordem.id}')">
                        <i class="fas fa-truck"></i> Materiais
                    </button>
                </td>
            `;
        }

        // ========================================
        // 🔍 SISTEMA DE FILTROS
        // ========================================

        async function aplicarFiltros() {
            const filtroOP = document.getElementById('filtroOP')?.value?.trim();
            const filtroStatus = document.getElementById('filtroStatus')?.value;
            const filtroMaterial = document.getElementById('filtroMaterial')?.value;
            const mostrarFinalizadas = document.getElementById('mostrarFinalizadas')?.checked;

            let ordensFiltradas = [...ordens];

            if (filtroOP) {
                ordensFiltradas = ordensFiltradas.filter(op =>
                    op.numero.toString().includes(filtroOP)
                );
            }

            if (filtroStatus) {
                ordensFiltradas = ordensFiltradas.filter(op => op.status === filtroStatus);
            }

            // Filtro para ocultar OPs concluídas e canceladas por padrão
            let opsOcultasCount = 0;
            if (!mostrarFinalizadas) {
                const antesDoFiltro = ordensFiltradas.length;
                ordensFiltradas = ordensFiltradas.filter(op =>
                    op.status !== 'Concluída' && op.status !== 'Cancelada'
                );
                opsOcultasCount = antesDoFiltro - ordensFiltradas.length;
            }
            opsOcultas = opsOcultasCount;

            return ordensFiltradas;
        }

        // ========================================
        // 🎨 FUNÇÕES DE ESTILO
        // ========================================

        function getStatusClass(status) {
            switch (status) {
                case 'Concluída': return 'badge-success';
                case 'Em Produção': return 'badge-info';
                case 'Pausada': return 'badge-warning';
                case 'Cancelada': return 'badge-danger';
                default: return 'badge-secondary';
            }
        }

        function getStatusMaterialClass(status) {
            switch (status) {
                case 'COMPLETO': return 'badge-success';
                case 'PARCIAL': return 'badge-warning';
                case 'AGUARDANDO': return 'badge-info';
                case 'PENDENTE': return 'badge-danger';
                default: return 'badge-secondary';
            }
        }

        // ========================================
        // 🔗 INTEGRAÇÃO COM MOVIMENTAÇÃO
        // ========================================

        window.abrirMovimentacao = function(ordemId) {
            const ordem = ordens.find(o => o.id === ordemId);
            if (!ordem) {
                alert('Ordem não encontrada!');
                return;
            }

            // Redirecionar para o sistema de movimentação com a OP selecionada
            const url = `movimentacao_armazem_novo.html?op=${ordem.numero}`;
            window.open(url, '_blank');
        };

        // ========================================
        // 🔄 LISTENERS DE EVENTOS
        // ========================================

        function configurarEventListeners() {
            const filtroOP = document.getElementById('filtroOP');
            const filtroStatus = document.getElementById('filtroStatus');
            const filtroMaterial = document.getElementById('filtroMaterial');
            const mostrarFinalizadas = document.getElementById('mostrarFinalizadas');

            if (filtroOP) {
                filtroOP.addEventListener('input', debounce(renderizarTabela, 300));
            }
            if (filtroStatus) {
                filtroStatus.addEventListener('change', renderizarTabela);
            }
            if (filtroMaterial) {
                filtroMaterial.addEventListener('change', renderizarTabela);
            }
            if (mostrarFinalizadas) {
                mostrarFinalizadas.addEventListener('change', renderizarTabela);
            }
        }

        // Função debounce para otimizar busca
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        console.log('🎯 Sistema de Apontamentos Moderno carregado e pronto!');
    </script>
</body>
</html>
