<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 Capturar Telas para Apostila</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2em;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .content {
            padding: 30px;
        }

        .capture-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .capture-info {
            flex: 1;
        }

        .capture-info h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .capture-info p {
            margin: 5px 0;
            color: #666;
        }

        .capture-actions {
            display: flex;
            gap: 10px;
            flex-direction: column;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .instructions h3 {
            color: #0c5460;
            margin-top: 0;
        }

        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #17a2b8;
        }

        .filename {
            background: #2c3e50;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-captured {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-camera"></i>
                Capturar Telas para Apostila
            </h1>
            <p>Guia para capturar todas as telas necessárias do módulo de Apontamentos</p>
        </div>

        <div class="content">
            <div class="instructions">
                <h3><i class="fas fa-info-circle"></i> Instruções Gerais</h3>
                <div class="step">
                    <strong>1.</strong> Clique em "Abrir Tela" para abrir a funcionalidade
                </div>
                <div class="step">
                    <strong>2.</strong> Use <kbd>Win + Shift + S</kbd> (Windows) ou <kbd>Cmd + Shift + 4</kbd> (Mac) para capturar
                </div>
                <div class="step">
                    <strong>3.</strong> Salve com o nome exato mostrado em cada item
                </div>
                <div class="step">
                    <strong>4.</strong> Coloque todas as imagens na pasta raiz do projeto
                </div>
            </div>

            <!-- Tela Principal -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-home"></i> Tela Principal</h3>
                    <p><strong>Descrição:</strong> Lista principal de OPs com filtros</p>
                    <p><strong>Arquivo:</strong> <span class="filename">tela_principal_apontamentos.png</span></p>
                    <p><strong>Foco:</strong> Lista de OPs, filtros superiores, botões de ação</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                </div>
            </div>

            <!-- Filtros de Busca -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-filter"></i> Filtros de Busca</h3>
                    <p><strong>Descrição:</strong> Área de filtros expandida</p>
                    <p><strong>Arquivo:</strong> <span class="filename">filtros_busca_apontamentos.png</span></p>
                    <p><strong>Foco:</strong> Campos de filtro por data, armazém, status</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                </div>
            </div>

            <!-- Botão Apontar Produção -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-play-circle"></i> Botão Apontar Produção</h3>
                    <p><strong>Descrição:</strong> Botão verde de apontamento ativo</p>
                    <p><strong>Arquivo:</strong> <span class="filename">botao_apontar_producao.png</span></p>
                    <p><strong>Foco:</strong> Linha da OP com botão verde habilitado</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                </div>
            </div>

            <!-- Modal de Apontamento -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-edit"></i> Modal de Apontamento</h3>
                    <p><strong>Descrição:</strong> Formulário de apontamento aberto</p>
                    <p><strong>Arquivo:</strong> <span class="filename">modal_apontamento.png</span></p>
                    <p><strong>Foco:</strong> Modal completo com todos os campos</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                    <button class="btn btn-info" onclick="alert('1. Clique em Apontar Produção\n2. Capture o modal que abrir')">
                        <i class="fas fa-question-circle"></i> Como Capturar
                    </button>
                </div>
            </div>

            <!-- Botão Verificar Saldo -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-search"></i> Botão Verificar Saldo</h3>
                    <p><strong>Descrição:</strong> Botão azul de verificação</p>
                    <p><strong>Arquivo:</strong> <span class="filename">botao_verificar_saldo.png</span></p>
                    <p><strong>Foco:</strong> Linha da OP com botão azul de verificação</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                </div>
            </div>

            <!-- Modal Materiais Faltantes -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-exclamation-triangle"></i> Modal Materiais Faltantes</h3>
                    <p><strong>Descrição:</strong> Modal com lista de materiais em falta</p>
                    <p><strong>Arquivo:</strong> <span class="filename">modal_materiais_faltantes.png</span></p>
                    <p><strong>Foco:</strong> Tabela com materiais, códigos, descrições, quantidades</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                    <button class="btn btn-info" onclick="alert('1. Clique em Verificar Saldo\n2. Capture o modal que abrir')">
                        <i class="fas fa-question-circle"></i> Como Capturar
                    </button>
                </div>
            </div>

            <!-- Botão Imprimir OP -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-print"></i> Botão Imprimir OP</h3>
                    <p><strong>Descrição:</strong> Botão de impressão da OP</p>
                    <p><strong>Arquivo:</strong> <span class="filename">botao_imprimir_op.png</span></p>
                    <p><strong>Foco:</strong> Linha da OP com botão de impressão</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="apontamentos_simplificado.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Tela
                    </a>
                </div>
            </div>

            <!-- Relatório de Materiais -->
            <div class="capture-item">
                <div class="capture-info">
                    <h3><i class="fas fa-chart-bar"></i> Relatório de Materiais</h3>
                    <p><strong>Descrição:</strong> Relatório detalhado de materiais</p>
                    <p><strong>Arquivo:</strong> <span class="filename">relatorio_materiais.png</span></p>
                    <p><strong>Foco:</strong> Tabela completa com status dos materiais</p>
                    <span class="status status-pending">📋 Pendente</span>
                </div>
                <div class="capture-actions">
                    <a href="relatorio_materiais_producao.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Relatório
                    </a>
                </div>
            </div>

            <div class="instructions">
                <h3><i class="fas fa-upload"></i> Após Capturar Todas as Telas</h3>
                <div class="step">
                    <strong>1.</strong> Verifique se todas as 8 imagens foram salvas na pasta do projeto
                </div>
                <div class="step">
                    <strong>2.</strong> Abra a apostila em <a href="apostila_apontamentos.html" target="_blank">apostila_apontamentos.html</a>
                </div>
                <div class="step">
                    <strong>3.</strong> As imagens aparecerão automaticamente no lugar dos placeholders
                </div>
                <div class="step">
                    <strong>4.</strong> Se alguma imagem não aparecer, verifique o nome do arquivo
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="apostila_apontamentos.html" target="_blank" class="btn btn-success">
                    <i class="fas fa-book-open"></i> Ver Apostila Final
                </a>
            </div>
        </div>
    </div>
</body>
</html>
