<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Relatório Completo de Coleções</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script type="module" src="firebase-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .content {
            padding: 30px;
        }

        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid;
        }

        .metric-card.primary { border-color: #3498db; }
        .metric-card.success { border-color: #27ae60; }
        .metric-card.warning { border-color: #f39c12; }
        .metric-card.danger { border-color: #e74c3c; }
        .metric-card.info { border-color: #17a2b8; }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 14px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            font-size: 12px;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid;
        }

        .alert-info {
            background: #e7f3ff;
            border-color: #3498db;
            color: #2c3e50;
        }

        .alert-warning {
            background: #fff8e1;
            border-color: #f39c12;
            color: #2c3e50;
        }

        .alert-danger {
            background: #ffeaea;
            border-color: #e74c3c;
            color: #2c3e50;
        }

        .alert-success {
            background: #eafaf1;
            border-color: #27ae60;
            color: #2c3e50;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tab {
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .tab.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .search-box {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .search-box:focus {
            outline: none;
            border-color: #3498db;
        }

        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .collection-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3498db;
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .summary-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-database"></i>
                Relatório Completo de Coleções
            </h1>
            <div>
                <button class="btn btn-primary" onclick="exportarTudo()">
                    <i class="fas fa-download"></i> Exportar Tudo
                </button>
                <button class="btn btn-success" onclick="atualizarTudo()">
                    <i class="fas fa-sync"></i> Atualizar
                </button>
            </div>
        </div>

        <div class="content">
            <!-- Controles -->
            <div class="controls">
                <h3><i class="fas fa-cogs"></i> Controles de Análise</h3>
                <div class="grid">
                    <button class="btn btn-primary" onclick="carregarTodasColecoes()">
                        <i class="fas fa-database"></i> Carregar Todas as Coleções
                    </button>
                    <button class="btn btn-success" onclick="analisarRelatorioMovimentacoes()">
                        <i class="fas fa-chart-line"></i> Analisar Relatório Movimentações
                    </button>
                    <button class="btn btn-warning" onclick="compararColecoes()">
                        <i class="fas fa-balance-scale"></i> Comparar Coleções
                    </button>
                    <button class="btn btn-danger" onclick="buscarInconsistencias()">
                        <i class="fas fa-exclamation-triangle"></i> Buscar Inconsistências
                    </button>
                </div>
            </div>

            <!-- Métricas Gerais -->
            <div id="metricas" class="grid" style="display: none;">
                <!-- Será preenchido dinamicamente -->
            </div>

            <!-- Loading -->
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Carregando dados...</p>
            </div>

            <!-- Resultados -->
            <div id="resultados" style="display: none;">
                <!-- Tabs -->
                <div class="tabs">
                    <button class="tab active" onclick="mostrarTab('resumo')">
                        <i class="fas fa-chart-pie"></i> Resumo
                    </button>
                    <button class="tab" onclick="mostrarTab('colecoes')">
                        <i class="fas fa-database"></i> Coleções
                    </button>
                    <button class="tab" onclick="mostrarTab('movimentacoes')">
                        <i class="fas fa-exchange-alt"></i> Movimentações
                    </button>
                    <button class="tab" onclick="mostrarTab('recebimentos')">
                        <i class="fas fa-inbox"></i> Recebimentos
                    </button>
                    <button class="tab" onclick="mostrarTab('comparacao')">
                        <i class="fas fa-not-equal"></i> Comparação
                    </button>
                    <button class="tab" onclick="mostrarTab('json-raw')">
                        <i class="fas fa-code"></i> JSON Raw
                    </button>
                </div>

                <!-- Conteúdo das Tabs -->
                <div id="resumo" class="tab-content active">
                    <!-- Será preenchido dinamicamente -->
                </div>

                <div id="colecoes" class="tab-content">
                    <!-- Será preenchido dinamicamente -->
                </div>

                <div id="movimentacoes" class="tab-content">
                    <!-- Será preenchido dinamicamente -->
                </div>

                <div id="recebimentos" class="tab-content">
                    <!-- Será preenchido dinamicamente -->
                </div>

                <div id="comparacao" class="tab-content">
                    <!-- Será preenchido dinamicamente -->
                </div>

                <div id="json-raw" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-code"></i> Dados Brutos (JSON)
                        </div>
                        <div class="card-body">
                            <input type="text" class="search-box" placeholder="Buscar no JSON..." onkeyup="filtrarJson(this.value)">
                            <div id="jsonViewer" class="json-viewer">
                                <!-- Será preenchido dinamicamente -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Importar Firebase config
        import { db } from './firebase-config.js';
        // Importar funções do Firestore
        import { collection, getDocs, query, where, orderBy, limit } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let dadosCompletos = {};
        let analiseRelatorio = {};

        // Expor funções globalmente
        window.db = db;
        window.collection = collection;
        window.getDocs = getDocs;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.limit = limit;

        // ===================================================================
        // FUNÇÕES PRINCIPAIS
        // ===================================================================

        window.carregarTodasColecoes = async function() {
            mostrarLoading(true);

            try {
                console.log('🔍 Iniciando carregamento completo de todas as coleções...');

                dadosCompletos = {
                    timestamp: new Date().toISOString(),
                    colecoes: {},
                    estatisticas: {},
                    problemas: []
                };

                // Lista de todas as coleções do sistema
                const colecoes = [
                    // Coleções principais de estoque e movimentações
                    'ajustesEstoque',
                    'estoques',
                    'movimentacoesEstoque',
                    'movimentacoes_estoque',
                    'transferenciasArmazem',

                    // Coleções de compras
                    'solicitacoesCompra',
                    'cotacoes',
                    'pedidosCompra',
                    'recebimentoMateriais',
                    'recebimentos',
                    'recebimentosDetalhes',
                    'recebimentosMateriais',

                    // Coleções de produção
                    'ordensProducao',
                    'apontamentos',
                    'operacoes',
                    'estruturas',
                    'recursos',

                    // Coleções de qualidade
                    'estoqueQualidade',
                    'especificacoesInspecao',
                    'avaliacoesFornecedores',

                    // Coleções de cadastros básicos
                    'produtos',
                    'fornecedores',
                    'clientes',
                    'armazens',
                    'familias',
                    'grupos',
                    'usuarios',
                    'empresa',

                    // Coleções de configurações
                    'parametros',
                    'configuracoes',
                    'centrosCusto',
                    'condicoesPagamento',
                    'cadastroTES',
                    'permissoes',
                    'contadores',
                    'sequenciais',

                    // Coleções de auditoria e logs
                    'auditoria',
                    'auditoriaMovimentacoes',
                    'logsAlteracoes',
                    'logsAtividades',
                    'logsAuditoria',
                    'log_cotacoes',
                    'log_envios',
                    'historico_produtos',

                    // Coleções especiais
                    'cancelamentosNecessidades',
                    'produtos_fornecedores',
                    'recomendacoesIA',
                    'orcamentos',
                    'emails',
                    'backups',
                    'central',
                    'relatoriosGerados'
                ];

                console.log(`📋 Carregando ${colecoes.length} coleções...`);

                // Carregar cada coleção
                for (const nomeColecao of colecoes) {
                    try {
                        console.log(`📦 Carregando coleção: ${nomeColecao}`);

                        const snapshot = await getDocs(collection(db, nomeColecao));
                        const documentos = snapshot.docs.map(doc => ({
                            id: doc.id,
                            ...doc.data()
                        }));

                        dadosCompletos.colecoes[nomeColecao] = {
                            total: documentos.length,
                            documentos: documentos,
                            amostra: documentos.slice(0, 5), // Primeiros 5 para análise
                            campos: extrairCampos(documentos),
                            ultimaAtualizacao: new Date().toISOString()
                        };

                        console.log(`✅ ${nomeColecao}: ${documentos.length} documentos`);

                    } catch (error) {
                        console.error(`❌ Erro ao carregar ${nomeColecao}:`, error);
                        dadosCompletos.problemas.push({
                            colecao: nomeColecao,
                            erro: error.message,
                            tipo: 'CARREGAMENTO'
                        });
                    }
                }

                // Calcular estatísticas
                calcularEstatisticas();

                // Mostrar resultados
                mostrarResultados();

                console.log('✅ Carregamento completo finalizado');

            } catch (error) {
                console.error('❌ Erro geral no carregamento:', error);
                mostrarAlerta('Erro no carregamento: ' + error.message, 'danger');
            } finally {
                mostrarLoading(false);
            }
        };

        function extrairCampos(documentos) {
            const campos = new Set();
            const tipos = {};

            documentos.forEach(doc => {
                Object.keys(doc).forEach(campo => {
                    campos.add(campo);

                    const valor = doc[campo];
                    const tipo = Array.isArray(valor) ? 'array' : typeof valor;

                    if (!tipos[campo]) {
                        tipos[campo] = new Set();
                    }
                    tipos[campo].add(tipo);
                });
            });

            return {
                lista: Array.from(campos),
                tipos: Object.fromEntries(
                    Object.entries(tipos).map(([campo, tiposSet]) => [campo, Array.from(tiposSet)])
                )
            };
        }

        function calcularEstatisticas() {
            const stats = {
                totalColecoes: Object.keys(dadosCompletos.colecoes).length,
                totalDocumentos: 0,
                colecoesVazias: 0,
                colecoesComDados: 0,
                problemas: dadosCompletos.problemas.length
            };

            Object.values(dadosCompletos.colecoes).forEach(colecao => {
                stats.totalDocumentos += colecao.total;
                if (colecao.total === 0) {
                    stats.colecoesVazias++;
                } else {
                    stats.colecoesComDados++;
                }
            });

            dadosCompletos.estatisticas = stats;
        }

        window.analisarRelatorioMovimentacoes = async function() {
            console.log('🔍 Analisando como o relatório de movimentações funciona...');

            try {
                analiseRelatorio = {
                    timestamp: new Date().toISOString(),
                    funcionamento: {},
                    problemas: [],
                    recomendacoes: []
                };

                // Simular o carregamento do relatório de movimentações
                console.log('📊 Simulando carregamento do relatório...');

                // 1. Verificar movimentacoesEstoque
                const movEstoque = dadosCompletos.colecoes['movimentacoesEstoque'];
                let totalMovimentacoes = 0;
                let produtosUnicos = new Set();
                let tiposMovimentacao = {};

                if (movEstoque) {
                    totalMovimentacoes = movEstoque.total;
                    
                    // Analisar amostra para extrair informações
                    movEstoque.amostra.forEach(doc => {
                        if (doc.codigo) produtosUnicos.add(doc.codigo);
                        if (doc.tipo) {
                            tiposMovimentacao[doc.tipo] = (tiposMovimentacao[doc.tipo] || 0) + 1;
                        }
                    });

                    analiseRelatorio.funcionamento.movimentacoesEstoque = {
                        total: movEstoque.total,
                        campos: movEstoque.campos.lista,
                        amostra: movEstoque.amostra.map(doc => ({
                            id: doc.id,
                            tipo: doc.tipo,
                            dataMovimentacao: doc.dataMovimentacao,
                            numeroNotaFiscal: doc.numeroNotaFiscal,
                            codigo: doc.codigo
                        }))
                    };
                }

                // 2. Verificar recebimentosMateriais
                const recebimentos = dadosCompletos.colecoes['recebimentosMateriais'];
                if (recebimentos) {
                    analiseRelatorio.funcionamento.recebimentosMateriais = {
                        total: recebimentos.total,
                        campos: recebimentos.campos.lista,
                        amostra: recebimentos.amostra.map(doc => ({
                            id: doc.id,
                            numeroNotaFiscal: doc.numeroNotaFiscal,
                            dataRecebimento: doc.dataRecebimento,
                            itens: doc.itens?.length || 0
                        }))
                    };
                }

                // 3. Analisar problemas
                analisarProblemasRelatorio();

                // 4. Criar dados para o relatório
                const dadosRelatorio = {
                    totalMovimentacoes: totalMovimentacoes,
                    produtosUnicos: produtosUnicos.size,
                    periodoAnalise: `${new Date().toLocaleDateString('pt-BR')} - Análise atual`,
                    tiposMovimentacao: Object.entries(tiposMovimentacao).map(([nome, quantidade]) => ({
                        nome: nome || 'Sem tipo',
                        quantidade: quantidade
                    })),
                    problemas: analiseRelatorio.problemas.map(p => p.descricao),
                    recomendacoes: analiseRelatorio.recomendacoes
                };

                // 5. Mostrar análise
                mostrarAnaliseRelatorio(dadosRelatorio);

                console.log('✅ Análise do relatório concluída');

            } catch (error) {
                console.error('❌ Erro na análise do relatório:', error);
                alert('Erro ao analisar o relatório: ' + error.message);
            }
        };

    // Função para mostrar análise do relatório
    function mostrarAnaliseRelatorio(dados) {
        console.log('📊 Exibindo análise do relatório...');

        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 80%;
            max-height: 80%;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;

        content.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; color: #2c3e50;">📊 Análise do Relatório de Movimentações</h2>
                <button onclick="this.closest('.modal').remove()" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">✕</button>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="color: #34495e;">📈 Resumo Geral</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #3498db;">
                    <p><strong>Total de Movimentações:</strong> ${dados.totalMovimentacoes}</p>
                    <p><strong>Produtos Únicos:</strong> ${dados.produtosUnicos}</p>
                    <p><strong>Período Analisado:</strong> ${dados.periodoAnalise}</p>
                    <p><strong>Última Atualização:</strong> ${new Date().toLocaleString()}</p>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="color: #34495e;">🔍 Tipos de Movimentação</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    ${dados.tiposMovimentacao.map(tipo => `
                        <div style="background: #ecf0f1; padding: 10px; border-radius: 5px; text-align: center;">
                            <strong>${tipo.nome}</strong><br>
                            <span style="color: #27ae60; font-size: 18px;">${tipo.quantidade}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="color: #34495e;">⚠️ Análise de Problemas</h3>
                <div style="background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <ul style="margin: 0; padding-left: 20px;">
                        ${dados.problemas.map(problema => `<li>${problema}</li>`).join('')}
                    </ul>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h3 style="color: #34495e;">💡 Recomendações</h3>
                <div style="background: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #17a2b8;">
                    <ul style="margin: 0; padding-left: 20px;">
                        ${dados.recomendacoes.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        modal.appendChild(content);
        document.body.appendChild(modal);

        // Fechar modal ao clicar fora
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

        function analisarProblemasRelatorio() {
            const movEstoque = dadosCompletos.colecoes['movimentacoesEstoque'];
            const recebimentos = dadosCompletos.colecoes['recebimentosMateriais'];

            // Problema 1: Coleções vazias
            if (!movEstoque || movEstoque.total === 0) {
                analiseRelatorio.problemas.push({
                    tipo: 'COLECAO_VAZIA',
                    descricao: 'Coleção movimentacoesEstoque está vazia',
                    impacto: 'Relatório não mostra movimentações de estoque',
                    solucao: 'Verificar se movimentações estão sendo salvas corretamente'
                });
            }

            if (!recebimentos || recebimentos.total === 0) {
                analiseRelatorio.problemas.push({
                    tipo: 'COLECAO_VAZIA',
                    descricao: 'Coleção recebimentosMateriais está vazia',
                    impacto: 'Relatório não mostra recebimentos',
                    solucao: 'Verificar se recebimentos estão sendo salvos'
                });
            }

            // Problema 2: Campos inconsistentes
            if (movEstoque && recebimentos) {
                const camposMovEstoque = new Set(movEstoque.campos.lista);
                const camposRecebimentos = new Set(recebimentos.campos.lista);

                const camposComuns = [...camposMovEstoque].filter(campo => camposRecebimentos.has(campo));

                if (camposComuns.length < 3) {
                    analiseRelatorio.problemas.push({
                        tipo: 'CAMPOS_INCONSISTENTES',
                        descricao: 'Poucos campos em comum entre as coleções',
                        impacto: 'Dificuldade para unificar dados no relatório',
                        solucao: 'Padronizar campos entre coleções'
                    });
                }
            }

            // Recomendações
            analiseRelatorio.recomendacoes = [
                'Usar MaterialEntryService para padronizar movimentações',
                'Garantir que recebimentos criem movimentações correspondentes',
                'Implementar campos padrão (numeroNotaFiscal, dataMovimentacao, etc.)',
                'Adicionar logs de debug para rastrear problemas'
            ];
        }

        // ===================================================================
        // FUNÇÕES DE EXIBIÇÃO
        // ===================================================================

        function mostrarResultados() {
            // Mostrar métricas
            mostrarMetricas();

            // Mostrar resumo
            mostrarResumo();

            // Mostrar coleções
            mostrarColecoes();

            // Mostrar movimentações
            mostrarMovimentacoes();

            // Mostrar recebimentos
            mostrarRecebimentos();

            // Mostrar JSON
            mostrarJson();

            // Exibir resultados
            document.getElementById('resultados').style.display = 'block';
        }

        function mostrarMetricas() {
            const stats = dadosCompletos.estatisticas;

            const html = `
                <div class="metric-card primary">
                    <div class="metric-value">${stats.totalColecoes}</div>
                    <div class="metric-label">Total de Coleções</div>
                </div>
                <div class="metric-card success">
                    <div class="metric-value">${stats.totalDocumentos}</div>
                    <div class="metric-label">Total de Documentos</div>
                </div>
                <div class="metric-card warning">
                    <div class="metric-value">${stats.colecoesComDados}</div>
                    <div class="metric-label">Coleções com Dados</div>
                </div>
                <div class="metric-card danger">
                    <div class="metric-value">${stats.colecoesVazias}</div>
                    <div class="metric-label">Coleções Vazias</div>
                </div>
                <div class="metric-card info">
                    <div class="metric-value">${stats.problemas}</div>
                    <div class="metric-label">Problemas Encontrados</div>
                </div>
            `;

            document.getElementById('metricas').innerHTML = html;
            document.getElementById('metricas').style.display = 'grid';
        }

        function mostrarResumo() {
            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-pie"></i> Resumo Geral do Sistema
                    </div>
                    <div class="card-body">
                        <div class="collection-summary">
            `;

            Object.entries(dadosCompletos.colecoes).forEach(([nome, dados]) => {
                const cor = dados.total > 0 ? 'success' : 'danger';
                html += `
                    <div class="summary-item">
                        <div class="summary-value" style="color: ${dados.total > 0 ? '#27ae60' : '#e74c3c'}">${dados.total}</div>
                        <div class="summary-label">${nome}</div>
                    </div>
                `;
            });

            html += `
                        </div>

                        <h4>📈 Análise por Categoria:</h4>
                        <div class="collection-summary">
            `;

            // Categorizar coleções
            const categorias = {
                'Estoque e Movimentações': ['ajustesEstoque', 'estoques', 'movimentacoesEstoque', 'movimentacoes_estoque', 'transferenciasArmazem'],
                'Compras': ['solicitacoesCompra', 'cotacoes', 'pedidosCompra', 'recebimentoMateriais', 'recebimentos', 'recebimentosDetalhes', 'recebimentosMateriais'],
                'Produção': ['ordensProducao', 'apontamentos', 'operacoes', 'estruturas', 'recursos'],
                'Qualidade': ['estoqueQualidade', 'especificacoesInspecao', 'avaliacoesFornecedores'],
                'Cadastros Básicos': ['produtos', 'fornecedores', 'clientes', 'armazens', 'familias', 'grupos', 'usuarios', 'empresa'],
                'Configurações': ['parametros', 'configuracoes', 'centrosCusto', 'condicoesPagamento', 'cadastroTES', 'permissoes', 'contadores', 'sequenciais'],
                'Auditoria e Logs': ['auditoria', 'auditoriaMovimentacoes', 'logsAlteracoes', 'logsAtividades', 'logsAuditoria', 'log_cotacoes', 'log_envios', 'historico_produtos'],
                'Especiais': ['cancelamentosNecessidades', 'produtos_fornecedores', 'recomendacoesIA', 'orcamentos', 'emails', 'backups', 'central', 'relatoriosGerados']
            };

            Object.entries(categorias).forEach(([categoria, colecoesCategoria]) => {
                const totalColecoes = colecoesCategoria.length;
                const colecoesComDados = colecoesCategoria.filter(nome => 
                    dadosCompletos.colecoes[nome] && dadosCompletos.colecoes[nome].total > 0
                ).length;
                const percentual = totalColecoes > 0 ? ((colecoesComDados / totalColecoes) * 100).toFixed(1) : 0;

                html += `
                    <div class="summary-item">
                        <div class="summary-value" style="color: ${colecoesComDados > 0 ? '#27ae60' : '#e74c3c'}">${colecoesComDados}/${totalColecoes}</div>
                        <div class="summary-label">${categoria}</div>
                        <div style="font-size: 10px; color: #6c757d;">${percentual}% ativas</div>
                    </div>
                `;
            });

            html += `
                        </div>

                        <h4>📊 Análise por Coleção:</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Coleção</th>
                                    <th>Documentos</th>
                                    <th>Campos</th>
                                    <th>Status</th>
                                    <th>Última Atualização</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            Object.entries(dadosCompletos.colecoes).forEach(([nome, dados]) => {
                const status = dados.total > 0 ?
                    `<span style="color: #27ae60;">✅ Ativa</span>` :
                    `<span style="color: #e74c3c;">❌ Vazia</span>`;

                html += `
                    <tr>
                        <td><strong>${nome}</strong></td>
                        <td>${dados.total}</td>
                        <td>${dados.campos.lista.length}</td>
                        <td>${status}</td>
                        <td>${new Date(dados.ultimaAtualizacao).toLocaleString('pt-BR')}</td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            if (dadosCompletos.problemas.length > 0) {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-exclamation-triangle"></i> Problemas Encontrados
                        </div>
                        <div class="card-body">
                `;

                dadosCompletos.problemas.forEach(problema => {
                    html += `
                        <div class="alert alert-warning">
                            <strong>${problema.colecao}:</strong> ${problema.erro}
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            document.getElementById('resumo').innerHTML = html;
        }

        function mostrarColecoes() {
            let html = '';

            Object.entries(dadosCompletos.colecoes).forEach(([nome, dados]) => {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-database"></i> ${nome} (${dados.total} documentos)
                        </div>
                        <div class="card-body">
                            <h5>📋 Campos Disponíveis (${dados.campos.lista.length}):</h5>
                            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin-bottom: 20px;">
                `;

                dados.campos.lista.forEach(campo => {
                    const tipos = dados.campos.tipos[campo] || ['unknown'];
                    html += `
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; font-size: 12px;">
                            <strong>${campo}</strong><br>
                            <small style="color: #6c757d;">${tipos.join(', ')}</small>
                        </div>
                    `;
                });

                html += `
                            </div>

                            <h5>📄 Amostra de Documentos:</h5>
                            <div class="json-viewer" style="max-height: 300px;">
                                ${JSON.stringify(dados.amostra, null, 2)}
                            </div>
                        </div>
                    </div>
                `;
            });

            document.getElementById('colecoes').innerHTML = html;
        }

        function mostrarMovimentacoes() {
            const movEstoque = dadosCompletos.colecoes['movimentacoesEstoque'];

            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-exchange-alt"></i> Análise de Movimentações de Estoque
                    </div>
                    <div class="card-body">
            `;

            if (!movEstoque || movEstoque.total === 0) {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Coleção movimentacoesEstoque está vazia!</strong><br>
                        Isso explica por que o relatório de movimentações não mostra dados.
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Encontradas ${movEstoque.total} movimentações</strong>
                    </div>

                    <h5>📊 Análise dos Dados:</h5>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tipo</th>
                                <th>Código</th>
                                <th>Quantidade</th>
                                <th>Data</th>
                                <th>NF</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                movEstoque.amostra.forEach(mov => {
                    html += `
                        <tr>
                            <td>${mov.id.substring(0, 8)}...</td>
                            <td>${mov.tipo || 'N/A'}</td>
                            <td>${mov.codigo || 'N/A'}</td>
                            <td>${mov.quantidade || 'N/A'}</td>
                            <td>${mov.dataMovimentacao ? new Date(mov.dataMovimentacao.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'}</td>
                            <td>${mov.numeroNotaFiscal || 'N/A'}</td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            document.getElementById('movimentacoes').innerHTML = html;
        }

        function mostrarRecebimentos() {
            const recebimentos = dadosCompletos.colecoes['recebimentosMateriais'];

            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-inbox"></i> Análise de Recebimentos de Materiais
                    </div>
                    <div class="card-body">
            `;

            if (!recebimentos || recebimentos.total === 0) {
                html += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Coleção recebimentosMateriais está vazia!</strong><br>
                        Isso indica que nenhum recebimento foi processado ou há problema no salvamento.
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Encontrados ${recebimentos.total} recebimentos</strong>
                    </div>

                    <h5>📊 Análise dos Dados:</h5>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>NF</th>
                                <th>Data</th>
                                <th>Itens</th>
                                <th>Valor</th>
                                <th>Usuário</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                recebimentos.amostra.forEach(rec => {
                    html += `
                        <tr>
                            <td>${rec.id.substring(0, 8)}...</td>
                            <td>${rec.numeroNotaFiscal || rec.numeroNF || 'N/A'}</td>
                            <td>${rec.dataRecebimento ? new Date(rec.dataRecebimento.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'}</td>
                            <td>${rec.itens?.length || 0}</td>
                            <td>R$ ${(rec.valorTotal || 0).toFixed(2)}</td>
                            <td>${rec.usuarioRecebimento || rec.recebidoPor || 'N/A'}</td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            document.getElementById('recebimentos').innerHTML = html;
        }

        function mostrarJson() {
            document.getElementById('jsonViewer').textContent = JSON.stringify(dadosCompletos, null, 2);
        }

        // ===================================================================
        // FUNÇÕES UTILITÁRIAS
        // ===================================================================

        window.mostrarTab = function(tabId) {
            // Esconder todas as tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Mostrar tab selecionada
            document.getElementById(tabId).classList.add('active');
            event.target.classList.add('active');
        };

        function mostrarLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function mostrarAlerta(mensagem, tipo = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${tipo}`;
            alertDiv.innerHTML = `
                <i class="fas fa-${tipo === 'danger' ? 'exclamation-circle' :
                                   tipo === 'warning' ? 'exclamation-triangle' :
                                   tipo === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${mensagem}
            `;

            const content = document.querySelector('.content');
            content.insertBefore(alertDiv, content.firstChild);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        window.compararColecoes = function() {
            const movEstoque = dadosCompletos.colecoes['movimentacoesEstoque'];
            const recebimentos = dadosCompletos.colecoes['recebimentosMateriais'];

            let html = `
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-balance-scale"></i> Comparação entre Coleções
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <h5>📦 movimentacoesEstoque</h5>
                                <p><strong>Total:</strong> ${movEstoque?.total || 0}</p>
                                <p><strong>Campos:</strong> ${movEstoque?.campos.lista.length || 0}</p>
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;">
                                    ${movEstoque?.campos.lista.join(', ') || 'Nenhum campo'}
                                </div>
                            </div>
                            <div>
                                <h5>📥 recebimentosMateriais</h5>
                                <p><strong>Total:</strong> ${recebimentos?.total || 0}</p>
                                <p><strong>Campos:</strong> ${recebimentos?.campos.lista.length || 0}</p>
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;">
                                    ${recebimentos?.campos.lista.join(', ') || 'Nenhum campo'}
                                </div>
                            </div>
                        </div>

                        <h5 style="margin-top: 20px;">🔍 Análise de Compatibilidade:</h5>
            `;

            if (movEstoque && recebimentos) {
                const camposMovEstoque = new Set(movEstoque.campos.lista);
                const camposRecebimentos = new Set(recebimentos.campos.lista);
                const camposComuns = [...camposMovEstoque].filter(campo => camposRecebimentos.has(campo));

                html += `
                    <div class="alert alert-info">
                        <strong>Campos em comum:</strong> ${camposComuns.length}<br>
                        <small>${camposComuns.join(', ')}</small>
                    </div>
                `;

                if (camposComuns.includes('numeroNotaFiscal')) {
                    html += `<div class="alert alert-success">✅ Campo numeroNotaFiscal presente em ambas - busca deve funcionar</div>`;
                } else {
                    html += `<div class="alert alert-danger">❌ Campo numeroNotaFiscal não está em ambas - busca pode falhar</div>`;
                }
            } else {
                html += `<div class="alert alert-warning">⚠️ Uma ou ambas as coleções estão vazias - não é possível comparar</div>`;
            }

            html += `
                    </div>
                </div>
            `;

            document.getElementById('comparacao').innerHTML = html;
            mostrarTab('comparacao');
        };

        window.buscarInconsistencias = function() {
            console.log('🔍 Buscando inconsistências...');
            // Implementar busca de inconsistências
            mostrarAlerta('Função de busca de inconsistências em desenvolvimento', 'info');
        };

        window.exportarTudo = function() {
            const dados = {
                timestamp: new Date().toISOString(),
                dadosCompletos: dadosCompletos,
                analiseRelatorio: analiseRelatorio
            };

            const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `relatorio_completo_colecoes_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
            mostrarAlerta('Dados exportados com sucesso!', 'success');
        };

        window.atualizarTudo = function() {
            carregarTodasColecoes();
        };

        window.filtrarJson = function(termo) {
            const jsonViewer = document.getElementById('jsonViewer');
            const textoOriginal = JSON.stringify(dadosCompletos, null, 2);

            if (!termo) {
                jsonViewer.textContent = textoOriginal;
                return;
            }

            const linhas = textoOriginal.split('\n');
            const linhasFiltradas = linhas.filter(linha =>
                linha.toLowerCase().includes(termo.toLowerCase())
            );

            jsonViewer.textContent = linhasFiltradas.join('\n');
        };

        // Inicialização automática
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Relatório Completo de Coleções carregado');
            mostrarAlerta('Clique em "Carregar Todas as Coleções" para começar a análise', 'info');
        });
    </script>
</body>
</html>