// Serviço para gerenciar fluxos de trabalho do sistema
import { db } from '../firebase-config.js';
import { 
  collection, 
  doc, 
  updateDoc, 
  addDoc, 
  Timestamp,
  writeBatch,
  arrayUnion,
  query,
  where,
  getDocs,
  increment,
  getDoc
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class WorkflowService {
  // Gerencia rejeições de documentos (orçamentos, pedidos, etc)
  static async handleRejection(params) {
    const { 
      documentType,
      documentId,
      motivo,
      observacoes,
      usuarioId,
      usuarioNome,
      nivelAprovacao
    } = params;

    const batch = writeBatch(db);

    try {
      const docRef = doc(db, `${documentType}s`, documentId);

      // Atualiza status e adiciona histórico
      batch.update(docRef, {
        status: 'Rejeitado',
        dataRejeicao: Timestamp.now(),
        motivoRejeicao: motivo,
        observacoesRejeicao: observacoes,
        rejeitadoPor: {
          id: usuarioId,
          nome: usuarioNome,
          nivel: nivelAprovacao
        },
        historico: arrayUnion({
          data: Timestamp.now(),
          acao: 'Rejeitado',
          motivo,
          observacoes,
          usuario: {
            id: usuarioId,
            nome: usuarioNome
          }
        })
      });

      await batch.commit();
      return { success: true };
    } catch (error) {
      console.error("Erro ao processar rejeição:", error);
      throw error;
    }
  }

  // Gerencia cancelamentos
  static async handleCancellation(params) {
    const {
      documentType,
      documentId,
      motivo,
      observacoes,
      usuarioId,
      usuarioNome
    } = params;

    const batch = writeBatch(db);

    try {
      const docRef = doc(db, `${documentType}s`, documentId);

      // Atualiza status e adiciona histórico
      batch.update(docRef, {
        status: 'Cancelado',
        dataCancelamento: Timestamp.now(),
        motivoCancelamento: motivo,
        observacoesCancelamento: observacoes,
        canceladoPor: {
          id: usuarioId,
          nome: usuarioNome
        },
        historico: arrayUnion({
          data: Timestamp.now(),
          acao: 'Cancelado',
          motivo,
          observacoes,
          usuario: {
            id: usuarioId,
            nome: usuarioNome
          }
        })
      });

      await batch.commit();
      return { success: true };
    } catch (error) {
      console.error("Erro ao processar cancelamento:", error);
      throw error;
    }
  }

  // Gerencia alertas críticos do sistema
  static async checkCriticalAlerts() {
    try {
      const batch = writeBatch(db);
      const alertsToCreate = [];

      // Verifica limite de crédito dos clientes
      const clientesSnapshot = await getDocs(collection(db, "fornecedores"));
      const clientes = clientesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      for (const cliente of clientes) {
        const limiteInfo = await this.verificarLimiteCredito(cliente.id);

        if (limiteInfo && limiteInfo.limiteExcedido) {
          alertsToCreate.push({
            tipo: 'LIMITE_CREDITO',
            severidade: 'CRITICA',
            titulo: `Limite de Crédito Excedido - ${cliente.nome || cliente.razaoSocial}`,
            mensagem: `O cliente excedeu seu limite de crédito. Limite: R$ ${limiteInfo.limiteTotal.toFixed(2)}, Utilizado: R$ ${limiteInfo.limiteUtilizado.toFixed(2)}`,
            cliente: {
              id: cliente.id,
              nome: cliente.nome || cliente.razaoSocial
            },
            dataCriacao: Timestamp.now(),
            status: 'ATIVO',
            acaoRequerida: true
          });
        }
      }

      // Cria os alertas no banco
      for (const alerta of alertsToCreate) {
        const alertaRef = doc(collection(db, "alertas"));
        batch.set(alertaRef, alerta);
      }

      await batch.commit();
    } catch (error) {
      console.error('Erro ao verificar alertas críticos:', error);
    }
  }

  static async verificarLimiteCredito(clienteId) {
    try {
      const clienteRef = doc(db, "fornecedores", clienteId);
      const clienteDoc = await getDoc(clienteRef);

      if (!clienteDoc.exists()) {
        return null;
      }

      const cliente = clienteDoc.data();
      const limiteTotal = cliente.limiteCredito || 0;

      return {
        limiteTotal,
        limiteUtilizado: 0,
        limiteDisponivel: limiteTotal,
        limiteExcedido: false
      };
    } catch (error) {
      console.error('Erro ao verificar limite de crédito:', error);
      return null;
    }
  }
}