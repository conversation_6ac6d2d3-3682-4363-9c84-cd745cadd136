<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Cadastro de TES - Tipos de Entrada e Saída</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/sistema-padronizado.css">
    <style>
        /* Estilos específicos para TES */
        .cfop-integration {
            background: var(--info-light);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-top: var(--spacing-lg);
            border-left: 4px solid var(--info-color);
        }

        .cfop-tag {
            background: var(--info-color);
            color: var(--text-white);
            padding: 4px 8px;
            border-radius: var(--border-radius-xl);
            font-size: var(--font-size-xs);
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-exchange-alt"></i>
                Cadastro de TES
            </h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openModal()">
                    <i class="fas fa-plus"></i>
                    Novo TES
                </button>
                <button class="btn btn-warning" onclick="importarTESPadrao()">
                    <i class="fas fa-download"></i>
                    Importar Padrão
                </button>
                <a href="auditoria-tes.html" class="btn btn-secondary">
                    <i class="fas fa-search"></i>
                    Auditoria
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Alertas do Sistema -->
            <div id="systemAlerts"></div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTES">0</div>
                    <div class="stat-label">Total de TES</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number" id="ativosTES">0</div>
                    <div class="stat-label">TES Ativos</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="entradaTES">0</div>
                    <div class="stat-label">TES Entrada</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number" id="saidaTES">0</div>
                    <div class="stat-label">TES Saída</div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters-section">
                <h3><i class="fas fa-filter"></i> Filtros</h3>
                <div class="filters-row">
                    <div class="form-group">
                        <label>Buscar TES</label>
                        <input type="text" class="form-control" id="searchTES" placeholder="Código, descrição ou CFOP..." onkeyup="filterTES()">
                    </div>
                    <div class="form-group">
                        <label>Tipo de Movimento</label>
                        <select class="form-control" id="filterTipo" onchange="filterTES()">
                            <option value="">Todos os tipos</option>
                            <option value="ENTRADA">Entrada</option>
                            <option value="SAIDA">Saída</option>
                            <option value="AMBOS">Ambos</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Categoria</label>
                        <select class="form-control" id="filterCategoria" onchange="filterTES()">
                            <option value="">Todas as categorias</option>
                            <option value="COMPRA">Compra</option>
                            <option value="VENDA">Venda</option>
                            <option value="PRODUCAO">Produção</option>
                            <option value="CONSUMO">Consumo</option>
                            <option value="AJUSTE">Ajuste</option>
                            <option value="TRANSFERENCIA">Transferência</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control" id="filterStatus" onchange="filterTES()">
                            <option value="">Todos</option>
                            <option value="true">Ativos</option>
                            <option value="false">Inativos</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Limpar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabela de TES -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Código</th>
                            <th>Descrição</th>
                            <th style="width: 100px;">Tipo</th>
                            <th style="width: 120px;">Categoria</th>
                            <th style="width: 80px;">CFOP</th>
                            <th style="width: 100px;">Atualiza Estoque</th>
                            <th style="width: 80px;">Status</th>
                            <th style="width: 120px;">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="tesTableBody">
                        <!-- TES serão carregados dinamicamente -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Cadastro/Edição -->
    <div id="tesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">
                    <i class="fas fa-plus"></i>
                    Novo TES
                </h2>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="tesForm">
                    <input type="hidden" id="editingId" value="">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="codigo">
                                <i class="fas fa-hashtag"></i>
                                Código TES <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="codigo" required maxlength="3" placeholder="Ex: 001">
                            <small class="form-text text-muted">Código de 3 dígitos único</small>
                        </div>
                        <div class="form-group">
                            <label for="categoria">
                                <i class="fas fa-tags"></i>
                                Categoria <span class="required">*</span>
                            </label>
                            <select class="form-control" id="categoria" required onchange="updateCFOPSuggestions()">
                                <option value="">Selecione a categoria</option>
                                <option value="COMPRA">Compra</option>
                                <option value="VENDA">Venda</option>
                                <option value="PRODUCAO">Produção</option>
                                <option value="CONSUMO">Consumo</option>
                                <option value="AJUSTE">Ajuste</option>
                                <option value="TRANSFERENCIA">Transferência</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="descricao">
                                <i class="fas fa-align-left"></i>
                                Descrição <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="descricao" required maxlength="100" placeholder="Ex: Compra para estoque">
                        </div>
                        <div class="form-group">
                            <label for="tipo">
                                <i class="fas fa-exchange-alt"></i>
                                Tipo de Movimento <span class="required">*</span>
                            </label>
                            <select class="form-control" id="tipo" required>
                                <option value="">Selecione o tipo</option>
                                <option value="ENTRADA">Entrada</option>
                                <option value="SAIDA">Saída</option>
                                <option value="AMBOS">Ambos (Entrada/Saída)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="cfop">
                                <i class="fas fa-file-invoice"></i>
                                CFOP <span class="required">*</span>
                            </label>
                            <input type="text" class="form-control" id="cfop" required maxlength="4" placeholder="Ex: 1102">
                            <small class="form-text text-muted">Código Fiscal de Operações e Prestações</small>
                        </div>
                        <div class="form-group">
                            <label for="naturezaOperacao">
                                <i class="fas fa-info-circle"></i>
                                Natureza da Operação
                            </label>
                            <input type="text" class="form-control" id="naturezaOperacao" maxlength="60" placeholder="Ex: Compra para comercialização">
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-cog"></i> Configurações de Controle</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="atualizaEstoque" checked>
                                    <label for="atualizaEstoque">
                                        <i class="fas fa-boxes"></i>
                                        Atualiza Estoque
                                    </label>
                                </div>
                                <small class="form-text text-muted">Define se movimentações com este TES atualizam o estoque</small>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="permiteEstoqueNegativo">
                                    <label for="permiteEstoqueNegativo">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Permite Estoque Negativo
                                    </label>
                                </div>
                                <small class="form-text text-muted">Permite que o estoque fique negativo com este TES</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="exigeNotaFiscal">
                                    <label for="exigeNotaFiscal">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                        Exige Nota Fiscal
                                    </label>
                                </div>
                                <small class="form-text text-muted">Torna obrigatório informar número da nota fiscal</small>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="exigeFornecedor">
                                    <label for="exigeFornecedor">
                                        <i class="fas fa-building"></i>
                                        Exige Fornecedor/Cliente
                                    </label>
                                </div>
                                <small class="form-text text-muted">Torna obrigatório informar fornecedor ou cliente</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="exigeOrdemProducao">
                                    <label for="exigeOrdemProducao">
                                        <i class="fas fa-industry"></i>
                                        Exige Ordem de Produção
                                    </label>
                                </div>
                                <small class="form-text text-muted">Torna obrigatório informar ordem de produção</small>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="exigeJustificativa">
                                    <label for="exigeJustificativa">
                                        <i class="fas fa-comment"></i>
                                        Exige Justificativa
                                    </label>
                                </div>
                                <small class="form-text text-muted">Torna obrigatório informar justificativa/observação</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-calculator"></i> Configurações Fiscais</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="icms">
                                    <i class="fas fa-percentage"></i>
                                    ICMS (%)
                                </label>
                                <input type="number" class="form-control" id="icms" step="0.01" min="0" max="100" value="0">
                            </div>
                            <div class="form-group">
                                <label for="ipi">
                                    <i class="fas fa-percentage"></i>
                                    IPI (%)
                                </label>
                                <input type="number" class="form-control" id="ipi" step="0.01" min="0" max="100" value="0">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="pis">
                                    <i class="fas fa-percentage"></i>
                                    PIS (%)
                                </label>
                                <input type="number" class="form-control" id="pis" step="0.01" min="0" max="100" value="0">
                            </div>
                            <div class="form-group">
                                <label for="cofins">
                                    <i class="fas fa-percentage"></i>
                                    COFINS (%)
                                </label>
                                <input type="number" class="form-control" id="cofins" step="0.01" min="0" max="100" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="observacoes">
                                <i class="fas fa-sticky-note"></i>
                                Observações
                            </label>
                            <textarea class="form-control" id="observacoes" rows="3" placeholder="Observações adicionais sobre este TES..."></textarea>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="ativo" checked>
                                <label for="ativo">
                                    <i class="fas fa-toggle-on"></i>
                                    TES Ativo
                                </label>
                            </div>
                            <small class="form-text text-muted">TES inativos não aparecem nas seleções</small>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            Salvar TES
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import {
            getFirestore,
            collection,
            addDoc,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            query,
            orderBy,
            where,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Configuração do Firebase
        import { firebaseConfig } from './firebase-config.js';
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Variáveis globais
        let tesList = [];
        let filteredTES = [];
        let editingId = null;

        // ===== CARREGAMENTO DE DADOS =====
        async function loadTES() {
            try {
                console.log('📋 Carregando TES do Firebase...');
                const tesSnapshot = await getDocs(query(collection(db, "tes"), orderBy("codigo")));
                tesList = tesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ ${tesList.length} TES carregados`);

                // Se não há TES cadastrados, importar os padrão
                if (tesList.length === 0) {
                    console.log('📥 Nenhum TES encontrado, importando TES padrão...');
                    await importarTESPadrao();
                    return;
                }

                filteredTES = [...tesList];
                renderTESTable();
                updateStatistics();

            } catch (error) {
                console.error('❌ Erro ao carregar TES:', error);
                showNotification('Erro ao carregar TES: ' + error.message, 'error');
            }
        }

        // ===== RENDERIZAÇÃO DA TABELA =====
        function renderTESTable() {
            const tbody = document.getElementById('tesTableBody');

            if (filteredTES.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                            Nenhum TES encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredTES.map(tes => `
                <tr>
                    <td><strong>${tes.codigo}</strong></td>
                    <td>${tes.descricao}</td>
                    <td>
                        <span class="badge badge-${tes.tipo.toLowerCase()}">
                            ${tes.tipo}
                        </span>
                    </td>
                    <td>${tes.categoria}</td>
                    <td>${tes.cfop || '-'}</td>
                    <td>
                        <span class="badge ${tes.atualizaEstoque ? 'badge-ativo' : 'badge-inativo'}">
                            ${tes.atualizaEstoque ? 'Sim' : 'Não'}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${tes.ativo ? 'badge-ativo' : 'badge-inativo'}">
                            ${tes.ativo ? 'Ativo' : 'Inativo'}
                        </span>
                    </td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-secondary btn-sm" onclick="editTES('${tes.id}')" title="Editar">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteTES('${tes.id}')" title="Excluir">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // ===== ESTATÍSTICAS =====
        function updateStatistics() {
            const total = tesList.length;
            const ativos = tesList.filter(t => t.ativo).length;
            const entrada = tesList.filter(t => t.tipo === 'ENTRADA').length;
            const saida = tesList.filter(t => t.tipo === 'SAIDA').length;

            document.getElementById('totalTES').textContent = total;
            document.getElementById('ativosTES').textContent = ativos;
            document.getElementById('entradaTES').textContent = entrada;
            document.getElementById('saidaTES').textContent = saida;
        }

        // ===== FILTROS =====
        window.filterTES = function() {
            const searchText = document.getElementById('searchTES').value.toLowerCase();
            const filterTipo = document.getElementById('filterTipo').value;
            const filterCategoria = document.getElementById('filterCategoria').value;
            const filterStatus = document.getElementById('filterStatus').value;

            filteredTES = tesList.filter(tes => {
                const matchSearch = !searchText ||
                    tes.codigo.toLowerCase().includes(searchText) ||
                    tes.descricao.toLowerCase().includes(searchText) ||
                    (tes.cfop && tes.cfop.includes(searchText));

                const matchTipo = !filterTipo || tes.tipo === filterTipo;
                const matchCategoria = !filterCategoria || tes.categoria === filterCategoria;
                const matchStatus = filterStatus === '' || tes.ativo.toString() === filterStatus;

                return matchSearch && matchTipo && matchCategoria && matchStatus;
            });

            renderTESTable();
        };

        window.clearFilters = function() {
            document.getElementById('searchTES').value = '';
            document.getElementById('filterTipo').value = '';
            document.getElementById('filterCategoria').value = '';
            document.getElementById('filterStatus').value = '';
            filteredTES = [...tesList];
            renderTESTable();
        };

        // ===== MODAL =====
        window.openModal = function(tesId = null) {
            editingId = tesId;
            const modal = document.getElementById('tesModal');
            const modalTitle = document.getElementById('modalTitle');
            const form = document.getElementById('tesForm');

            if (tesId) {
                const tes = tesList.find(t => t.id === tesId);
                if (tes) {
                    modalTitle.innerHTML = '<i class="fas fa-edit"></i> Editar TES';
                    fillForm(tes);
                }
            } else {
                modalTitle.innerHTML = '<i class="fas fa-plus"></i> Novo TES';
                form.reset();
                // Valores padrão
                document.getElementById('atualizaEstoque').checked = true;
                document.getElementById('ativo').checked = true;
            }

            modal.style.display = 'block';
        };

        window.closeModal = function() {
            document.getElementById('tesModal').style.display = 'none';
            editingId = null;
        };

        // ===== PREENCHIMENTO DO FORMULÁRIO =====
        function fillForm(tes) {
            document.getElementById('editingId').value = tes.id;
            document.getElementById('codigo').value = tes.codigo;
            document.getElementById('categoria').value = tes.categoria;
            document.getElementById('descricao').value = tes.descricao;
            document.getElementById('tipo').value = tes.tipo;
            document.getElementById('cfop').value = tes.cfop || '';
            document.getElementById('naturezaOperacao').value = tes.naturezaOperacao || '';
            document.getElementById('atualizaEstoque').checked = tes.atualizaEstoque;
            document.getElementById('permiteEstoqueNegativo').checked = tes.permiteEstoqueNegativo || false;
            document.getElementById('exigeNotaFiscal').checked = tes.exigeNotaFiscal || false;
            document.getElementById('exigeFornecedor').checked = tes.exigeFornecedor || false;
            document.getElementById('exigeOrdemProducao').checked = tes.exigeOrdemProducao || false;
            document.getElementById('exigeJustificativa').checked = tes.exigeJustificativa || false;
            document.getElementById('icms').value = tes.icms || 0;
            document.getElementById('ipi').value = tes.ipi || 0;
            document.getElementById('pis').value = tes.pis || 0;
            document.getElementById('cofins').value = tes.cofins || 0;
            document.getElementById('observacoes').value = tes.observacoes || '';
            document.getElementById('ativo').checked = tes.ativo;
        }

        // ===== CRUD OPERATIONS =====
        document.getElementById('tesForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            try {
                const formData = {
                    codigo: document.getElementById('codigo').value.trim(),
                    categoria: document.getElementById('categoria').value,
                    descricao: document.getElementById('descricao').value.trim(),
                    tipo: document.getElementById('tipo').value,
                    cfop: document.getElementById('cfop').value.trim(),
                    naturezaOperacao: document.getElementById('naturezaOperacao').value.trim(),
                    atualizaEstoque: document.getElementById('atualizaEstoque').checked,
                    permiteEstoqueNegativo: document.getElementById('permiteEstoqueNegativo').checked,
                    exigeNotaFiscal: document.getElementById('exigeNotaFiscal').checked,
                    exigeFornecedor: document.getElementById('exigeFornecedor').checked,
                    exigeOrdemProducao: document.getElementById('exigeOrdemProducao').checked,
                    exigeJustificativa: document.getElementById('exigeJustificativa').checked,
                    icms: parseFloat(document.getElementById('icms').value) || 0,
                    ipi: parseFloat(document.getElementById('ipi').value) || 0,
                    pis: parseFloat(document.getElementById('pis').value) || 0,
                    cofins: parseFloat(document.getElementById('cofins').value) || 0,
                    observacoes: document.getElementById('observacoes').value.trim(),
                    ativo: document.getElementById('ativo').checked,
                    dataAtualizacao: Timestamp.now()
                };

                // Validações
                if (!formData.codigo || !formData.categoria || !formData.descricao || !formData.tipo) {
                    showNotification('Preencha todos os campos obrigatórios', 'error');
                    return;
                }

                // Verificar se código já existe (apenas para novos TES)
                if (!editingId) {
                    const existingTES = tesList.find(t => t.codigo === formData.codigo);
                    if (existingTES) {
                        showNotification('Já existe um TES com este código', 'error');
                        return;
                    }
                }

                if (editingId) {
                    // Atualizar TES existente
                    await updateDoc(doc(db, "tes", editingId), formData);
                    showNotification('TES atualizado com sucesso!', 'success');
                } else {
                    // Criar novo TES
                    formData.dataCriacao = Timestamp.now();
                    await addDoc(collection(db, "tes"), formData);
                    showNotification('TES cadastrado com sucesso!', 'success');
                }

                closeModal();
                await loadTES();

            } catch (error) {
                console.error('❌ Erro ao salvar TES:', error);
                showNotification('Erro ao salvar TES: ' + error.message, 'error');
            }
        });

        window.editTES = function(tesId) {
            openModal(tesId);
        };

        window.deleteTES = async function(tesId) {
            const tes = tesList.find(t => t.id === tesId);
            if (!tes) return;

            if (!confirm(`Tem certeza que deseja excluir o TES ${tes.codigo} - ${tes.descricao}?`)) {
                return;
            }

            try {
                await deleteDoc(doc(db, "tes", tesId));
                showNotification('TES excluído com sucesso!', 'success');
                await loadTES();
            } catch (error) {
                console.error('❌ Erro ao excluir TES:', error);
                showNotification('Erro ao excluir TES: ' + error.message, 'error');
            }
        };

        // ===== IMPORTAÇÃO DE TES PADRÃO =====
        window.importarTESPadrao = async function() {
            if (tesList.length > 0) {
                if (!confirm('Já existem TES cadastrados. Deseja importar os TES padrão mesmo assim? Isso não irá duplicar códigos existentes.')) {
                    return;
                }
            }

            try {
                console.log('📥 Importando TES padrão...');

                const tessPadrao = getTESPadrao();
                let importados = 0;
                let ignorados = 0;

                for (const tes of tessPadrao) {
                    // Verificar se já existe
                    const existingTES = tesList.find(t => t.codigo === tes.codigo);
                    if (existingTES) {
                        ignorados++;
                        continue;
                    }

                    // Adicionar TES
                    await addDoc(collection(db, "tes"), {
                        ...tes,
                        dataCriacao: Timestamp.now(),
                        dataAtualizacao: Timestamp.now()
                    });
                    importados++;
                }

                showNotification(`Importação concluída! ${importados} TES importados, ${ignorados} ignorados (já existiam)`, 'success');
                await loadTES();

            } catch (error) {
                console.error('❌ Erro ao importar TES padrão:', error);
                showNotification('Erro ao importar TES padrão: ' + error.message, 'error');
            }
        };

        // ===== TES PADRÃO =====
        function getTESPadrao() {
            return [
                // COMPRAS
                {
                    codigo: '001',
                    categoria: 'COMPRA',
                    descricao: 'Compra para estoque',
                    tipo: 'ENTRADA',
                    cfop: '1102',
                    naturezaOperacao: 'Compra para comercialização',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 18,
                    ipi: 0,
                    pis: 1.65,
                    cofins: 7.6,
                    ativo: true
                },
                {
                    codigo: '002',
                    categoria: 'COMPRA',
                    descricao: 'Compra para consumo',
                    tipo: 'ENTRADA',
                    cfop: '1556',
                    naturezaOperacao: 'Compra de material para uso ou consumo',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 18,
                    ipi: 0,
                    pis: 1.65,
                    cofins: 7.6,
                    ativo: true
                },
                {
                    codigo: '003',
                    categoria: 'COMPRA',
                    descricao: 'Devolução de venda',
                    tipo: 'ENTRADA',
                    cfop: '1202',
                    naturezaOperacao: 'Devolução de venda de produção do estabelecimento',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '004',
                    categoria: 'COMPRA',
                    descricao: 'Compra para ativo imobilizado',
                    tipo: 'ENTRADA',
                    cfop: '1551',
                    naturezaOperacao: 'Compra de bem para o ativo imobilizado',
                    atualizaEstoque: false,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 18,
                    ipi: 0,
                    pis: 1.65,
                    cofins: 7.6,
                    ativo: true
                },

                // VENDAS
                {
                    codigo: '500',
                    categoria: 'VENDA',
                    descricao: 'Venda de produto',
                    tipo: 'SAIDA',
                    cfop: '5102',
                    naturezaOperacao: 'Venda de produção do estabelecimento',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 18,
                    ipi: 0,
                    pis: 1.65,
                    cofins: 7.6,
                    ativo: true
                },
                {
                    codigo: '501',
                    categoria: 'VENDA',
                    descricao: 'Remessa para beneficiamento',
                    tipo: 'SAIDA',
                    cfop: '5124',
                    naturezaOperacao: 'Remessa para industrialização por encomenda',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '502',
                    categoria: 'VENDA',
                    descricao: 'Devolução de compra',
                    tipo: 'SAIDA',
                    cfop: '5202',
                    naturezaOperacao: 'Devolução de compra para comercialização',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: true,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },

                // PRODUÇÃO
                {
                    codigo: '200',
                    categoria: 'PRODUCAO',
                    descricao: 'Entrada por produção',
                    tipo: 'ENTRADA',
                    cfop: '5101',
                    naturezaOperacao: 'Entrada de produção do estabelecimento',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: true,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '201',
                    categoria: 'PRODUCAO',
                    descricao: 'Consumo de componentes',
                    tipo: 'SAIDA',
                    cfop: '5101',
                    naturezaOperacao: 'Consumo na produção',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: true,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },

                // CONSUMO
                {
                    codigo: '400',
                    categoria: 'CONSUMO',
                    descricao: 'Requisição para consumo',
                    tipo: 'SAIDA',
                    cfop: '5949',
                    naturezaOperacao: 'Outra saída de mercadoria ou prestação de serviço não especificado',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '401',
                    categoria: 'CONSUMO',
                    descricao: 'Requisição para manutenção',
                    tipo: 'SAIDA',
                    cfop: '5949',
                    naturezaOperacao: 'Consumo para manutenção',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },

                // AJUSTES
                {
                    codigo: '900',
                    categoria: 'AJUSTE',
                    descricao: 'Ajuste de inventário entrada',
                    tipo: 'ENTRADA',
                    cfop: '1949',
                    naturezaOperacao: 'Outra entrada de mercadoria ou prestação de serviço não especificado',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: true,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '901',
                    categoria: 'AJUSTE',
                    descricao: 'Ajuste de inventário saída',
                    tipo: 'SAIDA',
                    cfop: '5949',
                    naturezaOperacao: 'Outra saída de mercadoria ou prestação de serviço não especificado',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: true,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },

                // TRANSFERÊNCIAS
                {
                    codigo: '100',
                    categoria: 'TRANSFERENCIA',
                    descricao: 'Transferência entre armazéns',
                    tipo: 'AMBOS',
                    cfop: '5949',
                    naturezaOperacao: 'Transferência entre armazéns',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: false,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '101',
                    categoria: 'TRANSFERENCIA',
                    descricao: 'Transferência para filial',
                    tipo: 'SAIDA',
                    cfop: '5152',
                    naturezaOperacao: 'Transferência de produção do estabelecimento',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                },
                {
                    codigo: '102',
                    categoria: 'TRANSFERENCIA',
                    descricao: 'Recebimento de filial',
                    tipo: 'ENTRADA',
                    cfop: '1152',
                    naturezaOperacao: 'Transferência para industrialização',
                    atualizaEstoque: true,
                    permiteEstoqueNegativo: false,
                    exigeNotaFiscal: true,
                    exigeFornecedor: false,
                    exigeOrdemProducao: false,
                    exigeJustificativa: false,
                    icms: 0,
                    ipi: 0,
                    pis: 0,
                    cofins: 0,
                    ativo: true
                }
            ];
        }

        // ===== FUNÇÕES AUXILIARES =====
        window.updateCFOPSuggestions = function() {
            const categoria = document.getElementById('categoria').value;
            const cfopField = document.getElementById('cfop');

            const cfopSuggestions = {
                'COMPRA': '1102',
                'VENDA': '5102',
                'PRODUCAO': '5101',
                'CONSUMO': '5949',
                'AJUSTE': '1949',
                'TRANSFERENCIA': '5152'
            };

            if (categoria && cfopSuggestions[categoria]) {
                cfopField.value = cfopSuggestions[categoria];
            }
        };

        // ===== NOTIFICAÇÕES =====
        function showNotification(message, type = 'info') {
            const alertsContainer = document.getElementById('systemAlerts');
            const alertId = 'alert_' + Date.now();

            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type}">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                </div>
            `;

            alertsContainer.innerHTML = alertHTML;

            // Auto-remover após 5 segundos
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // ===== EVENTOS DO MODAL =====
        window.onclick = function(event) {
            const modal = document.getElementById('tesModal');
            if (event.target === modal) {
                closeModal();
            }
        };

        // ===== INICIALIZAÇÃO =====
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 Inicializando sistema de cadastro de TES...');
                await loadTES();
                showNotification('Sistema carregado com sucesso!', 'success');
            } catch (error) {
                console.error('❌ Erro na inicialização:', error);
                showNotification('Erro ao carregar sistema: ' + error.message, 'error');
            }
        });

    </script>
</body>
</html>