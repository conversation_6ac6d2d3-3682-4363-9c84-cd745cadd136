<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor de Pedidos de Compra - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .main-content {
            padding: 30px;
        }

        .search-bar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }

        .search-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .table th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            padding: 15px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
        }

        .status-pendente { background: #fff3cd; color: #856404; }
        .status-aprovado { background: #d4edda; color: #155724; }
        .status-enviado { background: #d1ecf1; color: #0c5460; }
        .status-recebido { background: #d4edda; color: #155724; }
        .status-cancelado { background: #f8d7da; color: #721c24; }
        .status-parcialmente_recebido { background: #ffeaa7; color: #6c5ce7; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 1400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-control {
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success { background: #28a745; }
        .notification-error { background: #dc3545; }
        .notification-warning { background: #ffc107; color: #212529; }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .text-truncate {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .field-readonly {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .items-section {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
        }

        .item-row {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            display: grid;
            grid-template-columns: 1fr 1fr 100px 80px 120px 120px auto;
            gap: 10px;
            align-items: center;
        }

        .fornecedor-section {
            background: #f0f8f0;
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
        }

        .danger-zone {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .danger-zone h4 {
            color: #e53e3e;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> Editor Administrativo - Pedidos de Compra</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>ATENÇÃO:</strong> Esta é uma ferramenta administrativa. Use com cuidado. Alterações aqui afetam diretamente o banco de dados.
            </div>

            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" placeholder="Buscar por número, fornecedor, status..." oninput="filterPedidos()">
                <button class="btn btn-primary" onclick="loadAllPedidos()">
                    <i class="fas fa-sync"></i> Recarregar
                </button>
                <div id="totalCount" style="font-weight: 600; color: #495057;"></div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Data</th>
                            <th>Fornecedor</th>
                            <th>Status</th>
                            <th>Itens</th>
                            <th>Valor Total</th>
                            <th>Entrega</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="pedidosTableBody">
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-spinner fa-spin fa-2x"></i><br><br>
                                Carregando pedidos...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Pedido de Compra</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="pedidoId" name="id">
                    
                    <!-- Seção: Identificação -->
                    <h4 style="margin-bottom: 15px; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-id-card"></i> Identificação
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="numero">Número do Pedido</label>
                            <input type="text" id="numero" class="form-control" placeholder="Ex: PC-2024-0001">
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" class="form-control" required>
                                <option value="PENDENTE">Pendente</option>
                                <option value="APROVADO">Aprovado</option>
                                <option value="ENVIADO">Enviado</option>
                                <option value="PARCIALMENTE_RECEBIDO">Parcialmente Recebido</option>
                                <option value="RECEBIDO">Recebido</option>
                                <option value="CANCELADO">Cancelado</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tipo">Tipo</label>
                            <select id="tipo" class="form-control">
                                <option value="NORMAL">Normal</option>
                                <option value="URGENTE">Urgente</option>
                                <option value="PROGRAMADO">Programado</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="criadoPor">Criado Por</label>
                            <input type="text" id="criadoPor" class="form-control">
                        </div>
                    </div>

                    <!-- Seção: Fornecedor -->
                    <div class="fornecedor-section">
                        <h4 style="margin-bottom: 15px; color: #495057;">
                            <i class="fas fa-building"></i> Dados do Fornecedor
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="fornecedorId">ID do Fornecedor</label>
                                <input type="text" id="fornecedorId" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="fornecedorRazaoSocial">Razão Social</label>
                                <input type="text" id="fornecedorRazaoSocial" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="fornecedorCnpj">CNPJ</label>
                                <input type="text" id="fornecedorCnpj" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="fornecedorContato">Contato</label>
                                <input type="text" id="fornecedorContato" class="form-control">
                            </div>
                        </div>
                    </div>

                    <!-- Seção: Datas -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-calendar"></i> Datas
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="dataCriacao">Data de Criação</label>
                            <input type="datetime-local" id="dataCriacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="ultimaAtualizacao">Última Atualização</label>
                            <input type="datetime-local" id="ultimaAtualizacao" class="form-control field-readonly" readonly>
                        </div>
                        <div class="form-group">
                            <label for="dataEntrega">Data de Entrega</label>
                            <input type="date" id="dataEntrega" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="prazoEntrega">Prazo Entrega (dias)</label>
                            <input type="number" id="prazoEntrega" class="form-control" min="1">
                        </div>
                    </div>

                    <!-- Seção: Valores -->
                    <h4 style="margin: 25px 0 15px 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 5px;">
                        <i class="fas fa-dollar-sign"></i> Valores
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="valorTotal">Valor Total</label>
                            <input type="number" id="valorTotal" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="valorFrete">Valor do Frete</label>
                            <input type="number" id="valorFrete" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="valorDesconto">Valor Desconto</label>
                            <input type="number" id="valorDesconto" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="formaPagamento">Forma de Pagamento</label>
                            <select id="formaPagamento" class="form-control">
                                <option value="A_VISTA">À Vista</option>
                                <option value="30_DIAS">30 dias</option>
                                <option value="60_DIAS">60 dias</option>
                                <option value="90_DIAS">90 dias</option>
                                <option value="PARCELADO">Parcelado</option>
                            </select>
                        </div>
                    </div>

                    <!-- Seção: Observações -->
                    <div class="form-group">
                        <label for="observacoes">Observações</label>
                        <textarea id="observacoes" class="form-control" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="condicoesPagamento">Condições de Pagamento</label>
                        <textarea id="condicoesPagamento" class="form-control" rows="2"></textarea>
                    </div>

                    <!-- Seção: Itens -->
                    <div class="items-section">
                        <h4 style="margin-bottom: 15px; color: #495057;">
                            <i class="fas fa-list"></i> Itens do Pedido
                            <button type="button" class="btn btn-success btn-sm" style="float: right;" onclick="addNewItem()">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </button>
                        </h4>
                        <div id="itemsContainer">
                            <!-- Itens serão adicionados dinamicamente -->
                        </div>
                    </div>

                    <!-- Zona de Perigo -->
                    <div class="danger-zone">
                        <h4><i class="fas fa-exclamation-triangle"></i> Zona de Perigo</h4>
                        <p style="margin-bottom: 15px;">Ações irreversíveis que afetam permanentemente os dados.</p>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteForever()">
                            <i class="fas fa-trash"></i> Excluir Permanentemente
                        </button>
                    </div>

                    <div style="text-align: right; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn btn-secondary" onclick="closeModal()" style="margin-right: 10px;">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection,
            getDocs,
            doc,
            updateDoc,
            deleteDoc,
            Timestamp,
            orderBy,
            query
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let allPedidos = [];
        let filteredPedidos = [];
        let currentEditId = null;
        let currentItems = [];

        // Carregar todos os pedidos
        window.loadAllPedidos = async function() {
            try {
                showNotification('Carregando pedidos...', 'info');
                
                const q = query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"));
                const snapshot = await getDocs(q);
                
                allPedidos = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                
                filteredPedidos = [...allPedidos];
                renderPedidos();
                updateTotalCount();
                
                showNotification(`${allPedidos.length} pedidos carregados com sucesso!`, 'success');
            } catch (error) {
                console.error('Erro ao carregar pedidos:', error);
                showNotification('Erro ao carregar pedidos: ' + error.message, 'error');
            }
        };

        // Renderizar tabela
        function renderPedidos() {
            const tbody = document.getElementById('pedidosTableBody');
            
            if (filteredPedidos.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-search fa-2x"></i><br><br>
                            Nenhum pedido encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredPedidos.map(ped => {
                const dataFormatada = formatDate(ped.dataCriacao);
                const numItens = (ped.itens || []).length;
                const entregaFormatada = formatDate(ped.dataEntrega) || 'N/A';
                const fornecedorNome = ped.fornecedor?.razaoSocial || ped.fornecedorRazaoSocial || 'N/A';

                return `
                    <tr>
                        <td><strong>${ped.numero || 'N/A'}</strong></td>
                        <td>${dataFormatada}</td>
                        <td class="text-truncate">${fornecedorNome}</td>
                        <td><span class="status status-${(ped.status || 'pendente').toLowerCase().replace('_', '_')}">${getStatusText(ped.status)}</span></td>
                        <td style="text-align: center;">${numItens}</td>
                        <td>R$ ${formatCurrency(ped.valorTotal || 0)}</td>
                        <td>${entregaFormatada}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="editPedido('${ped.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Filtrar pedidos
        window.filterPedidos = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            if (!searchTerm) {
                filteredPedidos = [...allPedidos];
            } else {
                filteredPedidos = allPedidos.filter(ped => 
                    (ped.numero || '').toLowerCase().includes(searchTerm) ||
                    (ped.fornecedor?.razaoSocial || '').toLowerCase().includes(searchTerm) ||
                    (ped.fornecedorRazaoSocial || '').toLowerCase().includes(searchTerm) ||
                    (ped.status || '').toLowerCase().includes(searchTerm) ||
                    (ped.observacoes || '').toLowerCase().includes(searchTerm)
                );
            }
            
            renderPedidos();
            updateTotalCount();
        };

        // Atualizar contador
        function updateTotalCount() {
            document.getElementById('totalCount').textContent = 
                `${filteredPedidos.length} de ${allPedidos.length} pedidos`;
        }

        // Editar pedido
        window.editPedido = function(id) {
            const pedido = allPedidos.find(p => p.id === id);
            if (!pedido) {
                showNotification('Pedido não encontrado!', 'error');
                return;
            }

            currentEditId = id;
            populateEditForm(pedido);
            document.getElementById('editModal').style.display = 'block';
        };

        // Preencher formulário de edição
        function populateEditForm(ped) {
            document.getElementById('pedidoId').value = ped.id;
            document.getElementById('numero').value = ped.numero || '';
            document.getElementById('status').value = ped.status || 'PENDENTE';
            document.getElementById('tipo').value = ped.tipo || 'NORMAL';
            document.getElementById('criadoPor').value = ped.criadoPor || '';
            
            // Dados do fornecedor
            document.getElementById('fornecedorId').value = ped.fornecedorId || '';
            document.getElementById('fornecedorRazaoSocial').value = ped.fornecedor?.razaoSocial || ped.fornecedorRazaoSocial || '';
            document.getElementById('fornecedorCnpj').value = ped.fornecedor?.cnpj || ped.fornecedorCnpj || '';
            document.getElementById('fornecedorContato').value = ped.fornecedor?.contato || ped.fornecedorContato || '';
            
            // Valores
            document.getElementById('valorTotal').value = ped.valorTotal || 0;
            document.getElementById('valorFrete').value = ped.valorFrete || 0;
            document.getElementById('valorDesconto').value = ped.valorDesconto || 0;
            document.getElementById('formaPagamento').value = ped.formaPagamento || 'A_VISTA';
            document.getElementById('prazoEntrega').value = ped.prazoEntrega || '';
            
            // Observações
            document.getElementById('observacoes').value = ped.observacoes || '';
            document.getElementById('condicoesPagamento').value = ped.condicoesPagamento || '';

            // Datas
            if (ped.dataCriacao) {
                document.getElementById('dataCriacao').value = timestampToDatetime(ped.dataCriacao);
            }
            if (ped.ultimaAtualizacao) {
                document.getElementById('ultimaAtualizacao').value = timestampToDatetime(ped.ultimaAtualizacao);
            }
            if (ped.dataEntrega) {
                document.getElementById('dataEntrega').value = timestampToDate(ped.dataEntrega);
            }

            // Renderizar itens
            currentItems = JSON.parse(JSON.stringify(ped.itens || []));
            renderItems();
        }

        // Renderizar itens
        function renderItems() {
            const container = document.getElementById('itemsContainer');
            
            if (currentItems.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #6c757d;">
                        <i class="fas fa-box-open fa-2x"></i><br><br>
                        Nenhum item adicionado
                    </div>
                `;
                return;
            }

            container.innerHTML = currentItems.map((item, index) => `
                <div class="item-row" data-index="${index}">
                    <input type="text" placeholder="Código" value="${item.codigo || ''}" onchange="updateItem(${index}, 'codigo', this.value)">
                    <input type="text" placeholder="Descrição" value="${item.descricao || ''}" onchange="updateItem(${index}, 'descricao', this.value)">
                    <input type="number" placeholder="Qtd" value="${item.quantidade || 1}" step="0.001" onchange="updateItem(${index}, 'quantidade', parseFloat(this.value))">
                    <input type="text" placeholder="Un" value="${item.unidade || 'UN'}" onchange="updateItem(${index}, 'unidade', this.value)">
                    <input type="number" placeholder="Valor Unit." value="${item.valorUnitario || 0}" step="0.01" onchange="updateItem(${index}, 'valorUnitario', parseFloat(this.value))">
                    <div style="font-weight: 600;">R$ ${formatCurrency((item.quantidade || 0) * (item.valorUnitario || 0))}</div>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        // Funções de itens
        window.addNewItem = function() {
            const newItem = {
                codigo: '',
                descricao: '',
                quantidade: 1,
                unidade: 'UN',
                valorUnitario: 0
            };
            currentItems.push(newItem);
            renderItems();
        };

        window.updateItem = function(index, field, value) {
            if (currentItems[index]) {
                currentItems[index][field] = value;
                renderItems();
            }
        };

        window.removeItem = function(index) {
            if (confirm('Remover este item?')) {
                currentItems.splice(index, 1);
                renderItems();
            }
        };

        // Salvar alterações
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!currentEditId) {
                showNotification('Erro: ID do pedido não encontrado!', 'error');
                return;
            }

            try {
                const formData = {
                    numero: document.getElementById('numero').value,
                    status: document.getElementById('status').value,
                    tipo: document.getElementById('tipo').value,
                    criadoPor: document.getElementById('criadoPor').value,
                    fornecedorId: document.getElementById('fornecedorId').value,
                    valorTotal: parseFloat(document.getElementById('valorTotal').value) || 0,
                    valorFrete: parseFloat(document.getElementById('valorFrete').value) || 0,
                    valorDesconto: parseFloat(document.getElementById('valorDesconto').value) || 0,
                    formaPagamento: document.getElementById('formaPagamento').value,
                    prazoEntrega: parseInt(document.getElementById('prazoEntrega').value) || null,
                    observacoes: document.getElementById('observacoes').value,
                    condicoesPagamento: document.getElementById('condicoesPagamento').value,
                    itens: currentItems,
                    ultimaAtualizacao: Timestamp.now()
                };

                // Dados do fornecedor
                formData.fornecedor = {
                    razaoSocial: document.getElementById('fornecedorRazaoSocial').value,
                    cnpj: document.getElementById('fornecedorCnpj').value,
                    contato: document.getElementById('fornecedorContato').value
                };

                // Processar data de entrega
                if (document.getElementById('dataEntrega').value) {
                    formData.dataEntrega = Timestamp.fromDate(new Date(document.getElementById('dataEntrega').value));
                }

                // Remover campos vazios
                Object.keys(formData).forEach(key => {
                    if (formData[key] === '' || formData[key] === null || formData[key] === undefined) {
                        delete formData[key];
                    }
                });

                await updateDoc(doc(db, "pedidosCompra", currentEditId), formData);
                
                showNotification('Pedido atualizado com sucesso!', 'success');
                closeModal();
                loadAllPedidos();

            } catch (error) {
                console.error('Erro ao salvar:', error);
                showNotification('Erro ao salvar: ' + error.message, 'error');
            }
        });

        // Excluir permanentemente
        window.deleteForever = async function() {
            if (!currentEditId) return;

            const confirmText = prompt('ATENÇÃO! Esta ação é IRREVERSÍVEL.\n\nDigite "EXCLUIR PERMANENTEMENTE" para confirmar:');
            
            if (confirmText !== 'EXCLUIR PERMANENTEMENTE') {
                showNotification('Operação cancelada.', 'warning');
                return;
            }

            try {
                await deleteDoc(doc(db, "pedidosCompra", currentEditId));
                showNotification('Pedido excluído permanentemente!', 'success');
                closeModal();
                loadAllPedidos();
            } catch (error) {
                console.error('Erro ao excluir:', error);
                showNotification('Erro ao excluir: ' + error.message, 'error');
            }
        };

        // Fechar modal
        window.closeModal = function() {
            document.getElementById('editModal').style.display = 'none';
            currentEditId = null;
            currentItems = [];
        };

        // Funções utilitárias
        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';

            try {
                // É um Timestamp do Firebase
                if (timestamp && typeof timestamp.seconds === 'number') {
                    const date = new Date(timestamp.seconds * 1000);
                    return date.toLocaleString('pt-BR');
                }

                // Tem método toDate (Firebase Timestamp)
                if (timestamp && typeof timestamp.toDate === 'function') {
                    const date = timestamp.toDate();
                    return date.toLocaleString('pt-BR');
                }

                // É uma string ou número
                const date = new Date(timestamp);
                if (isNaN(date.getTime())) return 'N/A';
                return date.toLocaleString('pt-BR');

            } catch (error) {
                console.warn('Erro ao formatar data:', error, timestamp);
                return 'Data inválida';
            }
        }

        function timestampToDatetime(timestamp) {
            if (!timestamp) return '';
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toISOString().slice(0, 16);
            } catch {
                return '';
            }
        }

        function timestampToDate(timestamp) {
            if (!timestamp) return '';
            try {
                const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
                return date.toISOString().slice(0, 10);
            } catch {
                return '';
            }
        }

        function formatCurrency(value) {
            return (value || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }

        function getStatusText(status) {
            const statusMap = {
                'PENDENTE': 'Pendente',
                'APROVADO': 'Aprovado',
                'ENVIADO': 'Enviado',
                'PARCIALMENTE_RECEBIDO': 'Parc. Recebido',
                'RECEBIDO': 'Recebido',
                'CANCELADO': 'Cancelado'
            };
            return statusMap[status] || status || 'N/A';
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Inicializar ao carregar a página
        window.addEventListener('load', function() {
            loadAllPedidos();
        });

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeModal();
            }
        };
    </script>
</body>
</html> 