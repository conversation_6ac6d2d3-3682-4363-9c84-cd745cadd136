<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificar Campo Necessidade</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .problema { background: #ffeb3b; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 Verificar Campo 'necessidade' da OP</h1>
    
    <button onclick="buscarOP()">Buscar OP OP25060758</button>
    <button onclick="corrigirNecessidade()">Corrigir Campo 'necessidade'</button>
    
    <div id="resultado"></div>

    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore-compat.js"></script>
    
    <script>
        const firebaseConfig = {
            apiKey: "AIzaSyBxRnfR0wqYysZorCcg-kJpx5ZXAuGaEIc",
            authDomain: "banco-novo.firebaseapp.com",
            projectId: "banco-novo",
            storageBucket: "banco-novo.firebasestorage.app",
            messagingSenderId: "437400408801",
            appId: "1:437400408801:web:bcdc5df6e3257528e79da0",
            measurementId: "G-VP7F3LBQB2"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        let opEncontrada = null;
        let produtos = [];

        async function buscarOP() {
            const resultado = document.getElementById('resultado');
            resultado.innerHTML = '<div class="info">🔄 Buscando OP...</div>';

            try {
                // Buscar todas as OPs
                const opsSnap = await db.collection('ordensProducao').get();
                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Procurar por número
                opEncontrada = ops.find(op => op.numero === 'OP25060758');
                
                if (!opEncontrada) {
                    resultado.innerHTML = '<div class="error">❌ OP OP25060758 não encontrada!</div>';
                    return;
                }

                // Buscar produtos
                const produtosSnap = await db.collection('produtos').get();
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Analisar materiais
                const materiais = opEncontrada.materiaisNecessarios || [];
                
                let html = `<h2>📋 OP Encontrada: ${opEncontrada.numero}</h2>`;
                html += `<div class="info"><strong>ID:</strong> ${opEncontrada.id}</div>`;
                html += `<div class="info"><strong>Total de materiais:</strong> ${materiais.length}</div>`;

                if (materiais.length === 0) {
                    html += '<div class="error">❌ Nenhum material na OP!</div>';
                    resultado.innerHTML = html;
                    return;
                }

                html += `
                    <table>
                        <thead>
                            <tr>
                                <th>Produto ID</th>
                                <th>Código</th>
                                <th>Tipo</th>
                                <th>Quantidade</th>
                                <th>Necessidade</th>
                                <th>Problema?</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                let problemasEncontrados = 0;

                materiais.forEach(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    const codigo = produto?.codigo || 'N/A';
                    const tipo = produto?.tipo || 'N/A';
                    
                    const quantidade = material.quantidade || 0;
                    const necessidade = material.necessidade || 0;
                    
                    const temProblema = (tipo === 'MP' && necessidade <= 0);
                    if (temProblema) problemasEncontrados++;
                    
                    const classe = temProblema ? 'problema' : '';
                    
                    html += `
                        <tr class="${classe}">
                            <td>${material.produtoId}</td>
                            <td>${codigo}</td>
                            <td>${tipo}</td>
                            <td>${quantidade}</td>
                            <td>${necessidade}</td>
                            <td>${temProblema ? '❌ SIM - Necessidade = 0' : '✅ OK'}</td>
                        </tr>
                    `;
                });

                html += '</tbody></table>';
                
                if (problemasEncontrados > 0) {
                    html += `
                        <div class="error">
                            <strong>🚨 PROBLEMA ENCONTRADO!</strong><br>
                            ${problemasEncontrados} material(is) MP com necessidade = 0.<br>
                            Por isso não aparecem no movimentacao_armazem.html!
                        </div>
                    `;
                } else {
                    html += '<div class="success">✅ Todos os materiais MP têm necessidade > 0</div>';
                }

                resultado.innerHTML = html;

            } catch (error) {
                console.error('Erro:', error);
                resultado.innerHTML = `<div class="error">❌ Erro: ${error.message}</div>`;
            }
        }

        async function corrigirNecessidade() {
            if (!opEncontrada) {
                alert('Busque a OP primeiro!');
                return;
            }

            if (!confirm('Corrigir campo "necessidade" = "quantidade" para materiais MP?')) {
                return;
            }

            const resultado = document.getElementById('resultado');
            resultado.innerHTML = '<div class="info">🔄 Corrigindo...</div>';

            try {
                const materiaisCorrigidos = opEncontrada.materiaisNecessarios.map(material => {
                    const produto = produtos.find(p => p.id === material.produtoId);
                    
                    // Se é MP e necessidade <= 0, corrigir
                    if (produto?.tipo === 'MP' && (material.necessidade || 0) <= 0) {
                        return { ...material, necessidade: material.quantidade || 0 };
                    }
                    return material;
                });

                // Atualizar no banco
                await db.collection('ordensProducao').doc(opEncontrada.id).update({
                    materiaisNecessarios: materiaisCorrigidos
                });

                resultado.innerHTML = `
                    <div class="success">
                        ✅ <strong>Correção realizada!</strong><br>
                        Campo "necessidade" corrigido para materiais MP.<br><br>
                        <strong>🎯 Agora teste o movimentacao_armazem.html!</strong>
                    </div>
                `;

            } catch (error) {
                console.error('Erro:', error);
                resultado.innerHTML = `<div class="error">❌ Erro: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
