/**
 * SERVIÇO DE INTEGRIDADE DE ESTOQUE - CORREÇÃO DE FALHAS
 * Implementa controles rigorosos para garantir integridade das movimentações
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    doc, 
    runTransaction,
    query,
    where,
    getDocs,
    Timestamp,
    writeBatch
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class StockIntegrityService {
    
    /**
     * 🔒 MOVIMENTAÇÃO SEGURA COM CONTROLE TOTAL
     */
    static async safeStockMovement(movementData) {
        return await runTransaction(db, async (transaction) => {
            try {
                // 1. VALIDAÇÃO RIGOROSA
                const validation = await this.validateMovementData(movementData);
                if (!validation.valid) {
                    throw new Error(`Validação falhou: ${validation.errors.join(', ')}`);
                }

                // 2. BUSCAR ESTOQUE ATUAL COM LOCK
                const stockRef = await this.getStockReference(
                    movementData.produtoId, 
                    movementData.armazemId
                );
                
                const stockDoc = await transaction.get(stockRef);
                const currentStock = stockDoc.exists() ? stockDoc.data() : {
                    produtoId: movementData.produtoId,
                    armazemId: movementData.armazemId,
                    saldo: 0,
                    saldoReservado: 0,
                    saldoEmpenhado: 0,
                    valorTotal: 0,
                    custoMedio: 0
                };

                // 3. CALCULAR NOVO SALDO
                const newBalance = this.calculateNewBalance(
                    currentStock, 
                    movementData
                );

                // 4. VALIDAR SALDO RESULTANTE
                if (!this.validateResultingBalance(newBalance, movementData)) {
                    throw new Error('Saldo resultante inválido - operação cancelada');
                }

                // 5. REGISTRAR MOVIMENTAÇÃO
                const movementRef = doc(collection(db, "movimentacoesEstoque"));
                const movementRecord = {
                    ...movementData,
                    id: movementRef.id,
                    timestamp: Timestamp.now(),
                    saldoAnterior: currentStock.saldo,
                    saldoPosterior: newBalance.saldo,
                    custoMedioAnterior: currentStock.custoMedio,
                    custoMedioPosterior: newBalance.custoMedio,
                    status: 'CONFIRMADA',
                    checksum: this.generateChecksum(newBalance),
                    // TES obrigatório - usar padrão se não informado
                    tes: movementData.tes || (movementData.tipo === 'ENTRADA' ? '900' : '901'),
                    tesValidado: true
                };

                transaction.set(movementRef, movementRecord);

                // 6. ATUALIZAR ESTOQUE
                transaction.set(stockRef, {
                    ...newBalance,
                    ultimaMovimentacao: Timestamp.now(),
                    ultimoMovimentoId: movementRef.id,
                    versao: (currentStock.versao || 0) + 1
                });

                // 7. REGISTRAR AUDITORIA
                const auditRef = doc(collection(db, "auditoria"));
                transaction.set(auditRef, {
                    acao: 'MOVIMENTACAO_ESTOQUE',
                    produtoId: movementData.produtoId,
                    armazemId: movementData.armazemId,
                    dadosAnteriores: currentStock,
                    dadosNovos: newBalance,
                    movimentacaoId: movementRef.id,
                    usuario: movementData.usuario || 'Sistema',
                    timestamp: Timestamp.now(),
                    ip: movementData.ip || 'unknown'
                });

                return {
                    success: true,
                    movimentacaoId: movementRef.id,
                    saldoAnterior: currentStock.saldo,
                    saldoPosterior: newBalance.saldo,
                    checksum: movementRecord.checksum
                };

            } catch (error) {
                console.error('Erro na movimentação segura:', error);
                throw error;
            }
        });
    }

    /**
     * ✅ VALIDAÇÃO RIGOROSA DE DADOS
     */
    static async validateMovementData(data) {
        const errors = [];

        // Validar campos obrigatórios
        if (!data.produtoId) errors.push('Produto não identificado');
        if (!data.armazemId) errors.push('Armazém não identificado');
        if (!data.tipo || !['ENTRADA', 'SAIDA'].includes(data.tipo)) {
            errors.push('Tipo de movimentação inválido');
        }
        if (!data.quantidade || data.quantidade <= 0) {
            errors.push('Quantidade inválida');
        }

        // Validar TES obrigatório
        if (!data.tes) {
            errors.push('TES (Tipo de Entrada/Saída) é obrigatório');
        } else {
            // Validar TES válido
            const tesValido = this.validateTES(data.tes, data.tipo);
            if (!tesValido) {
                errors.push(`TES ${data.tes} não é válido para movimentação de ${data.tipo}`);
            }
        }

        // Validar produto existe
        if (data.produtoId) {
            const produtoExists = await this.checkProductExists(data.produtoId);
            if (!produtoExists) {
                errors.push('Produto não encontrado');
            }
        }

        // Validar armazém existe
        if (data.armazemId) {
            const armazemExists = await this.checkWarehouseExists(data.armazemId);
            if (!armazemExists) {
                errors.push('Armazém não encontrado');
            }
        }

        // Validar saldo para saídas
        if (data.tipo === 'SAIDA') {
            const stockCheck = await this.checkAvailableStock(
                data.produtoId, 
                data.armazemId, 
                data.quantidade
            );
            if (!stockCheck.sufficient) {
                errors.push(`Saldo insuficiente. Disponível: ${stockCheck.available}, Necessário: ${data.quantidade}`);
            }
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * ✅ VALIDAR TES (Tipo de Entrada/Saída)
     */
    static validateTES(tes, tipoMovimentacao) {
        if (!tes) return false;

        // TES válidos por tipo de operação
        const tesValidos = {
            'ENTRADA': ['001', '002', '003', '200', '900', '100'], // Compra, Produção, Ajuste, Transferência
            'SAIDA': ['201', '500', '501', '901', '100']           // Consumo, Venda, Ajuste, Transferência
        };

        const tesPermitidos = tesValidos[tipoMovimentacao] || [];
        return tesPermitidos.includes(tes);
    }

    /**
     * 📊 CALCULAR NOVO SALDO
     */
    static calculateNewBalance(currentStock, movementData) {
        const isEntry = movementData.tipo === 'ENTRADA';
        const quantity = movementData.quantidade;
        const unitValue = movementData.valorUnitario || 0;

        let newQuantity = currentStock.saldo;
        let newTotalValue = currentStock.valorTotal || 0;
        let newAverageCost = currentStock.custoMedio || 0;

        if (isEntry) {
            // ENTRADA
            newQuantity += quantity;
            const entryValue = quantity * unitValue;
            newTotalValue += entryValue;
            newAverageCost = newQuantity > 0 ? newTotalValue / newQuantity : 0;
        } else {
            // SAÍDA
            newQuantity -= quantity;
            const exitValue = quantity * (currentStock.custoMedio || 0);
            newTotalValue = Math.max(0, newTotalValue - exitValue);
            // Custo médio mantém o mesmo na saída
        }

        return {
            ...currentStock,
            saldo: newQuantity,
            valorTotal: newTotalValue,
            custoMedio: newAverageCost
        };
    }

    /**
     * ✅ VALIDAR SALDO RESULTANTE
     */
    static validateResultingBalance(newBalance, movementData) {
        // Não permitir saldo negativo (exceto se configurado)
        if (newBalance.saldo < 0 && !this.isNegativeStockAllowed()) {
            return false;
        }

        // Validar valores monetários
        if (newBalance.valorTotal < 0) {
            return false;
        }

        // Validar custo médio
        if (newBalance.custoMedio < 0) {
            return false;
        }

        return true;
    }

    /**
     * 🔍 VERIFICAR ESTOQUE DISPONÍVEL
     */
    static async checkAvailableStock(produtoId, armazemId, requiredQuantity) {
        try {
            const stockQuery = query(
                collection(db, "estoques"),
                where("produtoId", "==", produtoId),
                where("armazemId", "==", armazemId)
            );

            const stockSnapshot = await getDocs(stockQuery);
            
            if (stockSnapshot.empty) {
                return { sufficient: false, available: 0 };
            }

            const stock = stockSnapshot.docs[0].data();
            const available = (stock.saldo || 0) - 
                            (stock.saldoReservado || 0) - 
                            (stock.saldoEmpenhado || 0);

            return {
                sufficient: available >= requiredQuantity,
                available: Math.max(0, available)
            };
        } catch (error) {
            console.error('Erro ao verificar estoque:', error);
            return { sufficient: false, available: 0 };
        }
    }

    /**
     * 🔗 OBTER REFERÊNCIA DO ESTOQUE
     */
    static async getStockReference(produtoId, armazemId) {
        const stockQuery = query(
            collection(db, "estoques"),
            where("produtoId", "==", produtoId),
            where("armazemId", "==", armazemId)
        );

        const stockSnapshot = await getDocs(stockQuery);
        
        if (!stockSnapshot.empty) {
            return stockSnapshot.docs[0].ref;
        }

        // Criar nova referência se não existir
        return doc(collection(db, "estoques"));
    }

    /**
     * 🔐 GERAR CHECKSUM PARA INTEGRIDADE
     */
    static generateChecksum(stockData) {
        const data = `${stockData.produtoId}_${stockData.armazemId}_${stockData.saldo}_${stockData.valorTotal}_${Date.now()}`;
        return btoa(data).substring(0, 16);
    }

    /**
     * ✅ VERIFICAR SE PRODUTO EXISTE
     */
    static async checkProductExists(produtoId) {
        try {
            const produtoQuery = query(
                collection(db, "produtos"),
                where("__name__", "==", produtoId)
            );
            const snapshot = await getDocs(produtoQuery);
            return !snapshot.empty;
        } catch (error) {
            return false;
        }
    }

    /**
     * ✅ VERIFICAR SE ARMAZÉM EXISTE
     */
    static async checkWarehouseExists(armazemId) {
        try {
            const armazemQuery = query(
                collection(db, "armazens"),
                where("__name__", "==", armazemId)
            );
            const snapshot = await getDocs(armazemQuery);
            return !snapshot.empty;
        } catch (error) {
            return false;
        }
    }

    /**
     * ⚙️ VERIFICAR SE ESTOQUE NEGATIVO É PERMITIDO
     */
    static isNegativeStockAllowed() {
        // Buscar configuração do sistema
        return false; // Por padrão, não permitir
    }

    /**
     * 🔍 AUDITORIA DE INTEGRIDADE
     */
    static async auditStockIntegrity(produtoId = null, armazemId = null) {
        const issues = [];

        try {
            // Buscar todos os estoques ou filtrados
            let stockQuery = collection(db, "estoques");
            
            if (produtoId && armazemId) {
                stockQuery = query(stockQuery, 
                    where("produtoId", "==", produtoId),
                    where("armazemId", "==", armazemId)
                );
            }

            const stockSnapshot = await getDocs(stockQuery);

            for (const stockDoc of stockSnapshot.docs) {
                const stock = stockDoc.data();
                const stockIssues = await this.validateStockRecord(stock);
                
                if (stockIssues.length > 0) {
                    issues.push({
                        stockId: stockDoc.id,
                        produtoId: stock.produtoId,
                        armazemId: stock.armazemId,
                        issues: stockIssues
                    });
                }
            }

            return {
                totalChecked: stockSnapshot.docs.length,
                issuesFound: issues.length,
                issues
            };

        } catch (error) {
            console.error('Erro na auditoria:', error);
            throw error;
        }
    }

    /**
     * ✅ VALIDAR REGISTRO DE ESTOQUE
     */
    static async validateStockRecord(stock) {
        const issues = [];

        // Verificar saldos negativos não permitidos
        if (stock.saldo < 0 && !this.isNegativeStockAllowed()) {
            issues.push('Saldo negativo não permitido');
        }

        // Verificar consistência de valores
        if (stock.valorTotal < 0) {
            issues.push('Valor total negativo');
        }

        // Verificar custo médio
        if (stock.saldo > 0 && stock.valorTotal > 0) {
            const expectedCost = stock.valorTotal / stock.saldo;
            const difference = Math.abs(expectedCost - (stock.custoMedio || 0));
            
            if (difference > 0.01) { // Tolerância de 1 centavo
                issues.push('Custo médio inconsistente');
            }
        }

        // Verificar se produto existe
        const productExists = await this.checkProductExists(stock.produtoId);
        if (!productExists) {
            issues.push('Produto não encontrado');
        }

        // Verificar se armazém existe
        const warehouseExists = await this.checkWarehouseExists(stock.armazemId);
        if (!warehouseExists) {
            issues.push('Armazém não encontrado');
        }

        return issues;
    }

    /**
     * 🔧 CORRIGIR INCONSISTÊNCIAS
     */
    static async fixStockInconsistencies(stockId, corrections) {
        return await runTransaction(db, async (transaction) => {
            const stockRef = doc(db, "estoques", stockId);
            const stockDoc = await transaction.get(stockRef);

            if (!stockDoc.exists()) {
                throw new Error('Estoque não encontrado');
            }

            const currentStock = stockDoc.data();
            const correctedStock = { ...currentStock, ...corrections };

            // Validar correções
            const validation = await this.validateStockRecord(correctedStock);
            if (validation.length > 0) {
                throw new Error(`Correções inválidas: ${validation.join(', ')}`);
            }

            // Aplicar correções
            transaction.update(stockRef, {
                ...correctedStock,
                ultimaCorrecao: Timestamp.now(),
                versao: (currentStock.versao || 0) + 1
            });

            // Registrar auditoria da correção
            const auditRef = doc(collection(db, "auditoria"));
            transaction.set(auditRef, {
                acao: 'CORRECAO_ESTOQUE',
                stockId,
                dadosAnteriores: currentStock,
                dadosNovos: correctedStock,
                timestamp: Timestamp.now(),
                usuario: 'Sistema'
            });

            return {
                success: true,
                stockId,
                corrections: corrections
            };
        });
    }
}

// Exportar para uso global
window.StockIntegrityService = StockIntegrityService;