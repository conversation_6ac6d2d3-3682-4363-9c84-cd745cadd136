/**
 * Script para corrigir problemas de condições de pagamento undefined
 * 
 * Este script:
 * 1. Identifica pedidos sem condição de pagamento
 * 2. Atribui condição padrão "À Vista"
 * 3. Corrige contas a pagar com condicaoPagamentoId undefined
 */

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  query, 
  where, 
  limit,
  writeBatch
} from 'firebase/firestore';

// Configuração do Firebase (substitua pela sua configuração)
const firebaseConfig = {
  // Sua configuração aqui
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Busca condição de pagamento padrão
 */
async function getDefaultPaymentCondition() {
  try {
    console.log('🔍 Buscando condição de pagamento padrão...');
    
    // Primeiro, tentar buscar por descrição "À Vista"
    const aVistaQuery = query(
      collection(db, "condicoesPagamento"),
      where("descricao", "==", "À Vista"),
      limit(1)
    );
    
    const aVistaSnap = await getDocs(aVistaQuery);
    if (!aVistaSnap.empty) {
      const condition = { id: aVistaSnap.docs[0].id, ...aVistaSnap.docs[0].data() };
      console.log('✅ Encontrada condição "À Vista":', condition.descricao);
      return condition;
    }
    
    // Se não encontrar "À Vista", buscar por tipo "A_VISTA"
    const tipoQuery = query(
      collection(db, "condicoesPagamento"),
      where("tipo", "==", "A_VISTA"),
      limit(1)
    );
    
    const tipoSnap = await getDocs(tipoQuery);
    if (!tipoSnap.empty) {
      const condition = { id: tipoSnap.docs[0].id, ...tipoSnap.docs[0].data() };
      console.log('✅ Encontrada condição tipo "A_VISTA":', condition.descricao);
      return condition;
    }
    
    // Se não encontrar nenhuma, buscar a primeira disponível
    const allQuery = query(collection(db, "condicoesPagamento"), limit(1));
    const allSnap = await getDocs(allQuery);
    
    if (!allSnap.empty) {
      const condition = { id: allSnap.docs[0].id, ...allSnap.docs[0].data() };
      console.warn('⚠️ Usando primeira condição disponível:', condition.descricao);
      return condition;
    }
    
    throw new Error('❌ Nenhuma condição de pagamento encontrada no sistema');
    
  } catch (error) {
    console.error('❌ Erro ao buscar condição de pagamento padrão:', error);
    throw error;
  }
}

/**
 * Corrige pedidos sem condição de pagamento
 */
async function fixPurchaseOrders(defaultCondition) {
  try {
    console.log('\n📋 Verificando pedidos de compra...');
    
    const pedidosSnap = await getDocs(collection(db, "pedidosCompra"));
    const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const pedidosSemCondicao = pedidos.filter(p => !p.condicaoPagamentoId);
    
    console.log(`📊 Total de pedidos: ${pedidos.length}`);
    console.log(`⚠️ Pedidos sem condição de pagamento: ${pedidosSemCondicao.length}`);
    
    if (pedidosSemCondicao.length === 0) {
      console.log('✅ Todos os pedidos já possuem condição de pagamento');
      return 0;
    }
    
    const batch = writeBatch(db);
    let count = 0;
    
    for (const pedido of pedidosSemCondicao) {
      const pedidoRef = doc(db, "pedidosCompra", pedido.id);
      batch.update(pedidoRef, {
        condicaoPagamentoId: defaultCondition.id,
        observacoes: (pedido.observacoes || '') + 
          `\n[CORREÇÃO AUTOMÁTICA] Condição de pagamento atribuída: ${defaultCondition.descricao}`
      });
      
      count++;
      console.log(`🔧 Corrigindo pedido ${pedido.numero || pedido.id}`);
    }
    
    await batch.commit();
    console.log(`✅ ${count} pedidos corrigidos com sucesso`);
    
    return count;
    
  } catch (error) {
    console.error('❌ Erro ao corrigir pedidos:', error);
    throw error;
  }
}

/**
 * Corrige contas a pagar com condição undefined
 */
async function fixPayableAccounts(defaultCondition) {
  try {
    console.log('\n💰 Verificando contas a pagar...');
    
    const contasSnap = await getDocs(collection(db, "contasAPagar"));
    const contas = contasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const contasSemCondicao = contas.filter(c => !c.condicaoPagamentoId);
    
    console.log(`📊 Total de contas: ${contas.length}`);
    console.log(`⚠️ Contas sem condição de pagamento: ${contasSemCondicao.length}`);
    
    if (contasSemCondicao.length === 0) {
      console.log('✅ Todas as contas já possuem condição de pagamento');
      return 0;
    }
    
    const batch = writeBatch(db);
    let count = 0;
    
    for (const conta of contasSemCondicao) {
      const contaRef = doc(db, "contasAPagar", conta.id);
      batch.update(contaRef, {
        condicaoPagamentoId: defaultCondition.id,
        observacoes: (conta.observacoes || '') + 
          `\n[CORREÇÃO AUTOMÁTICA] Condição de pagamento atribuída: ${defaultCondition.descricao}`
      });
      
      count++;
      console.log(`🔧 Corrigindo conta ${conta.numeroDocumento || conta.id}`);
    }
    
    await batch.commit();
    console.log(`✅ ${count} contas corrigidas com sucesso`);
    
    return count;
    
  } catch (error) {
    console.error('❌ Erro ao corrigir contas a pagar:', error);
    throw error;
  }
}

/**
 * Função principal
 */
async function main() {
  try {
    console.log('🚀 Iniciando correção de condições de pagamento...\n');
    
    // 1. Buscar condição padrão
    const defaultCondition = await getDefaultPaymentCondition();
    
    // 2. Corrigir pedidos
    const pedidosCorrigidos = await fixPurchaseOrders(defaultCondition);
    
    // 3. Corrigir contas a pagar
    const contasCorrigidas = await fixPayableAccounts(defaultCondition);
    
    // 4. Resumo
    console.log('\n📋 RESUMO DA CORREÇÃO:');
    console.log(`✅ Pedidos corrigidos: ${pedidosCorrigidos}`);
    console.log(`✅ Contas corrigidas: ${contasCorrigidas}`);
    console.log(`🎯 Condição padrão usada: ${defaultCondition.descricao}`);
    
    if (pedidosCorrigidos + contasCorrigidas > 0) {
      console.log('\n⚠️ IMPORTANTE:');
      console.log('- Verifique se as correções estão corretas');
      console.log('- Considere revisar os documentos corrigidos');
      console.log('- Configure condições de pagamento adequadas para novos documentos');
    }
    
    console.log('\n🎉 Correção concluída com sucesso!');
    
  } catch (error) {
    console.error('\n❌ ERRO DURANTE A CORREÇÃO:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as fixPaymentConditions };
