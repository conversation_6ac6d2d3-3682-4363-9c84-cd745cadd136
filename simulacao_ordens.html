<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simulação de Ordens de Produção</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.4/xlsx.full.min.js"></script>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --warning-color: #e9730c;
      --header-bg: #354a5f;
    }

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      margin: 0;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: bold;
    }

    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      padding: 10px 20px;
      background-color: var(--success-color);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }

    button:hover {
      background-color: var(--success-hover);
    }

    .back-button {
      background-color: #6c757d;
    }

    .preview-container {
      margin-top: 20px;
      border: 1px solid var(--border-color);
      padding: 20px;
      border-radius: 4px;
    }

    .materials-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .materials-table th,
    .materials-table td {
      padding: 8px;
      border: 1px solid #ddd;
      text-align: left;
    }

    .materials-table th {
      background-color: #f8f9fa;
    }

    .status-ok { 
      color: var(--success-color);
      cursor: help;
    }
    .status-warning { 
      color: var(--warning-color);
      cursor: help;
    }
    .status-error { 
      color: var(--danger-color);
      cursor: help;
    }

    /* Estilo para o tooltip */
    [title] {
      position: relative;
    }

    [title]:hover::after {
      content: attr(title);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      padding: 5px 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 1000;
    }

    .loading {
      display: none;
    }

    .confirm-button {
      background-color: var(--primary-color);
      margin-right: 10px;
    }

    .confirm-button:hover {
      background-color: var(--primary-hover);
    }

    .export-button {
      background-color: #28a745;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      margin-left: 10px;
    }

    .export-button:hover {
      background-color: #218838;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Simulação de Ordens de Produção</h1>
      <button onclick="window.location.href='index.html'" class="back-button">Voltar</button>
    </div>

    <div class="form-group">
      <div class="form-row">
        <div class="form-col">
          <label>Produto:</label>
          <select id="productSelect" required>
            <option value="">Selecione o produto...</option>
          </select>
        </div>
        <div class="form-col">
          <label>Quantidade:</label>
          <input type="number" id="quantity" min="0.001" step="0.001" required>
        </div>
      </div>

      <div class="form-row">
        <div class="form-col">
          <label>Data de Entrega:</label>
          <input type="date" id="dueDate" required>
        </div>
        <div class="form-col">
          <label>Prioridade:</label>
          <select id="priority" required>
            <option value="normal">Normal</option>
            <option value="alta">Alta</option>
            <option value="urgente">Urgente</option>
          </select>
        </div>
      </div>

      <div class="form-row">
        <div class="form-col">
          <label>Centro de Custo:</label>
          <select id="centroCusto" required>
            <option value="">Selecione...</option>
          </select>
        </div>
        <div class="form-col">
          <label>Armazém de Produção:</label>
          <select id="warehouseProducao" required>
            <option value="">Selecione...</option>
          </select>
        </div>
      </div>

      <div style="margin-top: 20px;">
        <button onclick="generateSimulation()">Simular Ordem</button>
      </div>
    </div>

    <div id="previewContent" class="preview-container" style="display: none;">
      <!-- O conteúdo da simulação será gerado aqui -->
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estruturas = [];
    let estoques = [];
    let centrosCusto = [];
    let armazens = [];

    // Configurações de status de saldo
    const STOCK_CONFIG = {
      SAFETY_FACTOR: 1.2, // Fator de segurança (120%)
      MIN_STOCK_DAYS: 5,  // Dias mínimos de estoque
    };

    function checkStockStatus(item) {
      // Se não for matéria-prima, sempre OK
      if (item.tipo !== 'MP') {
        return {
          class: 'status-ok',
          text: 'OK',
          details: 'Não é matéria-prima'
        };
      }

      const saldoAtual = parseFloat(item.saldoEstoque) || 0;
      const quantidadeNecessaria = parseFloat(item.quantidade) || 0;
      const necessidade = parseFloat(item.necessidade) || 0;

      // Sem saldo suficiente
      if (necessidade > 0) {
        return {
          class: 'status-error',
          text: 'Sem Saldo',
          details: `Faltam ${necessidade.toFixed(3)} unidades`
        };
      }

      // Saldo abaixo do fator de segurança
      const saldoMinimo = quantidadeNecessaria * STOCK_CONFIG.SAFETY_FACTOR;
      if (saldoAtual < saldoMinimo) {
        const percentualSaldo = ((saldoAtual / quantidadeNecessaria) * 100).toFixed(1);
        return {
          class: 'status-warning',
          text: 'Saldo Baixo',
          details: `${percentualSaldo}% do necessário`
        };
      }

      // Saldo OK
      const diasEstoque = ((saldoAtual / quantidadeNecessaria) * 30).toFixed(1);
      return {
        class: 'status-ok',
        text: 'OK',
        details: `${diasEstoque} dias de estoque`
      };
    }

    function getOverallStatus(bomStructure) {
      // Filtrar apenas itens MP
      const mpItems = bomStructure.filter(item => item.tipo === 'MP');

      if (mpItems.length === 0) {
        return {
          class: 'status-ok',
          text: 'OK',
          details: 'Não há matérias-primas'
        };
      }

      // Contar status
      let semSaldo = 0;
      let saldoBaixo = 0;
      let saldoOk = 0;
      let detalhes = [];

      mpItems.forEach(item => {
        const status = checkStockStatus(item);
        if (status.class === 'status-error') {
          semSaldo++;
          detalhes.push(`${item.codigo}: Faltam ${item.necessidade} ${item.unidade}`);
        } else if (status.class === 'status-warning') {
          saldoBaixo++;
          detalhes.push(`${item.codigo}: Saldo baixo`);
        } else {
          saldoOk++;
        }
      });

      // Determinar status geral
      if (semSaldo > 0) {
        return {
          class: 'status-error',
          text: `${semSaldo} item(s) sem saldo`,
          details: detalhes.join('\n')
        };
      } else if (saldoBaixo > 0) {
        return {
          class: 'status-warning',
          text: `${saldoBaixo} item(s) com saldo baixo`,
          details: detalhes.join('\n')
        };
      }

      return {
        class: 'status-ok',
        text: 'Todos os itens OK',
        details: `${mpItems.length} matérias-primas com saldo suficiente`
      };
    }

    window.onload = async function() {
      await loadData();
      updateProductSelect();
      setupCentroCustoSelect();
      setupWarehouseSelect();
    };

    async function loadData() {
      try {
        const [produtosSnap, estruturasSnap, estoquesSnap, centrosCustoSnap, armazensSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "centrosCusto")),
          getDocs(collection(db, "armazens"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        centrosCusto = centrosCustoSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        alert("Erro ao carregar dados. Por favor, recarregue a página.");
      }
    }

    function updateProductSelect() {
      const select = document.getElementById('productSelect');
      select.innerHTML = '<option value="">Selecione o produto...</option>';

      produtos
        .filter(p => p.tipo === 'PA' || p.tipo === 'SP')
        .forEach(produto => {
          select.innerHTML += `<option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>`;
        });
    }

    function setupCentroCustoSelect() {
      const select = document.getElementById('centroCusto');
      select.innerHTML = '<option value="">Selecione...</option>';

      centrosCusto.forEach(cc => {
        select.innerHTML += `<option value="${cc.id}">${cc.codigo} - ${cc.descricao}</option>`;
      });
    }

    function setupWarehouseSelect() {
      const select = document.getElementById('warehouseProducao');
      select.innerHTML = '<option value="">Selecione...</option>';

      armazens
        .filter(a => a.tipo === 'PRODUCAO')
        .forEach(armazem => {
          select.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
        });
    }

    async function generateBOMStructure(produtoId, quantidade, level = 0, path = []) {
      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);

      if (!produto) return null;
      if (path.includes(produtoId)) return null;

      let materialsRows = [];

      materialsRows.push({
        level,
        codigo: produto.codigo,
        descricao: produto.descricao,
        tipo: produto.tipo,
        quantidade: quantidade.toFixed(3),
        unidade: produto.unidade,
        saldoEstoque: 0,
        necessidade: 0
      });

      if (estrutura && estrutura.componentes) {
        for (const comp of estrutura.componentes) {
          const quantidadeTotal = quantidade * comp.quantidade;
          const subStructure = await generateBOMStructure(
            comp.componentId, 
            quantidadeTotal,
            level + 1,
            [...path, produtoId]
          );

          if (subStructure) {
            materialsRows = materialsRows.concat(subStructure);
          }
        }
      }

      return materialsRows;
    }

    window.generateSimulation = async function() {
      const produtoId = document.getElementById('productSelect').value;
      const quantidade = parseFloat(document.getElementById('quantity').value);
      const dataEntrega = document.getElementById('dueDate').value;
      const prioridade = document.getElementById('priority').value;
      const centroCustoId = document.getElementById('centroCusto').value;
      const armazemId = document.getElementById('warehouseProducao').value;

      if (!produtoId || !quantidade || !dataEntrega || !centroCustoId || !armazemId) {
        alert('Por favor, preencha todos os campos.');
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
      const centroCusto = centrosCusto.find(cc => cc.id === centroCustoId);
      const armazem = armazens.find(a => a.id === armazemId);

      if (!estrutura) {
        alert('Este produto não possui estrutura cadastrada.');
        return;
      }

      const bomStructure = await generateBOMStructure(produtoId, quantidade);

      // Calcular saldos e necessidades
      for (const item of bomStructure) {
        const estoque = estoques.find(e => {
          const prod = produtos.find(p => p.codigo === item.codigo);
          return prod && e.produtoId === prod.id && e.armazemId === armazemId;
        });

        item.saldoEstoque = estoque ? estoque.saldo : 0;
        item.necessidade = Math.max(0, item.quantidade - item.saldoEstoque);
      }

      const previewContent = document.getElementById('previewContent');
      const overallStatus = getOverallStatus(bomStructure);

      previewContent.innerHTML = `
        <h2>Simulação da Ordem de Produção</h2>
        <div class="order-info">
          <p><strong>Produto:</strong> ${produto.codigo} - ${produto.descricao}</p>
          <p><strong>Quantidade:</strong> ${quantidade} ${produto.unidade}</p>
          <p><strong>Data de Entrega:</strong> ${new Date(dataEntrega).toLocaleDateString()}</p>
          <p><strong>Prioridade:</strong> ${prioridade}</p>
          <p><strong>Centro de Custo:</strong> ${centroCusto.codigo} - ${centroCusto.descricao}</p>
          <p><strong>Armazém de Produção:</strong> ${armazem.codigo} - ${armazem.nome}</p>
          <p><strong>Status Geral:</strong> <span class="${overallStatus.class}" title="${overallStatus.details}">${overallStatus.text}</span></p>
        </div>

        <h3>Lista de Materiais</h3>
        <table class="materials-table">
          <thead>
            <tr>
              <th>Nível</th>
              <th>Código</th>
              <th>Descrição</th>
              <th>Tipo</th>
              <th>Quantidade</th>
              <th>Unidade</th>
              <th>Saldo</th>
              <th>Necessidade</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${bomStructure.map(item => {
              const status = checkStockStatus(item);

              return `
                <tr>
                  <td>${'→'.repeat(item.level)}</td>
                  <td>${item.codigo}</td>
                  <td>${item.descricao}</td>
                  <td>${item.tipo}</td>
                  <td>${item.quantidade}</td>
                  <td>${item.unidade}</td>
                  <td>${item.saldoEstoque}</td>
                  <td>${item.necessidade}</td>
                  <td class="${status.class}" title="${status.details}">${status.text}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <button class="confirm-button" onclick="confirmarOrdem()">Confirmar Ordem</button>
          <button onclick="window.location.reload()">Nova Simulação</button>
          <button class="export-button" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> Exportar Excel</button>
        </div>
      `;

      previewContent.style.display = 'block';
    };

    window.confirmarOrdem = async function() {
      if (!confirm('Deseja confirmar esta ordem de produção?')) {
        return;
      }

      const produtoId = document.getElementById('productSelect').value;
      const quantidade = parseFloat(document.getElementById('quantity').value);
      const dataEntrega = document.getElementById('dueDate').value;
      const prioridade = document.getElementById('priority').value;
      const centroCustoId = document.getElementById('centroCusto').value;
      const armazemId = document.getElementById('warehouseProducao').value;

      // Redirecionar para a página de ordens de produção com os parâmetros
      const params = new URLSearchParams({
        produtoId,
        quantidade,
        dataEntrega,
        prioridade,
        centroCustoId,
        armazemId
      });

      window.location.href = `ordens_producao.html?${params.toString()}`;
    };

    window.exportToExcel = function() {
      const produto = produtos.find(p => p.id === document.getElementById('productSelect').value);
      const quantidade = parseFloat(document.getElementById('quantity').value);
      const dataEntrega = document.getElementById('dueDate').value;
      const prioridade = document.getElementById('priority').value;
      const centroCusto = centrosCusto.find(cc => cc.id === document.getElementById('centroCusto').value);
      const armazem = armazens.find(a => a.id === document.getElementById('warehouseProducao').value);

      // Get table data
      const table = document.querySelector('.materials-table');
      const rows = Array.from(table.querySelectorAll('tbody tr'));

      // Prepare worksheet data
      const wsData = [
        ['Simulação de Ordem de Produção'],
        [],
        ['Produto:', `${produto.codigo} - ${produto.descricao}`],
        ['Quantidade:', quantidade, produto.unidade], // Quantidade como número
        ['Data de Entrega:', new Date(dataEntrega).toLocaleDateString()],
        ['Prioridade:', prioridade],
        ['Centro de Custo:', `${centroCusto.codigo} - ${centroCusto.descricao}`],
        ['Armazém de Produção:', `${armazem.codigo} - ${armazem.nome}`],
        [],
        ['Lista de Materiais'],
        ['Nível', 'Código', 'Descrição', 'Tipo', 'Quantidade', 'Unidade', 'Saldo', 'Necessidade', 'Status']
      ];

      // Add table data with numeric values
      rows.forEach(row => {
        const cells = Array.from(row.querySelectorAll('td'));
        wsData.push([
          cells[0].textContent, // Nível (setas)
          cells[1].textContent, // Código
          cells[2].textContent, // Descrição
          cells[3].textContent, // Tipo
          parseFloat(cells[4].textContent), // Quantidade como número
          cells[5].textContent, // Unidade
          parseFloat(cells[6].textContent), // Saldo como número
          parseFloat(cells[7].textContent), // Necessidade como número
          cells[8].textContent  // Status
        ]);
      });

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // Set column widths
      const colWidths = [10, 15, 40, 10, 15, 10, 15, 15, 15];
      ws['!cols'] = colWidths.map(width => ({ width }));

      // Format numeric columns
      const numericColumns = ['E', 'G', 'H']; // Colunas de quantidade, saldo e necessidade
      const range = XLSX.utils.decode_range(ws['!ref']);

      // Começar da linha 12 (após os cabeçalhos)
      for (let R = 11; R <= range.e.r; R++) {
        numericColumns.forEach(C => {
          const cell = ws[`${C}${R + 1}`];
          if (cell) {
            cell.z = '#,##0.000'; // Formato numérico com 3 casas decimais
          }
        });
      }

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Simulação OP');

      // Generate filename with current date
      const date = new Date().toISOString().split('T')[0];
      const filename = `simulacao_op_${produto.codigo}_${date}.xlsx`;

      // Save file
      XLSX.writeFile(wb, filename);
    };
  </script>
</body>
</html> 