<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Teste Firebase - Diagnóstico de Conexão</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .log-container {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .config-display {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Teste Firebase - Diagnóstico de Conexão</h1>
            <p>Ferramenta para diagnosticar problemas de conexão com Firebase</p>
        </div>

        <div class="status-card" id="connectionStatus">
            <h3>📡 Status da Conexão</h3>
            <p>Aguardando teste...</p>
        </div>

        <div class="status-card" id="configStatus">
            <h3>⚙️ Configuração Firebase</h3>
            <div class="config-display" id="configDisplay">Carregando...</div>
        </div>

        <div class="status-card" id="cacheStatus">
            <h3>🗄️ Status do Cache</h3>
            <p id="cacheInfo">Verificando cache...</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="testarConexao()">🔍 Testar Conexão</button>
            <button class="btn danger" onclick="limparCache()">🗑️ Limpar Cache</button>
            <button class="btn" onclick="recarregarPagina()">🔄 Recarregar</button>
        </div>

        <div class="log-container" id="logContainer">
            <div>📋 Log de Diagnóstico:</div>
        </div>
    </div>

    <script type="module">
        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Função para limpar cache
        async function limparCache() {
            addLog('🧹 Iniciando limpeza de cache...');
            
            try {
                // Limpar localStorage
                let removedLS = 0;
                Object.keys(localStorage).forEach(key => {
                    if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                        localStorage.removeItem(key);
                        removedLS++;
                    }
                });
                addLog(`🗑️ Removidos ${removedLS} itens do localStorage`);

                // Limpar sessionStorage
                let removedSS = 0;
                Object.keys(sessionStorage).forEach(key => {
                    if (key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')) {
                        sessionStorage.removeItem(key);
                        removedSS++;
                    }
                });
                addLog(`🗑️ Removidos ${removedSS} itens do sessionStorage`);

                // Limpar IndexedDB
                if ('indexedDB' in window) {
                    const databases = [
                        'firebase-heartbeat-database',
                        'firebase-installations-database',
                        'firebaseLocalStorageDb',
                        'sistema-mrp-9a8c5',
                        'banco-mrp'
                    ];

                    for (const dbName of databases) {
                        try {
                            await new Promise((resolve) => {
                                const deleteReq = indexedDB.deleteDatabase(dbName);
                                deleteReq.onsuccess = () => {
                                    addLog(`🗑️ Database removida: ${dbName}`);
                                    resolve();
                                };
                                deleteReq.onerror = () => resolve();
                                deleteReq.onblocked = () => resolve();
                            });
                        } catch (error) {
                            addLog(`⚠️ Erro ao remover ${dbName}: ${error.message}`, 'error');
                        }
                    }
                }

                addLog('✅ Cache limpo com sucesso!', 'success');
                document.getElementById('cacheStatus').className = 'status-card success';
                document.getElementById('cacheInfo').textContent = 'Cache limpo com sucesso!';
                
            } catch (error) {
                addLog(`❌ Erro ao limpar cache: ${error.message}`, 'error');
            }
        }

        // Função para testar conexão
        async function testarConexao() {
            addLog('🔍 Iniciando teste de conexão...');
            
            try {
                // Importar Firebase
                const { db } = await import('./firebase-config.js');
                addLog('✅ Firebase config carregado', 'success');
                
                // Verificar configuração
                if (db && db.app) {
                    const config = {
                        projectId: db.app.options.projectId,
                        authDomain: db.app.options.authDomain,
                        appName: db.app.name
                    };
                    
                    document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
                    addLog(`🔍 Projeto conectado: ${config.projectId}`, 'success');
                    
                    // Testar conexão real
                    const { collection, getDocs } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                    
                    addLog('🔄 Testando conexão com Firestore...');
                    const testCollection = await getDocs(collection(db, "produtos"));
                    
                    addLog(`✅ Conexão bem-sucedida! ${testCollection.docs.length} produtos encontrados`, 'success');
                    
                    document.getElementById('connectionStatus').className = 'status-card success';
                    document.getElementById('connectionStatus').innerHTML = `
                        <h3>📡 Status da Conexão</h3>
                        <p>✅ Conectado com sucesso ao projeto: <strong>${config.projectId}</strong></p>
                        <p>📊 ${testCollection.docs.length} produtos encontrados</p>
                    `;
                    
                } else {
                    throw new Error('Firebase não inicializado corretamente');
                }
                
            } catch (error) {
                addLog(`❌ Erro na conexão: ${error.message}`, 'error');
                document.getElementById('connectionStatus').className = 'status-card error';
                document.getElementById('connectionStatus').innerHTML = `
                    <h3>📡 Status da Conexão</h3>
                    <p>❌ Erro na conexão: ${error.message}</p>
                `;
            }
        }

        // Função para recarregar página
        function recarregarPagina() {
            window.location.reload();
        }

        // Verificar cache ao carregar
        function verificarCache() {
            const firebaseKeys = Object.keys(localStorage).filter(key => 
                key.includes('firebase') || key.includes('firestore') || key.includes('sistema-mrp')
            );
            
            if (firebaseKeys.length > 0) {
                document.getElementById('cacheStatus').className = 'status-card warning';
                document.getElementById('cacheInfo').innerHTML = `
                    ⚠️ ${firebaseKeys.length} itens de cache encontrados:<br>
                    ${firebaseKeys.slice(0, 3).join(', ')}${firebaseKeys.length > 3 ? '...' : ''}
                `;
            } else {
                document.getElementById('cacheStatus').className = 'status-card success';
                document.getElementById('cacheInfo').textContent = '✅ Nenhum cache Firebase encontrado';
            }
        }

        // Expor funções globalmente
        window.testarConexao = testarConexao;
        window.limparCache = limparCache;
        window.recarregarPagina = recarregarPagina;

        // Inicializar
        addLog('🚀 Ferramenta de diagnóstico carregada');
        verificarCache();
    </script>
</body>
</html>
