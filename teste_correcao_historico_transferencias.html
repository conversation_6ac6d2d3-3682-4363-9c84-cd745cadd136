<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Correção Histórico de Transferências</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        input, select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Teste - Correção Histórico de Transferências</h1>
            <p>Ferramenta para testar a preservação do histórico de transferências ao alterar OPs</p>
        </div>

        <!-- Seção 1: Verificar OP -->
        <div class="test-section">
            <h3>📋 1. Verificar OP e Histórico</h3>
            <p>Digite o ID da OP para verificar o histórico atual de transferências:</p>
            
            <input type="text" id="opIdInput" placeholder="ID da OP (ex: OP25071021)" />
            <button class="btn btn-primary" onclick="verificarOP()">🔍 Verificar OP</button>
            
            <div id="resultadoVerificacao" class="result" style="display: none;"></div>
        </div>

        <!-- Seção 2: Simular Alteração -->
        <div class="test-section">
            <h3>⚙️ 2. Simular Alteração com Preservação</h3>
            <p>Simula uma alteração na OP preservando o histórico de transferências:</p>
            
            <button class="btn btn-warning" onclick="simularAlteracao()" disabled id="btnSimular">
                🔄 Simular Alteração
            </button>
            
            <div id="resultadoSimulacao" class="result" style="display: none;"></div>
        </div>

        <!-- Seção 3: Comparar Antes/Depois -->
        <div class="test-section">
            <h3>📊 3. Comparação Antes/Depois</h3>
            <p>Compare o estado da OP antes e depois da alteração:</p>
            
            <button class="btn btn-success" onclick="compararEstados()" disabled id="btnComparar">
                📈 Comparar Estados
            </button>
            
            <div id="resultadoComparacao" class="result" style="display: none;"></div>
        </div>

        <!-- Seção 4: Logs do Console -->
        <div class="test-section">
            <h3>📝 4. Logs do Sistema</h3>
            <p>Logs detalhados das operações:</p>
            
            <button class="btn btn-info" onclick="mostrarLogs()">📋 Mostrar Logs</button>
            <button class="btn btn-danger" onclick="limparLogs()">🗑️ Limpar Logs</button>
            
            <div id="logsContainer" class="result info" style="display: none; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Firebase -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getFirestore, 
            collection, 
            doc, 
            getDoc, 
            getDocs, 
            query, 
            where, 
            updateDoc,
            Timestamp 
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Configuração do Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyAIp-rFuZZsBCNVJ3pSge4TE-XUuwYygrI",
            authDomain: "bancomrp-f9f5d.firebaseapp.com",
            projectId: "banco-mrp",
            storageBucket: "bancomrp-f9f5d.appspot.com",
            messagingSenderId: "888734550315",
            appId: "1:888734550315:web:4e9d8b2c5c7b8f9e6d4c5a"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Variáveis globais
        window.db = db;
        window.opAtual = null;
        window.estadoAntes = null;
        window.estadoDepois = null;
        window.logs = [];

        // Função para adicionar log
        window.addLog = function(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            window.logs.push({
                timestamp: timestamp,
                message: message,
                type: type
            });
            console.log(`[${timestamp}] ${message}`);
        };

        console.log('🚀 Sistema de teste inicializado!');
        window.addLog('Sistema de teste inicializado', 'success');
    </script>

    <script>
        // Função para verificar OP
        async function verificarOP() {
            const opId = document.getElementById('opIdInput').value.trim();
            const resultado = document.getElementById('resultadoVerificacao');
            
            if (!opId) {
                alert('❌ Digite o ID da OP!');
                return;
            }
            
            try {
                addLog(`Verificando OP: ${opId}`, 'info');
                
                // Buscar OP
                const opDoc = await getDoc(doc(db, "ordensProducao", opId));
                if (!opDoc.exists()) {
                    throw new Error(`OP ${opId} não encontrada`);
                }
                
                window.opAtual = { id: opId, ...opDoc.data() };
                window.estadoAntes = JSON.parse(JSON.stringify(window.opAtual));
                
                // Buscar transferências
                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", opId),
                    where("tipo", "==", "OP")
                );
                
                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const transferencias = transferenciasSnapshot.docs.map(doc => doc.data());
                
                // Montar relatório
                let relatorio = `✅ OP ENCONTRADA\n\n`;
                relatorio += `ID: ${opId}\n`;
                relatorio += `Número: ${window.opAtual.numero || window.opAtual.numeroOP}\n`;
                relatorio += `Status: ${window.opAtual.status}\n`;
                relatorio += `Materiais: ${window.opAtual.materiaisNecessarios?.length || 0}\n`;
                relatorio += `Transferências: ${transferencias.length}\n\n`;
                
                if (window.opAtual.materiaisNecessarios) {
                    relatorio += `📦 MATERIAIS:\n`;
                    window.opAtual.materiaisNecessarios.forEach((material, index) => {
                        const transferenciasDoMaterial = transferencias.filter(t => 
                            t.produtoId === material.produtoId
                        );
                        const totalTransferido = transferenciasDoMaterial.reduce((total, t) => 
                            total + (t.quantidade || 0), 0
                        );
                        
                        relatorio += `${index + 1}. ${material.produtoId}\n`;
                        relatorio += `   Saldo Reservado: ${material.saldoReservado || 0}\n`;
                        relatorio += `   Total Transferido: ${totalTransferido}\n`;
                        relatorio += `   Status: ${(material.saldoReservado || 0) === totalTransferido ? '✅ OK' : '⚠️ DIVERGÊNCIA'}\n\n`;
                    });
                }
                
                resultado.className = 'result success';
                resultado.textContent = relatorio;
                resultado.style.display = 'block';
                
                // Habilitar botões
                document.getElementById('btnSimular').disabled = false;
                document.getElementById('btnComparar').disabled = false;
                
                addLog(`OP ${opId} verificada com sucesso`, 'success');
                
            } catch (error) {
                resultado.className = 'result error';
                resultado.textContent = `❌ Erro: ${error.message}`;
                resultado.style.display = 'block';
                
                addLog(`Erro ao verificar OP: ${error.message}`, 'error');
            }
        }

        // Função para simular alteração
        async function simularAlteracao() {
            if (!window.opAtual) {
                alert('❌ Nenhuma OP carregada!');
                return;
            }
            
            const resultado = document.getElementById('resultadoSimulacao');
            
            try {
                addLog('Iniciando simulação de alteração...', 'info');
                
                // Simular preservação do histórico
                const historicoPreservado = await preservarHistoricoTransferencias(
                    window.opAtual.id,
                    window.opAtual.materiaisNecessarios || [],
                    window.opAtual.materiaisNecessarios || []
                );
                
                let relatorio = `🔄 SIMULAÇÃO DE ALTERAÇÃO\n\n`;
                relatorio += `OP: ${window.opAtual.numero || window.opAtual.numeroOP}\n`;
                relatorio += `Materiais processados: ${historicoPreservado.length}\n\n`;
                
                relatorio += `📊 RESULTADO:\n`;
                historicoPreservado.forEach((material, index) => {
                    relatorio += `${index + 1}. ${material.produtoId}\n`;
                    relatorio += `   Saldo Reservado: ${material.saldoReservado || 0}\n`;
                    relatorio += `   Histórico Preservado: ${material.historicoPreservado ? 'Sim' : 'Não'}\n\n`;
                });
                
                resultado.className = 'result success';
                resultado.textContent = relatorio;
                resultado.style.display = 'block';
                
                addLog('Simulação concluída com sucesso', 'success');
                
            } catch (error) {
                resultado.className = 'result error';
                resultado.textContent = `❌ Erro na simulação: ${error.message}`;
                resultado.style.display = 'block';
                
                addLog(`Erro na simulação: ${error.message}`, 'error');
            }
        }

        // Função para comparar estados
        async function compararEstados() {
            if (!window.estadoAntes) {
                alert('❌ Nenhum estado anterior disponível!');
                return;
            }
            
            const resultado = document.getElementById('resultadoComparacao');
            
            try {
                // Buscar estado atual da OP
                const opDoc = await getDoc(doc(db, "ordensProducao", window.opAtual.id));
                window.estadoDepois = opDoc.exists() ? opDoc.data() : null;
                
                let relatorio = `📊 COMPARAÇÃO DE ESTADOS\n\n`;
                relatorio += `OP: ${window.opAtual.numero || window.opAtual.numeroOP}\n\n`;
                
                relatorio += `📋 ANTES:\n`;
                relatorio += `Materiais: ${window.estadoAntes.materiaisNecessarios?.length || 0}\n`;
                if (window.estadoAntes.materiaisNecessarios) {
                    window.estadoAntes.materiaisNecessarios.forEach((material, index) => {
                        relatorio += `${index + 1}. ${material.produtoId} - Reservado: ${material.saldoReservado || 0}\n`;
                    });
                }
                
                relatorio += `\n📋 DEPOIS:\n`;
                relatorio += `Materiais: ${window.estadoDepois?.materiaisNecessarios?.length || 0}\n`;
                if (window.estadoDepois?.materiaisNecessarios) {
                    window.estadoDepois.materiaisNecessarios.forEach((material, index) => {
                        relatorio += `${index + 1}. ${material.produtoId} - Reservado: ${material.saldoReservado || 0}\n`;
                    });
                }
                
                resultado.className = 'result info';
                resultado.textContent = relatorio;
                resultado.style.display = 'block';
                
                addLog('Comparação de estados concluída', 'info');
                
            } catch (error) {
                resultado.className = 'result error';
                resultado.textContent = `❌ Erro na comparação: ${error.message}`;
                resultado.style.display = 'block';
                
                addLog(`Erro na comparação: ${error.message}`, 'error');
            }
        }

        // Função para mostrar logs
        function mostrarLogs() {
            const container = document.getElementById('logsContainer');
            
            if (window.logs.length === 0) {
                container.textContent = 'Nenhum log disponível.';
            } else {
                let logsText = '';
                window.logs.forEach(log => {
                    logsText += `[${log.timestamp}] ${log.message}\n`;
                });
                container.textContent = logsText;
            }
            
            container.style.display = 'block';
        }

        // Função para limpar logs
        function limparLogs() {
            window.logs = [];
            const container = document.getElementById('logsContainer');
            container.style.display = 'none';
            addLog('Logs limpos', 'info');
        }

        // Função de preservação do histórico (copiada do arquivo principal)
        async function preservarHistoricoTransferencias(opId, materiaisAtuais, novosMateriais) {
            try {
                addLog(`Preservando histórico de transferências para OP: ${opId}`, 'info');
                
                // Buscar transferências já realizadas para esta OP
                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", opId),
                    where("tipo", "==", "OP")
                );
                
                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const transferenciasRealizadas = {};
                
                // Agrupar transferências por produto
                transferenciasSnapshot.docs.forEach(doc => {
                    const transfer = doc.data();
                    if (!transferenciasRealizadas[transfer.produtoId]) {
                        transferenciasRealizadas[transfer.produtoId] = 0;
                    }
                    transferenciasRealizadas[transfer.produtoId] += transfer.quantidade;
                });
                
                addLog(`Transferências encontradas: ${JSON.stringify(transferenciasRealizadas)}`, 'info');
                
                // Aplicar transferências aos novos materiais
                const materiaisComHistorico = novosMateriais.map(novoMaterial => {
                    const transferido = transferenciasRealizadas[novoMaterial.produtoId] || 0;
                    
                    if (transferido > 0) {
                        addLog(`Preservando ${transferido} transferidos para produto ${novoMaterial.produtoId}`, 'info');
                        return {
                            ...novoMaterial,
                            saldoReservado: transferido,
                            historicoPreservado: true
                        };
                    }
                    
                    return novoMaterial;
                });
                
                return materiaisComHistorico;
                
            } catch (error) {
                addLog(`Erro ao preservar histórico de transferências: ${error.message}`, 'error');
                // Em caso de erro, retornar materiais originais
                return novosMateriais;
            }
        }
    </script>
</body>
</html>
