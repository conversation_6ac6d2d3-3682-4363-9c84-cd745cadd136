<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Relatório de Movimentações - Sistema MRP</title>
    <link rel="stylesheet" href="styles/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .report-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .report-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: column;
            text-align: center;
        }

        .report-header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .main-content {
            padding: 30px;
        }

        .controls-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
            text-align: center;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .summary-card:nth-child(1) {
            border-left-color: #9b59b6;
        }

        .summary-card:nth-child(2) {
            border-left-color: #3498db;
        }

        .summary-card:nth-child(3) {
            border-left-color: #f39c12;
        }

        .summary-card:nth-child(4) {
            border-left-color: #27ae60;
        }

        .summary-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-card div:last-child {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }
        .movements-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .data-table th:hover {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            vertical-align: middle;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }
        .movement-type {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .type-entrada { background: #d4edda; color: #155724; }
        .type-saida { background: #f8d7da; color: #721c24; }
        .type-transferencia { background: #d1ecf1; color: #0c5460; }
        .type-ajuste { background: #fff3cd; color: #856404; }
        .type-producao { background: #e2e3e5; color: #383d41; }
        .type-solicitacao { background: #cce5ff; color: #004085; }
        .type-cotacao { background: #e6f3ff; color: #0056b3; }
        .type-pedido { background: #fff0e6; color: #cc5500; }
        .type-recebimento { background: #e6ffe6; color: #006600; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .font-weight-bold { font-weight: bold; }
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-ativo { background: #d4edda; color: #155724; }
        .status-pendente { background: #fff3cd; color: #856404; }
        .status-cancelado { background: #f8d7da; color: #721c24; }
        .status-finalizado { background: #e2e3e5; color: #383d41; }
        .status-recebido { background: #d4edda; color: #155724; }

        /* Estilos para botões de ação */
        .action-buttons {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .btn-action {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }



        /* Filtros Inteligentes */
        .smart-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .filter-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #bbdefb;
        }

        .filter-tag:hover {
            background: #1976d2;
            color: white;
            transform: translateY(-1px);
        }

        .filter-tag.active {
            background: #1976d2;
            color: white;
        }

        /* Estatísticas Avançadas */
        .stats-advanced {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-mini {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            border-left: 4px solid;
        }

        .stat-mini:nth-child(1) { border-left-color: #e74c3c; }
        .stat-mini:nth-child(2) { border-left-color: #3498db; }
        .stat-mini:nth-child(3) { border-left-color: #f39c12; }
        .stat-mini:nth-child(4) { border-left-color: #27ae60; }
        .stat-mini:nth-child(5) { border-left-color: #9b59b6; }
        .stat-mini:nth-child(6) { border-left-color: #e67e22; }

        .stat-mini-number {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
        }

        .stat-mini-label {
            font-size: 11px;
            color: #7f8c8d;
            text-transform: uppercase;
            font-weight: 600;
            margin-top: 5px;
        }

        /* Melhorias na tabela */
        .data-table tbody tr {
            transition: all 0.2s ease;
        }

        .data-table tbody tr:hover {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(5px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Responsividade */

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }

            .summary-cards {
                grid-template-columns: 1fr 1fr;
            }

            .smart-filters {
                justify-content: center;
            }
        }

        /* Notificações modernas */
        .notification-modern {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: all 0.4s ease;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        .notification-modern.show {
            transform: translateX(0);
        }

        .notification-modern.success { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .notification-modern.error { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .notification-modern.info { background: linear-gradient(135deg, #3498db, #2980b9); }
        .notification-modern.warning { background: linear-gradient(135deg, #f39c12, #e67e22); }

        /* Toggle para modo escuro */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .theme-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        /* Modo escuro */
        body.dark-mode {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        body.dark-mode .report-container {
            background: #2c3e50;
            color: white;
        }

        body.dark-mode .summary-card,
        body.dark-mode .controls-section,
        body.dark-mode .movements-table {
            background: #34495e;
            color: white;
            border-color: #4a5f7a;
        }

        body.dark-mode .filter-input {
            background: #4a5f7a;
            color: white;
            border-color: #5a6f8a;
        }

        body.dark-mode .data-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        body.dark-mode .data-table tbody tr:hover {
            background: rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <!-- Botão de tema -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Alternar tema escuro/claro">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <div class="report-container">
        <!-- Header -->
        <div class="report-header">
            <h1><i class="fas fa-chart-line"></i> Relatório de Movimentações - MRP</h1>
            <p>Acompanhe todas as atividades e transações do sistema em tempo real</p>

            <!-- Alerta sobre funcionalidades -->
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-top: 15px; text-align: left;">
                <h4 style="margin: 0 0 8px 0; color: #fff3cd;">🆕 Funcionalidades Disponíveis</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em; line-height: 1.4;">
                    <div>
                        <strong>📅 Filtros de Data Corrigidos:</strong><br>
                        ✅ Botão "Apenas Hoje" para ver movimentos de hoje<br>
                        ✅ Botão "Últimos 7 Dias" para período recente<br>
                        ✅ Filtro de data fim agora inclui o dia selecionado
                    </div>
                    <div>
                        <strong>🗑️ Exclusão de Recebimentos:</strong><br>
                        ✅ Excluir recebimentos parciais diretamente na tabela<br>
                        ✅ Reversão automática de movimentações de estoque<br>
                        ✅ Restauração do status do pedido de compra
                    </div>
                </div>
            </div>
        </div>

        <!-- Controles -->
        <div class="controls-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <button class="btn btn-info" onclick="atualizarAutomatico()">
                        🔄 Atualização Automática
                    </button>
                    <button class="btn btn-warning" onclick="window.location.href='index.html'">
                        🏠 Voltar
                    </button>
                </div>
                <div>
                    <strong>📊 Status:</strong> <span id="reportStatus">Pronto para carregar</span>
                </div>
            </div>

            <!-- Filtros -->
            <div style="border-top: 2px solid #e9ecef; padding-top: 20px;">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">🔍 Filtros Avançados</h3>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>Data Início:</label>
                        <input type="date" class="filter-input" id="dataInicio">
                    </div>
                    <div class="filter-group">
                        <label>Data Fim:</label>
                        <input type="date" class="filter-input" id="dataFim">
                    </div>
                    <div class="filter-group">
                        <label>Tipo de Movimentação:</label>
                        <select class="filter-input" id="tipoFilter">
                            <option value="">Todos os Tipos</option>
                            <option value="entrada">📦 Entrada de Estoque</option>
                            <option value="saida">📤 Saída de Estoque</option>
                            <option value="transferencia">🔄 Transferência</option>
                            <option value="ajuste">⚙️ Ajuste de Estoque</option>
                            <option value="producao">🏭 Produção</option>
                            <option value="solicitacao">📋 Solicitação</option>
                            <option value="cotacao">💰 Cotação</option>
                            <option value="pedido">🛒 Pedido</option>
                            <option value="recebimento">📥 Recebimento</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Usuário:</label>
                        <select class="filter-input" id="usuarioFilter">
                            <option value="">Todos os Usuários</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Armazém:</label>
                        <select class="filter-input" id="armazemFilter">
                            <option value="">Todos os Armazéns</option>
                            <option value="ALM01">🏢 ALM01 - Almoxarifado</option>
                            <option value="PROD1">🏭 PROD1 - Produção</option>
                            <option value="QUALIDADE">🔬 Qualidade</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Buscar:</label>
                        <input type="text" class="filter-input" id="searchInput" placeholder="Produto, documento, observação...">
                    </div>
                </div>
                <div class="text-right">
                    <button class="btn btn-primary" onclick="carregarMovimentacoes()">
                        <i class="fas fa-search"></i> Carregar Dados
                    </button>
                    <button class="btn btn-success" onclick="exportarCSV()" id="btnExport" disabled>
                        <i class="fas fa-file-csv"></i> Exportar CSV
                    </button>
                    <button class="btn btn-info" onclick="exportarExcel()" id="btnExportExcel" disabled>
                        <i class="fas fa-file-excel"></i> Exportar Excel
                    </button>
                    <button class="btn btn-warning" onclick="recarregarCaches()" title="Recarregar dados de produtos e armazéns">
                        <i class="fas fa-sync-alt"></i> Atualizar Cache
                    </button>
                    <button class="btn btn-secondary" onclick="diagnosticarDados()" title="Diagnosticar problemas nos dados">
                        <i class="fas fa-stethoscope"></i> Diagnóstico
                    </button>
                    <button class="btn btn-info" onclick="atualizarAutomatico()">
                        <i class="fas fa-clock"></i> Auto-Update
                    </button>
                    <button class="btn btn-dark" onclick="abrirRelatorioDetalhado()" id="btnDetalhado" disabled>
                        <i class="fas fa-chart-bar"></i> Relatório Detalhado
                    </button>
                    <button class="btn btn-warning" onclick="window.location.href='index.html'">
                        <i class="fas fa-home"></i> Voltar
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtros Inteligentes -->
        <div class="smart-filters" id="smartFilters" style="display: none;">
            <div class="filter-tag" onclick="aplicarFiltroInteligente('hoje')">📅 Hoje</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('7dias')">📅 Últimos 7 dias</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('30dias')">📅 Últimos 30 dias</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('entradas')">📦 Entradas</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('saidas')">📤 Saídas</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('recebimentos')">📥 Recebimentos</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('problemas')">⚠️ Com Problemas</div>
            <div class="filter-tag" onclick="aplicarFiltroInteligente('limpar')">🗑️ Limpar Filtros</div>
        </div>

        <!-- Resumo -->
        <div class="summary-cards" id="summaryCards" style="display: none;">
            <div class="summary-card">
                <div class="summary-number" id="totalMovimentos">0</div>
                <div>Total de Movimentos</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="movimentosHoje">0</div>
                <div>Movimentos Hoje</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="usuariosAtivos">0</div>
                <div>Usuários Ativos</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="valorMovimentado">R$ 0</div>
                <div>Valor Movimentado</div>
            </div>
        </div>

        <!-- Estatísticas Avançadas -->
        <div class="stats-advanced" id="statsAdvanced" style="display: none;">
            <div class="stat-mini">
                <div class="stat-mini-number" id="statEntradas">0</div>
                <div class="stat-mini-label">Entradas</div>
            </div>
            <div class="stat-mini">
                <div class="stat-mini-number" id="statSaidas">0</div>
                <div class="stat-mini-label">Saídas</div>
            </div>
            <div class="stat-mini">
                <div class="stat-mini-number" id="statTransferencias">0</div>
                <div class="stat-mini-label">Transferências</div>
            </div>
            <div class="stat-mini">
                <div class="stat-mini-number" id="statAjustes">0</div>
                <div class="stat-mini-label">Ajustes</div>
            </div>
            <div class="stat-mini">
                <div class="stat-mini-number" id="statRecebimentos">0</div>
                <div class="stat-mini-label">Recebimentos</div>
            </div>
            <div class="stat-mini">
                <div class="stat-mini-number" id="statProducao">0</div>
                <div class="stat-mini-label">Produção</div>
            </div>
        </div>



        <!-- Tabela de Movimentações -->
        <div class="movements-table" id="movementsTable" style="display: none;">
            <div class="table-header">
                <h3>📋 Movimentações do Sistema</h3>
                <p><span id="itemCount">0 movimentos</span> | Última atualização: <span id="lastUpdate">-</span></p>
            </div>
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th onclick="ordenarTabela(0)">Data/Hora</th>
                            <th onclick="ordenarTabela(1)">Tipo</th>
                            <th onclick="ordenarTabela(2)">Documento</th>
                            <th onclick="ordenarTabela(3)">Produto/Item</th>
                            <th onclick="ordenarTabela(4)">Quantidade</th>
                            <th onclick="ordenarTabela(5)">Armazém</th>
                            <th onclick="ordenarTabela(6)">Usuário</th>
                            <th onclick="ordenarTabela(7)">Status</th>
                            <th onclick="ordenarTabela(8)">Observações</th>
                            <th style="width: 120px;">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="movementsTableBody">
                        <!-- Dados serão inseridos aqui -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Loading -->
        <div class="loading" id="loadingIndicator">
            <h3>📊 Carregando movimentações...</h3>
            <p>Aguarde enquanto coletamos todas as atividades do sistema.</p>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            query,
            orderBy,
            where,
            limit,
            deleteDoc,
            doc,
            getDoc,
            updateDoc,
            Timestamp
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Sistema', uid: 'sistema' };
        let allMovements = [];
        let filteredMovements = [];
        let autoUpdateInterval = null;
        let produtosCache = new Map();
        let armazensCache = new Map();

        // ===== FUNÇÕES DE CACHE =====
        async function loadProdutosCache() {
            try {
                console.log('📦 Carregando cache de produtos...');
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtosSnap.docs.forEach(doc => {
                    const data = doc.data();
                    produtosCache.set(doc.id, {
                        codigo: data.codigo,
                        descricao: data.descricao,
                        nome: data.nome || data.descricao
                    });
                });
                console.log(`✅ ${produtosCache.size} produtos carregados no cache`);
            } catch (error) {
                console.error('❌ Erro ao carregar produtos:', error);
            }
        }

        async function loadArmazensCache() {
            try {
                console.log('🏪 Carregando cache de armazéns...');
                const armazensSnap = await getDocs(collection(db, "armazens"));
                armazensSnap.docs.forEach(doc => {
                    const data = doc.data();

                    // Debug apenas se necessário
                    if (armazensCache.size < 3) {
                        console.log('🔍 DEBUG Armazém:', {
                            id: doc.id,
                            nome: data.nome,
                            descricao: data.descricao,
                            codigo: data.codigo
                        });
                    }

                    // Tentar múltiplos campos para nome do armazém
                    const nomeArmazem = data.nome || data.descricao || data.codigo || doc.id;

                    armazensCache.set(doc.id, {
                        nome: nomeArmazem,
                        codigo: data.codigo || doc.id,
                        descricao: data.descricao || data.nome || doc.id
                    });
                });
                console.log(`✅ ${armazensCache.size} armazéns carregados no cache`);

                // 🔍 DEBUG: Mostrar todos os armazéns carregados
                console.log('📋 Armazéns no cache:', Array.from(armazensCache.entries()));

            } catch (error) {
                console.error('❌ Erro ao carregar armazéns:', error);
            }
        }

        function getProdutoInfo(produtoId, codigo, descricao) {
            // Primeiro, tentar usar dados já disponíveis
            if (codigo && descricao) {
                return `${codigo} - ${descricao}`;
            }

            // Tentar buscar no cache
            if (produtoId && produtosCache.has(produtoId)) {
                const produto = produtosCache.get(produtoId);
                return `${produto.codigo} - ${produto.descricao}`;
            }

            // Fallback
            return codigo || descricao || produtoId || 'N/A';
        }

        function getArmazemInfo(armazemId, armazemNome) {
            // Primeiro, usar nome se disponível
            if (armazemNome && armazemNome !== 'N/A') {
                return armazemNome;
            }

            // Tentar buscar no cache
            if (armazemId && armazensCache.has(armazemId)) {
                const armazem = armazensCache.get(armazemId);
                return armazem.nome;
            }

            // Log apenas quando não encontrar
            if (armazemId && armazemId !== 'N/A') {
                console.warn('⚠️ Armazém não encontrado no cache:', armazemId);
            }

            // Fallback
            return armazemId || 'N/A';
        }

        function updateStatus(message) {
            document.getElementById('reportStatus').textContent = message;
        }

        // Definir datas padrão (últimos 30 dias)
        function setDefaultDates() {
            const hoje = new Date();
            const trintaDiasAtras = new Date();
            trintaDiasAtras.setDate(hoje.getDate() - 30);

            // CORREÇÃO: Para incluir movimentos de hoje, definir data fim como amanhã
            const amanha = new Date();
            amanha.setDate(hoje.getDate() + 1);

            document.getElementById('dataFim').value = amanha.toISOString().split('T')[0];
            document.getElementById('dataInicio').value = trintaDiasAtras.toISOString().split('T')[0];

            console.log('📅 Datas padrão definidas:');
            console.log('  Início:', trintaDiasAtras.toLocaleDateString('pt-BR'));
            console.log('  Fim:', amanha.toLocaleDateString('pt-BR'), '(inclui movimentos de hoje)');
        }

        // Filtros rápidos de data
        window.filtrarHoje = function() {
            const hoje = new Date();
            const amanha = new Date();
            amanha.setDate(hoje.getDate() + 1);

            document.getElementById('dataInicio').value = hoje.toISOString().split('T')[0];
            document.getElementById('dataFim').value = amanha.toISOString().split('T')[0];

            console.log('📅 Filtro aplicado: Apenas hoje');

            // Aplicar filtros automaticamente se já há dados carregados
            if (allMovements.length > 0) {
                aplicarFiltros();
                atualizarResumo();
                renderizarTabela();
            }
        };

        window.filtrarUltimos7Dias = function() {
            const hoje = new Date();
            const seteDiasAtras = new Date();
            seteDiasAtras.setDate(hoje.getDate() - 7);
            const amanha = new Date();
            amanha.setDate(hoje.getDate() + 1);

            document.getElementById('dataInicio').value = seteDiasAtras.toISOString().split('T')[0];
            document.getElementById('dataFim').value = amanha.toISOString().split('T')[0];

            console.log('📅 Filtro aplicado: Últimos 7 dias');

            // Aplicar filtros automaticamente se já há dados carregados
            if (allMovements.length > 0) {
                aplicarFiltros();
                atualizarResumo();
                renderizarTabela();
            }
        };

        // Função para recarregar caches
        window.recarregarCaches = async function() {
            updateStatus('Recarregando dados de produtos e armazéns...');

            try {
                // Limpar caches existentes
                produtosCache.clear();
                armazensCache.clear();

                // Recarregar caches
                await Promise.all([
                    loadProdutosCache(),
                    loadArmazensCache()
                ]);

                updateStatus('Dados atualizados com sucesso!');

                // Se há movimentações carregadas, re-renderizar
                if (allMovements.length > 0) {
                    // Reprocessar movimentações com novos dados
                    allMovements.forEach(movement => {
                        if (movement.origem === 'estoque') {
                            // Atualizar informações de produto e armazém
                            movement.produto = getProdutoInfo(movement.produtoId, movement.codigo, movement.descricao);
                            movement.armazem = getArmazemInfo(movement.armazemId, movement.armazemNome);
                        }
                    });

                    aplicarFiltros();
                    atualizarResumo();
                    renderizarTabela();
                }

            } catch (error) {
                console.error('❌ Erro ao recarregar caches:', error);
                updateStatus('Erro ao atualizar dados');
                alert('Erro ao atualizar dados: ' + error.message);
            }
        };

        // Função de diagnóstico
        window.diagnosticarDados = async function() {
            console.log('🔍 INICIANDO DIAGNÓSTICO DE DADOS...');

            try {
                // 1. Verificar armazéns
                console.log('\n📋 1. DIAGNÓSTICO DE ARMAZÉNS:');
                const armazensSnap = await getDocs(collection(db, "armazens"));
                console.log(`Total de armazéns no banco: ${armazensSnap.docs.length}`);

                armazensSnap.docs.forEach((doc, index) => {
                    const data = doc.data();
                    console.log(`Armazém ${index + 1}:`, {
                        id: doc.id,
                        campos: Object.keys(data),
                        nome: data.nome,
                        descricao: data.descricao,
                        codigo: data.codigo,
                        data: data
                    });
                });

                // 2. Verificar movimentações recentes
                console.log('\n📦 2. DIAGNÓSTICO DE MOVIMENTAÇÕES:');
                const movSnap = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), orderBy("dataMovimentacao", "desc"), limit(5))
                );
                console.log(`Últimas 5 movimentações:`);

                movSnap.docs.forEach((doc, index) => {
                    const data = doc.data();
                    console.log(`Movimentação ${index + 1}:`, {
                        id: doc.id,
                        tipo: data.tipo,
                        produto: data.codigo || data.produtoId,
                        armazemId: data.armazemId,
                        armazemDestinoId: data.armazemDestinoId,
                        armazemNome: data.armazemNome,
                        armazem: data.armazem,
                        campos_armazem: {
                            armazemId: data.armazemId,
                            armazemDestinoId: data.armazemDestinoId,
                            armazemOrigemId: data.armazemOrigemId,
                            armazemNome: data.armazemNome,
                            armazem: data.armazem
                        }
                    });
                });

                // 3. Verificar cache atual
                console.log('\n💾 3. DIAGNÓSTICO DE CACHE:');
                console.log(`Produtos no cache: ${produtosCache.size}`);
                console.log(`Armazéns no cache: ${armazensCache.size}`);
                console.log('Cache de armazéns:', Array.from(armazensCache.entries()));

                // 4. Testar função de busca
                console.log('\n🧪 4. TESTE DE FUNÇÕES:');
                if (allMovements.length > 0) {
                    const primeiraMovimentacao = allMovements[0];
                    console.log('Testando primeira movimentação:', primeiraMovimentacao);

                    if (primeiraMovimentacao.armazemId || primeiraMovimentacao.armazemDestinoId) {
                        const armazemId = primeiraMovimentacao.armazemId || primeiraMovimentacao.armazemDestinoId;
                        const resultado = getArmazemInfo(armazemId, primeiraMovimentacao.armazemNome);
                        console.log(`Resultado da busca para armazém ${armazemId}:`, resultado);
                    }
                }

                alert('Diagnóstico concluído! Verifique o console (F12) para ver os resultados detalhados.');

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                alert('Erro no diagnóstico: ' + error.message);
            }
        };

        // Função principal para carregar movimentações
        window.carregarMovimentacoes = async function() {
            updateStatus('Carregando movimentações...');
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('summaryCards').style.display = 'none';
            document.getElementById('movementsTable').style.display = 'none';

            try {
                allMovements = [];

                // CORREÇÃO: Carregar movimentações de estoque
                updateStatus('Carregando movimentações de estoque...');
                const movEstoqueSnap = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), orderBy("dataMovimentacao", "desc"), limit(1000))
                );

                console.log(`📦 Movimentações de estoque carregadas: ${movEstoqueSnap.docs.length}`);

                movEstoqueSnap.docs.forEach(doc => {
                    const data = doc.data();

                    // Debug apenas para primeiras 3 movimentações
                    if (allMovements.length < 3) {
                        console.log('🔍 DEBUG Movimentação:', {
                            id: doc.id,
                            tipo: data.tipo,
                            produto: data.codigo,
                            campos: Object.keys(data),
                            armazem_campos: {
                                armazemId: data.armazemId,
                                armazemDestinoId: data.armazemDestinoId,
                                armazemOrigemId: data.armazemOrigemId,
                                armazemNome: data.armazemNome,
                                armazem: data.armazem
                            },
                            resultado_busca: getArmazemInfo(
                                data.armazemId || data.armazemDestinoId || data.armazemOrigemId,
                                data.armazemNome || data.armazem
                            )
                        });
                    }

                    // Determinar produto usando função de cache
                    let produtoInfo = getProdutoInfo(data.produtoId, data.codigo, data.descricao);

                    // Determinar documento com múltiplas fontes
                    let documentoInfo = 'N/A';
                    if (data.numeroNotaFiscal) {
                        documentoInfo = `NF: ${data.numeroNotaFiscal}`;
                    } else if (data.numeroDocumento) {
                        documentoInfo = data.numeroDocumento;
                    } else if (data.documento) {
                        documentoInfo = data.documento;
                    } else if (data.pedidoCompraId) {
                        documentoInfo = `PC: ${data.pedidoCompraId}`;
                    }

                    // Determinar armazém usando função de cache
                    let armazemInfo = getArmazemInfo(
                        data.armazemId || data.armazemDestinoId || data.armazemOrigemId,
                        data.armazemNome || data.armazem
                    );

                // Debug apenas se armazém não foi encontrado
                if (armazemInfo === 'N/A' && (data.armazemId || data.armazemDestinoId)) {
                    console.warn('⚠️ Armazém não encontrado para movimentação:', {
                        movimento_id: doc.id,
                        armazemId: data.armazemId || data.armazemDestinoId,
                        tipo: data.tipo
                    });
                }

                    // Determinar usuário
                    let usuarioInfo = 'Sistema';
                    if (data.usuarioMovimentacao) {
                        usuarioInfo = data.usuarioMovimentacao;
                    } else if (data.usuario) {
                        usuarioInfo = data.usuario;
                    } else if (data.criadoPor) {
                        usuarioInfo = data.criadoPor;
                    }

                    allMovements.push({
                        id: doc.id,
                        tipo: getTipoMovimentacao(data.tipo),
                        dataHora: data.dataMovimentacao,
                        documento: documentoInfo,
                        produto: produtoInfo,
                        quantidade: data.quantidade || 0,
                        armazem: armazemInfo,
                        usuario: usuarioInfo,
                        status: 'FINALIZADO',
                        observacoes: data.observacoes || data.motivo || '',
                        valor: data.valorTotal || (data.valorUnitario ? (data.quantidade * data.valorUnitario) : 0),
                        origem: 'estoque',
                        // Campos adicionais para debug
                        tes: data.tes || '',
                        tipoDocumento: data.tipoDocumento || '',
                        recebimentoId: data.recebimentoId || '',
                        pedidoCompraId: data.pedidoCompraId || ''
                    });
                });

                // CORREÇÃO: Carregar recebimentos de materiais
                updateStatus('Carregando recebimentos de materiais...');
                const recebimentosMateriaisSnap = await getDocs(
                    query(collection(db, "recebimentosMateriais"), orderBy("dataRecebimento", "desc"), limit(1000))
                );

                console.log(`📥 Recebimentos de materiais carregados: ${recebimentosMateriaisSnap.docs.length}`);

                recebimentosMateriaisSnap.docs.forEach(doc => {
                    const data = doc.data();

                    // Debug apenas para primeiros 3 recebimentos
                    if (recebimentosMateriaisSnap.docs.indexOf(doc) < 3) {
                        console.log('🔍 DEBUG Recebimento:', {
                            id: doc.id,
                            numeroNF: data.numeroNotaFiscal || data.numeroNF,
                            itens: data.itens?.length || 0,
                            campos: Object.keys(data)
                        });
                    }

                    // Se o recebimento tem itens, criar uma movimentação para cada item
                    if (data.itens && Array.isArray(data.itens)) {
                        data.itens.forEach(item => {
                            // Determinar produto usando função de cache
                            let produtoInfo = getProdutoInfo(item.produtoId, item.codigo, item.descricao);

                            // Determinar armazém
                            let armazemInfo = getArmazemInfo(
                                data.armazemDestinoId || data.armazemId,
                                data.armazemDestinoNome || data.armazemNome
                            );

                            allMovements.push({
                                id: `${doc.id}_${item.codigo || item.produtoId}`,
                                tipo: 'ENTRADA',
                                dataHora: data.dataRecebimento,
                                documento: `NF: ${data.numeroNotaFiscal || data.numeroNF || 'S/N'}`,
                                produto: produtoInfo.codigo,
                                descricao: produtoInfo.descricao,
                                quantidade: parseFloat(item.quantidadeRecebida || item.quantidade || 0),
                                valorUnitario: parseFloat(item.precoUnitario || item.valorUnitario || 0),
                                valorTotal: parseFloat(item.valorTotal || 0),
                                armazem: armazemInfo.nome,
                                usuario: data.usuarioRecebimento || data.recebidoPor || 'Sistema',
                                observacoes: data.observacoes || `Recebimento NF: ${data.numeroNotaFiscal || 'S/N'}`,
                                origem: 'recebimento',
                                // Campos adicionais
                                tes: data.tes || '',
                                pedidoCompraId: data.pedidoCompraId || '',
                                recebimentoId: doc.id
                            });
                        });
                    } else {
                        // Se não tem itens detalhados, criar uma entrada genérica
                        let armazemInfo = getArmazemInfo(
                            data.armazemDestinoId || data.armazemId,
                            data.armazemDestinoNome || data.armazemNome
                        );

                        allMovements.push({
                            id: doc.id,
                            tipo: 'ENTRADA',
                            dataHora: data.dataRecebimento,
                            documento: `NF: ${data.numeroNotaFiscal || data.numeroNF || 'S/N'}`,
                            produto: 'Recebimento',
                            descricao: 'Recebimento de materiais',
                            quantidade: 1,
                            valorUnitario: parseFloat(data.valorTotal || 0),
                            valorTotal: parseFloat(data.valorTotal || 0),
                            armazem: armazemInfo.nome,
                            usuario: data.usuarioRecebimento || data.recebidoPor || 'Sistema',
                            observacoes: data.observacoes || `Recebimento NF: ${data.numeroNotaFiscal || 'S/N'}`,
                            origem: 'recebimento',
                            tes: data.tes || '',
                            pedidoCompraId: data.pedidoCompraId || '',
                            recebimentoId: doc.id
                        });
                    }
                });

                // Carregar solicitações de compra
                updateStatus('Carregando solicitações de compra...');
                const solicitacoesSnap = await getDocs(
                    query(collection(db, "solicitacoesCompra"), orderBy("dataCriacao", "desc"), limit(500))
                );

                solicitacoesSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'solicitacao',
                        dataHora: data.dataCriacao,
                        documento: `SC-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: '📋 Solicitação',
                        usuario: data.solicitante || data.criadoPor || 'Sistema',
                        status: data.status || 'PENDENTE',
                        observacoes: data.justificativa || data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'solicitacao'
                    });
                });

                // Carregar cotações
                updateStatus('Carregando cotações...');
                const cotacoesSnap = await getDocs(
                    query(collection(db, "cotacoes"), orderBy("dataCriacao", "desc"), limit(500))
                );

                cotacoesSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'cotacao',
                        dataHora: data.dataCriacao,
                        documento: `CT-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: '💰 Cotação',
                        usuario: data.criadoPor || 'Sistema',
                        status: data.status || 'ABERTA',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'cotacao'
                    });
                });

                // Carregar pedidos de compra
                updateStatus('Carregando pedidos de compra...');
                const pedidosSnap = await getDocs(
                    query(collection(db, "pedidosCompra"), orderBy("dataCriacao", "desc"), limit(500))
                );

                pedidosSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'pedido',
                        dataHora: data.dataCriacao,
                        documento: `PC-${data.numero || doc.id.substring(0, 8)}`,
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidade || 0), 0) : 0,
                        armazem: '🛒 Pedido',
                        usuario: data.criadoPor || 'Sistema',
                        status: data.status || 'ABERTO',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'pedido'
                    });
                });

                // Carregar recebimentos
                updateStatus('Carregando recebimentos...');
                const recebimentosSnap = await getDocs(
                    query(collection(db, "recebimentos"), orderBy("dataRecebimento", "desc"), limit(500))
                );

                recebimentosSnap.docs.forEach(doc => {
                    const data = doc.data();
                    allMovements.push({
                        id: doc.id,
                        tipo: 'recebimento',
                        dataHora: data.dataRecebimento,
                        documento: data.numeroNota || data.documento || 'N/A',
                        produto: data.itens ? `${data.itens.length} itens` : 'N/A',
                        quantidade: data.itens ? data.itens.reduce((sum, item) => sum + (item.quantidadeRecebida || 0), 0) : 0,
                        armazem: getArmazemInfo(data.armazemDestinoId, data.armazemDestino) || '📦 Recebimento',
                        usuario: data.recebidoPor || 'Sistema',
                        status: data.status || 'RECEBIDO',
                        observacoes: data.observacoes || '',
                        valor: data.valorTotal || 0,
                        origem: 'recebimento'
                    });
                });

                // Ordenar por data (mais recente primeiro)
                allMovements.sort((a, b) => {
                    const dateA = a.dataHora?.seconds ? new Date(a.dataHora.seconds * 1000) : new Date(a.dataHora);
                    const dateB = b.dataHora?.seconds ? new Date(b.dataHora.seconds * 1000) : new Date(b.dataHora);
                    return dateB - dateA;
                });

                filteredMovements = [...allMovements];

                // Aplicar filtros se houver
                aplicarFiltros();

                // Mostrar resultados
                atualizarResumo();
                renderizarTabela();

                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('summaryCards').style.display = 'grid';
                document.getElementById('movementsTable').style.display = 'block';
                document.getElementById('btnExport').disabled = false;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('pt-BR');

                updateStatus(`${allMovements.length} movimentações carregadas`);

            } catch (error) {
                console.error('Erro ao carregar movimentações:', error);
                updateStatus('Erro ao carregar movimentações');
                alert('Erro ao carregar movimentações: ' + error.message);
            }
        };

        function getTipoMovimentacao(tipo) {
            const tipos = {
                'entrada': 'entrada',
                'saida': 'saida',
                'transferencia': 'transferencia',
                'ajuste': 'ajuste',
                'producao': 'producao',
                'consumo': 'saida',
                'devolucao': 'entrada',
                'inventario': 'ajuste'
            };
            return tipos[tipo] || 'ajuste';
        }

        // Aplicar filtros
        function aplicarFiltros() {
            const dataInicio = document.getElementById('dataInicio').value;
            const dataFim = document.getElementById('dataFim').value;
            const tipoFilter = document.getElementById('tipoFilter').value;
            const usuarioFilter = document.getElementById('usuarioFilter').value;
            const armazemFilter = document.getElementById('armazemFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredMovements = allMovements.filter(movement => {
                // Filtro de data - CORRIGIDO
                if (dataInicio || dataFim) {
                    const movDate = movement.dataHora?.seconds ?
                        new Date(movement.dataHora.seconds * 1000) :
                        new Date(movement.dataHora);

                    // Normalizar data da movimentação (apenas data, sem hora)
                    const movDateOnly = new Date(movDate.getFullYear(), movDate.getMonth(), movDate.getDate());

                    if (dataInicio) {
                        const startDate = new Date(dataInicio);
                        const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
                        if (movDateOnly < startDateOnly) return false;
                    }

                    if (dataFim) {
                        const endDate = new Date(dataFim);
                        const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
                        // CORREÇÃO: Usar <= para incluir o dia selecionado
                        if (movDateOnly > endDateOnly) return false;
                    }
                }

                // Filtro de tipo
                if (tipoFilter && movement.tipo !== tipoFilter) return false;

                // Filtro de usuário
                if (usuarioFilter && !(movement.usuario || '').toLowerCase().includes(usuarioFilter.toLowerCase())) return false;

                // Filtro de armazém
                if (armazemFilter && !movement.armazem.includes(armazemFilter)) return false;

                // Filtro de busca
                if (searchTerm) {
                    const searchFields = [
                        movement.documento,
                        movement.produto,
                        movement.observacoes,
                        movement.usuario
                    ].join(' ').toLowerCase();

                    if (!searchFields.includes(searchTerm)) return false;
                }

                return true;
            });
        }

        // Atualizar resumo
        function atualizarResumo() {
            const totalMovimentos = filteredMovements.length;

            // Movimentos de hoje
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);
            const movimentosHoje = filteredMovements.filter(mov => {
                const movDate = mov.dataHora?.seconds ?
                    new Date(mov.dataHora.seconds * 1000) :
                    new Date(mov.dataHora);
                movDate.setHours(0, 0, 0, 0);
                return movDate.getTime() === hoje.getTime();
            }).length;

            // Usuários únicos
            const usuariosUnicos = new Set(filteredMovements.map(mov => mov.usuario)).size;

            // Valor total movimentado
            const valorTotal = filteredMovements.reduce((sum, mov) => sum + (mov.valor || 0), 0);

            document.getElementById('totalMovimentos').textContent = totalMovimentos.toLocaleString();
            document.getElementById('movimentosHoje').textContent = movimentosHoje.toLocaleString();
            document.getElementById('usuariosAtivos').textContent = usuariosUnicos.toLocaleString();
            document.getElementById('valorMovimentado').textContent = 'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
        }

        // Renderizar tabela
        function renderizarTabela() {
            const tbody = document.getElementById('movementsTableBody');
            tbody.innerHTML = '';

            filteredMovements.slice(0, 500).forEach((movement, index) => {
                const row = document.createElement('tr');

                // Debug apenas se armazém for N/A
                if (movement.armazem === 'N/A') {
                    console.warn('⚠️ Linha da tabela com armazém N/A:', {
                        index,
                        movimento_id: movement.id,
                        tipo: movement.tipo,
                        origem: movement.origem
                    });
                }

                const dataHora = movement.dataHora?.seconds ?
                    new Date(movement.dataHora.seconds * 1000) :
                    new Date(movement.dataHora);

                // Criar botões de ação para recebimentos
                let actionsHtml = '';
                if (movement.tipo === 'recebimento' && movement.status !== 'CANCELADO') {
                    actionsHtml = `
                        <div class="action-buttons">
                            <button class="btn-action btn-delete"
                                    onclick="excluirRecebimento('${movement.id}', '${movement.documento}')"
                                    title="Excluir recebimento e reverter todas as movimentações relacionadas">
                                🗑️ Excluir
                            </button>
                        </div>
                    `;
                } else {
                    actionsHtml = '<div class="text-center"><span style="color: #6c757d;">-</span></div>';
                }

                // Melhorar exibição do documento com mais informações
                let documentoDisplay = movement.documento;
                if (movement.pedidoCompraId && movement.documento !== movement.pedidoCompraId) {
                    documentoDisplay += `<br><small style="color: #6c757d;">PC: ${movement.pedidoCompraId}</small>`;
                }
                if (movement.recebimentoId) {
                    documentoDisplay += `<br><small style="color: #28a745;">REC: ${movement.recebimentoId.substring(0, 8)}...</small>`;
                }

                // Melhorar exibição das observações
                let observacoesDisplay = movement.observacoes || '-';
                if (movement.tes) {
                    observacoesDisplay += `<br><small style="color: #007bff;">TES: ${movement.tes}</small>`;
                }

                // Melhorar exibição da quantidade com valor
                let quantidadeDisplay = movement.quantidade.toLocaleString();
                if (movement.valor && movement.valor > 0) {
                    quantidadeDisplay += `<br><small style="color: #28a745;">R$ ${movement.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</small>`;
                }

                row.innerHTML = `
                    <td class="font-weight-bold">${dataHora.toLocaleString('pt-BR')}</td>
                    <td><span class="movement-type type-${movement.tipo || 'entrada'}">${getTipoText(movement.tipo || '')}</span></td>
                    <td>${documentoDisplay}</td>
                    <td>${movement.produto || 'N/A'}</td>
                    <td class="text-right font-weight-bold">${quantidadeDisplay}</td>
                    <td>${movement.armazem || 'N/A'}</td>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">${(movement.usuario || 'N').charAt(0).toUpperCase()}</div>
                            <span>${movement.usuario || 'N/A'}</span>
                        </div>
                    </td>
                    <td><span class="status-badge status-${(movement.status || 'pendente').toLowerCase()}">${movement.status || 'Pendente'}</span></td>
                    <td>${observacoesDisplay}</td>
                    <td>${actionsHtml}</td>
                `;

                tbody.appendChild(row);
            });

            document.getElementById('itemCount').textContent = `${filteredMovements.length} movimentos`;
        }

        function getTipoText(tipo) {
            if (!tipo) return 'N/A';

            const tipos = {
                'entrada': 'Entrada',
                'saida': 'Saída',
                'transferencia': 'Transferência',
                'ajuste': 'Ajuste',
                'producao': 'Produção',
                'solicitacao': 'Solicitação',
                'cotacao': 'Cotação',
                'pedido': 'Pedido',
                'recebimento': 'Recebimento'
            };
            return tipos[tipo.toLowerCase()] || tipo;
        }

        // Filtrar em tempo real
        ['dataInicio', 'dataFim', 'tipoFilter', 'usuarioFilter', 'armazemFilter', 'searchInput'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                if (allMovements.length > 0) {
                    aplicarFiltros();
                    atualizarResumo();
                    renderizarTabela();
                }
            });
        });

        document.getElementById('searchInput').addEventListener('keyup', () => {
            if (allMovements.length > 0) {
                aplicarFiltros();
                atualizarResumo();
                renderizarTabela();
            }
        });

        // Exportar CSV
        window.exportarCSV = function() {
            if (filteredMovements.length === 0) {
                alert('Nenhuma movimentação para exportar!');
                return;
            }

            let csv = 'Data/Hora,Tipo,Documento,Produto/Item,Quantidade,Armazém,Usuário,Status,Observações,Valor\n';

            filteredMovements.forEach(movement => {
                const dataHora = movement.dataHora?.seconds ?
                    new Date(movement.dataHora.seconds * 1000) :
                    new Date(movement.dataHora);

                csv += `"${dataHora.toLocaleString('pt-BR')}","${getTipoText(movement.tipo)}","${movement.documento}","${movement.produto}",${movement.quantidade},"${movement.armazem}","${movement.usuario}","${movement.status}","${movement.observacoes || ''}","R$ ${(movement.valor || 0).toFixed(2)}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `movimentacoes_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };

        // Ordenação da tabela
        let sortDirection = {};
        window.ordenarTabela = function(columnIndex) {
            const direction = sortDirection[columnIndex] === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = direction;

            const columns = ['dataHora', 'tipo', 'documento', 'produto', 'quantidade', 'armazem', 'usuario', 'status', 'observacoes'];
            const column = columns[columnIndex];

            filteredMovements.sort((a, b) => {
                let valueA = a[column];
                let valueB = b[column];

                if (column === 'dataHora') {
                    valueA = valueA?.seconds ? new Date(valueA.seconds * 1000) : new Date(valueA);
                    valueB = valueB?.seconds ? new Date(valueB.seconds * 1000) : new Date(valueB);
                } else if (typeof valueA === 'string') {
                    valueA = (valueA || '').toLowerCase();
                    valueB = (valueB || '').toLowerCase();
                }

                if (direction === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });

            renderizarTabela();
        };

        // Atualização automática
        window.atualizarAutomatico = function() {
            // CORREÇÃO: Verificar se o elemento existe antes de tentar acessá-lo
            const btn = document.getElementById('btnAutoUpdate');

            if (!btn) {
                console.warn('⚠️ Elemento btnAutoUpdate não encontrado, procurando botões alternativos...');

                // Procurar por botões que chamam atualizarAutomatico()
                const autoUpdateButtons = document.querySelectorAll('button[onclick*="atualizarAutomatico"]');

                if (autoUpdateButtons.length === 0) {
                    console.error('❌ Nenhum botão de auto-update encontrado');
                    showNotification('Erro: Botão de auto-update não encontrado', 'error');
                    return;
                }

                // Usar o primeiro botão encontrado
                const alternativeBtn = autoUpdateButtons[0];

                if (autoUpdateInterval) {
                    clearInterval(autoUpdateInterval);
                    autoUpdateInterval = null;
                    alternativeBtn.innerHTML = '🔄 Atualização Automática';
                    alternativeBtn.classList.remove('btn-success');
                    alternativeBtn.classList.add('btn-info');
                    showNotification('⏸️ Atualização automática pausada', 'info');
                } else {
                    autoUpdateInterval = setInterval(() => {
                        console.log('🔄 Atualizando dados automaticamente...');
                        carregarMovimentacoes();
                    }, 30000); // Atualizar a cada 30 segundos

                    alternativeBtn.innerHTML = '⏹️ Parar Atualização';
                    alternativeBtn.classList.remove('btn-info');
                    alternativeBtn.classList.add('btn-success');
                    showNotification('✅ Atualização automática ativada (30s)', 'success');
                }
                return;
            }

            // Código original se o botão existir
            if (autoUpdateInterval) {
                clearInterval(autoUpdateInterval);
                autoUpdateInterval = null;
                btn.textContent = '🔄 Atualização Automática';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-info');
                showNotification('⏸️ Atualização automática pausada', 'info');
            } else {
                autoUpdateInterval = setInterval(() => {
                    console.log('🔄 Atualizando dados automaticamente...');
                    carregarMovimentacoes();
                }, 30000); // Atualizar a cada 30 segundos

                btn.textContent = '⏹️ Parar Atualização';
                btn.classList.remove('btn-info');
                btn.classList.add('btn-success');
                showNotification('✅ Atualização automática ativada (30s)', 'success');
            }
        };

        // ===================================================================
        // FUNÇÃO DE DIAGNÓSTICO PARA INVESTIGAR PROBLEMAS
        // ===================================================================

        window.diagnosticarDados = async function() {
            console.log('🔍 INICIANDO DIAGNÓSTICO COMPLETO...');

            try {
                // 1. Verificar movimentações gerais
                console.log('📊 1. Verificando movimentações gerais...');
                const todasMovimentacoes = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), limit(10))
                );
                console.log(`Total de movimentações encontradas (amostra): ${todasMovimentacoes.docs.length}`);

                // 2. Verificar movimentações com NF específica
                console.log('📋 2. Verificando movimentações com numeroNotaFiscal...');
                const movimentacoesComNF = await getDocs(
                    query(
                        collection(db, "movimentacoesEstoque"),
                        where("numeroNotaFiscal", "!=", ""),
                        limit(5)
                    )
                );
                console.log(`Movimentações com NF encontradas: ${movimentacoesComNF.docs.length}`);

                movimentacoesComNF.docs.forEach((doc, index) => {
                    const data = doc.data();
                    console.log(`NF ${index + 1}:`, {
                        id: doc.id,
                        numeroNotaFiscal: data.numeroNotaFiscal,
                        numeroDocumento: data.numeroDocumento,
                        documento: data.documento,
                        tipo: data.tipo,
                        codigo: data.codigo,
                        dataMovimentacao: data.dataMovimentacao?.toDate?.() || data.dataMovimentacao
                    });
                });

                // 3. Verificar movimentações recentes
                console.log('📅 3. Verificando movimentações dos últimos 7 dias...');
                const seteDiasAtras = new Date();
                seteDiasAtras.setDate(seteDiasAtras.getDate() - 7);

                const movimentacoesRecentes = await getDocs(
                    query(
                        collection(db, "movimentacoesEstoque"),
                        where("dataMovimentacao", ">=", Timestamp.fromDate(seteDiasAtras)),
                        orderBy("dataMovimentacao", "desc"),
                        limit(10)
                    )
                );

                console.log(`Movimentações dos últimos 7 dias: ${movimentacoesRecentes.docs.length}`);

                movimentacoesRecentes.docs.forEach((doc, index) => {
                    const data = doc.data();
                    console.log(`Recente ${index + 1}:`, {
                        id: doc.id,
                        tipo: data.tipo,
                        codigo: data.codigo,
                        quantidade: data.quantidade,
                        numeroNotaFiscal: data.numeroNotaFiscal,
                        numeroDocumento: data.numeroDocumento,
                        documento: data.documento,
                        dataMovimentacao: data.dataMovimentacao?.toDate?.() || data.dataMovimentacao
                    });
                });

                // 4. Buscar especificamente por "9373"
                console.log('🔍 4. Buscando especificamente por "9373"...');

                // Buscar em diferentes campos
                const campos = ['numeroNotaFiscal', 'numeroDocumento', 'documento'];

                for (const campo of campos) {
                    try {
                        const resultado = await getDocs(
                            query(
                                collection(db, "movimentacoesEstoque"),
                                where(campo, "==", "9373")
                            )
                        );
                        console.log(`Busca por ${campo} = "9373": ${resultado.docs.length} resultados`);

                        resultado.docs.forEach((doc, index) => {
                            const data = doc.data();
                            console.log(`${campo} ${index + 1}:`, {
                                id: doc.id,
                                [campo]: data[campo],
                                tipo: data.tipo,
                                codigo: data.codigo,
                                quantidade: data.quantidade
                            });
                        });
                    } catch (error) {
                        console.log(`Erro ao buscar por ${campo}:`, error.message);
                    }
                }

                // 5. Buscar por texto contendo "9373"
                console.log('📝 5. Verificando campos de texto que contenham "9373"...');
                const todasParaBusca = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), limit(100))
                );

                let encontradas = 0;
                todasParaBusca.docs.forEach(doc => {
                    const data = doc.data();
                    const textoCompleto = JSON.stringify(data).toLowerCase();
                    if (textoCompleto.includes('9373')) {
                        encontradas++;
                        console.log('Encontrada movimentação com "9373":', {
                            id: doc.id,
                            data: data
                        });
                    }
                });

                console.log(`Total de movimentações contendo "9373": ${encontradas}`);

                console.log('✅ DIAGNÓSTICO COMPLETO FINALIZADO');
                // showNotification('Diagnóstico completo - veja o console', 'info');

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                // showNotification('Erro no diagnóstico: ' + error.message, 'error');
            }
        };

        // Função para buscar movimentação por qualquer campo
        window.buscarMovimentacao = async function(termo) {
            console.log(`🔍 Buscando movimentações com termo: "${termo}"`);

            try {
                const resultado = await getDocs(
                    query(collection(db, "movimentacoesEstoque"), limit(200))
                );

                const encontradas = [];
                resultado.docs.forEach(doc => {
                    const data = doc.data();
                    const textoCompleto = JSON.stringify(data).toLowerCase();
                    if (textoCompleto.includes(termo.toLowerCase())) {
                        encontradas.push({
                            id: doc.id,
                            data: data
                        });
                    }
                });

                console.log(`Encontradas ${encontradas.length} movimentações com "${termo}"`);
                encontradas.forEach((item, index) => {
                    console.log(`Resultado ${index + 1}:`, item);
                });

                return encontradas;

            } catch (error) {
                console.error('❌ Erro na busca:', error);
                return [];
            }
        };

        // ===== EXCLUSÃO DE RECEBIMENTOS =====
        window.excluirRecebimento = async function(recebimentoId, numeroNF) {
            try {
                // Primeiro, buscar detalhes do recebimento
                updateStatus('Carregando detalhes do recebimento...');
                const recebimentoDoc = await getDoc(doc(db, "recebimentos", recebimentoId));
                if (!recebimentoDoc.exists()) {
                    alert('❌ Recebimento não encontrado!');
                    return;
                }

                const recebimentoData = recebimentoDoc.data();
                const dataRecebimento = recebimentoData.dataRecebimento?.toDate?.() || new Date(recebimentoData.dataRecebimento);
                const valorTotal = recebimentoData.valorTotalNF || recebimentoData.valorTotal || 0;
                const qtdItens = recebimentoData.itens?.length || 0;

                // Mostrar detalhes e confirmar exclusão
                const confirmacao = confirm(`🗑️ EXCLUSÃO DE RECEBIMENTO\n\n📄 Nota Fiscal: ${numeroNF}\n📅 Data: ${dataRecebimento.toLocaleDateString('pt-BR')}\n💰 Valor: R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}\n📦 Itens: ${qtdItens}\n👤 Usuário: ${recebimentoData.usuarioRecebimento || recebimentoData.recebidoPor || 'N/A'}\n\n⚠️ ATENÇÃO: Esta ação irá:\n✅ Excluir o registro de recebimento\n✅ Reverter todas as movimentações de estoque\n✅ Restaurar o status do pedido de compra\n✅ Permitir novo lançamento\n\n❌ ESTA AÇÃO NÃO PODE SER DESFEITA!\n\nDeseja continuar?`);

                if (!confirmacao) {
                    updateStatus('Exclusão cancelada pelo usuário');
                    return;
                }

                // Confirmação adicional para segurança
                const confirmacaoFinal = confirm(`🚨 CONFIRMAÇÃO FINAL\n\nTem certeza absoluta que deseja excluir o recebimento da NF ${numeroNF}?\n\nDigite "CONFIRMAR" na próxima tela para prosseguir.`);

                if (!confirmacaoFinal) {
                    updateStatus('Exclusão cancelada pelo usuário');
                    return;
                }

                const textoConfirmacao = prompt('Digite "CONFIRMAR" (em maiúsculas) para prosseguir com a exclusão:');
                if (textoConfirmacao !== 'CONFIRMAR') {
                    alert('❌ Texto de confirmação incorreto. Exclusão cancelada.');
                    updateStatus('Exclusão cancelada - confirmação incorreta');
                    return;
                }

            } catch (error) {
                console.error('❌ Erro ao carregar detalhes:', error);
                alert('❌ Erro ao carregar detalhes do recebimento: ' + error.message);
                return;
            }

            try {
                updateStatus('Excluindo recebimento...');
                console.log('🗑️ Iniciando exclusão do recebimento:', recebimentoId);

                // 1. Buscar dados do recebimento novamente (já foi buscado antes)
                const recebimentoDoc = await getDoc(doc(db, "recebimentos", recebimentoId));
                if (!recebimentoDoc.exists()) {
                    throw new Error('Recebimento não encontrado!');
                }

                const recebimentoData = recebimentoDoc.data();
                console.log('📋 Dados do recebimento para exclusão:');
                console.log('  📄 NF:', recebimentoData.numeroNotaFiscal || recebimentoData.numeroNota);
                console.log('  💰 Valor:', recebimentoData.valorTotalNF || recebimentoData.valorTotal);
                console.log('  📦 Itens:', recebimentoData.itens?.length || 0);
                console.log('  🏢 Pedido:', recebimentoData.pedidoCompraId);

                // 2. Buscar movimentações relacionadas
                const movimentacoesQuery = query(
                    collection(db, "movimentacoesEstoque"),
                    where("recebimentoId", "==", recebimentoId)
                );
                const movimentacoesSnap = await getDocs(movimentacoesQuery);
                console.log('📦 Movimentações encontradas:', movimentacoesSnap.docs.length);

                // 3. Reverter movimentações de estoque
                let movimentacoesRevertidas = 0;
                let estoquesAtualizados = 0;

                for (const movDoc of movimentacoesSnap.docs) {
                    const movData = movDoc.data();
                    console.log(`🔄 Revertendo movimentação ${movimentacoesRevertidas + 1}/${movimentacoesSnap.docs.length}:`, movData.codigo || movData.produtoId, 'Qtd:', movData.quantidade);

                    try {
                        // Buscar estoque atual
                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", movData.produtoId),
                            where("armazemId", "==", movData.armazemId || movData.armazemDestinoId)
                        );
                        const estoqueSnap = await getDocs(estoqueQuery);

                        if (!estoqueSnap.empty) {
                            const estoqueDoc = estoqueSnap.docs[0];
                            const estoqueData = estoqueDoc.data();
                            const saldoAtual = estoqueData.saldo || 0;
                            const qtdReverter = movData.quantidade || 0;
                            const novoSaldo = saldoAtual - qtdReverter;

                            console.log(`  📊 Estoque atual: ${saldoAtual} | Reverter: ${qtdReverter} | Novo: ${novoSaldo}`);

                            if (novoSaldo < 0) {
                                console.warn(`  ⚠️ ATENÇÃO: Saldo ficará negativo para ${movData.codigo || movData.produtoId}`);
                                console.warn(`     Saldo atual: ${saldoAtual} | Quantidade a reverter: ${qtdReverter}`);
                            }

                            // Atualizar saldo (permitir negativo para não perder dados)
                            await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                                saldo: novoSaldo,
                                ultimaMovimentacao: Timestamp.now()
                            });

                            estoquesAtualizados++;
                            console.log(`  ✅ Estoque atualizado: ${movData.codigo || movData.produtoId} → Saldo: ${novoSaldo}`);
                        } else {
                            console.warn(`  ⚠️ Estoque não encontrado para produto: ${movData.produtoId} no armazém: ${movData.armazemId || movData.armazemDestinoId}`);
                        }

                        // Excluir movimentação
                        await deleteDoc(doc(db, "movimentacoesEstoque", movDoc.id));
                        movimentacoesRevertidas++;
                        console.log(`  🗑️ Movimentação excluída: ${movDoc.id}`);

                    } catch (error) {
                        console.error(`  ❌ Erro ao reverter movimentação ${movDoc.id}:`, error);
                        throw new Error(`Erro ao reverter movimentação do produto ${movData.codigo || movData.produtoId}: ${error.message}`);
                    }
                }

                console.log(`✅ Resumo da reversão: ${movimentacoesRevertidas} movimentações excluídas, ${estoquesAtualizados} estoques atualizados`);

                // 4. Atualizar pedido de compra
                if (recebimentoData.pedidoCompraId) {
                    await reverterPedidoCompra(recebimentoData);
                }

                // 5. Excluir recebimento
                await deleteDoc(doc(db, "recebimentos", recebimentoId));
                console.log('✅ Recebimento excluído:', recebimentoId);

                // 6. Recarregar dados
                console.log('🔄 Recarregando dados do sistema...');
                await carregarMovimentacoes();
                updateStatus('Recebimento excluído com sucesso!');

                // Mensagem de sucesso detalhada
                const resumo = `✅ EXCLUSÃO CONCLUÍDA COM SUCESSO!\n\n📄 Nota Fiscal: ${numeroNF}\n💰 Valor: R$ ${(recebimentoData.valorTotalNF || recebimentoData.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2})}\n\n📋 Ações realizadas:\n✅ 1 recebimento removido\n✅ ${movimentacoesRevertidas} movimentações revertidas\n✅ ${estoquesAtualizados} estoques atualizados\n✅ Status do pedido restaurado\n\n🎯 O pedido agora pode ser relançado normalmente!`;

                alert(resumo);
                console.log('🎉 Exclusão de recebimento concluída com sucesso!');

            } catch (error) {
                console.error('❌ Erro ao excluir recebimento:', error);
                updateStatus('Erro ao excluir recebimento');
                alert('❌ Erro ao excluir recebimento:\n\n' + error.message + '\n\nVerifique o console para mais detalhes.');
            }
        };

        async function reverterPedidoCompra(recebimentoData) {
            try {
                console.log('📋 Revertendo pedido de compra:', recebimentoData.pedidoCompraId);

                // Buscar pedido atual
                const pedidoDoc = await getDoc(doc(db, "pedidosCompra", recebimentoData.pedidoCompraId));
                if (!pedidoDoc.exists()) {
                    console.warn('⚠️ Pedido de compra não encontrado:', recebimentoData.pedidoCompraId);
                    return;
                }

                const pedidoData = pedidoDoc.data();
                console.log('📋 Dados do pedido atual:', pedidoData);

                // Reverter quantidades recebidas dos itens
                if (pedidoData.itens && recebimentoData.itens) {
                    pedidoData.itens.forEach(itemPedido => {
                        const itemRecebido = recebimentoData.itens.find(ir =>
                            ir.produtoId === itemPedido.produtoId || ir.codigo === itemPedido.codigo
                        );

                        if (itemRecebido) {
                            const qtdAnterior = itemPedido.quantidadeRecebida || 0;
                            const qtdReverter = itemRecebido.quantidadeRecebida || 0;
                            itemPedido.quantidadeRecebida = Math.max(0, qtdAnterior - qtdReverter);

                            console.log(`🔄 Item ${itemPedido.codigo}: ${qtdAnterior} → ${itemPedido.quantidadeRecebida} (revertido: ${qtdReverter})`);
                        }
                    });
                }

                // Determinar novo status
                const todosRecebidos = pedidoData.itens.every(item => {
                    const qtdRecebida = item.quantidadeRecebida || 0;
                    const qtdPedida = item.quantidade || 0;
                    return qtdRecebida >= qtdPedida;
                });

                const algumRecebido = pedidoData.itens.some(item => {
                    const qtdRecebida = item.quantidadeRecebida || 0;
                    return qtdRecebida > 0;
                });

                let novoStatus;
                if (todosRecebidos) {
                    novoStatus = 'RECEBIDO';
                } else if (algumRecebido) {
                    novoStatus = 'RECEBIDO_PARCIAL';
                } else {
                    novoStatus = 'APROVADO'; // Volta para aprovado se não há nada recebido
                }

                console.log(`📊 Status do pedido: ${pedidoData.status} → ${novoStatus}`);

                // Atualizar pedido
                await updateDoc(doc(db, "pedidosCompra", recebimentoData.pedidoCompraId), {
                    status: novoStatus,
                    itens: pedidoData.itens,
                    dataUltimaAtualizacao: Timestamp.now()
                });

                console.log('✅ Pedido de compra atualizado');

            } catch (error) {
                console.error('❌ Erro ao reverter pedido de compra:', error);
                throw error;
            }
        }

        // ===== NOVAS FUNCIONALIDADES =====

        // Tema escuro
        window.toggleTheme = function() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            body.classList.toggle('dark-mode');

            if (body.classList.contains('dark-mode')) {
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            }
        };

        // Aplicar tema salvo
        function applyTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                document.getElementById('themeIcon').className = 'fas fa-sun';
            }
        }

        // Filtros inteligentes
        window.aplicarFiltroInteligente = function(tipo) {
            const hoje = new Date();
            const dataFim = document.getElementById('dataFim');
            const dataInicio = document.getElementById('dataInicio');
            const tipoFilter = document.getElementById('tipoFilter');

            // Remover classes ativas
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });

            switch(tipo) {
                case 'hoje':
                    dataInicio.value = hoje.toISOString().split('T')[0];
                    dataFim.value = hoje.toISOString().split('T')[0];
                    break;
                case '7dias':
                    const seteDiasAtras = new Date(hoje);
                    seteDiasAtras.setDate(hoje.getDate() - 7);
                    dataInicio.value = seteDiasAtras.toISOString().split('T')[0];
                    dataFim.value = hoje.toISOString().split('T')[0];
                    break;
                case '30dias':
                    const trintaDiasAtras = new Date(hoje);
                    trintaDiasAtras.setDate(hoje.getDate() - 30);
                    dataInicio.value = trintaDiasAtras.toISOString().split('T')[0];
                    dataFim.value = hoje.toISOString().split('T')[0];
                    break;
                case 'entradas':
                    tipoFilter.value = 'entrada';
                    break;
                case 'saidas':
                    tipoFilter.value = 'saida';
                    break;
                case 'recebimentos':
                    tipoFilter.value = 'recebimento';
                    break;
                case 'problemas':
                    // Filtrar movimentações com status de erro ou observações especiais
                    document.getElementById('searchInput').value = 'erro';
                    break;
                case 'limpar':
                    dataInicio.value = '';
                    dataFim.value = '';
                    tipoFilter.value = '';
                    document.getElementById('searchInput').value = '';
                    document.getElementById('usuarioFilter').value = '';
                    document.getElementById('armazemFilter').value = '';
                    break;
            }

            if (tipo !== 'limpar') {
                event.target.classList.add('active');
            }

            // Aplicar filtros automaticamente
            carregarMovimentacoes();
        };

        // Notificações modernas
        function showModernNotification(message, type = 'info') {
            // Remover notificação anterior se existir
            const existing = document.querySelector('.notification-modern');
            if (existing) {
                existing.remove();
            }

            const notification = document.createElement('div');
            notification.className = `notification-modern ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // Animar entrada
            setTimeout(() => notification.classList.add('show'), 100);

            // Remover após 4 segundos
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 400);
            }, 4000);
        }

        // Estatísticas avançadas
        function atualizarEstatisticasAvancadas() {
            const stats = {
                entradas: 0,
                saidas: 0,
                transferencias: 0,
                ajustes: 0,
                recebimentos: 0,
                producao: 0
            };

            // Verificar se filteredMovements está definido e é um array
            if (!window.filteredMovements || !Array.isArray(window.filteredMovements)) {
                console.warn('⚠️ filteredMovements não disponível para estatísticas');
                // Zerar todos os stats
                document.getElementById('statEntradas').textContent = '0';
                document.getElementById('statSaidas').textContent = '0';
                document.getElementById('statTransferencias').textContent = '0';
                document.getElementById('statAjustes').textContent = '0';
                document.getElementById('statRecebimentos').textContent = '0';
                document.getElementById('statProducao').textContent = '0';
                return;
            }

            window.filteredMovements.forEach(movimento => {
                if (!movimento || !movimento.tipo) return; // Skip se movimento ou tipo inválido

                const tipo = (movimento.tipo || '').toLowerCase();

                if (tipo.includes('entrada')) stats.entradas++;
                else if (tipo.includes('saida')) stats.saidas++;
                else if (tipo.includes('transferencia')) stats.transferencias++;
                else if (tipo.includes('ajuste')) stats.ajustes++;
                else if (tipo.includes('recebimento')) stats.recebimentos++;
                else if (tipo.includes('producao')) stats.producao++;
            });

            document.getElementById('statEntradas').textContent = stats.entradas;
            document.getElementById('statSaidas').textContent = stats.saidas;
            document.getElementById('statTransferencias').textContent = stats.transferencias;
            document.getElementById('statAjustes').textContent = stats.ajustes;
            document.getElementById('statRecebimentos').textContent = stats.recebimentos;
            document.getElementById('statProducao').textContent = stats.producao;
        }



        // Garantir que filteredMovements seja global
        window.filteredMovements = window.filteredMovements || [];

        // Override da função original para incluir novas funcionalidades
        const originalCarregarMovimentacoes = window.carregarMovimentacoes;
        window.carregarMovimentacoes = async function() {
            try {
                // Chamar função original
                await originalCarregarMovimentacoes();

                // Mostrar novos elementos
                document.getElementById('smartFilters').style.display = 'flex';
                document.getElementById('statsAdvanced').style.display = 'grid';

                // Aguardar um pouco para garantir que os dados estão prontos
                setTimeout(() => {
                    try {
                        atualizarEstatisticasAvancadas();
                        showModernNotification('Relatório atualizado com sucesso!', 'success');
                    } catch (error) {
                        console.error('❌ Erro ao atualizar componentes:', error);
                        showModernNotification('Dados carregados, mas houve problema nas estatísticas', 'warning');
                    }
                }, 1000);

            } catch (error) {
                console.error('❌ Erro ao carregar movimentações:', error);
                showModernNotification('Erro ao carregar dados: ' + error.message, 'error');
            }
        };

        // Exportar para Excel
        window.exportarExcel = function() {
            if (filteredMovements.length === 0) {
                showModernNotification('Nenhum dado para exportar!', 'warning');
                return;
            }

            const data = filteredMovements.map(movimento => {
                const dataHora = movimento.dataHora?.seconds ? 
                    new Date(movimento.dataHora.seconds * 1000) : 
                    new Date(movimento.dataHora);

                return {
                    'Data/Hora': dataHora.toLocaleString('pt-BR'),
                    'Tipo': movimento.tipo || '',
                    'Documento': movimento.documento || movimento.numeroDocumento || '',
                    'Produto/Item': movimento.produto || movimento.codigo || '',
                    'Quantidade': movimento.quantidade || 0,
                    'Armazém': movimento.armazem || '',
                    'Usuário': movimento.usuario || '',
                    'Status': movimento.status || '',
                    'Observações': movimento.observacoes || '',
                    'Valor Unitário': movimento.valorUnitario || 0,
                    'Valor Total': movimento.valorTotal || 0
                };
            });

            // Simular exportação Excel (substitua por biblioteca real se necessário)
            const csvContent = "data:text/csv;charset=utf-8," + 
                Object.keys(data[0]).join(',') + '\n' +
                data.map(row => Object.values(row).map(value => 
                    typeof value === 'string' && value.includes(',') ? `"${value}"` : value
                ).join(',')).join('\n');

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `movimentacoes_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showModernNotification('Dados exportados para Excel!', 'success');
        };

        // Relatório detalhado
        window.abrirRelatorioDetalhado = function() {
            if (filteredMovements.length === 0) {
                showModernNotification('Carregue os dados primeiro!', 'warning');
                return;
            }

            // Calcular estatísticas detalhadas
            const stats = {
                totalMovimentos: filteredMovements.length,
                valorTotal: filteredMovements.reduce((sum, mov) => sum + (mov.valorTotal || 0), 0),
                usuariosUnicos: [...new Set(filteredMovements.map(mov => mov.usuario))].length,
                produtosUnicos: [...new Set(filteredMovements.map(mov => mov.codigo || mov.produtoId))].length,
                armazensUsados: [...new Set(filteredMovements.map(mov => mov.armazem))].length
            };

            // Análise por período
            const movimentosPorDia = {};
            filteredMovements.forEach(movimento => {
                const data = movimento.dataHora?.seconds ? 
                    new Date(movimento.dataHora.seconds * 1000) : 
                    new Date(movimento.dataHora);
                const dia = data.toLocaleDateString('pt-BR');
                movimentosPorDia[dia] = (movimentosPorDia[dia] || 0) + 1;
            });

            // Top produtos
            const produtoCount = {};
            filteredMovements.forEach(movimento => {
                const produto = movimento.produto || movimento.codigo || 'Não informado';
                produtoCount[produto] = (produtoCount[produto] || 0) + 1;
            });
            const topProdutos = Object.entries(produtoCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);

            // Criar relatório em nova janela
            const relatorioWindow = window.open('', '_blank', 'width=1200,height=800');
            relatorioWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Relatório Detalhado de Movimentações</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; color: #2c3e50; }
                        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 10px; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
                        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #667eea; }
                        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
                        .section { margin: 30px 0; }
                        .section h3 { color: #2c3e50; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                        th { background: #34495e; color: white; }
                        tr:nth-child(even) { background: #f8f9fa; }
                        .chart-placeholder { background: #ecf0f1; height: 200px; display: flex; align-items: center; justify-content: center; border-radius: 10px; margin: 20px 0; }
                        @media print { body { margin: 0; } .no-print { display: none; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📊 Relatório Detalhado de Movimentações</h1>
                        <p>Período: ${document.getElementById('dataInicio').value || 'Início'} até ${document.getElementById('dataFim').value || 'Hoje'}</p>
                        <p>Gerado em: ${new Date().toLocaleString('pt-BR')}</p>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${stats.totalMovimentos.toLocaleString()}</div>
                            <div>Total de Movimentos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">R$ ${stats.valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                            <div>Valor Total Movimentado</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.usuariosUnicos}</div>
                            <div>Usuários Diferentes</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.produtosUnicos}</div>
                            <div>Produtos Diferentes</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.armazensUsados}</div>
                            <div>Armazéns Utilizados</div>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📈 Movimentações por Dia</h3>
                        <table>
                            <thead>
                                <tr><th>Data</th><th>Quantidade de Movimentos</th></tr>
                            </thead>
                            <tbody>
                                ${Object.entries(movimentosPorDia)
                                    .sort((a, b) => {
                                        const [diaA, mesA, anoA] = a[0].split('/');
                                        const [diaB, mesB, anoB] = b[0].split('/');
                                        return new Date(anoB, mesB - 1, diaB) - new Date(anoA, mesA - 1, diaA);
                                    })
                                    .map(([dia, count]) => `<tr><td>${dia}</td><td>${count}</td></tr>`)
                                    .join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="section">
                        <h3>🏆 Top 10 Produtos Mais Movimentados</h3>
                        <table>
                            <thead>
                                <tr><th>Posição</th><th>Produto</th><th>Movimentações</th></tr>
                            </thead>
                            <tbody>
                                ${topProdutos.map(([produto, count], index) => 
                                    `<tr><td>${index + 1}º</td><td>${produto}</td><td>${count}</td></tr>`
                                ).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="no-print" style="text-align: center; margin: 30px 0;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            🖨️ Imprimir Relatório
                        </button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #95a5a6; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                            ❌ Fechar
                        </button>
                    </div>
                </body>
                </html>
            `);
            relatorioWindow.document.close();

            showModernNotification('Relatório detalhado aberto em nova janela!', 'success');
        };

        // Habilitar botões quando dados carregados
        const originalRenderizarTabela = window.renderizarTabela;
        window.renderizarTabela = function(movements) {
            // Garantir que filteredMovements seja global se passado como parâmetro
            if (movements && Array.isArray(movements)) {
                window.filteredMovements = movements;
            }

            originalRenderizarTabela(movements);

            // Habilitar botões de exportação
            document.getElementById('btnExportExcel').disabled = false;
            document.getElementById('btnDetalhado').disabled = false;

            // Atualizar estatísticas sempre que a tabela for renderizada
            setTimeout(() => {
                atualizarEstatisticasAvancadas();
            }, 100);
        };



        // Inicialização
        window.onload = async function() {
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            // Aplicar tema salvo
            applyTheme();

            setDefaultDates();
            updateStatus('Carregando dados auxiliares...');

            // Carregar caches primeiro
            await Promise.all([
                loadProdutosCache(),
                loadArmazensCache()
            ]);

            updateStatus('Pronto para carregar movimentações');

            // Carregar automaticamente
            carregarMovimentacoes();
        };

    </script>
</body>
</html>
