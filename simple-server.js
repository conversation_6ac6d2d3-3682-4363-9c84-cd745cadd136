const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const port = 8000;

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Se for a raiz, servir index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    const filePath = path.join(__dirname, pathname);
    const ext = path.parse(filePath).ext;
    const mimeType = mimeTypes[ext] || 'text/plain';
    
    // Log da requisição
    const timestamp = new Date().toLocaleString('pt-BR');
    console.log(`[${timestamp}] "${req.method} ${req.url}"`);
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            console.log(`[${timestamp}] Error 404: ${pathname}`);
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('404 - Arquivo não encontrado');
        } else {
            res.writeHead(200, { 
                'Content-Type': mimeType,
                'Access-Control-Allow-Origin': '*'
            });
            res.end(data);
        }
    });
});

server.listen(port, '127.0.0.1', () => {
    console.log(`🚀 Servidor rodando em http://127.0.0.1:${port}/`);
    console.log(`📁 Servindo arquivos de: ${__dirname}`);
    console.log('📋 Páginas disponíveis:');
    console.log(`   🏠 Principal: http://127.0.0.1:${port}/`);
    console.log(`   📋 Necessidades: http://127.0.0.1:${port}/controle_baixa_necessidades.html`);
    console.log(`   👥 Fornecedores: http://127.0.0.1:${port}/cadastro_fornecedores.html`);
    console.log(`   🏗️ Estruturas: http://127.0.0.1:${port}/estrutura_nova.html`);
    console.log('');
});

server.on('error', (err) => {
    console.log(`❌ Erro: ${err.message}`);
});
