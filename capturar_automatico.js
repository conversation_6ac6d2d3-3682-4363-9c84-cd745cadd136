// Script para captura automática de telas
// Requer: npm install puppeteer

const puppeteer = require('puppeteer');
const fs = require('fs');

async function capturarTelasApostila() {
  console.log('🚀 Iniciando captura automática de telas...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. Tela Principal
    console.log('📸 Capturando tela principal...');
    await page.goto('http://127.0.0.1:8000/apontamentos_simplificado.html');
    await page.waitForTimeout(3000);
    await page.screenshot({ 
      path: 'tela_principal_apontamentos.png',
      fullPage: false
    });
    
    // 2. Filtros (expandir se necessário)
    console.log('📸 Capturando filtros...');
    // Aqui você pode adicionar código para expandir filtros se necessário
    await page.screenshot({ 
      path: 'filtros_busca_apontamentos.png',
      clip: { x: 0, y: 0, width: 1200, height: 200 }
    });
    
    // 3. <PERSON><PERSON><PERSON> (procurar OP com botão habilitado)
    console.log('📸 Capturando botão apontar...');
    const botaoApontar = await page.$('.btn-success');
    if (botaoApontar) {
      await botaoApontar.screenshot({ path: 'botao_apontar_producao.png' });
    }
    
    // 4. Modal de Apontamento
    console.log('📸 Capturando modal de apontamento...');
    if (botaoApontar) {
      await botaoApontar.click();
      await page.waitForTimeout(1000);
      const modal = await page.$('.modal');
      if (modal) {
        await modal.screenshot({ path: 'modal_apontamento.png' });
        // Fechar modal
        await page.keyboard.press('Escape');
      }
    }
    
    // 5. Botão Verificar Saldo
    console.log('📸 Capturando botão verificar saldo...');
    const botaoVerificar = await page.$('.btn-info');
    if (botaoVerificar) {
      await botaoVerificar.screenshot({ path: 'botao_verificar_saldo.png' });
      
      // 6. Modal de Materiais
      console.log('📸 Capturando modal de materiais...');
      await botaoVerificar.click();
      await page.waitForTimeout(2000);
      const modalMateriais = await page.$('.modal');
      if (modalMateriais) {
        await modalMateriais.screenshot({ path: 'modal_materiais_faltantes.png' });
        await page.keyboard.press('Escape');
      }
    }
    
    // 7. Botão Imprimir
    console.log('📸 Capturando botão imprimir...');
    const botaoImprimir = await page.$('.btn-secondary');
    if (botaoImprimir) {
      await botaoImprimir.screenshot({ path: 'botao_imprimir_op.png' });
    }
    
    // 8. Relatório de Materiais
    console.log('📸 Capturando relatório de materiais...');
    await page.goto('http://127.0.0.1:8000/relatorio_materiais_producao.html');
    await page.waitForTimeout(3000);
    await page.screenshot({ 
      path: 'relatorio_materiais.png',
      fullPage: false
    });
    
    console.log('✅ Todas as capturas foram realizadas com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante a captura:', error);
  } finally {
    await browser.close();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  capturarTelasApostila();
}

module.exports = { capturarTelasApostila };
