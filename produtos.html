<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Produtos - Sistema MRP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --text-color: #333;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', sans-serif;
        }

        body {
            background-color: #f8f9fa;
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 24px;
            margin-bottom: 10px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .breadcrumb span {
            margin: 0 8px;
        }

        .form-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        .image-upload {
            border: 2px dashed var(--border-color);
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: border-color 0.2s;
        }

        .image-upload:hover {
            border-color: var(--primary-color);
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin-top: 10px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            font-weight: 500;
            position: relative;
        }

        .tab.active {
            color: var(--primary-color);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            display: none;
            padding: 20px 0;
        }

        .tab-content.active {
            display: block;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--secondary-color);
            font-weight: 500;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-inactive {
            background-color: #ffebee;
            color: #c62828;
        }

        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .toast-success {
            border-left: 4px solid var(--success-color);
        }

        .toast-error {
            border-left: 4px solid var(--danger-color);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Cadastro de Produtos</h1>
            <div class="breadcrumb">
                <a href="index.html">Início</a>
                <span>/</span>
                <a href="#">Cadastros</a>
                <span>/</span>
                <strong>Produtos</strong>
            </div>
        </div>

        <div class="form-container">
            <div class="search-container">
                <input type="text" id="searchInput" class="search-input" placeholder="Pesquisar produtos...">
                <button class="btn btn-primary" onclick="searchProducts()">
                    <i class="fas fa-search"></i> Buscar
                </button>
                <button class="btn btn-secondary" onclick="clearSearch()">
                    <i class="fas fa-times"></i> Limpar
                </button>
                <button class="btn btn-primary" onclick="showNewProductForm()">
                    <i class="fas fa-plus"></i> Novo Produto
                </button>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="switchTab('basic')">Dados Básicos</button>
                <button class="tab" onclick="switchTab('technical')">Dados Técnicos</button>
                <button class="tab" onclick="switchTab('fiscal')">Dados Fiscais</button>
                <button class="tab" onclick="switchTab('stock')">Estoque</button>
            </div>

            <form id="productForm" onsubmit="saveProduct(event)">
                <div id="basicTab" class="tab-content active">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="codigo">Código</label>
                            <input type="text" id="codigo" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="descricao">Descrição</label>
                            <input type="text" id="descricao" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="tipo">Tipo</label>
                            <select id="tipo" class="form-control" required>
                                <option value="">Selecione...</option>
                                <option value="PA">Produto Acabado</option>
                                <option value="MP">Matéria Prima</option>
                                <option value="PI">Produto Intermediário</option>
                                <option value="SV">Serviço</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="unidade">Unidade de Medida</label>
                            <select id="unidade" class="form-control" required>
                                <option value="">Selecione...</option>
                                <option value="UN">Unidade</option>
                                <option value="PC">Peça</option>
                                <option value="KG">Quilograma</option>
                                <option value="MT">Metro</option>
                                <option value="LT">Litro</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="grupo">Grupo</label>
                            <select id="grupo" class="form-control" required>
                                <option value="">Selecione...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subgrupo">Subgrupo</label>
                            <select id="subgrupo" class="form-control">
                                <option value="">Selecione...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="marca">Marca</label>
                            <input type="text" id="marca" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="modelo">Modelo</label>
                            <input type="text" id="modelo" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="observacoes">Observações</label>
                        <textarea id="observacoes" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Imagem do Produto</label>
                        <div class="image-upload" onclick="document.getElementById('imageInput').click()">
                            <input type="file" id="imageInput" hidden accept="image/*" onchange="handleImageUpload(event)">
                            <i class="fas fa-cloud-upload-alt fa-3x"></i>
                            <p>Clique ou arraste uma imagem aqui</p>
                            <img id="imagePreview" class="image-preview" style="display: none;">
                        </div>
                    </div>
                </div>

                <div id="technicalTab" class="tab-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="peso">Peso (kg)</label>
                            <input type="number" id="peso" class="form-control" step="0.001">
                        </div>
                        <div class="form-group">
                            <label for="altura">Altura (cm)</label>
                            <input type="number" id="altura" class="form-control" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="largura">Largura (cm)</label>
                            <input type="number" id="largura" class="form-control" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="comprimento">Comprimento (cm)</label>
                            <input type="number" id="comprimento" class="form-control" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="garantia">Garantia (meses)</label>
                            <input type="number" id="garantia" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="fabricante">Fabricante</label>
                            <input type="text" id="fabricante" class="form-control">
                        </div>
                    </div>
                </div>

                <div id="fiscalTab" class="tab-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="ncm">NCM</label>
                            <input type="text" id="ncm" class="form-control" maxlength="8">
                        </div>
                        <div class="form-group">
                            <label for="cest">CEST</label>
                            <input type="text" id="cest" class="form-control" maxlength="7">
                        </div>
                        <div class="form-group">
                            <label for="origem">Origem</label>
                            <select id="origem" class="form-control">
                                <option value="">Selecione...</option>
                                <option value="0">0 - Nacional</option>
                                <option value="1">1 - Estrangeira (Importação direta)</option>
                                <option value="2">2 - Estrangeira (Adquirida no mercado interno)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="icms">ICMS (%)</label>
                            <input type="number" id="icms" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="ipi">IPI (%)</label>
                            <input type="number" id="ipi" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="pis">PIS (%)</label>
                            <input type="number" id="pis" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="cofins">COFINS (%)</label>
                            <input type="number" id="cofins" class="form-control" step="0.01">
                        </div>
                    </div>
                </div>

                <div id="stockTab" class="tab-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="estoqueMinimo">Estoque Mínimo</label>
                            <input type="number" id="estoqueMinimo" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="estoqueMaximo">Estoque Máximo</label>
                            <input type="number" id="estoqueMaximo" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="pontoPedido">Ponto de Pedido</label>
                            <input type="number" id="pontoPedido" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="leadTime">Lead Time (dias)</label>
                            <input type="number" id="leadTime" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="loteMinimo">Lote Mínimo</label>
                            <input type="number" id="loteMinimo" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="loteMultiplo">Lote Múltiplo</label>
                            <input type="number" id="loteMultiplo" class="form-control" step="0.01">
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                </div>
            </form>
        </div>

        <div class="table-container">
            <table id="productsTable">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Descrição</th>
                        <th>Tipo</th>
                        <th>Unidade</th>
                        <th>Grupo</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Dados serão preenchidos via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script type="module">
        import { db, storage } from './firebase-config.js';
        import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { ref, uploadBytes, getDownloadURL } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

        let currentProductId = null;
        let imageFile = null;

        // Carregar grupos ao iniciar
        window.onload = async function() {
            await loadGroups();
            await loadProducts();
        };

        async function loadGroups() {
            try {
                const querySnapshot = await getDocs(collection(db, "grupos"));
                const grupoSelect = document.getElementById('grupo');
                grupoSelect.innerHTML = '<option value="">Selecione...</option>';

                querySnapshot.forEach((doc) => {
                    const grupo = doc.data();
                    const option = document.createElement('option');
                    option.value = doc.id;
                    option.textContent = grupo.nome;
                    grupoSelect.appendChild(option);
                });
            } catch (error) {
                showToast('Erro ao carregar grupos', 'error');
            }
        }

        async function loadProducts() {
            try {
                const querySnapshot = await getDocs(collection(db, "produtos"));
                const tbody = document.querySelector('#productsTable tbody');
                tbody.innerHTML = '';

                querySnapshot.forEach((doc) => {
                    const product = doc.data();
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${product.codigo}</td>
                        <td>${product.descricao}</td>
                        <td>${getTipoDescricao(product.tipo)}</td>
                        <td>${product.unidade}</td>
                        <td>${product.grupo}</td>
                        <td><span class="status-badge ${product.ativo ? 'status-active' : 'status-inactive'}">
                            ${product.ativo ? 'Ativo' : 'Inativo'}
                        </span></td>
                        <td>
                            <button class="btn btn-secondary" onclick="editProduct('${doc.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger" onclick="deleteProduct('${doc.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                showToast('Erro ao carregar produtos', 'error');
            }
        }

        function getTipoDescricao(tipo) {
            const tipos = {
                'PA': 'Produto Acabado',
                'MP': 'Matéria Prima',
                'PI': 'Produto Intermediário',
                'SV': 'Serviço'
            };
            return tipos[tipo] || tipo;
        }

        window.saveProduct = async function(event) {
            event.preventDefault();
            showLoading();

            try {
                const productData = {
                    codigo: document.getElementById('codigo').value,
                    descricao: document.getElementById('descricao').value,
                    tipo: document.getElementById('tipo').value,
                    unidade: document.getElementById('unidade').value,
                    grupo: document.getElementById('grupo').value,
                    subgrupo: document.getElementById('subgrupo').value,
                    marca: document.getElementById('marca').value,
                    modelo: document.getElementById('modelo').value,
                    observacoes: document.getElementById('observacoes').value,
                    // Dados técnicos
                    peso: parseFloat(document.getElementById('peso').value) || 0,
                    altura: parseFloat(document.getElementById('altura').value) || 0,
                    largura: parseFloat(document.getElementById('largura').value) || 0,
                    comprimento: parseFloat(document.getElementById('comprimento').value) || 0,
                    garantia: parseInt(document.getElementById('garantia').value) || 0,
                    fabricante: document.getElementById('fabricante').value,
                    // Dados fiscais
                    ncm: document.getElementById('ncm').value,
                    cest: document.getElementById('cest').value,
                    origem: document.getElementById('origem').value,
                    icms: parseFloat(document.getElementById('icms').value) || 0,
                    ipi: parseFloat(document.getElementById('ipi').value) || 0,
                    pis: parseFloat(document.getElementById('pis').value) || 0,
                    cofins: parseFloat(document.getElementById('cofins').value) || 0,
                    // Dados de estoque
                    estoqueMinimo: parseFloat(document.getElementById('estoqueMinimo').value) || 0,
                    estoqueMaximo: parseFloat(document.getElementById('estoqueMaximo').value) || 0,
                    pontoPedido: parseFloat(document.getElementById('pontoPedido').value) || 0,
                    leadTime: parseInt(document.getElementById('leadTime').value) || 0,
                    loteMinimo: parseFloat(document.getElementById('loteMinimo').value) || 0,
                    loteMultiplo: parseFloat(document.getElementById('loteMultiplo').value) || 0,
                    ativo: true,
                    dataCadastro: new Date()
                };

                if (imageFile) {
                    const storageRef = ref(storage, `produtos/${productData.codigo}`);
                    await uploadBytes(storageRef, imageFile);
                    productData.imageUrl = await getDownloadURL(storageRef);
                }

                if (currentProductId) {
                    await updateDoc(doc(db, "produtos", currentProductId), productData);
                    showToast('Produto atualizado com sucesso!', 'success');
                } else {
                    await addDoc(collection(db, "produtos"), productData);
                    showToast('Produto cadastrado com sucesso!', 'success');
                }

                clearForm();
                await loadProducts();
            } catch (error) {
                showToast('Erro ao salvar produto', 'error');
            } finally {
                hideLoading();
            }
        };

        window.editProduct = async function(id) {
            showLoading();
            try {
                const docRef = doc(db, "produtos", id);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const product = docSnap.data();
                    currentProductId = id;

                    // Preencher formulário
                    Object.keys(product).forEach(key => {
                        const element = document.getElementById(key);
                        if (element) {
                            element.value = product[key];
                        }
                    });

                    if (product.imageUrl) {
                        document.getElementById('imagePreview').src = product.imageUrl;
                        document.getElementById('imagePreview').style.display = 'block';
                    }

                    showToast('Produto carregado para edição', 'success');
                }
            } catch (error) {
                showToast('Erro ao carregar produto', 'error');
            } finally {
                hideLoading();
            }
        };

        window.deleteProduct = async function(id) {
            if (!confirm('Deseja realmente excluir este produto?')) return;

            showLoading();
            try {
                await deleteDoc(doc(db, "produtos", id));
                await loadProducts();
                showToast('Produto excluído com sucesso!', 'success');
            } catch (error) {
                showToast('Erro ao excluir produto', 'error');
            } finally {
                hideLoading();
            }
        };

        window.clearForm = function() {
            document.getElementById('productForm').reset();
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('imagePreview').src = '';
            currentProductId = null;
            imageFile = null;
        };

        window.handleImageUpload = function(event) {
            const file = event.target.files[0];
            if (file) {
                imageFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        };

        window.switchTab = function(tabName) {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');
        };

        window.searchProducts = async function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            showLoading();

            try {
                const querySnapshot = await getDocs(collection(db, "produtos"));
                const tbody = document.querySelector('#productsTable tbody');
                tbody.innerHTML = '';

                querySnapshot.forEach((doc) => {
                    const product = doc.data();
                    if (
                        product.codigo.toLowerCase().includes(searchTerm) ||
                        product.descricao.toLowerCase().includes(searchTerm)
                    ) {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${product.codigo}</td>
                            <td>${product.descricao}</td>
                            <td>${getTipoDescricao(product.tipo)}</td>
                            <td>${product.unidade}</td>
                            <td>${product.grupo}</td>
                            <td><span class="status-badge ${product.ativo ? 'status-active' : 'status-inactive'}">
                                ${product.ativo ? 'Ativo'
                                : 'Inativo'}
                            </span></td>
                            <td>
                                <button class="btn btn-secondary" onclick="editProduct('${doc.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger" onclick="deleteProduct('${doc.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    }
                });
            } catch (error) {
                showToast('Erro ao pesquisar produtos', 'error');
            } finally {
                hideLoading();
            }
        };

        window.clearSearch = async function() {
            document.getElementById('searchInput').value = '';
            await loadProducts();
        };

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showLoading() {
            const loading = document.createElement('div');
            loading.className = 'loading-overlay';
            loading.innerHTML = '<div class="spinner"></div>';
            document.body.appendChild(loading);
        }

        function hideLoading() {
            const loading = document.querySelector('.loading-overlay');
            if (loading) {
                loading.remove();
            }
        }

        // Exportar funções para o escopo global
        window.showToast = showToast;
        window.showLoading = showLoading;
        window.hideLoading = hideLoading;
    </script>
</body>
</html>