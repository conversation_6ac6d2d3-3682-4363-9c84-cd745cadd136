<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inicialização do Banco de Dados</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0854a0;
            text-align: center;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        button {
            background-color: #0854a0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background-color: #0a4d8c;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Inicialização do Banco de Dados</h1>
        
        <div class="warning">
            <strong>Atenção!</strong> Esta operação irá:
            <ul>
                <li>Criar um usuário administrador (admin/admin123)</li>
                <li>Configurar dados básicos da empresa</li>
                <li>Criar parâmetros iniciais do sistema</li>
                <li>Inicializar contadores</li>
                <li>Criar grupos básicos de produtos</li>
                <li>Criar um armazém padrão</li>
            </ul>
            <p>Esta operação só deve ser executada em um banco de dados vazio!</p>
        </div>

        <button id="initButton" onclick="initializeDatabase()">Inicializar Banco de Dados</button>
        <div id="status"></div>
    </div>

    <script type="module">
        import { initializeDatabase } from './init_database.js';

        window.initializeDatabase = async function() {
            const button = document.getElementById('initButton');
            const status = document.getElementById('status');
            
            button.disabled = true;
            status.style.display = 'block';
            status.className = '';
            status.textContent = 'Inicializando banco de dados...';

            try {
                await initializeDatabase();
                status.className = 'success';
                status.textContent = 'Banco de dados inicializado com sucesso!';
            } catch (error) {
                status.className = 'error';
                status.textContent = 'Erro ao inicializar banco de dados: ' + error.message;
                button.disabled = false;
            }
        };
    </script>
</body>
</html> 