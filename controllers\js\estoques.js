// ===================================================================
// ESTOQUES.JS - MÓDULO OTIMIZADO PARA GESTÃO DE ESTOQUES
// ===================================================================
// IMPORTAÇÃO CENTRALIZADA DO FIREBASE
// ===================================================================
import { db } from '../firebase-config.js';
import { 
  collection, 
  addDoc, 
  onSnapshot,
  doc,
  updateDoc,
  Timestamp,
  getDoc,
  query,
  limit,
  orderBy,
  startAfter,
  getDocs,
  where
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Configurações de performance
const CONFIG = {
  ITEMS_PER_PAGE: 50,
  CACHE_TIMEOUT: 10 * 60 * 1000, // 10 minutos
  SEARCH_DEBOUNCE: 500, // 500ms
  VIRTUAL_SCROLL_BUFFER: 10
};

// Estado global otimizado
class EstoqueState {
  constructor() {
    this.produtos = [];
    this.estoques = [];
    this.armazens = [];
    this.movimentacoes = [];
    this.lastVisible = null;
    this.currentPage = 1;
    this.isLoading = false;
    this.searchCache = new Map();
    this.listeners = [];
  }

  // Cache inteligente com TTL
  setCache(key, data, ttl = CONFIG.CACHE_TIMEOUT) {
    const item = {
      data,
      timestamp: Date.now(),
      ttl
    };
    localStorage.setItem(`estoque_${key}`, JSON.stringify(item));
  }

  getCache(key) {
    try {
      const item = JSON.parse(localStorage.getItem(`estoque_${key}`));
      if (item && (Date.now() - item.timestamp) < item.ttl) {
        return item.data;
      }
      localStorage.removeItem(`estoque_${key}`);
    } catch (e) {
      console.warn('Cache read error:', e);
    }
    return null;
  }

  // Debounce otimizado
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// Instância global do estado
const state = new EstoqueState();

// Utilitários de formatação otimizados
const formatters = {
  number: new Intl.NumberFormat('pt-BR', { 
    minimumFractionDigits: 3, 
    maximumFractionDigits: 3 
  }),
  currency: new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }),
  date: new Intl.DateTimeFormat('pt-BR')
};

// Classe para virtualização de tabela
class VirtualTable {
  constructor(container, itemHeight = 40) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + CONFIG.VIRTUAL_SCROLL_BUFFER;
    this.scrollTop = 0;
    this.data = [];
    this.renderFunction = null;
  }

  setData(data, renderFn) {
    this.data = data;
    this.renderFunction = renderFn;
    this.render();
  }

  render() {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleItems, this.data.length);
    
    // Criar container virtual
    const totalHeight = this.data.length * this.itemHeight;
    const offsetY = startIndex * this.itemHeight;
    
    this.container.innerHTML = `
      <div style="height: ${totalHeight}px; position: relative;">
        <div style="transform: translateY(${offsetY}px);">
          ${this.data.slice(startIndex, endIndex).map(this.renderFunction).join('')}
        </div>
      </div>
    `;
  }

  onScroll(scrollTop) {
    this.scrollTop = scrollTop;
    this.render();
  }
}

// Otimizações de consulta Firebase
class FirebaseOptimizer {
  static createCompoundQuery(collectionName, filters = [], orderField = null, limitCount = CONFIG.ITEMS_PER_PAGE) {
    let q = collection(db, collectionName);
    
    // Aplicar filtros
    filters.forEach(filter => {
      q = query(q, where(filter.field, filter.operator, filter.value));
    });
    
    // Aplicar ordenação
    if (orderField) {
      q = query(q, orderBy(orderField));
    }
    
    // Aplicar limite
    q = query(q, limit(limitCount));
    
    return q;
  }

  static async batchGet(queries) {
    const promises = queries.map(q => getDocs(q));
    return Promise.all(promises);
  }
}

// Gerenciador de loading states
class LoadingManager {
  static show(element, message = 'Carregando...') {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.innerHTML = `
        <div class="loading-skeleton">
          <div class="skeleton-line"></div>
          <div class="skeleton-line"></div>
          <div class="skeleton-line"></div>
          <p>${message}</p>
        </div>
      `;
      element.classList.add('loading');
    }
  }

  static hide(element) {
    if (typeof element === 'string') {
      element = document.getElementById(element);
    }
    if (element) {
      element.classList.remove('loading');
    }
  }
}

// Inicialização otimizada
async function initializeEstoques() {
  try {
    // Carregar dados do cache primeiro
    await loadFromCache();
    
    // Configurar listeners otimizados
    setupOptimizedListeners();
    
    // Configurar interface
    setupUI();
    
    // Carregar dados iniciais se necessário
    if (state.produtos.length === 0) {
      await loadInitialData();
    }
    
  } catch (error) {
    console.error('Erro na inicialização:', error);
    showError('Erro ao inicializar sistema de estoques');
  }
}

async function loadFromCache() {
  const cachedProdutos = state.getCache('produtos');
  const cachedArmazens = state.getCache('armazens');
  
  if (cachedProdutos) state.produtos = cachedProdutos;
  if (cachedArmazens) state.armazens = cachedArmazens;
  
  if (cachedProdutos && cachedArmazens) {
    updateProductSelect();
    updateWarehouseSelects();
    return true;
  }
  return false;
}

function setupOptimizedListeners() {
  // Listener otimizado para produtos com debounce
  const produtosListener = onSnapshot(
    collection(db, "produtos"),
    state.debounce((snapshot) => {
      state.produtos = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      state.setCache('produtos', state.produtos);
      updateProductSelect();
      loadStock(); // Recarregar apenas se necessário
    }, 1000)
  );

  // Listener otimizado para armazéns
  const armazensListener = onSnapshot(
    collection(db, "armazens"),
    state.debounce((snapshot) => {
      state.armazens = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      state.setCache('armazens', state.armazens);
      updateWarehouseSelects();
      updateWarehouseFilter();
    }, 1000)
  );

  state.listeners.push(produtosListener, armazensListener);
}

function setupUI() {
  // Configurar busca com debounce otimizado
  const searchInput = document.getElementById('searchInput');
  if (searchInput) {
    searchInput.addEventListener('input', 
      state.debounce(handleSearch, CONFIG.SEARCH_DEBOUNCE)
    );
  }

  // Configurar botão de busca
  const btnBuscar = document.getElementById('btnBuscarSaldos');
  if (btnBuscar) {
    btnBuscar.onclick = filterStock;
  }

  // Configurar paginação otimizada
  setupPagination();
}

// Exportar funções globais necessárias
window.initializeEstoques = initializeEstoques;
window.switchTab = switchTab;
window.filterStock = filterStock;
window.openMovementModal = openMovementModal;
window.closeModal = closeModal;
window.updateCalculations = updateCalculations;
window.handleMovement = handleMovement;
window.exportToExcel = exportToExcel;
window.handleFileSelect = handleFileSelect;

// Implementação das funções principais otimizadas
async function loadInitialData() {
  LoadingManager.show('stockTableBody', 'Carregando produtos...');

  try {
    const [produtosSnapshot, armazensSnapshot] = await Promise.all([
      getDocs(collection(db, "produtos")),
      getDocs(collection(db, "armazens"))
    ]);

    state.produtos = produtosSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    state.armazens = armazensSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Cache dos dados
    state.setCache('produtos', state.produtos);
    state.setCache('armazens', state.armazens);

    updateProductSelect();
    updateWarehouseSelects();
    updateWarehouseFilter();

    await loadStock();
  } catch (error) {
    console.error('Erro ao carregar dados iniciais:', error);
    showError('Erro ao carregar dados iniciais');
  } finally {
    LoadingManager.hide('stockTableBody');
  }
}

async function loadStock() {
  if (state.isLoading) return;
  state.isLoading = true;

  try {
    LoadingManager.show('stockTableBody');

    const stockQuery = query(
      collection(db, "estoques"),
      orderBy("produtoId"),
      limit(CONFIG.ITEMS_PER_PAGE)
    );

    const snapshot = await getDocs(stockQuery);
    state.lastVisible = snapshot.docs[snapshot.docs.length - 1];

    const stockData = await processStockData(snapshot.docs);
    renderStockTable(stockData);

    updatePaginationControls();
  } catch (error) {
    console.error("Erro ao carregar estoque:", error);
    showError("Erro ao carregar dados do estoque");
  } finally {
    state.isLoading = false;
    LoadingManager.hide('stockTableBody');
  }
}

async function processStockData(docs) {
  return docs.map(doc => {
    const estoque = { id: doc.id, ...doc.data() };
    const produto = state.produtos.find(p => p.id === estoque.produtoId);
    const armazem = state.armazens.find(a => a.id === estoque.armazemId);

    return {
      ...estoque,
      codigo: produto?.codigo || '-',
      descricao: produto?.descricao || '-',
      tipo: produto?.tipo || '-',
      unidade: produto?.unidade || '-',
      armazem: armazem?.codigo || '-'
    };
  });
}

function renderStockTable(stockData) {
  const tbody = document.getElementById('stockTableBody');
  if (!tbody) return;

  // Usar DocumentFragment para melhor performance
  const fragment = document.createDocumentFragment();

  stockData.forEach(item => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${item.codigo}</td>
      <td>${item.descricao}</td>
      <td>${item.tipo}</td>
      <td>${item.unidade}</td>
      <td>${item.armazem}</td>
      <td class="text-right">${formatters.number.format(item.saldo || 0)}</td>
      <td class="text-right">${formatters.currency.format(item.precoUnitario || 0)}</td>
      <td class="text-right">${formatters.currency.format((item.saldo || 0) * (item.precoUnitario || 0))}</td>
      <td>${formatDate(item.ultimaMovimentacao)}</td>
    `;
    fragment.appendChild(row);
  });

  tbody.innerHTML = '';
  tbody.appendChild(fragment);
}

function handleSearch(event) {
  const searchTerm = event.target.value.toLowerCase().trim();

  if (searchTerm.length >= 3) {
    searchStock(searchTerm);
  } else if (searchTerm.length === 0) {
    loadStock();
  }
}

async function searchStock(searchTerm) {
  if (state.isLoading) return;

  // Verificar cache de busca
  const cacheKey = `search_${searchTerm}`;
  const cachedResult = state.searchCache.get(cacheKey);

  if (cachedResult && (Date.now() - cachedResult.timestamp) < 30000) { // 30s cache
    renderStockTable(cachedResult.data);
    return;
  }

  state.isLoading = true;
  LoadingManager.show('stockTableBody', 'Buscando...');

  try {
    // Busca otimizada com múltiplas queries paralelas
    const queries = [
      query(
        collection(db, "estoques"),
        where("codigo", ">=", searchTerm),
        where("codigo", "<=", searchTerm + '\uf8ff'),
        limit(CONFIG.ITEMS_PER_PAGE)
      )
    ];

    const [stockSnapshot] = await Promise.all(queries.map(q => getDocs(q)));
    const stockData = await processStockData(stockSnapshot.docs);

    // Cache do resultado
    state.searchCache.set(cacheKey, {
      data: stockData,
      timestamp: Date.now()
    });

    renderStockTable(stockData);
    updatePaginationControls(true); // Modo busca
  } catch (error) {
    console.error("Erro na busca:", error);
    showError("Erro ao realizar busca");
  } finally {
    state.isLoading = false;
    LoadingManager.hide('stockTableBody');
  }
}

function setupPagination() {
  const paginationDiv = document.querySelector('.pagination');
  if (!paginationDiv) return;

  paginationDiv.innerHTML = `
    <button id="prevPage" class="btn btn-secondary" disabled>Anterior</button>
    <span id="pageInfo">Página 1</span>
    <button id="nextPage" class="btn btn-secondary">Próxima</button>
  `;

  document.getElementById('prevPage').onclick = loadPreviousPage;
  document.getElementById('nextPage').onclick = loadNextPage;
}

function updatePaginationControls(isSearchMode = false) {
  const pageInfo = document.getElementById('pageInfo');
  const prevBtn = document.getElementById('prevPage');
  const nextBtn = document.getElementById('nextPage');

  if (isSearchMode) {
    pageInfo.textContent = 'Resultados da busca';
    prevBtn.disabled = true;
    nextBtn.disabled = true;
  } else {
    pageInfo.textContent = `Página ${state.currentPage}`;
    prevBtn.disabled = state.currentPage === 1;
    nextBtn.disabled = !state.lastVisible;
  }
}

async function loadNextPage() {
  if (state.isLoading || !state.lastVisible) return;
  state.isLoading = true;
  state.currentPage++;

  try {
    const nextQuery = query(
      collection(db, "estoques"),
      orderBy("produtoId"),
      startAfter(state.lastVisible),
      limit(CONFIG.ITEMS_PER_PAGE)
    );

    const snapshot = await getDocs(nextQuery);
    state.lastVisible = snapshot.docs[snapshot.docs.length - 1];

    const stockData = await processStockData(snapshot.docs);
    renderStockTable(stockData);
    updatePaginationControls();
  } catch (error) {
    console.error("Erro ao carregar próxima página:", error);
    showError("Erro ao carregar próxima página");
  } finally {
    state.isLoading = false;
  }
}

async function loadPreviousPage() {
  if (state.isLoading || state.currentPage <= 1) return;
  state.currentPage--;
  await loadStock();
}

function switchTab(tab) {
  document.querySelectorAll('.tab-content').forEach(content =>
    content.classList.remove('active')
  );
  document.querySelectorAll('.btn').forEach(button =>
    button.classList.remove('active')
  );

  const tabContent = document.getElementById(`${tab}Tab`);
  const tabButton = document.querySelector(`button[onclick="switchTab('${tab}')"]`);

  if (tabContent) tabContent.classList.add('active');
  if (tabButton) tabButton.classList.add('active');

  if (tab === 'movimentacoes') {
    loadMovements();
  }
}

async function filterStock() {
  const searchText = document.getElementById('searchInput')?.value?.toLowerCase() || '';
  const typeFilter = document.getElementById('typeFilter')?.value || '';
  const warehouseFilter = document.getElementById('warehouseFilter')?.value || '';

  if (state.isLoading) return;
  state.isLoading = true;
  LoadingManager.show('stockTableBody', 'Filtrando...');

  try {
    // Construir query otimizada com filtros
    const filters = [];
    if (typeFilter) filters.push({ field: 'tipo', operator: '==', value: typeFilter });
    if (warehouseFilter) filters.push({ field: 'armazemId', operator: '==', value: warehouseFilter });

    const stockQuery = FirebaseOptimizer.createCompoundQuery('estoques', filters, 'produtoId');
    const snapshot = await getDocs(stockQuery);

    let stockData = await processStockData(snapshot.docs);

    // Filtro de texto no cliente para melhor performance
    if (searchText) {
      stockData = stockData.filter(item =>
        item.codigo.toLowerCase().includes(searchText) ||
        item.descricao.toLowerCase().includes(searchText)
      );
    }

    renderStockTable(stockData);
    updatePaginationControls(true);
  } catch (error) {
    console.error("Erro ao filtrar estoque:", error);
    showError("Erro ao filtrar dados");
  } finally {
    state.isLoading = false;
    LoadingManager.hide('stockTableBody');
  }
}

function updateProductSelect() {
  // Atualizar ambos os selects de produto (principal e modal)
  const selects = ['productSelect', 'modalProductSelect'];

  selects.forEach(selectId => {
    const select = document.getElementById(selectId);
    if (!select) return;

    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Selecione o produto...';
    fragment.appendChild(defaultOption);

    state.produtos.forEach(produto => {
      const option = document.createElement('option');
      option.value = produto.id;
      option.textContent = `${produto.codigo} - ${produto.descricao} (${produto.tipo})`;
      fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);
  });
}

function updateWarehouseSelects() {
  // Atualizar todos os selects de armazém (principal e modal)
  const selects = [
    'warehouseOrigin', 'warehouseDestiny',
    'modalWarehouseOrigin', 'modalWarehouseDestiny'
  ];

  selects.forEach(selectId => {
    const select = document.getElementById(selectId);
    if (!select) return;

    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';

    // Definir texto padrão baseado no ID
    if (selectId.includes('Origin')) {
      defaultOption.textContent = 'Selecione o armazém de origem...';
    } else {
      defaultOption.textContent = 'Selecione o armazém de destino...';
    }
    fragment.appendChild(defaultOption);

    state.armazens.forEach(armazem => {
      const option = document.createElement('option');
      option.value = armazem.id;
      option.textContent = armazem.codigo;
      fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);
  });
}

function updateWarehouseFilter() {
  const select = document.getElementById('warehouseFilter');
  if (!select) return;

  const currentValue = select.value;
  const fragment = document.createDocumentFragment();

  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = 'Todos os armazéns';
  fragment.appendChild(defaultOption);

  // Ordenar armazéns por código
  const sortedArmazens = [...state.armazens].sort((a, b) =>
    a.codigo.localeCompare(b.codigo)
  );

  sortedArmazens.forEach(armazem => {
    const option = document.createElement('option');
    option.value = armazem.id;
    option.textContent = armazem.codigo;
    fragment.appendChild(option);
  });

  select.innerHTML = '';
  select.appendChild(fragment);
  select.value = currentValue;
}

// Utilitários
function formatDate(timestamp) {
  if (!timestamp) return '';

  try {
    let date;
    if (typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } else if (typeof timestamp.seconds === 'number') {
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
      if (isNaN(date.getTime())) return '';
    } else if (typeof timestamp === 'number') {
      date = new Date(timestamp);
      if (isNaN(date.getTime())) return '';
    } else {
      return '';
    }
    return formatters.date.format(date);
  } catch (e) {
    console.warn('Erro ao formatar data:', e, timestamp);
    return '';
  }
}

function showError(message) {
  // Implementar sistema de notificação otimizado
  console.error(message);

  // Criar toast notification
  const toast = document.createElement('div');
  toast.className = 'toast error';
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f44336;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 5000);
}

// Funções de movimentação otimizadas
async function loadMovements() {
  const tableBody = document.getElementById('movementsTableBody');
  if (!tableBody) return;

  LoadingManager.show(tableBody, 'Carregando movimentações...');

  try {
    const movementsQuery = query(
      collection(db, "movimentacoesEstoque"),
      orderBy("dataHora", "desc"),
      limit(50)
    );

    const snapshot = await getDocs(movementsQuery);
    const movements = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    renderMovementsTable(movements);
  } catch (error) {
    console.error("Erro ao carregar movimentações:", error);
    showError("Erro ao carregar movimentações");
  } finally {
    LoadingManager.hide(tableBody);
  }
}

function renderMovementsTable(movements) {
  const tableBody = document.getElementById('movementsTableBody');
  if (!tableBody) return;

  const fragment = document.createDocumentFragment();

  movements.forEach(mov => {
    const produto = state.produtos.find(p => p.id === mov.produtoId);
    if (!produto) return;

    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${formatDate(mov.dataHora)}</td>
      <td>${produto.codigo} - ${produto.descricao}</td>
      <td><span class="movement-type type-${mov.tipo.toLowerCase()}">${mov.tipo}</span></td>
      <td class="text-right">${formatters.number.format(mov.quantidade)} ${produto.unidade}</td>
      <td class="text-right">${mov.valorUnitario ? formatters.currency.format(mov.valorUnitario) : '-'}</td>
      <td>${mov.tipoDocumento} ${mov.numeroDocumento}</td>
      <td>${mov.observacoes || ''}</td>
      <td>${mov.tes || ''}</td>
    `;
    fragment.appendChild(row);
  });

  if (movements.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = '<td colspan="8" class="text-center">Nenhuma movimentação encontrada</td>';
    fragment.appendChild(row);
  }

  tableBody.innerHTML = '';
  tableBody.appendChild(fragment);
}

function openMovementModal(type) {
  const modal = document.getElementById('movementModal');
  if (!modal) return;

  document.getElementById('movementType').value = type;
  document.getElementById('modalTitle').textContent =
    type === 'entrada' ? 'Nova Entrada de Estoque' : 'Nova Saída de Estoque';

  // Resetar formulário
  const form = document.getElementById('movementForm');
  if (form) form.reset();

  modal.style.display = 'block';
  updateTESOptions();
}

function closeModal() {
  const modal = document.getElementById('movementModal');
  if (modal) {
    modal.style.display = 'none';
    const form = document.getElementById('movementForm');
    if (form) form.reset();
  }
}

function updateCalculations() {
  // Detectar se está sendo chamado do modal ou formulário principal
  const modal = document.getElementById('movementModal');
  const isModal = modal && modal.style.display === 'block';

  // Definir prefixo baseado no contexto
  const prefix = isModal ? 'modal' : '';

  // Buscar elementos com o prefixo correto
  const produtoId = document.getElementById(`${prefix}${prefix ? 'P' : 'p'}roductSelect`)?.value ||
                   document.getElementById('productSelect')?.value;
  const type = document.getElementById(`${prefix}${prefix ? 'M' : 'm'}ovementType`)?.value ||
              document.getElementById('movementType')?.value;
  const quantidade = parseFloat(document.getElementById(`${prefix}${prefix ? 'Q' : 'q'}uantity`)?.value ||
                               document.getElementById('quantity')?.value) || 0;
  const valorUnitario = parseFloat(document.getElementById(`${prefix}${prefix ? 'V' : 'v'}alorUnitario`)?.value ||
                                  document.getElementById('valorUnitario')?.value) || 0;

  if (!produtoId) return;

  const produto = state.produtos.find(p => p.id === produtoId);
  if (!produto) return;

  // Buscar saldo atual
  const armazemId = type === 'entrada'
    ? (document.getElementById(`${prefix}${prefix ? 'W' : 'w'}arehouseDestiny`)?.value ||
       document.getElementById('warehouseDestiny')?.value)
    : (document.getElementById(`${prefix}${prefix ? 'W' : 'w'}arehouseOrigin`)?.value ||
       document.getElementById('warehouseOrigin')?.value);

  const estoque = state.estoques.find(e =>
    e.produtoId === produtoId && e.armazemId === armazemId
  ) || { saldo: 0 };

  const saldoFuturo = type === 'entrada'
    ? estoque.saldo + quantidade
    : estoque.saldo - quantidade;

  // Atualizar interface com o prefixo correto
  const currentStock = document.getElementById(`${prefix}${prefix ? 'C' : 'c'}urrentStock`) ||
                      document.getElementById('currentStock');
  const futureStock = document.getElementById(`${prefix}${prefix ? 'F' : 'f'}utureStock`) ||
                     document.getElementById('futureStock');
  const totalValue = document.getElementById(`${prefix}${prefix ? 'T' : 't'}otalValue`) ||
                    document.getElementById('totalValue');

  if (currentStock) {
    currentStock.textContent = `${formatters.number.format(estoque.saldo)} ${produto.unidade}`;
  }

  if (futureStock) {
    futureStock.textContent = `${formatters.number.format(saldoFuturo)} ${produto.unidade}`;
    futureStock.style.color = (type === 'saida' && saldoFuturo < 0) ? 'red' : '';
  }

  if (totalValue) {
    totalValue.textContent = formatters.currency.format(quantidade * valorUnitario);
  }
}

function updateTESOptions() {
  // Detectar se está sendo chamado do modal ou formulário principal
  const modal = document.getElementById('movementModal');
  const isModal = modal && modal.style.display === 'block';

  // Buscar elementos com o prefixo correto
  const docType = document.getElementById(isModal ? 'modalDocumentType' : 'documentType')?.value ||
                 document.getElementById('documentType')?.value;
  const tesSelect = document.getElementById(isModal ? 'modalTes' : 'tes') ||
                   document.getElementById('tes');
  if (!tesSelect) return;

  const tesOptions = {
    'COMPRA': [
      { value: '001', text: '001 - Compra para estoque' },
      { value: '002', text: '002 - Compra para consumo' },
      { value: '003', text: '003 - Devolução de venda' }
    ],
    'VENDA': [
      { value: '500', text: '500 - Venda de produto' },
      { value: '501', text: '501 - Remessa para beneficiamento' },
      { value: '502', text: '502 - Devolução de compra' }
    ],
    'PRODUCAO': [
      { value: '200', text: '200 - Entrada por produção' },
      { value: '201', text: '201 - Consumo de componentes' },
      { value: '202', text: '202 - Baixa por perda' }
    ],
    'CONSUMO': [
      { value: '400', text: '400 - Requisição para consumo' },
      { value: '401', text: '401 - Requisição para manutenção' },
      { value: '402', text: '402 - Requisição para projeto' }
    ],
    'AJUSTE': [
      { value: '900', text: '900 - Ajuste de inventário entrada' },
      { value: '901', text: '901 - Ajuste de inventário saída' },
      { value: '902', text: '902 - Ajuste por divergência' }
    ]
  };

  const fragment = document.createDocumentFragment();
  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = 'Selecione o TES...';
  fragment.appendChild(defaultOption);

  const options = tesOptions[docType] || [];
  options.forEach(opt => {
    const option = document.createElement('option');
    option.value = opt.value;
    option.textContent = opt.text;
    fragment.appendChild(option);
  });

  tesSelect.innerHTML = '';
  tesSelect.appendChild(fragment);
}

async function handleMovement(event) {
  event.preventDefault();

  const formData = new FormData(event.target);
  const movementData = Object.fromEntries(formData.entries());

  try {
    // Validações
    if (!movementData.productSelect || !movementData.quantity) {
      throw new Error('Dados obrigatórios não preenchidos');
    }

    // Processar movimentação
    const movimentacao = {
      produtoId: movementData.productSelect,
      tipo: movementData.movementType === 'entrada' ? 'ENTRADA' : 'SAIDA',
      quantidade: parseFloat(movementData.quantity),
      valorUnitario: parseFloat(movementData.valorUnitario),
      tipoDocumento: movementData.documentType,
      numeroDocumento: movementData.documentNumber,
      observacoes: movementData.observations,
      dataHora: Timestamp.now(),
      tes: movementData.tes,
      armazemOrigem: movementData.warehouseOrigin,
      armazemDestino: movementData.warehouseDestiny
    };

    await addDoc(collection(db, "movimentacoesEstoque"), movimentacao);

    // Atualizar estoque
    await updateStock(movimentacao);

    showSuccess('Movimentação registrada com sucesso!');
    closeModal();

  } catch (error) {
    console.error("Erro ao registrar movimentação:", error);
    showError("Erro ao registrar movimentação: " + error.message);
  }
}

async function updateStock(movimentacao) {
  const armazemId = movimentacao.tipo === 'ENTRADA'
    ? movimentacao.armazemDestino
    : movimentacao.armazemOrigem;

  const estoque = state.estoques.find(e =>
    e.produtoId === movimentacao.produtoId && e.armazemId === armazemId
  );

  const novoSaldo = estoque
    ? estoque.saldo + (movimentacao.tipo === 'ENTRADA' ? movimentacao.quantidade : -movimentacao.quantidade)
    : movimentacao.quantidade;

  if (estoque) {
    await updateDoc(doc(db, "estoques", estoque.id), {
      saldo: novoSaldo,
      ultimaMovimentacao: Timestamp.now()
    });
  } else {
    await addDoc(collection(db, "estoques"), {
      produtoId: movimentacao.produtoId,
      armazemId,
      saldo: novoSaldo,
      ultimaMovimentacao: Timestamp.now()
    });
  }
}

function showSuccess(message) {
  const toast = document.createElement('div');
  toast.className = 'toast success';
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4caf50;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 3000);
}

// Funções de importação/exportação (implementação básica)
async function exportToExcel() {
  try {
    const stockData = state.produtos.map(produto => {
      const estoque = state.estoques.find(e => e.produtoId === produto.id) || { saldo: 0 };
      return { 'Código': produto.codigo, 'Saldo': estoque.saldo };
    });

    // Usar biblioteca XLSX se disponível
    if (typeof XLSX !== 'undefined') {
      const worksheet = XLSX.utils.json_to_sheet(stockData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Estoques");

      const fileName = `Estoques_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      showSuccess('Exportação concluída com sucesso!');
    } else {
      throw new Error('Biblioteca XLSX não carregada');
    }
  } catch (error) {
    console.error('Erro na exportação:', error);
    showError('Erro na exportação: ' + error.message);
  }
}

async function handleFileSelect(event) {
  const file = event.target.files[0];
  if (!file) return;

  try {
    showSuccess('Processando arquivo...');

    // Implementação básica - expandir conforme necessário
    const data = await readExcelFile(file);

    // Processar dados...
    showSuccess('Importação concluída!');

  } catch (error) {
    console.error('Erro na importação:', error);
    showError('Erro na importação: ' + error.message);
  }

  event.target.value = '';
}

function readExcelFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => {
      try {
        if (typeof XLSX !== 'undefined') {
          const data = e.target.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const json = XLSX.utils.sheet_to_json(worksheet);
          resolve(json);
        } else {
          reject(new Error('Biblioteca XLSX não carregada'));
        }
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = reject;
    reader.readAsBinaryString(file);
  });
}

// Auto-inicialização quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeEstoques);
} else {
  initializeEstoques();
}
