<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório - Materiais em Produção</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .filters-section {
            background: #f8f9fa;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .actions-row {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.blue { border-left-color: #3498db; }
        .stat-card.green { border-left-color: #27ae60; }
        .stat-card.orange { border-left-color: #f39c12; }
        .stat-card.red { border-left-color: #e74c3c; }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 500;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            vertical-align: top;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .empenhos-list {
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }

        .empenho-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 6px;
        }

        .empenho-item:last-child {
            margin-bottom: 0;
        }

        .empenho-op {
            font-weight: 600;
            color: #2c3e50;
        }

        .empenho-qtd {
            color: #27ae60;
            font-weight: 600;
        }

        .empenho-data {
            color: #6c757d;
            font-size: 11px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .no-data-icon {
            font-size: 48px;
            opacity: 0.5;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .table-container {
                overflow-x: auto;
            }

            .table th,
            .table td {
                padding: 10px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🏭 Relatório de Materiais em Produção
            </h1>
            <div style="text-align: right;">
                <div style="font-size: 14px; opacity: 0.9;">
                    📅 <span id="currentDate"></span>
                </div>
                <div style="font-size: 12px; opacity: 0.7;">
                    🕒 Atualizado em tempo real
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Filtros -->
            <div class="filters-section">
                <h3 style="color: #2c3e50; margin-bottom: 20px; font-size: 18px;">🔍 Filtros</h3>
                
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>Armazém de Produção:</label>
                        <select id="filterArmazem">
                            <option value="">Todos os armazéns</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Tipo de Material:</label>
                        <select id="filterTipo">
                            <option value="">Todos os tipos</option>
                            <option value="MP">MP - Matéria Prima</option>
                            <option value="SP">SP - Semi-acabado</option>
                            <option value="PA">PA - Produto Acabado</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Status do Empenho:</label>
                        <select id="filterStatus">
                            <option value="">Todos os status</option>
                            <option value="COM_EMPENHO">Com empenho</option>
                            <option value="SEM_EMPENHO">Sem empenho</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Buscar Material:</label>
                        <input type="text" id="filterBusca" placeholder="Código ou descrição...">
                    </div>
                </div>
                
                <div class="actions-row">
                    <!-- AÇÕES PRINCIPAIS -->
                    <button class="btn btn-primary" onclick="aplicarFiltros()">
                        🔍 Aplicar Filtros
                    </button>
                    <button class="btn btn-success" onclick="exportarRelatorio()">
                        📊 Exportar Excel
                    </button>

                    <!-- MENU DE FERRAMENTAS -->
                    <div style="position: relative; display: inline-block;">
                        <button class="btn" onclick="toggleFerramentasMenu()" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white;">
                            🔧 Ferramentas ▼
                        </button>
                        <div id="ferramentasMenu" style="display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; min-width: 250px; margin-top: 5px;">
                            <div style="padding: 10px 0;">
                                <div style="padding: 8px 16px; font-weight: bold; color: #666; border-bottom: 1px solid #eee; font-size: 12px;">CORREÇÕES AUTOMÁTICAS</div>
                                <button onclick="corrigirEmpenhos(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔗 Criar Empenhos Faltantes
                                </button>
                                <button onclick="ajustarSaldosNegativos(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    ✅ Ajustar Saldos Negativos
                                </button>
                                <button onclick="limpezaOPsCanceladas(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🧹 Limpeza OPs Canceladas
                                </button>

                                <div style="padding: 8px 16px; font-weight: bold; color: #666; border-bottom: 1px solid #eee; border-top: 1px solid #eee; font-size: 12px; margin-top: 5px;">AÇÕES ESPECÍFICAS</div>
                                <button onclick="liberarEmpenhoOP(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔓 Liberar Empenho de OP
                                </button>
                                <button onclick="concentrarMaterialOP(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🎯 Concentrar Material
                                </button>

                                <div style="padding: 8px 16px; font-weight: bold; color: #666; border-bottom: 1px solid #eee; border-top: 1px solid #eee; font-size: 12px; margin-top: 5px;">RELATÓRIOS</div>
                                <button onclick="diagnosticarSaldosReais(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔍 Ver Saldos Reais
                                </button>
                                <button onclick="validarJornadaOP(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔍 Validar Jornada OP
                                </button>
                                <button onclick="relatorioStatusOPs(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    📋 Status de Todas OPs
                                </button>

                                <div style="padding: 8px 16px; font-weight: bold; color: #28a745; border-top: 1px solid #eee; font-size: 12px; margin-top: 5px;">🔍 DIAGNÓSTICO</div>
                                <button onclick="diagnosticarInconsistenciaEmpenhos(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #28a745;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔍 Diagnosticar Empenhos
                                </button>
                                <button onclick="reprocessarEmpenhosInconsistentes(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #28a745;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔧 Reprocessar Empenhos
                                </button>
                                <button onclick="testarOP25070888_MPS009(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #e67e22;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🎯 Testar OP25070888
                                </button>
                                <button onclick="analisarProblemaImpressao(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #8e44ad;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🖨️ Analisar Problema Impressão
                                </button>
                                <button onclick="corrigirProblemaImpressao(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #27ae60;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔧 Corrigir Problema Impressão
                                </button>
                                <button onclick="testarCorrigirOP25070887(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #e74c3c;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🎯 Testar/Corrigir OP25070887
                                </button>
                                <button onclick="investigarOP25070871(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #9b59b6;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔍 Investigar OP25070871
                                </button>
                                <button onclick="corrigirOP25070871(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #27ae60;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔧 Corrigir OP25070871
                                </button>
                                <button onclick="corrigirNecessidadeOP25070871(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #e67e22;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🎯 Corrigir Necessidade OP25070871
                                </button>
                                <button onclick="investigarEstoqueOP25070871(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #3498db;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    📊 Investigar Estoque OP25070871
                                </button>
                                <button onclick="investigarReserva01(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #f39c12;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🔍 Investigar Reserva 0.1
                                </button>

                                <div style="padding: 8px 16px; font-weight: bold; color: #dc3545; border-top: 1px solid #eee; font-size: 12px; margin-top: 5px;">⚠️ EMERGÊNCIA</div>
                                <button onclick="emergenciaZerarSaldosEmpenhados(); toggleFerramentasMenu();" style="width: 100%; text-align: left; padding: 10px 16px; border: none; background: none; cursor: pointer; font-size: 14px; color: #dc3545;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='none'">
                                    🚨 Zerar Todos Empenhos
                                </button>
                            </div>
                        </div>
                    </div>

                    <button class="btn" onclick="mostrarAjudaCorrecoes()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;">
                        ❓ Ajuda
                    </button>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card blue">
                    <div class="stat-value" id="totalMateriais">-</div>
                    <div class="stat-label">Total de Materiais</div>
                </div>
                
                <div class="stat-card green">
                    <div class="stat-value" id="materiaisEmpenhados">-</div>
                    <div class="stat-label">Materiais Empenhados</div>
                </div>
                
                <div class="stat-card orange">
                    <div class="stat-value" id="opsAtivas">-</div>
                    <div class="stat-label">OPs Ativas</div>
                </div>
                
                <div class="stat-card red">
                    <div class="stat-value" id="valorTotal">-</div>
                    <div class="stat-label">Valor Total (R$)</div>
                </div>
            </div>

            <!-- Tabela de Materiais -->
            <div class="table-container">
                <div id="loadingIndicator" class="loading">
                    <div class="loading-spinner"></div>
                    <div>Carregando dados...</div>
                </div>
                
                <div id="tableContent" style="display: none;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Tipo</th>
                                <th>Armazém</th>
                                <th>Saldo Atual</th>
                                <th>Saldo Empenhado</th>
                                <th>Saldo Disponível</th>
                                <th>Status</th>
                                <th>Empenhos Ativos</th>
                            </tr>
                        </thead>
                        <tbody id="materiaisTableBody">
                        </tbody>
                    </table>
                </div>
                
                <div id="noDataIndicator" class="no-data" style="display: none;">
                    <div class="no-data-icon">📦</div>
                    <div><strong>Nenhum material encontrado</strong></div>
                    <div>Ajuste os filtros para ver os resultados</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            query,
            where,
            orderBy,
            Timestamp,
            runTransaction
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let materiais = [];
        let armazens = [];
        let produtos = [];
        let estoques = [];
        let empenhos = [];
        let ordensProducao = [];
        let materiaisFiltrados = [];

        // Inicializar página
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentDate').textContent = new Date().toLocaleDateString('pt-BR');
            carregarDados();
        });

        // Carregar todos os dados necessários
        async function carregarDados() {
            try {
                console.log('🔄 Carregando dados...');

                const [
                    armazensSnap,
                    produtosSnap,
                    estoquesSnap,
                    empenhosSnap,
                    opsSnap
                ] = await Promise.all([
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`✅ Dados carregados: ${armazens.length} armazéns, ${produtos.length} produtos, ${estoques.length} estoques, ${empenhos.length} empenhos`);

                popularFiltros();
                processarMateriais();

            } catch (error) {
                console.error('❌ Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Verifique a conexão e tente novamente.');
            }
        }

        // Popular filtros com dados carregados
        function popularFiltros() {
            // Filtro de armazéns de produção
            const filterArmazem = document.getElementById('filterArmazem');
            const armazensProducao = armazens.filter(a => a.tipo === 'PRODUCAO' || a.tipo === 'PRODUÇÃO');

            armazensProducao.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.nome}`;
                filterArmazem.appendChild(option);
            });

            console.log(`📋 Filtros populados: ${armazensProducao.length} armazéns de produção`);
        }

        // Processar materiais e criar estrutura do relatório
        function processarMateriais() {
            console.log('🔄 Processando materiais...');

            materiais = [];

            // Filtrar apenas estoques de armazéns de produção
            const armazensProducaoIds = armazens
                .filter(a => a.tipo === 'PRODUCAO' || a.tipo === 'PRODUÇÃO')
                .map(a => a.id);

            const estoquesProducao = estoques.filter(e =>
                armazensProducaoIds.includes(e.armazemId) &&
                (e.saldo > 0 || e.saldoEmpenhado > 0)
            );

            console.log(`📦 Estoques em produção: ${estoquesProducao.length}`);

            estoquesProducao.forEach(estoque => {
                const produto = produtos.find(p => p.id === estoque.produtoId);
                const armazem = armazens.find(a => a.id === estoque.armazemId);

                if (!produto || !armazem) return;

                // Buscar empenhos ativos para este material
                const empenhosAtivos = empenhos.filter(e =>
                    e.produtoId === estoque.produtoId &&
                    e.armazemId === estoque.armazemId &&
                    e.status === 'ATIVO'
                );

                // Calcular totais
                const saldoTotal = estoque.saldo || 0;
                const saldoEmpenhado = estoque.saldoEmpenhado || 0;
                const saldoDisponivel = saldoTotal - saldoEmpenhado;

                // Criar lista de empenhos com detalhes das OPs
                const empenhosDetalhados = empenhosAtivos.map(empenho => {
                    const op = ordensProducao.find(o => o.id === empenho.ordemProducaoId);
                    return {
                        ...empenho,
                        opNumero: op?.numero || 'N/A',
                        opStatus: op?.status || 'N/A',
                        opProdutoFinal: op?.produtoFinal || 'N/A'
                    };
                });

                // Determinar status
                let status = 'SEM_EMPENHO';
                let statusLabel = 'Sem empenho';
                let statusClass = 'badge-info';

                if (saldoEmpenhado > 0) {
                    if (saldoEmpenhado >= saldoTotal) {
                        status = 'TOTALMENTE_EMPENHADO';
                        statusLabel = 'Totalmente empenhado';
                        statusClass = 'badge-danger';
                    } else {
                        status = 'PARCIALMENTE_EMPENHADO';
                        statusLabel = 'Parcialmente empenhado';
                        statusClass = 'badge-warning';
                    }
                }

                if (saldoTotal > 0 && saldoEmpenhado === 0) {
                    status = 'DISPONIVEL';
                    statusLabel = 'Disponível';
                    statusClass = 'badge-success';
                }

                materiais.push({
                    estoqueId: estoque.id, // Adicionar ID do estoque
                    produtoId: produto.id,
                    codigo: produto.codigo,
                    descricao: produto.descricao,
                    tipo: produto.tipo,
                    unidade: produto.unidade || 'UN',
                    armazemId: armazem.id,
                    armazemCodigo: armazem.codigo,
                    armazemNome: armazem.nome,
                    saldoTotal,
                    saldoEmpenhado,
                    saldoDisponivel,
                    status,
                    statusLabel,
                    statusClass,
                    empenhosAtivos: empenhosDetalhados,
                    valorUnitario: produto.custoMedio || 0,
                    valorTotal: saldoTotal * (produto.custoMedio || 0)
                });
            });

            console.log(`✅ Processados ${materiais.length} materiais`);

            materiaisFiltrados = [...materiais];
            atualizarEstatisticas();
            renderizarTabela();
        }

        // Aplicar filtros
        window.aplicarFiltros = function() {
            const filterArmazem = document.getElementById('filterArmazem').value;
            const filterTipo = document.getElementById('filterTipo').value;
            const filterStatus = document.getElementById('filterStatus').value;
            const filterBusca = document.getElementById('filterBusca').value.toLowerCase().trim();

            materiaisFiltrados = materiais.filter(material => {
                // Filtro por armazém
                if (filterArmazem && material.armazemId !== filterArmazem) {
                    return false;
                }

                // Filtro por tipo
                if (filterTipo && material.tipo !== filterTipo) {
                    return false;
                }

                // Filtro por status de empenho
                if (filterStatus) {
                    if (filterStatus === 'COM_EMPENHO' && material.saldoEmpenhado <= 0) {
                        return false;
                    }
                    if (filterStatus === 'SEM_EMPENHO' && material.saldoEmpenhado > 0) {
                        return false;
                    }
                }

                // Filtro por busca
                if (filterBusca) {
                    const searchText = `${material.codigo} ${material.descricao}`.toLowerCase();
                    if (!searchText.includes(filterBusca)) {
                        return false;
                    }
                }

                return true;
            });

            console.log(`🔍 Filtros aplicados: ${materiaisFiltrados.length} de ${materiais.length} materiais`);

            atualizarEstatisticas();
            renderizarTabela();
        };

        // Atualizar estatísticas
        function atualizarEstatisticas() {
            const totalMateriais = materiaisFiltrados.length;
            const materiaisEmpenhados = materiaisFiltrados.filter(m => m.saldoEmpenhado > 0).length;

            // 🔧 PADRONIZAÇÃO: Contar OPs ativas igual ao apontamentos_simplificado
            // OPs ativas = todas que não estão concluídas ou canceladas
            const opsAtivas = ordensProducao.filter(op =>
                op.status !== 'Concluída' &&
                op.status !== 'Cancelada' &&
                op.status !== 'concluida' &&
                op.status !== 'cancelada' &&
                op.status !== 'CONCLUÍDA' &&
                op.status !== 'CANCELADA'
            );

            const valorTotal = materiaisFiltrados.reduce((sum, m) => sum + m.valorTotal, 0);

            document.getElementById('totalMateriais').textContent = totalMateriais.toLocaleString('pt-BR');
            document.getElementById('materiaisEmpenhados').textContent = materiaisEmpenhados.toLocaleString('pt-BR');
            document.getElementById('opsAtivas').textContent = opsAtivas.length.toLocaleString('pt-BR');
            document.getElementById('valorTotal').textContent = valorTotal.toLocaleString('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            });
        }

        // Renderizar tabela
        function renderizarTabela() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const tableContent = document.getElementById('tableContent');
            const noDataIndicator = document.getElementById('noDataIndicator');
            const tbody = document.getElementById('materiaisTableBody');

            // Mostrar loading
            loadingIndicator.style.display = 'block';
            tableContent.style.display = 'none';
            noDataIndicator.style.display = 'none';

            // Simular delay para mostrar loading
            setTimeout(() => {
                tbody.innerHTML = '';

                if (materiaisFiltrados.length === 0) {
                    loadingIndicator.style.display = 'none';
                    noDataIndicator.style.display = 'block';
                    return;
                }

                materiaisFiltrados.forEach(material => {
                    const row = document.createElement('tr');

                    // Criar lista de empenhos
                    let empenhosHtml = '';
                    if (material.empenhosAtivos.length > 0) {
                        empenhosHtml = `
                            <div class="empenhos-list">
                                ${material.empenhosAtivos.map(empenho => `
                                    <div class="empenho-item">
                                        <div class="empenho-op">OP: ${empenho.opNumero}</div>
                                        <div class="empenho-qtd">Qtd: ${empenho.quantidadeEmpenhada?.toFixed(3) || '0'} ${material.unidade}</div>
                                        <div class="empenho-data">
                                            ${empenho.dataEmpenho?.toDate ?
                                                empenho.dataEmpenho.toDate().toLocaleDateString('pt-BR') :
                                                'Data não disponível'
                                            }
                                        </div>
                                        <div style="color: #6c757d; font-size: 10px;">
                                            Status OP: ${empenho.opStatus}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                    } else {
                        empenhosHtml = '<div style="text-align: center; color: #6c757d; font-style: italic;">Nenhum empenho ativo</div>';
                    }

                    row.innerHTML = `
                        <td style="font-weight: 600; color: #2c3e50;">${material.codigo}</td>
                        <td title="${material.descricao}">${material.descricao}</td>
                        <td>
                            <span class="badge badge-info">${material.tipo}</span>
                        </td>
                        <td>${material.armazemCodigo} - ${material.armazemNome}</td>
                        <td style="text-align: right; font-weight: 600;">
                            ${material.saldoTotal.toFixed(3)} ${material.unidade}
                        </td>
                        <td style="text-align: right; font-weight: 600; color: #e74c3c;">
                            ${material.saldoEmpenhado.toFixed(3)} ${material.unidade}
                        </td>
                        <td style="text-align: right; font-weight: 600; color: #27ae60;">
                            ${material.saldoDisponivel.toFixed(3)} ${material.unidade}
                        </td>
                        <td>
                            <span class="badge ${material.statusClass}">${material.statusLabel}</span>
                        </td>
                        <td style="max-width: 300px;">
                            ${empenhosHtml}
                        </td>
                    `;

                    tbody.appendChild(row);
                });

                loadingIndicator.style.display = 'none';
                tableContent.style.display = 'block';

                console.log(`✅ Tabela renderizada com ${materiaisFiltrados.length} materiais`);
            }, 500);
        }

        // Exportar relatório para Excel
        window.exportarRelatorio = function() {
            if (materiaisFiltrados.length === 0) {
                alert('❌ Nenhum material para exportar. Ajuste os filtros e tente novamente.');
                return;
            }

            console.log('📊 Iniciando exportação...');

            // Preparar dados para exportação
            const dadosExportacao = [];

            materiaisFiltrados.forEach(material => {
                if (material.empenhosAtivos.length > 0) {
                    // Uma linha para cada empenho
                    material.empenhosAtivos.forEach(empenho => {
                        dadosExportacao.push({
                            'Código': material.codigo,
                            'Descrição': material.descricao,
                            'Tipo': material.tipo,
                            'Unidade': material.unidade,
                            'Armazém': `${material.armazemCodigo} - ${material.armazemNome}`,
                            'Saldo Total': material.saldoTotal.toFixed(3),
                            'Saldo Empenhado': material.saldoEmpenhado.toFixed(3),
                            'Saldo Disponível': material.saldoDisponivel.toFixed(3),
                            'Status': material.statusLabel,
                            'OP Empenhada': empenho.opNumero,
                            'Status OP': empenho.opStatus,
                            'Qtd Empenhada': empenho.quantidadeEmpenhada?.toFixed(3) || '0',
                            'Data Empenho': empenho.dataEmpenho?.toDate ?
                                empenho.dataEmpenho.toDate().toLocaleDateString('pt-BR') :
                                'N/A',
                            'Produto Final OP': empenho.opProdutoFinal,
                            'Valor Unitário': material.valorUnitario.toFixed(2),
                            'Valor Total': material.valorTotal.toFixed(2)
                        });
                    });
                } else {
                    // Material sem empenho
                    dadosExportacao.push({
                        'Código': material.codigo,
                        'Descrição': material.descricao,
                        'Tipo': material.tipo,
                        'Unidade': material.unidade,
                        'Armazém': `${material.armazemCodigo} - ${material.armazemNome}`,
                        'Saldo Total': material.saldoTotal.toFixed(3),
                        'Saldo Empenhado': material.saldoEmpenhado.toFixed(3),
                        'Saldo Disponível': material.saldoDisponivel.toFixed(3),
                        'Status': material.statusLabel,
                        'OP Empenhada': 'N/A',
                        'Status OP': 'N/A',
                        'Qtd Empenhada': '0',
                        'Data Empenho': 'N/A',
                        'Produto Final OP': 'N/A',
                        'Valor Unitário': material.valorUnitario.toFixed(2),
                        'Valor Total': material.valorTotal.toFixed(2)
                    });
                }
            });

            // Converter para CSV
            const headers = Object.keys(dadosExportacao[0]);
            const csvContent = [
                headers.join(','),
                ...dadosExportacao.map(row =>
                    headers.map(header => `"${row[header]}"`).join(',')
                )
            ].join('\n');

            // Adicionar BOM para UTF-8
            const BOM = '\uFEFF';
            const csvWithBOM = BOM + csvContent;

            // Download do arquivo
            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            const dataAtual = new Date().toISOString().split('T')[0];
            const nomeArquivo = `relatorio_materiais_producao_${dataAtual}.csv`;

            link.setAttribute('href', url);
            link.setAttribute('download', nomeArquivo);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log(`✅ Exportação concluída: ${dadosExportacao.length} registros`);
            alert(`✅ Relatório exportado com sucesso!\n\n📊 ${dadosExportacao.length} registros exportados\n📁 Arquivo: ${nomeArquivo}\n\n💡 Dica: Abra o arquivo no Excel para melhor visualização`);
        };

        // Corrigir empenhos automáticos
        window.corrigirEmpenhos = async function() {
            if (!confirm('🔧 Esta função vai criar empenhos automáticos para materiais disponíveis em produção que são necessários para OPs ativas.\n\nDeseja continuar?')) {
                return;
            }

            try {
                console.log('🔧 Iniciando correção de empenhos...');

                // Buscar OPs ativas que precisam de materiais
                const opsAtivas = ordensProducao.filter(op =>
                    ['Pendente', 'Em Produção', 'Firme'].includes(op.status) &&
                    op.materiaisNecessarios &&
                    op.materiaisNecessarios.length > 0
                );

                console.log(`📋 Encontradas ${opsAtivas.length} OPs ativas`);

                let empenhosCorrigidos = 0;
                const relatorioCorrecoes = [];

                for (const op of opsAtivas) {
                    console.log(`🔍 Analisando OP ${op.numero}...`);

                    for (const materialNecessario of op.materiaisNecessarios) {
                        // Verificar se já existe empenho ativo
                        const empenhoExistente = empenhos.find(e =>
                            e.ordemProducaoId === op.id &&
                            e.produtoId === materialNecessario.produtoId &&
                            e.status === 'ATIVO'
                        );

                        if (empenhoExistente) {
                            continue; // Já tem empenho
                        }

                        // Buscar material disponível no armazém de produção
                        const materialDisponivel = materiais.find(m =>
                            m.produtoId === materialNecessario.produtoId &&
                            m.armazemId === op.armazemProducaoId &&
                            m.saldoDisponivel > 0
                        );

                        if (!materialDisponivel) {
                            continue; // Não tem material disponível
                        }

                        // Calcular quantidade a empenhar
                        const necessidade = materialNecessario.necessidade || materialNecessario.quantidade || 0;
                        const quantidadeEmpenhar = Math.min(necessidade, materialDisponivel.saldoDisponivel);

                        if (quantidadeEmpenhar <= 0) {
                            continue;
                        }

                        console.log(`✅ Criando empenho: OP ${op.numero} - Material ${materialDisponivel.codigo} - Qtd: ${quantidadeEmpenhar}`);

                        // Criar empenho
                        await addDoc(collection(db, "empenhos"), {
                            ordemProducaoId: op.id,
                            produtoId: materialNecessario.produtoId,
                            armazemId: op.armazemProducaoId,
                            quantidadeEmpenhada: quantidadeEmpenhar,
                            quantidadeConsumida: 0,
                            status: 'ATIVO',
                            dataEmpenho: Timestamp.now(),
                            origemReserva: false,
                            correcaoAutomatica: true,
                            observacoes: `Empenho criado automaticamente via correção - ${new Date().toLocaleString('pt-BR')}`
                        });

                        // Atualizar estoque
                        if (materialDisponivel.estoqueId) {
                            const estoqueRef = doc(db, "estoques", materialDisponivel.estoqueId);
                            await updateDoc(estoqueRef, {
                                saldoEmpenhado: (materialDisponivel.saldoEmpenhado || 0) + quantidadeEmpenhar,
                                ultimaMovimentacao: Timestamp.now()
                            });
                        }

                        empenhosCorrigidos++;
                        relatorioCorrecoes.push({
                            op: op.numero,
                            material: materialDisponivel.codigo,
                            quantidade: quantidadeEmpenhar,
                            armazem: materialDisponivel.armazemCodigo
                        });
                    }
                }

                if (empenhosCorrigidos > 0) {
                    // Recarregar dados
                    await carregarDados();

                    const relatorio = relatorioCorrecoes.map(r =>
                        `• OP ${r.op}: ${r.material} - ${r.quantidade.toFixed(3)} (${r.armazem})`
                    ).join('\n');

                    alert(`✅ Correção concluída!\n\n📊 ${empenhosCorrigidos} empenhos criados:\n\n${relatorio}\n\n🔄 Dados atualizados automaticamente.`);
                } else {
                    alert('ℹ️ Nenhuma correção necessária.\n\nTodos os materiais já estão corretamente empenhados ou não há materiais disponíveis.');
                }

            } catch (error) {
                console.error('❌ Erro na correção de empenhos:', error);
                alert('❌ Erro na correção de empenhos:\n\n' + error.message);
            }
        };

        // RESET COMPLETO dos saldos empenhados
        window.resetarSaldosEmpenhados = async function() {
            if (!confirm('🔄 RESET COMPLETO DOS SALDOS EMPENHADOS\n\nEsta função vai:\n1. ZERAR todos os saldos empenhados\n2. Recalcular baseado nos empenhos ativos reais\n\n⚠️ Esta é uma operação de correção total!\n\nDeseja continuar?')) {
                return;
            }

            try {
                console.log('🔄 Iniciando RESET completo dos saldos empenhados...');

                // PASSO 1: ZERAR todos os saldos empenhados
                console.log('📋 PASSO 1: Zerando todos os saldos empenhados...');
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                let estoquesZerados = 0;

                for (const estoqueDoc of estoquesSnapshot.docs) {
                    const estoque = estoqueDoc.data();
                    if ((estoque.saldoEmpenhado || 0) !== 0) {
                        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: 0,
                            resetTimestamp: Timestamp.now()
                        });
                        estoquesZerados++;
                    }
                }

                console.log(`✅ ${estoquesZerados} estoques zerados`);

                // PASSO 2: Buscar todos os empenhos ativos
                console.log('📋 PASSO 2: Buscando empenhos ativos...');
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("status", "==", "ATIVO")
                );
                const empenhosSnapshot = await getDocs(empenhosQuery);
                const empenhosAtivos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                console.log(`📊 Encontrados ${empenhosAtivos.length} empenhos ativos`);

                // PASSO 3: Agrupar empenhos por produto e armazém
                console.log('📋 PASSO 3: Agrupando empenhos...');
                const empenhosAgrupados = new Map();

                empenhosAtivos.forEach(empenho => {
                    const chave = `${empenho.produtoId}_${empenho.armazemId}`;
                    if (!empenhosAgrupados.has(chave)) {
                        empenhosAgrupados.set(chave, {
                            produtoId: empenho.produtoId,
                            armazemId: empenho.armazemId,
                            totalEmpenhado: 0,
                            empenhos: []
                        });
                    }

                    const grupo = empenhosAgrupados.get(chave);
                    const saldoEmpenho = (empenho.quantidadeEmpenhada || 0) - (empenho.quantidadeConsumida || 0);
                    grupo.totalEmpenhado += saldoEmpenho;
                    grupo.empenhos.push({
                        id: empenho.id,
                        quantidade: saldoEmpenho
                    });
                });

                console.log(`📊 Agrupados em ${empenhosAgrupados.size} combinações produto/armazém`);

                // PASSO 4: Aplicar os saldos corretos
                console.log('📋 PASSO 4: Aplicando saldos corretos...');
                let estoquesAtualizados = 0;
                const relatorioCorrecoes = [];

                for (const [chave, grupo] of empenhosAgrupados) {
                    if (grupo.totalEmpenhado > 0) {
                        // Buscar o estoque correspondente
                        const estoqueQuery = query(
                            collection(db, "estoques"),
                            where("produtoId", "==", grupo.produtoId),
                            where("armazemId", "==", grupo.armazemId)
                        );

                        const estoqueSnapshot = await getDocs(estoqueQuery);

                        if (!estoqueSnapshot.empty) {
                            const estoqueDoc = estoqueSnapshot.docs[0];

                            await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                                saldoEmpenhado: grupo.totalEmpenhado,
                                ultimaCorrecao: Timestamp.now()
                            });

                            const produto = produtos.find(p => p.id === grupo.produtoId);
                            const armazem = armazens.find(a => a.id === grupo.armazemId);

                            estoquesAtualizados++;
                            relatorioCorrecoes.push({
                                produto: produto?.codigo || grupo.produtoId,
                                armazem: armazem?.codigo || grupo.armazemId,
                                saldoEmpenhado: grupo.totalEmpenhado,
                                empenhosCount: grupo.empenhos.length
                            });

                            console.log(`✅ Estoque atualizado: ${produto?.codigo} (${armazem?.codigo}) = ${grupo.totalEmpenhado.toFixed(3)}`);
                        }
                    }
                }

                // Recarregar dados
                await carregarDados();

                const relatorio = relatorioCorrecoes.map(r =>
                    `• ${r.produto} (${r.armazem}): ${r.saldoEmpenhado.toFixed(3)} empenhado (${r.empenhosCount} empenho(s))`
                ).join('\n');

                alert(`✅ RESET COMPLETO CONCLUÍDO!\n\n📊 Resultados:\n• ${estoquesZerados} estoques zerados\n• ${estoquesAtualizados} estoques atualizados\n• ${empenhosAtivos.length} empenhos processados\n\n📋 Saldos aplicados:\n${relatorio}\n\n🔄 Dados atualizados automaticamente.`);

            } catch (error) {
                console.error('❌ Erro no reset de saldos:', error);
                alert('❌ Erro no reset de saldos:\n\n' + error.message);
            }
        };

        // Corrigir saldos empenhados incorretos (FUNÇÃO ANTIGA - MANTER PARA BACKUP)
        window.corrigirSaldosEmpenhados = async function() {
            if (!confirm('🔧 Esta função vai recalcular os saldos empenhados baseado nos empenhos ativos.\n\nDeseja continuar?')) {
                return;
            }

            try {
                console.log('🔧 Iniciando correção de saldos empenhados...');

                // Buscar todos os empenhos ativos
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("status", "==", "ATIVO")
                );
                const empenhosSnapshot = await getDocs(empenhosQuery);
                const empenhosAtivos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                console.log(`📋 Encontrados ${empenhosAtivos.length} empenhos ativos`);

                // Agrupar empenhos por produto e armazém
                const empenhosAgrupados = new Map();

                empenhosAtivos.forEach(empenho => {
                    const chave = `${empenho.produtoId}_${empenho.armazemId}`;
                    if (!empenhosAgrupados.has(chave)) {
                        empenhosAgrupados.set(chave, {
                            produtoId: empenho.produtoId,
                            armazemId: empenho.armazemId,
                            totalEmpenhado: 0
                        });
                    }

                    const grupo = empenhosAgrupados.get(chave);
                    grupo.totalEmpenhado += (empenho.quantidadeEmpenhada || 0) - (empenho.quantidadeConsumida || 0);
                });

                console.log(`📊 Agrupados em ${empenhosAgrupados.size} combinações produto/armazém`);

                let estoquesCorrigidos = 0;
                const relatorioCorrecoes = [];

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));

                for (const estoqueDoc of estoquesSnapshot.docs) {
                    const estoque = estoqueDoc.data();
                    const chave = `${estoque.produtoId}_${estoque.armazemId}`;

                    const empenhoCalculado = empenhosAgrupados.get(chave);
                    const saldoEmpenhado = empenhoCalculado ? empenhoCalculado.totalEmpenhado : 0;
                    const saldoAtual = estoque.saldoEmpenhado || 0;

                    // Verificar se precisa corrigir
                    if (Math.abs(saldoAtual - saldoEmpenhado) > 0.001) {
                        console.log(`🔧 Corrigindo estoque: ${estoque.produtoId} - ${saldoAtual} → ${saldoEmpenhado}`);

                        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: saldoEmpenhado,
                            ultimaCorrecao: Timestamp.now()
                        });

                        const produto = produtos.find(p => p.id === estoque.produtoId);
                        const armazem = armazens.find(a => a.id === estoque.armazemId);

                        estoquesCorrigidos++;
                        relatorioCorrecoes.push({
                            produto: produto?.codigo || estoque.produtoId,
                            armazem: armazem?.codigo || estoque.armazemId,
                            anterior: saldoAtual,
                            novo: saldoEmpenhado,
                            diferenca: saldoEmpenhado - saldoAtual
                        });
                    }
                }

                if (estoquesCorrigidos > 0) {
                    // Recarregar dados
                    await carregarDados();

                    const relatorio = relatorioCorrecoes.map(r =>
                        `• ${r.produto} (${r.armazem}): ${r.anterior.toFixed(3)} → ${r.novo.toFixed(3)} (${r.diferenca >= 0 ? '+' : ''}${r.diferenca.toFixed(3)})`
                    ).join('\n');

                    alert(`✅ Correção concluída!\n\n📊 ${estoquesCorrigidos} estoques corrigidos:\n\n${relatorio}\n\n🔄 Dados atualizados automaticamente.`);
                } else {
                    alert('ℹ️ Nenhuma correção necessária.\n\nTodos os saldos empenhados estão corretos.');
                }

            } catch (error) {
                console.error('❌ Erro na correção de saldos:', error);
                alert('❌ Erro na correção de saldos:\n\n' + error.message);
            }
        };

        // Diagnosticar saldos reais dos produtos
        window.diagnosticarSaldosReais = async function() {
            try {
                console.log('🔍 Iniciando diagnóstico de saldos reais...');

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                const todosEstoques = estoquesSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar todos os empenhos ativos
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("status", "==", "ATIVO")
                );
                const empenhosSnapshot = await getDocs(empenhosQuery);
                const empenhosAtivos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Focar nos produtos com problema
                const produtosProblema = ['102033', '100952', '101169', '108289'];
                const diagnostico = [];

                for (const codigoProduto of produtosProblema) {
                    const produto = produtos.find(p => p.codigo === codigoProduto);
                    if (!produto) continue;

                    // Buscar estoques deste produto
                    const estoquesProduct = todosEstoques.filter(e => e.produtoId === produto.id);

                    // Buscar empenhos deste produto
                    const empenhosProduct = empenhosAtivos.filter(e => e.produtoId === produto.id);

                    for (const estoque of estoquesProduct) {
                        const armazem = armazens.find(a => a.id === estoque.armazemId);

                        // Calcular empenhos reais para este estoque
                        const empenhosEstoque = empenhosProduct.filter(e => e.armazemId === estoque.armazemId);
                        const saldoEmpenhado_Real = empenhosEstoque.reduce((total, emp) => {
                            return total + ((emp.quantidadeEmpenhada || 0) - (emp.quantidadeConsumida || 0));
                        }, 0);

                        const saldoTotal = estoque.saldo || 0;
                        const saldoEmpenhado_Sistema = estoque.saldoEmpenhado || 0;
                        const saldoReservado = estoque.saldoReservado || 0;
                        const saldoDisponivel_Atual = saldoTotal - saldoReservado - saldoEmpenhado_Sistema;
                        const saldoDisponivel_Real = saldoTotal - saldoReservado - saldoEmpenhado_Real;

                        diagnostico.push({
                            produto: codigoProduto,
                            armazem: armazem?.codigo || estoque.armazemId,
                            saldoTotal,
                            saldoReservado,
                            saldoEmpenhado_Sistema,
                            saldoEmpenhado_Real,
                            saldoDisponivel_Atual,
                            saldoDisponivel_Real,
                            diferenca: saldoEmpenhado_Sistema - saldoEmpenhado_Real,
                            empenhosCount: empenhosEstoque.length,
                            problema: saldoDisponivel_Atual < 0 || Math.abs(saldoEmpenhado_Sistema - saldoEmpenhado_Real) > 0.001
                        });
                    }
                }

                // Mostrar diagnóstico
                mostrarDiagnosticoSaldos(diagnostico);

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                alert('❌ Erro no diagnóstico:\n\n' + error.message);
            }
        };

        function mostrarDiagnosticoSaldos(diagnostico) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white; border-radius: 15px; max-width: 1200px;
                width: 100%; max-height: 90vh; overflow-y: auto; padding: 0;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            `;

            let tabelaHtml = `
                <div style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                    <h2 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 24px;">🔍</span>
                        Diagnóstico de Saldos Reais
                    </h2>
                </div>

                <div style="padding: 20px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Produto</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Armazém</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Saldo Total</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Reservado</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Empenhado (Sistema)</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Empenhado (Real)</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Disponível (Atual)</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">Disponível (Real)</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">Status</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            diagnostico.forEach(item => {
                const corLinha = item.problema ? '#ffebee' : '#f8f9fa';
                const statusIcon = item.problema ? '🚨' : '✅';
                const statusText = item.problema ? 'PROBLEMA' : 'OK';

                tabelaHtml += `
                    <tr style="background: ${corLinha};">
                        <td style="border: 1px solid #dee2e6; padding: 8px; font-weight: bold;">${item.produto}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">${item.armazem}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">${item.saldoTotal.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right;">${item.saldoReservado.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; ${item.diferenca !== 0 ? 'background: #ffcdd2;' : ''}">${item.saldoEmpenhado_Sistema.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; font-weight: bold; color: #2e7d32;">${item.saldoEmpenhado_Real.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; ${item.saldoDisponivel_Atual < 0 ? 'background: #ffcdd2; color: #d32f2f; font-weight: bold;' : ''}">${item.saldoDisponivel_Atual.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; font-weight: bold; color: #2e7d32;">${item.saldoDisponivel_Real.toFixed(3)}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${statusIcon} ${statusText}</td>
                    </tr>
                `;
            });

            tabelaHtml += `
                        </tbody>
                    </table>

                    <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">📊 Legenda:</h4>
                        <ul style="margin: 0; padding-left: 20px; color: #1976d2;">
                            <li><strong>Empenhado (Sistema):</strong> Valor atual no banco de dados</li>
                            <li><strong>Empenhado (Real):</strong> Soma real dos empenhos ativos</li>
                            <li><strong>Disponível (Atual):</strong> Cálculo com valor do sistema (pode estar errado)</li>
                            <li><strong>Disponível (Real):</strong> Cálculo correto baseado nos empenhos reais</li>
                        </ul>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove()"
                                style="background: #6c757d; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px;">
                            ✖️ Fechar
                        </button>
                    </div>
                </div>
            `;

            modalContent.innerHTML = tabelaHtml;
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Fechar ao clicar fora
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // Ajustar saldos empenhados para eliminar negativos
        window.ajustarSaldosNegativos = async function() {
            if (!confirm('✅ AJUSTAR SALDOS NEGATIVOS\n\nEsta função vai:\n1. Identificar saldos disponíveis negativos\n2. Ajustar saldos empenhados para o máximo possível\n3. Manter empenhos válidos dentro do limite de estoque\n\nDeseja continuar?')) {
                return;
            }

            try {
                console.log('✅ Iniciando ajuste de saldos negativos...');

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                const todosEstoques = estoquesSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                let estoquesAjustados = 0;
                const relatorioAjustes = [];

                for (const estoque of todosEstoques) {
                    const saldoTotal = estoque.saldo || 0;
                    const saldoReservado = estoque.saldoReservado || 0;
                    const saldoEmpenhado = estoque.saldoEmpenhado || 0;
                    const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

                    // Verificar se o saldo disponível está negativo
                    if (saldoDisponivel < 0) {
                        // Calcular o máximo que pode estar empenhado
                        const maxEmpenhado = Math.max(0, saldoTotal - saldoReservado);

                        if (maxEmpenhado !== saldoEmpenhado) {
                            console.log(`🔧 Ajustando estoque: ${estoque.produtoId} - Empenhado: ${saldoEmpenhado} → ${maxEmpenhado}`);

                            await updateDoc(doc(db, "estoques", estoque.id), {
                                saldoEmpenhado: maxEmpenhado,
                                ajusteNegativo: Timestamp.now(),
                                saldoEmpenhado_anterior: saldoEmpenhado
                            });

                            const produto = produtos.find(p => p.id === estoque.produtoId);
                            const armazem = armazens.find(a => a.id === estoque.armazemId);

                            estoquesAjustados++;
                            relatorioAjustes.push({
                                produto: produto?.codigo || estoque.produtoId,
                                armazem: armazem?.codigo || estoque.armazemId,
                                saldoTotal,
                                saldoReservado,
                                empenhado_anterior: saldoEmpenhado,
                                empenhado_novo: maxEmpenhado,
                                disponivel_anterior: saldoDisponivel,
                                disponivel_novo: saldoTotal - saldoReservado - maxEmpenhado
                            });
                        }
                    }
                }

                if (estoquesAjustados > 0) {
                    // Recarregar dados
                    await carregarDados();

                    const relatorio = relatorioAjustes.map(r =>
                        `• ${r.produto} (${r.armazem}):\n  Empenhado: ${r.empenhado_anterior.toFixed(3)} → ${r.empenhado_novo.toFixed(3)}\n  Disponível: ${r.disponivel_anterior.toFixed(3)} → ${r.disponivel_novo.toFixed(3)}`
                    ).join('\n\n');

                    alert(`✅ Ajuste concluído!\n\n📊 ${estoquesAjustados} estoques ajustados:\n\n${relatorio}\n\n🔄 Agora todos os saldos disponíveis devem estar ≥ 0.\n\n💡 Os empenhos foram limitados ao máximo possível.`);
                } else {
                    alert('ℹ️ Nenhum ajuste necessário.\n\nTodos os saldos disponíveis já estão corretos.');
                }

            } catch (error) {
                console.error('❌ Erro no ajuste de saldos:', error);
                alert('❌ Erro no ajuste de saldos:\n\n' + error.message);
            }
        };

        // Concentrar material em uma OP específica
        window.concentrarMaterialOP = async function() {
            const opEscolhida = prompt('🎯 CONCENTRAR MATERIAL EM UMA OP\n\nDigite o código da OP que deve receber TODO o material disponível:\n\nExemplo: OP25070820');

            if (!opEscolhida) return;

            const opLimpa = opEscolhida.replace(/^OP/i, '').trim();

            if (!confirm(`🎯 CONCENTRAR MATERIAL\n\nVocê escolheu: ${opEscolhida}\n\nEsta função vai:\n1. Liberar empenhos de TODAS as outras OPs\n2. Concentrar TODO o material disponível na OP ${opEscolhida}\n3. Permitir apontamento completo desta OP\n\nDeseja continuar?`)) {
                return;
            }

            try {
                console.log(`🎯 Concentrando material na OP: ${opEscolhida}`);

                // Buscar todos os empenhos
                const empenhosSnapshot = await getDocs(collection(db, "empenhos"));
                const todosEmpenhos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                const todosEstoques = estoquesSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                let empenhosLiberados = 0;
                let empenhosConcentrados = 0;
                const relatorioConcentracao = [];

                // Agrupar empenhos por produto e armazém
                const empenhosPorMaterial = {};

                for (const empenho of todosEmpenhos) {
                    const chave = `${empenho.produtoId}_${empenho.armazemId}`;
                    if (!empenhosPorMaterial[chave]) {
                        empenhosPorMaterial[chave] = {
                            produtoId: empenho.produtoId,
                            armazemId: empenho.armazemId,
                            empenhos: []
                        };
                    }
                    empenhosPorMaterial[chave].empenhos.push(empenho);
                }

                // Processar cada material
                for (const chave in empenhosPorMaterial) {
                    const material = empenhosPorMaterial[chave];
                    const empenhosDoMaterial = material.empenhos;

                    // Separar empenhos da OP escolhida vs outras OPs
                    const empenhosOPEscolhida = empenhosDoMaterial.filter(e =>
                        e.ordemProducaoId && e.ordemProducaoId.includes(opLimpa)
                    );
                    const empenhosOutrasOPs = empenhosDoMaterial.filter(e =>
                        !e.ordemProducaoId || !e.ordemProducaoId.includes(opLimpa)
                    );

                    if (empenhosOutrasOPs.length === 0) continue;

                    // Calcular total empenhado em outras OPs
                    const totalOutrasOPs = empenhosOutrasOPs.reduce((sum, e) => sum + (e.quantidade || 0), 0);

                    if (totalOutrasOPs > 0) {
                        // Liberar empenhos de outras OPs
                        for (const empenho of empenhosOutrasOPs) {
                            await deleteDoc(doc(db, "empenhos", empenho.id));
                            empenhosLiberados++;
                        }

                        // Se existe empenho da OP escolhida, aumentar a quantidade
                        if (empenhosOPEscolhida.length > 0) {
                            const empenhoExistente = empenhosOPEscolhida[0];
                            const novaQuantidade = (empenhoExistente.quantidade || 0) + totalOutrasOPs;

                            await updateDoc(doc(db, "empenhos", empenhoExistente.id), {
                                quantidade: novaQuantidade,
                                concentracao: Timestamp.now(),
                                quantidade_anterior: empenhoExistente.quantidade
                            });

                            empenhosConcentrados++;
                        } else {
                            // Criar novo empenho para a OP escolhida
                            await addDoc(collection(db, "empenhos"), {
                                produtoId: material.produtoId,
                                armazemId: material.armazemId,
                                ordemProducaoId: opEscolhida,
                                quantidade: totalOutrasOPs,
                                dataEmpenho: Timestamp.now(),
                                concentracao: Timestamp.now(),
                                origem: 'concentracao_material'
                            });

                            empenhosConcentrados++;
                        }

                        const produto = produtos.find(p => p.id === material.produtoId);
                        const armazem = armazens.find(a => a.id === material.armazemId);

                        relatorioConcentracao.push({
                            produto: produto?.codigo || material.produtoId,
                            armazem: armazem?.codigo || material.armazemId,
                            quantidadeConcentrada: totalOutrasOPs
                        });
                    }
                }

                // Recalcular saldos empenhados nos estoques
                for (const estoque of todosEstoques) {
                    const empenhosAtualizados = await getDocs(
                        query(collection(db, "empenhos"),
                              where("produtoId", "==", estoque.produtoId),
                              where("armazemId", "==", estoque.armazemId))
                    );

                    const novoSaldoEmpenhado = empenhosAtualizados.docs.reduce((sum, doc) =>
                        sum + (doc.data().quantidade || 0), 0
                    );

                    await updateDoc(doc(db, "estoques", estoque.id), {
                        saldoEmpenhado: novoSaldoEmpenhado,
                        concentracao: Timestamp.now()
                    });
                }

                if (empenhosLiberados > 0 || empenhosConcentrados > 0) {
                    // Recarregar dados
                    await carregarDados();

                    const relatorio = relatorioConcentracao.map(r =>
                        `• ${r.produto} (${r.armazem}): +${r.quantidadeConcentrada.toFixed(3)} KG`
                    ).join('\n');

                    alert(`🎯 Concentração concluída!\n\n📊 Resultado:\n• ${empenhosLiberados} empenhos liberados de outras OPs\n• ${empenhosConcentrados} empenhos concentrados na ${opEscolhida}\n\n📦 Material concentrado:\n${relatorio}\n\n✅ Agora a ${opEscolhida} tem TODO o material disponível!\n\n🚀 Você pode fazer o apontamento completo desta OP.`);
                } else {
                    alert(`ℹ️ Nenhuma concentração necessária.\n\nA ${opEscolhida} já possui todo o material disponível.`);
                }

            } catch (error) {
                console.error('❌ Erro na concentração de material:', error);
                alert('❌ Erro na concentração de material:\n\n' + error.message);
            }
        };

        // Relatório de status de todas as OPs
        window.relatorioStatusOPs = async function() {
            try {
                console.log('📋 Gerando relatório de status de todas as OPs...');

                // Buscar todos os empenhos
                const empenhosSnapshot = await getDocs(collection(db, "empenhos"));
                const todosEmpenhos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                const todosEstoques = estoquesSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Agrupar empenhos por OP
                const empenhosPorOP = {};

                for (const empenho of todosEmpenhos) {
                    if (empenho.ordemProducaoId) {
                        if (!empenhosPorOP[empenho.ordemProducaoId]) {
                            empenhosPorOP[empenho.ordemProducaoId] = [];
                        }
                        empenhosPorOP[empenho.ordemProducaoId].push(empenho);
                    }
                }

                const statusOPs = [];

                // Analisar cada OP
                for (const opId in empenhosPorOP) {
                    const empenhosOP = empenhosPorOP[opId];
                    const materiaisOP = [];
                    let totalMateriais = 0;
                    let materiaisComSaldo = 0;
                    let materiaisSuficientes = 0;

                    for (const empenho of empenhosOP) {
                        // Buscar estoque correspondente
                        const estoque = todosEstoques.find(e =>
                            e.produtoId === empenho.produtoId &&
                            e.armazemId === empenho.armazemId
                        );

                        if (estoque) {
                            const saldoTotal = estoque.saldo || 0;
                            const saldoReservado = estoque.saldoReservado || 0;
                            const saldoEmpenhado = estoque.saldoEmpenhado || 0;
                            const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;
                            const quantidadeEmpenhada = empenho.quantidade || 0;

                            const produto = produtos.find(p => p.id === empenho.produtoId);
                            const armazem = armazens.find(a => a.id === empenho.armazemId);

                            const statusMaterial = {
                                produto: produto?.codigo || empenho.produtoId,
                                armazem: armazem?.codigo || empenho.armazemId,
                                empenhado: quantidadeEmpenhada,
                                disponivel: saldoDisponivel,
                                total: saldoTotal,
                                suficiente: saldoDisponivel >= 0,
                                percentual: saldoTotal > 0 ? ((quantidadeEmpenhada / saldoTotal) * 100) : 0
                            };

                            materiaisOP.push(statusMaterial);
                            totalMateriais++;

                            if (saldoDisponivel >= 0) {
                                materiaisComSaldo++;
                            }

                            if (quantidadeEmpenhada > 0 && saldoDisponivel >= 0) {
                                materiaisSuficientes++;
                            }
                        }
                    }

                    // Determinar status geral da OP
                    let statusGeral = '';
                    let icone = '';

                    if (materiaisSuficientes === totalMateriais && totalMateriais > 0) {
                        statusGeral = 'PRONTA PARA PRODUZIR';
                        icone = '✅';
                    } else if (materiaisSuficientes > 0) {
                        statusGeral = 'PRODUÇÃO PARCIAL';
                        icone = '⚠️';
                    } else {
                        statusGeral = 'AGUARDANDO MATERIAL';
                        icone = '❌';
                    }

                    statusOPs.push({
                        op: opId,
                        status: statusGeral,
                        icone: icone,
                        totalMateriais: totalMateriais,
                        materiaisSuficientes: materiaisSuficientes,
                        percentualCompleto: totalMateriais > 0 ? Math.round((materiaisSuficientes / totalMateriais) * 100) : 0,
                        materiais: materiaisOP
                    });
                }

                // Ordenar OPs por status (prontas primeiro)
                statusOPs.sort((a, b) => {
                    if (a.status === 'PRONTA PARA PRODUZIR' && b.status !== 'PRONTA PARA PRODUZIR') return -1;
                    if (b.status === 'PRONTA PARA PRODUZIR' && a.status !== 'PRONTA PARA PRODUZIR') return 1;
                    if (a.status === 'PRODUÇÃO PARCIAL' && b.status === 'AGUARDANDO MATERIAL') return -1;
                    if (b.status === 'PRODUÇÃO PARCIAL' && a.status === 'AGUARDANDO MATERIAL') return 1;
                    return a.op.localeCompare(b.op);
                });

                // Gerar relatório
                let relatorio = '📋 RELATÓRIO DE STATUS DE TODAS AS OPs\n\n';

                const opsProducao = statusOPs.filter(op => op.status === 'PRONTA PARA PRODUZIR');
                const opsParcias = statusOPs.filter(op => op.status === 'PRODUÇÃO PARCIAL');
                const opsAguardando = statusOPs.filter(op => op.status === 'AGUARDANDO MATERIAL');

                relatorio += `📊 RESUMO GERAL:\n`;
                relatorio += `✅ Prontas para produzir: ${opsProducao.length}\n`;
                relatorio += `⚠️ Produção parcial: ${opsParcias.length}\n`;
                relatorio += `❌ Aguardando material: ${opsAguardando.length}\n`;
                relatorio += `📦 Total de OPs: ${statusOPs.length}\n\n`;

                if (opsProducao.length > 0) {
                    relatorio += '✅ OPs PRONTAS PARA PRODUZIR:\n';
                    opsProducao.forEach(op => {
                        relatorio += `• ${op.op} (${op.materiaisSuficientes}/${op.totalMateriais} materiais)\n`;
                    });
                    relatorio += '\n';
                }

                if (opsParcias.length > 0) {
                    relatorio += '⚠️ OPs COM PRODUÇÃO PARCIAL:\n';
                    opsParcias.forEach(op => {
                        relatorio += `• ${op.op} (${op.materiaisSuficientes}/${op.totalMateriais} materiais - ${op.percentualCompleto}%)\n`;
                    });
                    relatorio += '\n';
                }

                if (opsAguardando.length > 0) {
                    relatorio += '❌ OPs AGUARDANDO MATERIAL:\n';
                    opsAguardando.forEach(op => {
                        relatorio += `• ${op.op} (0/${op.totalMateriais} materiais)\n`;
                    });
                    relatorio += '\n';
                }

                relatorio += '💡 RECOMENDAÇÕES:\n';
                if (opsProducao.length > 0) {
                    relatorio += `• Priorize as ${opsProducao.length} OPs prontas\n`;
                }
                if (opsParcias.length > 0) {
                    relatorio += `• Considere produção parcial das ${opsParcias.length} OPs\n`;
                }
                if (opsAguardando.length > 0) {
                    relatorio += `• Providencie material para ${opsAguardando.length} OPs\n`;
                }

                alert(relatorio);

                // Log detalhado no console
                console.log('📋 Status detalhado das OPs:', statusOPs);

            } catch (error) {
                console.error('❌ Erro no relatório de OPs:', error);
                alert('❌ Erro no relatório de OPs:\n\n' + error.message);
            }
        };

        // Validar jornada completa de uma OP
        window.validarJornadaOP = async function() {
            const opCodigo = prompt('🔍 VALIDAR JORNADA COMPLETA DA OP\n\nDigite o código da OP para validar toda sua jornada:\n\nExemplo: OP25070820 ou 25070820');

            if (!opCodigo) return;

            const opLimpa = opCodigo.replace(/^OP/i, '').trim();

            try {
                console.log(`🔍 Iniciando validação da jornada da OP: ${opCodigo}`);

                // ===== ETAPA 1: BUSCAR DADOS DA OP =====
                const ordensSnapshot = await getDocs(collection(db, "ordensProducao"));
                const op = ordensSnapshot.docs.find(doc => {
                    const data = doc.data();
                    return data.numero === opLimpa || data.id === opLimpa || doc.id === opLimpa;
                });

                if (!op) {
                    // Mostrar OPs disponíveis para ajudar o usuário
                    const opsDisponiveis = ordensSnapshot.docs.map(doc => {
                        const data = doc.data();
                        return `• ${data.numero || doc.id} - ${data.produtoFinal || 'N/A'} (${data.status || 'N/A'})`;
                    }).slice(0, 10); // Mostrar apenas as primeiras 10

                    const listaOPs = opsDisponiveis.length > 0 ?
                        `\n\n📋 OPs disponíveis (primeiras 10):\n${opsDisponiveis.join('\n')}` :
                        '\n\n📋 Nenhuma OP encontrada no sistema.';

                    alert(`❌ OP não encontrada: ${opCodigo}\n\nVerifique se o código está correto.${listaOPs}\n\n💡 Dica: Use apenas o número da OP (ex: 25070821)`);
                    return;
                }

                const opData = op.data();
                const opId = op.id;

                // ===== ETAPA 2: BUSCAR DADOS RELACIONADOS =====

                // Buscar transferências
                const transferenciasQuery = query(
                    collection(db, "transferenciasArmazem"),
                    where("ordemProducaoId", "==", opId)
                );
                const transferenciasSnapshot = await getDocs(transferenciasQuery);
                const transferencias = transferenciasSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar empenhos
                const empenhosQuery = query(
                    collection(db, "empenhos"),
                    where("ordemProducaoId", "==", opId)
                );
                const empenhosSnapshot = await getDocs(empenhosQuery);
                const empenhos = empenhosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar apontamentos
                const apontamentosQuery = query(
                    collection(db, "apontamentos"),
                    where("ordemProducaoId", "==", opId)
                );
                const apontamentosSnapshot = await getDocs(apontamentosQuery);
                const apontamentos = apontamentosSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Buscar movimentações de estoque relacionadas
                const movimentacoesQuery = query(
                    collection(db, "movimentacoesEstoque"),
                    where("observacoes", ">=", `OP ${opLimpa}`),
                    where("observacoes", "<=", `OP ${opLimpa}\uf8ff`)
                );
                const movimentacoesSnapshot = await getDocs(movimentacoesQuery);
                const movimentacoes = movimentacoesSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // ===== ETAPA 3: ANÁLISE DA JORNADA =====

                const jornada = {
                    op: {
                        codigo: opData.numero || opLimpa,
                        status: opData.status || 'N/A',
                        produto: opData.produtoFinal || 'N/A',
                        quantidade: opData.quantidade || 0,
                        dataAbertura: opData.dataAbertura?.toDate?.() || 'N/A',
                        materialTransferido: opData.materialTransferido || false
                    },
                    transferencias: {
                        total: transferencias.length,
                        concluidas: transferencias.filter(t => t.status === 'CONCLUIDA').length,
                        pendentes: transferencias.filter(t => t.status === 'PENDENTE').length,
                        detalhes: transferencias.map(t => ({
                            status: t.status,
                            data: t.dataTransferencia?.toDate?.() || 'N/A',
                            quantidade: t.quantidade || 0
                        }))
                    },
                    empenhos: {
                        total: empenhos.length,
                        ativos: empenhos.filter(e => e.status === 'ATIVO').length,
                        liberados: empenhos.filter(e => e.status === 'LIBERADO').length,
                        totalEmpenhado: empenhos.reduce((sum, e) => sum + (e.quantidadeEmpenhada || e.quantidade || 0), 0),
                        totalConsumido: empenhos.reduce((sum, e) => sum + (e.quantidadeConsumida || 0), 0),
                        detalhes: empenhos.map(e => {
                            const produto = produtos.find(p => p.id === e.produtoId);
                            return {
                                produto: produto?.codigo || e.produtoId,
                                quantidade: e.quantidadeEmpenhada || e.quantidade || 0,
                                consumido: e.quantidadeConsumida || 0,
                                status: e.status,
                                data: e.dataEmpenho?.toDate?.() || 'N/A'
                            };
                        })
                    },
                    apontamentos: {
                        total: apontamentos.length,
                        quantidadeProduzida: apontamentos.reduce((sum, a) => sum + (a.quantidadeProduzida || 0), 0),
                        detalhes: apontamentos.map(a => ({
                            quantidade: a.quantidadeProduzida || 0,
                            data: a.dataApontamento?.toDate?.() || 'N/A',
                            operador: a.operador || 'N/A'
                        }))
                    },
                    movimentacoes: {
                        total: movimentacoes.length,
                        entradas: movimentacoes.filter(m => m.tipo === 'ENTRADA').length,
                        saidas: movimentacoes.filter(m => m.tipo === 'SAIDA').length
                    }
                };

                // ===== ETAPA 4: VALIDAÇÕES E DIAGNÓSTICOS =====

                const validacoes = [];
                const alertas = [];
                const erros = [];

                // Validação 1: Status da OP
                if (jornada.op.status === 'Aberta') {
                    validacoes.push('✅ OP está aberta e pronta para produção');
                } else if (jornada.op.status === 'Em Produção') {
                    validacoes.push('🔄 OP está em produção');
                } else if (jornada.op.status === 'Finalizada') {
                    validacoes.push('🏁 OP foi finalizada');
                } else {
                    alertas.push(`⚠️ Status da OP: ${jornada.op.status}`);
                }

                // Validação 2: Transferências vs Flag
                if (jornada.transferencias.concluidas > 0 && !jornada.op.materialTransferido) {
                    erros.push('❌ Existem transferências concluídas mas flag materialTransferido = false');
                } else if (jornada.transferencias.concluidas === 0 && jornada.op.materialTransferido) {
                    alertas.push('⚠️ Flag materialTransferido = true mas não há transferências concluídas');
                } else if (jornada.transferencias.concluidas > 0 && jornada.op.materialTransferido) {
                    validacoes.push('✅ Transferências e flag materialTransferido estão consistentes');
                }

                // Validação 3: Empenhos vs Transferências
                if (jornada.transferencias.concluidas > 0 && jornada.empenhos.ativos === 0) {
                    erros.push('❌ Há transferências mas não há empenhos ativos');
                } else if (jornada.empenhos.ativos > 0 && jornada.transferencias.concluidas === 0) {
                    alertas.push('⚠️ Há empenhos ativos mas não há transferências concluídas');
                } else if (jornada.empenhos.ativos > 0 && jornada.transferencias.concluidas > 0) {
                    validacoes.push('✅ Empenhos e transferências estão consistentes');
                }

                // Validação 4: Apontamentos vs Produção
                if (jornada.apontamentos.total > 0 && jornada.op.status !== 'Em Produção' && jornada.op.status !== 'Finalizada') {
                    alertas.push('⚠️ Há apontamentos mas OP não está em produção');
                } else if (jornada.apontamentos.quantidadeProduzida >= jornada.op.quantidade) {
                    validacoes.push('✅ Quantidade produzida atende ao planejado');
                } else if (jornada.apontamentos.quantidadeProduzida > 0) {
                    alertas.push(`⚠️ Produção parcial: ${jornada.apontamentos.quantidadeProduzida}/${jornada.op.quantidade}`);
                }

                // Validação 5: Consumo vs Empenho
                const consumoTotal = jornada.empenhos.totalConsumido;
                const empenhoTotal = jornada.empenhos.totalEmpenhado;
                if (consumoTotal > empenhoTotal) {
                    erros.push('❌ Consumo maior que empenho - possível erro de dados');
                } else if (consumoTotal > 0 && empenhoTotal > 0) {
                    const percentualConsumo = (consumoTotal / empenhoTotal) * 100;
                    if (percentualConsumo > 80) {
                        validacoes.push(`✅ Alto consumo de material: ${percentualConsumo.toFixed(1)}%`);
                    } else {
                        alertas.push(`⚠️ Baixo consumo de material: ${percentualConsumo.toFixed(1)}%`);
                    }
                }

                // ===== ETAPA 5: GERAR RELATÓRIO =====

                let relatorio = `🔍 VALIDAÇÃO COMPLETA DA JORNADA\n`;
                relatorio += `OP: ${jornada.op.codigo}\n\n`;

                relatorio += `📋 DADOS GERAIS:\n`;
                relatorio += `• Status: ${jornada.op.status}\n`;
                relatorio += `• Produto: ${jornada.op.produto}\n`;
                relatorio += `• Quantidade: ${jornada.op.quantidade}\n`;
                relatorio += `• Data Abertura: ${jornada.op.dataAbertura}\n`;
                relatorio += `• Material Transferido: ${jornada.op.materialTransferido ? 'Sim' : 'Não'}\n\n`;

                relatorio += `📦 TRANSFERÊNCIAS:\n`;
                relatorio += `• Total: ${jornada.transferencias.total}\n`;
                relatorio += `• Concluídas: ${jornada.transferencias.concluidas}\n`;
                relatorio += `• Pendentes: ${jornada.transferencias.pendentes}\n\n`;

                relatorio += `🔗 EMPENHOS:\n`;
                relatorio += `• Total: ${jornada.empenhos.total}\n`;
                relatorio += `• Ativos: ${jornada.empenhos.ativos}\n`;
                relatorio += `• Liberados: ${jornada.empenhos.liberados}\n`;
                relatorio += `• Empenhado: ${jornada.empenhos.totalEmpenhado.toFixed(3)}\n`;
                relatorio += `• Consumido: ${jornada.empenhos.totalConsumido.toFixed(3)}\n\n`;

                relatorio += `📊 APONTAMENTOS:\n`;
                relatorio += `• Total: ${jornada.apontamentos.total}\n`;
                relatorio += `• Produzido: ${jornada.apontamentos.quantidadeProduzida}\n\n`;

                relatorio += `📈 MOVIMENTAÇÕES:\n`;
                relatorio += `• Total: ${jornada.movimentacoes.total}\n`;
                relatorio += `• Entradas: ${jornada.movimentacoes.entradas}\n`;
                relatorio += `• Saídas: ${jornada.movimentacoes.saidas}\n\n`;

                if (validacoes.length > 0) {
                    relatorio += `✅ VALIDAÇÕES APROVADAS:\n`;
                    validacoes.forEach(v => relatorio += `${v}\n`);
                    relatorio += `\n`;
                }

                if (alertas.length > 0) {
                    relatorio += `⚠️ ALERTAS:\n`;
                    alertas.forEach(a => relatorio += `${a}\n`);
                    relatorio += `\n`;
                }

                if (erros.length > 0) {
                    relatorio += `❌ ERROS ENCONTRADOS:\n`;
                    erros.forEach(e => relatorio += `${e}\n`);
                    relatorio += `\n`;
                }

                // Determinar status geral
                let statusGeral = '';
                if (erros.length > 0) {
                    statusGeral = '❌ JORNADA COM ERROS';
                } else if (alertas.length > 0) {
                    statusGeral = '⚠️ JORNADA COM ALERTAS';
                } else {
                    statusGeral = '✅ JORNADA VÁLIDA';
                }

                relatorio += `🎯 STATUS GERAL: ${statusGeral}`;

                alert(relatorio);

                // Log detalhado no console
                console.log('🔍 Jornada completa da OP:', jornada);

            } catch (error) {
                console.error('❌ Erro na validação da jornada:', error);
                alert('❌ Erro na validação da jornada:\n\n' + error.message);
            }
        };

        // 🔍 FUNÇÃO PARA DIAGNOSTICAR INCONSISTÊNCIAS DE EMPENHOS
        window.diagnosticarInconsistenciaEmpenhos = async function(opNumero = null, codigoProduto = null) {
            try {
                console.log('🔍 Iniciando diagnóstico de inconsistências de empenhos...');

                // Buscar dados necessários
                const [opsSnap, empenhosSnap, estoquesSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log(`📊 Dados carregados: ${ops.length} OPs, ${empenhos.length} empenhos, ${estoques.length} estoques`);

                // Filtrar por OP específica se fornecida
                let opsParaAnalisar = ops;
                if (opNumero) {
                    opsParaAnalisar = ops.filter(op => op.numero === opNumero);
                    if (opsParaAnalisar.length === 0) {
                        alert(`❌ OP ${opNumero} não encontrada!`);
                        return;
                    }
                }

                const problemas = [];
                let totalAnalisados = 0;

                for (const op of opsParaAnalisar) {
                    if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) continue;

                    // Filtrar materiais MP
                    const materiaisMP = op.materiaisNecessarios.filter(material => {
                        const produto = produtos.find(p => p.id === material.produtoId);
                        return produto && produto.tipo === 'MP' && material.necessidade > 0;
                    });

                    for (const material of materiaisMP) {
                        const produto = produtos.find(p => p.id === material.produtoId);
                        if (!produto) continue;

                        // Filtrar por produto específico se fornecido
                        if (codigoProduto && produto.codigo !== codigoProduto) continue;

                        totalAnalisados++;

                        // Buscar empenhos ativos para este material/OP
                        const empenhosAtivos = empenhos.filter(e =>
                            e.ordemProducaoId === op.id &&
                            e.produtoId === material.produtoId &&
                            e.status === 'ATIVO'
                        );

                        // Buscar estoque
                        const estoque = estoques.find(e =>
                            e.produtoId === material.produtoId &&
                            e.armazemId === op.armazemProducaoId
                        );

                        const saldoTotal = estoque?.saldo || 0;
                        const saldoEmpenhado = estoque?.saldoEmpenhado || 0;
                        const saldoDisponivel = saldoTotal - saldoEmpenhado;

                        // Calcular total empenhado pelos empenhos ativos
                        const totalEmpenhado = empenhosAtivos.reduce((total, e) =>
                            total + (e.quantidadeEmpenhada || 0), 0
                        );

                        // DETECTAR PROBLEMAS
                        const problemaAtual = {
                            op: op.numero,
                            produto: produto.codigo,
                            descricao: produto.descricao,
                            necessidade: material.necessidade,
                            saldoReservado: material.saldoReservado || 0,
                            saldoTotal,
                            saldoEmpenhado,
                            saldoDisponivel,
                            empenhosAtivos: empenhosAtivos.length,
                            totalEmpenhado,
                            problemas: []
                        };

                        // Problema 1: Material empenhado mas saldo disponível = 0
                        if (totalEmpenhado > 0 && saldoDisponivel <= 0) {
                            problemaAtual.problemas.push('MATERIAL_EMPENHADO_SEM_SALDO_DISPONIVEL');
                        }

                        // Problema 2: Saldo empenhado no estoque diferente do total de empenhos
                        if (Math.abs(saldoEmpenhado - totalEmpenhado) > 0.001) {
                            problemaAtual.problemas.push('INCONSISTENCIA_SALDO_EMPENHADO');
                        }

                        // Problema 3: Material necessário mas sem empenho
                        if (material.necessidade > 0 && totalEmpenhado === 0) {
                            problemaAtual.problemas.push('MATERIAL_NECESSARIO_SEM_EMPENHO');
                        }

                        // Problema 4: Empenho maior que necessidade
                        if (totalEmpenhado > material.necessidade) {
                            problemaAtual.problemas.push('EMPENHO_MAIOR_QUE_NECESSIDADE');
                        }

                        if (problemaAtual.problemas.length > 0) {
                            problemas.push(problemaAtual);
                        }
                    }
                }

                // Gerar relatório
                let relatorio = `🔍 DIAGNÓSTICO DE INCONSISTÊNCIAS DE EMPENHOS\n\n`;
                relatorio += `📊 Materiais analisados: ${totalAnalisados}\n`;
                relatorio += `🚨 Problemas encontrados: ${problemas.length}\n\n`;

                if (problemas.length === 0) {
                    relatorio += `✅ Nenhuma inconsistência encontrada!`;
                } else {
                    relatorio += `❌ PROBLEMAS DETECTADOS:\n\n`;

                    problemas.forEach((problema, index) => {
                        relatorio += `${index + 1}. OP ${problema.op} - ${problema.produto}\n`;
                        relatorio += `   📦 ${problema.descricao}\n`;
                        relatorio += `   📊 Necessidade: ${problema.necessidade}\n`;
                        relatorio += `   📊 Saldo Total: ${problema.saldoTotal}\n`;
                        relatorio += `   📊 Saldo Empenhado: ${problema.saldoEmpenhado}\n`;
                        relatorio += `   📊 Saldo Disponível: ${problema.saldoDisponivel}\n`;
                        relatorio += `   📊 Empenhos Ativos: ${problema.empenhosAtivos}\n`;
                        relatorio += `   📊 Total Empenhado: ${problema.totalEmpenhado}\n`;
                        relatorio += `   🚨 Problemas: ${problema.problemas.join(', ')}\n\n`;
                    });
                }

                console.log(relatorio);
                alert(relatorio);

                return { problemas, totalAnalisados };

            } catch (error) {
                console.error('❌ Erro no diagnóstico:', error);
                alert('❌ Erro ao executar diagnóstico: ' + error.message);
            }
        };

        // Listar todas as OPs disponíveis no sistema
        window.listarTodasOPs = async function() {
            try {
                console.log('📋 Listando todas as OPs disponíveis...');

                const ordensSnapshot = await getDocs(collection(db, "ordensProducao"));

                if (ordensSnapshot.empty) {
                    alert('📋 LISTA DE OPs\n\n❌ Nenhuma OP encontrada no sistema.');
                    return;
                }

                const ops = ordensSnapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        numero: data.numero || 'N/A',
                        produtoFinal: data.produtoFinal || 'N/A',
                        status: data.status || 'N/A',
                        quantidade: data.quantidade || 0,
                        dataAbertura: data.dataAbertura?.toDate?.() || null,
                        materialTransferido: data.materialTransferido || false
                    };
                });

                // Ordenar por número da OP (mais recentes primeiro)
                ops.sort((a, b) => {
                    const numA = parseInt(a.numero) || 0;
                    const numB = parseInt(b.numero) || 0;
                    return numB - numA;
                });

                // Agrupar por status
                const porStatus = {
                    'Aberta': ops.filter(op => op.status === 'Aberta'),
                    'Em Produção': ops.filter(op => op.status === 'Em Produção'),
                    'Finalizada': ops.filter(op => op.status === 'Finalizada'),
                    'Outros': ops.filter(op => !['Aberta', 'Em Produção', 'Finalizada'].includes(op.status))
                };

                let relatorio = '📋 LISTA DE TODAS AS OPs\n\n';
                relatorio += `📊 RESUMO: ${ops.length} OPs encontradas\n\n`;

                // Mostrar por status
                for (const [status, opsStatus] of Object.entries(porStatus)) {
                    if (opsStatus.length > 0) {
                        relatorio += `${getStatusIcon(status)} ${status.toUpperCase()} (${opsStatus.length}):\n`;

                        opsStatus.slice(0, 15).forEach(op => { // Limitar a 15 por status
                            const dataFormatada = op.dataAbertura ?
                                op.dataAbertura.toLocaleDateString('pt-BR') : 'N/A';
                            const materialIcon = op.materialTransferido ? '📦' : '⚪';

                            relatorio += `• ${op.numero} - ${op.produtoFinal} ${materialIcon}\n`;
                            relatorio += `  Qtd: ${op.quantidade} | Data: ${dataFormatada}\n`;
                        });

                        if (opsStatus.length > 15) {
                            relatorio += `  ... e mais ${opsStatus.length - 15} OPs\n`;
                        }
                        relatorio += '\n';
                    }
                }

                relatorio += '💡 LEGENDA:\n';
                relatorio += '📦 = Material transferido\n';
                relatorio += '⚪ = Material não transferido\n\n';

                relatorio += '🔍 Para validar uma OP específica:\n';
                relatorio += 'Use o botão "🔍 Validar Jornada OP" com o número da OP';

                alert(relatorio);

                // Log detalhado no console
                console.log('📋 Lista completa de OPs:', ops);

            } catch (error) {
                console.error('❌ Erro ao listar OPs:', error);
                alert('❌ Erro ao listar OPs:\n\n' + error.message);
            }
        };

        // Função auxiliar para ícones de status
        function getStatusIcon(status) {
            switch (status) {
                case 'Aberta': return '🆕';
                case 'Em Produção': return '🔄';
                case 'Finalizada': return '✅';
                default: return '❓';
            }
        }

        // FUNÇÃO DE EMERGÊNCIA: Zerar todos os saldos empenhados
        window.emergenciaZerarSaldosEmpenhados = async function() {
            if (!confirm('🚨 FUNÇÃO DE EMERGÊNCIA\n\nEsta função vai ZERAR todos os saldos empenhados de todos os estoques.\n\nUse apenas para reverter problemas causados pelas correções anteriores.\n\n⚠️ ATENÇÃO: Esta ação não pode ser desfeita!\n\nDeseja continuar?')) {
                return;
            }

            if (!confirm('🚨 CONFIRMAÇÃO FINAL\n\nTem certeza que deseja ZERAR TODOS os saldos empenhados?\n\nIsso vai restaurar os saldos disponíveis ao estado anterior.')) {
                return;
            }

            try {
                console.log('🚨 EMERGÊNCIA: Zerando todos os saldos empenhados...');

                // Buscar todos os estoques
                const estoquesSnapshot = await getDocs(collection(db, "estoques"));
                let estoquesZerados = 0;
                const relatorioZerados = [];

                for (const estoqueDoc of estoquesSnapshot.docs) {
                    const estoque = estoqueDoc.data();
                    const saldoEmpenhado = estoque.saldoEmpenhado || 0;

                    if (saldoEmpenhado !== 0) {
                        await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                            saldoEmpenhado: 0,
                            emergenciaZerado: Timestamp.now(),
                            saldoEmpenhado_backup: saldoEmpenhado // Backup do valor anterior
                        });

                        const produto = produtos.find(p => p.id === estoque.produtoId);
                        const armazem = armazens.find(a => a.id === estoque.armazemId);

                        estoquesZerados++;
                        relatorioZerados.push({
                            produto: produto?.codigo || estoque.produtoId,
                            armazem: armazem?.codigo || estoque.armazemId,
                            saldoAnterior: saldoEmpenhado
                        });

                        console.log(`🔄 Zerado: ${produto?.codigo} (${armazem?.codigo}) - Era: ${saldoEmpenhado.toFixed(3)}`);
                    }
                }

                // Recarregar dados
                await carregarDados();

                const relatorio = relatorioZerados.map(r =>
                    `• ${r.produto} (${r.armazem}): ${r.saldoAnterior.toFixed(3)} → 0.000`
                ).join('\n');

                alert(`✅ EMERGÊNCIA CONCLUÍDA!\n\n📊 ${estoquesZerados} saldos empenhados foram zerados:\n\n${relatorio}\n\n🔄 Agora todos os saldos disponíveis devem estar corretos.\n\n💡 Os valores anteriores foram salvos como backup.`);

            } catch (error) {
                console.error('❌ Erro na função de emergência:', error);
                alert('❌ Erro na função de emergência:\n\n' + error.message);
            }
        };

        // Mostrar ajuda para correções
        window.mostrarAjudaCorrecoes = function() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white; border-radius: 15px; max-width: 800px;
                width: 100%; max-height: 90vh; overflow-y: auto; padding: 0;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            `;

            modalContent.innerHTML = `
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                    <h2 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 24px;">🛠️</span>
                        Guia de Correções - Materiais de Produção
                    </h2>
                </div>

                <div style="padding: 30px;">
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="background: #e74c3c; color: white; padding: 8px; border-radius: 50%; font-size: 16px;">🔧</span>
                            Problema: Saldos Negativos ou Incorretos
                        </h3>
                        <div style="background: #ffebee; border-left: 4px solid #e74c3c; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                            <strong>Sintomas:</strong>
                            <ul style="margin: 10px 0 0 20px;">
                                <li>Saldo disponível negativo (ex: -134.000)</li>
                                <li>Saldo empenhado maior que saldo total</li>
                                <li>Valores inconsistentes entre empenhos e estoques</li>
                            </ul>
                        </div>
                        <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px;">
                            <strong>✅ Solução:</strong> Use o botão
                            <span style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">🔧 Corrigir Saldos Negativos</span>
                            <br><br>
                            <strong>O que faz:</strong> Recalcula todos os saldos empenhados baseado nos empenhos ativos reais.
                        </div>
                    </div>

                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="background: #8e44ad; color: white; padding: 8px; border-radius: 50%; font-size: 16px;">🔗</span>
                            Problema: Materiais sem Empenho
                        </h3>
                        <div style="background: #f3e5f5; border-left: 4px solid #8e44ad; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                            <strong>Sintomas:</strong>
                            <ul style="margin: 10px 0 0 20px;">
                                <li>Material está no armazém de produção</li>
                                <li>Não aparece empenho para nenhuma OP</li>
                                <li>Apontamento reclama de material faltante</li>
                            </ul>
                        </div>
                        <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px;">
                            <strong>✅ Solução:</strong> Use o botão
                            <span style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%); color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">🔗 Criar Empenhos Faltantes</span>
                            <br><br>
                            <strong>O que faz:</strong> Cria empenhos automáticos para materiais que estão em produção mas não têm empenho.
                        </div>
                    </div>

                    <div style="background: #fff3cd; border: 1px solid #ffc107; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="color: #856404; margin: 0 0 10px 0; display: flex; align-items: center; gap: 8px;">
                            <span>⚡</span> Para seu problema atual:
                        </h4>
                        <p style="margin: 0; color: #856404; font-weight: 600;">
                            Como você tem saldos negativos, use primeiro o botão
                            <span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 3px;">🔧 Corrigir Saldos Negativos</span>
                        </p>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove()"
                                style="background: #6c757d; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 16px;">
                            ✖️ Fechar
                        </button>
                    </div>
                </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Fechar ao clicar fora
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        };

        // Adicionar listeners para filtros em tempo real
        document.addEventListener('DOMContentLoaded', function() {
            // Filtro de busca em tempo real
            document.getElementById('filterBusca').addEventListener('input', function() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    aplicarFiltros();
                }, 300);
            });

            // Outros filtros aplicam automaticamente
            ['filterArmazem', 'filterTipo', 'filterStatus'].forEach(filterId => {
                document.getElementById(filterId).addEventListener('change', aplicarFiltros);
            });
        });

        // ===== CONTROLE DO MENU DE FERRAMENTAS =====

        window.toggleFerramentasMenu = function() {
            const menu = document.getElementById('ferramentasMenu');
            const isVisible = menu.style.display !== 'none';

            // Fechar todos os menus abertos
            document.querySelectorAll('[id$="Menu"]').forEach(m => m.style.display = 'none');

            // Abrir/fechar o menu atual
            menu.style.display = isVisible ? 'none' : 'block';
        };

        // Fechar menu ao clicar fora
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('ferramentasMenu');
            const button = event.target.closest('button[onclick*="toggleFerramentasMenu"]');

            if (!button && !menu?.contains(event.target)) {
                if (menu) menu.style.display = 'none';
            }
        });

        // ===== FUNÇÃO DE LIMPEZA DE OPs CANCELADAS =====

        window.limpezaOPsCanceladas = async function() {
            if (!confirm('🧹 LIMPEZA DE OPs CANCELADAS\n\nEsta função vai:\n\n1️⃣ Identificar OPs canceladas\n2️⃣ Liberar empenhos órfãos\n3️⃣ Corrigir saldos empenhados\n4️⃣ Gerar relatório de limpeza\n\n⚠️ Esta operação não pode ser desfeita!\n\nDeseja continuar?')) {
                return;
            }

            const loadingDiv = document.createElement('div');
            loadingDiv.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                align-items: center; justify-content: center; color: white;
                font-size: 18px; font-weight: bold;
            `;
            loadingDiv.innerHTML = `
                <div style="text-align: center; background: #2c3e50; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.5);">
                    <div style="font-size: 48px; margin-bottom: 20px;">🧹</div>
                    <div>Executando Limpeza de Sistema...</div>
                    <div id="limpezaProgress" style="margin-top: 15px; font-size: 14px; color: #bdc3c7;"></div>
                </div>
            `;
            document.body.appendChild(loadingDiv);

            const updateProgress = (message) => {
                const progressDiv = document.getElementById('limpezaProgress');
                if (progressDiv) {
                    progressDiv.innerHTML = message;
                }
                console.log('🧹 LIMPEZA:', message);
            };

            try {
                updateProgress('1️⃣ Carregando dados do sistema...');

                // Carregar todos os dados necessários
                const [opsSnap, empenhosSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "estoques"))
                ]);

                const ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                updateProgress('2️⃣ Identificando OPs canceladas...');

                // Identificar OPs canceladas
                const opsCanceladas = ordensProducao.filter(op =>
                    op.status === 'Cancelada' || op.status === 'cancelada' || op.status === 'CANCELADA'
                );

                updateProgress(`3️⃣ Encontradas ${opsCanceladas.length} OPs canceladas. Verificando empenhos...`);

                // Identificar empenhos órfãos (de OPs canceladas)
                const empenhosOrfaos = empenhos.filter(empenho => {
                    const opCancelada = opsCanceladas.find(op => op.id === empenho.ordemProducaoId);
                    return opCancelada && empenho.status === 'ATIVO';
                });

                updateProgress(`4️⃣ Encontrados ${empenhosOrfaos.length} empenhos órfãos. Iniciando limpeza...`);

                let empenhosLiberados = 0;
                let saldosCorrigidos = 0;
                const relatorioLimpeza = [];

                // Processar cada empenho órfão
                for (const empenho of empenhosOrfaos) {
                    try {
                        const opCancelada = opsCanceladas.find(op => op.id === empenho.ordemProducaoId);

                        updateProgress(`5️⃣ Liberando empenho: OP ${opCancelada?.numero || empenho.ordemProducaoId}...`);

                        // Liberar empenho
                        await updateDoc(doc(db, "empenhos", empenho.id), {
                            status: 'LIBERADO',
                            dataLiberacao: Timestamp.now(),
                            motivoLiberacao: 'Limpeza automática - OP cancelada',
                            limpezaAutomatica: true
                        });

                        // Corrigir saldo empenhado no estoque
                        const estoqueId = `${empenho.produtoId}_${empenho.armazemId}`;
                        const estoqueRef = doc(db, "estoques", estoqueId);

                        try {
                            await updateDoc(estoqueRef, {
                                saldoEmpenhado: increment(-(empenho.quantidadeEmpenhada - (empenho.quantidadeConsumida || 0)))
                            });
                            saldosCorrigidos++;
                        } catch (estoqueError) {
                            console.warn('⚠️ Erro ao corrigir estoque:', estoqueId, estoqueError);
                        }

                        empenhosLiberados++;

                        relatorioLimpeza.push({
                            opNumero: opCancelada?.numero || 'N/A',
                            opId: empenho.ordemProducaoId,
                            produtoId: empenho.produtoId,
                            quantidade: empenho.quantidadeEmpenhada,
                            armazemId: empenho.armazemId,
                            dataLiberacao: new Date().toLocaleString('pt-BR')
                        });

                    } catch (error) {
                        console.error('❌ Erro ao processar empenho:', empenho.id, error);
                    }
                }

                updateProgress('6️⃣ Gerando relatório final...');

                // Gerar relatório de limpeza
                const relatorioFinal = `
🧹 RELATÓRIO DE LIMPEZA DE SISTEMA
📅 Data: ${new Date().toLocaleString('pt-BR')}

📊 RESUMO:
• OPs Canceladas: ${opsCanceladas.length}
• Empenhos Órfãos Encontrados: ${empenhosOrfaos.length}
• Empenhos Liberados: ${empenhosLiberados}
• Saldos Corrigidos: ${saldosCorrigidos}

📋 DETALHES DAS OPs CANCELADAS:
${opsCanceladas.map(op => `• ${op.numero || op.id} - ${op.status}`).join('\n')}

🔧 EMPENHOS LIBERADOS:
${relatorioLimpeza.map(item =>
    `• OP ${item.opNumero} - Produto ${item.produtoId} - Qtd: ${item.quantidade}`
).join('\n')}

✅ LIMPEZA CONCLUÍDA COM SUCESSO!
                `;

                document.body.removeChild(loadingDiv);

                // Mostrar relatório
                const relatorioDiv = document.createElement('div');
                relatorioDiv.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                    align-items: center; justify-content: center;
                `;
                relatorioDiv.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 15px; max-width: 800px; max-height: 80vh; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.5);">
                        <h2 style="color: #27ae60; margin-bottom: 20px; text-align: center;">
                            🧹 Limpeza Concluída!
                        </h2>
                        <pre style="background: #f8f9fa; padding: 20px; border-radius: 8px; font-size: 12px; line-height: 1.4; white-space: pre-wrap;">${relatorioFinal}</pre>
                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="this.closest('div[style*=\"position: fixed\"]').remove(); carregarDados();"
                                    style="background: #27ae60; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600; margin-right: 10px;">
                                ✅ Fechar e Atualizar
                            </button>
                            <button onclick="navigator.clipboard.writeText(\`${relatorioFinal.replace(/`/g, '\\`')}\`).then(() => alert('Relatório copiado!'))"
                                    style="background: #3498db; color: white; border: none; padding: 12px 30px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                                📋 Copiar Relatório
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(relatorioDiv);

            } catch (error) {
                document.body.removeChild(loadingDiv);
                console.error('❌ Erro na limpeza:', error);
                alert(`❌ Erro durante a limpeza:\n\n${error.message}\n\nVerifique o console para mais detalhes.`);
            }
        };

        // 🔧 FUNÇÃO PARA REPROCESSAR EMPENHOS INCONSISTENTES
        window.reprocessarEmpenhosInconsistentes = async function(opNumero = null, codigoProduto = null) {
            try {
                console.log('🔧 Iniciando reprocessamento de empenhos inconsistentes...');

                // Primeiro, executar diagnóstico
                const diagnostico = await diagnosticarInconsistenciaEmpenhos(opNumero, codigoProduto);

                if (!diagnostico || diagnostico.problemas.length === 0) {
                    alert('✅ Nenhuma inconsistência encontrada para reprocessar!');
                    return;
                }

                const confirmar = confirm(`🔧 REPROCESSAMENTO DE EMPENHOS\n\n` +
                    `Foram encontrados ${diagnostico.problemas.length} problemas.\n\n` +
                    `Esta operação irá:\n` +
                    `• Liberar empenhos órfãos/inconsistentes\n` +
                    `• Corrigir saldos empenhados nos estoques\n` +
                    `• Recriar empenhos corretos quando necessário\n\n` +
                    `Deseja continuar?`);

                if (!confirmar) return;

                // Buscar dados atualizados
                const [opsSnap, empenhosSnap, estoquesSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                let empenhosLiberados = 0;
                let empenhosCorrigidos = 0;
                let empenhosRecriados = 0;
                const relatorioCorrecoes = [];

                for (const problema of diagnostico.problemas) {
                    try {
                        const op = ops.find(o => o.numero === problema.op);
                        const produto = produtos.find(p => p.codigo === problema.produto);

                        if (!op || !produto) continue;

                        // Buscar empenhos ativos para este material/OP
                        const empenhosAtivos = empenhos.filter(e =>
                            e.ordemProducaoId === op.id &&
                            e.produtoId === produto.id &&
                            e.status === 'ATIVO'
                        );

                        // Buscar estoque
                        const estoqueId = `${produto.id}_${op.armazemProducaoId}`;
                        const estoque = estoques.find(e => e.id === estoqueId);

                        // CORREÇÃO 1: Liberar empenhos órfãos (quando saldo disponível = 0 mas há empenho)
                        if (problema.problemas.includes('MATERIAL_EMPENHADO_SEM_SALDO_DISPONIVEL')) {
                            for (const empenho of empenhosAtivos) {
                                // Liberar empenho
                                await updateDoc(doc(db, "empenhos", empenho.id), {
                                    status: 'LIBERADO',
                                    dataLiberacao: Timestamp.now(),
                                    motivoLiberacao: 'Reprocessamento - Material empenhado sem saldo disponível',
                                    reprocessamento: true
                                });

                                // Corrigir saldo empenhado no estoque
                                if (estoque) {
                                    await updateDoc(doc(db, "estoques", estoque.id), {
                                        saldoEmpenhado: increment(-(empenho.quantidadeEmpenhada || 0))
                                    });
                                }

                                empenhosLiberados++;
                                relatorioCorrecoes.push({
                                    acao: 'LIBERADO',
                                    op: problema.op,
                                    produto: problema.produto,
                                    quantidade: empenho.quantidadeEmpenhada,
                                    motivo: 'Material empenhado sem saldo disponível'
                                });
                            }
                        }

                        // CORREÇÃO 2: Corrigir inconsistência de saldo empenhado
                        if (problema.problemas.includes('INCONSISTENCIA_SALDO_EMPENHADO')) {
                            if (estoque) {
                                const novoSaldoEmpenhado = problema.totalEmpenhado;
                                await updateDoc(doc(db, "estoques", estoque.id), {
                                    saldoEmpenhado: novoSaldoEmpenhado,
                                    correcaoSaldoEmpenhado: Timestamp.now(),
                                    saldoEmpenhado_anterior: problema.saldoEmpenhado
                                });

                                empenhosCorrigidos++;
                                relatorioCorrecoes.push({
                                    acao: 'CORRIGIDO_SALDO',
                                    op: problema.op,
                                    produto: problema.produto,
                                    saldoAnterior: problema.saldoEmpenhado,
                                    saldoNovo: novoSaldoEmpenhado,
                                    motivo: 'Inconsistência entre saldo empenhado e total de empenhos'
                                });
                            }
                        }

                        // CORREÇÃO 3: Criar empenho para material necessário sem empenho
                        if (problema.problemas.includes('MATERIAL_NECESSARIO_SEM_EMPENHO')) {
                            const material = op.materiaisNecessarios.find(m => m.produtoId === produto.id);
                            if (material && material.necessidade > 0) {
                                // Verificar se há saldo disponível para empenhar
                                const saldoDisponivel = problema.saldoTotal - problema.saldoEmpenhado;
                                const quantidadeEmpenhar = Math.min(material.necessidade, saldoDisponivel);

                                if (quantidadeEmpenhar > 0) {
                                    // Criar novo empenho
                                    await addDoc(collection(db, "empenhos"), {
                                        ordemProducaoId: op.id,
                                        produtoId: produto.id,
                                        armazemId: op.armazemProducaoId,
                                        quantidadeEmpenhada: quantidadeEmpenhar,
                                        quantidadeConsumida: 0,
                                        status: 'ATIVO',
                                        dataEmpenho: Timestamp.now(),
                                        origemReserva: false,
                                        reprocessamento: true,
                                        observacoes: `Empenho criado via reprocessamento - ${new Date().toLocaleString('pt-BR')}`
                                    });

                                    // Atualizar saldo empenhado no estoque
                                    if (estoque) {
                                        await updateDoc(doc(db, "estoques", estoque.id), {
                                            saldoEmpenhado: increment(quantidadeEmpenhar)
                                        });
                                    }

                                    empenhosRecriados++;
                                    relatorioCorrecoes.push({
                                        acao: 'CRIADO',
                                        op: problema.op,
                                        produto: problema.produto,
                                        quantidade: quantidadeEmpenhar,
                                        motivo: 'Material necessário sem empenho'
                                    });
                                }
                            }
                        }

                    } catch (error) {
                        console.error(`❌ Erro ao processar problema:`, problema, error);
                        relatorioCorrecoes.push({
                            acao: 'ERRO',
                            op: problema.op,
                            produto: problema.produto,
                            erro: error.message
                        });
                    }
                }

                // Gerar relatório final
                let relatorioFinal = `🔧 REPROCESSAMENTO DE EMPENHOS CONCLUÍDO\n\n`;
                relatorioFinal += `📊 Estatísticas:\n`;
                relatorioFinal += `• Empenhos liberados: ${empenhosLiberados}\n`;
                relatorioFinal += `• Saldos corrigidos: ${empenhosCorrigidos}\n`;
                relatorioFinal += `• Empenhos recriados: ${empenhosRecriados}\n`;
                relatorioFinal += `• Total de correções: ${relatorioCorrecoes.length}\n\n`;

                if (relatorioCorrecoes.length > 0) {
                    relatorioFinal += `📋 DETALHES DAS CORREÇÕES:\n\n`;
                    relatorioCorrecoes.forEach((correcao, index) => {
                        relatorioFinal += `${index + 1}. ${correcao.acao} - OP ${correcao.op} - ${correcao.produto}\n`;
                        if (correcao.quantidade) relatorioFinal += `   Quantidade: ${correcao.quantidade}\n`;
                        if (correcao.saldoAnterior !== undefined) relatorioFinal += `   Saldo: ${correcao.saldoAnterior} → ${correcao.saldoNovo}\n`;
                        if (correcao.motivo) relatorioFinal += `   Motivo: ${correcao.motivo}\n`;
                        if (correcao.erro) relatorioFinal += `   Erro: ${correcao.erro}\n`;
                        relatorioFinal += `\n`;
                    });
                }

                console.log(relatorioFinal);
                alert(relatorioFinal);

                return {
                    empenhosLiberados,
                    empenhosCorrigidos,
                    empenhosRecriados,
                    relatorioCorrecoes
                };

            } catch (error) {
                console.error('❌ Erro no reprocessamento:', error);
                alert('❌ Erro ao executar reprocessamento: ' + error.message);
            }
        };

        // 🎯 FUNÇÃO ESPECÍFICA PARA TESTAR OP25070888 E MPS009
        window.testarOP25070888_MPS009 = async function() {
            try {
                console.log('🎯 Testando especificamente OP25070888 e material MPS009...');

                // Executar diagnóstico específico
                console.log('1️⃣ Executando diagnóstico...');
                const diagnostico = await diagnosticarInconsistenciaEmpenhos('OP25070888', 'MPS009');

                if (diagnostico && diagnostico.problemas.length > 0) {
                    console.log('2️⃣ Problemas encontrados, executando reprocessamento...');
                    const resultado = await reprocessarEmpenhosInconsistentes('OP25070888', 'MPS009');

                    if (resultado) {
                        console.log('3️⃣ Reprocessamento concluído, verificando resultado...');

                        // Aguardar um pouco para as alterações serem processadas
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // Executar diagnóstico novamente para verificar se foi corrigido
                        const diagnosticoPos = await diagnosticarInconsistenciaEmpenhos('OP25070888', 'MPS009');

                        if (diagnosticoPos && diagnosticoPos.problemas.length === 0) {
                            alert('✅ SUCESSO!\n\nO problema da OP25070888 com material MPS009 foi corrigido!\n\nAgora o material deve aparecer corretamente na lista de transferência.');
                        } else {
                            alert('⚠️ ATENÇÃO!\n\nO reprocessamento foi executado, mas ainda há inconsistências.\n\nVerifique o console para mais detalhes.');
                        }
                    }
                } else {
                    alert('✅ NENHUM PROBLEMA ENCONTRADO!\n\nA OP25070888 e material MPS009 estão consistentes.\n\nSe o material não aparece na transferência, pode ser outro tipo de problema.');
                }

            } catch (error) {
                console.error('❌ Erro no teste:', error);
                alert('❌ Erro ao executar teste: ' + error.message);
            }
        };

        // 🎯 FUNÇÃO PARA ANALISAR PROBLEMA DE "MATERIAIS INSUFICIENTES PARA IMPRESSÃO"
        window.analisarProblemaImpressao = async function(opNumero = null) {
            try {
                console.log('🎯 Analisando problema de "Materiais Insuficientes para Impressão"...');

                // Buscar dados necessários
                const [opsSnap, estoquesSnap, produtosSnap, armazensSnap, transferenciasSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "transferenciasArmazem"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Filtrar OP específica se fornecida
                let opParaAnalisar = null;
                if (opNumero) {
                    opParaAnalisar = ops.find(op => op.numero === opNumero);
                    if (!opParaAnalisar) {
                        alert(`❌ OP ${opNumero} não encontrada!`);
                        return;
                    }
                } else {
                    // Se não especificou, pedir para o usuário
                    const numeroOP = prompt('🎯 ANÁLISE DE PROBLEMA DE IMPRESSÃO\n\nDigite o número da OP que está com problema:\n\nExemplo: OP25070887');
                    if (!numeroOP) return;

                    opParaAnalisar = ops.find(op => op.numero === numeroOP);
                    if (!opParaAnalisar) {
                        alert(`❌ OP ${numeroOP} não encontrada!`);
                        return;
                    }
                }

                const op = opParaAnalisar;
                const armazemProducao = armazens.find(a => a.id === op.armazemProducaoId);

                console.log(`📋 Analisando OP: ${op.numero}`);
                console.log(`🏭 Armazém de Produção: ${armazemProducao?.nome || 'N/A'}`);

                // Analisar cada material necessário
                const analise = {
                    op: op.numero,
                    armazemProducao: armazemProducao?.nome || 'N/A',
                    materiaisAnalisados: [],
                    problemas: [],
                    transferenciasEncontradas: [],
                    resumo: {
                        totalMateriais: 0,
                        materiaisOK: 0,
                        materiaisProblema: 0,
                        materiaisTransferidos: 0,
                        materiaisNaoTransferidos: 0
                    }
                };

                if (!op.materiaisNecessarios || op.materiaisNecessarios.length === 0) {
                    analise.problemas.push('OP não possui materiais necessários definidos');
                } else {
                    // Buscar transferências desta OP
                    const transferenciasOP = transferencias.filter(t => t.ordemProducaoId === op.id);
                    analise.transferenciasEncontradas = transferenciasOP.map(t => ({
                        id: t.id,
                        produtoId: t.produtoId,
                        quantidade: t.quantidade,
                        status: t.status,
                        dataHora: t.dataHora
                    }));

                    for (const material of op.materiaisNecessarios) {
                        const produto = produtos.find(p => p.id === material.produtoId);
                        if (!produto) continue;

                        analise.resumo.totalMateriais++;

                        // Buscar estoque no armazém de produção
                        const estoque = estoques.find(e =>
                            e.produtoId === material.produtoId &&
                            e.armazemId === op.armazemProducaoId
                        );

                        // Verificar transferências deste material
                        const transferenciasDoMaterial = transferenciasOP.filter(t => t.produtoId === material.produtoId);
                        const quantidadeTransferida = transferenciasDoMaterial.reduce((total, t) => total + (t.quantidade || 0), 0);

                        const saldoTotal = estoque?.saldo || 0;
                        const saldoReservado = estoque?.saldoReservado || 0;
                        const saldoEmpenhado = estoque?.saldoEmpenhado || 0;
                        const saldoDisponivel = saldoTotal - saldoReservado - saldoEmpenhado;

                        const materialAnalise = {
                            codigo: produto.codigo,
                            descricao: produto.descricao,
                            tipo: produto.tipo,
                            necessidade: material.necessidade,
                            saldoReservado: material.saldoReservado || 0,
                            estoque: {
                                existe: !!estoque,
                                saldoTotal,
                                saldoReservado,
                                saldoEmpenhado,
                                saldoDisponivel
                            },
                            transferencias: {
                                quantidade: transferenciasDoMaterial.length,
                                quantidadeTransferida,
                                detalhes: transferenciasDoMaterial
                            },
                            problemas: []
                        };

                        // DETECTAR PROBLEMAS

                        // 1. Material transferido mas não aparece no estoque
                        if (quantidadeTransferida > 0 && saldoTotal === 0) {
                            materialAnalise.problemas.push('TRANSFERIDO_MAS_SEM_ESTOQUE');
                            analise.problemas.push(`${produto.codigo}: Material foi transferido (${quantidadeTransferida}) mas não aparece no estoque`);
                        }

                        // 2. Material transferido mas saldo disponível insuficiente
                        if (quantidadeTransferida > 0 && saldoDisponivel < material.necessidade) {
                            materialAnalise.problemas.push('TRANSFERIDO_MAS_INSUFICIENTE');
                            analise.problemas.push(`${produto.codigo}: Material transferido mas saldo disponível (${saldoDisponivel}) < necessidade (${material.necessidade})`);
                        }

                        // 3. Material não transferido
                        if (quantidadeTransferida === 0) {
                            materialAnalise.problemas.push('NAO_TRANSFERIDO');
                            analise.problemas.push(`${produto.codigo}: Material não foi transferido para produção`);
                            analise.resumo.materiaisNaoTransferidos++;
                        } else {
                            analise.resumo.materiaisTransferidos++;
                        }

                        // 4. Estoque não existe no armazém de produção
                        if (!estoque) {
                            materialAnalise.problemas.push('SEM_ESTOQUE_PRODUCAO');
                            analise.problemas.push(`${produto.codigo}: Não há estoque no armazém de produção`);
                        }

                        // 5. Saldo empenhado maior que total
                        if (saldoEmpenhado > saldoTotal) {
                            materialAnalise.problemas.push('EMPENHO_MAIOR_QUE_TOTAL');
                            analise.problemas.push(`${produto.codigo}: Saldo empenhado (${saldoEmpenhado}) > saldo total (${saldoTotal})`);
                        }

                        // Contabilizar status
                        if (materialAnalise.problemas.length === 0) {
                            analise.resumo.materiaisOK++;
                        } else {
                            analise.resumo.materiaisProblema++;
                        }

                        analise.materiaisAnalisados.push(materialAnalise);
                    }
                }

                // Gerar relatório
                let relatorio = `🎯 ANÁLISE: MATERIAIS INSUFICIENTES PARA IMPRESSÃO\n\n`;
                relatorio += `📋 OP: ${analise.op}\n`;
                relatorio += `🏭 Armazém de Produção: ${analise.armazemProducao}\n`;
                relatorio += `📦 Transferências encontradas: ${analise.transferenciasEncontradas.length}\n\n`;

                relatorio += `📊 RESUMO:\n`;
                relatorio += `• Total de materiais: ${analise.resumo.totalMateriais}\n`;
                relatorio += `• Materiais OK: ${analise.resumo.materiaisOK}\n`;
                relatorio += `• Materiais com problema: ${analise.resumo.materiaisProblema}\n`;
                relatorio += `• Materiais transferidos: ${analise.resumo.materiaisTransferidos}\n`;
                relatorio += `• Materiais não transferidos: ${analise.resumo.materiaisNaoTransferidos}\n\n`;

                if (analise.problemas.length > 0) {
                    relatorio += `🚨 PROBLEMAS DETECTADOS:\n\n`;
                    analise.problemas.forEach((problema, index) => {
                        relatorio += `${index + 1}. ${problema}\n`;
                    });
                    relatorio += `\n`;
                }

                relatorio += `📋 DETALHES POR MATERIAL:\n\n`;
                analise.materiaisAnalisados.forEach((material, index) => {
                    relatorio += `${index + 1}. ${material.codigo} - ${material.descricao}\n`;
                    relatorio += `   📊 Necessidade: ${material.necessidade}\n`;
                    relatorio += `   📦 Estoque: ${material.estoque.saldoTotal} (Disponível: ${material.estoque.saldoDisponivel})\n`;
                    relatorio += `   🚚 Transferido: ${material.transferencias.quantidadeTransferida}\n`;
                    if (material.problemas.length > 0) {
                        relatorio += `   🚨 Problemas: ${material.problemas.join(', ')}\n`;
                    }
                    relatorio += `\n`;
                });

                console.log(relatorio);
                alert(relatorio);

                return analise;

            } catch (error) {
                console.error('❌ Erro na análise:', error);
                alert('❌ Erro ao executar análise: ' + error.message);
            }
        };

        // 🔧 FUNÇÃO PARA CORRIGIR PROBLEMA DE IMPRESSÃO
        window.corrigirProblemaImpressao = async function(opNumero = null) {
            try {
                console.log('🔧 Iniciando correção de problema de impressão...');

                // Primeiro, executar análise
                const analise = await analisarProblemaImpressao(opNumero);

                if (!analise || analise.problemas.length === 0) {
                    alert('✅ Nenhum problema encontrado para corrigir!');
                    return;
                }

                const confirmar = confirm(`🔧 CORREÇÃO DE PROBLEMA DE IMPRESSÃO\n\n` +
                    `OP: ${analise.op}\n` +
                    `Problemas encontrados: ${analise.problemas.length}\n\n` +
                    `Esta operação irá:\n` +
                    `• Corrigir saldos empenhados inconsistentes\n` +
                    `• Ajustar estoques de materiais transferidos\n` +
                    `• Sincronizar dados entre transferência e estoque\n\n` +
                    `Deseja continuar?`);

                if (!confirmar) return;

                // Buscar dados atualizados
                const [opsSnap, estoquesSnap, produtosSnap, transferenciasSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "transferenciasArmazem"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const op = ops.find(o => o.numero === analise.op);
                if (!op) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                let correcoes = 0;
                const relatorioCorrecoes = [];

                // Buscar transferências desta OP
                const transferenciasOP = transferencias.filter(t => t.ordemProducaoId === op.id);

                for (const materialAnalise of analise.materiaisAnalisados) {
                    try {
                        const produto = produtos.find(p => p.codigo === materialAnalise.codigo);
                        if (!produto) continue;

                        const transferenciasDoMaterial = transferenciasOP.filter(t => t.produtoId === produto.id);
                        const quantidadeTransferida = transferenciasDoMaterial.reduce((total, t) => total + (t.quantidade || 0), 0);

                        // CORREÇÃO 1: Material transferido mas sem estoque
                        if (materialAnalise.problemas.includes('TRANSFERIDO_MAS_SEM_ESTOQUE') && quantidadeTransferida > 0) {
                            const estoqueId = `${produto.id}_${op.armazemProducaoId}`;

                            // Verificar se estoque existe
                            const estoqueExistente = estoques.find(e => e.id === estoqueId);

                            if (!estoqueExistente) {
                                // Criar estoque
                                await setDoc(doc(db, "estoques", estoqueId), {
                                    produtoId: produto.id,
                                    armazemId: op.armazemProducaoId,
                                    saldo: quantidadeTransferida,
                                    saldoReservado: 0,
                                    saldoEmpenhado: 0,
                                    ultimaMovimentacao: Timestamp.now(),
                                    criadoPorCorrecao: true,
                                    observacoes: `Estoque criado por correção de problema de impressão - ${new Date().toLocaleString('pt-BR')}`
                                });
                            } else {
                                // Atualizar estoque existente
                                await updateDoc(doc(db, "estoques", estoqueId), {
                                    saldo: quantidadeTransferida,
                                    ultimaMovimentacao: Timestamp.now(),
                                    corrigidoPorImpressao: Timestamp.now()
                                });
                            }

                            correcoes++;
                            relatorioCorrecoes.push({
                                acao: 'ESTOQUE_CRIADO_ATUALIZADO',
                                produto: materialAnalise.codigo,
                                quantidade: quantidadeTransferida,
                                motivo: 'Material transferido mas sem estoque'
                            });
                        }

                        // CORREÇÃO 2: Saldo empenhado maior que total
                        if (materialAnalise.problemas.includes('EMPENHO_MAIOR_QUE_TOTAL')) {
                            const estoqueId = `${produto.id}_${op.armazemProducaoId}`;
                            const estoque = estoques.find(e => e.id === estoqueId);

                            if (estoque) {
                                const novoSaldoEmpenhado = Math.min(estoque.saldoEmpenhado || 0, estoque.saldo || 0);

                                await updateDoc(doc(db, "estoques", estoqueId), {
                                    saldoEmpenhado: novoSaldoEmpenhado,
                                    correcaoEmpenho: Timestamp.now(),
                                    saldoEmpenhado_anterior: estoque.saldoEmpenhado
                                });

                                correcoes++;
                                relatorioCorrecoes.push({
                                    acao: 'EMPENHO_CORRIGIDO',
                                    produto: materialAnalise.codigo,
                                    saldoAnterior: estoque.saldoEmpenhado,
                                    saldoNovo: novoSaldoEmpenhado,
                                    motivo: 'Saldo empenhado maior que total'
                                });
                            }
                        }

                        // CORREÇÃO 3: Ajustar saldo reservado na OP
                        if (quantidadeTransferida > 0 && materialAnalise.saldoReservado !== quantidadeTransferida) {
                            // Atualizar materiaisNecessarios da OP
                            const materiaisAtualizados = op.materiaisNecessarios.map(material => {
                                if (material.produtoId === produto.id) {
                                    return {
                                        ...material,
                                        saldoReservado: quantidadeTransferida,
                                        corrigidoPorImpressao: true
                                    };
                                }
                                return material;
                            });

                            await updateDoc(doc(db, "ordensProducao", op.id), {
                                materiaisNecessarios: materiaisAtualizados,
                                correcaoImpressao: Timestamp.now()
                            });

                            correcoes++;
                            relatorioCorrecoes.push({
                                acao: 'SALDO_RESERVADO_CORRIGIDO',
                                produto: materialAnalise.codigo,
                                saldoAnterior: materialAnalise.saldoReservado,
                                saldoNovo: quantidadeTransferida,
                                motivo: 'Sincronizar saldo reservado com transferências'
                            });
                        }

                    } catch (error) {
                        console.error(`❌ Erro ao corrigir material ${materialAnalise.codigo}:`, error);
                        relatorioCorrecoes.push({
                            acao: 'ERRO',
                            produto: materialAnalise.codigo,
                            erro: error.message
                        });
                    }
                }

                // Gerar relatório final
                let relatorioFinal = `🔧 CORREÇÃO DE PROBLEMA DE IMPRESSÃO CONCLUÍDA\n\n`;
                relatorioFinal += `📋 OP: ${analise.op}\n`;
                relatorioFinal += `📊 Correções realizadas: ${correcoes}\n\n`;

                if (relatorioCorrecoes.length > 0) {
                    relatorioFinal += `📋 DETALHES DAS CORREÇÕES:\n\n`;
                    relatorioCorrecoes.forEach((correcao, index) => {
                        relatorioFinal += `${index + 1}. ${correcao.acao} - ${correcao.produto}\n`;
                        if (correcao.quantidade) relatorioFinal += `   Quantidade: ${correcao.quantidade}\n`;
                        if (correcao.saldoAnterior !== undefined) relatorioFinal += `   Saldo: ${correcao.saldoAnterior} → ${correcao.saldoNovo}\n`;
                        if (correcao.motivo) relatorioFinal += `   Motivo: ${correcao.motivo}\n`;
                        if (correcao.erro) relatorioFinal += `   Erro: ${correcao.erro}\n`;
                        relatorioFinal += `\n`;
                    });
                }

                relatorioFinal += `✅ Agora tente imprimir a OP novamente!`;

                console.log(relatorioFinal);
                alert(relatorioFinal);

                return {
                    correcoes,
                    relatorioCorrecoes
                };

            } catch (error) {
                console.error('❌ Erro na correção:', error);
                alert('❌ Erro ao executar correção: ' + error.message);
            }
        };

        // 🎯 FUNÇÃO ESPECÍFICA PARA TESTAR E CORRIGIR OP25070887
        window.testarCorrigirOP25070887 = async function() {
            try {
                console.log('🎯 Testando e corrigindo especificamente OP25070887...');

                // 1. Analisar problema
                console.log('1️⃣ Analisando problema...');
                const analise = await analisarProblemaImpressao('OP25070887');

                if (analise && analise.problemas.length > 0) {
                    console.log('2️⃣ Problemas encontrados, executando correção...');
                    const resultado = await corrigirProblemaImpressao('OP25070887');

                    if (resultado && resultado.correcoes > 0) {
                        console.log('3️⃣ Correção concluída, verificando resultado...');

                        // Aguardar um pouco para as alterações serem processadas
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // Executar análise novamente para verificar se foi corrigido
                        const analisePos = await analisarProblemaImpressao('OP25070887');

                        if (analisePos && analisePos.problemas.length === 0) {
                            alert('✅ SUCESSO!\n\nO problema da OP25070887 foi corrigido!\n\nAgora você deve conseguir imprimir/apontar a OP normalmente.');
                        } else {
                            alert('⚠️ ATENÇÃO!\n\nA correção foi executada, mas ainda há alguns problemas.\n\nTente imprimir a OP e veja se o erro persiste.');
                        }
                    } else {
                        alert('⚠️ Nenhuma correção foi necessária ou possível.\n\nVerifique se os materiais foram realmente transferidos.');
                    }
                } else {
                    alert('✅ NENHUM PROBLEMA ENCONTRADO!\n\nA OP25070887 parece estar correta.\n\nSe ainda há erro de impressão, pode ser outro tipo de problema.');
                }

            } catch (error) {
                console.error('❌ Erro no teste:', error);
                alert('❌ Erro ao executar teste: ' + error.message);
            }
        };

        // 🔍 FUNÇÃO ESPECÍFICA PARA INVESTIGAR OP25070871
        window.investigarOP25070871 = async function() {
            try {
                console.log('🔍 Investigando OP25070871...');

                // Buscar dados necessários
                const [opsSnap, estoquesSnap, produtosSnap, estruturasSnap, empenhosSnap, transferenciasSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturasProduto")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "transferenciasArmazem"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Buscar OP específica
                const op = ops.find(o => o.numero === 'OP25070871');
                if (!op) {
                    alert('❌ OP25070871 não encontrada!');
                    return;
                }

                // Buscar produto principal
                const produtoPrincipal = produtos.find(p => p.id === op.produtoId);

                // Buscar estrutura do produto
                const estrutura = estruturas.find(e => e.produtoId === op.produtoId || e.produtoPaiId === op.produtoId);

                let relatorio = `🔍 INVESTIGAÇÃO COMPLETA - OP25070871\n\n`;
                relatorio += `📋 DADOS BÁSICOS:\n`;
                relatorio += `• Número: ${op.numero}\n`;
                relatorio += `• Status: ${op.status}\n`;
                relatorio += `• Quantidade: ${op.quantidade} ${op.unidade || 'PC'}\n`;
                relatorio += `• Produto: ${produtoPrincipal?.codigo || 'N/A'} - ${produtoPrincipal?.descricao || 'N/A'}\n`;
                relatorio += `• Armazém: ${op.armazemProducaoId}\n\n`;

                // Analisar estrutura do produto
                relatorio += `🏗️ ESTRUTURA DO PRODUTO:\n`;
                if (estrutura && estrutura.componentes) {
                    relatorio += `• Estrutura encontrada: ${estrutura.componentes.length} componente(s)\n`;
                    estrutura.componentes.forEach((comp, index) => {
                        const produtoComp = produtos.find(p => p.id === comp.componentId);
                        const qtdCalculada = comp.quantidade * op.quantidade;
                        relatorio += `  ${index + 1}. ${produtoComp?.codigo || comp.componentId}\n`;
                        relatorio += `     Qtd por unidade: ${comp.quantidade}\n`;
                        relatorio += `     Qtd total calculada: ${qtdCalculada}\n`;
                        relatorio += `     Unidade: ${comp.unidade || 'N/A'}\n`;
                    });
                } else {
                    relatorio += `• ❌ Estrutura não encontrada ou sem componentes\n`;
                }
                relatorio += `\n`;

                // Analisar materiais necessários da OP
                relatorio += `📦 MATERIAIS NECESSÁRIOS NA OP:\n`;
                if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
                    op.materiaisNecessarios.forEach((material, index) => {
                        const produto = produtos.find(p => p.id === material.produtoId);
                        relatorio += `${index + 1}. ${produto?.codigo || material.produtoId}\n`;
                        relatorio += `   Descrição: ${produto?.descricao || 'N/A'}\n`;
                        relatorio += `   Necessidade: ${material.necessidade || 0}\n`;
                        relatorio += `   Saldo Reservado: ${material.saldoReservado || 0}\n`;
                        relatorio += `   Unidade: ${produto?.unidade || 'N/A'}\n`;

                        // Verificar se a proporção faz sentido
                        const proporcao = (material.necessidade || 0) / op.quantidade;
                        relatorio += `   Proporção: ${proporcao} por unidade do produto final\n`;

                        if (proporcao > 50) {
                            relatorio += `   ⚠️ ATENÇÃO: Proporção muito alta!\n`;
                        }
                        relatorio += `\n`;
                    });
                } else {
                    relatorio += `• ❌ Nenhum material necessário definido\n\n`;
                }

                // Analisar empenhos
                const empenhosOP = empenhos.filter(e => e.ordemProducaoId === op.id);
                relatorio += `🔒 EMPENHOS:\n`;
                relatorio += `• Total de empenhos: ${empenhosOP.length}\n`;
                empenhosOP.forEach((empenho, index) => {
                    const produto = produtos.find(p => p.id === empenho.produtoId);
                    relatorio += `${index + 1}. ${produto?.codigo || empenho.produtoId}\n`;
                    relatorio += `   Quantidade: ${empenho.quantidadeEmpenhada || 0}\n`;
                    relatorio += `   Status: ${empenho.status}\n`;
                });
                relatorio += `\n`;

                // Analisar transferências
                const transferenciasOP = transferencias.filter(t => t.ordemProducaoId === op.id);
                relatorio += `🚚 TRANSFERÊNCIAS:\n`;
                relatorio += `• Total de transferências: ${transferenciasOP.length}\n`;
                transferenciasOP.forEach((transfer, index) => {
                    const produto = produtos.find(p => p.id === transfer.produtoId);
                    relatorio += `${index + 1}. ${produto?.codigo || transfer.produtoId}\n`;
                    relatorio += `   Quantidade: ${transfer.quantidade || 0}\n`;
                    relatorio += `   Status: ${transfer.status}\n`;
                    relatorio += `   Data: ${transfer.dataHora ? new Date(transfer.dataHora.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}\n`;
                });
                relatorio += `\n`;

                // Verificar estoque do material 110397
                const produto110397 = produtos.find(p => p.codigo === '110397');
                if (produto110397) {
                    const estoque110397 = estoques.find(e => e.produtoId === produto110397.id && e.armazemId === op.armazemProducaoId);
                    relatorio += `🔍 ANÁLISE ESPECÍFICA - MATERIAL 110397:\n`;
                    relatorio += `• Produto: ${produto110397.codigo} - ${produto110397.descricao}\n`;
                    relatorio += `• Tipo: ${produto110397.tipo}\n`;
                    relatorio += `• Unidade: ${produto110397.unidade}\n`;
                    if (estoque110397) {
                        relatorio += `• Saldo Total: ${estoque110397.saldo || 0}\n`;
                        relatorio += `• Saldo Reservado: ${estoque110397.saldoReservado || 0}\n`;
                        relatorio += `• Saldo Empenhado: ${estoque110397.saldoEmpenhado || 0}\n`;
                        relatorio += `• Saldo Disponível: ${(estoque110397.saldo || 0) - (estoque110397.saldoReservado || 0) - (estoque110397.saldoEmpenhado || 0)}\n`;
                    } else {
                        relatorio += `• ❌ Estoque não encontrado no armazém de produção\n`;
                    }
                }
                relatorio += `\n`;

                // Conclusões e recomendações
                relatorio += `💡 CONCLUSÕES E RECOMENDAÇÕES:\n`;

                // Verificar se há discrepância na estrutura
                if (estrutura && op.materiaisNecessarios) {
                    const materialOP = op.materiaisNecessarios.find(m => {
                        const prod = produtos.find(p => p.id === m.produtoId);
                        return prod?.codigo === '110397';
                    });

                    const componenteEstrutura = estrutura.componentes?.find(c => {
                        const prod = produtos.find(p => p.id === c.componentId);
                        return prod?.codigo === '110397';
                    });

                    if (materialOP && componenteEstrutura) {
                        const qtdEstrutura = componenteEstrutura.quantidade * op.quantidade;
                        const qtdOP = materialOP.necessidade;

                        if (Math.abs(qtdEstrutura - qtdOP) > 0.001) {
                            relatorio += `• ⚠️ DISCREPÂNCIA: Estrutura indica ${qtdEstrutura}, OP tem ${qtdOP}\n`;
                        } else {
                            relatorio += `• ✅ Quantidade na OP confere com a estrutura\n`;
                        }
                    }
                }

                // Verificar proporção
                if (op.materiaisNecessarios) {
                    const material110397 = op.materiaisNecessarios.find(m => {
                        const prod = produtos.find(p => p.id === m.produtoId);
                        return prod?.codigo === '110397';
                    });

                    if (material110397) {
                        const proporcao = material110397.necessidade / op.quantidade;
                        if (proporcao > 50) {
                            relatorio += `• ⚠️ PROPORÇÃO SUSPEITA: ${proporcao} unidades de material por produto final\n`;
                            relatorio += `• 💡 Verifique se a unidade de medida está correta\n`;
                        }
                    }
                }

                console.log(relatorio);
                alert(relatorio);

                return {
                    op,
                    estrutura,
                    empenhosOP,
                    transferenciasOP,
                    relatorio
                };

            } catch (error) {
                console.error('❌ Erro na investigação:', error);
                alert('❌ Erro ao investigar OP: ' + error.message);
            }
        };

        // 🔧 FUNÇÃO PARA CORRIGIR PROBLEMAS DA OP25070871
        window.corrigirOP25070871 = async function() {
            try {
                console.log('🔧 Iniciando correção da OP25070871...');

                // Primeiro executar investigação
                const investigacao = await investigarOP25070871();

                if (!investigacao) {
                    alert('❌ Não foi possível investigar a OP. Tente novamente.');
                    return;
                }

                const confirmar = confirm(`🔧 CORREÇÃO DA OP25070871\n\n` +
                    `Esta operação irá:\n` +
                    `• Verificar e corrigir proporções de materiais\n` +
                    `• Ajustar empenhos inconsistentes\n` +
                    `• Corrigir saldos reservados\n` +
                    `• Sincronizar estrutura com materiais necessários\n\n` +
                    `Deseja continuar?`);

                if (!confirmar) return;

                // Buscar dados atualizados
                const [opsSnap, estoquesSnap, produtosSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const op = ops.find(o => o.numero === 'OP25070871');
                if (!op) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                let correcoes = 0;
                const relatorioCorrecoes = [];

                // CORREÇÃO 1: Verificar material 110397
                const produto110397 = produtos.find(p => p.codigo === '110397');
                if (produto110397 && op.materiaisNecessarios) {
                    const material110397 = op.materiaisNecessarios.find(m => m.produtoId === produto110397.id);

                    if (material110397) {
                        const proporcao = material110397.necessidade / op.quantidade;

                        // Se proporção muito alta, pode ser erro de unidade
                        if (proporcao > 50) {
                            const novaQuantidade = op.quantidade; // 1 por 1 seria mais lógico

                            const confirmarCorrecao = confirm(`⚠️ PROPORÇÃO SUSPEITA DETECTADA\n\n` +
                                `Material 110397:\n` +
                                `• Atual: ${material110397.necessidade} para ${op.quantidade} produtos\n` +
                                `• Proporção: ${proporcao} por produto\n\n` +
                                `Deseja corrigir para 1:1 (${novaQuantidade} unidades)?`);

                            if (confirmarCorrecao) {
                                // Atualizar material na OP
                                const materiaisAtualizados = op.materiaisNecessarios.map(m => {
                                    if (m.produtoId === produto110397.id) {
                                        return {
                                            ...m,
                                            necessidade: novaQuantidade,
                                            quantidadeOriginal: material110397.necessidade,
                                            corrigidoEm: new Date(),
                                            motivoCorrecao: 'Proporção muito alta - ajustado para 1:1'
                                        };
                                    }
                                    return m;
                                });

                                await updateDoc(doc(db, "ordensProducao", op.id), {
                                    materiaisNecessarios: materiaisAtualizados,
                                    correcaoOP25070871: new Date(),
                                    observacoes: (op.observacoes || '') + `\n[${new Date().toLocaleString('pt-BR')}] Corrigida proporção do material 110397`
                                });

                                correcoes++;
                                relatorioCorrecoes.push({
                                    acao: 'PROPORCAO_CORRIGIDA',
                                    material: '110397',
                                    quantidadeAnterior: material110397.necessidade,
                                    quantidadeNova: novaQuantidade,
                                    motivo: 'Proporção muito alta'
                                });
                            }
                        }

                        // CORREÇÃO 2: Ajustar saldo reservado
                        if (material110397.saldoReservado !== material110397.necessidade) {
                            const estoqueId = `${produto110397.id}_${op.armazemProducaoId}`;
                            const estoque = estoques.find(e => e.id === estoqueId);

                            if (estoque) {
                                const novoSaldoReservado = material110397.necessidade;

                                await updateDoc(doc(db, "estoques", estoqueId), {
                                    saldoReservado: novoSaldoReservado,
                                    correcaoOP25070871: new Date(),
                                    saldoReservado_anterior: estoque.saldoReservado
                                });

                                // Atualizar também na OP
                                const materiaisAtualizados = op.materiaisNecessarios.map(m => {
                                    if (m.produtoId === produto110397.id) {
                                        return {
                                            ...m,
                                            saldoReservado: novoSaldoReservado
                                        };
                                    }
                                    return m;
                                });

                                await updateDoc(doc(db, "ordensProducao", op.id), {
                                    materiaisNecessarios: materiaisAtualizados
                                });

                                correcoes++;
                                relatorioCorrecoes.push({
                                    acao: 'SALDO_RESERVADO_AJUSTADO',
                                    material: '110397',
                                    saldoAnterior: material110397.saldoReservado,
                                    saldoNovo: novoSaldoReservado,
                                    motivo: 'Sincronizar reserva com necessidade'
                                });
                            }
                        }
                    }
                }

                // Gerar relatório final
                let relatorioFinal = `🔧 CORREÇÃO DA OP25070871 CONCLUÍDA\n\n`;
                relatorioFinal += `📊 Correções realizadas: ${correcoes}\n\n`;

                if (relatorioCorrecoes.length > 0) {
                    relatorioFinal += `📋 DETALHES DAS CORREÇÕES:\n\n`;
                    relatorioCorrecoes.forEach((correcao, index) => {
                        relatorioFinal += `${index + 1}. ${correcao.acao} - ${correcao.material}\n`;
                        if (correcao.quantidadeAnterior !== undefined) {
                            relatorioFinal += `   Quantidade: ${correcao.quantidadeAnterior} → ${correcao.quantidadeNova}\n`;
                        }
                        if (correcao.saldoAnterior !== undefined) {
                            relatorioFinal += `   Saldo: ${correcao.saldoAnterior} → ${correcao.saldoNovo}\n`;
                        }
                        relatorioFinal += `   Motivo: ${correcao.motivo}\n\n`;
                    });
                } else {
                    relatorioFinal += `✅ Nenhuma correção foi necessária.\n`;
                }

                console.log(relatorioFinal);
                alert(relatorioFinal);

                return {
                    correcoes,
                    relatorioCorrecoes
                };

            } catch (error) {
                console.error('❌ Erro na correção:', error);
                alert('❌ Erro ao corrigir OP: ' + error.message);
            }
        };

        // 🔧 FUNÇÃO ESPECÍFICA PARA CORRIGIR NECESSIDADE ZERADA DA OP25070871
        window.corrigirNecessidadeOP25070871 = async function() {
            try {
                console.log('🔧 Corrigindo necessidade zerada da OP25070871...');

                const confirmar = confirm(`🔧 CORREÇÃO ESPECÍFICA - OP25070871\n\n` +
                    `PROBLEMA DETECTADO:\n` +
                    `• Necessidade do material 110397 está zerada no banco\n` +
                    `• Interface mostra 1.000 PC mas banco tem 0 PC\n` +
                    `• Estrutura do produto não existe\n\n` +
                    `CORREÇÃO PROPOSTA:\n` +
                    `• Corrigir necessidade para 1 PC (1:1 com produto final)\n` +
                    `• Ajustar empenhos e reservas\n` +
                    `• Criar estrutura básica do produto\n\n` +
                    `Deseja continuar?`);

                if (!confirmar) return;

                // Buscar dados
                const [opsSnap, produtosSnap, estruturasSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturasProduto"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const op = ops.find(o => o.numero === 'OP25070871');
                if (!op) {
                    alert('❌ OP não encontrada!');
                    return;
                }

                const produto110397 = produtos.find(p => p.codigo === '110397');
                const produtoPrincipal = produtos.find(p => p.id === op.produtoId);

                if (!produto110397 || !produtoPrincipal) {
                    alert('❌ Produtos não encontrados!');
                    return;
                }

                let correcoes = 0;
                const relatorioCorrecoes = [];

                // CORREÇÃO 1: Corrigir necessidade do material
                if (op.materiaisNecessarios) {
                    const materiaisAtualizados = op.materiaisNecessarios.map(material => {
                        if (material.produtoId === produto110397.id) {
                            return {
                                ...material,
                                necessidade: 1, // 1:1 com produto final (5 PC produto = 5 PC material)
                                necessidadeAnterior: material.necessidade,
                                corrigidoEm: new Date(),
                                motivoCorrecao: 'Necessidade estava zerada - corrigido para proporção 1:1'
                            };
                        }
                        return material;
                    });

                    await updateDoc(doc(db, "ordensProducao", op.id), {
                        materiaisNecessarios: materiaisAtualizados,
                        correcaoNecessidade: new Date(),
                        observacoes: (op.observacoes || '') + `\n[${new Date().toLocaleString('pt-BR')}] Corrigida necessidade do material 110397 de 0 para 1 PC`
                    });

                    correcoes++;
                    relatorioCorrecoes.push({
                        acao: 'NECESSIDADE_CORRIGIDA',
                        material: '110397',
                        necessidadeAnterior: 0,
                        necessidadeNova: 1,
                        motivo: 'Necessidade estava zerada'
                    });
                }

                // CORREÇÃO 2: Criar estrutura básica do produto se não existir
                const estruturaExistente = estruturas.find(e =>
                    e.produtoId === op.produtoId || e.produtoPaiId === op.produtoId
                );

                if (!estruturaExistente) {
                    const novaEstrutura = {
                        produtoId: op.produtoId,
                        produtoPaiId: op.produtoId,
                        versao: 1,
                        ativa: true,
                        componentes: [{
                            componentId: produto110397.id,
                            quantidade: 1,
                            unidade: 'PC',
                            tipo: 'MP',
                            obrigatorio: true,
                            observacoes: 'Estrutura criada automaticamente para correção da OP25070871'
                        }],
                        criadaEm: new Date(),
                        criadaPor: 'Sistema - Correção OP25070871',
                        observacoes: 'Estrutura básica criada para corrigir OP sem estrutura'
                    };

                    await addDoc(collection(db, "estruturasProduto"), novaEstrutura);

                    correcoes++;
                    relatorioCorrecoes.push({
                        acao: 'ESTRUTURA_CRIADA',
                        produto: produtoPrincipal.codigo,
                        componentes: 1,
                        motivo: 'Produto não tinha estrutura definida'
                    });
                }

                // CORREÇÃO 3: Ajustar empenho se necessário
                const empenhosSnap = await getDocs(collection(db, "empenhos"));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhoOP = empenhos.find(e =>
                    e.ordemProducaoId === op.id && e.produtoId === produto110397.id && e.status === 'ATIVO'
                );

                if (empenhoOP && empenhoOP.quantidadeEmpenhada !== 5) {
                    await updateDoc(doc(db, "empenhos", empenhoOP.id), {
                        quantidadeEmpenhada: 5,
                        quantidadeAnterior: empenhoOP.quantidadeEmpenhada,
                        corrigidoEm: new Date(),
                        observacoes: (empenhoOP.observacoes || '') + ' - Quantidade corrigida para 5 PC'
                    });

                    correcoes++;
                    relatorioCorrecoes.push({
                        acao: 'EMPENHO_AJUSTADO',
                        material: '110397',
                        quantidadeAnterior: empenhoOP.quantidadeEmpenhada,
                        quantidadeNova: 5,
                        motivo: 'Ajustar empenho para nova necessidade'
                    });
                }

                // Gerar relatório final
                let relatorioFinal = `🔧 CORREÇÃO ESPECÍFICA OP25070871 CONCLUÍDA\n\n`;
                relatorioFinal += `📊 Correções realizadas: ${correcoes}\n\n`;

                if (relatorioCorrecoes.length > 0) {
                    relatorioFinal += `📋 DETALHES DAS CORREÇÕES:\n\n`;
                    relatorioCorrecoes.forEach((correcao, index) => {
                        relatorioFinal += `${index + 1}. ${correcao.acao}\n`;
                        if (correcao.material) relatorioFinal += `   Material: ${correcao.material}\n`;
                        if (correcao.produto) relatorioFinal += `   Produto: ${correcao.produto}\n`;
                        if (correcao.necessidadeAnterior !== undefined) {
                            relatorioFinal += `   Necessidade: ${correcao.necessidadeAnterior} → ${correcao.necessidadeNova}\n`;
                        }
                        if (correcao.quantidadeAnterior !== undefined) {
                            relatorioFinal += `   Quantidade: ${correcao.quantidadeAnterior} → ${correcao.quantidadeNova}\n`;
                        }
                        if (correcao.componentes) relatorioFinal += `   Componentes: ${correcao.componentes}\n`;
                        relatorioFinal += `   Motivo: ${correcao.motivo}\n\n`;
                    });
                }

                relatorioFinal += `✅ Agora execute novamente a investigação para verificar as correções!`;

                console.log(relatorioFinal);
                alert(relatorioFinal);

                return {
                    correcoes,
                    relatorioCorrecoes
                };

            } catch (error) {
                console.error('❌ Erro na correção específica:', error);
                alert('❌ Erro ao corrigir necessidade: ' + error.message);
            }
        };

        // 🔍 FUNÇÃO PARA INVESTIGAR INCONSISTÊNCIA DE ESTOQUE OP25070871
        window.investigarEstoqueOP25070871 = async function() {
            try {
                console.log('🔍 Investigando inconsistência de estoque OP25070871...');

                // Buscar dados de múltiplas fontes
                const [opsSnap, estoquesSnap, produtosSnap, transferenciasSnap, empenhosSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "ordensProducao")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "transferenciasArmazem")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "movimentacoesEstoque"))
                ]);

                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const transferencias = transferenciasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const op = ops.find(o => o.numero === 'OP25070871');
                const produto110397 = produtos.find(p => p.codigo === '110397');

                if (!op || !produto110397) {
                    alert('❌ OP ou produto não encontrado!');
                    return;
                }

                // Buscar todos os registros relacionados ao material 110397
                const estoquesPROD1 = estoques.filter(e =>
                    e.produtoId === produto110397.id &&
                    (e.armazemId === 'PROD1' || e.armazemId === op.armazemProducaoId)
                );

                const transferenciasOP = transferencias.filter(t =>
                    t.ordemProducaoId === op.id && t.produtoId === produto110397.id
                );

                const empenhosOP = empenhos.filter(e =>
                    e.ordemProducaoId === op.id && e.produtoId === produto110397.id
                );

                const movimentacoesMaterial = movimentacoes.filter(m =>
                    m.produtoId === produto110397.id &&
                    (m.armazemId === 'PROD1' || m.armazemId === op.armazemProducaoId)
                );

                let relatorio = `🔍 INVESTIGAÇÃO DE INCONSISTÊNCIA DE ESTOQUE\n`;
                relatorio += `OP25070871 - Material 110397\n\n`;

                relatorio += `📋 DADOS CONFIRMADOS DA INTERFACE:\n`;
                relatorio += `• Necessário: 1 PC\n`;
                relatorio += `• Estoque: 0 PC\n`;
                relatorio += `• Falta: 0 PC\n\n`;

                relatorio += `📊 DADOS DO BANCO DE DADOS:\n`;
                relatorio += `• Registros de estoque PROD1: ${estoquesPROD1.length}\n`;
                estoquesPROD1.forEach((estoque, index) => {
                    relatorio += `  ${index + 1}. ID: ${estoque.id}\n`;
                    relatorio += `     Saldo: ${estoque.saldo || 0}\n`;
                    relatorio += `     Reservado: ${estoque.saldoReservado || 0}\n`;
                    relatorio += `     Empenhado: ${estoque.saldoEmpenhado || 0}\n`;
                    relatorio += `     Disponível: ${(estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0)}\n`;
                });
                relatorio += `\n`;

                relatorio += `🚚 TRANSFERÊNCIAS:\n`;
                relatorio += `• Total: ${transferenciasOP.length}\n`;
                transferenciasOP.forEach((transfer, index) => {
                    relatorio += `  ${index + 1}. Quantidade: ${transfer.quantidade}\n`;
                    relatorio += `     Status: ${transfer.status || 'N/A'}\n`;
                    relatorio += `     Data: ${transfer.dataHora ? new Date(transfer.dataHora.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}\n`;
                    relatorio += `     Origem: ${transfer.armazemOrigemId || 'N/A'}\n`;
                    relatorio += `     Destino: ${transfer.armazemDestinoId || 'N/A'}\n`;
                });
                relatorio += `\n`;

                relatorio += `🔒 EMPENHOS:\n`;
                relatorio += `• Total: ${empenhosOP.length}\n`;
                empenhosOP.forEach((empenho, index) => {
                    relatorio += `  ${index + 1}. Quantidade: ${empenho.quantidadeEmpenhada || 0}\n`;
                    relatorio += `     Status: ${empenho.status}\n`;
                    relatorio += `     Armazém: ${empenho.armazemId || 'N/A'}\n`;
                });
                relatorio += `\n`;

                relatorio += `📈 MOVIMENTAÇÕES RECENTES:\n`;
                relatorio += `• Total: ${movimentacoesMaterial.length}\n`;
                movimentacoesMaterial
                    .sort((a, b) => (b.dataMovimentacao?.seconds || 0) - (a.dataMovimentacao?.seconds || 0))
                    .slice(0, 5)
                    .forEach((mov, index) => {
                        relatorio += `  ${index + 1}. Tipo: ${mov.tipo || 'N/A'}\n`;
                        relatorio += `     Quantidade: ${mov.quantidade || 0}\n`;
                        relatorio += `     Data: ${mov.dataMovimentacao ? new Date(mov.dataMovimentacao.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}\n`;
                        relatorio += `     Motivo: ${mov.motivo || 'N/A'}\n`;
                    });
                relatorio += `\n`;

                // Análise de inconsistências
                relatorio += `🚨 ANÁLISE DE INCONSISTÊNCIAS:\n`;

                if (estoquesPROD1.length === 0) {
                    relatorio += `• ❌ PROBLEMA: Nenhum registro de estoque encontrado para PROD1\n`;
                } else if (estoquesPROD1.length > 1) {
                    relatorio += `• ⚠️ ATENÇÃO: Múltiplos registros de estoque (${estoquesPROD1.length})\n`;
                }

                const saldoTotalBanco = estoquesPROD1.reduce((total, e) => total + (e.saldo || 0), 0);
                if (saldoTotalBanco !== 0) {
                    relatorio += `• 🔍 DISCREPÂNCIA: Interface mostra 0, banco tem ${saldoTotalBanco}\n`;
                }

                if (transferenciasOP.length > 0 && saldoTotalBanco === 0) {
                    relatorio += `• ❌ PROBLEMA: Material transferido mas estoque zerado\n`;
                }

                relatorio += `\n💡 POSSÍVEIS CAUSAS:\n`;
                relatorio += `• Cache desatualizado na interface\n`;
                relatorio += `• Transferência não processada corretamente\n`;
                relatorio += `• Consumo não registrado adequadamente\n`;
                relatorio += `• Múltiplos registros de estoque conflitantes\n`;

                console.log(relatorio);
                alert(relatorio);

                return {
                    estoquesPROD1,
                    transferenciasOP,
                    empenhosOP,
                    movimentacoesMaterial,
                    saldoTotalBanco,
                    relatorio
                };

            } catch (error) {
                console.error('❌ Erro na investigação de estoque:', error);
                alert('❌ Erro ao investigar estoque: ' + error.message);
            }
        };

        // 🔍 FUNÇÃO PARA INVESTIGAR SALDO RESERVADO 0.1
        window.investigarReserva01 = async function() {
            try {
                console.log('🔍 Investigando saldo reservado 0.1...');

                // Buscar dados
                const [estoquesSnap, empenhosSnap, produtosSnap, opsSnap] = await Promise.all([
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "empenhos")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "ordensProducao"))
                ]);

                const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const empenhos = empenhosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                const produto110397 = produtos.find(p => p.codigo === '110397');
                if (!produto110397) {
                    alert('❌ Produto 110397 não encontrado!');
                    return;
                }

                // Buscar todos os estoques do produto 110397
                const estoquesP110397 = estoques.filter(e => e.produtoId === produto110397.id);

                // Buscar todos os empenhos do produto 110397
                const empenhosP110397 = empenhos.filter(e => e.produtoId === produto110397.id);

                let relatorio = `🔍 INVESTIGAÇÃO SALDO RESERVADO 0.1\n`;
                relatorio += `Material: 110397 - TÁBUA PLACA DE POLIETILENO\n\n`;

                relatorio += `📊 ESTOQUES ENCONTRADOS:\n`;
                relatorio += `• Total de registros: ${estoquesP110397.length}\n\n`;

                let saldoTotalGeral = 0;
                let reservadoTotalGeral = 0;
                let empenhadoTotalGeral = 0;

                estoquesP110397.forEach((estoque, index) => {
                    const saldo = estoque.saldo || 0;
                    const reservado = estoque.saldoReservado || 0;
                    const empenhado = estoque.saldoEmpenhado || 0;

                    saldoTotalGeral += saldo;
                    reservadoTotalGeral += reservado;
                    empenhadoTotalGeral += empenhado;

                    relatorio += `${index + 1}. Armazém: ${estoque.armazemId}\n`;
                    relatorio += `   ID: ${estoque.id}\n`;
                    relatorio += `   Saldo: ${saldo}\n`;
                    relatorio += `   Reservado: ${reservado}\n`;
                    relatorio += `   Empenhado: ${empenhado}\n`;
                    relatorio += `   Disponível: ${saldo - reservado - empenhado}\n`;

                    if (reservado === 0.1) {
                        relatorio += `   🚨 AQUI ESTÁ O PROBLEMA! Reservado = 0.1\n`;
                    }
                    relatorio += `\n`;
                });

                relatorio += `📊 TOTAIS GERAIS:\n`;
                relatorio += `• Saldo Total: ${saldoTotalGeral}\n`;
                relatorio += `• Reservado Total: ${reservadoTotalGeral}\n`;
                relatorio += `• Empenhado Total: ${empenhadoTotalGeral}\n`;
                relatorio += `• Disponível Total: ${saldoTotalGeral - reservadoTotalGeral - empenhadoTotalGeral}\n\n`;

                relatorio += `🔒 EMPENHOS ENCONTRADOS:\n`;
                relatorio += `• Total de empenhos: ${empenhosP110397.length}\n\n`;

                empenhosP110397.forEach((empenho, index) => {
                    const op = ops.find(o => o.id === empenho.ordemProducaoId);
                    relatorio += `${index + 1}. OP: ${op?.numero || 'N/A'}\n`;
                    relatorio += `   Quantidade: ${empenho.quantidadeEmpenhada || 0}\n`;
                    relatorio += `   Status: ${empenho.status}\n`;
                    relatorio += `   Armazém: ${empenho.armazemId || 'N/A'}\n`;
                    relatorio += `   Data: ${empenho.dataEmpenho ? new Date(empenho.dataEmpenho.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}\n`;

                    if (empenho.quantidadeEmpenhada === 0.1) {
                        relatorio += `   🚨 EMPENHO COM 0.1! Pode ser a causa\n`;
                    }
                    relatorio += `\n`;
                });

                // Análise específica para OP25070871
                const op25070871 = ops.find(o => o.numero === 'OP25070871');
                if (op25070871) {
                    const empenhoOP = empenhosP110397.find(e => e.ordemProducaoId === op25070871.id);
                    relatorio += `🎯 ANÁLISE ESPECÍFICA OP25070871:\n`;
                    if (empenhoOP) {
                        relatorio += `• Empenho encontrado: ${empenhoOP.quantidadeEmpenhada} PC\n`;
                        relatorio += `• Status: ${empenhoOP.status}\n`;

                        if (empenhoOP.quantidadeEmpenhada === 0.1) {
                            relatorio += `• 🚨 PROBLEMA: Empenho de 0.1 PC (deveria ser 1 PC)\n`;
                        }
                    } else {
                        relatorio += `• ❌ Nenhum empenho encontrado para OP25070871\n`;
                    }

                    // Verificar material necessário na OP
                    if (op25070871.materiaisNecessarios) {
                        const material = op25070871.materiaisNecessarios.find(m => m.produtoId === produto110397.id);
                        if (material) {
                            relatorio += `• Material na OP: ${material.necessidade} PC necessário\n`;
                            relatorio += `• Saldo Reservado na OP: ${material.saldoReservado || 0} PC\n`;

                            if (material.saldoReservado === 0.1) {
                                relatorio += `• 🚨 PROBLEMA: OP registra reserva de 0.1 PC\n`;
                            }
                        }
                    }
                }
                relatorio += `\n`;

                relatorio += `💡 POSSÍVEIS CAUSAS DO 0.1:\n`;
                relatorio += `• Erro de conversão de unidades (1000g → 0.1kg?)\n`;
                relatorio += `• Processo de empenho interrompido\n`;
                relatorio += `• Bug no cálculo de reserva automática\n`;
                relatorio += `• Empenho manual incorreto\n`;
                relatorio += `• Problema de sincronização entre tabelas\n\n`;

                relatorio += `🔧 RECOMENDAÇÕES:\n`;
                if (reservadoTotalGeral === 0.1) {
                    relatorio += `• Corrigir saldo reservado de 0.1 para 1.0\n`;
                }
                if (empenhosP110397.some(e => e.quantidadeEmpenhada === 0.1)) {
                    relatorio += `• Corrigir empenho de 0.1 para 1.0\n`;
                }
                relatorio += `• Verificar se há conversão de unidades incorreta\n`;
                relatorio += `• Sincronizar dados entre estoque e empenhos\n`;

                console.log(relatorio);
                alert(relatorio);

                return {
                    estoquesP110397,
                    empenhosP110397,
                    saldoTotalGeral,
                    reservadoTotalGeral,
                    relatorio
                };

            } catch (error) {
                console.error('❌ Erro na investigação de reserva:', error);
                alert('❌ Erro ao investigar reserva: ' + error.message);
            }
        };

    </script>
</body>
</html>
