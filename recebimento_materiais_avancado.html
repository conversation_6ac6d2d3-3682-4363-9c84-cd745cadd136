<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recebimento de Materiais - Sistema Avançado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Dashboard Styles */
        .dashboard-section {
            background: var(--light-bg);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid var(--secondary-color);
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .dashboard-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .dashboard-card.pending {
            border-left: 4px solid var(--info-color);
        }

        .dashboard-card.delayed {
            border-left: 4px solid var(--danger-color);
        }

        .dashboard-card.partial {
            border-left: 4px solid var(--warning-color);
        }

        .dashboard-card.completed {
            border-left: 4px solid var(--success-color);
        }

        .card-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .dashboard-card.pending .card-icon {
            color: var(--info-color);
        }

        .dashboard-card.delayed .card-icon {
            color: var(--danger-color);
        }

        .dashboard-card.partial .card-icon {
            color: var(--warning-color);
        }

        .dashboard-card.completed .card-icon {
            color: var(--success-color);
        }

        .card-content {
            flex: 1;
        }

        .card-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .card-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        /* Form Styles */
        .form-section {
            background: var(--light-bg);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
        }

        .form-section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-control {
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-control.error {
            border-color: var(--danger-color);
            background: #fff5f5;
        }

        .form-control.warning {
            border-color: var(--warning-color);
            background: #fffbf0;
        }

        /* Supplier Info Card */
        .supplier-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .supplier-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .supplier-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .supplier-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .supplier-status.active {
            background: #d4edda;
            color: #155724;
        }

        .supplier-status.delayed {
            background: #f8d7da;
            color: #721c24;
        }

        .supplier-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .detail-value {
            color: var(--primary-color);
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, var(--primary-color) 100%);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-size: 0.9rem;
        }

        .table tbody tr:hover {
            background: var(--light-bg);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.quality {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-badge.stock {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-badge.delayed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.ok {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }

        /* Price Validation */
        .price-validation {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.8rem;
        }

        .price-original {
            color: #6c757d;
            text-decoration: line-through;
        }

        .price-current {
            font-weight: bold;
        }

        .price-current.ok {
            color: var(--success-color);
        }

        .price-current.warning {
            color: var(--warning-color);
        }

        .price-current.error {
            color: var(--danger-color);
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.8rem;
        }

        /* Delivery History */
        .delivery-history {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .delivery-history h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .delivery-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
        }

        .delivery-item:last-child {
            border-bottom: none;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transform: translateX(400px);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .supplier-details {
                grid-template-columns: 1fr;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-truck-loading"></i> Recebimento de Materiais - Sistema Avançado</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Dashboard de Entregas -->
            <div class="dashboard-section">
                <h3><i class="fas fa-chart-bar"></i> Dashboard de Entregas</h3>
                <div class="dashboard-cards">
                    <div class="dashboard-card pending">
                        <div class="card-icon"><i class="fas fa-clock"></i></div>
                        <div class="card-content">
                            <div class="card-number" id="pendingCount">0</div>
                            <div class="card-label">Aguardando Entrega</div>
                        </div>
                    </div>
                    <div class="dashboard-card delayed">
                        <div class="card-icon"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="card-content">
                            <div class="card-number" id="delayedCount">0</div>
                            <div class="card-label">Em Atraso</div>
                        </div>
                    </div>
                    <div class="dashboard-card partial">
                        <div class="card-icon"><i class="fas fa-truck"></i></div>
                        <div class="card-content">
                            <div class="card-number" id="partialCount">0</div>
                            <div class="card-label">Recebimento Parcial</div>
                        </div>
                    </div>
                    <div class="dashboard-card completed">
                        <div class="card-icon"><i class="fas fa-check-circle"></i></div>
                        <div class="card-content">
                            <div class="card-number" id="completedCount">0</div>
                            <div class="card-label">Recebidos</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alertas do Sistema -->
            <div id="systemAlerts"></div>

            <!-- Seleção do Pedido -->
            <div class="form-section">
                <h3><i class="fas fa-search"></i> Selecionar Pedido de Compra</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label>Pedido de Compra</label>
                        <select class="form-control" id="orderSelect" onchange="selectOrder()">
                            <option value="">Selecione o pedido...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Buscar por Número/Fornecedor</label>
                        <input type="text" class="form-control" id="orderSearch" placeholder="Digite para buscar..." onkeyup="filterOrders()">
                    </div>
                </div>
            </div>

            <!-- Informações do Fornecedor -->
            <div id="supplierSection" style="display: none;">
                <div class="supplier-card">
                    <div class="supplier-header">
                        <div class="supplier-name" id="supplierName"></div>
                        <div class="supplier-status" id="supplierStatus"></div>
                    </div>
                    <div class="supplier-details">
                        <div class="detail-item">
                            <div class="detail-label">CNPJ</div>
                            <div class="detail-value" id="supplierCnpj"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Contato</div>
                            <div class="detail-value" id="supplierContact"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Solicitante</div>
                            <div class="detail-value" id="orderRequester"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Data do Pedido</div>
                            <div class="detail-value" id="orderDate"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Entrega Prevista</div>
                            <div class="detail-value" id="deliveryDate"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Status do Prazo</div>
                            <div class="detail-value" id="deliveryStatus"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dados do Recebimento -->
            <div id="receiptSection" class="form-section" style="display: none;">
                <h3><i class="fas fa-clipboard-list"></i> Dados do Recebimento</h3>

                <!-- Alerta de Entrega Parcial -->
                <div id="partialDeliveryAlert" class="alert alert-info" style="display: none;">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Entrega Parcial Detectada:</strong>
                        <span id="partialDeliveryMessage"></span>
                        <br><small>Cada nota fiscal deve ser processada separadamente, mesmo para o mesmo pedido.</small>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Número da Nota Fiscal *</label>
                        <input type="text" class="form-control" id="invoiceNumber" placeholder="Ex: 123456" required>
                    </div>
                    <div class="form-group">
                        <label>Série da NF</label>
                        <input type="text" class="form-control" id="invoiceSeries" placeholder="Ex: 1" maxlength="3">
                    </div>
                    <div class="form-group">
                        <label>Data da Nota Fiscal *</label>
                        <input type="date" class="form-control" id="invoiceDate" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Valor dos Produtos *</label>
                        <input type="number" class="form-control" id="invoiceValue" step="0.01" placeholder="0,00" required onchange="calculateTotalInvoice()">
                    </div>
                    <div class="form-group">
                        <label>Valor do Frete</label>
                        <input type="number" class="form-control" id="freightValue" step="0.01" placeholder="0,00" value="0" onchange="calculateTotalInvoice()">
                    </div>
                    <div class="form-group">
                        <label>Valor do Seguro</label>
                        <input type="number" class="form-control" id="insuranceValue" step="0.01" placeholder="0,00" value="0" onchange="calculateTotalInvoice()">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Outras Despesas</label>
                        <input type="number" class="form-control" id="otherExpenses" step="0.01" placeholder="0,00" value="0" onchange="calculateTotalInvoice()">
                    </div>
                    <div class="form-group">
                        <label>Valor Total da NF</label>
                        <input type="number" class="form-control" id="totalInvoiceValue" step="0.01" placeholder="0,00" readonly style="background-color: #f8f9fa; font-weight: bold;">
                    </div>
                    <div class="form-group">
                        <label>Transportadora</label>
                        <input type="text" class="form-control" id="transportCompany" placeholder="Nome da transportadora">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>TES - Tipo de Entrada *</label>
                        <select class="form-control" id="tesSelect" required onchange="onTesChange()">
                            <option value="">Selecione o TES...</option>
                        </select>
                        <small id="tesHint" class="form-text text-muted">Selecione o tipo de entrada para definir tratamento fiscal</small>
                    </div>
                    <div class="form-group">
                        <label>CFOP</label>
                        <input type="text" class="form-control" id="cfopCode" placeholder="Ex: 1102" readonly style="background-color: #f8f9fa;">
                        <small class="form-text text-muted">Preenchido automaticamente pelo TES</small>
                    </div>
                    <div class="form-group">
                        <label>Armazém de Destino *</label>
                        <select class="form-control" id="warehouseSelect" required>
                            <option value="">Selecione o armazém...</option>
                        </select>
                        <small id="warehouseHint" style="color: #6c757d; margin-top: 5px;"></small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" id="receiptNotes" rows="3" placeholder="Observações sobre o recebimento..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Resumo do Pedido -->
            <div id="orderSummary" class="form-section" style="display: none;">
                <h3><i class="fas fa-chart-pie"></i> Resumo do Pedido</h3>
                <div class="supplier-details">
                    <div class="detail-item">
                        <div class="detail-label">Total de Itens</div>
                        <div class="detail-value" id="summaryTotalItems">0</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Total do Pedido</div>
                        <div class="detail-value" id="summaryTotalValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Já Recebido</div>
                        <div class="detail-value" id="summaryReceivedValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Valor Pendente</div>
                        <div class="detail-value" id="summaryPendingValue">R$ 0,00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Progresso</div>
                        <div class="detail-value" id="summaryProgress">0%</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Status do Pedido</div>
                        <div class="detail-value" id="summaryStatus">PENDENTE</div>
                    </div>
                </div>
            </div>

            <!-- Itens do Pedido -->
            <div id="itemsSection" style="display: none;">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 100px;">Código</th>
                                <th style="min-width: 200px;">Descrição</th>
                                <th style="width: 80px;">
                                    Unidade
                                    <br><small style="font-weight: normal; opacity: 0.8;">Interna/Compra</small>
                                </th>
                                <th style="width: 90px;">Qtd. Pedida</th>
                                <th style="width: 90px;">Qtd. Recebida</th>
                                <th style="width: 100px;">
                                    Saldo
                                    <br><small style="font-weight: normal; opacity: 0.8;">Interno/Compra</small>
                                </th>
                                <th style="width: 100px;">
                                    Qtd. a Receber
                                    <br><small style="font-weight: normal; opacity: 0.8;">Unidade Interna</small>
                                </th>
                                <th style="width: 100px;">Preço Pedido</th>
                                <th style="width: 100px;">
                                    Preço NF
                                    <br><small style="font-weight: normal; opacity: 0.8;">Por un. compra</small>
                                </th>
                                <th style="width: 100px;">Total Item</th>
                                <th style="width: 120px;">Status Item</th>
                                <th style="width: 150px;">Destino</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- Itens serão carregados dinamicamente -->
                        </tbody>
                        <tfoot>
                            <tr style="background-color: #f8f9fa; font-weight: bold;">
                                <td colspan="9" style="text-align: right; padding-right: 10px;">Total dos Itens a Receber:</td>
                                <td id="totalItemsValue" style="text-align: right;">R$ 0,00</td>
                                <td colspan="2"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Histórico de Entregas -->
                <div id="deliveryHistory" class="delivery-history" style="display: none;">
                    <h4><i class="fas fa-history"></i> Histórico de Entregas Parciais</h4>
                    <div id="deliveryHistoryContent"></div>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <button class="btn btn-success" onclick="processReceipt()">
                        <i class="fas fa-check"></i> Processar Recebimento
                    </button>
                    <button class="btn btn-warning" onclick="requestAuthorization()" id="authButton" style="display: none;">
                        <i class="fas fa-key"></i> Solicitar Autorização
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <!-- Scripts -->
    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            getDoc,
            Timestamp,
            query,
            where,
            orderBy
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Importar configuração TES existente
        import { TESConfig } from './config/tes-config.js';
        import { TESAuditService } from './services/tes-audit-service.js';

        // Variáveis globais
        let systemConfig = {};
        let pedidosCompra = [];
        let produtos = [];
        let armazens = [];
        let fornecedores = [];
        let currentOrder = null;
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || { nome: 'Usuário' };

        // ===== INICIALIZAÇÃO =====
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                showNotification('Carregando sistema avançado...', 'info');
                await loadSystemConfiguration();
                await loadInitialData();
                setupInterface();
                showNotification('Sistema carregado com sucesso!', 'success');
            } catch (error) {
                console.error('Erro na inicialização:', error);
                showNotification('Erro ao carregar sistema: ' + error.message, 'error');
            }
        });

        // ===== CARREGAMENTO DE CONFIGURAÇÕES =====
        async function loadSystemConfiguration() {
            try {
                const configDoc = await getDoc(doc(db, "parametros", "sistema"));

                if (configDoc.exists()) {
                    const data = configDoc.data();
                    systemConfig = {
                        // Configurações de qualidade
                        controleQualidade: data.configuracaoSistema?.controleQualidade || false,
                        armazemQualidade: data.configuracaoSistema?.armazemQualidade || false,
                        inspecaoRecebimento: data.configuracaoSistema?.inspecaoRecebimento || 'manual',
                        controleQualidadeObrigatorio: data.controleQualidadeObrigatorio || false,

                        // Configurações de estoque
                        armazemPadrao: data.armazemPadrao || '',
                        permitirEstoqueNegativo: data.permitirEstoqueNegativo || false,
                        toleranciaRecebimento: data.toleranciaRecebimento || 10,
                        toleranciaPreco: data.toleranciaPreco || 5,
                        bloquearPrecoDivergente: data.bloquearPrecoDivergente || false,
                        permitirRecebimentoParcial: data.permitirRecebimentoParcial || true,
                        diasAlerteAtraso: data.diasAlerteAtraso || 3,
                        exigirAutorizacaoExcesso: data.exigirAutorizacaoExcesso || false,
                        controlarHistoricoEntregas: data.controlarHistoricoEntregas || true,

                        // Outras configurações
                        homologacaoFornecedor: data.configuracaoSistema?.homologacaoFornecedor || false
                    };
                } else {
                    // Configurações padrão
                    systemConfig = {
                        controleQualidade: false,
                        armazemQualidade: false,
                        inspecaoRecebimento: 'manual',
                        controleQualidadeObrigatorio: false,
                        armazemPadrao: '',
                        permitirEstoqueNegativo: false,
                        toleranciaRecebimento: 10,
                        toleranciaPreco: 5,
                        bloquearPrecoDivergente: false,
                        permitirRecebimentoParcial: true,
                        diasAlerteAtraso: 3,
                        exigirAutorizacaoExcesso: false,
                        controlarHistoricoEntregas: true,
                        homologacaoFornecedor: false
                    };
                }

                console.log('Configurações carregadas:', systemConfig);
                updateSystemAlerts();

            } catch (error) {
                console.error('Erro ao carregar configurações:', error);
                throw error;
            }
        }

        function updateSystemAlerts() {
            const alertsContainer = document.getElementById('systemAlerts');
            let alerts = [];

            // Alerta sobre controle de qualidade
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                alerts.push({
                    type: 'warning',
                    icon: 'fas fa-clipboard-check',
                    message: `<strong>Controle de Qualidade Ativo:</strong> Materiais críticos serão direcionados para inspeção. Tipo: ${getInspectionTypeText(systemConfig.inspecaoRecebimento)}`
                });
            } else {
                alerts.push({
                    type: 'info',
                    icon: 'fas fa-warehouse',
                    message: `<strong>Entrada Direta:</strong> Materiais serão direcionados diretamente para o estoque após recebimento.`
                });
            }

            // Alerta sobre tolerâncias
            alerts.push({
                type: 'info',
                icon: 'fas fa-percentage',
                message: `<strong>Tolerâncias Configuradas:</strong> Quantidade: ${systemConfig.toleranciaRecebimento}% | Preço: ${systemConfig.toleranciaPreco}%`
            });

            // Alerta sobre recebimento parcial
            if (!systemConfig.permitirRecebimentoParcial) {
                alerts.push({
                    type: 'warning',
                    icon: 'fas fa-ban',
                    message: `<strong>Recebimento Parcial Bloqueado:</strong> Só será permitido receber a quantidade total do pedido.`
                });
            }

            alertsContainer.innerHTML = alerts.map(alert => `
                <div class="alert alert-${alert.type}">
                    <i class="${alert.icon}"></i>
                    <span>${alert.message}</span>
                </div>
            `).join('');
        }

        function getInspectionTypeText(type) {
            const types = {
                'todos': 'Todos os materiais',
                'criticos': 'Apenas materiais críticos',
                'manual': 'Definição manual por produto'
            };
            return types[type] || 'Não definido';
        }

        // ===== CARREGAMENTO DE DADOS =====
        async function loadInitialData() {
            try {
                const [pedidosSnap, produtosSnap, armazensSnap, fornecedoresSnap] = await Promise.all([
                    getDocs(collection(db, "pedidosCompra")),
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "fornecedores"))
                ]);

                pedidosCompra = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                fornecedores = fornecedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('Dados carregados:', {
                    pedidos: pedidosCompra.length,
                    produtos: produtos.length,
                    armazens: armazens.length,
                    fornecedores: fornecedores.length,
                    tesConfig: 'Usando configuração existente'
                });

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                throw error;
            }
        }

        // ===== TIPOS DE TES =====
        function populateTESSelect() {
            const select = document.getElementById('tesSelect');
            const hint = document.getElementById('tesHint');

            select.innerHTML = '<option value="">Selecione o TES...</option>';

            // Usar TES de compra da configuração existente
            const tesCompra = TESConfig.tesOptions.COMPRA || [];

            tesCompra.forEach(tes => {
                const option = document.createElement('option');
                option.value = tes.value;
                option.textContent = tes.text;
                option.dataset.tipo = tes.tipo;
                option.dataset.atualizaEstoque = tes.atualizaEstoque ? 'S' : 'N';
                select.appendChild(option);
            });

            if (tesCompra.length === 0) {
                hint.textContent = 'Nenhum TES de compra configurado. Verifique a configuração do sistema.';
                hint.style.color = '#e74c3c';
            } else {
                hint.textContent = `${tesCompra.length} tipos de entrada disponíveis`;
                hint.style.color = '#28a745';
            }
        }

        window.onTesChange = function() {
            const select = document.getElementById('tesSelect');
            const cfopField = document.getElementById('cfopCode');
            const selectedOption = select.options[select.selectedIndex];

            if (selectedOption && selectedOption.value) {
                const tesCode = selectedOption.value;

                // Buscar configuração do TES
                const tesConfig = TESConfig.getTESConfig(tesCode);

                if (tesConfig) {
                    // Preencher CFOP se disponível
                    cfopField.value = tesConfig.cfop || '';

                    console.log('TES selecionado:', {
                        codigo: tesCode,
                        descricao: tesConfig.descricao,
                        tipo: tesConfig.tipo,
                        atualizaEstoque: tesConfig.atualizaEstoque
                    });

                    // Atualizar hint com informações do TES
                    const hint = document.getElementById('tesHint');
                    hint.innerHTML = `
                        <strong>${tesCode} - ${tesConfig.descricao || selectedOption.textContent}</strong><br>
                        Tipo: ${tesConfig.tipo} |
                        Estoque: ${tesConfig.atualizaEstoque ? 'Atualiza' : 'Não atualiza'}
                    `;
                    hint.style.color = '#17a2b8';
                } else {
                    // Fallback para TES sem configuração detalhada
                    const hint = document.getElementById('tesHint');
                    hint.innerHTML = `<strong>${tesCode} - ${selectedOption.textContent}</strong>`;
                    hint.style.color = '#17a2b8';
                }
            } else {
                cfopField.value = '';
                document.getElementById('tesHint').textContent = 'Selecione o tipo de entrada para definir tratamento fiscal';
                document.getElementById('tesHint').style.color = '#6c757d';
            }
        };

        // ===== DASHBOARD DE ENTREGAS =====
        async function updateDashboard() {
            try {
                const hoje = new Date();
                hoje.setHours(0, 0, 0, 0);

                let pendingCount = 0;
                let delayedCount = 0;
                let partialCount = 0;
                let completedCount = 0;

                // Filtrar pedidos relevantes para recebimento
                const pedidosRelevantes = pedidosCompra.filter(p =>
                    p.status === 'APROVADO' ||
                    p.status === 'ENVIADO' ||
                    p.status === 'RECEBIDO_PARCIAL' ||
                    p.status === 'RECEBIDO'
                );

                pedidosRelevantes.forEach(pedido => {
                    // Verificar se tem data de entrega
                    let dataEntrega = null;
                    if (pedido.dataEntregaPrevista) {
                        if (pedido.dataEntregaPrevista.seconds) {
                            dataEntrega = new Date(pedido.dataEntregaPrevista.seconds * 1000);
                        } else if (pedido.dataEntregaPrevista.toDate) {
                            dataEntrega = pedido.dataEntregaPrevista.toDate();
                        } else {
                            dataEntrega = new Date(pedido.dataEntregaPrevista);
                        }
                        dataEntrega.setHours(0, 0, 0, 0);
                    }

                    // Classificar pedidos
                    if (pedido.status === 'RECEBIDO') {
                        completedCount++;
                    } else if (pedido.status === 'RECEBIDO_PARCIAL') {
                        partialCount++;
                    } else if (dataEntrega && dataEntrega < hoje) {
                        delayedCount++;
                    } else {
                        pendingCount++;
                    }
                });

                // Atualizar interface
                document.getElementById('pendingCount').textContent = pendingCount;
                document.getElementById('delayedCount').textContent = delayedCount;
                document.getElementById('partialCount').textContent = partialCount;
                document.getElementById('completedCount').textContent = completedCount;

                console.log('📊 Dashboard atualizado:', {
                    pendentes: pendingCount,
                    atrasados: delayedCount,
                    parciais: partialCount,
                    completos: completedCount
                });

            } catch (error) {
                console.error('❌ Erro ao atualizar dashboard:', error);
            }
        }

        function setupInterface() {
            populateOrderSelect();
            populateWarehouseSelect();
            populateTESSelect();
            updateSystemAlerts();
            updateDashboard();
        }

        function populateOrderSelect() {
            const select = document.getElementById('orderSelect');
            select.innerHTML = '<option value="">Selecione o pedido...</option>';

            // Filtrar pedidos que ainda têm itens pendentes para recebimento
            const availableOrders = pedidosCompra.filter(p => {
                // Incluir apenas pedidos que podem ser recebidos
                const statusValidos = ['APROVADO', 'ENVIADO', 'RECEBIDO_PARCIAL'];
                if (!statusValidos.includes(p.status)) {
                    return false;
                }

                // Verificar se tem itens e se há itens pendentes
                if (!p.itens || !Array.isArray(p.itens) || p.itens.length === 0) {
                    return false;
                }

                // Verificar se há pelo menos um item com quantidade pendente
                const temItensPendentes = p.itens.some(item => {
                    const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                    const qtdPedida = parseFloat(item.quantidade || 0);
                    return qtdRecebida < qtdPedida;
                });

                return temItensPendentes;
            });

            // Ordenar por prioridade (atrasados primeiro, depois por data)
            availableOrders.sort((a, b) => {
                const aDelayed = isOrderDelayed(a);
                const bDelayed = isOrderDelayed(b);

                if (aDelayed && !bDelayed) return -1;
                if (!aDelayed && bDelayed) return 1;

                return (b.numero || 0) - (a.numero || 0);
            });

            availableOrders.forEach(pedido => {
                const fornecedor = fornecedores.find(f => f.id === pedido.fornecedorId);
                const isDelayed = isOrderDelayed(pedido);

                const option = document.createElement('option');
                option.value = pedido.id;
                option.textContent = `${isDelayed ? '⚠️ ' : ''}PC-${pedido.numero || 'N/A'} - ${fornecedor?.razaoSocial || pedido.fornecedorNome || 'Fornecedor N/A'}`;
                option.style.color = isDelayed ? '#e74c3c' : '';
                option.style.fontWeight = isDelayed ? 'bold' : '';
                select.appendChild(option);
            });
        }

        function isOrderDelayed(order) {
            if (!order.dataEntregaPrevista) return false;

            const today = new Date();
            let deliveryDate;

            // Tratar diferentes formatos de data
            if (order.dataEntregaPrevista.toDate) {
                deliveryDate = order.dataEntregaPrevista.toDate();
            } else if (order.dataEntregaPrevista.seconds) {
                deliveryDate = new Date(order.dataEntregaPrevista.seconds * 1000);
            } else if (typeof order.dataEntregaPrevista === 'string') {
                deliveryDate = new Date(order.dataEntregaPrevista);
            } else {
                deliveryDate = new Date(order.dataEntregaPrevista);
            }

            const diffDays = Math.ceil((today - deliveryDate) / (1000 * 60 * 60 * 24));
            return diffDays >= (systemConfig.diasAlerteAtraso || 3);
        }

        function populateWarehouseSelect() {
            const select = document.getElementById('warehouseSelect');
            const hint = document.getElementById('warehouseHint');

            select.innerHTML = '<option value="">Selecione o armazém...</option>';

            armazens.forEach(armazem => {
                const option = document.createElement('option');
                option.value = armazem.id;
                option.textContent = `${armazem.codigo} - ${armazem.descricao}`;
                select.appendChild(option);
            });

            // Definir armazém padrão baseado na configuração
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                const qualityWarehouse = armazens.find(a =>
                    a.tipo === 'QUALIDADE' ||
                    a.codigo?.toUpperCase().includes('QUALIDADE') ||
                    a.descricao?.toUpperCase().includes('QUALIDADE')
                );

                if (qualityWarehouse) {
                    select.value = qualityWarehouse.id;
                    hint.textContent = 'Armazém da qualidade selecionado automaticamente';
                    hint.style.color = '#f39c12';
                } else {
                    hint.textContent = 'Atenção: Armazém da qualidade não encontrado';
                    hint.style.color = '#e74c3c';
                }
            } else if (systemConfig.armazemPadrao) {
                const defaultWarehouse = armazens.find(a => a.id === systemConfig.armazemPadrao);
                if (defaultWarehouse) {
                    select.value = systemConfig.armazemPadrao;
                    hint.textContent = 'Armazém padrão selecionado automaticamente';
                    hint.style.color = '#27ae60';
                }
            }
        }

        // ===== FILTRO DE PEDIDOS =====
        window.filterOrders = function() {
            const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
            const select = document.getElementById('orderSelect');
            const options = Array.from(select.options);

            options.forEach(option => {
                if (option.value === '') return; // Manter opção vazia

                const text = option.textContent.toLowerCase();
                option.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        };

        // ===== CÁLCULO AUTOMÁTICO DA NOTA FISCAL =====
        window.calculateTotalInvoice = function() {
            const invoiceValue = parseFloat(document.getElementById('invoiceValue').value) || 0;
            const freightValue = parseFloat(document.getElementById('freightValue').value) || 0;
            const insuranceValue = parseFloat(document.getElementById('insuranceValue').value) || 0;
            const otherExpenses = parseFloat(document.getElementById('otherExpenses').value) || 0;

            const totalValue = invoiceValue + freightValue + insuranceValue + otherExpenses;
            document.getElementById('totalInvoiceValue').value = totalValue.toFixed(2);

            // Atualizar cor do campo se houver divergência com o total dos itens
            const itemsTotal = parseFloat(document.getElementById('totalItemsValue')?.textContent?.replace('R$ ', '').replace(',', '.')) || 0;
            const totalField = document.getElementById('totalInvoiceValue');

            if (Math.abs(totalValue - itemsTotal) > 0.01 && itemsTotal > 0) {
                totalField.style.backgroundColor = '#fff3cd';
                totalField.style.borderColor = '#ffc107';
                totalField.title = `Divergência detectada! Total dos itens: R$ ${itemsTotal.toFixed(2)}`;
            } else {
                totalField.style.backgroundColor = '#f8f9fa';
                totalField.style.borderColor = '#ced4da';
                totalField.title = '';
            }
        };

        // ===== SELEÇÃO DE PEDIDO =====
        window.selectOrder = async function() {
            const orderId = document.getElementById('orderSelect').value;

            if (!orderId) {
                document.getElementById('supplierSection').style.display = 'none';
                document.getElementById('receiptSection').style.display = 'none';
                document.getElementById('itemsSection').style.display = 'none';
                return;
            }

            try {
                currentOrder = pedidosCompra.find(p => p.id === orderId);

                if (!currentOrder) {
                    showNotification('Pedido não encontrado', 'error');
                    return;
                }

                // Carregar informações do fornecedor
                await loadSupplierInfo();

                // Mostrar seções
                document.getElementById('supplierSection').style.display = 'block';
                document.getElementById('receiptSection').style.display = 'block';
                document.getElementById('orderSummary').style.display = 'block';
                document.getElementById('itemsSection').style.display = 'block';

                // Carregar resumo e itens
                loadOrderSummary();
                loadOrderItems();
                checkPartialDelivery();

                // Carregar histórico de entregas
                if (systemConfig.controlarHistoricoEntregas) {
                    await loadDeliveryHistory();
                }

                showNotification('Pedido carregado com sucesso', 'success');

            } catch (error) {
                console.error('Erro ao carregar pedido:', error);
                showNotification('Erro ao carregar pedido: ' + error.message, 'error');
            }
        };

        async function loadSupplierInfo() {
            const fornecedor = fornecedores.find(f => f.id === currentOrder.fornecedorId);
            const isDelayed = isOrderDelayed(currentOrder);

            // Debug para verificar dados do fornecedor
            console.log('🔍 Dados do fornecedor:', {
                fornecedorId: currentOrder.fornecedorId,
                fornecedor: fornecedor,
                cnpjCpf: fornecedor?.cnpjCpf,
                cnpj: fornecedor?.cnpj,
                cpf: fornecedor?.cpf
            });

            // Nome e status
            document.getElementById('supplierName').textContent = fornecedor?.razaoSocial || currentOrder.fornecedorNome || 'Fornecedor N/A';

            const statusElement = document.getElementById('supplierStatus');
            if (isDelayed) {
                statusElement.textContent = 'EM ATRASO';
                statusElement.className = 'supplier-status delayed';
            } else {
                statusElement.textContent = 'NO PRAZO';
                statusElement.className = 'supplier-status active';
            }

            // Detalhes - Corrigido o campo CNPJ/CPF
            const cnpjCpf = fornecedor?.cnpjCpf || fornecedor?.cnpj || fornecedor?.cpf || currentOrder.fornecedorCnpj || 'N/A';
            document.getElementById('supplierCnpj').textContent = cnpjCpf;
            document.getElementById('supplierContact').textContent = fornecedor?.telefone1 || fornecedor?.telefone || fornecedor?.email || 'N/A';
            document.getElementById('orderRequester').textContent = currentOrder.solicitante || currentOrder.criadoPor || 'N/A';

            // Datas - tratamento robusto para diferentes formatos
            let orderDate = null;
            if (currentOrder.dataCriacao) {
                if (currentOrder.dataCriacao.toDate) {
                    orderDate = currentOrder.dataCriacao.toDate();
                } else if (currentOrder.dataCriacao.seconds) {
                    orderDate = new Date(currentOrder.dataCriacao.seconds * 1000);
                } else {
                    orderDate = new Date(currentOrder.dataCriacao);
                }
            }

            let deliveryDate = null;
            if (currentOrder.dataEntregaPrevista) {
                if (currentOrder.dataEntregaPrevista.toDate) {
                    deliveryDate = currentOrder.dataEntregaPrevista.toDate();
                } else if (currentOrder.dataEntregaPrevista.seconds) {
                    deliveryDate = new Date(currentOrder.dataEntregaPrevista.seconds * 1000);
                } else {
                    deliveryDate = new Date(currentOrder.dataEntregaPrevista);
                }
            }

            document.getElementById('orderDate').textContent = orderDate ? formatDate(orderDate) : 'N/A';
            document.getElementById('deliveryDate').textContent = deliveryDate ? formatDate(deliveryDate) : 'N/A';

            // Status do prazo
            const statusPrazo = document.getElementById('deliveryStatus');
            if (isDelayed) {
                const diffDays = Math.ceil((new Date() - deliveryDate) / (1000 * 60 * 60 * 24));
                statusPrazo.innerHTML = `<span class="status-badge delayed">${diffDays} dias em atraso</span>`;
            } else {
                const diffDays = Math.ceil((deliveryDate - new Date()) / (1000 * 60 * 60 * 24));
                if (diffDays <= systemConfig.diasAlerteAtraso) {
                    statusPrazo.innerHTML = `<span class="status-badge warning">Vence em ${diffDays} dias</span>`;
                } else {
                    statusPrazo.innerHTML = `<span class="status-badge ok">No prazo</span>`;
                }
            }
        }

        function formatDate(date) {
            return date.toLocaleDateString('pt-BR');
        }

        function loadOrderSummary() {
            if (!currentOrder || !currentOrder.itens) return;

            let totalItems = currentOrder.itens.length;
            let totalValue = 0;
            let receivedValue = 0;
            let totalQuantity = 0;
            let receivedQuantity = 0;

            currentOrder.itens.forEach(item => {
                const itemTotal = item.quantidade * (item.valorUnitario || 0);
                const itemReceived = (item.quantidadeRecebida || 0) * (item.valorUnitario || 0);

                totalValue += itemTotal;
                receivedValue += itemReceived;
                totalQuantity += item.quantidade;
                receivedQuantity += (item.quantidadeRecebida || 0);
            });

            const pendingValue = totalValue - receivedValue;
            const progress = totalValue > 0 ? (receivedValue / totalValue * 100) : 0;

            // Atualizar interface
            document.getElementById('summaryTotalItems').textContent = totalItems;
            document.getElementById('summaryTotalValue').textContent = `R$ ${totalValue.toFixed(2)}`;
            document.getElementById('summaryReceivedValue').textContent = `R$ ${receivedValue.toFixed(2)}`;
            document.getElementById('summaryPendingValue').textContent = `R$ ${pendingValue.toFixed(2)}`;
            document.getElementById('summaryProgress').textContent = `${progress.toFixed(1)}%`;

            // Status do pedido
            const statusElement = document.getElementById('summaryStatus');
            if (progress === 0) {
                statusElement.innerHTML = '<span class="status-badge pending">PENDENTE</span>';
            } else if (progress < 100) {
                statusElement.innerHTML = '<span class="status-badge warning">PARCIAL</span>';
            } else {
                statusElement.innerHTML = '<span class="status-badge ok">COMPLETO</span>';
            }
        }

        function checkPartialDelivery() {
            if (!currentOrder || !currentOrder.itens) return;

            const hasPartialDelivery = currentOrder.itens.some(item =>
                (item.quantidadeRecebida || 0) > 0 &&
                (item.quantidadeRecebida || 0) < item.quantidade
            );

            const hasCompleteItems = currentOrder.itens.some(item =>
                (item.quantidadeRecebida || 0) >= item.quantidade
            );

            const alertElement = document.getElementById('partialDeliveryAlert');
            const messageElement = document.getElementById('partialDeliveryMessage');

            if (hasPartialDelivery || hasCompleteItems) {
                const totalReceived = currentOrder.itens.reduce((sum, item) =>
                    sum + (item.quantidadeRecebida || 0), 0
                );
                const totalOrdered = currentOrder.itens.reduce((sum, item) =>
                    sum + item.quantidade, 0
                );

                messageElement.innerHTML = `
                    Este pedido já teve entregas anteriores.
                    <strong>Recebido:</strong> ${totalReceived} de ${totalOrdered} itens totais.
                    <br><strong>Importante:</strong> Processe cada nota fiscal separadamente.
                `;
                alertElement.style.display = 'block';
            } else {
                alertElement.style.display = 'none';
            }
        }

        function loadOrderItems() {
            const tbody = document.getElementById('itemsTableBody');
            tbody.innerHTML = '';

            if (!currentOrder.itens) {
                tbody.innerHTML = '<tr><td colspan="12" style="text-align: center;">Nenhum item encontrado</td></tr>';
                return;
            }

            let hasExcessQuantity = false;
            let hasPriceDivergence = false;
            let totalItemsValue = 0;

            currentOrder.itens.forEach((item, index) => {
                // 🔍 Debug detalhado do item
                console.log(`🔍 Analisando item ${index}:`, {
                    produtoId: item.produtoId,
                    codigo: item.codigo,
                    descricao: item.descricao,
                    itemCompleto: item
                });

                // 🔍 Debug dos primeiros produtos para comparação
                if (index === 0 && produtos.length > 0) {
                    console.log(`🔍 Exemplo de produtos cadastrados:`, produtos.slice(0, 3).map(p => ({
                        id: p.id,
                        codigo: p.codigo,
                        descricao: p.descricao
                    })));
                }

                // Buscar produto por ID ou por código
                let produto = produtos.find(p => p.id === item.produtoId);

                // Se não encontrou por ID, tentar por código
                if (!produto && item.codigo) {
                    // Primeiro, verificar se o "código" é na verdade um ID
                    produto = produtos.find(p => p.id === item.codigo);
                    if (produto) {
                        console.log(`✅ ATENÇÃO: O campo 'codigo' contém um ID! Produto encontrado: ${produto.codigo} (${produto.descricao})`);
                        item.produtoId = produto.id;
                        item.codigo = produto.codigo; // Corrigir o código
                    } else {
                        // Buscar pelo código real
                        produto = produtos.find(p => p.codigo === item.codigo);
                        if (produto) {
                            item.produtoId = produto.id;
                            console.log(`✅ Produto encontrado pelo código ${item.codigo}, atualizando produtoId: ${produto.id}`);
                        }
                    }
                }

                const quantidadeRecebida = item.quantidadeRecebida || 0;
                const saldoPendente = item.quantidade - quantidadeRecebida;

                if (saldoPendente <= 0 && !systemConfig.permitirRecebimentoParcial) return;

                // ✅ Melhor tratamento para dados do produto
                const codigoProduto = produto?.codigo || item.codigo || `PROD-${item.produtoId?.substring(0,8) || 'N/A'}`;
                const descricaoProduto = produto?.descricao || item.descricao || `Produto ${codigoProduto}`;
                const unidadeProduto = produto?.unidade || item.unidade || 'UN';

                // ✅ Melhor tratamento para preços
                const precoOriginal = parseFloat(item.valorUnitario || item.precoUnitario || produto?.precoMedio || 0);
                const precoExibir = precoOriginal > 0 ? precoOriginal : 0;

                // ⚠️ Log para debug
                if (!produto) {
                    console.warn(`⚠️ Produto não encontrado para item:`, {
                        produtoId: item.produtoId,
                        codigo: item.codigo,
                        descricao: item.descricao,
                        valorUnitario: item.valorUnitario
                    });
                }

                const destinationInfo = getItemDestination(produto);

                const row = document.createElement('tr');
                // Verificar se há conversão de unidades
                let unidadeCompraInfo = '';
                let quantidadeInfo = '';

                if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
                    // Mostrar informação da conversão
                    const unidadeCompra = produto.unidadeSecundaria;
                    const quantidadeCompra = Math.max(0, saldoPendente) * produto.fatorConversao;

                    unidadeCompraInfo = `
                        <div style="font-size: 0.8rem; color: #6c757d; margin-top: 2px;">
                            Compra: ${unidadeCompra} (Fator: ${produto.fatorConversao})
                        </div>
                    `;

                    quantidadeInfo = `
                        <div style="font-size: 0.8rem; color: #28a745; margin-top: 2px;">
                            Qtd. Compra: ${quantidadeCompra.toFixed(3)} ${unidadeCompra}
                        </div>
                    `;
                } else {
                    quantidadeInfo = `<div style="font-size: 0.8rem; color: #6c757d;">Sem conversão</div>`;
                }

                row.innerHTML = `
                    <td>${codigoProduto}</td>
                    <td>${descricaoProduto}</td>
                    <td>
                        ${unidadeProduto}
                        ${unidadeCompraInfo}
                    </td>
                    <td style="text-align: center;">${item.quantidade}</td>
                    <td style="text-align: center;">${quantidadeRecebida}</td>
                    <td style="text-align: center;">
                        <strong>${Math.max(0, saldoPendente)}</strong>
                        ${quantidadeInfo}
                    </td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="qty_${index}"
                               max="${systemConfig.exigirAutorizacaoExcesso ? saldoPendente : saldoPendente * 2}"
                               min="0"
                               step="0.01"
                               value="${Math.max(0, saldoPendente)}"
                               style="width: 100px; text-align: center;"
                               onchange="validateQuantity(${index}, this.value); calculateItemTotal(${index})"
                               onkeyup="validateQuantity(${index}, this.value); calculateItemTotal(${index})"
                               title="${produto && produto.unidadeSecundaria ? `Quantidade em ${unidadeProduto} (será convertida automaticamente)` : `Quantidade em ${unidadeProduto}`}">
                        <div style="font-size: 0.7rem; color: #6c757d; margin-top: 2px;">
                            ${unidadeProduto}
                        </div>
                    </td>
                    <td style="text-align: right;">
                        <span class="price-original">R$ ${precoExibir.toFixed(2)}</span>
                    </td>
                    <td>
                        <input type="number"
                               class="form-control"
                               id="price_${index}"
                               step="0.01"
                               value="${precoExibir.toFixed(2)}"
                               style="width: 100px; text-align: right;"
                               onchange="validatePrice(${index}, this.value); calculateItemTotal(${index})"
                               onkeyup="validatePrice(${index}, this.value); calculateItemTotal(${index})"
                               ${precoExibir === 0 ? 'class="form-control warning" title="Preço não definido - favor informar"' : ''}
                               title="Preço unitário na unidade de compra">
                    </td>
                    <td style="text-align: right;">
                        <span id="itemTotal_${index}" class="item-total">R$ 0,00</span>
                    </td>
                    <td>
                        <span class="status-badge pending" id="status_${index}">PENDENTE</span>
                    </td>
                    <td>
                        <span class="status-badge ${destinationInfo.class}">
                            ${destinationInfo.text}
                        </span>
                    </td>
                `;
                tbody.appendChild(row);

                // Validar preço inicial e calcular total
                validatePrice(index, item.valorUnitario || 0);
                calculateItemTotal(index);
            });

            // Atualizar total geral dos itens
            updateTotalItemsValue();
        }

        // ===== CÁLCULO DO TOTAL POR ITEM =====
        window.calculateItemTotal = function(index) {
            const qtyInput = document.getElementById(`qty_${index}`);
            const priceInput = document.getElementById(`price_${index}`);
            const totalSpan = document.getElementById(`itemTotal_${index}`);

            if (!qtyInput || !priceInput || !totalSpan) return;

            const quantity = parseFloat(qtyInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;

            // Buscar dados do produto para conversão
            const item = currentOrder.itens[index];
            const produto = produtos.find(p => p.id === item.produtoId || p.codigo === item.codigo);

            // Calcular quantidade na unidade de compra para o valor total
            let quantidadeCompra = quantity;
            if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
                quantidadeCompra = quantity * produto.fatorConversao;
            }

            const total = quantidadeCompra * price;

            totalSpan.textContent = `R$ ${total.toFixed(2)}`;

            // Mostrar informação da conversão no total se aplicável
            if (produto && produto.unidadeSecundaria && produto.fatorConversao && quantity > 0) {
                totalSpan.title = `${quantity} ${produto.unidade} = ${quantidadeCompra.toFixed(3)} ${produto.unidadeSecundaria} × R$ ${price.toFixed(2)}`;
            }

            // Atualizar cor baseado na quantidade
            if (quantity > 0) {
                totalSpan.style.fontWeight = 'bold';
                totalSpan.style.color = '#28a745';
            } else {
                totalSpan.style.fontWeight = 'normal';
                totalSpan.style.color = '#6c757d';
            }

            // Atualizar total geral
            updateTotalItemsValue();
        };

        function updateTotalItemsValue() {
            let totalValue = 0;

            if (currentOrder && currentOrder.itens) {
                currentOrder.itens.forEach((item, index) => {
                    const qtyInput = document.getElementById(`qty_${index}`);
                    const priceInput = document.getElementById(`price_${index}`);

                    if (qtyInput && priceInput) {
                        const quantity = parseFloat(qtyInput.value) || 0;
                        const price = parseFloat(priceInput.value) || 0;

                        // Buscar dados do produto para conversão
                        const produto = produtos.find(p => p.id === item.produtoId || p.codigo === item.codigo);

                        // Calcular quantidade na unidade de compra para o valor total
                        let quantidadeCompra = quantity;
                        if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
                            quantidadeCompra = quantity * produto.fatorConversao;
                        }

                        totalValue += quantidadeCompra * price;
                    }
                });
            }

            const totalElement = document.getElementById('totalItemsValue');
            if (totalElement) {
                totalElement.textContent = `R$ ${totalValue.toFixed(2)}`;            }

            // Recalcular total da nota fiscal se necessário
            calculateTotalInvoice();
        }

        window.validateQuantity = function(index, quantity) {
            const item = currentOrder.itens[index];
            const quantidadeRecebida = item.quantidadeRecebida || 0;
            const saldoPendente = item.quantidade - quantidadeRecebida;
            const qtyInput = document.getElementById(`qty_${index}`);
            const statusElement = document.getElementById(`status_${index}`);
            const authButton = document.getElementById('authButton');

            const qty = parseFloat(quantity) || 0;
            const tolerance = (saldoPendente * systemConfig.toleranciaRecebimento / 100);
            const maxAllowed = saldoPendente + tolerance;

            // Resetar classes
            qtyInput.classList.remove('error', 'warning');

            if (qty > saldoPendente) {
                const excess = qty - saldoPendente;
                const excessPercent = (excess / saldoPendente) * 100;

                if (excessPercent > systemConfig.toleranciaRecebimento) {
                    if (systemConfig.exigirAutorizacaoExcesso) {
                        qtyInput.classList.add('error');
                        statusElement.innerHTML = '<span class="status-badge delayed">EXCESSO - AUTORIZAÇÃO</span>';
                        authButton.style.display = 'inline-flex';
                        return false;
                    } else {
                        qtyInput.classList.add('warning');
                        statusElement.innerHTML = '<span class="status-badge warning">EXCESSO</span>';
                    }
                } else {
                    qtyInput.classList.add('warning');
                    statusElement.innerHTML = '<span class="status-badge warning">TOLERÂNCIA</span>';
                }
            } else if (qty < saldoPendente && !systemConfig.permitirRecebimentoParcial) {
                qtyInput.classList.add('error');
                statusElement.innerHTML = '<span class="status-badge delayed">PARCIAL BLOQUEADO</span>';
                return false;
            } else {
                statusElement.innerHTML = '<span class="status-badge ok">OK</span>';
            }

            // Atualizar resumo em tempo real
            updateCurrentReceiptSummary();

            return true;
        };

        window.validatePrice = function(index, price) {
            const item = currentOrder.itens[index];
            const priceInput = document.getElementById(`price_${index}`);
            const statusElement = document.getElementById(`status_${index}`);

            const currentPrice = parseFloat(price) || 0;
            const originalPrice = item.valorUnitario || 0;
            const priceDiff = Math.abs(currentPrice - originalPrice);
            const pricePercent = originalPrice > 0 ? (priceDiff / originalPrice) * 100 : 0;

            // Resetar classes
            priceInput.classList.remove('error', 'warning');

            if (pricePercent > systemConfig.toleranciaPreco) {
                if (systemConfig.bloquearPrecoDivergente) {
                    priceInput.classList.add('error');
                    statusElement.innerHTML = '<span class="status-badge delayed">PREÇO DIVERGENTE</span>';
                    return false;
                } else {
                    priceInput.classList.add('warning');
                    statusElement.innerHTML = '<span class="status-badge warning">PREÇO ALTERADO</span>';
                }
            }

            // Atualizar visualização do preço
            const priceValidation = priceInput.parentElement.previousElementSibling.querySelector('.price-validation');
            if (priceValidation) {
                const priceCurrentElement = priceValidation.querySelector('.price-current') || document.createElement('span');
                priceCurrentElement.className = 'price-current';
                priceCurrentElement.textContent = `R$ ${currentPrice.toFixed(2)}`;

                if (pricePercent > systemConfig.toleranciaPreco) {
                    priceCurrentElement.classList.add(systemConfig.bloquearPrecoDivergente ? 'error' : 'warning');
                } else {
                    priceCurrentElement.classList.add('ok');
                }

                if (!priceValidation.querySelector('.price-current')) {
                    priceValidation.appendChild(document.createTextNode(' → '));
                    priceValidation.appendChild(priceCurrentElement);
                }
            }

            return true;
        };

        function getItemDestination(produto) {
            if (systemConfig.controleQualidade && systemConfig.armazemQualidade) {
                const needsInspection = checkIfNeedsInspection(produto);

                if (needsInspection) {
                    return {
                        text: 'QUALIDADE',
                        class: 'quality'
                    };
                }
            }

            return {
                text: 'ESTOQUE',
                class: 'stock'
            };
        }

        function checkIfNeedsInspection(produto) {
            if (!produto) return false;

            switch (systemConfig.inspecaoRecebimento) {
                case 'todos':
                    return true;
                case 'criticos':
                    return produto.critico === true || produto.tipo === 'CRITICO';
                case 'manual':
                    return produto.inspecaoObrigatoria === true;
                default:
                    return false;
            }
        }

        async function loadDeliveryHistory() {
            if (!systemConfig.controlarHistoricoEntregas) return;

            const historySection = document.getElementById('deliveryHistory');
            const historyContent = document.getElementById('deliveryHistoryContent');

            // Buscar histórico de recebimentos anteriores
            try {
                // Usar query mais simples para evitar erro de índice
                const movimentacoesQuery = query(
                    collection(db, "movimentacoesEstoque"),
                    where("pedidoCompraId", "==", currentOrder.id),
                    where("tipo", "==", "ENTRADA")
                );

                const movimentacoesSnap = await getDocs(movimentacoesQuery);
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                if (movimentacoes.length > 0) {
                    historyContent.innerHTML = movimentacoes.map(mov => `
                        <div class="delivery-item">
                            <div>
                                <strong>NF: ${mov.numeroNF}</strong> - ${formatDate(mov.dataMovimentacao.toDate())}
                                <br><small>${mov.itens?.length || 0} itens recebidos</small>
                            </div>
                            <div>
                                <span class="status-badge ok">R$ ${(mov.valorTotal || 0).toFixed(2)}</span>
                            </div>
                        </div>
                    `).join('');

                    historySection.style.display = 'block';
                } else {
                    historySection.style.display = 'none';
                }
            } catch (error) {
                console.error('Erro ao carregar histórico:', error);
                historySection.style.display = 'none';
            }
        }

        function updateCurrentReceiptSummary() {
            if (!currentOrder || !currentOrder.itens) return;

            let currentReceiptValue = 0;
            let currentReceiptItems = 0;

            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                const priceInput = document.getElementById(`price_${index}`);

                if (qtyInput && priceInput) {
                    const qty = parseFloat(qtyInput.value) || 0;
                    const price = parseFloat(priceInput.value) || 0;

                    if (qty > 0) {
                        currentReceiptValue += qty * price;
                        currentReceiptItems++;
                    }
                }
            });

            // Atualizar campo de valor da NF se estiver vazio
            const invoiceValueInput = document.getElementById('invoiceValue');
            if (invoiceValueInput && !invoiceValueInput.value && currentReceiptValue > 0) {
                invoiceValueInput.value = currentReceiptValue.toFixed(2);
            }

            // Mostrar resumo da entrega atual
            const receiptSection = document.getElementById('receiptSection');
            let currentSummary = receiptSection.querySelector('.current-receipt-summary');

            if (!currentSummary && currentReceiptValue > 0) {
                currentSummary = document.createElement('div');
                currentSummary.className = 'current-receipt-summary alert alert-info';
                currentSummary.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    <div>
                        <strong>Resumo da Entrega Atual:</strong>
                        <span id="currentReceiptSummaryText"></span>
                    </div>
                `;
                receiptSection.appendChild(currentSummary);
            }

            if (currentSummary) {
                const summaryText = document.getElementById('currentReceiptSummaryText');
                if (currentReceiptValue > 0) {
                    summaryText.textContent = `${currentReceiptItems} itens - Valor: R$ ${currentReceiptValue.toFixed(2)}`;
                    currentSummary.style.display = 'block';
                } else {
                    currentSummary.style.display = 'none';
                }
            }
        }

        // ===== FUNÇÕES AUXILIARES =====
        function cleanUndefinedFields(obj) {
            if (obj === null || obj === undefined) {
                return null;
            }

            if (Array.isArray(obj)) {
                return obj.map(item => cleanUndefinedFields(item)).filter(item => item !== null && item !== undefined);
            }

            if (typeof obj === 'object') {
                const cleaned = {};
                for (const [key, value] of Object.entries(obj)) {
                    if (value !== undefined && value !== null) {
                        const cleanedValue = cleanUndefinedFields(value);
                        if (cleanedValue !== undefined && cleanedValue !== null) {
                            cleaned[key] = cleanedValue;
                        }
                    }
                }
                return cleaned;
            }

            return obj;
        }

        // Importar MaterialEntryService
        import { MaterialEntryService } from './services/material-entry-service.js';

        // ===== PROCESSAMENTO DO RECEBIMENTO =====
        window.processReceipt = async function() {
            try {
                // Validar se há pedido selecionado
                if (!currentOrder || !currentOrder.id) {
                    showNotification('Nenhum pedido selecionado!', 'error');
                    return;
                }

                // Validar se há itens no pedido
                if (!currentOrder.itens || !Array.isArray(currentOrder.itens) || currentOrder.itens.length === 0) {
                    showNotification('Pedido não possui itens válidos!', 'error');
                    return;
                }

                // Validar se os produtos estão carregados
                if (!produtos || produtos.length === 0) {
                    showNotification('Produtos não carregados. Recarregue a página.', 'error');
                    return;
                }

                // Verificar se todos os itens têm produtoId ou podem ser encontrados pelo código
                const itensProblematicos = [];
                currentOrder.itens.forEach(item => {
                    let produtoId = item.produtoId;

                    // Se não tem produtoId, tentar encontrar pelo código
                    if (!produtoId && item.codigo) {
                        // Primeiro, verificar se o "código" é na verdade um ID
                        let produtoEncontrado = produtos.find(p => p.id === item.codigo);
                        if (produtoEncontrado) {
                            console.log(`🔧 Corrigindo: campo 'codigo' contém ID. Produto: ${produtoEncontrado.codigo}`);
                            item.produtoId = produtoEncontrado.id;
                            item.codigo = produtoEncontrado.codigo; // Corrigir o código
                        } else {
                            // Buscar pelo código real
                            produtoEncontrado = produtos.find(p => p.codigo === item.codigo);
                            if (produtoEncontrado) {
                                item.produtoId = produtoEncontrado.id; // Atualizar para próximas operações
                            } else {
                                itensProblematicos.push(item.codigo || 'Código não informado');
                            }
                        }
                    } else if (!produtoId) {
                        itensProblematicos.push(item.codigo || 'Código não informado');
                    }
                });

                if (itensProblematicos.length > 0) {
                    showNotification(`Produtos não encontrados no cadastro: ${itensProblematicos.join(', ')}`, 'error');
                    return;
                }

                if (!validateReceiptData()) {
                    return;
                }

                showNotification('Processando recebimento...', 'info');

                // Coletar dados do formulário
                const tesSelected = document.getElementById('tesSelect').value;
                const tesConfig = TESConfig.getTESConfig(tesSelected);
                const invoiceNumber = document.getElementById('invoiceNumber').value?.trim() || '';
                const invoiceDate = document.getElementById('invoiceDate').value;
                const invoiceValue = document.getElementById('invoiceValue').value;
                const totalInvoiceValue = document.getElementById('totalInvoiceValue').value;
                const warehouseSelect = document.getElementById('warehouseSelect').value;

                if (!invoiceNumber || !invoiceDate || !invoiceValue || !totalInvoiceValue || !warehouseSelect) {
                    showNotification('Preencha todos os campos obrigatórios!', 'error');
                    return;
                }

                // Processar cada item usando MaterialEntryService
                const processedItems = [];
                let hasItemsToReceive = false;

                for (let i = 0; i < currentOrder.itens.length; i++) {
                    const item = currentOrder.itens[i];
                    const qtyInput = document.getElementById(`qty_${i}`);
                    const priceInput = document.getElementById(`price_${i}`);

                    if (qtyInput && parseFloat(qtyInput.value || '0') > 0) {
                        hasItemsToReceive = true;
                        const quantidadeRecebidaNF = parseFloat(qtyInput.value || '0'); // Quantidade na NF (unidade de compra)
                        const precoUnitario = parseFloat(priceInput?.value || item?.valorUnitario || '0');

                        // Buscar dados do produto para conversão
                        const produto = produtos.find(p => p.id === item.produtoId || p.codigo === item.codigo);

                        // Calcular conversão de unidades
                        let quantidadeEstoque = quantidadeRecebidaNF; // Quantidade para o estoque (unidade interna)
                        let unidadeEstoque = item.unidade || 'UN';
                        let unidadeCompra = item.unidadeCompra || item.unidade || 'UN';

                        // Se o produto tem conversão configurada
                        if (produto && produto.unidadeSecundaria && produto.fatorConversao) {
                            // Se a compra foi feita na unidade secundária, converter para a unidade principal
                            if (unidadeCompra === produto.unidadeSecundaria) {
                                quantidadeEstoque = quantidadeRecebidaNF / produto.fatorConversao;
                                unidadeEstoque = produto.unidade;

                                console.log(`🔄 Conversão de unidade - Item ${item.codigo}:`, {
                                    quantidadeNF: quantidadeRecebidaNF,
                                    unidadeCompra: unidadeCompra,
                                    quantidadeEstoque: quantidadeEstoque.toFixed(3),
                                    unidadeEstoque: unidadeEstoque,
                                    fatorConversao: produto.fatorConversao
                                });
                            }
                        }

                        // Determinar produtoId correto
                        let produtoId = item.produtoId;
                        let codigoCorreto = item.codigo;

                        // Se não tem produtoId, tentar encontrar pelo código
                        if (!produtoId && item.codigo) {
                            // Primeiro, verificar se o "código" é na verdade um ID
                            let produtoEncontrado = produtos.find(p => p.id === item.codigo);
                            if (produtoEncontrado) {
                                produtoId = produtoEncontrado.id;
                                codigoCorreto = produtoEncontrado.codigo;
                                console.log(`🔧 Campo 'codigo' continha ID. Corrigido: ${codigoCorreto}`);
                            } else {
                                // Buscar pelo código real
                                produtoEncontrado = produtos.find(p => p.codigo === item.codigo);
                                if (produtoEncontrado) {
                                    produtoId = produtoEncontrado.id;
                                    console.log(`✅ ProdutoId encontrado pelo código ${item.codigo}: ${produtoId}`);
                                }
                            }
                        }

                        // Se ainda não tem produtoId, pular este item
                        if (!produtoId) {
                            console.error(`❌ Não foi possível determinar produtoId para item:`, {
                                codigo: item.codigo,
                                descricao: item.descricao,
                                itemCompleto: item
                            });
                            showNotification(`Erro: Produto ${item.codigo} não encontrado no cadastro`, 'error');
                            return;
                        }

                        // Preparar dados para MaterialEntryService
                        const entryData = {
                            produtoId: produtoId,
                            codigo: codigoCorreto,
                            descricao: item.descricao,
                            quantidade: quantidadeEstoque, // Quantidade convertida para unidade interna
                            quantidadeNF: quantidadeRecebidaNF, // Quantidade original da NF
                            unidade: unidadeEstoque, // Unidade interna
                            unidadeNF: unidadeCompra, // Unidade da NF
                            numeroNF: invoiceNumber,
                            loteFornecedor: item.loteFornecedor || '',
                            loteInterno: item.loteInterno || '',
                            armazemId: warehouseSelect,
                            recebimentoId: null, // Será preenchido após salvar recebimento
                            conversaoAplicada: produto && produto.unidadeSecundaria && unidadeCompra === produto.unidadeSecundaria,
                            tes: tesSelected || '001', // TES selecionada no formulário
                            usuario: (currentUser && currentUser.nome) ? currentUser.nome : 'usuario_atual'
                        };

                        // Processar entrada usando o serviço inteligente
                        try {
                            const result = await MaterialEntryService.processEntry(entryData);
                            processedItems.push({
                                ...entryData,
                                precoUnitario: precoUnitario,
                                valorTotal: quantidadeRecebidaNF * precoUnitario, // Valor baseado na quantidade da NF
                                processResult: result
                            });

                            console.log(`✅ Item ${item.codigo} processado:`, result.message);

                            // Atualizar quantidade recebida no pedido (usar quantidade do estoque)
                            item.quantidadeRecebida = (item.quantidadeRecebida || 0) + quantidadeEstoque;

                        } catch (error) {
                            console.error(`❌ Erro ao processar item ${item.codigo}:`, error);
                            showNotification(`Erro ao processar item ${item.codigo}: ${error.message}`, 'error');
                            return;
                        }
                    }
                }

                if (!hasItemsToReceive) {
                    showNotification('Nenhum item para receber!', 'warning');
                    return;
                }

                // Salvar registro do recebimento
                // Debug: verificar dados antes de criar receiptData
                console.log('🔍 Dados para receiptData:', {
                    currentOrder: currentOrder.id,
                    invoiceNumber,
                    invoiceDate,
                    invoiceValue,
                    totalInvoiceValue,
                    tesSelected,
                    tesConfig,
                    warehouseSelect,
                    currentUser,
                    processedItems: processedItems.length
                });

                const receiptData = {
                    pedidoCompraId: currentOrder.id,
                    numeroNotaFiscal: invoiceNumber,
                    serieNotaFiscal: document.getElementById('invoiceSeries')?.value?.trim() || '1',
                    dataNotaFiscal: Timestamp.fromDate(new Date(invoiceDate)),
                    valorProdutos: parseFloat(invoiceValue) || 0,
                    valorFrete: parseFloat(document.getElementById('freightValue')?.value || '0') || 0,
                    valorSeguro: parseFloat(document.getElementById('insuranceValue')?.value || '0') || 0,
                    outrasDesp: parseFloat(document.getElementById('otherExpenses')?.value || '0') || 0,
                    valorTotalNF: parseFloat(totalInvoiceValue) || 0,
                    transportadora: document.getElementById('transportCompany')?.value?.trim() || '',
                    observacoes: document.getElementById('receiptNotes')?.value?.trim() || '',
                    tes: tesSelected || '001',
                    tipoDocumento: 'COMPRA',
                    numeroDocumento: invoiceNumber,
                    cfop: (tesConfig && tesConfig.cfop) ? tesConfig.cfop : '1102',
                    armazemDestinoId: warehouseSelect,
                    dataRecebimento: Timestamp.now(),
                    usuarioRecebimento: (currentUser && currentUser.nome) ? currentUser.nome : 'usuario_atual',
                    itens: processedItems.map(item => ({
                        produtoId: item.produtoId || '',
                        codigo: item.codigo || '',
                        descricao: item.descricao || '',
                        quantidadeRecebida: item.quantidade || 0, // Quantidade no estoque (unidade interna)
                        quantidadeNF: item.quantidadeNF || 0, // Quantidade da NF (unidade de compra)
                        precoUnitario: item.precoUnitario || 0,
                        valorTotal: item.valorTotal || 0,
                        unidade: item.unidade || 'UN', // Unidade interna
                        unidadeNF: item.unidadeNF || 'UN', // Unidade da NF
                        conversaoAplicada: item.conversaoAplicada || false,
                        destinoFinal: item.processResult?.type || 'DIRECT_STOCK'
                    }))
                };

                // Debug: verificar se há campos undefined no receiptData
                console.log('🔍 receiptData antes da limpeza:', receiptData);

                // Limpar campos undefined antes de salvar
                const cleanReceiptData = cleanUndefinedFields(receiptData);

                // Debug: verificar se ainda há campos undefined
                console.log('📋 Dados do recebimento (limpos):', cleanReceiptData);

                // Salvar recebimento no Firebase
                const recebimentoRef = await addDoc(collection(db, "recebimentos"), cleanReceiptData);
                console.log('✅ Recebimento salvo:', recebimentoRef.id);

                // Atualizar status do pedido
                await updateOrderStatus();

                // Atualizar dashboard e lista de pedidos
                await updateDashboard();
                populateOrderSelect();

                showNotification('Recebimento processado com sucesso!', 'success');

                // Limpar formulário após sucesso
                setTimeout(() => {
                    clearReceiptForm();
                    if (currentOrder.status === 'RECEBIDO') {
                        document.getElementById('orderSelect').value = '';
                        currentOrder = null;
                        document.getElementById('supplierSection').style.display = 'none';
                        document.getElementById('receiptSection').style.display = 'none';
                        document.getElementById('orderSummary').style.display = 'none';
                        document.getElementById('itemsSection').style.display = 'none';
                        showNotification('Pedido totalmente recebido!', 'success');
                    }
                }, 2000);

            } catch (error) {
                console.error('❌ Erro ao processar recebimento:', error);
                showNotification('Erro ao processar recebimento: ' + error.message, 'error');
            }
        };

        // ===== FUNÇÕES AUXILIARES DE PROCESSAMENTO =====
        // Nota: Movimentações de estoque são processadas pelo MaterialEntryService

        async function updateOrderStatus() {
            console.log('🔍 Verificando status do pedido...');
            console.log('📋 Itens do pedido:', currentOrder.itens.map(item => ({
                codigo: item.codigo,
                quantidade: item.quantidade,
                quantidadeRecebida: item.quantidadeRecebida || 0,
                percentualRecebido: ((item.quantidadeRecebida || 0) / item.quantidade * 100).toFixed(1) + '%'
            })));

            // Verificar se todos os itens foram recebidos
            const allItemsReceived = currentOrder.itens.every(item => {
                const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                const qtdPedida = parseFloat(item.quantidade || 0);
                const isComplete = qtdRecebida >= qtdPedida;
                console.log(`📦 Item ${item.codigo}: ${qtdRecebida}/${qtdPedida} - ${isComplete ? 'COMPLETO' : 'PENDENTE'}`);
                return isComplete;
            });

            const hasPartialReceived = currentOrder.itens.some(item => {
                const qtdRecebida = parseFloat(item.quantidadeRecebida || 0);
                return qtdRecebida > 0;
            });

            let newStatus = currentOrder.status;
            if (allItemsReceived) {
                newStatus = 'RECEBIDO';
                console.log('✅ Todos os itens foram recebidos - Status: RECEBIDO');
            } else if (hasPartialReceived) {
                newStatus = 'RECEBIDO_PARCIAL';
                console.log('🟡 Recebimento parcial - Status: RECEBIDO_PARCIAL');
            } else {
                console.log('⏳ Nenhum item recebido - Status mantido:', currentOrder.status);
            }

            console.log(`📊 Status atual: ${currentOrder.status} → Novo status: ${newStatus}`);

            if (newStatus !== currentOrder.status) {
                try {
                    await updateDoc(doc(db, "pedidosCompra", currentOrder.id), {
                        status: newStatus,
                        itens: currentOrder.itens,
                        dataUltimaAtualizacao: Timestamp.now()
                    });

                    currentOrder.status = newStatus;
                    console.log('✅ Status do pedido atualizado no Firebase para:', newStatus);
                } catch (error) {
                    console.error('❌ Erro ao atualizar status do pedido:', error);
                    throw error;
                }
            } else {
                console.log('ℹ️ Status não alterado, nenhuma atualização necessária');
            }
        }

        function clearReceiptForm() {
            // Limpar campos da nota fiscal
            document.getElementById('invoiceNumber').value = '';
            document.getElementById('invoiceSeries').value = '';
            document.getElementById('invoiceDate').value = '';
            document.getElementById('invoiceValue').value = '';
            document.getElementById('freightValue').value = '0';
            document.getElementById('insuranceValue').value = '0';
            document.getElementById('otherExpenses').value = '0';
            document.getElementById('totalInvoiceValue').value = '';
            document.getElementById('transportCompany').value = '';
            document.getElementById('receiptNotes').value = '';

            // Limpar TES e CFOP
            document.getElementById('tesSelect').value = '';
            document.getElementById('cfopCode').value = '';
            document.getElementById('tesHint').textContent = 'Selecione o tipo de entrada para definir tratamento fiscal';
            document.getElementById('tesHint').style.color = '#6c757d';

            // Limpar quantidades dos itens
            if (currentOrder && currentOrder.itens) {
                currentOrder.itens.forEach((item, index) => {
                    const qtyInput = document.getElementById(`qty_${index}`);
                    const totalSpan = document.getElementById(`itemTotal_${index}`);
                    if (qtyInput) qtyInput.value = '';
                    if (totalSpan) totalSpan.textContent = 'R$ 0,00';
                });
            }

            // Limpar total dos itens
            const totalElement = document.getElementById('totalItemsValue');
            if (totalElement) totalElement.textContent = 'R$ 0,00';

            // Ocultar seções
            document.getElementById('supplierSection').style.display = 'none';
            document.getElementById('receiptSection').style.display = 'none';
            document.getElementById('orderSummary').style.display = 'none';
            document.getElementById('itemsSection').style.display = 'none';

            // Resetar seleção
            document.getElementById('orderSelect').value = '';
            currentOrder = null;
        }

        window.requestAuthorization = function() {
            showNotification('Solicitação de autorização enviada para aprovação', 'info');
            // Implementar lógica de autorização
        };

        function validateReceiptData() {
            const invoiceNumber = document.getElementById('invoiceNumber').value.trim();
            const invoiceDate = document.getElementById('invoiceDate').value;
            const invoiceValue = document.getElementById('invoiceValue').value;
            const totalInvoiceValue = document.getElementById('totalInvoiceValue').value;
            const warehouse = document.getElementById('warehouseSelect').value;
            const tesSelected = document.getElementById('tesSelect').value;

            if (!invoiceNumber) {
                showNotification('Número da nota fiscal é obrigatório', 'warning');
                document.getElementById('invoiceNumber').focus();
                return false;
            }

            if (!tesSelected) {
                showNotification('Selecione o TES (Tipo de Entrada)', 'warning');
                document.getElementById('tesSelect').focus();
                return false;
            }

            if (!invoiceDate) {
                showNotification('Data da nota fiscal é obrigatória', 'warning');
                document.getElementById('invoiceDate').focus();
                return false;
            }

            if (!invoiceValue || parseFloat(invoiceValue) <= 0) {
                showNotification('Valor dos produtos deve ser maior que zero', 'warning');
                document.getElementById('invoiceValue').focus();
                return false;
            }

            if (!totalInvoiceValue || parseFloat(totalInvoiceValue) <= 0) {
                showNotification('Valor total da NF deve ser maior que zero', 'warning');
                document.getElementById('totalInvoiceValue').focus();
                return false;
            }

            if (!warehouse) {
                showNotification('Selecione o armazém de destino', 'warning');
                document.getElementById('warehouseSelect').focus();
                return false;
            }

            // Validar se a data da NF não é futura
            const nfDate = new Date(invoiceDate);
            const today = new Date();
            today.setHours(23, 59, 59, 999);

            if (nfDate > today) {
                showNotification('Data da nota fiscal não pode ser futura', 'warning');
                document.getElementById('invoiceDate').focus();
                return false;
            }

            // Validar itens
            let hasValidItems = false;
            let hasErrors = false;

            currentOrder.itens.forEach((item, index) => {
                const qtyInput = document.getElementById(`qty_${index}`);
                const priceInput = document.getElementById(`price_${index}`);

                if (qtyInput && parseFloat(qtyInput.value) > 0) {
                    hasValidItems = true;

                    if (qtyInput.classList.contains('error') || priceInput.classList.contains('error')) {
                        hasErrors = true;
                    }
                }
            });

            if (!hasValidItems) {
                showNotification('Informe a quantidade a receber para pelo menos um item', 'warning');
                return false;
            }

            if (hasErrors) {
                showNotification('Corrija os erros nos itens antes de processar', 'error');
                return false;
            }

            return true;
        }

        // Função para aplicar conversão de unidades no recebimento
        function applyUnitConversion(item, quantidadeRecebida) {
            const produto = produtos.find(p => p.codigo === item.codigo);

            if (!produto) {
                console.warn('Produto não encontrado:', item.codigo);
                return {
                    quantidadeInterna: quantidadeRecebida,
                    unidadeInterna: item.unidade || 'UN',
                    quantidadeOriginal: quantidadeRecebida,
                    unidadeOriginal: item.unidade || 'UN',
                    temConversao: false
                };
            }

            // Verificar se há conversão configurada no cadastro do produto
            const hasConversion = produto.unidadeSecundaria && produto.fatorConversao && produto.fatorConversao > 0;

            if (!hasConversion) {
                return {
                    quantidadeInterna: quantidadeRecebida,
                    unidadeInterna: produto.unidade,
                    quantidadeOriginal: quantidadeRecebida,
                    unidadeOriginal: produto.unidade,
                    temConversao: false
                };
            }

            // A unidade do item vem da cotação/pedido (pode ser a secundária)
            const unidadeRecebimento = item.unidade || produto.unidade;
            let quantidadeInterna = quantidadeRecebida;

            // Se o recebimento está na unidade secundária, converter para a unidade principal (interna)
            if (unidadeRecebimento === produto.unidadeSecundaria) {
                // Conversão: unidade secundária -> unidade principal
                // Se 1 PC = 2.5 KG, então para converter KG para PC: KG / 2.5
                quantidadeInterna = quantidadeRecebida / produto.fatorConversao;

                console.log('🔄 Conversão de unidade aplicada:', {
                    produto: produto.codigo,
                    quantidadeRecebida: quantidadeRecebida,
                    unidadeRecebimento: produto.unidadeSecundaria,
                    quantidadeInterna: quantidadeInterna.toFixed(6),
                    unidadeInterna: produto.unidade,
                    fatorConversao: produto.fatorConversao,
                    calculo: `${quantidadeRecebida} ${produto.unidadeSecundaria} ÷ ${produto.fatorConversao} = ${quantidadeInterna.toFixed(6)} ${produto.unidade}`
                });
            } else {
                console.log('✅ Mesma unidade - sem conversão:', {
                    produto: produto.codigo,
                    unidade: produto.unidade,
                    quantidade: quantidadeRecebida
                });
            }

            return {
                quantidadeInterna: quantidadeInterna,
                unidadeInterna: produto.unidade,
                quantidadeOriginal: quantidadeRecebida,
                unidadeOriginal: unidadeRecebimento,
                temConversao: hasConversion && (unidadeRecebimento === produto.unidadeSecundaria),
                fatorConversao: produto.fatorConversao,
                observacaoConversao: hasConversion && (unidadeRecebimento === produto.unidadeSecundaria) ? 
                    `Convertido: ${quantidadeRecebida} ${produto.unidadeSecundaria} → ${quantidadeInterna.toFixed(6)} ${produto.unidade}` : null
            };
        }
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>