Análise do Problema
Você está enfrentando um problema de validação de quantidade onde o sistema está exibindo a mensagem "O valor mínimo (0,001) tem de ser inferior ao valor máximo (0)" mesmo para itens que não estão selecionados (checkbox não marcado).

Solução Proposta
Vou modificar a função validateQuantity() para:

Não aplicar validação se o checkbox não estiver marcado

Permitir transferência parcial mesmo quando o saldo é zero (com aviso)

Melhorar a lógica de validação

Código Atualizado:
javascript
window.validateQuantity = function(produtoId, quantidadeRestante, quantidadeMaxima) {
    const input = document.getElementById(`quantity_${produtoId}`);
    const checkbox = document.getElementById(`material_${produtoId}`);
    const value = parseFloat(input.value) || 0;
    const produto = produtos.find(p => p.id === produtoId);

    // 1. Não validar se o checkbox não estiver marcado
    if (!checkbox || !checkbox.checked) {
        input.style.borderColor = '#e0e6ed';
        input.title = '';
        input.setAttribute('min', '0.001');
        input.setAttribute('max', '999999'); // Valor alto para evitar conflito
        return true;
    }

    // 2. Configurar atributos básicos
    input.setAttribute('min', '0.001');
    input.setAttribute('max', Math.max(0.001, quantidadeMaxima || 0.001).toFixed(3));
    input.setAttribute('step', '0.001');

    // 3. Validações apenas para itens selecionados
    if (value <= 0) {
        input.style.borderColor = '#dc3545';
        input.title = 'Quantidade deve ser maior que zero';
        input.value = '0.001'; // Corrige automaticamente
        return false;
    }

    // 4. Verificar se há saldo disponível (apenas aviso, não bloqueia)
    const sourceId = document.getElementById('sourceWarehouse').value;
    if (sourceId) {
        const sourceEstoque = estoques.find(e => e.produtoId === produtoId && e.armazemId === sourceId);
        const saldoDisponivel = sourceEstoque ? sourceEstoque.saldo : 0;

        if (value > saldoDisponivel) {
            input.style.borderColor = '#ffc107';
            input.title = `Atenção: Saldo insuficiente (Disponível: ${saldoDisponivel.toFixed(3)} ${produto?.unidade || 'UN'})`;
            
            // Sugere ajuste automático se confirmado
            if (confirm(`Saldo insuficiente para ${produto?.codigo || produtoId} (${saldoDisponivel.toFixed(3)} ${produto?.unidade || 'UN'} disponíveis). Deseja usar o saldo disponível?`)) {
                input.value = saldoDisponivel.toFixed(3);
                input.style.borderColor = '#28a745';
                input.title = `Quantidade ajustada: ${saldoDisponivel.toFixed(3)} ${produto?.unidade || 'UN'}`;
                return true;
            }
            return false;
        }
    }

    // 5. Verificar necessidade restante (apenas aviso)
    if (value > quantidadeRestante) {
        input.style.borderColor = '#e74c3c';
        input.title = `Atenção: Quantidade excede necessidade restante (${quantidadeRestante.toFixed(3)} ${produto?.unidade || 'UN'})`;
        
        // Sugere ajuste automático se confirmado
        if (confirm(`Quantidade excede necessidade restante para ${produto?.codigo || produtoId}. Deseja ajustar para ${quantidadeRestante.toFixed(3)} ${produto?.unidade || 'UN'}?`)) {
            input.value = quantidadeRestante.toFixed(3);
            input.style.borderColor = '#28a745';
            input.title = `Quantidade ajustada: ${quantidadeRestante.toFixed(3)} ${produto?.unidade || 'UN'}`;
            return true;
        }
        return false;
    }

    // 6. Quantidade válida
    input.style.borderColor = '#28a745';
    input.title = `Quantidade válida: ${value.toFixed(3)} ${produto?.unidade || 'UN'}`;
    return true;
};
Mudanças Principais:
Validação condicional: Só valida se o checkbox estiver marcado

Atributos dinâmicos: Configura min/max apropriadamente

Tratamento de saldo zero: Permite continuar com aviso

Ajustes automáticos: Sugere correções quando há problemas

Feedback visual: Melhora a experiência do usuário com cores e tooltips

Como Implementar:
Substitua a função validateQuantity() existente pelo código acima

Teste com diferentes cenários:

Itens não selecionados

Saldo zero

Quantidades parciais

Excesso de quantidade

Isso deve resolver seu problema permitindo transferências parciais mesmo quando o saldo é zero, desde que o usuário confirme a ação.

Você também pode querer atualizar a função updateMaterialSelection() para garantir que ela não interfira com essa nova lógica.