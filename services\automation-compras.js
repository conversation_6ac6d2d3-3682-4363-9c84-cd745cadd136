/**
 * 🤖 SERVIÇO DE AUTOMAÇÃO PARA COMPRAS
 * Automatiza processos pós-aprovação de pedidos
 */

import { 
  collection, 
  addDoc, 
  updateDoc,
  doc,
  query, 
  where, 
  getDocs,
  Timestamp,
  runTransaction
} from 'firebase/firestore';
import { db } from '../firebase-config.js';

export class AutomationCompras {
  
  /**
   * 📧 ENVIO AUTOMÁTICO DE PEDIDOS PARA FORNECEDORES
   */
  static async enviarPedidoAutomatico(pedidoId) {
    try {
      console.log(`📧 Enviando pedido ${pedidoId} automaticamente...`);
      
      // Buscar dados do pedido
      const pedidoDoc = await getDoc(doc(db, "pedidosCompra", pedidoId));
      const pedido = pedidoDoc.data();
      
      // Buscar dados do fornecedor
      const fornecedorDoc = await getDoc(doc(db, "fornecedores", pedido.fornecedorId));
      const fornecedor = fornecedorDoc.data();
      
      // Gerar PDF do pedido
      const pdfData = await this.gerarPDFPedido(pedido, fornecedor);
      
      // Enviar por email
      const emailData = {
        para: fornecedor.email,
        assunto: `Pedido de Compra ${pedido.numero} - ${fornecedor.razaoSocial}`,
        corpo: this.gerarCorpoEmailPedido(pedido, fornecedor),
        anexos: [
          {
            nome: `Pedido_${pedido.numero}.pdf`,
            dados: pdfData,
            tipo: 'application/pdf'
          }
        ],
        status: 'PENDENTE',
        dataEnvio: Timestamp.now(),
        tipo: 'PEDIDO_COMPRA'
      };
      
      await addDoc(collection(db, "filaEmails"), emailData);
      
      // Atualizar status do pedido
      await updateDoc(doc(db, "pedidosCompra", pedidoId), {
        status: 'ENVIADO',
        dataEnvio: Timestamp.now(),
        enviadoPor: 'SISTEMA_AUTOMATICO'
      });
      
      console.log(`✅ Pedido ${pedidoId} enviado automaticamente`);
      
    } catch (error) {
      console.error('❌ Erro no envio automático:', error);
      throw error;
    }
  }

  /**
   * 🔍 CRIAÇÃO AUTOMÁTICA DE INSPEÇÕES
   */
  static async criarInspecaoAutomatica(recebimentoId, itemData) {
    try {
      console.log(`🔍 Criando inspeção automática para ${recebimentoId}...`);
      
      // Verificar se produto requer inspeção
      const produtoDoc = await getDoc(doc(db, "produtos", itemData.produtoId));
      const produto = produtoDoc.data();
      
      if (!produto.requerInspecao) {
        console.log('ℹ️ Produto não requer inspeção');
        return null;
      }
      
      // Criar inspeção automática
      const inspecaoData = {
        numero: `INS-${Date.now()}`,
        recebimentoId: recebimentoId,
        produtoId: itemData.produtoId,
        codigo: itemData.codigo,
        descricao: itemData.descricao,
        quantidade: itemData.quantidade,
        lote: itemData.lote || `LOTE-${Date.now()}`,
        tipo: produto.tipoInspecao || 'VISUAL',
        prioridade: produto.criticidade || 'MEDIA',
        criterios: produto.criteriosInspecao || [],
        status: 'PENDENTE',
        dataCriacao: Timestamp.now(),
        dataLimite: Timestamp.fromDate(
          new Date(Date.now() + (produto.prazoInspecao || 3) * 24 * 60 * 60 * 1000)
        ),
        criadoPor: 'SISTEMA_AUTOMATICO'
      };
      
      const inspecaoRef = await addDoc(collection(db, "inspecoesQualidade"), inspecaoData);
      
      // Registrar no estoque de qualidade
      await addDoc(collection(db, "estoqueQualidade"), {
        inspecaoId: inspecaoRef.id,
        produtoId: itemData.produtoId,
        codigo: itemData.codigo,
        descricao: itemData.descricao,
        quantidade: itemData.quantidade,
        lote: inspecaoData.lote,
        status: 'QUARENTENA',
        dataEntrada: Timestamp.now(),
        origem: `Recebimento ${recebimentoId}`
      });
      
      console.log(`✅ Inspeção ${inspecaoRef.id} criada automaticamente`);
      return inspecaoRef.id;
      
    } catch (error) {
      console.error('❌ Erro ao criar inspeção automática:', error);
      throw error;
    }
  }

  /**
   * 📦 LIBERAÇÃO AUTOMÁTICA PARA ESTOQUE
   */
  static async liberarParaEstoqueAutomatico(inspecaoId, resultado) {
    try {
      console.log(`📦 Liberando para estoque: ${inspecaoId} - ${resultado}`);
      
      if (resultado !== 'APROVADO') {
        console.log('❌ Item rejeitado, não será liberado para estoque');
        return;
      }
      
      return await runTransaction(db, async (transaction) => {
        // Buscar dados da inspeção
        const inspecaoRef = doc(db, "inspecoesQualidade", inspecaoId);
        const inspecaoDoc = await transaction.get(inspecaoRef);
        const inspecao = inspecaoDoc.data();
        
        // Buscar item no estoque de qualidade
        const qualidadeQuery = query(
          collection(db, "estoqueQualidade"),
          where("inspecaoId", "==", inspecaoId)
        );
        const qualidadeSnap = await getDocs(qualidadeQuery);
        
        if (qualidadeSnap.empty) {
          throw new Error('Item não encontrado no estoque de qualidade');
        }
        
        const itemQualidade = qualidadeSnap.docs[0];
        const itemData = itemQualidade.data();
        
        // Atualizar status no estoque de qualidade
        transaction.update(itemQualidade.ref, {
          status: 'LIBERADO',
          dataLiberacao: Timestamp.now()
        });
        
        // Criar movimentação de entrada no estoque principal
        const movimentacaoRef = doc(collection(db, "movimentacoesEstoque"));
        transaction.set(movimentacaoRef, {
          tipo: 'ENTRADA',
          subtipo: 'LIBERACAO_QUALIDADE',
          produtoId: itemData.produtoId,
          codigo: itemData.codigo,
          descricao: itemData.descricao,
          quantidade: itemData.quantidade,
          lote: itemData.lote,
          armazemOrigemId: 'QUALIDADE',
          armazemDestinoId: 'PRINCIPAL',
          dataMovimentacao: Timestamp.now(),
          usuario: 'SISTEMA_AUTOMATICO',
          observacoes: `Liberação automática após inspeção ${inspecaoId}`,
          inspecaoId: inspecaoId
        });
        
        // Atualizar saldo no estoque principal
        const estoqueQuery = query(
          collection(db, "estoques"),
          where("produtoId", "==", itemData.produtoId),
          where("armazemId", "==", "PRINCIPAL")
        );
        const estoqueSnap = await getDocs(estoqueQuery);
        
        if (!estoqueSnap.empty) {
          const estoqueDoc = estoqueSnap.docs[0];
          const estoqueAtual = estoqueDoc.data();
          
          transaction.update(estoqueDoc.ref, {
            saldo: (estoqueAtual.saldo || 0) + itemData.quantidade,
            ultimaMovimentacao: Timestamp.now()
          });
        } else {
          // Criar novo registro de estoque
          const novoEstoqueRef = doc(collection(db, "estoques"));
          transaction.set(novoEstoqueRef, {
            produtoId: itemData.produtoId,
            armazemId: 'PRINCIPAL',
            saldo: itemData.quantidade,
            saldoReservado: 0,
            ultimaMovimentacao: Timestamp.now()
          });
        }
        
        console.log(`✅ Item liberado automaticamente para estoque principal`);
        return movimentacaoRef.id;
      });
      
    } catch (error) {
      console.error('❌ Erro na liberação automática:', error);
      throw error;
    }
  }

  /**
   * 💰 GERAÇÃO AUTOMÁTICA DE CONTAS A PAGAR
   */
  static async gerarContaPagarAutomatica(pedidoId, notaFiscalData) {
    try {
      console.log(`💰 Gerando conta a pagar para pedido ${pedidoId}...`);

      // Buscar dados do pedido
      const pedidoDoc = await getDoc(doc(db, "pedidosCompra", pedidoId));
      if (!pedidoDoc.exists()) {
        throw new Error(`Pedido ${pedidoId} não encontrado`);
      }

      const pedido = pedidoDoc.data();

      // Validar se o pedido tem condição de pagamento
      if (!pedido.condicaoPagamentoId) {
        console.warn(`⚠️ Pedido ${pedidoId} não possui condição de pagamento definida. Usando condição padrão.`);

        // Buscar condição de pagamento padrão (à vista)
        const condicoesSnap = await getDocs(
          query(collection(db, "condicoesPagamento"),
                where("descricao", "==", "À Vista"),
                limit(1))
        );

        if (!condicoesSnap.empty) {
          pedido.condicaoPagamentoId = condicoesSnap.docs[0].id;
          console.log(`✅ Usando condição de pagamento padrão: ${pedido.condicaoPagamentoId}`);
        } else {
          throw new Error('Nenhuma condição de pagamento encontrada. Configure uma condição "À Vista" como padrão.');
        }
      }

      // Buscar condição de pagamento
      const condicaoDoc = await getDoc(doc(db, "condicoesPagamento", pedido.condicaoPagamentoId));
      if (!condicaoDoc.exists()) {
        throw new Error(`Condição de pagamento ${pedido.condicaoPagamentoId} não encontrada`);
      }

      const condicao = condicaoDoc.data();

      // Gerar parcelas
      const parcelas = this.gerarParcelas(notaFiscalData.valor, condicao);

      // Criar conta a pagar
      const contaData = {
        pedidoId: pedidoId,
        fornecedorId: pedido.fornecedorId,
        numeroNF: notaFiscalData.numero,
        chaveNF: notaFiscalData.chave,
        dataEmissaoNF: notaFiscalData.dataEmissao,
        valorTotal: notaFiscalData.valor,
        condicaoPagamentoId: pedido.condicaoPagamentoId,
        centroCusto: pedido.centroCusto,
        parcelas: parcelas,
        status: 'PENDENTE',
        dataCriacao: Timestamp.now(),
        criadoPor: 'SISTEMA_AUTOMATICO',
        observacoes: `Conta gerada automaticamente do pedido ${pedido.numero}`
      };

      // Remover campos undefined antes de salvar
      Object.keys(contaData).forEach(key => {
        if (contaData[key] === undefined) {
          delete contaData[key];
        }
      });

      const contaRef = await addDoc(collection(db, "contasAPagar"), contaData);

      console.log(`✅ Conta a pagar ${contaRef.id} criada automaticamente`);
      return contaRef.id;
      
    } catch (error) {
      console.error('❌ Erro ao gerar conta a pagar:', error);
      throw error;
    }
  }

  /**
   * 📊 ATUALIZAÇÃO AUTOMÁTICA DE STATUS
   */
  static async atualizarStatusAutomatico(pedidoId) {
    try {
      console.log(`📊 Atualizando status automático do pedido ${pedidoId}...`);
      
      return await runTransaction(db, async (transaction) => {
        // Buscar pedido
        const pedidoRef = doc(db, "pedidosCompra", pedidoId);
        const pedidoDoc = await transaction.get(pedidoRef);
        const pedido = pedidoDoc.data();
        
        // Verificar recebimentos
        const recebimentosQuery = query(
          collection(db, "recebimentosMateriais"),
          where("pedidoId", "==", pedidoId)
        );
        const recebimentosSnap = await getDocs(recebimentosQuery);
        
        let quantidadeTotal = 0;
        let quantidadeRecebida = 0;
        
        pedido.itens.forEach(item => {
          quantidadeTotal += item.quantidade;
        });
        
        recebimentosSnap.forEach(doc => {
          const recebimento = doc.data();
          recebimento.itens?.forEach(item => {
            quantidadeRecebida += item.quantidadeRecebida || 0;
          });
        });
        
        // Determinar novo status
        let novoStatus = pedido.status;
        if (quantidadeRecebida >= quantidadeTotal) {
          novoStatus = 'RECEBIDO';
        } else if (quantidadeRecebida > 0) {
          novoStatus = 'PARCIALMENTE_RECEBIDO';
        }
        
        // Atualizar se necessário
        if (novoStatus !== pedido.status) {
          transaction.update(pedidoRef, {
            status: novoStatus,
            percentualRecebido: (quantidadeRecebida / quantidadeTotal) * 100,
            ultimaAtualizacao: Timestamp.now()
          });
          
          console.log(`✅ Status atualizado para: ${novoStatus}`);
        }
        
        return novoStatus;
      });
      
    } catch (error) {
      console.error('❌ Erro na atualização automática:', error);
      throw error;
    }
  }

  /**
   * 🔧 MÉTODOS AUXILIARES
   */
  
  static gerarParcelas(valor, condicao) {
    const parcelas = [];
    const valorParcela = valor / condicao.numParcelas;
    
    for (let i = 1; i <= condicao.numParcelas; i++) {
      const dataVencimento = new Date();
      dataVencimento.setDate(dataVencimento.getDate() + (condicao.intervalo * i));
      
      parcelas.push({
        numero: i,
        valor: valorParcela,
        dataVencimento: Timestamp.fromDate(dataVencimento),
        status: 'PENDENTE',
        valorPago: 0
      });
    }
    
    return parcelas;
  }
  
  static gerarCorpoEmailPedido(pedido, fornecedor) {
    return `
      Prezado(a) ${fornecedor.razaoSocial},
      
      Segue em anexo o Pedido de Compra ${pedido.numero}.
      
      Dados do Pedido:
      - Número: ${pedido.numero}
      - Data: ${new Date().toLocaleDateString('pt-BR')}
      - Valor Total: R$ ${pedido.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
      - Prazo de Entrega: ${pedido.prazoEntrega} dias
      
      Por favor, confirme o recebimento deste pedido.
      
      Atenciosamente,
      Departamento de Compras
    `;
  }
  
  static async gerarPDFPedido(pedido, fornecedor) {
    // Implementar geração de PDF
    // Por enquanto, retorna dados mock
    return new Uint8Array([/* dados do PDF */]);
  }
}

// Configurar execução automática de processos
if (typeof window !== 'undefined') {
  // Verificar processos automáticos a cada 30 minutos
  setInterval(async () => {
    try {
      console.log('🤖 Executando processos automáticos...');
      
      // Verificar pedidos aprovados para envio automático
      const pedidosAprovados = query(
        collection(db, "pedidosCompra"),
        where("status", "==", "APROVADO"),
        where("envioAutomatico", "==", true)
      );
      
      const pedidosSnap = await getDocs(pedidosAprovados);
      
      for (const pedidoDoc of pedidosSnap.docs) {
        await AutomationCompras.enviarPedidoAutomatico(pedidoDoc.id);
      }
      
    } catch (error) {
      console.error('❌ Erro nos processos automáticos:', error);
    }
  }, 30 * 60 * 1000); // 30 minutos
}
