
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Correção - Movimentações de Recebimento</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }

        .main-content {
            padding: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Correção - Movimentações de Recebimento</h1>
            <p>Ferramenta para corrigir e padronizar movimentações de recebimento</p>
        </div>

        <div class="main-content">
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="totalMovimentacoes">-</div>
                    <div class="stat-label">Total de Movimentações</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="movimentacoesSemTES">-</div>
                    <div class="stat-label">Sem TES</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="recebimentosSemMovimentacao">-</div>
                    <div class="stat-label">Recebimentos Sem Movimentação</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="corrigidos">0</div>
                    <div class="stat-label">Corrigidos</div>
                </div>
            </div>

            <div style="margin: 20px 0;">
                <button class="btn btn-success" onclick="analisarProblemas()">
                    🔍 Analisar Problemas
                </button>
                <button class="btn btn-warning" onclick="corrigirMovimentacoes()">
                    🔧 Corrigir Movimentações
                </button>
                <button class="btn btn-success" onclick="criarMovimentacoesFaltantes()">
                    ➕ Criar Movimentações Faltantes
                </button>
            </div>

            <div class="log-container" id="logContainer">
                Sistema pronto para análise e correção...
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            doc, 
            getDocs, 
            updateDoc, 
            addDoc,
            query,
            where,
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let problems = {
            movimentacoesSemTES: [],
            recebimentosSemMovimentacao: [],
            total: 0
        };

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logContainer.textContent += `[${timestamp}] ${icon} ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        window.analisarProblemas = async function() {
            try {
                addLog('🔍 Iniciando análise de problemas...');
                
                // 1. Analisar movimentações sem TES
                const movimentacoesSnapshot = await getDocs(collection(db, "movimentacoesEstoque"));
                problems.total = movimentacoesSnapshot.size;
                problems.movimentacoesSemTES = [];

                movimentacoesSnapshot.forEach(doc => {
                    const data = doc.data();
                    if (!data.tes || data.tes === '') {
                        problems.movimentacoesSemTES.push({
                            id: doc.id,
                            ...data
                        });
                    }
                });

                // 2. Analisar recebimentos sem movimentações
                const recebimentosSnapshot = await getDocs(collection(db, "recebimentoMateriais"));
                problems.recebimentosSemMovimentacao = [];

                for (const recDoc of recebimentosSnapshot.docs) {
                    const recData = recDoc.data();
                    
                    // Verificar se existe movimentação para este recebimento
                    const movQuery = query(
                        collection(db, "movimentacoesEstoque"),
                        where("recebimentoId", "==", recDoc.id)
                    );
                    const movSnapshot = await getDocs(movQuery);
                    
                    if (movSnapshot.empty && recData.status === 'CONFIRMADO') {
                        problems.recebimentosSemMovimentacao.push({
                            id: recDoc.id,
                            ...recData
                        });
                    }
                }

                // Atualizar estatísticas
                document.getElementById('totalMovimentacoes').textContent = problems.total;
                document.getElementById('movimentacoesSemTES').textContent = problems.movimentacoesSemTES.length;
                document.getElementById('recebimentosSemMovimentacao').textContent = problems.recebimentosSemMovimentacao.length;

                addLog(`📊 Análise concluída:`, 'success');
                addLog(`   - Total de movimentações: ${problems.total}`);
                addLog(`   - Movimentações sem TES: ${problems.movimentacoesSemTES.length}`);
                addLog(`   - Recebimentos sem movimentação: ${problems.recebimentosSemMovimentacao.length}`);

            } catch (error) {
                addLog(`❌ Erro na análise: ${error.message}`, 'error');
            }
        };

        window.corrigirMovimentacoes = async function() {
            if (problems.movimentacoesSemTES.length === 0) {
                addLog('⚠️ Execute a análise primeiro!', 'warning');
                return;
            }

            try {
                addLog('🔧 Iniciando correção de movimentações...');
                let corrigidos = 0;

                for (const mov of problems.movimentacoesSemTES) {
                    try {
                        // Determinar TES baseado no tipo de movimento
                        let tes = '001'; // Default para entrada por compra
                        
                        if (mov.tipoDocumento === 'RECEBIMENTO' || mov.origem === 'recebimento') {
                            tes = '001'; // Entrada por compra
                        } else if (mov.tipoDocumento === 'PRODUCAO') {
                            tes = mov.tipo === 'ENTRADA' ? '002' : '201'; // Produção ou consumo
                        } else if (mov.tipoDocumento === 'TRANSFERENCIA') {
                            tes = '100'; // Transferência
                        } else if (mov.tipoDocumento === 'AJUSTE') {
                            tes = mov.tipo === 'ENTRADA' ? '900' : '901'; // Ajuste
                        }

                        // Atualizar movimentação
                        await updateDoc(doc(db, "movimentacoesEstoque", mov.id), {
                            tes: tes,
                            dataCorrecao: Timestamp.now(),
                            usuarioCorrecao: 'Sistema'
                        });

                        corrigidos++;
                        addLog(`✅ Corrigido: ${mov.id} - TES: ${tes}`);

                    } catch (error) {
                        addLog(`❌ Erro ao corrigir ${mov.id}: ${error.message}`, 'error');
                    }
                }

                document.getElementById('corrigidos').textContent = corrigidos;
                addLog(`🎉 Correção concluída! ${corrigidos} movimentações corrigidas.`, 'success');

            } catch (error) {
                addLog(`❌ Erro na correção: ${error.message}`, 'error');
            }
        };

        window.criarMovimentacoesFaltantes = async function() {
            if (problems.recebimentosSemMovimentacao.length === 0) {
                addLog('⚠️ Execute a análise primeiro!', 'warning');
                return;
            }

            try {
                addLog('➕ Criando movimentações faltantes...');
                let criados = 0;

                for (const rec of problems.recebimentosSemMovimentacao) {
                    try {
                        // Processar itens do recebimento
                        if (rec.itens && Array.isArray(rec.itens)) {
                            for (const item of rec.itens) {
                                if (item.quantidadeRecebida > 0) {
                                    const movimentacaoData = {
                                        produtoId: item.produtoId,
                                        armazemId: rec.armazemDestinoId,
                                        tipo: 'ENTRADA',
                                        quantidade: item.quantidadeRecebida,
                                        tipoDocumento: 'RECEBIMENTO',
                                        numeroDocumento: rec.numeroNotaFiscal,
                                        observacoes: `Recebimento NF: ${rec.numeroNotaFiscal} - Correção automática`,
                                        dataHora: rec.dataRecebimento || Timestamp.now(),
                                        tes: '001', // Entrada por compra
                                        recebimentoId: rec.id,
                                        pedidoCompraId: rec.pedidoCompraId,
                                        valorUnitario: item.valorUnitario || 0,
                                        valorTotal: (item.valorUnitario || 0) * item.quantidadeRecebida,
                                        usuario: rec.usuarioRecebimento || 'Sistema',
                                        saldoAnterior: 0, // Será calculado posteriormente
                                        saldoPosterior: item.quantidadeRecebida,
                                        status: 'CONFIRMADA',
                                        criadoPorCorrecao: true
                                    };

                                    await addDoc(collection(db, "movimentacoesEstoque"), movimentacaoData);
                                    criados++;
                                    addLog(`✅ Criada movimentação para: ${item.codigo || item.produtoId}`);
                                }
                            }
                        }

                    } catch (error) {
                        addLog(`❌ Erro ao criar movimentação para ${rec.id}: ${error.message}`, 'error');
                    }
                }

                addLog(`🎉 Criação concluída! ${criados} movimentações criadas.`, 'success');
                
                // Atualizar contadores
                const currentCorrigidos = parseInt(document.getElementById('corrigidos').textContent) || 0;
                document.getElementById('corrigidos').textContent = currentCorrigidos + criados;

            } catch (error) {
                addLog(`❌ Erro na criação: ${error.message}`, 'error');
            }
        };

        // Inicialização
        addLog('🚀 Sistema de correção carregado');
        addLog('💡 Dica: Execute "Analisar Problemas" primeiro');
    </script>
</body>
</html>
