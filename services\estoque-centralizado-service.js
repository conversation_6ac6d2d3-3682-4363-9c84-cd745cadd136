/**
 * 📦 SERVIÇO CENTRALIZADO DE ESTOQUE
 * Resolve inconsistências entre diferentes sistemas de consulta de estoque
 * 
 * PROBLEMA RESOLVIDO:
 * - movimentacao_armazem_novo.html mostrava 50.405 KG (soma todos armazéns)
 * - ajuste_estoque.html mostrava 0.001 KG (armazém específico)
 */

import { db } from '../firebase-config.js';
import {
    collection,
    getDocs,
    query,
    where
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class EstoqueCentralizadoService {
    
    /**
     * 📊 CONSULTAR SALDO DISPONÍVEL - MÉTODO PADRONIZADO
     * @param {string} produtoId - ID do produto
     * @param {string|null} armazemId - ID do armazém (null = todos os armazéns)
     * @param {boolean} incluirReservados - Se deve incluir saldos reservados/empenhados
     * @returns {Object} Detalhes completos do estoque
     */
    static async consultarSaldoDisponivel(produtoId, armazemId = null, incluirReservados = false) {
        try {
            // Construir query baseada nos parâmetros
            let estoqueQuery = query(
                collection(db, "estoques"),
                where("produtoId", "==", produtoId)
            );

            // Filtrar por armazém se especificado
            if (armazemId) {
                estoqueQuery = query(
                    collection(db, "estoques"),
                    where("produtoId", "==", produtoId),
                    where("armazemId", "==", armazemId)
                );
            }

            const estoqueSnapshot = await getDocs(estoqueQuery);

            if (estoqueSnapshot.empty) {
                return {
                    saldoTotal: 0,
                    saldoReservado: 0,
                    saldoEmpenhado: 0,
                    saldoDisponivel: 0,
                    registros: [],
                    armazens: [],
                    metodo: armazemId ? 'ARMAZEM_ESPECIFICO' : 'TODOS_ARMAZENS'
                };
            }

            // Processar todos os registros encontrados
            let saldoTotal = 0;
            let saldoReservado = 0;
            let saldoEmpenhado = 0;
            const registros = [];
            const armazens = [];

            estoqueSnapshot.forEach(doc => {
                const estoque = doc.data();
                const saldo = estoque.saldo || 0;
                const reservado = estoque.saldoReservado || 0;
                const empenhado = estoque.saldoEmpenhado || 0;

                saldoTotal += saldo;
                saldoReservado += reservado;
                saldoEmpenhado += empenhado;

                registros.push({
                    id: doc.id,
                    armazemId: estoque.armazemId,
                    saldo: saldo,
                    saldoReservado: reservado,
                    saldoEmpenhado: empenhado,
                    ultimaMovimentacao: estoque.ultimaMovimentacao
                });

                if (!armazens.includes(estoque.armazemId)) {
                    armazens.push(estoque.armazemId);
                }
            });

            // Calcular saldo disponível
            const saldoDisponivel = incluirReservados ? 
                saldoTotal : 
                Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado);

            return {
                saldoTotal,
                saldoReservado,
                saldoEmpenhado,
                saldoDisponivel,
                registros,
                armazens,
                metodo: armazemId ? 'ARMAZEM_ESPECIFICO' : 'TODOS_ARMAZENS',
                incluirReservados
            };

        } catch (error) {
            console.error('Erro ao consultar saldo disponível:', error);
            throw error;
        }
    }

    /**
     * 🏪 CONSULTAR ESTOQUE POR ARMAZÉM
     * @param {string} produtoId - ID do produto
     * @returns {Array} Lista de estoques por armazém
     */
    static async consultarEstoquePorArmazem(produtoId) {
        try {
            const resultado = await this.consultarSaldoDisponivel(produtoId, null, false);
            
            // Buscar informações dos armazéns
            const armazensSnapshot = await getDocs(collection(db, "armazens"));
            const armazensInfo = {};
            
            armazensSnapshot.forEach(doc => {
                const armazem = doc.data();
                armazensInfo[doc.id] = {
                    codigo: armazem.codigo,
                    nome: armazem.nome,
                    tipo: armazem.tipo
                };
            });

            // Enriquecer dados com informações dos armazéns
            return resultado.registros.map(registro => ({
                ...registro,
                armazemInfo: armazensInfo[registro.armazemId] || {
                    codigo: 'N/A',
                    nome: 'Armazém não encontrado',
                    tipo: 'DESCONHECIDO'
                },
                saldoLiquido: Math.max(0, registro.saldo - registro.saldoReservado - registro.saldoEmpenhado)
            }));

        } catch (error) {
            console.error('Erro ao consultar estoque por armazém:', error);
            throw error;
        }
    }

    /**
     * 📋 GERAR RELATÓRIO DE INCONSISTÊNCIAS
     * @param {string} produtoId - ID do produto
     * @returns {Object} Relatório detalhado
     */
    static async gerarRelatorioInconsistencias(produtoId) {
        try {
            const [estoqueTotal, estoquePorArmazem] = await Promise.all([
                this.consultarSaldoDisponivel(produtoId, null, false),
                this.consultarEstoquePorArmazem(produtoId)
            ]);

            // Buscar informações do produto
            const produtosSnapshot = await getDocs(
                query(collection(db, "produtos"), where("codigo", "==", produtoId))
            );
            
            const produto = produtosSnapshot.empty ? null : produtosSnapshot.docs[0].data();

            return {
                produto: produto ? {
                    codigo: produto.codigo,
                    descricao: produto.descricao,
                    unidade: produto.unidade
                } : null,
                resumo: {
                    saldoTotalTodosArmazens: estoqueTotal.saldoTotal,
                    saldoDisponivelTodosArmazens: estoqueTotal.saldoDisponivel,
                    saldoReservadoTotal: estoqueTotal.saldoReservado,
                    saldoEmpenhadoTotal: estoqueTotal.saldoEmpenhado,
                    quantidadeArmazens: estoqueTotal.armazens.length
                },
                detalhePorArmazem: estoquePorArmazem,
                inconsistencias: this.detectarInconsistencias(estoquePorArmazem),
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('Erro ao gerar relatório de inconsistências:', error);
            throw error;
        }
    }

    /**
     * 🚨 DETECTAR INCONSISTÊNCIAS
     * @param {Array} estoquePorArmazem - Lista de estoques por armazém
     * @returns {Array} Lista de inconsistências encontradas
     */
    static detectarInconsistencias(estoquePorArmazem) {
        const inconsistencias = [];

        estoquePorArmazem.forEach(estoque => {
            // Saldo negativo
            if (estoque.saldo < 0) {
                inconsistencias.push({
                    tipo: 'SALDO_NEGATIVO',
                    armazemId: estoque.armazemId,
                    valor: estoque.saldo,
                    descricao: `Saldo negativo no armazém ${estoque.armazemInfo.codigo}`
                });
            }

            // Saldo reservado maior que saldo total
            if (estoque.saldoReservado > estoque.saldo) {
                inconsistencias.push({
                    tipo: 'RESERVADO_MAIOR_QUE_SALDO',
                    armazemId: estoque.armazemId,
                    saldo: estoque.saldo,
                    reservado: estoque.saldoReservado,
                    descricao: `Saldo reservado (${estoque.saldoReservado}) maior que saldo total (${estoque.saldo})`
                });
            }

            // Saldo empenhado maior que saldo total
            if (estoque.saldoEmpenhado > estoque.saldo) {
                inconsistencias.push({
                    tipo: 'EMPENHADO_MAIOR_QUE_SALDO',
                    armazemId: estoque.armazemId,
                    saldo: estoque.saldo,
                    empenhado: estoque.saldoEmpenhado,
                    descricao: `Saldo empenhado (${estoque.saldoEmpenhado}) maior que saldo total (${estoque.saldo})`
                });
            }

            // Saldo líquido negativo
            if (estoque.saldoLiquido < 0) {
                inconsistencias.push({
                    tipo: 'SALDO_LIQUIDO_NEGATIVO',
                    armazemId: estoque.armazemId,
                    saldoLiquido: estoque.saldoLiquido,
                    descricao: `Saldo líquido negativo (${estoque.saldoLiquido}) após descontar reservas/empenhos`
                });
            }
        });

        return inconsistencias;
    }

    /**
     * 🔧 FUNÇÃO HELPER PARA COMPATIBILIDADE
     * Substitui a função calcularEstoqueDisponivel do movimentacao_armazem_novo.html
     */
    static async calcularEstoqueDisponivelCompatibilidade(produtoId, incluirReservados = false) {
        const resultado = await this.consultarSaldoDisponivel(produtoId, null, incluirReservados);
        return resultado.saldoDisponivel;
    }

    /**
     * 🔧 FUNÇÃO HELPER PARA AJUSTE DE ESTOQUE
     * Substitui a lógica do ajuste_estoque.html
     */
    static async obterSaldoArmazemEspecifico(produtoId, armazemId) {
        const resultado = await this.consultarSaldoDisponivel(produtoId, armazemId, false);
        return {
            saldo: resultado.saldoTotal,
            saldoReservado: resultado.saldoReservado,
            saldoEmpenhado: resultado.saldoEmpenhado,
            saldoDisponivel: resultado.saldoDisponivel,
            existe: resultado.registros.length > 0
        };
    }
}

// Exportar para uso global
window.EstoqueCentralizadoService = EstoqueCentralizadoService;
