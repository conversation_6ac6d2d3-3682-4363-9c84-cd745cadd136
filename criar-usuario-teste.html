<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Criar <PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Criar Usuário de Teste</h1>
        <p>Esta ferramenta cria um usuário temporário para acessar o sistema.</p>
        
        <div id="status" class="status info">
            Clique no botão abaixo para criar um usuário de teste.
        </div>
        
        <button class="btn" onclick="criarUsuario()">👤 Criar Usuário de Teste</button>
        <button class="btn" onclick="verificarUsuario()">🔍 Verificar Usuário Atual</button>
        <button class="btn" onclick="limparUsuario()">🗑️ Limpar Usuário</button>
        <button class="btn success" onclick="abrirIndex()">🏠 Abrir Sistema</button>
        
        <hr style="margin: 20px 0;">
        
        <h3>📋 Informações do Sistema</h3>
        <div id="userInfo" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;">
            Carregando...
        </div>
    </div>

    <script>
        function criarUsuario() {
            const usuario = {
                id: 'teste-001',
                nome: 'Usuário Teste',
                email: '<EMAIL>',
                nivel: 9,
                permissoes: ['admin', 'all'],
                dataLogin: new Date().toISOString(),
                ativo: true
            };
            
            localStorage.setItem('currentUser', JSON.stringify(usuario));
            
            document.getElementById('status').className = 'status success';
            document.getElementById('status').textContent = '✅ Usuário de teste criado com sucesso!';
            
            verificarUsuario();
        }
        
        function verificarUsuario() {
            const usuario = localStorage.getItem('currentUser');
            const userInfo = document.getElementById('userInfo');
            
            if (usuario) {
                try {
                    const userData = JSON.parse(usuario);
                    userInfo.innerHTML = `
<strong>✅ Usuário Logado:</strong>
Nome: ${userData.nome}
Email: ${userData.email}
Nível: ${userData.nivel}
Data Login: ${userData.dataLogin}
Ativo: ${userData.ativo ? 'Sim' : 'Não'}

<strong>📦 Dados Completos:</strong>
${JSON.stringify(userData, null, 2)}
                    `;
                    
                    document.getElementById('status').className = 'status success';
                    document.getElementById('status').textContent = '✅ Usuário encontrado no localStorage';
                } catch (error) {
                    userInfo.textContent = '❌ Erro ao ler dados do usuário: ' + error.message;
                    document.getElementById('status').className = 'status error';
                    document.getElementById('status').textContent = '❌ Dados do usuário corrompidos';
                }
            } else {
                userInfo.textContent = '❌ Nenhum usuário encontrado no localStorage';
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = '❌ Nenhum usuário logado';
            }
        }
        
        function limparUsuario() {
            localStorage.removeItem('currentUser');
            document.getElementById('status').className = 'status info';
            document.getElementById('status').textContent = '🗑️ Usuário removido do localStorage';
            verificarUsuario();
        }
        
        function abrirIndex() {
            const usuario = localStorage.getItem('currentUser');
            if (usuario) {
                window.open('http://127.0.0.1:8000/index.html', '_blank');
            } else {
                alert('❌ Crie um usuário de teste primeiro!');
            }
        }
        
        // Verificar usuário ao carregar a página
        window.onload = function() {
            verificarUsuario();
        };
    </script>
</body>
</html>
