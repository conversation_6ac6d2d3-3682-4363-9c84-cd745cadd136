firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T10:36:33.105Z', apps: 1}
recebimento_materiais_avancado.html:1051 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1127 Dados carregados: {pedidos: 39, produtos: 1668, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1137 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', condicaoPagamento: '60DIAS', ultimaLimpezaCritica: Timestamp, fornecedorId: 'xJrwvEHQor3ASxHYxUAE', alteradoPor: 'Correção de Numeração', …}
recebimento_materiais_avancado.html:1139 📦 Exemplo de item: {codigo: '110732', valorUnitario: 1805.17, valorTotal: 1805.17, produtoId: '1sHECTgPNMjBw0ScLbbq', descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', …}
recebimento_materiais_avancado.html:1143 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', unidade: 'PC', cest: null, tipo: 'MP', pontoPedido: 0, …}
recebimento_materiais_avancado.html:1146 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf3: '', email2: '', contato2: '', latitudeCLI: '', …}
 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
 📋 Pedidos de Compra: 39
 🏷️ Produtos: 1668
 🏢 Fornecedores: 778
 🏪 Armazéns: 9
 📋 Estrutura do pedido: (22) ['id', 'condicaoPagamento', 'ultimaLimpezaCritica', 'fornecedorId', 'alteradoPor', 'dataCriacao', 'numeroAnterior', 'limpoPor', 'ultimaAtualizacao', 'status', 'cotacaoId', 'solicitacaoId', 'aprovadoPor', 'atualizacoesEntrega', 'valorTotal', 'sincronizadoPor', 'historico', 'itens', 'criadoPor', 'dataAprovacao', 'prazoEntrega', 'numero']
 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', condicaoPagamento: '60DIAS', ultimaLimpezaCritica: Timestamp, fornecedorId: 'xJrwvEHQor3ASxHYxUAE', alteradoPor: 'Correção de Numeração', …}
 📦 Estrutura do item: (7) ['codigo', 'valorUnitario', 'valorTotal', 'produtoId', 'descricao', 'unidade', 'quantidade']
 📦 Item exemplo: {codigo: '110732', valorUnitario: 1805.17, valorTotal: 1805.17, produtoId: '1sHECTgPNMjBw0ScLbbq', descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', …}
 🏢 Estrutura do fornecedor: (71) ['id', 'cnpjCpf3', 'email2', 'contato2', 'latitudeCLI', 'codigoArea', 'cotacao', 'cnpjCpf', 'tipoPessoa', 'indicacao', 'estado', 'intervista', 'telefone3', 'cnpjCpf2', 'contaContabil', 'codCusto', 'numero', 'dataAtualizacao', 'emailNfe', 'codigoClassificacao', 'homePage', 'codigoPais', 'longitudeCLI', 'bairro', 'telefone4', 'categorias', 'observacoes', 'complemento', 'codigoRegiao', 'nascimento', 'simplesNacional', 'nomeFantasia', 'departamento3', 'cidade', 'celular3', 'ultimaCompra', 'cep', 'autorizaXml1', 'inscricaoMunicipal', 'tipo', 'reducao', 'email1', 'cargo2', 'telefone1', 'pais', 'categoriaPrincipal', 'inscricaoEstadual', 'dataCadastro', 'contato1', 'celular2', 'contato3', 'endereco', 'limite', 'acrescimoCLI', 'statusHomologacao', 'telefone2', 'autorizaXml2', 'departamento1', 'fax', 'temSubstituicao', 'celular1', 'razaoSocial', 'email3', 'codigo', 'cargo3', 'suframa', 'departamento2', 'ativo', 'codigoVendedor', 'email', 'im']
 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf3: '', email2: '', contato2: '', latitudeCLI: '', …}
 🏷️ Estrutura do produto: (31) ['id', 'unidade', 'cest', 'tipo', 'pontoPedido', 'status', 'fatorConversao', 'tipoItem', 'margemLucro', 'descricao', 'familia', 'metodoCusteio', 'prateleira', 'centroCustoObrigatorio', 'precoVenda', 'dataCadastro', 'origem', 'custoMedio', 'posicao', 'loteCompra', 'grupo', 'rastreabilidadeLote', 'corredor', 'ultimoCusto', 'estoqueMinimo', 'armazemPadraoId', 'estoqueMaximo', 'ncm', 'unidadeSecundaria', 'codigo', 'inspecaoRecebimento']
 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', unidade: 'PC', cest: null, tipo: 'MP', pontoPedido: 0, …}
 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
 📋 DEBUG populateOrderSelect - Total de pedidos: 39
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'APROVADO', temItens: 2}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'APROVADO', temItens: 5}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1368 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1395 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1298 📊 Dashboard atualizado: {pendentes: 15, atrasados: 0, parciais: 0, completos: 22}
recebimento_materiais_avancado.html:2177 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2186 Dados encontrados: {movimentacoes: 3489, estoqueQualidade: 63, recebimentosDetalhes: 16}
recebimento_materiais_avancado.html:2301 Total de registros de histórico encontrados: 99
recebimento_materiais_avancado.html:1570 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1510 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1519 🔍 DEBUG selectOrderById - currentOrder encontrado: {id: 'STenDi3OPZy4ieeJIrxk', itens: Array(2), cotacaoNumero: '000063', prazoEntrega: 7, dataAprovacao: Timestamp, …}
recebimento_materiais_avancado.html:1527 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1587 🔍 DEBUG loadSupplierInfo - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', itens: Array(2), cotacaoNumero: '000063', prazoEntrega: 7, dataAprovacao: Timestamp, …}
recebimento_materiais_avancado.html:1588 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1589 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1592 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1593 📋 Campos do currentOrder: (22) ['id', 'itens', 'cotacaoNumero', 'prazoEntrega', 'dataAprovacao', 'valorTotal', 'uidAprovacao', 'dataUltimaAtualizacao', 'dataRecebimento', 'dataCriacao', 'historico', 'criadoPor', 'condicoesPagamento', 'ultimoRecebimento', 'fornecedorId', 'recebidoPor', 'cotacaoId', 'numero', 'status', 'fornecedorNome', 'aprovadoPor', 'observacoes']
recebimento_materiais_avancado.html:1594 📄 Dados do fornecedor no pedido: {fornecedorId: 'YqfHIA1xCUe30ndFcIAj', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', fornecedorCnpj: undefined, fornecedorDocumento: undefined, fornecedorContato: undefined, …}
recebimento_materiais_avancado.html:1626 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: {id: 'YqfHIA1xCUe30ndFcIAj', departamento3: '', codigo: '1083', numero: '06', razaoSocial: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', …}
recebimento_materiais_avancado.html:1630 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1631 📋 Campos disponíveis: (70) ['id', 'departamento3', 'codigo', 'numero', 'razaoSocial', 'limite', 'homePage', 'observacoes', 'cargo3', 'longitudeCLI', 'pais', 'cnpjCpf2', 'celular1', 'contaContabil', 'cotacao', 'codigoArea', 'complemento', 'categorias', 'codigoVendedor', 'email3', 'email2', 'departamento2', 'simplesNacional', 'email1', 'autorizaXml1', 'estado', 'cidade', 'contato1', 'intervista', 'acrescimoCLI', 'statusHomologacao', 'indicacao', 'temSubstituicao', 'cep', 'suframa', 'telefone1', 'dataAtualizacao', 'reducao', 'celular2', 'telefone4', 'nomeFantasia', 'email', 'nascimento', 'contato2', 'codCusto', 'codigoRegiao', 'fax', 'codigoClassificacao', 'endereco', 'tipoPessoa', 'tipo', 'telefone3', 'codigoPais', 'im', 'inscricaoMunicipal', 'departamento1', 'inscricaoEstadual', 'cargo2', 'bairro', 'telefone2', 'contato3', 'celular3', 'autorizaXml2', 'ultimaCompra', 'cnpjCpf3', 'cnpjCpf', 'latitudeCLI', 'emailNfe', 'ativo', 'dataCadastro']
recebimento_materiais_avancado.html:1632 📄 CNPJ/CPF campos: {cnpj: undefined, cpfCnpj: undefined, cnpjCpf: '07.686.277/0001-69', documento: undefined, cpf: undefined}
recebimento_materiais_avancado.html:1653 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:927 🔍 DEBUG extrairCnpjCpf - Tentativas: (17) [undefined, undefined, '07.686.277/0001-69', undefined, undefined, '07.686.277/0001-69', undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined]
recebimento_materiais_avancado.html:928 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1691 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1692 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1693 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1710 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1711 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1712 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1715 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: {fornecedor?.cnpj: undefined, fornecedor?.cpfCnpj: undefined, fornecedor?.cnpjCpf: '07.686.277/0001-69', fornecedor?.documento: undefined, fornecedor?.cpf: undefined, …}
recebimento_materiais_avancado.html:1540 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1849 🔍 DEBUG loadOrderItems - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', itens: Array(2), cotacaoNumero: '000063', prazoEntrega: 7, dataAprovacao: Timestamp, …}
recebimento_materiais_avancado.html:1850 🔍 DEBUG loadOrderItems - itens: (2) [{…}, {…}]
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 0: {ipi: 0, quantidade: 2, unidade: 'PC', codigo: '105239', icms: 0, …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: {id: '94M0qmNlWSZt9opark0E', descricao: 'CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM', pontoPedido: 0, grupo: '301', estoqueMinimo: 0, …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 1: {icms: 0, unidade: 'PC', ipi: 0, codigo: '105240', precoUnitario: 397, …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: {id: 'UPqNBsHPmH3CAwc38Hrp', tipo: 'MP', codigo: '105240', unidade: 'PC', armazemPadraoId: 'BtRauPc2d0XyLfeBOZFj', …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2166 Erro ao carregar histórico: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2166
await in loadDeliveryHistory
selectOrderById @ recebimento_materiais_avancado.html:1556
handleMouseUp_ @ unknown
await in handleMouseUp_
window.selectOrder @ recebimento_materiais_avancado.html:1583
onchange @ recebimento_materiais_avancado.html:599
handleMouseUp_ @ unknown
