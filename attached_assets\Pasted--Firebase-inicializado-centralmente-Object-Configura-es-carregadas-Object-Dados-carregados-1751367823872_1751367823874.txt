 🔥 Firebase inicializado centralmente: Object
 Configurações carregadas: Object
 Dados carregados: Object
 📋 Exemplo de pedido: Object
 📦 Exemplo de item: Object
 🏷️ Exemplo de produto: Object
 🏢 Exemplo de fornecedor: Object
 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
 📋 Pedidos de Compra: 39
 🏷️ Produtos: 1668
 🏢 Fornecedores: 778
 🏪 Armazéns: 9
 📋 Estrutura do pedido: Array(22)
 📋 Pedido exemplo: Object
 📦 Estrutura do item: Array(7)
 📦 Item exemplo: Object
 🏢 Estrutura do fornecedor: Array(71)
 🏢 Fornecedor exemplo: Object
 🏷️ Estrutura do produto: Array(31)
 🏷️ Produto exemplo: Object
 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
 📋 DEBUG populateOrderSelect - Total de pedidos: 39
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: Object
 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: Object
 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: Object
 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: Object
 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: Object
 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: Object
 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: Object
 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: Object
 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: Object
 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: Object
 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: Object
 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: Object
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: Object
recebimento_materiais_avancado.html:1397 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1402 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1374 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: Object
recebimento_materiais_avancado.html:1382 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1374 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: Object
recebimento_materiais_avancado.html:1397 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1402 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1374 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: Object
recebimento_materiais_avancado.html:1397 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1402 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1374 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: Object
recebimento_materiais_avancado.html:1397 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1402 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1374 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: Object
recebimento_materiais_avancado.html:1382 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1415 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1439 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1442 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1345 📊 Dashboard atualizado: Object
recebimento_materiais_avancado.html:1617 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1557 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1566 🔍 DEBUG selectOrderById - currentOrder encontrado: Object
recebimento_materiais_avancado.html:1574 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1634 🔍 DEBUG loadSupplierInfo - currentOrder: Object
recebimento_materiais_avancado.html:1635 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1636 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1639 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1640 📋 Campos do currentOrder: Array(22)
recebimento_materiais_avancado.html:1641 📄 Dados do fornecedor no pedido: Object
recebimento_materiais_avancado.html:1673 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: Object
recebimento_materiais_avancado.html:1677 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1678 📋 Campos disponíveis: Array(70)
recebimento_materiais_avancado.html:1679 📄 CNPJ/CPF campos: Object
recebimento_materiais_avancado.html:1700 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:996 🔍 DEBUG extrairCnpjCpf - Tentativas: Array(17)
recebimento_materiais_avancado.html:997 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1738 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1739 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1740 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1757 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1758 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1759 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1762 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: Object
recebimento_materiais_avancado.html:1587 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1896 🔍 DEBUG loadOrderItems - currentOrder: Object
recebimento_materiais_avancado.html:1897 🔍 DEBUG loadOrderItems - itens: Array(2)
recebimento_materiais_avancado.html:1910 🔍 DEBUG loadOrderItems - Item 0: Object
recebimento_materiais_avancado.html:1913 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: Object
recebimento_materiais_avancado.html:1920 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1932 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1910 🔍 DEBUG loadOrderItems - Item 1: Object
recebimento_materiais_avancado.html:1913 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: Object
recebimento_materiais_avancado.html:1920 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1932 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2173 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2189 Consulta com índice falhou, usando consulta alternativa: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2189
recebimento_materiais_avancado.html:2264 Erro ao carregar histórico: ReferenceError: globalData is not defined
    at loadDeliveryHistory (recebimento_materiais_avancado.html:2203:39)
    at async selectOrderById (recebimento_materiais_avancado.html:1603:21)
    at async window.selectOrder (recebimento_materiais_avancado.html:1630:13)
loadDeliveryHistory @ recebimento_materiais_avancado.html:2264
recebimento_materiais_avancado.html:2291 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2300 Dados encontrados: Object
recebimento_materiais_avancado.html:2415 Total de registros de histórico encontrados: 99
recebimento_materiais_avancado.html:1258 TES selecionado: Object
recebimento_materiais_avancado.html:852 Uncaught ReferenceError: processReceipt is not defined
    at HTMLButtonElement.onclick (recebimento_materiais_avancado.html:852:76)
