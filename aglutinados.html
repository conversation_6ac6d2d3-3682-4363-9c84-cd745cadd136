<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análise de Estruturas de Produtos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f0f3f6;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --danger-color: #bb0000;
            --danger-hover: #a30000;
            --warning-color: #e9730c;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
        }

        .search-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
            padding: 20px;
            background-color: var(--secondary-color);
            border-radius: 8px;
        }

        .search-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .search-group label {
            font-weight: bold;
            color: var(--text-color);
        }

        .search-group input, .search-group select {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            background-color: var(--primary-color);
            color: white;
            transition: background-color 0.2s;
        }

        .button:hover {
            background-color: var(--primary-hover);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .product-list, .structure-analysis {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
        }

        .product-list h2, .structure-analysis h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 1;
        }

        .product-item {
            padding: 15px;
            border: 1px solid var(--border-color);
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .product-item:hover {
            background-color: var(--secondary-color);
            transform: translateX(5px);
        }

        .product-item.selected {
            border-left: 4px solid var(--primary-color);
            background-color: var(--secondary-color);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .product-code {
            font-weight: bold;
            color: var(--primary-color);
        }

        .product-level {
            background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .product-description {
            color: var(--text-secondary);
            margin-bottom: 10px;
        }

        .component-list {
            margin-top: 10px;
            padding-left: 20px;
            border-left: 2px solid var(--border-color);
        }

        .component-item {
            padding: 8px;
            margin-bottom: 5px;
            background-color: var(--secondary-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .component-item .quantity {
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 8px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid var(--secondary-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .comparison-table th, .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table th {
            background-color: var(--header-bg);
            color: white;
            font-weight: 500;
        }

        .match-high {
            background-color: rgba(16, 126, 62, 0.1);
        }

        .match-medium {
            background-color: rgba(233, 115, 12, 0.1);
        }

        .match-low {
            background-color: rgba(187, 0, 0, 0.1);
        }

        .common-components-badge {
            background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }

        .usage-count {
            background-color: var(--success-color);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            margin-left: 8px;
        }

        .common-component {
            border-left: 3px solid var(--success-color);
        }

        .analysis-section {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--secondary-color);
            border-radius: 8px;
        }

        .suggestion-item {
            margin-top: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border-left: 4px solid var(--warning-color);
        }

        .suggestion-item h5 {
            color: var(--warning-color);
            margin-bottom: 10px;
        }

        .suggestion-item ul {
            margin-left: 20px;
            margin-top: 10px;
        }

        .suggestion-item li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Análise de Estruturas de Produtos</h1>
            <button class="button" onclick="exportarAnalise()">
                <i class="fas fa-file-export"></i> Exportar Análise
            </button>
        </div>

        <div class="search-panel">
            <div class="search-group">
                <label for="nivelProduto">Nível do Produto</label>
                <select id="nivelProduto">
                    <option value="2">Nível 2</option>
                    <option value="3">Nível 3</option>
                    <option value="todos">Todos os Níveis</option>
                </select>
            </div>
            <div class="search-group">
                <label for="grupoProduto">Grupo</label>
                <select id="grupoProduto">
                    <option value="todos">Todos</option>
                    <!-- Será preenchido dinamicamente -->
                </select>
            </div>
            <div class="search-group">
                <label for="buscaProduto">Buscar Produto</label>
                <input type="text" id="buscaProduto" placeholder="Código ou descrição">
            </div>
            <div class="search-group" style="align-self: end;">
                <button class="button" onclick="buscarEstruturas()">
                    <i class="fas fa-search"></i> Buscar Estruturas
                </button>
            </div>
        </div>

        <div class="loading">
            <div class="spinner"></div>
            <p>Buscando estruturas de produtos...</p>
        </div>

        <div class="main-content">
            <div class="product-list">
                <h2>Produtos Nível 2</h2>
                <div id="produtosList">
                    <!-- Será preenchido dinamicamente -->
                </div>
            </div>

            <div class="structure-analysis">
                <h2>Análise de Componentes</h2>
                <div id="analiseComponentes">
                    <p>Selecione um produto para ver a análise de componentes</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs,
            doc,
            getDoc
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais para armazenar dados
        let produtos = [];
        let estruturas = [];
        let grupos = [];

        // Função para carregar dados do Firebase
        async function carregarDados() {
            const loading = document.querySelector('.loading');
            loading.classList.add('active');

            try {
                const [produtosSnap, estruturasSnap, gruposSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estruturas")),
                    getDocs(collection(db, "grupos"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                grupos = gruposSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Preencher select de grupos
                const selectGrupo = document.getElementById('grupoProduto');
                selectGrupo.innerHTML = '<option value="todos">Todos</option>';
                grupos.forEach(grupo => {
                    const option = document.createElement('option');
                    option.value = grupo.id;
                    option.textContent = grupo.codigo + ' - ' + grupo.descricao;
                    selectGrupo.appendChild(option);
                });

                // Buscar estruturas inicialmente
                buscarEstruturas();
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Por favor, tente novamente.');
            } finally {
                loading.classList.remove('active');
            }
        }

        // Função para buscar estruturas de produtos
        async function buscarEstruturas() {
            const loading = document.querySelector('.loading');
            loading.classList.add('active');

            try {
                const nivelProduto = document.getElementById('nivelProduto').value;
                const grupoProduto = document.getElementById('grupoProduto').value;
                const buscaProduto = document.getElementById('buscaProduto').value.toLowerCase();

                // Filtrar produtos conforme critérios
                let produtosFiltrados = produtos.filter(produto => {
                    const matchNivel = nivelProduto === 'todos' || 
                        (nivelProduto === '2' && produto.tipo === 'PA') ||
                        (nivelProduto === '3' && produto.tipo === 'SP');

                    const matchGrupo = grupoProduto === 'todos' || produto.grupo === grupoProduto;

                    const matchBusca = !buscaProduto || 
                        produto.codigo.toLowerCase().includes(buscaProduto) ||
                        produto.descricao.toLowerCase().includes(buscaProduto);

                    return matchNivel && matchGrupo && matchBusca;
                });

                // Analisar estruturas e identificar componentes comuns
                const analise = analisarEstruturas(produtosFiltrados);
                atualizarListaProdutos(analise);
            } catch (error) {
                console.error('Erro ao buscar estruturas:', error);
                alert('Erro ao buscar estruturas de produtos. Por favor, tente novamente.');
            } finally {
                loading.classList.remove('active');
            }
        }

        // Função para analisar estruturas e identificar componentes comuns
        function analisarEstruturas(produtosFiltrados) {
            const analise = {
                produtos: [],
                componentesComuns: new Map() // Mapa para rastrear componentes comuns
            };

            // Para cada produto, analisar sua estrutura
            produtosFiltrados.forEach(produto => {
                const estrutura = estruturas.find(e => e.produtoPaiId === produto.id);
                if (!estrutura) return;

                const produtoAnalise = {
                    ...produto,
                    componentes: [],
                    componentesComuns: new Set(),
                    niveisUtilizacao: new Map() // Mapa para rastrear níveis de utilização
                };

                // Analisar componentes recursivamente
                if (estrutura.componentes) {
                    estrutura.componentes.forEach(comp => {
                        const componenteProduto = produtos.find(p => p.id === comp.componentId);
                        if (!componenteProduto) return;

                        const componenteInfo = {
                            ...comp,
                            produto: componenteProduto,
                            niveisUtilizacao: new Set()
                        };

                        // Analisar onde este componente é usado
                        const usos = encontrarUsosComponente(comp.componentId);
                        componenteInfo.usos = usos;

                        // Se o componente é usado em múltiplos lugares, adicionar à lista de componentes comuns
                        if (usos.length > 1) {
                            produtoAnalise.componentesComuns.add(comp.componentId);
                            if (!analise.componentesComuns.has(comp.componentId)) {
                                analise.componentesComuns.set(comp.componentId, {
                                    produto: componenteProduto,
                                    usos: usos,
                                    produtosPai: new Set()
                                });
                            }
                            analise.componentesComuns.get(comp.componentId).produtosPai.add(produto.id);
                        }

                        produtoAnalise.componentes.push(componenteInfo);
                    });
                }

                analise.produtos.push(produtoAnalise);
            });

            return analise;
        }

        // Função para encontrar onde um componente é usado
        function encontrarUsosComponente(componentId, level = 0, path = []) {
            const usos = [];
            if (!estruturas || !componentId) return usos;

            estruturas.forEach(estrutura => {
                if (estrutura.componentes && estrutura.componentes.some(comp => comp.componentId === componentId)) {
                    const produtoPai = produtos.find(p => p.id === estrutura.produtoPaiId);
                    if (!produtoPai || path.includes(produtoPai.id)) return;

                    const componenteNaEstrutura = estrutura.componentes.find(comp => comp.componentId === componentId);
                    usos.push({
                        produtoPai: produtoPai,
                        nivel: level,
                        quantidade: componenteNaEstrutura.quantidade,
                        unidade: componenteNaEstrutura.unidade
                    });

                    // Buscar recursivamente onde o produto pai é usado
                    const parentUsages = encontrarUsosComponente(produtoPai.id, level + 1, [...path, produtoPai.id]);
                    usos.push(...parentUsages);
                }
            });

            return usos;
        }

        // Função para atualizar a lista de produtos
        function atualizarListaProdutos(analise) {
            const container = document.getElementById('produtosList');
            container.innerHTML = '';

            analise.produtos.forEach(produto => {
                const div = document.createElement('div');
                div.className = 'product-item';
                div.onclick = () => selecionarProduto(produto, analise.componentesComuns);

                const componentesComunsCount = produto.componentesComuns.size;
                const temComponentesComuns = componentesComunsCount > 0;

                div.innerHTML = `
                    <div class="product-header">
                        <span class="product-code">${produto.codigo}</span>
                        <span class="product-level">${produto.tipo}</span>
                        ${temComponentesComuns ? 
                            `<span class="common-components-badge" title="${componentesComunsCount} componentes em comum">
                                <i class="fas fa-link"></i> ${componentesComunsCount}
                            </span>` : 
                            ''}
                    </div>
                    <div class="product-description">${produto.descricao}</div>
                    <div class="component-list">
                        ${produto.componentes.map(comp => `
                            <div class="component-item ${comp.usos.length > 1 ? 'common-component' : ''}">
                                <span class="quantity">${comp.quantidade}x</span>
                                ${comp.produto.codigo} - ${comp.produto.descricao}
                                ${comp.usos.length > 1 ? 
                                    `<span class="usage-count" title="Usado em ${comp.usos.length} lugares">
                                        <i class="fas fa-link"></i> ${comp.usos.length}
                                    </span>` : 
                                    ''}
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Função para selecionar um produto e mostrar sua análise
        function selecionarProduto(produto, componentesComuns) {
            // Remove seleção anterior
            document.querySelectorAll('.product-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Adiciona seleção ao item clicado
            event.currentTarget.classList.add('selected');

            // Atualiza a análise de componentes
            const analiseContainer = document.getElementById('analiseComponentes');

            // Agrupar componentes por tipo de uso
            const componentesPorTipo = {
                comuns: produto.componentes.filter(comp => comp.usos.length > 1),
                unicos: produto.componentes.filter(comp => comp.usos.length === 1)
            };

            analiseContainer.innerHTML = `
                <h3>Análise de Componentes - ${produto.codigo}</h3>
                <p>Produto: ${produto.descricao}</p>
                <p>Tipo: ${produto.tipo}</p>
                <p>Grupo: ${produto.grupo || 'Não definido'}</p>

                <div class="analysis-section">
                    <h4>Componentes em Comum com Outros Produtos</h4>
                    ${componentesPorTipo.comuns.length > 0 ? `
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>Componente</th>
                                    <th>Quantidade</th>
                                    <th>Usado em</th>
                                    <th>Níveis</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${componentesPorTipo.comuns.map(comp => `
                                    <tr class="match-high">
                                        <td>${comp.produto.codigo} - ${comp.produto.descricao}</td>
                                        <td>${comp.quantidade} ${comp.unidade}</td>
                                        <td>${comp.usos.length} produtos</td>
                                        <td>${Array.from(new Set(comp.usos.map(u => u.nivel))).join(', ')}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p>Nenhum componente em comum encontrado.</p>'}
                </div>

                <div class="analysis-section">
                    <h4>Componentes Únicos</h4>
                    ${componentesPorTipo.unicos.length > 0 ? `
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>Componente</th>
                                    <th>Quantidade</th>
                                    <th>Tipo</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${componentesPorTipo.unicos.map(comp => `
                                    <tr>
                                        <td>${comp.produto.codigo} - ${comp.produto.descricao}</td>
                                        <td>${comp.quantidade} ${comp.unidade}</td>
                                        <td>${comp.produto.tipo}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p>Nenhum componente único encontrado.</p>'}
                </div>

                <div class="analysis-section">
                    <h4>Sugestões de Otimização</h4>
                    ${gerarSugestoesOtimizacao(produto, componentesComuns)}
                </div>
            `;
        }

        // Função para gerar sugestões de otimização
        function gerarSugestoesOtimizacao(produto, componentesComuns) {
            const sugestoes = [];

            // Verificar componentes comuns que podem ser consolidados
            const componentesParaConsolidar = produto.componentes.filter(comp => 
                comp.usos.length > 1 && comp.produto.tipo === 'SP'
            );

            if (componentesParaConsolidar.length > 0) {
                sugestoes.push(`
                    <div class="suggestion-item">
                        <h5><i class="fas fa-lightbulb"></i> Consolidação de Produção</h5>
                        <p>Os seguintes componentes podem ser consolidados em uma única ordem de produção:</p>
                        <ul>
                            ${componentesParaConsolidar.map(comp => `
                                <li>${comp.produto.codigo} - ${comp.produto.descricao} 
                                    (usado em ${comp.usos.length} produtos)</li>
                            `).join('')}
                        </ul>
                    </div>
                `);
            }

            // Verificar componentes que aparecem em múltiplos níveis
            const componentesMultiNivel = produto.componentes.filter(comp => {
                const niveis = new Set(comp.usos.map(u => u.nivel));
                return niveis.size > 1;
            });

            if (componentesMultiNivel.length > 0) {
                sugestoes.push(`
                    <div class="suggestion-item">
                        <h5><i class="fas fa-sitemap"></i> Otimização de Níveis</h5>
                        <p>Os seguintes componentes aparecem em múltiplos níveis da estrutura:</p>
                        <ul>
                            ${componentesMultiNivel.map(comp => `
                                <li>${comp.produto.codigo} - ${comp.produto.descricao} 
                                    (níveis: ${Array.from(new Set(comp.usos.map(u => u.nivel))).join(', ')})</li>
                            `).join('')}
                        </ul>
                    </div>
                `);
            }

            return sugestoes.length > 0 ? sugestoes.join('') : '<p>Nenhuma sugestão de otimização disponível.</p>';
        }

        // Função para exportar a análise
        function exportarAnalise() {
            const analise = {
                data: new Date().toISOString(),
                produtos: document.querySelectorAll('.product-item').length,
                componentesComuns: Array.from(document.querySelectorAll('.common-component')).length,
                detalhes: Array.from(document.querySelectorAll('.product-item')).map(item => {
                    const codigo = item.querySelector('.product-code').textContent;
                    const descricao = item.querySelector('.product-description').textContent;
                    const componentes = Array.from(item.querySelectorAll('.component-item')).map(comp => ({
                        codigo: comp.textContent.split(' - ')[0].trim(),
                        quantidade: comp.querySelector('.quantity').textContent,
                        comum: comp.classList.contains('common-component')
                    }));
                    return { codigo, descricao, componentes };
                })
            };

            const blob = new Blob([JSON.stringify(analise, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analise_estrutura_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', () => {
            carregarDados();
        });
    </script>
</body>
</html> 