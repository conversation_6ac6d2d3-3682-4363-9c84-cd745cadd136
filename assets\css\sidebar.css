/* 📋 SIDEBAR - FYRON MRP */

.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--sidebar-bg) 0%, #1a242e 100%);
    padding: var(--spacing-lg);
    transition: var(--transition);
    overflow-y: auto;
    z-index: var(--z-sidebar);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

/* 🏢 LOGO */
.sidebar .logo {
    display: block !important;
    width: 100% !important;
    max-width: var(--sidebar-width) !important;
    height: auto !important;
    margin: 0 auto var(--spacing-lg) auto !important;
    padding: 0 var(--spacing-sm);
}

.logo-placeholder {
    transition: var(--transition);
}

.logo-placeholder:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
}

#logoConfigBtn {
    transition: var(--animation-fast) ease;
}

#logoConfigBtn:hover {
    background: rgba(255,255,255,1) !important;
    transform: scale(1.1);
}

#logoMenu {
    animation: fadeIn var(--animation-fast) ease;
}

/* 👤 INFORMAÇÕES DO USUÁRIO */
.sidebar .user-info {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.sidebar .user-info .username {
    font-weight: bold;
    color: var(--primary-color);
}

.sidebar .logout-btn {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    font-size: var(--font-size-base);
    margin-top: var(--spacing-sm);
    transition: var(--transition);
}

.sidebar .logout-btn:hover {
    color: #a30000;
    transform: scale(1.05);
}

/* 🔍 BUSCA RÁPIDA */
.search-container {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255,255,255,0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255,255,255,0.1);
}

.search-title {
    color: var(--warning-color);
    font-size: var(--font-size-sm);
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.search-input {
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 6px;
    background: rgba(255,255,255,0.1);
    color: white;
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255,255,255,0.15);
}

.search-hint {
    font-size: 10px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 📋 NAVEGAÇÃO */
.nav-list {
    list-style: none;
}

.nav-list li {
    margin-bottom: var(--spacing-xs);
}

.nav-list .section-title {
    color: #9191a5;
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 🔘 BOTÕES DO MENU */
.nav-list .menu-button {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    text-decoration: none;
    font-size: var(--font-size-base);
    transition: var(--transition);
    cursor: pointer;
    border: none;
    width: 100%;
    text-align: left;
    margin-bottom: 2px;
    position: relative;
    overflow: hidden;
}

.nav-list .menu-button::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--animation-normal) ease;
}

.nav-list .menu-button i {
    margin-right: var(--spacing-sm);
    width: 16px;
    text-align: center;
    font-size: var(--font-size-base);
}

.nav-list .menu-button:hover {
    background: var(--sidebar-hover);
    color: #ffffff;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(8, 84, 160, 0.2);
}

.nav-list .menu-button:hover::before {
    transform: scaleY(1);
}

/* 🌟 BOTÕES DESTACADOS */
.nav-list .menu-button.highlight-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a4d8c 100%);
    color: white;
    font-weight: 600;
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    box-shadow: 0 2px 8px rgba(8, 84, 160, 0.3);
}

.nav-list .menu-button.highlight-btn::before {
    background: #ffffff;
}

.nav-list .menu-button.highlight-btn:hover {
    background: linear-gradient(135deg, #0a4d8c 0%, var(--primary-color) 100%);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(8, 84, 160, 0.4);
    color: #ffffff;
}

.nav-list .menu-button.highlight-btn::after {
    content: "NOVO";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #ffc107, #ff9800);
    color: #000;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: bold;
    letter-spacing: 0.5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* 💰 BOTÕES FINANCEIROS */
.nav-list .financial {
    background: var(--success-color);
}

.nav-list .financial:hover {
    background: #0d6e36;
}

/* ⚙️ BOTÕES DE CONFIGURAÇÃO */
.nav-list .settings {
    background: var(--warning-color);
}

.nav-list .settings:hover {
    background: #d66a0b;
}

/* 📱 MENU TOGGLE (MOBILE) */
.menu-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: var(--z-menu-toggle);
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(8, 84, 160, 0.3);
    cursor: pointer;
    transition: var(--transition);
}

.menu-toggle:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(8, 84, 160, 0.4);
}

/* 📂 ACCORDION */
.section-toggle {
    background: none;
    color: #9191a5;
    font-size: var(--font-size-base);
    border: none;
    width: 100%;
    text-align: left;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
}

.section-toggle:focus {
    outline: none;
}

.section-toggle:hover {
    color: white;
    background: rgba(255,255,255,0.05);
    border-radius: 6px;
}

.accordion-section {
    margin-bottom: var(--spacing-sm);
}

.accordion-content {
    display: none;
    padding-left: 0;
}

.accordion-content.active {
    display: block;
    animation: fadeIn var(--animation-normal) ease;
}

/* 📱 RESPONSIVIDADE */
@media (max-width: 768px) {
    .sidebar {
        left: calc(-1 * var(--sidebar-width));
        box-shadow: none;
    }

    .sidebar.active {
        left: 0;
        box-shadow: 4px 0 20px rgba(0,0,0,0.3);
    }

    .menu-toggle {
        display: block;
    }

    .nav-list .menu-button {
        font-size: var(--font-size-lg);
        padding: var(--font-size-base) var(--spacing-md);
    }

    .nav-list .menu-button i {
        margin-right: var(--spacing-md);
        font-size: var(--font-size-lg);
    }

    /* Logo responsivo */
    #logoContainer {
        width: 100% !important;
        max-width: 250px;
    }

    #companyLogo {
        width: 100% !important;
        max-width: 250px;
        height: auto !important;
        min-height: 100px;
    }
}
