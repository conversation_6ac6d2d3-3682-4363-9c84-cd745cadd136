<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cadastro de Clientes</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --success-hover: #0d6e36;
      --danger-color: #bb0000;
      --danger-hover: #a30000;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      margin: -20px -20px 20px -20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .form-container {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: var(--primary-color);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: var(--success-hover);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: var(--danger-hover);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
    }

    .customers-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .customers-table th,
    .customers-table td {
      padding: 12px 15px;
      border: 1px solid var(--border-color);
      text-align: left;
    }

    .customers-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--text-secondary);
      cursor: pointer;
      position: relative;
    }

    .customers-table th:hover {
      background-color: #e0e0e0;
    }

    .customers-table tr:hover {
      background-color: #f8f9fa;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
    }

    .edit-btn, .delete-btn {
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      cursor: pointer;
    }

    .edit-btn {
      background-color: #ffc107;
      color: #000;
    }

    .delete-btn {
      background-color: var(--danger-color);
      color: white;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }

    .required::after {
      content: "*";
      color: var(--danger-color);
      margin-left: 4px;
    }

    .table-container {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: #fff;
    }

    .search-container {
      margin-bottom: 20px;
    }

    .search-container input {
      width: 300px;
      padding: 8px 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .sort-indicator {
      margin-left: 5px;
      font-size: 12px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-col {
      flex: 1;
    }

    .categories-container {
      margin-bottom: 15px;
    }

    .category-item {
      display: inline-block;
      padding: 5px 10px;
      margin: 5px;
      background-color: var(--secondary-color);
      border-radius: 15px;
      font-size: 14px;
    }

    .category-item button {
      background: none;
      border: none;
      color: var(--danger-color);
      margin-left: 5px;
      padding: 0 5px;
      cursor: pointer;
      width: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Cadastro de Clientes</h1>
      <div>
        <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
      </div>
    </div>

    <div class="form-container">
      <h2 class="form-title">Cadastrar Novo Cliente</h2>
      <form id="customerForm">
        <input type="hidden" id="editingId">
        
        <div class="form-row">
          <div class="form-col">
            <label for="codigo" class="required">Código</label>
            <input type="text" id="codigo" name="codigo" required>
          </div>
          <div class="form-col">
            <label for="cnpj" class="required">CNPJ</label>
            <input type="text" id="cnpj" name="cnpj" required>
          </div>
        </div>

        <label for="nome" class="required">Nome/Razão Social</label>
        <input type="text" id="nome" name="nome" required>

        <div class="form-row">
          <div class="form-col">
            <label for="email" class="required">Email</label>
            <input type="email" id="email" name="email" required>
          </div>
          <div class="form-col">
            <label for="telefone" class="required">Telefone</label>
            <input type="tel" id="telefone" name="telefone" required>
          </div>
        </div>

        <label for="endereco" class="required">Endereço</label>
        <textarea id="endereco" name="endereco" rows="3" required></textarea>

        <label for="segmento">Segmento</label>
        <div class="form-row">
          <div class="form-col">
            <select id="segmentoSelect">
              <option value="">Selecione um segmento...</option>
              <option value="INDUSTRIAL">Industrial</option>
              <option value="COMERCIAL">Comercial</option>
              <option value="SERVICOS">Serviços</option>
              <option value="GOVERNO">Governo</option>
              <option value="EDUCACAO">Educação</option>
              <option value="SAUDE">Saúde</option>
              <option value="OUTROS">Outros</option>
            </select>
          </div>
          <div class="form-col">
            <button type="button" class="btn-secondary" onclick="addSegmento()">Adicionar</button>
          </div>
        </div>
        <div id="segmentosContainer" class="categories-container"></div>
        <input type="hidden" id="segmentos" name="segmentos">

        <label for="observacoes">Observações</label>
        <textarea id="observacoes" name="observacoes" rows="3"></textarea>

        <div class="form-actions">
          <button type="button" class="btn-secondary" onclick="cancelEdit()">Cancelar</button>
          <button type="submit" class="btn-success" id="submitButton">Cadastrar Cliente</button>
        </div>
      </form>
    </div>

    <div class="table-container">
      <h2 class="form-title">Clientes Cadastrados</h2>
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="Pesquisar por código, nome, CNPJ...">
      </div>
      <table class="customers-table">
        <thead>
          <tr>
            <th onclick="sortTable('codigo')">Código <span id="sortCodigo" class="sort-indicator"></span></th>
            <th onclick="sortTable('nome')">Nome/Razão Social <span id="sortNome" class="sort-indicator"></span></th>
            <th onclick="sortTable('cnpj')">CNPJ <span id="sortCnpj" class="sort-indicator"></span></th>
            <th onclick="sortTable('telefone')">Telefone <span id="sortTelefone" class="sort-indicator"></span></th>
            <th>Segmentos</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody id="customersTableBody">
        </tbody>
      </table>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      updateDoc, 
      deleteDoc 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let clientes = [];
    let selectedSegmentos = new Set();
    let sortDirection = 'asc';
    let currentSortColumn = '';

    window.onload = async function() {
      await loadClientes();
      document.getElementById('searchInput').addEventListener('input', filterClientes);
    };

    async function loadClientes() {
      try {
        const snapshot = await getDocs(collection(db, "clientes"));
        clientes = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        displayClientes();
      } catch (error) {
        console.error("Erro ao carregar clientes:", error);
        alert("Erro ao carregar clientes. Por favor, recarregue a página.");
      }
    }

    function displayClientes(filteredClientes = clientes) {
      const tableBody = document.getElementById('customersTableBody');
      tableBody.innerHTML = '';

      filteredClientes.forEach(cliente => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${cliente.codigo}</td>
          <td>${cliente.nome}</td>
          <td>${cliente.cnpj}</td>
          <td>${cliente.telefone}</td>
          <td>${formatSegmentos(cliente.segmentos)}</td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editCliente('${cliente.id}')">Editar</button>
            <button class="delete-btn" onclick="deleteCliente('${cliente.id}')">Excluir</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function formatSegmentos(segmentos) {
      if (!segmentos) return '';
      return segmentos.join(', ');
    }

    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      if (sortBy === 'codigo') {
        clientes.sort((a, b) => sortDirection === 'asc' 
          ? a.codigo.localeCompare(b.codigo) 
          : b.codigo.localeCompare(a.codigo));
      } else if (sortBy === 'nome') {
        clientes.sort((a, b) => sortDirection === 'asc' 
          ? a.nome.localeCompare(b.nome) 
          : b.nome.localeCompare(a.nome));
      } else if (sortBy === 'cnpj') {
        clientes.sort((a, b) => sortDirection === 'asc' 
          ? a.cnpj.localeCompare(b.cnpj) 
          : b.cnpj.localeCompare(a.cnpj));
      } else if (sortBy === 'telefone') {
        clientes.sort((a, b) => sortDirection === 'asc' 
          ? a.telefone.localeCompare(b.telefone) 
          : b.telefone.localeCompare(a.telefone));
      }

      updateSortIndicators(sortBy, sortDirection);
      displayClientes();
    };

    function updateSortIndicators(column, direction) {
      document.getElementById('sortCodigo').innerHTML = '';
      document.getElementById('sortNome').innerHTML = '';
      document.getElementById('sortCnpj').innerHTML = '';
      document.getElementById('sortTelefone').innerHTML = '';

      if (column === 'codigo') {
        document.getElementById('sortCodigo').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'nome') {
        document.getElementById('sortNome').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'cnpj') {
        document.getElementById('sortCnpj').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'telefone') {
        document.getElementById('sortTelefone').innerHTML = direction === 'asc' ? '▲' : '▼';
      }
    }

    window.addSegmento = function() {
      const select = document.getElementById('segmentoSelect');
      const segmento = select.value;
      
      if (!segmento) {
        alert('Selecione um segmento.');
        return;
      }

      if (selectedSegmentos.has(segmento)) {
        alert('Este segmento já foi adicionado.');
        return;
      }

      selectedSegmentos.add(segmento);
      updateSegmentosDisplay();
      select.value = '';
    };

    function updateSegmentosDisplay() {
      const container = document.getElementById('segmentosContainer');
      const segmentosInput = document.getElementById('segmentos');
      
      container.innerHTML = '';
      segmentosInput.value = Array.from(selectedSegmentos).join(',');

      selectedSegmentos.forEach(segmento => {
        const item = document.createElement('span');
        item.className = 'category-item';
        item.innerHTML = `
          ${segmento}
          <button type="button" onclick="removeSegmento('${segmento}')">×</button>
        `;
        container.appendChild(item);
      });
    }

    window.removeSegmento = function(segmento) {
      selectedSegmentos.delete(segmento);
      updateSegmentosDisplay();
    };

    window.editCliente = function(clienteId) {
      const cliente = clientes.find(c => c.id === clienteId);
      if (cliente) {
        document.getElementById('editingId').value = clienteId;
        document.getElementById('codigo').value = cliente.codigo;
        document.getElementById('nome').value = cliente.nome;
        document.getElementById('cnpj').value = cliente.cnpj;
        document.getElementById('email').value = cliente.email;
        document.getElementById('telefone').value = cliente.telefone;
        document.getElementById('endereco').value = cliente.endereco || '';
        document.getElementById('observacoes').value = cliente.observacoes || '';
        
        selectedSegmentos = new Set(cliente.segmentos || []);
        updateSegmentosDisplay();
        
        document.getElementById('submitButton').textContent = 'Atualizar Cliente';
      }
    };

    window.cancelEdit = function() {
      document.getElementById('customerForm').reset();
      document.getElementById('editingId').value = '';
      selectedSegmentos.clear();
      updateSegmentosDisplay();
      document.getElementById('submitButton').textContent = 'Cadastrar Cliente';
    };

    window.deleteCliente = async function(clienteId) {
      if (confirm('Tem certeza que deseja excluir este cliente?')) {
        try {
          await deleteDoc(doc(db, "clientes", clienteId));
          await loadClientes();
          alert('Cliente excluído com sucesso!');
        } catch (error) {
          console.error("Erro ao excluir cliente:", error);
          alert("Erro ao excluir cliente. Por favor, tente novamente.");
        }
      }
    };

    function filterClientes() {
      const searchText = document.getElementById('searchInput').value.toLowerCase();
      const filteredClientes = clientes.filter(cliente => 
        cliente.codigo.toLowerCase().includes(searchText) ||
        cliente.nome.toLowerCase().includes(searchText) ||
        cliente.cnpj.toLowerCase().includes(searchText)
      );
      displayClientes(filteredClientes);
    }

    document.getElementById('customerForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const formData = {
        codigo: document.getElementById('codigo').value,
        nome: document.getElementById('nome').value,
        cnpj: document.getElementById('cnpj').value,
        email: document.getElementById('email').value,
        telefone: document.getElementById('telefone').value,
        endereco: document.getElementById('endereco').value,
        segmentos: Array.from(selectedSegmentos),
        observacoes: document.getElementById('observacoes').value,
        dataCadastro: new Date()
      };

      const editingId = document.getElementById('editingId').value;

      try {
        if (editingId) {
          await updateDoc(doc(db, "clientes", editingId), formData);
          alert("Cliente atualizado com sucesso!");
        } else {
          const existingClient = clientes.find(c => 
            c.codigo === formData.codigo || c.cnpj === formData.cnpj
          );
          
          if (existingClient) {
            alert("Já existe um cliente com este código ou CNPJ.");
            return;
          }
          
          await addDoc(collection(db, "clientes"), formData);
          alert("Cliente cadastrado com sucesso!");
        }

        await loadClientes();
        event.target.reset();
        cancelEdit();
      } catch (error) {
        console.error("Erro ao salvar cliente:", error);
        alert("Erro ao salvar cliente. Por favor, tente novamente.");
      }
    });
  </script>
</body>
</html>