/**
 * NOTIFICATIONS COMPONENT - SISTEMA NALITECK
 * Componente para gerenciar notificações do sistema
 */

import { db } from '../firebase-config.js';
import { collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

export class NotificationsComponent {
  constructor(containerId, userId) {
    this.containerId = containerId;
    this.userId = userId;
    this.notifications = [];
    this.alerts = [];
    this.isVisible = false;
    
    this.initialize();
  }

  async initialize() {
    try {
      this.createNotificationButton();
      await this.loadData();
      this.startPeriodicUpdate();
    } catch (error) {
      console.error('Erro ao inicializar notificações:', error);
    }
  }

  createNotificationButton() {
    const container = document.getElementById(this.containerId);
    if (!container) return;

    container.innerHTML = `
      <button class="notifications-toggle" onclick="window.notificationsComponent.toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
      </button>
      <div class="notifications-wrapper" id="notificationsWrapper">
        <div class="notifications-header">
          <h3>Notificações</h3>
          <button onclick="window.notificationsComponent.markAllAsRead()">
            <i class="fas fa-check"></i> Marcar todas como lidas
          </button>
        </div>
        <div id="notificationsList">
          <div style="padding: 20px; text-align: center; color: #666;">
            Carregando notificações...
          </div>
        </div>
      </div>
    `;
  }

  async loadData() {
    try {
      console.log('📡 Carregando notificações...');
      
      // Busca simples sem filtros complexos
      const notificacoesSnapshot = await getDocs(collection(db, "notificacoes"));
      const alertasSnapshot = await getDocs(collection(db, "alertas"));
      
      const todasNotificacoes = notificacoesSnapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      }));
      
      const todosAlertas = alertasSnapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      }));
      
      // Filtra no cliente para o usuário atual
      this.notifications = todasNotificacoes.filter(notif => 
        notif.destinatarios && 
        (Array.isArray(notif.destinatarios) ? 
          notif.destinatarios.includes(this.userId) : 
          notif.destinatarios === this.userId)
      );
      
      this.alerts = todosAlertas.filter(alert => 
        alert.status === 'ATIVO'
      );
      
      this.updateUI();
      console.log(`✅ Carregadas ${this.notifications.length} notificações e ${this.alerts.length} alertas`);
      
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
      this.showError();
    }
  }

  updateUI() {
    const badge = document.getElementById('notificationBadge');
    const list = document.getElementById('notificationsList');
    
    if (!badge || !list) return;

    const unreadCount = this.notifications.filter(n => !n.lida).length + this.alerts.length;
    
    if (unreadCount > 0) {
      badge.textContent = unreadCount;
      badge.style.display = 'flex';
    } else {
      badge.style.display = 'none';
    }

    list.innerHTML = '';

    // Adiciona alertas críticos primeiro
    this.alerts.forEach(alert => {
      const alertElement = this.createAlertElement(alert);
      list.appendChild(alertElement);
    });

    // Adiciona notificações
    if (this.notifications.length === 0 && this.alerts.length === 0) {
      list.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Nenhuma notificação</div>';
    } else {
      this.notifications.forEach(notification => {
        const notificationElement = this.createNotificationElement(notification);
        list.appendChild(notificationElement);
      });
    }
  }

  createAlertElement(alert) {
    const div = document.createElement('div');
    div.className = `alert-item alert-${alert.severidade?.toLowerCase() || 'info'}`;
    
    const timeAgo = this.getTimeAgo(alert.dataCriacao);
    
    div.innerHTML = `
      <div class="alert-header">
        <span class="alert-title">${alert.titulo || 'Alerta'}</span>
        <span class="alert-time">${timeAgo}</span>
      </div>
      <div class="alert-message">${alert.mensagem || 'Sem descrição'}</div>
      ${alert.acaoRequerida ? `
        <div class="alert-actions">
          <button onclick="window.notificationsComponent.resolveAlert('${alert.id}')">
            <i class="fas fa-check"></i> Resolver
          </button>
          <button onclick="window.notificationsComponent.dismissAlert('${alert.id}')">
            <i class="fas fa-times"></i> Dispensar
          </button>
        </div>
      ` : ''}
    `;
    
    return div;
  }

  createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item ${!notification.lida ? 'unread' : ''}`;
    
    const timeAgo = this.getTimeAgo(notification.dataEnvio);
    
    div.innerHTML = `
      <div class="notification-header">
        <span class="notification-title">${notification.titulo || 'Notificação'}</span>
        <span class="notification-time">${timeAgo}</span>
      </div>
      <div class="notification-message">${notification.mensagem || 'Sem descrição'}</div>
    `;
    
    div.onclick = () => this.markAsRead(notification.id);
    
    return div;
  }

  getTimeAgo(timestamp) {
    if (!timestamp) return 'Agora';
    
    try {
      let date;
      if (timestamp.toDate) {
        date = timestamp.toDate();
      } else if (timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        date = new Date(timestamp);
      }
      
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      if (diffMins < 1) return 'Agora';
      if (diffMins < 60) return `${diffMins}min`;
      if (diffHours < 24) return `${diffHours}h`;
      return `${diffDays}d`;
    } catch (error) {
      return 'Agora';
    }
  }

  toggleNotifications() {
    const wrapper = document.getElementById('notificationsWrapper');
    if (!wrapper) return;
    
    this.isVisible = !this.isVisible;
    wrapper.classList.toggle('show', this.isVisible);
    
    if (this.isVisible) {
      this.loadData();
    }
  }

  async markAsRead(notificationId) {
    try {
      const notification = this.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.lida = true;
        this.updateUI();
      }
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
    }
  }

  async markAllAsRead() {
    try {
      this.notifications.forEach(n => n.lida = true);
      this.updateUI();
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  }

  async resolveAlert(alertId) {
    try {
      this.alerts = this.alerts.filter(a => a.id !== alertId);
      this.updateUI();
    } catch (error) {
      console.error('Erro ao resolver alerta:', error);
    }
  }

  async dismissAlert(alertId) {
    try {
      this.alerts = this.alerts.filter(a => a.id !== alertId);
      this.updateUI();
    } catch (error) {
      console.error('Erro ao dispensar alerta:', error);
    }
  }

  showError() {
    const list = document.getElementById('notificationsList');
    if (list) {
      list.innerHTML = '<div style="padding: 20px; text-align: center; color: #dc3545;">Erro ao carregar notificações</div>';
    }
  }

  startPeriodicUpdate() {
    // Atualiza a cada 5 minutos
    setInterval(() => {
      if (!this.isVisible) {
        this.loadData();
      }
    }, 5 * 60 * 1000);
  }
}