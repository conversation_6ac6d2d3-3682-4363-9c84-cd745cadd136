const express = require('express');
const path = require('path');

const app = express();
const PORT = 8000;

// Middleware para servir arquivos estáticos
app.use(express.static('.'));

// Middleware para logs
app.use((req, res, next) => {
    console.log(`[${new Date().toLocaleTimeString()}] ${req.method} ${req.url}`);
    next();
});

// Rota principal
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Iniciar servidor
app.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 Servidor rodando em http://127.0.0.1:${PORT}/`);
    console.log(`📁 Servindo arquivos de: ${__dirname}`);
    console.log(`⏰ Iniciado em: ${new Date().toLocaleString('pt-BR')}`);
    console.log('📋 Páginas disponíveis:');
    console.log(`   🏠 Principal: http://127.0.0.1:${PORT}/`);
    console.log(`   📋 Apontamentos: http://127.0.0.1:${PORT}/apontamentos_simplificado_novo.html`);
    console.log(`   📋 Necessidades: http://127.0.0.1:${PORT}/controle_baixa_necessidades.html`);
    console.log(`   👥 Fornecedores: http://127.0.0.1:${PORT}/cadastro_fornecedores.html`);
    console.log(`   🏗️ Estruturas: http://127.0.0.1:${PORT}/estrutura_nova.html`);
    console.log(`   🏭 Produção: http://127.0.0.1:${PORT}/ordens_producao.html`);
    console.log('');
    console.log('💡 Para parar o servidor, pressione Ctrl+C');
});