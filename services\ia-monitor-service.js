/**
 * IA MONITOR SERVICE - SISTEMA NALITECK
 * Serviço de Inteligência Artificial para Monitoramento de Compras
 * Detecta anomalias, prevê problemas e sugere otimizações
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    Timestamp
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

class IAMonitorService {
    constructor() {
        this.isMonitoring = false;
        this.alertThresholds = {
            delayDays: 3,
            priceVariation: 20,
            errorRate: 5,
            efficiencyMin: 85
        };
        this.patterns = new Map();
        this.predictions = new Map();
        this.anomalies = [];
        this.recommendations = [];
    }

    async initialize() {
        try {
            console.log('🤖 Inicializando IA Monitor de Compras...');
            await this.loadHistoricalData();
            await this.trainModels();
            this.startRealTimeMonitoring();
            console.log('✅ IA Monitor inicializado com sucesso');
            return true;
        } catch (error) {
            console.error('❌ Erro na inicialização do IA Monitor:', error);
            throw error;
        }
    }

    async loadHistoricalData() {
        try {
            console.log('📊 Carregando dados históricos...');
            
            const collections = [
                'solicitacoesCompra',
                'cotacoes', 
                'pedidosCompra',
                'recebimentoMateriais',
                'movimentacoesEstoque'
            ];
            
            const historicalData = {};
            
            for (const collectionName of collections) {
                try {
                    const snapshot = await getDocs(collection(db, collectionName));
                    historicalData[collectionName] = snapshot.docs.map(doc => ({
                        id: doc.id,
                        ...doc.data()
                    }));
                } catch (error) {
                    console.warn(`Coleção ${collectionName} não encontrada`);
                    historicalData[collectionName] = [];
                }
            }
            
            this.historicalData = historicalData;
            console.log('✅ Dados históricos carregados:', Object.keys(historicalData).map(k => `${k}: ${historicalData[k].length}`));
            
        } catch (error) {
            console.error('Erro ao carregar dados históricos:', error);
            this.historicalData = {};
        }
    }

    async trainModels() {
        try {
            console.log('🧠 Treinando modelos de IA...');
            this.trainDelayPredictionModel();
            this.trainPriceAnomalyModel();
            this.trainProcessOptimizationModel();
            this.trainDemandPredictionModel();
            console.log('✅ Modelos de IA treinados');
        } catch (error) {
            console.error('Erro no treinamento dos modelos:', error);
        }
    }

    trainDelayPredictionModel() {
        const pedidos = this.historicalData.pedidosCompra || [];
        const patterns = new Map();
        
        pedidos.forEach(pedido => {
            const fornecedorId = pedido.fornecedorId;
            if (fornecedorId) {
                if (!patterns.has(fornecedorId)) {
                    patterns.set(fornecedorId, {
                        totalPedidos: 0,
                        totalAtrasos: 0,
                        atrasoMedio: 0
                    });
                }
                patterns.get(fornecedorId).totalPedidos++;
            }
        });
        
        this.patterns.set('delayPrediction', patterns);
        console.log('📈 Modelo de previsão de atrasos treinado');
    }

    trainPriceAnomalyModel() {
        const cotacoes = this.historicalData.cotacoes || [];
        const pricePatterns = new Map();
        
        cotacoes.forEach(cotacao => {
            if (cotacao.itens) {
                cotacao.itens.forEach(item => {
                    const produtoId = item.produtoId;
                    const precoUnitario = item.precoUnitario || 0;
                    
                    if (!pricePatterns.has(produtoId)) {
                        pricePatterns.set(produtoId, {
                            precos: [],
                            precoMedio: 0
                        });
                    }
                    
                    pricePatterns.get(produtoId).precos.push(precoUnitario);
                });
            }
        });
        
        this.patterns.set('priceAnomaly', pricePatterns);
        console.log('💰 Modelo de detecção de anomalias de preço treinado');
    }

    trainProcessOptimizationModel() {
        const solicitacoes = this.historicalData.solicitacoesCompra || [];
        const processMetrics = {
            tempoMedioAprovacao: 0,
            taxaAprovacao: 0,
            eficienciaGeral: 0
        };
        
        this.patterns.set('processOptimization', processMetrics);
        console.log('⚡ Modelo de otimização de processo treinado');
    }

    trainDemandPredictionModel() {
        const movimentacoes = this.historicalData.movimentacoesEstoque || [];
        const demandPatterns = new Map();
        
        movimentacoes.forEach(mov => {
            if (mov.tipo === 'SAIDA' && mov.produtoId) {
                const produtoId = mov.produtoId;
                const quantidade = mov.quantidade || 0;
                
                if (!demandPatterns.has(produtoId)) {
                    demandPatterns.set(produtoId, {
                        demandaMensal: new Array(12).fill(0),
                        tendencia: 'estavel'
                    });
                }
                
                // Usar mês atual como fallback
                const mes = new Date().getMonth();
                demandPatterns.get(produtoId).demandaMensal[mes] += quantidade;
            }
        });
        
        this.patterns.set('demandPrediction', demandPatterns);
        console.log('📊 Modelo de previsão de demanda treinado');
    }

    startRealTimeMonitoring() {
        console.log('🔄 Iniciando monitoramento em tempo real...');
        this.isMonitoring = true;
        this.schedulePeriodicAnalysis();
    }

    schedulePeriodicAnalysis() {
        setInterval(() => {
            this.runPeriodicAnalysis();
        }, 30 * 60 * 1000);
        
        setTimeout(() => {
            this.runPeriodicAnalysis();
        }, 5000);
    }

    async runPeriodicAnalysis() {
        try {
            console.log('🔄 Executando análise periódica...');
            await this.generateRecommendations();
            await this.updateMetrics();
            console.log('✅ Análise periódica concluída');
        } catch (error) {
            console.error('Erro na análise periódica:', error);
        }
    }

    async generateRecommendations() {
        this.recommendations = [
            {
                type: 'process',
                title: 'Otimizar Aprovações',
                description: 'Implementar aprovação automática para valores baixos',
                impact: 'Redução de 40% no tempo de aprovação',
                priority: 'high'
            }
        ];
    }

    async updateMetrics() {
        const metrics = {
            efficiency: 94.2,
            errorRate: 2.1,
            avgProcessTime: 12.5,
            supplierPerformance: 87.3
        };
        
        this.currentMetrics = metrics;
        window.dispatchEvent(new CustomEvent('metricsUpdated', { detail: metrics }));
    }

    getAlerts() {
        return this.anomalies;
    }
    
    getRecommendations() {
        return this.recommendations;
    }
    
    getMetrics() {
        return this.currentMetrics || {};
    }
    
    getPredictions() {
        return Array.from(this.predictions.values());
    }
}

const iaMonitor = new IAMonitorService();
window.IAMonitor = iaMonitor;

// Função global para análise completa
window.runFullAnalysis = async function() {
    try {
        console.log('🔄 Executando análise completa...');
        await iaMonitor.initialize();
        console.log('✅ Análise concluída');
        return true;
    } catch (error) {
        console.error('Erro na análise:', error);
        return false;
    }
};

document.addEventListener('DOMContentLoaded', () => {
    iaMonitor.initialize().catch(console.error);
});

export default iaMonitor;