/**
 * SCRIPT DE CORREÇÃO DE INCONSISTÊNCIAS DE ESTOQUE
 * Execute este script para corrigir problemas identificados no sistema
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    doc, 
    updateDoc, 
    runTransaction,
    query,
    where,
    Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

class StockFixService {
    
    /**
     * 🔧 EXECUTAR TODAS AS CORREÇÕES
     */
    static async runAllFixes() {
        console.log('🚀 Iniciando correção de inconsistências de estoque...');
        
        const results = {
            fieldStandardization: await this.standardizeFields(),
            negativeStockFix: await this.fixNegativeStocks(),
            duplicateRecordsFix: await this.fixDuplicateRecords(),
            missingDataFix: await this.fixMissingData(),
            averageCostFix: await this.recalculateAverageCosts()
        };

        console.log('✅ Correções concluídas:', results);
        return results;
    }

    /**
     * 📝 PADRONIZAR CAMPOS
     */
    static async standardizeFields() {
        console.log('📝 Padronizando campos de estoque...');
        
        try {
            const stockSnapshot = await getDocs(collection(db, "estoques"));
            let fixedCount = 0;

            for (const stockDoc of stockSnapshot.docs) {
                const data = stockDoc.data();
                const updates = {};
                let needsUpdate = false;

                // Padronizar campo 'quantidade' para 'saldo'
                if (data.quantidade !== undefined && data.saldo === undefined) {
                    updates.saldo = data.quantidade;
                    updates.quantidade = null; // Remover campo antigo
                    needsUpdate = true;
                }

                // Garantir campos obrigatórios
                if (data.saldoReservado === undefined) {
                    updates.saldoReservado = 0;
                    needsUpdate = true;
                }

                if (data.saldoEmpenhado === undefined) {
                    updates.saldoEmpenhado = 0;
                    needsUpdate = true;
                }

                if (data.valorTotal === undefined) {
                    updates.valorTotal = 0;
                    needsUpdate = true;
                }

                if (data.custoMedio === undefined) {
                    updates.custoMedio = 0;
                    needsUpdate = true;
                }

                if (data.versao === undefined) {
                    updates.versao = 1;
                    needsUpdate = true;
                }

                if (needsUpdate) {
                    await updateDoc(stockDoc.ref, {
                        ...updates,
                        ultimaAtualizacao: Timestamp.now()
                    });
                    fixedCount++;
                }
            }

            return { success: true, fixedRecords: fixedCount };

        } catch (error) {
            console.error('Erro na padronização:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * ❌ CORRIGIR SALDOS NEGATIVOS
     */
    static async fixNegativeStocks() {
        console.log('❌ Corrigindo saldos negativos...');
        
        try {
            const stockQuery = query(
                collection(db, "estoques"),
                where("saldo", "<", 0)
            );
            
            const negativeStocks = await getDocs(stockQuery);
            let fixedCount = 0;

            for (const stockDoc of negativeStocks.docs) {
                const data = stockDoc.data();
                
                // Registrar ajuste antes de corrigir
                await this.registerAdjustment(stockDoc.id, {
                    tipo: 'AJUSTE_SALDO_NEGATIVO',
                    saldoAnterior: data.saldo,
                    saldoNovo: 0,
                    motivo: 'Correção automática de saldo negativo',
                    usuario: 'Sistema'
                });

                // Zerar saldo negativo
                await updateDoc(stockDoc.ref, {
                    saldo: 0,
                    valorTotal: 0,
                    custoMedio: 0,
                    ultimaCorrecao: Timestamp.now(),
                    versao: (data.versao || 0) + 1
                });

                fixedCount++;
            }

            return { success: true, fixedRecords: fixedCount };

        } catch (error) {
            console.error('Erro na correção de saldos negativos:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔄 CORRIGIR REGISTROS DUPLICADOS
     */
    static async fixDuplicateRecords() {
        console.log('🔄 Corrigindo registros duplicados...');
        
        try {
            const stockSnapshot = await getDocs(collection(db, "estoques"));
            const stockMap = new Map();
            const duplicates = [];

            // Identificar duplicatas
            stockSnapshot.docs.forEach(doc => {
                const data = doc.data();
                const key = `${data.produtoId}_${data.armazemId}`;
                
                if (stockMap.has(key)) {
                    duplicates.push({
                        key,
                        existing: stockMap.get(key),
                        duplicate: { id: doc.id, data }
                    });
                } else {
                    stockMap.set(key, { id: doc.id, data });
                }
            });

            let fixedCount = 0;

            // Consolidar duplicatas
            for (const dup of duplicates) {
                await this.consolidateDuplicateRecords(dup.existing, dup.duplicate);
                fixedCount++;
            }

            return { success: true, fixedRecords: fixedCount };

        } catch (error) {
            console.error('Erro na correção de duplicatas:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 📊 CONSOLIDAR REGISTROS DUPLICADOS
     */
    static async consolidateDuplicateRecords(existing, duplicate) {
        return await runTransaction(db, async (transaction) => {
            const existingRef = doc(db, "estoques", existing.id);
            const duplicateRef = doc(db, "estoques", duplicate.id);

            // Somar saldos
            const consolidatedData = {
                ...existing.data,
                saldo: (existing.data.saldo || 0) + (duplicate.data.saldo || 0),
                saldoReservado: (existing.data.saldoReservado || 0) + (duplicate.data.saldoReservado || 0),
                saldoEmpenhado: (existing.data.saldoEmpenhado || 0) + (duplicate.data.saldoEmpenhado || 0),
                valorTotal: (existing.data.valorTotal || 0) + (duplicate.data.valorTotal || 0),
                ultimaConsolidacao: Timestamp.now(),
                versao: (existing.data.versao || 0) + 1
            };

            // Recalcular custo médio
            if (consolidatedData.saldo > 0) {
                consolidatedData.custoMedio = consolidatedData.valorTotal / consolidatedData.saldo;
            }

            // Atualizar registro principal
            transaction.update(existingRef, consolidatedData);

            // Remover duplicata
            transaction.delete(duplicateRef);

            // Registrar auditoria
            const auditRef = doc(collection(db, "auditoria"));
            transaction.set(auditRef, {
                acao: 'CONSOLIDACAO_DUPLICATA',
                registroPrincipal: existing.id,
                registroDuplicado: duplicate.id,
                dadosConsolidados: consolidatedData,
                timestamp: Timestamp.now()
            });
        });
    }

    /**
     * 📋 CORRIGIR DADOS FALTANTES
     */
    static async fixMissingData() {
        console.log('📋 Corrigindo dados faltantes...');
        
        try {
            const stockSnapshot = await getDocs(collection(db, "estoques"));
            const [productsSnapshot, warehousesSnapshot] = await Promise.all([
                getDocs(collection(db, "produtos")),
                getDocs(collection(db, "armazens"))
            ]);

            const products = new Map();
            const warehouses = new Map();

            productsSnapshot.docs.forEach(doc => {
                products.set(doc.id, doc.data());
            });

            warehousesSnapshot.docs.forEach(doc => {
                warehouses.set(doc.id, doc.data());
            });

            let fixedCount = 0;

            for (const stockDoc of stockSnapshot.docs) {
                const data = stockDoc.data();
                const updates = {};
                let needsUpdate = false;

                // Completar dados do produto
                if (data.produtoId && products.has(data.produtoId)) {
                    const product = products.get(data.produtoId);
                    
                    if (!data.codigo && product.codigo) {
                        updates.codigo = product.codigo;
                        needsUpdate = true;
                    }
                    
                    if (!data.descricao && product.descricao) {
                        updates.descricao = product.descricao;
                        needsUpdate = true;
                    }
                    
                    if (!data.unidade && product.unidade) {
                        updates.unidade = product.unidade;
                        needsUpdate = true;
                    }
                }

                // Completar dados do armazém
                if (data.armazemId && warehouses.has(data.armazemId)) {
                    const warehouse = warehouses.get(data.armazemId);
                    
                    if (!data.armazemCodigo && warehouse.codigo) {
                        updates.armazemCodigo = warehouse.codigo;
                        needsUpdate = true;
                    }
                }

                if (needsUpdate) {
                    await updateDoc(stockDoc.ref, {
                        ...updates,
                        ultimaAtualizacao: Timestamp.now()
                    });
                    fixedCount++;
                }
            }

            return { success: true, fixedRecords: fixedCount };

        } catch (error) {
            console.error('Erro na correção de dados faltantes:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 💰 RECALCULAR CUSTOS MÉDIOS
     */
    static async recalculateAverageCosts() {
        console.log('💰 Recalculando custos médios...');
        
        try {
            const stockSnapshot = await getDocs(collection(db, "estoques"));
            let fixedCount = 0;

            for (const stockDoc of stockSnapshot.docs) {
                const data = stockDoc.data();
                
                if (data.saldo > 0 && data.valorTotal > 0) {
                    const correctAverageCost = data.valorTotal / data.saldo;
                    const currentAverageCost = data.custoMedio || 0;
                    
                    // Verificar se há diferença significativa
                    if (Math.abs(correctAverageCost - currentAverageCost) > 0.01) {
                        await updateDoc(stockDoc.ref, {
                            custoMedio: correctAverageCost,
                            custoMedioRecalculado: Timestamp.now(),
                            versao: (data.versao || 0) + 1
                        });
                        fixedCount++;
                    }
                } else if (data.saldo === 0) {
                    // Zerar custo médio se não há saldo
                    await updateDoc(stockDoc.ref, {
                        custoMedio: 0,
                        valorTotal: 0,
                        custoMedioRecalculado: Timestamp.now(),
                        versao: (data.versao || 0) + 1
                    });
                    fixedCount++;
                }
            }

            return { success: true, fixedRecords: fixedCount };

        } catch (error) {
            console.error('Erro no recálculo de custos médios:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 📝 REGISTRAR AJUSTE
     */
    static async registerAdjustment(stockId, adjustmentData) {
        const adjustmentRef = doc(collection(db, "ajustesEstoque"));
        
        await updateDoc(adjustmentRef, {
            stockId,
            ...adjustmentData,
            timestamp: Timestamp.now()
        });
    }

    /**
     * 📊 GERAR RELATÓRIO DE CORREÇÕES
     */
    static async generateFixReport() {
        console.log('📊 Gerando relatório de correções...');
        
        const report = {
            timestamp: new Date().toISOString(),
            corrections: await this.runAllFixes(),
            summary: {}
        };

        // Calcular resumo
        const totalFixed = Object.values(report.corrections)
            .reduce((sum, result) => sum + (result.fixedRecords || 0), 0);

        report.summary = {
            totalCorrections: totalFixed,
            success: Object.values(report.corrections).every(r => r.success),
            details: report.corrections
        };

        console.log('📋 Relatório de correções:', report);
        return report;
    }
}

// Executar correções automaticamente se chamado diretamente
if (typeof window !== 'undefined') {
    window.StockFixService = StockFixService;
    
    // Função global para executar correções
    window.runStockFixes = async () => {
        try {
            const report = await StockFixService.generateFixReport();
            alert(`Correções concluídas! Total de registros corrigidos: ${report.summary.totalCorrections}`);
            return report;
        } catch (error) {
            console.error('Erro nas correções:', error);
            alert('Erro ao executar correções: ' + error.message);
        }
    };
}

export { StockFixService };