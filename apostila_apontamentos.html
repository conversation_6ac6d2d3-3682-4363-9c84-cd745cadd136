<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Apostila - <PERSON>ó<PERSON><PERSON> de Apontamentos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: #34495e;
            padding: 15px 30px;
            border-bottom: 3px solid #3498db;
        }

        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }

        .nav a.active {
            background: #3498db;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: white;
            border-left: 5px solid #3498db;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h3 {
            color: #34495e;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .feature-card h4 {
            color: #27ae60;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .step-list {
            counter-reset: step-counter;
        }

        .step-item {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            position: relative;
        }

        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-item h4 {
            margin-left: 20px;
            color: #2c3e50;
        }

        .step-item p {
            margin-left: 20px;
            margin-top: 8px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .screenshot-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            color: #6c757d;
        }

        .screenshot-placeholder i {
            font-size: 3em;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .screenshot-container {
            margin: 20px 0;
            text-align: center;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .screenshot {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 8px;
            transition: transform 0.3s ease;
        }

        .screenshot:hover {
            transform: scale(1.02);
            cursor: pointer;
        }

        .screenshot-caption {
            background: #f8f9fa;
            padding: 10px;
            margin: 0;
            font-size: 0.9em;
            color: #495057;
            border-top: 1px solid #dee2e6;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Cabeçalho -->
        <div class="header">
            <h1>
                <i class="fas fa-clipboard-list"></i>
                APOSTILA - MÓDULO DE APONTAMENTOS
            </h1>
            <p>Sistema MRP - Gestão de Produção</p>
        </div>

        <!-- Navegação -->
        <nav class="nav">
            <ul>
                <li><a href="#visao-geral"><i class="fas fa-eye"></i> Visão Geral</a></li>
                <li><a href="#interface"><i class="fas fa-desktop"></i> Interface</a></li>
                <li><a href="#funcionalidades"><i class="fas fa-cogs"></i> Funcionalidades</a></li>
                <li><a href="#processo"><i class="fas fa-tasks"></i> Processo</a></li>
                <li><a href="#validacao"><i class="fas fa-check-circle"></i> Validação</a></li>
                <li><a href="#relatorios"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-tools"></i> Suporte</a></li>
            </ul>
        </nav>

        <!-- Conteúdo -->
        <div class="content">
            <!-- Visão Geral -->
            <section id="visao-geral" class="section">
                <h2><i class="fas fa-eye"></i> Visão Geral</h2>
                
                <p>O <strong>Módulo de Apontamentos</strong> é o coração do controle de produção do sistema MRP. Ele permite o registro em tempo real da produção, garantindo rastreabilidade completa e controle preciso dos materiais consumidos.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-clipboard-check"></i> Apontamento de Produção</h4>
                        <p>Registro rápido e preciso da produção realizada com validação automática de materiais.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-boxes"></i> Controle de Materiais</h4>
                        <p>Verificação automática de disponibilidade e consumo de matérias-primas e subprodutos.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-clock"></i> Registro de Tempos</h4>
                        <p>Controle de horários de produção e produtividade por operador e máquina.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-line"></i> Relatórios em Tempo Real</h4>
                        <p>Dashboards e relatórios atualizados automaticamente com os dados de produção.</p>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Importante:</strong> O módulo integra-se automaticamente com Estoque, Compras e Planejamento, mantendo todos os dados sincronizados.
                </div>
            </section>

            <!-- Interface Principal -->
            <section id="interface" class="section">
                <h2><i class="fas fa-desktop"></i> Interface Principal</h2>

                <h3><i class="fas fa-home"></i> Tela Inicial</h3>
                <div class="screenshot-container">
                    <img src="tela_principal_apontamentos.png" alt="Tela Principal do Módulo" class="screenshot">
                    <p class="screenshot-caption"><strong>Tela Principal do Módulo</strong> - Lista de OPs disponíveis para apontamento</p>
                </div>

                <h3><i class="fas fa-filter"></i> Filtros e Busca</h3>
                <p>A interface oferece múltiplas opções de filtro para localizar rapidamente as ordens de produção:</p>
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>Filtro</th>
                            <th>Descrição</th>
                            <th>Exemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-calendar"></i> Data</td>
                            <td>Filtro por período de criação ou previsão</td>
                            <td>01/01/2025 a 31/01/2025</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-warehouse"></i> Armazém</td>
                            <td>Filtro por local de produção</td>
                            <td>Produção Principal</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-tag"></i> Status</td>
                            <td>Filtro por situação da OP</td>
                            <td>LIBERADA, EM_PRODUCAO</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-search"></i> Busca</td>
                            <td>Busca por número da OP ou produto</td>
                            <td>OP25070821 ou PROD001</td>
                        </tr>
                    </tbody>
                </table>

                <div class="screenshot-container">
                    <img src="filtros_busca_apontamentos.png" alt="Filtros de Busca" class="screenshot">
                    <p class="screenshot-caption"><strong>Filtros de Busca</strong> - Interface de filtros na parte superior da tela</p>
                </div>
            </section>

            <!-- Funcionalidades Principais -->
            <section id="funcionalidades" class="section">
                <h2><i class="fas fa-cogs"></i> Funcionalidades Principais</h2>

                <h3><i class="fas fa-clipboard-check"></i> 1. Apontamento de Produção</h3>
                <p>A funcionalidade principal permite registrar a produção realizada de forma rápida e precisa.</p>

                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Disponível quando:</strong> OP com status "LIBERADA" e materiais validados
                </div>

                <div class="screenshot-container">
                    <img src="botao_apontar_producao.png" alt="Botão Apontar Produção" class="screenshot">
                    <p class="screenshot-caption"><strong>Botão Apontar Produção</strong> - Botão verde disponível na linha da OP</p>
                </div>

                <h4><i class="fas fa-form"></i> Modal de Apontamento</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Campo</th>
                            <th>Descrição</th>
                            <th>Obrigatório</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-hashtag"></i> OP</td>
                            <td>Número da ordem de produção (somente leitura)</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-box"></i> Produto</td>
                            <td>Código e descrição do produto (somente leitura)</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-calculator"></i> Quantidade</td>
                            <td>Quantidade produzida a ser apontada</td>
                            <td>✅ Sim</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-calendar"></i> Data</td>
                            <td>Data do apontamento</td>
                            <td>✅ Sim</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-clock"></i> Hora</td>
                            <td>Hora do apontamento</td>
                            <td>✅ Sim</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-user"></i> Operador</td>
                            <td>Responsável pela produção</td>
                            <td>✅ Sim</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-comment"></i> Observações</td>
                            <td>Comentários adicionais</td>
                            <td>❌ Não</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-search"></i> 2. Verificação de Saldo</h3>
                <p>Funcionalidade essencial que valida a disponibilidade de todos os materiais necessários antes da produção.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-boxes"></i> Matérias-Primas</h4>
                        <p>Verifica saldo disponível no armazém de produção, considerando reservas e empenhos.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-industry"></i> Subprodutos</h4>
                        <p>Identifica se subprodutos necessários já foram produzidos e estão disponíveis.</p>
                    </div>
                </div>

                <div class="screenshot-placeholder">
                    <i class="fas fa-image"></i>
                    <p><strong>Captura de Tela: Modal de Materiais Faltantes</strong></p>
                    <p>Tabela detalhada com status de cada material</p>
                </div>

                <h3><i class="fas fa-print"></i> 3. Impressão de OP</h3>
                <p>Gera relatório completo da ordem de produção para acompanhamento no chão de fábrica.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Conteúdo do Relatório:</strong> Dados da OP, lista de materiais, quantidades, especificações técnicas e informações do armazém.
                </div>
            </section>

            <!-- Processo de Apontamento -->
            <section id="processo" class="section">
                <h2><i class="fas fa-tasks"></i> Processo de Apontamento</h2>

                <p>O processo de apontamento segue uma sequência lógica que garante a integridade dos dados e a rastreabilidade completa.</p>

                <div class="step-list">
                    <div class="step-item">
                        <h4>Localizar a Ordem de Produção</h4>
                        <p>Use os filtros disponíveis para encontrar a OP desejada. Verifique se o status é "LIBERADA" e se todos os pré-requisitos foram atendidos.</p>
                    </div>

                    <div class="step-item">
                        <h4>Verificar Disponibilidade de Materiais</h4>
                        <p>Clique em "Verificar Saldo" para validar se todos os materiais necessários estão disponíveis no armazém de produção.</p>
                    </div>

                    <div class="step-item">
                        <h4>Resolver Pendências (se houver)</h4>
                        <p>Caso existam materiais em falta, resolva as pendências transferindo materiais ou produzindo subprodutos necessários.</p>
                    </div>

                    <div class="step-item">
                        <h4>Realizar o Apontamento</h4>
                        <p>Com todos os materiais validados, clique em "Apontar Produção" e preencha os dados solicitados no formulário.</p>
                    </div>

                    <div class="step-item">
                        <h4>Confirmar e Finalizar</h4>
                        <p>Revise os dados informados e confirme o apontamento. O sistema atualizará automaticamente todos os saldos relacionados.</p>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Atenção:</strong> Após confirmar o apontamento, os dados não podem ser alterados. Certifique-se de que todas as informações estão corretas.
                </div>
            </section>

            <!-- Validação de Materiais -->
            <section id="validacao" class="section">
                <h2><i class="fas fa-check-circle"></i> Validação de Materiais</h2>

                <p>O sistema realiza validação automática e inteligente dos materiais, considerando diferentes tipos e suas particularidades.</p>

                <h3><i class="fas fa-cog"></i> Tipos de Validação</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-box"></i> Matérias-Primas (MP)</h4>
                        <ul>
                            <li>✅ Verifica saldo no armazém de produção</li>
                            <li>📊 Considera reservas para outras OPs</li>
                            <li>🔒 Considera empenhos existentes</li>
                            <li>⚠️ Alerta sobre saldo insuficiente</li>
                            <li>🔄 Sugere transferência do almoxarifado</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-industry"></i> Subprodutos (SP)</h4>
                        <ul>
                            <li>✅ Verifica se já foram produzidos</li>
                            <li>📋 Identifica OPs de SP pendentes</li>
                            <li>⚠️ Alerta se SP não disponível</li>
                            <li>🏭 Sugere produção do SP primeiro</li>
                            <li>🔗 Mostra dependências entre OPs</li>
                        </ul>
                    </div>
                </div>

                <h3><i class="fas fa-traffic-light"></i> Status de Validação</h3>

                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>✅ VALIDADO:</strong> Todos os materiais disponíveis, saldos suficientes, pode realizar apontamento.
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>⚠️ PENDENTE:</strong> Alguns materiais em falta, necessita ação do usuário, requer transferência/produção.
                </div>

                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>❌ BLOQUEADO:</strong> Materiais críticos em falta, não pode realizar apontamento, necessita resolução urgente.
                </div>
            </section>

            <!-- Relatórios e Consultas -->
            <section id="relatorios" class="section">
                <h2><i class="fas fa-chart-bar"></i> Relatórios e Consultas</h2>

                <h3><i class="fas fa-list"></i> Relatório de Materiais</h3>
                <p>Relatório detalhado que mostra o status de todos os materiais necessários para a produção.</p>

                <div class="screenshot-placeholder">
                    <i class="fas fa-image"></i>
                    <p><strong>Captura de Tela: Relatório de Materiais</strong></p>
                    <p>Tabela com código, descrição, tipo, necessário, disponível e faltante</p>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th>Coluna</th>
                            <th>Descrição</th>
                            <th>Exemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-barcode"></i> Código</td>
                            <td>Código do material</td>
                            <td>100866, MPA033</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-tag"></i> Descrição</td>
                            <td>Nome/descrição do produto</td>
                            <td>CHAPA LISA 16,0 - 1140x700 POS</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-industry"></i> Tipo</td>
                            <td>Classificação do material</td>
                            <td>MATÉRIA-PRIMA (MP), SUBPRODUTO (SP)</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-calculator"></i> Necessário</td>
                            <td>Quantidade necessária para produção</td>
                            <td>1.000, 2.000</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-boxes"></i> Disponível</td>
                            <td>Saldo atual no armazém</td>
                            <td>0.000, 1.500</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-exclamation-triangle"></i> Faltante</td>
                            <td>Quantidade em falta</td>
                            <td>1.000, 0.500</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-history"></i> Histórico de Apontamentos</h3>
                <p>Registro completo de todos os apontamentos realizados, permitindo rastreabilidade total da produção.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-calendar-alt"></i> Dados Temporais</h4>
                        <p>Data e hora exata de cada apontamento, permitindo análise de produtividade por período.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-user-tie"></i> Responsabilidade</h4>
                        <p>Registro do operador responsável por cada apontamento para controle de qualidade.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-line"></i> Quantidades</h4>
                        <p>Histórico detalhado das quantidades produzidas e materiais consumidos.</p>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-sticky-note"></i> Observações</h4>
                        <p>Comentários e observações registradas durante o processo produtivo.</p>
                    </div>
                </div>
            </section>

            <!-- Troubleshooting -->
            <section id="troubleshooting" class="section">
                <h2><i class="fas fa-tools"></i> Troubleshooting</h2>

                <h3><i class="fas fa-exclamation-circle"></i> Problemas Comuns</h3>

                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>❌ "Material em Falta"</strong>
                </div>
                <p><strong>Causa:</strong> Saldo insuficiente no armazém de produção</p>
                <p><strong>Solução:</strong></p>
                <ol>
                    <li>🔍 Verificar saldo no almoxarifado principal</li>
                    <li>📦 Transferir material necessário para produção</li>
                    <li>🔄 Executar nova validação de materiais</li>
                    <li>✅ Confirmar disponibilidade antes de apontar</li>
                </ol>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>⚠️ "Subproduto Não Disponível"</strong>
                </div>
                <p><strong>Causa:</strong> Subproduto necessário não foi produzido ainda</p>
                <p><strong>Solução:</strong></p>
                <ol>
                    <li>📋 Localizar OP do subproduto necessário</li>
                    <li>🏭 Produzir o subproduto primeiro</li>
                    <li>✅ Confirmar conclusão da OP do SP</li>
                    <li>🔄 Retornar à OP principal</li>
                </ol>

                <div class="alert alert-info">
                    <i class="fas fa-lock"></i>
                    <strong>🔒 "OP Não Liberada"</strong>
                </div>
                <p><strong>Causa:</strong> OP não passou pela validação completa</p>
                <p><strong>Solução:</strong></p>
                <ol>
                    <li>✅ Verificar status atual da OP</li>
                    <li>📋 Completar etapas de planejamento</li>
                    <li>🔍 Validar disponibilidade de materiais</li>
                    <li>🔄 Aguardar liberação automática</li>
                </ol>

                <h3><i class="fas fa-lightbulb"></i> Dicas de Uso</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-check-double"></i> Boas Práticas</h4>
                        <ul>
                            <li>🔍 Sempre verificar saldo antes de apontar</li>
                            <li>📅 Manter datas e horários atualizados</li>
                            <li>👤 Registrar operador correto</li>
                            <li>📝 Documentar observações importantes</li>
                            <li>🔄 Validar dados antes de confirmar</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-shield-alt"></i> Cuidados Especiais</h4>
                        <ul>
                            <li>🔄 Não apontar sem validação prévia</li>
                            <li>📊 Não exceder quantidade planejada</li>
                            <li>⏰ Manter horários realistas</li>
                            <li>🔒 Não alterar OPs finalizadas</li>
                            <li>💾 Fazer backup dos dados importantes</li>
                        </ul>
                    </div>
                </div>

                <h3><i class="fas fa-headset"></i> Suporte Técnico</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-phone"></i> Contatos</h4>
                        <ul>
                            <li>📧 <strong>Email:</strong> <EMAIL></li>
                            <li>📱 <strong>Telefone:</strong> (17) 3042-1507</li>
                            <li>💬 <strong>Chat:</strong> Disponível no sistema</li>
                            <li>🕐 <strong>Horário:</strong> 8h às 18h</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4><i class="fas fa-book"></i> Documentação</h4>
                        <ul>
                            <li>📚 Manual do Usuário Completo</li>
                            <li>🎥 Vídeos Tutoriais</li>
                            <li>❓ FAQ - Perguntas Frequentes</li>
                            <li>🔧 Guia de Troubleshooting</li>
                        </ul>
                    </div>
                </div>
            </section>
        </div>

        <!-- Rodapé -->
        <div class="footer">
            <p><i class="fas fa-copyright"></i> 2025 Sistema MRP - Módulo de Apontamentos</p>
            <p>Apostila gerada automaticamente | Versão 1.0 | Última atualização: Janeiro 2025</p>
            <p><i class="fas fa-envelope"></i> <EMAIL> | <i class="fas fa-phone"></i> (17) 3042-1507</p>
        </div>
    </div>

    <script>
        // Navegação suave
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Destacar seção ativa na navegação
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
