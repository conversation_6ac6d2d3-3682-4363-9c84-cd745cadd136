<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Correção - condicaoPagamentoId undefined</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      margin: 0;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
      padding: 20px;
      text-align: center;
    }

    .content {
      padding: 30px;
    }

    .alert {
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid;
    }

    .alert-danger {
      background-color: #f8d7da;
      border-color: #dc3545;
      color: #721c24;
    }

    .alert-warning {
      background-color: #fff3cd;
      border-color: #ffc107;
      color: #856404;
    }

    .alert-success {
      background-color: #d4edda;
      border-color: #28a745;
      color: #155724;
    }

    .alert-info {
      background-color: #d1ecf1;
      border-color: #17a2b8;
      color: #0c5460;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin: 5px;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background-color: #0056b3;
    }

    .btn-success {
      background-color: #28a745;
      color: white;
    }

    .btn-success:hover {
      background-color: #1e7e34;
    }

    .btn-warning {
      background-color: #ffc107;
      color: #212529;
    }

    .btn-warning:hover {
      background-color: #e0a800;
    }

    .progress {
      width: 100%;
      height: 20px;
      background-color: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
      margin: 10px 0;
    }

    .progress-bar {
      height: 100%;
      background-color: #007bff;
      transition: width 0.3s ease;
      border-radius: 10px;
    }

    .log-container {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      padding: 15px;
      max-height: 300px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      margin-top: 20px;
    }

    .log-entry {
      margin-bottom: 5px;
      padding: 2px 0;
    }

    .log-success {
      color: #28a745;
    }

    .log-error {
      color: #dc3545;
    }

    .log-warning {
      color: #ffc107;
    }

    .log-info {
      color: #17a2b8;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .stat-card {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      text-align: center;
      border-left: 4px solid #007bff;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #007bff;
    }

    .stat-label {
      font-size: 12px;
      color: #6c757d;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔧 Correção de Erro: condicaoPagamentoId undefined</h1>
      <p>Ferramenta para corrigir o erro de campo undefined no Firestore</p>
    </div>

    <div class="content">
      <div class="alert alert-danger">
        <h4>🚨 Problema Identificado</h4>
        <p><strong>Erro:</strong> FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field condicaoPagamentoId in document contasAPagar/...)</p>
        <p><strong>Causa:</strong> O sistema está tentando salvar documentos com campos undefined, o que não é permitido no Firestore.</p>
      </div>

      <div class="alert alert-info">
        <h4>📋 O que esta ferramenta faz:</h4>
        <ul>
          <li>✅ Verifica se existe condição de pagamento padrão</li>
          <li>✅ Cria condição padrão se não existir</li>
          <li>✅ Corrige documentos com condicaoPagamentoId undefined</li>
          <li>✅ Aplica validação para prevenir futuros erros</li>
        </ul>
      </div>

      <div class="stats">
        <div class="stat-card">
          <div class="stat-value" id="totalPedidos">-</div>
          <div class="stat-label">Pedidos Analisados</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="pedidosProblema">-</div>
          <div class="stat-label">Com Problema</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="contasProblema">-</div>
          <div class="stat-label">Contas com Problema</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="corrigidos">-</div>
          <div class="stat-label">Corrigidos</div>
        </div>
      </div>

      <div style="text-align: center; margin: 20px 0;">
        <button class="btn btn-primary" id="btnAnalyze" onclick="analisarProblemas()">
          🔍 Analisar Problemas
        </button>
        <button class="btn btn-warning" id="btnFix" onclick="corrigirProblemas()" disabled>
          🔧 Corrigir Problemas
        </button>
        <button class="btn btn-success" id="btnValidate" onclick="aplicarValidacao()">
          ✅ Aplicar Validação
        </button>
      </div>

      <div class="progress" style="display: none;" id="progressContainer">
        <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
      </div>

      <div class="log-container" id="logContainer">
        <div class="log-entry log-info">🚀 Sistema pronto para análise...</div>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      addDoc, 
      updateDoc, 
      doc, 
      query, 
      where, 
      limit,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let problemData = null;
    let defaultCondition = null;

    // Função para adicionar log
    function addLog(message, type = 'info') {
      const logContainer = document.getElementById('logContainer');
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry log-${type}`;
      logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    }

    // Função para atualizar progresso
    function updateProgress(percent) {
      const progressContainer = document.getElementById('progressContainer');
      const progressBar = document.getElementById('progressBar');
      
      if (percent > 0) {
        progressContainer.style.display = 'block';
        progressBar.style.width = percent + '%';
      } else {
        progressContainer.style.display = 'none';
      }
    }

    // Função para buscar ou criar condição de pagamento padrão
    async function getDefaultPaymentCondition() {
      try {
        addLog('🔍 Buscando condição de pagamento padrão...', 'info');
        
        // Primeiro, tentar buscar por descrição "À Vista"
        const aVistaQuery = query(
          collection(db, "condicoesPagamento"),
          where("descricao", "==", "À Vista"),
          limit(1)
        );
        
        const aVistaSnap = await getDocs(aVistaQuery);
        if (!aVistaSnap.empty) {
          const condition = { id: aVistaSnap.docs[0].id, ...aVistaSnap.docs[0].data() };
          addLog(`✅ Encontrada condição "À Vista": ${condition.descricao}`, 'success');
          return condition;
        }
        
        // Se não encontrar "À Vista", buscar por tipo "A_VISTA"
        const tipoQuery = query(
          collection(db, "condicoesPagamento"),
          where("tipo", "==", "A_VISTA"),
          limit(1)
        );
        
        const tipoSnap = await getDocs(tipoQuery);
        if (!tipoSnap.empty) {
          const condition = { id: tipoSnap.docs[0].id, ...tipoSnap.docs[0].data() };
          addLog(`✅ Encontrada condição tipo "A_VISTA": ${condition.descricao}`, 'success');
          return condition;
        }
        
        // Se não encontrar nenhuma, buscar a primeira disponível
        const allQuery = query(collection(db, "condicoesPagamento"), limit(1));
        const allSnap = await getDocs(allQuery);
        
        if (!allSnap.empty) {
          const condition = { id: allSnap.docs[0].id, ...allSnap.docs[0].data() };
          addLog(`⚠️ Usando primeira condição disponível: ${condition.descricao}`, 'warning');
          return condition;
        }
        
        // Se não existir nenhuma, criar uma padrão
        addLog('📝 Criando condição de pagamento padrão...', 'info');
        const newCondition = {
          descricao: 'À Vista',
          tipo: 'A_VISTA',
          parcelas: 1,
          intervalo: 0,
          entrada: 100,
          ativo: true,
          dataCriacao: Timestamp.now()
        };
        
        const docRef = await addDoc(collection(db, "condicoesPagamento"), newCondition);
        addLog('✅ Condição padrão criada com sucesso', 'success');
        
        return { id: docRef.id, ...newCondition };
        
      } catch (error) {
        addLog(`❌ Erro ao buscar condição de pagamento: ${error.message}`, 'error');
        throw error;
      }
    }

    // Função para analisar problemas
    window.analisarProblemas = async function() {
      try {
        const btnAnalyze = document.getElementById('btnAnalyze');
        const btnFix = document.getElementById('btnFix');
        
        btnAnalyze.disabled = true;
        btnAnalyze.textContent = '🔍 Analisando...';
        
        addLog('🚀 Iniciando análise de problemas...', 'info');
        updateProgress(10);
        
        // Buscar condição padrão
        defaultCondition = await getDefaultPaymentCondition();
        updateProgress(30);
        
        // Analisar pedidos
        addLog('📋 Analisando pedidos de compra...', 'info');
        const pedidosSnap = await getDocs(collection(db, "pedidosCompra"));
        const pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const pedidosSemCondicao = pedidos.filter(p => !p.condicaoPagamentoId);
        
        updateProgress(60);
        
        // Analisar contas a pagar
        addLog('💰 Analisando contas a pagar...', 'info');
        const contasSnap = await getDocs(collection(db, "contasAPagar"));
        const contas = contasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const contasSemCondicao = contas.filter(c => !c.condicaoPagamentoId);
        
        updateProgress(90);
        
        // Resultado da análise
        problemData = {
          defaultCondition,
          pedidos: {
            total: pedidos.length,
            semCondicao: pedidosSemCondicao.length,
            lista: pedidosSemCondicao
          },
          contas: {
            total: contas.length,
            semCondicao: contasSemCondicao.length,
            lista: contasSemCondicao
          }
        };
        
        // Atualizar estatísticas
        document.getElementById('totalPedidos').textContent = pedidos.length;
        document.getElementById('pedidosProblema').textContent = pedidosSemCondicao.length;
        document.getElementById('contasProblema').textContent = contasSemCondicao.length;
        document.getElementById('corrigidos').textContent = '0';
        
        updateProgress(100);
        
        addLog(`📊 Análise concluída:`, 'success');
        addLog(`   • ${pedidos.length} pedidos analisados`, 'info');
        addLog(`   • ${pedidosSemCondicao.length} pedidos sem condição`, 'warning');
        addLog(`   • ${contas.length} contas analisadas`, 'info');
        addLog(`   • ${contasSemCondicao.length} contas sem condição`, 'warning');
        
        if (pedidosSemCondicao.length > 0 || contasSemCondicao.length > 0) {
          btnFix.disabled = false;
          addLog('⚠️ Problemas encontrados! Clique em "Corrigir Problemas"', 'warning');
        } else {
          addLog('✅ Nenhum problema encontrado!', 'success');
        }
        
        setTimeout(() => updateProgress(0), 2000);
        
      } catch (error) {
        addLog(`❌ Erro na análise: ${error.message}`, 'error');
      } finally {
        btnAnalyze.disabled = false;
        btnAnalyze.textContent = '🔍 Analisar Problemas';
      }
    };

    // Função para corrigir problemas
    window.corrigirProblemas = async function() {
      if (!problemData || !defaultCondition) {
        addLog('❌ Execute a análise primeiro!', 'error');
        return;
      }

      try {
        const btnFix = document.getElementById('btnFix');
        btnFix.disabled = true;
        btnFix.textContent = '🔧 Corrigindo...';
        
        addLog('🔧 Iniciando correção de problemas...', 'info');
        let corrigidos = 0;
        
        // Corrigir pedidos
        if (problemData.pedidos.semCondicao.length > 0) {
          addLog(`📋 Corrigindo ${problemData.pedidos.semCondicao.length} pedidos...`, 'info');
          
          for (const pedido of problemData.pedidos.lista) {
            try {
              await updateDoc(doc(db, "pedidosCompra", pedido.id), {
                condicaoPagamentoId: defaultCondition.id
              });
              
              addLog(`✅ Pedido ${pedido.numero || pedido.id} corrigido`, 'success');
              corrigidos++;
              
            } catch (error) {
              addLog(`❌ Erro ao corrigir pedido ${pedido.id}: ${error.message}`, 'error');
            }
          }
        }
        
        // Corrigir contas a pagar
        if (problemData.contas.semCondicao.length > 0) {
          addLog(`💰 Corrigindo ${problemData.contas.semCondicao.length} contas...`, 'info');
          
          for (const conta of problemData.contas.lista) {
            try {
              await updateDoc(doc(db, "contasAPagar", conta.id), {
                condicaoPagamentoId: defaultCondition.id
              });
              
              addLog(`✅ Conta ${conta.numero || conta.id} corrigida`, 'success');
              corrigidos++;
              
            } catch (error) {
              addLog(`❌ Erro ao corrigir conta ${conta.id}: ${error.message}`, 'error');
            }
          }
        }
        
        document.getElementById('corrigidos').textContent = corrigidos;
        addLog(`🎉 Correção concluída! ${corrigidos} documentos corrigidos`, 'success');
        
      } catch (error) {
        addLog(`❌ Erro na correção: ${error.message}`, 'error');
      } finally {
        const btnFix = document.getElementById('btnFix');
        btnFix.disabled = false;
        btnFix.textContent = '🔧 Corrigir Problemas';
      }
    };

    // Função para aplicar validação
    window.aplicarValidacao = function() {
      addLog('📝 Código de validação para aplicar nos arquivos:', 'info');
      addLog('', 'info');
      addLog('// ADICIONAR ANTES DE addDoc(collection(db, "contasAPagar"), contaData):', 'warning');
      addLog('', 'info');
      addLog('// Validar e corrigir condicaoPagamentoId', 'info');
      addLog('if (!contaData.condicaoPagamentoId) {', 'info');
      addLog('  // Buscar condição padrão', 'info');
      addLog('  const condicoesSnap = await getDocs(', 'info');
      addLog('    query(collection(db, "condicoesPagamento"),', 'info');
      addLog('          where("descricao", "==", "À Vista"), limit(1))', 'info');
      addLog('  );', 'info');
      addLog('  if (!condicoesSnap.empty) {', 'info');
      addLog('    contaData.condicaoPagamentoId = condicoesSnap.docs[0].id;', 'info');
      addLog('  }', 'info');
      addLog('}', 'info');
      addLog('', 'info');
      addLog('// Remover campos undefined', 'info');
      addLog('Object.keys(contaData).forEach(key => {', 'info');
      addLog('  if (contaData[key] === undefined) {', 'info');
      addLog('    delete contaData[key];', 'info');
      addLog('  }', 'info');
      addLog('});', 'info');
      addLog('', 'info');
      addLog('✅ Copie este código e aplique nos arquivos que geram o erro!', 'success');
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
      addLog('🚀 Sistema de correção carregado', 'success');
      addLog('👆 Clique em "Analisar Problemas" para começar', 'info');
    });

  </script>
</body>
</html>
