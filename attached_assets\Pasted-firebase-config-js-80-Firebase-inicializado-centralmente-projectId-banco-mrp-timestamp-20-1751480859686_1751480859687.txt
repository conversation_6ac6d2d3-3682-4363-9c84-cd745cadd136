firebase-config.js:80 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-02T18:27:25.526Z', apps: 1}
pedidos_compra.html:1141 🔐 Verificando autenticação...
pedidos_compra.html:1150 ✅ Usuário autenticado: admin
notification-service.js:435 🔍 Verificando pedidos em atraso...
notification-service.js:564 🔔 Sistema de notificações inicializado
pedidos_compra.html:4341 🔔 Serviço de notificações carregado e inicializado
pedidos_compra.html:1314 Dados carregados: {fornecedores: 778, produtos: 1677, cotacoes: 59, pedidosCompra: 44, centrosCusto: 4, …}
pedidos_compra.html:1368 4 centros de custo ativos carregados
pedidos_compra.html:1171 📝 Acesso ao módulo de pedidos: admin
notification-service.js:520 ❌ Erro ao verificar notificação: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=Ck5wcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL25vdGlmaWNhY29lcy9pbmRleGVzL18QARoPCgtkb2N1bWVudG9JZBABGggKBHRpcG8QARoPCgtkYXRhQ3JpYWNhbxABGgwKCF9fbmFtZV9fEAE
wasNotifiedToday @ notification-service.js:520
await in wasNotifiedToday
checkDelayedOrders @ notification-service.js:485
notification-service.js:65 Erro ao criar notificação: FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field documentoOrigem in document notificacoes/xSsoOglDPXRcIHl8VuFQ)
createNotification @ notification-service.js:65
notifyAtrasoEntrega @ notification-service.js:382
await in notifyAtrasoEntrega
checkDelayedOrders @ notification-service.js:488
notification-service.js:396 ❌ Erro ao notificar atraso: FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field documentoOrigem in document notificacoes/xSsoOglDPXRcIHl8VuFQ)
notifyAtrasoEntrega @ notification-service.js:396
await in notifyAtrasoEntrega
checkDelayedOrders @ notification-service.js:488
notification-service.js:495 ✅ Verificação concluída: 2 pedidos verificados, 1 notificações enviadas
