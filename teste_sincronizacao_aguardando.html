<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Teste de Sincronização - Flag Aguardando</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #667eea;
            color: white;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-success {
            background: #28a745;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-aguardando {
            background: #17a2b8;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        .status-normal {
            background: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Teste de Sincronização - Flag Aguardando</h1>
            <p>Verificação da sincronização entre movimentacao_armazem_novo.html e apontamentos_simplificado_novo.html</p>
        </div>

        <div class="section">
            <h3>📋 Como Funciona a Sincronização</h3>
            <ol>
                <li><strong>movimentacao_armazem_novo.html:</strong> Define o status "AGUARDANDO" para um material específico</li>
                <li><strong>Firebase:</strong> Salva o campo <code>aguardandoMaterial: true</code> no array <code>materiaisNecessarios</code></li>
                <li><strong>apontamentos_simplificado_novo.html:</strong> Lê automaticamente e exibe o flag</li>
            </ol>
        </div>

        <button class="btn" onclick="verificarSincronizacao()">🔍 Verificar Sincronização</button>
        <button class="btn btn-warning" onclick="simularMarcacaoAguardando()">⏰ Simular Marcação</button>
        <button class="btn" onclick="limparLog()">🧹 Limpar Log</button>

        <div class="section">
            <h3>📋 Log de Verificação</h3>
            <div id="log" class="log">Clique em "Verificar Sincronização" para iniciar...</div>
        </div>

        <div class="section">
            <h3>📊 Status dos Materiais</h3>
            <div id="statusMateriais">Aguardando verificação...</div>
        </div>

        <div class="section">
            <h3>🔧 Instruções de Teste</h3>
            <div>
                <h4>Para testar a sincronização:</h4>
                <ol>
                    <li>Abra <strong>movimentacao_armazem_novo.html</strong></li>
                    <li>Selecione uma OP com materiais</li>
                    <li>Clique no botão <strong>"Aguardar"</strong> de um material</li>
                    <li>Confirme a ação</li>
                    <li>Abra <strong>apontamentos_simplificado_novo.html</strong></li>
                    <li>Verifique se o material aparece com o flag <strong>"Aguardando chegada"</strong></li>
                </ol>
                
                <h4>Campos sincronizados:</h4>
                <ul>
                    <li><code>aguardandoMaterial</code>: boolean</li>
                    <li><code>dataAguardo</code>: timestamp</li>
                    <li><code>usuarioAguardo</code>: string</li>
                </ul>
            </div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            query,
            where,
            orderBy,
            limit
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let logContainer;

        window.onload = function() {
            logContainer = document.getElementById('log');
        };

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        window.limparLog = function() {
            logContainer.innerHTML = 'Log limpo...<br>';
        };

        window.verificarSincronizacao = async function() {
            try {
                log('🔍 Iniciando verificação de sincronização...');
                
                // Buscar ordens de produção com materiais aguardando
                log('📦 Buscando ordens de produção...');
                const ordensSnap = await getDocs(
                    query(
                        collection(db, "ordensProducao"),
                        orderBy("numero", "desc"),
                        limit(20)
                    )
                );
                
                const ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                log(`📦 Encontradas ${ordens.length} ordens de produção`);
                
                // Buscar produtos para referência
                const produtosSnap = await getDocs(collection(db, "produtos"));
                const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                // Analisar materiais aguardando
                let materiaisAguardando = [];
                let totalMateriais = 0;
                
                ordens.forEach(ordem => {
                    if (ordem.materiaisNecessarios && Array.isArray(ordem.materiaisNecessarios)) {
                        ordem.materiaisNecessarios.forEach(material => {
                            totalMateriais++;
                            
                            if (material.aguardandoMaterial) {
                                const produto = produtos.find(p => p.id === material.produtoId);
                                materiaisAguardando.push({
                                    ordemNumero: ordem.numero,
                                    ordemId: ordem.id,
                                    produtoId: material.produtoId,
                                    produtoCodigo: produto?.codigo || 'N/A',
                                    produtoDescricao: produto?.descricao || 'N/A',
                                    dataAguardo: material.dataAguardo,
                                    usuarioAguardo: material.usuarioAguardo
                                });
                            }
                        });
                    }
                });
                
                log(`📊 Total de materiais analisados: ${totalMateriais}`);
                log(`⏰ Materiais aguardando: ${materiaisAguardando.length}`);
                
                exibirStatusMateriais(materiaisAguardando);
                
                if (materiaisAguardando.length > 0) {
                    log('✅ Sincronização funcionando! Materiais aguardando encontrados.');
                } else {
                    log('ℹ️ Nenhum material aguardando encontrado. Teste marcando um material como aguardando.');
                }
                
            } catch (error) {
                log(`❌ Erro durante verificação: ${error.message}`);
                console.error('Erro:', error);
            }
        };

        function exibirStatusMateriais(materiaisAguardando) {
            let html = '';
            
            if (materiaisAguardando.length === 0) {
                html = `
                    <div style="text-align: center; padding: 20px; color: #6c757d;">
                        <i class="fas fa-info-circle" style="font-size: 2em; margin-bottom: 10px;"></i>
                        <p>Nenhum material aguardando encontrado.</p>
                        <p>Marque um material como "aguardando" no sistema de movimentação para testar.</p>
                    </div>
                `;
            } else {
                html = `
                    <table class="data-table">
                        <tr>
                            <th>OP</th>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Data Aguardo</th>
                            <th>Usuário</th>
                            <th>Status</th>
                        </tr>
                `;
                
                materiaisAguardando.forEach(material => {
                    const dataAguardo = material.dataAguardo ? 
                        new Date(material.dataAguardo.seconds * 1000).toLocaleString('pt-BR') : 
                        'N/A';
                    
                    html += `
                        <tr>
                            <td><strong>${material.ordemNumero}</strong></td>
                            <td>${material.produtoCodigo}</td>
                            <td>${material.produtoDescricao}</td>
                            <td>${dataAguardo}</td>
                            <td>${material.usuarioAguardo || 'N/A'}</td>
                            <td><span class="status-aguardando">⏰ AGUARDANDO</span></td>
                        </tr>
                    `;
                });
                
                html += '</table>';
            }
            
            document.getElementById('statusMateriais').innerHTML = html;
        }

        window.simularMarcacaoAguardando = function() {
            log('💡 Para simular a marcação de aguardando:');
            log('1. Abra movimentacao_armazem_novo.html');
            log('2. Selecione uma OP');
            log('3. Clique em "Aguardar" em um material');
            log('4. Volte aqui e clique em "Verificar Sincronização"');
            log('5. Abra apontamentos_simplificado_novo.html para ver o flag');
            
            alert(`
🔧 INSTRUÇÕES PARA TESTE:

1. Abra movimentacao_armazem_novo.html
2. Selecione uma OP com materiais
3. Clique no botão "Aguardar" de um material
4. Confirme a ação
5. Volte aqui e clique em "Verificar Sincronização"
6. Abra apontamentos_simplificado_novo.html
7. Verifique se o material aparece com "⏰ Aguardando chegada"

A sincronização é automática via Firebase!
            `);
        };
    </script>
</body>
</html>
