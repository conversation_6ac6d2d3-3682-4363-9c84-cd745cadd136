<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow de Aprovação - Orçamentos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --totvs-primary: #0854a0;
            --totvs-secondary: #f0f3f6;
            --totvs-success: #107e3e;
            --totvs-warning: #e9730c;
            --totvs-danger: #bb0000;
            --totvs-info: #17a2b8;
            --totvs-border: #d4d4d4;
            --totvs-text: #333;
            --totvs-bg: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--totvs-bg);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--totvs-primary) 0%, #0a4d8c 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 4px solid var(--totvs-primary);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.pending {
            background: var(--totvs-warning);
        }

        .stat-icon.approved {
            background: var(--totvs-success);
        }

        .stat-icon.rejected {
            background: var(--totvs-danger);
        }

        .stat-icon.total {
            background: var(--totvs-info);
        }

        .stat-info h3 {
            font-size: 24px;
            color: var(--totvs-text);
            margin-bottom: 5px;
        }

        .stat-info p {
            color: #666;
            font-size: 14px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card h3 {
            color: var(--totvs-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            border-bottom: 2px solid var(--totvs-secondary);
            padding-bottom: 10px;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--totvs-text);
            margin-bottom: 5px;
        }

        .form-control {
            padding: 10px;
            border: 2px solid var(--totvs-border);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--totvs-primary);
            box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--totvs-primary);
            color: white;
        }

        .btn-success {
            background: var(--totvs-success);
            color: white;
        }

        .btn-warning {
            background: var(--totvs-warning);
            color: white;
        }

        .btn-danger {
            background: var(--totvs-danger);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--totvs-border);
        }

        .table th {
            background: var(--totvs-secondary);
            font-weight: 600;
            color: var(--totvs-text);
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: #fff3cd;
            color: #856404;
        }

        .status-aprovado {
            background: #d4edda;
            color: #155724;
        }

        .status-rejeitado {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority-alta {
            background: var(--totvs-danger);
            color: white;
        }

        .priority-media {
            background: var(--totvs-warning);
            color: white;
        }

        .priority-baixa {
            background: var(--totvs-info);
            color: white;
        }

        .workflow-steps {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .workflow-step {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .workflow-step.completed {
            background: #d4edda;
            color: #155724;
        }

        .workflow-step.current {
            background: #fff3cd;
            color: #856404;
        }

        .workflow-step.pending {
            background: #e2e3e5;
            color: #6c757d;
        }

        .workflow-arrow {
            color: #6c757d;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: var(--totvs-primary);
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--totvs-border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            opacity: 0.7;
        }

        .approval-details {
            background: var(--totvs-secondary);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .approval-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }

        .approval-item:last-child {
            border-bottom: none;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--totvs-success);
        }

        .notification-error {
            background: var(--totvs-danger);
        }

        .notification-info {
            background: var(--totvs-primary);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--totvs-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-tasks"></i>
                Workflow de Aprovação - Orçamentos
            </h1>
            <div>
                <button class="btn btn-primary" onclick="atualizarDados()">
                    <i class="fas fa-sync"></i> Atualizar
                </button>
                <a href="index.html" class="btn btn-warning">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="statPendentes">0</h3>
                        <p>Pendentes de Aprovação</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon approved">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="statAprovados">0</h3>
                        <p>Aprovados Hoje</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon rejected">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="statRejeitados">0</h3>
                        <p>Rejeitados Hoje</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon total">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="statTotal">R$ 0</h3>
                        <p>Valor Total Pendente</p>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="card">
                <h3><i class="fas fa-filter"></i> Filtros</h3>
                
                <div class="filters">
                    <div class="form-group">
                        <label>Status</label>
                        <select id="filtroStatus" class="form-control" onchange="aplicarFiltros()">
                            <option value="">Todos</option>
                            <option value="PENDENTE">Pendente</option>
                            <option value="APROVADO">Aprovado</option>
                            <option value="REJEITADO">Rejeitado</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Prioridade</label>
                        <select id="filtroPrioridade" class="form-control" onchange="aplicarFiltros()">
                            <option value="">Todas</option>
                            <option value="ALTA">Alta</option>
                            <option value="MEDIA">Média</option>
                            <option value="BAIXA">Baixa</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Vendedor</label>
                        <select id="filtroVendedor" class="form-control" onchange="aplicarFiltros()">
                            <option value="">Todos</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Valor Mínimo</label>
                        <input type="number" id="filtroValorMin" class="form-control" placeholder="R$ 0,00" onchange="aplicarFiltros()">
                    </div>
                </div>
            </div>

            <!-- Lista de Orçamentos -->
            <div class="card">
                <h3><i class="fas fa-list"></i> Orçamentos Pendentes de Aprovação</h3>
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Cliente</th>
                            <th>Vendedor</th>
                            <th>Valor</th>
                            <th>Margem</th>
                            <th>Prioridade</th>
                            <th>Status</th>
                            <th>Workflow</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="orcamentosTableBody">
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px;">
                                Carregando orçamentos...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Aprovação -->
    <div id="approvalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">
                    <i class="fas fa-check-circle"></i> Aprovar Orçamento
                </h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <div id="orcamentoDetails"></div>
                
                <div class="form-group">
                    <label><i class="fas fa-comment"></i> Observações</label>
                    <textarea id="observacoesAprovacao" class="form-control" rows="4" placeholder="Observações sobre a aprovação/rejeição..."></textarea>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-warning" onclick="closeModal()">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button type="button" class="btn btn-danger" onclick="rejeitarOrcamento()">
                    <i class="fas fa-times-circle"></i> Rejeitar
                </button>
                <button type="button" class="btn btn-success" onclick="aprovarOrcamento()">
                    <i class="fas fa-check-circle"></i> Aprovar
                </button>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            updateDoc,
            doc,
            Timestamp,
            query,
            where,
            orderBy
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

        // Variáveis globais
        let orcamentos = [];
        let vendedores = [];
        let currentOrcamento = null;

        // Configurações de alçadas
        const alcadas = {
            vendedor: { limite: 10000, margemMinima: 20 },
            supervisor: { limite: 50000, margemMinima: 15 },
            gerente: { limite: 100000, margemMinima: 10 },
            diretor: { limite: Infinity, margemMinima: 5 }
        };

        // Inicialização
        document.addEventListener('DOMContentLoaded', async () => {
            await loadData();
            await updateStats();
        });

        // Carregar dados
        async function loadData() {
            try {
                showLoading(true);

                const [orcamentosSnap, vendedoresSnap] = await Promise.all([
                    getDocs(query(collection(db, "orcamentos"), orderBy("dataCriacao", "desc"))),
                    getDocs(query(collection(db, "usuarios"), where("perfil", "==", "vendedor")))
                ]);

                orcamentos = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                vendedores = vendedoresSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                populateVendedoresFilter();
                displayOrcamentos();

                console.log('Dados carregados:', {
                    orcamentos: orcamentos.length,
                    vendedores: vendedores.length
                });

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados do sistema', 'error');
            } finally {
                showLoading(false);
            }
        }

        // Popular filtro de vendedores
        function populateVendedoresFilter() {
            const select = document.getElementById('filtroVendedor');
            select.innerHTML = '<option value="">Todos</option>';

            vendedores.forEach(vendedor => {
                const option = document.createElement('option');
                option.value = vendedor.id;
                option.textContent = vendedor.nome;
                select.appendChild(option);
            });
        }

        // Atualizar estatísticas
        async function updateStats() {
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);

            const pendentes = orcamentos.filter(o => o.statusAprovacao === 'PENDENTE');
            const aprovadosHoje = orcamentos.filter(o => {
                const dataAprovacao = o.dataAprovacao?.toDate?.();
                return o.statusAprovacao === 'APROVADO' &&
                       dataAprovacao && dataAprovacao >= hoje;
            });
            const rejeitadosHoje = orcamentos.filter(o => {
                const dataRejeicao = o.dataRejeicao?.toDate?.();
                return o.statusAprovacao === 'REJEITADO' &&
                       dataRejeicao && dataRejeicao >= hoje;
            });

            const valorTotalPendente = pendentes.reduce((sum, o) => sum + (o.valorTotal || 0), 0);

            document.getElementById('statPendentes').textContent = pendentes.length;
            document.getElementById('statAprovados').textContent = aprovadosHoje.length;
            document.getElementById('statRejeitados').textContent = rejeitadosHoje.length;
            document.getElementById('statTotal').textContent = `R$ ${valorTotalPendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        }

        // Exibir orçamentos
        function displayOrcamentos(orcamentosToShow = orcamentos) {
            const tbody = document.getElementById('orcamentosTableBody');

            // Filtrar apenas pendentes por padrão
            const orcamentosFiltrados = orcamentosToShow.filter(o =>
                o.statusAprovacao === 'PENDENTE' || !o.statusAprovacao
            );

            if (orcamentosFiltrados.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-check-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                            Nenhum orçamento pendente de aprovação
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            orcamentosFiltrados.forEach(orcamento => {
                const row = document.createElement('tr');

                // Calcular prioridade baseada em valor e margem
                const prioridade = calcularPrioridade(orcamento);
                const workflow = gerarWorkflowSteps(orcamento);

                row.innerHTML = `
                    <td style="font-weight: 600; color: var(--totvs-primary);">${orcamento.numero}</td>
                    <td>${orcamento.clienteNome || 'N/A'}</td>
                    <td>${orcamento.vendedorNome || 'N/A'}</td>
                    <td style="font-weight: 600;">R$ ${(orcamento.valorTotal || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</td>
                    <td>${(orcamento.margemMedia || 0).toFixed(1)}%</td>
                    <td>
                        <span class="priority-badge priority-${prioridade.toLowerCase()}">
                            ${prioridade}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge status-${(orcamento.statusAprovacao || 'pendente').toLowerCase()}">
                            ${orcamento.statusAprovacao || 'PENDENTE'}
                        </span>
                    </td>
                    <td>
                        <div class="workflow-steps">
                            ${workflow}
                        </div>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="openApprovalModal('${orcamento.id}')" style="margin-right: 5px;">
                            <i class="fas fa-eye"></i> Analisar
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Calcular prioridade
        function calcularPrioridade(orcamento) {
            const valor = orcamento.valorTotal || 0;
            const margem = orcamento.margemMedia || 0;

            if (valor > 50000 || margem < 10) return 'ALTA';
            if (valor > 20000 || margem < 15) return 'MEDIA';
            return 'BAIXA';
        }

        // Gerar steps do workflow
        function gerarWorkflowSteps(orcamento) {
            const valor = orcamento.valorTotal || 0;
            const margem = orcamento.margemMedia || 0;

            let steps = [];

            // Determinar níveis necessários
            if (valor <= alcadas.vendedor.limite && margem >= alcadas.vendedor.margemMinima) {
                steps = ['Vendedor'];
            } else if (valor <= alcadas.supervisor.limite && margem >= alcadas.supervisor.margemMinima) {
                steps = ['Vendedor', 'Supervisor'];
            } else if (valor <= alcadas.gerente.limite && margem >= alcadas.gerente.margemMinima) {
                steps = ['Vendedor', 'Supervisor', 'Gerente'];
            } else {
                steps = ['Vendedor', 'Supervisor', 'Gerente', 'Diretor'];
            }

            const currentStep = orcamento.nivelAprovacaoAtual || 0;

            return steps.map((step, index) => {
                let className = 'workflow-step ';
                if (index < currentStep) className += 'completed';
                else if (index === currentStep) className += 'current';
                else className += 'pending';

                const icon = index < currentStep ? 'fa-check' :
                           index === currentStep ? 'fa-clock' : 'fa-circle';

                return `<span class="${className}"><i class="fas ${icon}"></i> ${step}</span>`;
            }).join('<i class="fas fa-arrow-right workflow-arrow"></i>');
        }

        // Aplicar filtros
        window.aplicarFiltros = function() {
            const status = document.getElementById('filtroStatus').value;
            const prioridade = document.getElementById('filtroPrioridade').value;
            const vendedor = document.getElementById('filtroVendedor').value;
            const valorMin = parseFloat(document.getElementById('filtroValorMin').value) || 0;

            let orcamentosFiltrados = orcamentos;

            if (status) {
                orcamentosFiltrados = orcamentosFiltrados.filter(o =>
                    (o.statusAprovacao || 'PENDENTE') === status
                );
            }

            if (prioridade) {
                orcamentosFiltrados = orcamentosFiltrados.filter(o =>
                    calcularPrioridade(o) === prioridade
                );
            }

            if (vendedor) {
                orcamentosFiltrados = orcamentosFiltrados.filter(o =>
                    o.vendedorId === vendedor
                );
            }

            if (valorMin > 0) {
                orcamentosFiltrados = orcamentosFiltrados.filter(o =>
                    (o.valorTotal || 0) >= valorMin
                );
            }

            displayOrcamentos(orcamentosFiltrados);
        };

        // Abrir modal de aprovação
        window.openApprovalModal = function(orcamentoId) {
            currentOrcamento = orcamentos.find(o => o.id === orcamentoId);

            if (!currentOrcamento) {
                showNotification('Orçamento não encontrado', 'error');
                return;
            }

            // Preencher detalhes do orçamento
            const detailsDiv = document.getElementById('orcamentoDetails');
            detailsDiv.innerHTML = `
                <div class="approval-details">
                    <h4><i class="fas fa-file-alt"></i> Detalhes do Orçamento</h4>
                    <div class="approval-item">
                        <span><strong>Número:</strong></span>
                        <span>${currentOrcamento.numero}</span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Cliente:</strong></span>
                        <span>${currentOrcamento.clienteNome || 'N/A'}</span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Vendedor:</strong></span>
                        <span>${currentOrcamento.vendedorNome || 'N/A'}</span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Valor Total:</strong></span>
                        <span style="font-weight: 600;">R$ ${(currentOrcamento.valorTotal || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Margem Média:</strong></span>
                        <span style="font-weight: 600; color: ${(currentOrcamento.margemMedia || 0) >= 20 ? 'green' : 'red'};">
                            ${(currentOrcamento.margemMedia || 0).toFixed(1)}%
                        </span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Prioridade:</strong></span>
                        <span class="priority-badge priority-${calcularPrioridade(currentOrcamento).toLowerCase()}">
                            ${calcularPrioridade(currentOrcamento)}
                        </span>
                    </div>
                    <div class="approval-item">
                        <span><strong>Data Criação:</strong></span>
                        <span>${currentOrcamento.dataCriacao?.toDate?.()?.toLocaleDateString('pt-BR') || 'N/A'}</span>
                    </div>
                </div>
            `;

            document.getElementById('approvalModal').style.display = 'block';
        };

        // Fechar modal
        window.closeModal = function() {
            document.getElementById('approvalModal').style.display = 'none';
            document.getElementById('observacoesAprovacao').value = '';
            currentOrcamento = null;
        };

        // Aprovar orçamento
        window.aprovarOrcamento = async function() {
            if (!currentOrcamento) return;

            try {
                showLoading(true);

                const observacoes = document.getElementById('observacoesAprovacao').value;

                await updateDoc(doc(db, "orcamentos", currentOrcamento.id), {
                    statusAprovacao: 'APROVADO',
                    dataAprovacao: Timestamp.now(),
                    aprovadoPor: 'usuario_atual', // Implementar autenticação
                    observacoesAprovacao: observacoes,
                    nivelAprovacaoAtual: (currentOrcamento.nivelAprovacaoAtual || 0) + 1
                });

                showNotification('Orçamento aprovado com sucesso!', 'success');
                closeModal();
                await loadData();
                await updateStats();

            } catch (error) {
                console.error('Erro ao aprovar orçamento:', error);
                showNotification('Erro ao aprovar orçamento: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Rejeitar orçamento
        window.rejeitarOrcamento = async function() {
            if (!currentOrcamento) return;

            const observacoes = document.getElementById('observacoesAprovacao').value;

            if (!observacoes.trim()) {
                showNotification('Informe o motivo da rejeição', 'error');
                return;
            }

            try {
                showLoading(true);

                await updateDoc(doc(db, "orcamentos", currentOrcamento.id), {
                    statusAprovacao: 'REJEITADO',
                    dataRejeicao: Timestamp.now(),
                    rejeitadoPor: 'usuario_atual', // Implementar autenticação
                    motivoRejeicao: observacoes
                });

                showNotification('Orçamento rejeitado', 'info');
                closeModal();
                await loadData();
                await updateStats();

            } catch (error) {
                console.error('Erro ao rejeitar orçamento:', error);
                showNotification('Erro ao rejeitar orçamento: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Atualizar dados
        window.atualizarDados = async function() {
            await loadData();
            await updateStats();
            showNotification('Dados atualizados com sucesso!', 'success');
        };

        // Funções utilitárias
        function showLoading(show) {
            document.getElementById('loading').classList.toggle('active', show);
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('approvalModal');
            if (event.target === modal) {
                closeModal();
            }
        };
    </script>
</body>
</html>
