<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar CFOPs - Sistema ERP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-content {
            padding: 30px;
        }

        .search-bar {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 18px;
        }

        .filters {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 3px solid #2c3e50;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
        }

        .status-ativo {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-inativo {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 800px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: scale(1.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            z-index: 1001;
            display: none;
            opacity: 1;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.cfops-ativos {
            border-left-color: #27ae60;
        }

        .stat-card.cfops-entrada {
            border-left-color: #3498db;
        }

        .stat-card.cfops-saida {
            border-left-color: #f39c12;
        }

        .stat-card.total {
            border-left-color: #9b59b6;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
            font-size: 14px;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .main-content {
                padding: 20px;
            }

            .quick-actions {
                justify-content: center;
            }

            .table-container {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">HM-SYSTEMS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

  
    
        
            
                
                    
                        
                        
                        
                    
                    
                        
                        
                    
                    
                        
                        
                    
                
            
        

        
            <!-- Barra de Pesquisa -->
            <div class="search-bar">
                <input type="text" class="search-input" id="searchInput" placeholder="Pesquisar por código, descrição ou tipo...">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- Ações Rápidas -->
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="showAllCfops()">
                    <i class="fas fa-list"></i> Todos
                </button>
                <button class="btn btn-success" onclick="showActiveCfops()">
                    <i class="fas fa-check"></i> Ativos
                </button>
                <button class="btn btn-info" onclick="showEntradaCfops()">
                    <i class="fas fa-arrow-down"></i> Entrada
                </button>
                <button class="btn btn-warning" onclick="showSaidaCfops()">
                    <i class="fas fa-arrow-up"></i> Saída
                </button>
                <button class="btn btn-secondary" onclick="showInactiveCfops()">
                    <i class="fas fa-times"></i> Inativos
                </button>
            </div>

            <!-- Filtros -->
            <div class="filters">
                <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
                <div class="filter-row">
                    <div class="form-group">
                        <label>Tipo</label>
                        <select class="form-control" id="tipoFilter">
                            <option value="">Todos</option>
                            <option value="Entrada">Entrada</option>
                            <option value="Saída">Saída</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control" id="statusFilter">
                            <option value="">Todos</option>
                            <option value="Ativo">Ativo</option>
                            <option value="Inativo">Inativo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Aplicação</label>
                        <select class="form-control" id="aplicacaoFilter">
                            <option value="">Todas</option>
                            <option value="Estadual">Estadual</option>
                            <option value="Interestadual">Interestadual</option>
                            <option value="Internacional">Internacional</option>
                        </select>
                    </div>
                </div>
                <div style="display: flex; gap: 15px; justify-content: flex-end;">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-filter"></i> Aplicar Filtros
                    </button>
                    <button class="btn btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Limpar
                    </button>
                </div>
            </div>
    

  <div class="container">
    <div class="sap-title"><i class="fas fa-list-alt"></i> Gerenciar CFOPs</div>

    <!-- Formulário para Adicionar/Editar CFOP -->
    <div class="totvs-form">
      <h2>Adicionar Novo CFOP</h2>
      <form id="cfopForm" onsubmit="handleCFOP(event)">
        <div class="form-row">
          <div class="form-group">
            <label>ID do CFOP</label>
            <input type="text" id="cfopId" class="totvs-input" placeholder="Ex.: cfop_5102" required readonly>
          </div>
          <div class="form-group">
            <label>Código</label>
            <input type="text" id="cfopCodigo" class="totvs-input" placeholder="Ex.: 5102" required maxlength="4">
          </div>
          <div class="form-group">
            <label>Descrição</label>
            <input type="text" id="cfopDescricao" class="totvs-input" placeholder="Ex.: Venda de mercadoria dentro do estado" required>
          </div>
          <div class="form-group">
            <label>Tipo</label>
            <select id="cfopTipo" class="totvs-select" required>
              <option value="">Selecione...</option>
              <option value="Entrada">Entrada</option>
              <option value="Saída">Saída</option>
            </select>
          </div>
          <div class="form-group">
            <label>Ativo</label>
            <select id="cfopAtivo" class="totvs-select" required>
              <option value="true">Sim</option>
              <option value="false">Não</option>
            </select>
          </div>
        </div>
        <div class="form-actions">
          <button type="submit" class="btn-totvs-primary"><i class="fas fa-save"></i> Salvar</button>
          <button type="button" class="btn-totvs-secondary" onclick="resetForm()"><i class="fas fa-times"></i> Cancelar</button>
        </div>
      </form>
    </div>

    <!-- Tabela de CFOPs -->
    <table class="totvs-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Código</th>
          <th>Descrição</th>
          <th>Tipo</th>
          <th>Ativo</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="cfopTableBody"></tbody>
    </table>

    <div class="form-actions">
      <button class="btn-totvs-secondary" onclick="window.location.href='orcamentos.html'">
        <i class="fas fa-arrow-left"></i> Voltar
      </button>
      <button class="btn-totvs-danger" id="logoutButton" style="display: none;" onclick="logout()">
        <i class="fas fa-sign-out-alt"></i> Sair
      </button>
    </div>
  </div>

  <div class="sap-status">
    <div>Transação: ZCFOP - Gerenciar CFOPs</div>
    <div>Sistema: PRD | Cliente: 800</div>
  </div>

  
        
    

  
        
        
            
                0
                🟢 CFOPs Ativos
            
            
                0
                📥 Entrada
            
            
                0
                📤 Saída
            
            
                0
                📊 Total
            
        

        
            
                
                
                    
                    
                        
                            
                            
                            
                        
                        
                            
                            
                                
                                
                                
                            
                        
                        
                            
                            
                            
                        
                    
                    
                        
                            
                                
                                
                            
                            
                                
                                
                            
                        
                    
                
                
                    
                    
                        
                        
                    
                    
                        
                        
                    
                    
                        
                        
                    
                
            
        
    

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs,
      doc,
      updateDoc,
      deleteDoc,
      onSnapshot
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
    import * as Swal from 'https://cdn.jsdelivr.net/npm/sweetalert2@11/+esm';

    let cfops = [];
    let usuarioAtual = null;
    let editMode = false;
    let currentEditId = null;

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      document.getElementById('logoutButton').style.display = 'inline';

      await loadCFOPs();

      onSnapshot(collection(db, "cfops"), (snapshot) => {
        cfops = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
      });
    };

    async function loadCFOPs() {
      try {
        const cfopsSnap = await getDocs(collection(db, "cfops"));
        cfops = cfopsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        loadCFOPTable();
      } catch (error) {
        console.error("Erro ao carregar CFOPs:", error);
        showNotification("Erro ao carregar CFOPs", "error");
      }
    }

    function loadCFOPTable() {
      const tableBody = document.getElementById('cfopTableBody');
      tableBody.innerHTML = '';

      cfops.forEach(cfop => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${cfop.id}</td>
          <td>${cfop.codigo}</td>
          <td>${cfop.descricao}</td>
          <td>${cfop.tipo}</td>
          <td><span class="totvs-status ${cfop.ativo ? 'status-active' : 'status-inactive'}">${cfop.ativo ? 'Ativo' : 'Inativo'}</span></td>
          <td>
            <button class="btn-totvs" onclick="editCFOP('${cfop.id}')"><i class="fas fa-edit"></i> Editar</button>
            <button class="btn-totvs" onclick="deleteCFOP('${cfop.id}')"><i class="fas fa-trash"></i> Excluir</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    window.handleCFOP = async function(event) {
      event.preventDefault();

      if (!usuarioAtual) {
        showNotification('Por favor, faça login para gerenciar CFOPs.', 'error');
        window.location.href = 'login.html';
        return;
      }

      const cfopId = document.getElementById('cfopId').value;
      const codigo = document.getElementById('cfopCodigo').value;
      const descricao = document.getElementById('cfopDescricao').value;
      const tipo = document.getElementById('cfopTipo').value;
      const ativo = document.getElementById('cfopAtivo').value === 'true';

      if (!codigo || !descricao || !tipo) {
        showNotification('Por favor, preencha todos os campos obrigatórios.', 'error');
        return;
      }

      if (!/^\d{4}$/.test(codigo)) {
        showNotification('O código do CFOP deve ter exatamente 4 dígitos.', 'error');
        return;
      }

      const cfopData = {
        id: cfopId,
        codigo: codigo,
        descricao: descricao,
        tipo: tipo,
        ativo: ativo
      };

      try {
        if (editMode) {
          // Atualizar CFOP existente
          await updateDoc(doc(db, "cfops", currentEditId), cfopData);
          showNotification(`CFOP ${codigo} atualizado com sucesso!`, 'success');
          editMode = false;
          currentEditId = null;
        } else {
          // Adicionar novo CFOP
          const docRef = await addDoc(collection(db, "cfops"), cfopData);
          await updateDoc(doc(db, "cfops", docRef.id), { id: `cfop_${codigo}` });
          showNotification(`CFOP ${codigo} criado com sucesso!`, 'success');
        }
        resetForm();
      } catch (error) {
        console.error("Erro ao salvar CFOP:", error);
        showNotification(`Erro ao salvar CFOP: ${error.message}`, 'error');
      }
    };

    window.editCFOP = function(cfopId) {
      const cfop = cfops.find(c => c.id === cfopId);
      if (!cfop) return;

      editMode = true;
      currentEditId = cfopId;

      document.getElementById('cfopId').value = cfop.id;
      document.getElementById('cfopCodigo').value = cfop.codigo;
      document.getElementById('cfopDescricao').value = cfop.descricao;
      document.getElementById('cfopTipo').value = cfop.tipo;
      document.getElementById('cfopAtivo').value = cfop.ativo.toString();

      document.querySelector('.totvs-form h2').textContent = `Editar CFOP ${cfop.codigo}`;
      document.getElementById('cfopId').readOnly = true;
      document.getElementById('cfopCodigo').readOnly = true; // Evitar alterar o código
    };

    window.deleteCFOP = async function(cfopId) {
      if (!usuarioAtual) {
        showNotification('Por favor, faça login para excluir CFOPs.', 'error');
        window.location.href = 'login.html';
        return;
      }

      if (!confirm('Tem certeza que deseja excluir este CFOP?')) return;

      try {
        await deleteDoc(doc(db, "cfops", cfopId));
        showNotification('CFOP excluído com sucesso!', 'success');
      } catch (error) {
        console.error("Erro ao excluir CFOP:", error);
        showNotification('Erro ao excluir CFOP.', 'error');
      }
    };

    window.resetForm = function() {
      document.getElementById('cfopForm').reset();
      document.getElementById('cfopId').value = '';
      document.getElementById('cfopId').readOnly = false;
      document.getElementById('cfopCodigo').readOnly = false;
      document.querySelector('.totvs-form h2').textContent = 'Adicionar Novo CFOP';
      editMode = false;
      currentEditId = null;

      // Gerar ID automático baseado no código ao digitar
      document.getElementById('cfopCodigo').addEventListener('input', function() {
        const codigo = this.value;
        if (codigo) {
          document.getElementById('cfopId').value = `cfop_${codigo}`;
        } else {
          document.getElementById('cfopId').value = '';
        }
      });
    };

    window.logout = function() {
      localStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    };

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.className = `notification notification-${type}`;
      document.querySelector('.container').prepend(notification);
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Inicializar o evento de geração de ID
    document.getElementById('cfopCodigo').addEventListener('input', function() {
      const codigo = this.value;
      if (codigo) {
        document.getElementById('cfopId').value = `cfop_${codigo}`;
      } else {
        document.getElementById('cfopId').value = '';
      }
    });
  </script>
</body>
</html>