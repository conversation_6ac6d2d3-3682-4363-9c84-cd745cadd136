import { db } from './firebase-config.js';
import { 
    collection, 
    doc, 
    setDoc,
    addDoc,
    getDoc,
    getDocs,
    Timestamp,
    writeBatch,
    runTransaction
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Verifica se o banco de dados está vazio
async function isDatabaseEmpty() {
    try {
        // Verifica se existe pelo menos um documento em alguma coleção principal
        const collections = ['usuarios', 'empresa', 'parametros', 'grupos', 'armazens'];
        for (const collectionName of collections) {
            const snapshot = await getDocs(collection(db, collectionName));
            if (!snapshot.empty) {
                return false;
            }
        }
        return true;
    } catch (error) {
        console.error("Erro ao verificar banco de dados:", error);
        throw new Error("Não foi possível verificar o estado do banco de dados");
    }
}

// Função para inicializar o banco de dados
export async function initializeDatabase() {
    try {
        // Verifica se o banco já está inicializado
        const isInitialized = await getDoc(doc(db, "empresa", "config"));
        if (isInitialized.exists()) {
            throw new Error("Banco de dados já inicializado");
        }

        // Usar batch para garantir consistência
        const batch = writeBatch(db);

        // 1. Criar usuário administrador
        const adminUser = {
            nome: "admin",
            senha: "admin123",
            nivel: 9, // Nível de superusuário
            ativo: true,
            dataCadastro: Timestamp.now(),
            primeiroAcesso: true, // Flag para indicar primeiro acesso
            ultimoAcesso: null,
            email: "<EMAIL>",
            departamento: "Administração",
            nomeCompleto: "Administrador do Sistema"
        };
        batch.set(doc(db, "usuarios", "admin"), adminUser);

        // 2. Criar configuração da empresa
        const empresaConfig = {
            nomeFantasia: "Nova Empresa",
            razaoSocial: "",
            cnpj: "",
            endereco: "",
            telefone: "",
            email: "",
            tamanhoLogo: 150,
            dataCadastro: Timestamp.now(),
            primeiroAcesso: true, // Flag para indicar primeiro acesso
            logoUrl: "",
            centerImageUrl: "",
            versaoSistema: "1.0.0",
            cep: "",
            cidade: "",
            estado: "",
            pais: "Brasil",
            site: "",
            observacoes: ""
        };
        batch.set(doc(db, "empresa", "config"), empresaConfig);

        // 3. Criar parâmetros do sistema
        const parametrosSistema = {
            controleQualidade: true,
            homologacaoFornecedor: true,
            inspecaoRecebimento: true,
            armazemQualidade: true,
            importacaoRapidaAtiva: false,
            dataAtualizacao: Timestamp.now(),
            primeiroAcesso: true, // Flag para indicar primeiro acesso
            diasValidadeHomologacao: 365,
            diasValidadeInspecao: 30,
            estoqueMinimo: 0,
            estoqueMaximo: 999999,
            alertaEstoqueBaixo: true,
            diasAntecedenciaAlerta: 30,
            diasAntecedenciaVencimento: 60,
            permitirEstoqueNegativo: false,
            bloquearVendaSemEstoque: true,
            exigirInspecaoRecebimento: true,
            exigirHomologacaoFornecedor: true
        };
        batch.set(doc(db, "parametros", "sistema"), parametrosSistema);

        // 4. Criar contadores
        const contadores = {
            ordens: { valor: 0, prefixo: "OP" },
            produtos: { valor: 0, prefixo: "P" },
            estruturas: { valor: 0, prefixo: "E" },
            solicitacoes: { valor: 0, prefixo: "SC" },
            cotacoes: { valor: 0, prefixo: "C" },
            pedidos: { valor: 0, prefixo: "PV" },
            faturas: { valor: 0, prefixo: "F" }
        };
        for (const [tipo, valor] of Object.entries(contadores)) {
            batch.set(doc(db, "contadores", tipo), valor);
        }

        // 5. Criar grupos básicos
        const gruposBasicos = [
            { codigo: "MP", descricao: "Matéria Prima", tipo: "MATERIA_PRIMA" },
            { codigo: "EM", descricao: "Embalagem", tipo: "EMBALAGEM" },
            { codigo: "AC", descricao: "Acessório", tipo: "ACESSORIO" },
            { codigo: "PF", descricao: "Produto Final", tipo: "PRODUTO_FINAL" },
            { codigo: "SM", descricao: "Semi-acabado", tipo: "SEMI_ACABADO" }
        ];
        for (const grupo of gruposBasicos) {
            const grupoRef = doc(collection(db, "grupos"));
            batch.set(grupoRef, {
                ...grupo,
                dataCadastro: Timestamp.now(),
                ativo: true,
                observacoes: "Grupo criado automaticamente na inicialização do sistema"
            });
        }

        // 6. Criar armazém padrão
        const armazemPadrao = {
            codigo: "01",
            descricao: "Armazém Principal",
            tipo: "MATERIA_PRIMA",
            ativo: true,
            dataCadastro: Timestamp.now(),
            endereco: "",
            responsavel: "",
            telefone: "",
            observacoes: "Armazém padrão do sistema",
            capacidade: 0,
            unidadeMedida: "M3",
            permiteEstoqueNegativo: false,
            permiteMovimentacao: true
        };
        batch.set(doc(db, "armazens", "01"), armazemPadrao);

        // 7. Criar permissões padrão
        const permissoesPadrao = {
            permissoes: [
                "cadastro_produtos",
                "cadastro_grupo",
                "cadastro_familia",
                "cadastro_recursos",
                "cadastro_armazem",
                "estrutura_produtos",
                "estrutura_nova",
                "centralDocumentos",
                "cadastro_clientes",
                "cadastro_operacoes",
                "cadastro_fornecedores",
                "cadastro_centro_custo",
                "cadastro_setores",
                "cadastro_usuarios",
                "produtos_fornecedores",
                "ordens_producao",
                "apontamentos",
                "estoques",
                "movimentacaoArmazem",
                "especificacoes_produtos",
                "homologacao_fornecedores",
                "recebimento_materiais",
                "inspecao_qualidade",
                "contas_pagar",
                "contas_receber",
                "fluxo_caixa",
                "orcamentos",
                "pedidos_venda",
                "faturamento",
                "relatorio_financeiro",
                "relatorio_inventario",
                "relatorio_mrp_compras"
            ]
        };
        batch.set(doc(db, "permissoes", "admin"), permissoesPadrao);

        // Executar todas as operações em lote
        await batch.commit();

        return "Inicialização do banco de dados concluída com sucesso!";
    } catch (error) {
        console.error("Erro ao inicializar banco de dados:", error);
        if (error.message === "Banco de dados já inicializado") {
            throw error;
        }
        throw new Error("Erro ao inicializar o banco de dados. Por favor, tente novamente.");
    }
}

// Função para verificar e inicializar o banco se necessário
export async function checkAndInitializeDatabase() {
    try {
        const isEmpty = await isDatabaseEmpty();
        if (isEmpty) {
            console.log("Banco de dados vazio detectado. Inicializando...");
            await initializeDatabase();
            return true; // Indica que foi inicializado
        }
        return false; // Indica que não foi necessário inicializar
    } catch (error) {
        console.error("Erro ao verificar/inicializar banco de dados:", error);
        throw error;
    }
}

// Função para verificar se é o primeiro acesso do usuário
export async function checkFirstAccess(userId) {
    try {
        const userDoc = await getDoc(doc(db, "usuarios", userId));
        if (userDoc.exists() && userDoc.data().primeiroAcesso) {
            // Verifica se a empresa também está no primeiro acesso
            const empresaDoc = await getDoc(doc(db, "empresa", "config"));
            return empresaDoc.exists() && empresaDoc.data().primeiroAcesso;
        }
        return false;
    } catch (error) {
        console.error("Erro ao verificar primeiro acesso:", error);
        return false;
    }
}

// Função para marcar primeiro acesso como concluído
export async function completeFirstAccess(userId) {
    try {
        const batch = writeBatch(db);

        // Atualiza usuário
        batch.update(doc(db, "usuarios", userId), {
            primeiroAcesso: false,
            ultimoAcesso: Timestamp.now()
        });

        // Atualiza empresa
        batch.update(doc(db, "empresa", "config"), {
            primeiroAcesso: false,
            dataAtualizacao: Timestamp.now()
        });

        // Atualiza parâmetros
        batch.update(doc(db, "parametros", "sistema"), {
            primeiroAcesso: false,
            dataAtualizacao: Timestamp.now()
        });

        await batch.commit();
        return true;
    } catch (error) {
        console.error("Erro ao concluir primeiro acesso:", error);
        throw error;
    }
}

// Executar a inicialização
initializeDatabase(); 