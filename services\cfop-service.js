
/**
 * SERVIÇO DE CFOP - CÓDIGO FISCAL DE OPERAÇÕES E PRESTAÇÕES
 * Gerencia e valida CFOPs no sistema
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    getDocs, 
    doc, 
    getDoc,
    query,
    where,
    orderBy 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class CFOPService {
    static cfopCache = null;
    static lastUpdate = null;
    static CACHE_DURATION = 10 * 60 * 1000; // 10 minutos

    /**
     * Carregar todos os CFOPs ativos
     */
    static async loadCFOPs(forceReload = false) {
        try {
            // Verificar cache
            if (!forceReload && this.cfopCache && this.lastUpdate && 
                (Date.now() - this.lastUpdate) < this.CACHE_DURATION) {
                return this.cfopCache;
            }

            console.log('🔄 Carregando CFOPs do Firebase...');
            const cfopSnapshot = await getDocs(
                query(
                    collection(db, "cfops"), 
                    where("ativo", "==", true),
                    orderBy("codigo")
                )
            );

            this.cfopCache = cfopSnapshot.docs.map(doc => ({ 
                id: doc.id, 
                ...doc.data() 
            }));
            this.lastUpdate = Date.now();

            console.log(`✅ ${this.cfopCache.length} CFOPs carregados`);
            return this.cfopCache;

        } catch (error) {
            console.error('❌ Erro ao carregar CFOPs:', error);
            throw error;
        }
    }

    /**
     * Validar CFOP para operação
     */
    static async validateCFOPForOperation(cfopCode, operationType, clienteUF, empresaUF = 'SP') {
        try {
            const cfops = await this.loadCFOPs();
            const cfop = cfops.find(c => c.codigo === cfopCode);

            if (!cfop) {
                return { 
                    valid: false, 
                    message: `CFOP ${cfopCode} não encontrado ou inativo` 
                };
            }

            // Verificar se é operação estadual ou interestadual
            const isEstadual = clienteUF === empresaUF;

            // Validações baseadas no tipo de operação
            const cfopNum = parseInt(cfopCode);
            
            if (operationType === 'ENTRADA') {
                if (isEstadual && (cfopNum < 1000 || cfopNum >= 2000)) {
                    return { 
                        valid: false, 
                        message: 'CFOP inválido para entrada estadual (deve estar entre 1000-1999)' 
                    };
                }
                if (!isEstadual && (cfopNum < 2000 || cfopNum >= 3000)) {
                    return { 
                        valid: false, 
                        message: 'CFOP inválido para entrada interestadual (deve estar entre 2000-2999)' 
                    };
                }
            } else if (operationType === 'SAIDA') {
                if (isEstadual && (cfopNum < 5000 || cfopNum >= 6000)) {
                    return { 
                        valid: false, 
                        message: 'CFOP inválido para saída estadual (deve estar entre 5000-5999)' 
                    };
                }
                if (!isEstadual && (cfopNum < 6000 || cfopNum >= 7000)) {
                    return { 
                        valid: false, 
                        message: 'CFOP inválido para saída interestadual (deve estar entre 6000-6999)' 
                    };
                }
            }

            return { 
                valid: true, 
                cfop: cfop,
                isEstadual: isEstadual
            };

        } catch (error) {
            console.error('❌ Erro ao validar CFOP:', error);
            return { valid: false, message: 'Erro na validação do CFOP' };
        }
    }

    /**
     * Obter CFOPs por tipo de operação
     */
    static async getCFOPsByType(operationType) {
        const cfops = await this.loadCFOPs();
        return cfops.filter(cfop => cfop.tipo === operationType);
    }

    /**
     * Sugerir CFOP baseado na operação
     */
    static async suggestCFOP(operationType, isEstadual, categoria = null) {
        try {
            const cfops = await this.loadCFOPs();
            
            let filteredCFOPs = cfops.filter(cfop => {
                const cfopNum = parseInt(cfop.codigo);
                
                if (operationType === 'ENTRADA') {
                    return isEstadual ? 
                        (cfopNum >= 1000 && cfopNum < 2000) : 
                        (cfopNum >= 2000 && cfopNum < 3000);
                } else if (operationType === 'SAIDA') {
                    return isEstadual ? 
                        (cfopNum >= 5000 && cfopNum < 6000) : 
                        (cfopNum >= 6000 && cfopNum < 7000);
                }
                
                return false;
            });

            // Aplicar filtros por categoria se especificado
            if (categoria) {
                const categoriaMap = {
                    'COMPRA': ['1102', '2102'], // Compra para comercialização
                    'VENDA': ['5102', '6102'], // Venda de mercadoria
                    'PRODUCAO': ['5101', '6101'], // Venda de produção
                    'TRANSFERENCIA': ['5152', '6152'] // Transferência
                };

                if (categoriaMap[categoria]) {
                    const suggestedCodes = categoriaMap[categoria];
                    const suggested = filteredCFOPs.filter(cfop => 
                        suggestedCodes.includes(cfop.codigo)
                    );
                    
                    if (suggested.length > 0) {
                        return suggested;
                    }
                }
            }

            return filteredCFOPs.slice(0, 10); // Retornar até 10 sugestões
        } catch (error) {
            console.error('❌ Erro ao sugerir CFOPs:', error);
            return [];
        }
    }

    /**
     * Obter opções de CFOP para select
     */
    static async getCFOPOptions(operationType = null, isEstadual = null) {
        try {
            let cfops = await this.loadCFOPs();

            if (operationType && isEstadual !== null) {
                cfops = cfops.filter(cfop => {
                    const cfopNum = parseInt(cfop.codigo);
                    
                    if (operationType === 'ENTRADA') {
                        return isEstadual ? 
                            (cfopNum >= 1000 && cfopNum < 2000) : 
                            (cfopNum >= 2000 && cfopNum < 3000);
                    } else if (operationType === 'SAIDA') {
                        return isEstadual ? 
                            (cfopNum >= 5000 && cfopNum < 6000) : 
                            (cfopNum >= 6000 && cfopNum < 7000);
                    }
                    
                    return true;
                });
            }

            return cfops.map(cfop => ({
                value: cfop.codigo,
                text: `${cfop.codigo} - ${cfop.descricao}`,
                tipo: cfop.tipo
            }));

        } catch (error) {
            console.error('❌ Erro ao obter opções de CFOP:', error);
            return [];
        }
    }

    /**
     * Limpar cache
     */
    static clearCache() {
        this.cfopCache = null;
        this.lastUpdate = null;
        console.log('🗑️ Cache de CFOPs limpo');
    }

    /**
     * Verificar se CFOP existe
     */
    static async cfopExists(cfopCode) {
        const cfops = await this.loadCFOPs();
        return cfops.some(cfop => cfop.codigo === cfopCode);
    }

    /**
     * Obter estatísticas dos CFOPs
     */
    static async getStatistics() {
        const cfops = await this.loadCFOPs();
        
        return {
            total: cfops.length,
            entrada: cfops.filter(c => c.tipo === 'Entrada').length,
            saida: cfops.filter(c => c.tipo === 'Saída').length,
            estaduais: cfops.filter(c => {
                const num = parseInt(c.codigo);
                return (num >= 1000 && num < 2000) || (num >= 5000 && num < 6000);
            }).length,
            interestaduais: cfops.filter(c => {
                const num = parseInt(c.codigo);
                return (num >= 2000 && num < 3000) || (num >= 6000 && num < 7000);
            }).length,
            importacao: cfops.filter(c => {
                const num = parseInt(c.codigo);
                return (num >= 3000 && num < 4000) || (num >= 7000 && num < 8000);
            }).length
        };
    }
}

// Exportar para uso global
if (typeof window !== 'undefined') {
    window.CFOPService = CFOPService;
}
