<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FYRON ERP - Cadastro de Clientes e Fornecedores</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f8f9fa;
      --border-color: #dee2e6;
      --text-color: #212529;
      --text-muted: #6c757d;
      --success-color: #28a745;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #17a2b8;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
      --totvs-blue: #0854a0;
      --totvs-gray: #f5f5f5;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-bottom: 25px;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 25px;
      border-bottom: 1px solid #dee2e6;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-body {
      padding: 25px;
    }

    /* Sistema de Abas */
    .tabs {
      display: flex;
      border-bottom: 1px solid #dee2e6;
      background-color: #f8f9fa;
      border-radius: 0;
      margin: 0;
    }

    .tab {
      padding: 15px 20px;
      cursor: pointer;
      border: none;
      background: none;
      color: #6c757d;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .tab.active {
      background-color: white;
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      font-weight: 600;
    }

    .tab:hover:not(.active) {
      color: #3498db;
      background-color: rgba(52, 152, 219, 0.05);
    }

    .tab-content {
      display: none;
      padding: 0;
    }

    .tab-content.active {
      display: block;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-text {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
      font-style: italic;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-col {
      display: flex;
      flex-direction: column;
    }

    label {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .required::after {
      content: " *";
      color: #e74c3c;
      font-weight: bold;
    }

    input, select, textarea {
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: white;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    .table-container {
      background: white;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .table-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 25px;
      border-bottom: 1px solid #dee2e6;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 15px 20px;
      text-align: left;
      border-bottom: 1px solid #f1f3f4;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    tr:hover {
      background: rgba(52, 152, 219, 0.05);
    }

    .section {
      background: white;
      border-radius: 12px;
      margin-bottom: 25px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .section-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 15px 20px;
      border-bottom: 1px solid #dee2e6;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .section-body {
      padding: 20px;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-ativo {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
    }

    .status-inativo {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
    }

    .status-pendente {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      color: #856404;
    }

    .actions {
      display: flex;
      gap: 15px;
      justify-content: flex-end;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;
    }

    .search-container {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      align-items: center;
    }

    .search-container input,
    .search-container select {
      min-width: 200px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .btn-sm {
      padding: 8px 12px;
      font-size: 12px;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 40px;
      color: #6c757d;
    }

    .loading.show {
      display: block;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
    }

    .empty-state i {
      font-size: 48px;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .edit-btn, .delete-btn {
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      border: none;
      transition: all 0.3s ease;
      font-weight: 600;
    }

    .edit-btn {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .edit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
    }

    .delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    }

    #transportSection {
      border: 2px solid #27ae60;
      background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
      animation: fadeIn 0.3s ease-in-out;
    }

    #transportSection .section-header {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: white;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        border-radius: 10px;
      }

      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 20px;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .main-content {
        padding: 20px;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .search-container {
        flex-direction: column;
        align-items: stretch;
      }

      .search-container input,
      .search-container select {
        min-width: auto;
        width: 100%;
      }

      .actions {
        flex-direction: column;
      }

      .action-buttons {
        justify-content: center;
      }

      th, td {
        padding: 10px;
        font-size: 12px;
      }

      .table-container {
        overflow-x: auto;
      }
    }



    /* Estilos para campos de transportadora */
    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-top: 5px;
    }

    .checkbox-group label {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
      cursor: pointer;
      padding: 5px 10px;
      border-radius: 5px;
      transition: background-color 0.2s;
      border: 1px solid #e9ecef;
      background: #f8f9fa;
    }

    .checkbox-group label:hover {
      background-color: #e9ecef;
      border-color: #28a745;
    }

    .checkbox-group input[type="checkbox"] {
      margin: 0;
      accent-color: #28a745;
    }





    /* Responsividade */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 10px;
      }

      .toolbar-content {
        flex-direction: column;
        align-items: stretch;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .search-container {
        flex-direction: column;
        gap: 10px;
      }

      .search-container input,
      .search-container select {
        width: 100%;
      }

      .suppliers-table {
        font-size: 12px;
      }

      .suppliers-table th,
      .suppliers-table td {
        padding: 8px 10px;
      }

      .action-buttons {
        flex-direction: column;
        gap: 3px;
      }

      .user-section {
        flex-direction: column;
        gap: 10px;
      }

      .main-container {
        padding: 0 10px;
      }

      .container {
        width: 95%;
        margin: 20px auto;
        padding: 15px;
      }

      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header > div {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
      }

      .tabs-header {
        flex-wrap: wrap;
      }

      .tab-button {
        min-width: 120px;
        padding: 12px 15px;
      }

      .shortcuts-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }

      .summary-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .form-row {
        grid-template-columns: 1fr;
      }

      .btn-totvs {
        width: 100%;
        justify-content: center;
      }

      .toolbar-content > div:last-child {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .container {
        width: 98%;
        margin: 10px auto;
        padding: 10px;
      }

      .suppliers-table th,
      .suppliers-table td {
        padding: 6px 8px;
        font-size: 11px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>
        <i class="fas fa-address-book"></i>
        Cadastro de Clientes e Fornecedores
      </h1>
      <div class="header-actions">
        <button class="btn btn-success" onclick="clearForm()">
          <i class="fas fa-plus"></i>
          Novo Cadastro
        </button>
        <button class="btn btn-primary" onclick="forceRefresh()" title="Atualizar lista manualmente">
          <i class="fas fa-sync-alt"></i>
          Atualizar
        </button>
        <button class="btn btn-info" onclick="exportSuppliers()">
          <i class="fas fa-download"></i>
          Exportar Excel
        </button>
        <button class="btn btn-primary" onclick="navigateBack()">
          <i class="fas fa-arrow-left"></i>
          Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
    <!-- Toolbar -->
    <div class="totvs-toolbar">
      <div class="toolbar-content">
        <div>
          

        </div>

      </div>
    </div>

    <!-- Atalhos de Homologação -->
    <div class="card">
      <div class="card-header">
        <div class="card-title">
          <i class="fas fa-rocket"></i>
          Atalhos para Homologação
        </div>
      </div>
      <div class="card-body">
        <div class="form-row">
          <div class="form-col">
            <button class="btn btn-primary" onclick="goToHomologacao()">
              <i class="fas fa-industry"></i>
              Processo de Homologação
            </button>
          </div>
          <div class="form-col">
            <button class="btn btn-info" onclick="goToDocumentos()">
              <i class="fas fa-file-alt"></i>
              Gerenciar Documentos
            </button>
          </div>
          <div class="form-col">
            <button class="btn btn-warning" onclick="goToQualidade()">
              <i class="fas fa-search"></i>
              Avaliação Qualidade
            </button>
          </div>
          <div class="form-col">
            <button class="btn btn-success" onclick="goToFinanceiro()">
              <i class="fas fa-dollar-sign"></i>
              Análise Financeira
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros de Pesquisa -->
    <div class="card">
      <div class="card-header">
        <div class="card-title">
          <i class="fas fa-filter"></i>
          Filtros de Pesquisa
        </div>
      </div>
      <div class="card-body">
        <div class="form-row">
          <div class="form-col">
            <label>Buscar</label>
            <input type="text" id="searchInput" placeholder="Código, razão social, CNPJ/CPF, cidade...">
          </div>
          <div class="form-col">
            <label>Tipo</label>
            <select id="tipoFilter">
              <option value="">Todos os tipos</option>
              <option value="Fornecedor">Fornecedor</option>
              <option value="Cliente">Cliente</option>
              <option value="Ambos">Ambos</option>
            </select>
          </div>
          <div class="form-col">
            <label>Status Homologação</label>
            <select id="statusFilter">
              <option value="">Todos os status</option>
              <option value="Homologado">Homologado</option>
              <option value="Pendente">Pendente</option>
            </select>
          </div>
          <div class="form-col">
            <label>Situação</label>
            <select id="ativoFilter">
              <option value="">Todos</option>
              <option value="true">Ativos</option>
              <option value="false">Inativos</option>
            </select>
          </div>
        </div>
        <div class="actions">
          <button class="btn btn-primary" onclick="filterSuppliers()">
            <i class="fas fa-search"></i>
            Pesquisar
          </button>
        </div>
      </div>
    </div>

    <!-- Resumo do Fornecedor (aparece durante edição) -->
    <div class="card" id="supplierSummary" style="display: none;">
      <div class="card-header">
        <div class="card-title">
          <i class="fas fa-info-circle"></i>
          Resumo do Fornecedor
        </div>
        <button class="btn btn-danger btn-sm" onclick="toggleSummary()">
          <i class="fas fa-eye-slash"></i>
          Ocultar
        </button>
      </div>
      <div class="card-body">
        <div class="form-row">
          <div class="form-col">
            <label>Código</label>
            <div id="summaryCode" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
          <div class="form-col">
            <label>Razão Social</label>
            <div id="summaryName" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
          <div class="form-col">
            <label>CNPJ/CPF</label>
            <div id="summaryCnpj" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
          <div class="form-col">
            <label>Status</label>
            <div id="summaryStatus" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
          <div class="form-col">
            <label>Categoria</label>
            <div id="summaryCategory" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
          <div class="form-col">
            <label>Cidade</label>
            <div id="summaryCity" style="font-weight: 600; color: #2c3e50;">-</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formulário Principal -->
    <div class="card">
      <div class="card-header">
        <div class="card-title">
          <i class="fas fa-user-plus"></i>
          Cadastrar Novo Parceiro
        </div>
      </div>

      <!-- Sistema de Abas -->
      <div class="tabs">
        <button class="tab active" onclick="switchTab('basicos')">
          <i class="fas fa-info-circle"></i>
          Dados Básicos
        </button>
        <button class="tab" onclick="switchTab('endereco')">
          <i class="fas fa-map-marker-alt"></i>
          Endereço
        </button>
        <button class="tab" onclick="switchTab('contatos')">
          <i class="fas fa-phone"></i>
          Contatos
        </button>
        <button class="tab" onclick="switchTab('comercial')">
          <i class="fas fa-handshake"></i>
          Comercial
        </button>
        <button class="tab" onclick="switchTab('documentos')">
          <i class="fas fa-file-alt"></i>
          Documentos
        </button>
      </div>

      <div class="card-body">
        <form id="supplierForm">
          <input type="hidden" id="editingId">

          <!-- Aba: Dados Básicos -->
          <div id="basicosTab" class="tab-content active">
            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-info-circle"></i> Informações Básicas
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="tipo" class="required">Tipo</label>
                    <select id="tipo" name="tipo" required>
                      <option value="Fornecedor">Fornecedor</option>
                      <option value="Cliente">Cliente</option>
                      <option value="Ambos">Ambos</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="codigo" class="required">Código</label>
                    <input type="text" id="codigo" name="codigo" required placeholder="Ex: F001">
                  </div>
                  <div class="form-col">
                    <label for="categoriaPrincipal" class="required">Categoria Principal</label>
                    <select id="categoriaPrincipal" name="categoriaPrincipal" required onchange="toggleTransportFields()">
                      <option value="">Selecione...</option>
                      <option value="FORNECEDOR_MATERIAL">🏭 Fornecedor de Material</option>
                      <option value="FORNECEDOR_SERVICO">🔧 Fornecedor de Serviço</option>
                      <option value="TRANSPORTADORA">🚛 Transportadora</option>
                      <option value="PRESTADOR_SERVICO">👷 Prestador de Serviço</option>
                    </select>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="tipoPessoa" class="required">Tipo Pessoa</label>
                    <select id="tipoPessoa" name="tipoPessoa" required onchange="updateCpfCnpjMask()">
                      <option value="Juridica">Jurídica</option>
                      <option value="Fisica">Física</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="cnpjCpf" class="required">CNPJ/CPF</label>
                    <input type="text" id="cnpjCpf" name="cnpjCpf" required placeholder="00.000.000/0000-00">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="razaoSocial" class="required">Razão Social</label>
                    <input type="text" id="razaoSocial" name="razaoSocial" required placeholder="Nome completo da empresa">
                  </div>
                  <div class="form-col">
                    <label for="nomeFantasia" class="required">Nome Fantasia</label>
                    <input type="text" id="nomeFantasia" name="nomeFantasia" required placeholder="Nome comercial">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="inscricaoEstadual">Inscrição Estadual</label>
                    <input type="text" id="inscricaoEstadual" name="inscricaoEstadual" placeholder="***************">
                  </div>
                  <div class="form-col">
                    <label for="inscricaoMunicipal">Inscrição Municipal</label>
                    <input type="text" id="inscricaoMunicipal" name="inscricaoMunicipal">
                  </div>
                  <div class="form-col">
                    <label for="nascimento">Data de Nascimento/Fundação</label>
                    <input type="date" id="nascimento" name="nascimento">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="statusHomologacao" class="required">Status de Homologação</label>
                    <select id="statusHomologacao" name="statusHomologacao" required>
                      <option value="Pendente">Pendente</option>
                      <option value="Em Análise">Em Análise</option>
                      <option value="Homologado">Homologado</option>
                      <option value="Rejeitado">Rejeitado</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="ativo">Situação</label>
                    <select id="ativo" name="ativo">
                      <option value="true">Ativo</option>
                      <option value="false">Inativo</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <!-- Seção: Dados de Transportadora (condicional) -->
            <div class="section" id="transportSection" style="display: none;">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-truck"></i> Dados de Transportadora
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="antt">ANTT (Registro ANTT)</label>
                    <input type="text" id="antt" name="antt" placeholder="Ex: 123456789">
                  </div>
                  <div class="form-col">
                    <label for="seguroTransporte">Seguro de Transporte</label>
                    <select id="seguroTransporte" name="seguroTransporte">
                      <option value="false">Não</option>
                      <option value="true">Sim</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="avaliacaoEntrega">Avaliação de Entrega (1-5)</label>
                    <input type="number" id="avaliacaoEntrega" name="avaliacaoEntrega" min="1" max="5" step="0.1" placeholder="4.5">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="tiposVeiculo">Tipos de Veículo</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="tiposVeiculo" value="CARRETA"> 🚛 Carreta</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="tiposVeiculo" value="TRUCK"> 🚚 Truck</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="tiposVeiculo" value="VUC"> 🚐 VUC</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="tiposVeiculo" value="MOTO"> 🏍️ Moto</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="tiposVeiculo" value="AEREO"> ✈️ Aéreo</label>
                    </div>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="rotasAtendidas">Rotas Atendidas</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="rotasAtendidas" value="NORTE"> 🌎 Norte</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="rotasAtendidas" value="NORDESTE"> 🌎 Nordeste</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="rotasAtendidas" value="CENTRO_OESTE"> 🌎 Centro-Oeste</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="rotasAtendidas" value="SUDESTE"> 🌎 Sudeste</label>
                      <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" name="rotasAtendidas" value="SUL"> 🌎 Sul</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Aba: Endereço -->
          <div id="enderecoTab" class="tab-content">
            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-map-marker-alt"></i> Endereço Principal
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="cep" class="required">CEP</label>
                    <input type="text" id="cep" name="cep" required placeholder="00000-000">
                    <div class="info-text">Digite o CEP para preenchimento automático</div>
                  </div>
                  <div class="form-col">
                    <label for="endereco" class="required">Logradouro</label>
                    <input type="text" id="endereco" name="endereco" required placeholder="Rua, Avenida, etc.">
                  </div>
                  <div class="form-col">
                    <label for="numero">Número</label>
                    <input type="text" id="numero" name="numero" placeholder="123">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="complemento">Complemento</label>
                    <input type="text" id="complemento" name="complemento" placeholder="Sala, Andar, etc.">
                  </div>
                  <div class="form-col">
                    <label for="bairro" class="required">Bairro</label>
                    <input type="text" id="bairro" name="bairro" required placeholder="Nome do bairro">
                  </div>
                  <div class="form-col">
                    <label for="cidade" class="required">Cidade</label>
                    <input type="text" id="cidade" name="cidade" required placeholder="Nome da cidade">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="estado" class="required">Estado</label>
                    <select id="estado" name="estado" required>
                      <option value="">Selecione...</option>
                      <option value="AC">Acre</option>
                      <option value="AL">Alagoas</option>
                      <option value="AP">Amapá</option>
                      <option value="AM">Amazonas</option>
                      <option value="BA">Bahia</option>
                      <option value="CE">Ceará</option>
                      <option value="DF">Distrito Federal</option>
                      <option value="ES">Espírito Santo</option>
                      <option value="GO">Goiás</option>
                      <option value="MA">Maranhão</option>
                      <option value="MT">Mato Grosso</option>
                      <option value="MS">Mato Grosso do Sul</option>
                      <option value="MG">Minas Gerais</option>
                      <option value="PA">Pará</option>
                      <option value="PB">Paraíba</option>
                      <option value="PR">Paraná</option>
                      <option value="PE">Pernambuco</option>
                      <option value="PI">Piauí</option>
                      <option value="RJ">Rio de Janeiro</option>
                      <option value="RN">Rio Grande do Norte</option>
                      <option value="RS">Rio Grande do Sul</option>
                      <option value="RO">Rondônia</option>
                      <option value="RR">Roraima</option>
                      <option value="SC">Santa Catarina</option>
                      <option value="SP">São Paulo</option>
                      <option value="SE">Sergipe</option>
                      <option value="TO">Tocantins</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="pais" class="required">País</label>
                    <select id="pais" name="pais" required>
                      <option value="Brasil">Brasil</option>
                      <option value="Argentina">Argentina</option>
                      <option value="Paraguai">Paraguai</option>
                      <option value="Uruguai">Uruguai</option>
                      <option value="Chile">Chile</option>
                      <option value="Bolívia">Bolívia</option>
                      <option value="Peru">Peru</option>
                      <option value="Colômbia">Colômbia</option>
                      <option value="Venezuela">Venezuela</option>
                      <option value="Equador">Equador</option>
                      <option value="Outro">Outro</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-cog"></i> Configurações Adicionais
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="codigoClassificacao">Código Classificação</label>
                    <input type="text" id="codigoClassificacao" name="codigoClassificacao" placeholder="Código interno">
                  </div>
                  <div class="form-col">
                    <label for="codigoPais">Código País</label>
                    <input type="text" id="codigoPais" name="codigoPais" placeholder="Ex: 1058">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Aba: Contatos -->
          <div id="contatosTab" class="tab-content">
            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-phone"></i> Informações de Contato
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="email" class="required">E-mail Principal</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                  </div>
                  <div class="form-col">
                    <label for="emailNfe">E-mail NFe</label>
                    <input type="email" id="emailNfe" name="emailNfe" placeholder="<EMAIL>">
                  </div>
                  <div class="form-col">
                    <label for="homePage">Website</label>
                    <input type="url" id="homePage" name="homePage" placeholder="https://www.empresa.com.br">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="telefone1" class="required">Telefone Principal</label>
                    <input type="tel" id="telefone1" name="telefone1" required placeholder="(11) 1234-5678">
                  </div>
                  <div class="form-col">
                    <label for="telefone2">Telefone Secundário</label>
                    <input type="tel" id="telefone2" name="telefone2" placeholder="(11) 1234-5678">
                  </div>
                  <div class="form-col">
                    <label for="celular1">Celular/WhatsApp</label>
                    <input type="tel" id="celular1" name="celular1" placeholder="(11) 99999-9999">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="fax">Fax</label>
                    <input type="tel" id="fax" name="fax" placeholder="(11) 1234-5678">
                  </div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-user-tie"></i> Contatos Principais
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="contatoPrincipal">Nome do Contato Principal</label>
                    <input type="text" id="contatoPrincipal" name="contatoPrincipal" placeholder="Nome completo">
                  </div>
                  <div class="form-col">
                    <label for="cargoContato">Cargo/Função</label>
                    <input type="text" id="cargoContato" name="cargoContato" placeholder="Ex: Gerente Comercial">
                  </div>
                  <div class="form-col">
                    <label for="telefoneContato">Telefone do Contato</label>
                    <input type="tel" id="telefoneContato" name="telefoneContato" placeholder="(11) 99999-9999">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="emailContato">E-mail do Contato</label>
                    <input type="email" id="emailContato" name="emailContato" placeholder="<EMAIL>">
                  </div>
                  <div class="form-col">
                    <label for="contatoFinanceiro">Contato Financeiro</label>
                    <input type="text" id="contatoFinanceiro" name="contatoFinanceiro" placeholder="Nome do responsável financeiro">
                  </div>
                  <div class="form-col">
                    <label for="telefoneFinanceiro">Telefone Financeiro</label>
                    <input type="tel" id="telefoneFinanceiro" name="telefoneFinanceiro" placeholder="(11) 99999-9999">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="emailFinanceiro">E-mail Financeiro</label>
                    <input type="email" id="emailFinanceiro" name="emailFinanceiro" placeholder="<EMAIL>">
                  </div>
                  <div class="form-col">
                    <label for="contatoTecnico">Contato Técnico</label>
                    <input type="text" id="contatoTecnico" name="contatoTecnico" placeholder="Nome do responsável técnico">
                  </div>
                  <div class="form-col">
                    <label for="emailTecnico">E-mail Técnico</label>
                    <input type="email" id="emailTecnico" name="emailTecnico" placeholder="<EMAIL>">
                  </div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-clock"></i> Horários de Atendimento
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="horarioAtendimento">Horário de Funcionamento</label>
                    <input type="text" id="horarioAtendimento" name="horarioAtendimento" placeholder="Ex: 08:00 às 18:00">
                  </div>
                  <div class="form-col">
                    <label for="diasFuncionamento">Dias de Funcionamento</label>
                    <input type="text" id="diasFuncionamento" name="diasFuncionamento" placeholder="Ex: Segunda a Sexta">
                  </div>
                  <div class="form-col">
                    <label for="fusoHorario">Fuso Horário</label>
                    <select id="fusoHorario" name="fusoHorario">
                      <option value="America/Sao_Paulo">Brasília (GMT-3)</option>
                      <option value="America/Manaus">Manaus (GMT-4)</option>
                      <option value="America/Rio_Branco">Acre (GMT-5)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Campos ocultos para compatibilidade -->
          <input type="hidden" id="contato1" name="contato1">
          <input type="hidden" id="cargo1" name="cargo1">
          <input type="hidden" id="departamento1" name="departamento1">
          <input type="hidden" id="email1" name="email1">
          <input type="hidden" id="autorizaXml1" name="autorizaXml1" value="false">
          <input type="hidden" id="contato2" name="contato2">
          <input type="hidden" id="cargo2" name="cargo2">
          <input type="hidden" id="departamento2" name="departamento2">
          <input type="hidden" id="telefone3" name="telefone3">
          <input type="hidden" id="telefone4" name="telefone4">
          <input type="hidden" id="celular2" name="celular2">
          <input type="hidden" id="celular3" name="celular3">
          <input type="hidden" id="email2" name="email2">
          <input type="hidden" id="autorizaXml2" name="autorizaXml2" value="false">
          <input type="hidden" id="categorias" name="categorias">
          <input type="hidden" id="observacoes" name="observacoes">
          <input type="hidden" id="dataAtualizacao" name="dataAtualizacao">
          <input type="hidden" id="codigoVendedor" name="codigoVendedor">
          <input type="hidden" id="codigoRegiao" name="codigoRegiao">
          <input type="hidden" id="codigoArea" name="codigoArea">
          <input type="hidden" id="limite" name="limite">
          <input type="hidden" id="temSubstituicao" name="temSubstituicao" value="false">
          <input type="hidden" id="simplesNacional" name="simplesNacional" value="false">
          <input type="hidden" id="cnpjCpf2" name="cnpjCpf2">
          <input type="hidden" id="cnpjCpf3" name="cnpjCpf3">
          <input type="hidden" id="suframa" name="suframa">
          <input type="hidden" id="im" name="im">
          <input type="hidden" id="indicacao" name="indicacao">
          <input type="hidden" id="latitudeCLI" name="latitudeCLI">
          <input type="hidden" id="longitudeCLI" name="longitudeCLI">
          <input type="hidden" id="intervista" name="intervista">
          <input type="hidden" id="acrescimoCLI" name="acrescimoCLI">
          <input type="hidden" id="codCusto" name="codCusto">
          <input type="hidden" id="cotacao" name="cotacao">
          <input type="hidden" id="reducao" name="reducao">
          <input type="hidden" id="contaContabil" name="contaContabil">
          <input type="hidden" id="condicoesPagamento" name="condicoesPagamento">
          <input type="hidden" id="limiteCredito" name="limiteCredito">
          <input type="hidden" id="desconto" name="desconto">
          <input type="hidden" id="prazoEntrega" name="prazoEntrega">
          <input type="hidden" id="valorMinimoCompra" name="valorMinimoCompra">
          <input type="hidden" id="moeda" name="moeda" value="BRL">
          <input type="hidden" id="avaliacaoQualidade" name="avaliacaoQualidade">
          <input type="hidden" id="avaliacaoPreco" name="avaliacaoPreco">
          <input type="hidden" id="classificacaoRisco" name="classificacaoRisco" value="BAIXO">
          <input type="hidden" id="fornecedorEstrategico" name="fornecedorEstrategico" value="false">
          <input type="hidden" id="dataHomologacao" name="dataHomologacao">
          <input type="hidden" id="validadeHomologacao" name="validadeHomologacao">
          <input type="hidden" id="certificacoes" name="certificacoes">
          <input type="hidden" id="horarioAtendimento" name="horarioAtendimento">
          <input type="hidden" id="diasFuncionamento" name="diasFuncionamento">
          <input type="hidden" id="fusoHorario" name="fusoHorario" value="America/Sao_Paulo">

          

          <!-- Aba: Comercial -->
          <div id="comercialTab" class="tab-content">
            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-handshake"></i> Informações Comerciais
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="condicoesPagamento">Condições de Pagamento</label>
                    <select id="condicoesPagamento" name="condicoesPagamento">
                      <option value="">Selecione...</option>
                      <option value="A_VISTA">À Vista</option>
                      <option value="7_DIAS">7 dias</option>
                      <option value="15_DIAS">15 dias</option>
                      <option value="30_DIAS">30 dias</option>
                      <option value="45_DIAS">45 dias</option>
                      <option value="60_DIAS">60 dias</option>
                      <option value="90_DIAS">90 dias</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="limiteCredito">Limite de Crédito (R$)</label>
                    <input type="number" id="limiteCredito" name="limiteCredito" step="0.01" placeholder="0,00">
                  </div>
                  <div class="form-col">
                    <label for="desconto">Desconto Padrão (%)</label>
                    <input type="number" id="desconto" name="desconto" step="0.01" min="0" max="100" placeholder="0,00">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="prazoEntrega">Prazo de Entrega (dias)</label>
                    <input type="number" id="prazoEntrega" name="prazoEntrega" min="1" placeholder="Ex: 15">
                  </div>
                  <div class="form-col">
                    <label for="valorMinimoCompra">Valor Mínimo de Compra (R$)</label>
                    <input type="number" id="valorMinimoCompra" name="valorMinimoCompra" step="0.01" placeholder="0,00">
                  </div>
                  <div class="form-col">
                    <label for="moeda">Moeda</label>
                    <select id="moeda" name="moeda">
                      <option value="BRL">Real (BRL)</option>
                      <option value="USD">Dólar (USD)</option>
                      <option value="EUR">Euro (EUR)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-chart-line"></i> Métricas e Avaliação
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="avaliacaoQualidade">Avaliação de Qualidade (1-5)</label>
                    <input type="number" id="avaliacaoQualidade" name="avaliacaoQualidade" min="1" max="5" step="0.1" placeholder="4.5">
                  </div>
                  <div class="form-col">
                    <label for="avaliacaoEntrega">Avaliação de Entrega (1-5)</label>
                    <input type="number" id="avaliacaoEntrega" name="avaliacaoEntrega" min="1" max="5" step="0.1" placeholder="4.5">
                  </div>
                  <div class="form-col">
                    <label for="avaliacaoPreco">Avaliação de Preço (1-5)</label>
                    <input type="number" id="avaliacaoPreco" name="avaliacaoPreco" min="1" max="5" step="0.1" placeholder="4.5">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="classificacaoRisco">Classificação de Risco</label>
                    <select id="classificacaoRisco" name="classificacaoRisco">
                      <option value="BAIXO">Baixo Risco</option>
                      <option value="MEDIO">Médio Risco</option>
                      <option value="ALTO">Alto Risco</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="fornecedorEstrategico">Fornecedor Estratégico</label>
                    <select id="fornecedorEstrategico" name="fornecedorEstrategico">
                      <option value="false">Não</option>
                      <option value="true">Sim</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Aba: Documentos -->
          <div id="documentosTab" class="tab-content">
            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-file-alt"></i> Documentação e Homologação
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="statusHomologacao" class="required">Status de Homologação</label>
                    <select id="statusHomologacao" name="statusHomologacao" required>
                      <option value="Pendente">Pendente</option>
                      <option value="Em Análise">Em Análise</option>
                      <option value="Homologado">Homologado</option>
                      <option value="Rejeitado">Rejeitado</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="dataHomologacao">Data de Homologação</label>
                    <input type="date" id="dataHomologacao" name="dataHomologacao">
                  </div>
                  <div class="form-col">
                    <label for="validadeHomologacao">Validade da Homologação</label>
                    <input type="date" id="validadeHomologacao" name="validadeHomologacao">
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="certificacoes">Certificações</label>
                    <textarea id="certificacoes" name="certificacoes" rows="3" placeholder="Ex: ISO 9001, ISO 14001, OHSAS 18001..."></textarea>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-col">
                    <label for="observacoes">Observações Gerais</label>
                    <textarea id="observacoes" name="observacoes" rows="4" placeholder="Informações adicionais sobre o fornecedor..."></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-cog"></i> Configurações do Sistema
                </div>
              </div>
              <div class="section-body">
                <div class="form-row">
                  <div class="form-col">
                    <label for="ativo">Situação</label>
                    <select id="ativo" name="ativo">
                      <option value="true">Ativo</option>
                      <option value="false">Inativo</option>
                    </select>
                  </div>
                  <div class="form-col">
                    <label for="dataAtualizacao">Data de Atualização</label>
                    <input type="text" id="dataAtualizacao" name="dataAtualizacao" readonly>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Ações do Formulário -->
          <div class="actions">
            <button type="button" class="btn btn-danger" onclick="cancelEdit()">
              <i class="fas fa-times"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-success" id="submitButton">
              <i class="fas fa-save"></i>
              Cadastrar Fornecedor
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Tabela de Fornecedores -->
    <div class="table-container" id="tableContainer" style="display: block !important; visibility: visible !important;">
      <div class="table-header">
        <div class="table-title">
          <i class="fas fa-table"></i>
          Parceiros Cadastrados
        </div>
        <div>
          <button class="btn btn-info btn-sm" onclick="filterSuppliers()">
            <i class="fas fa-sync-alt"></i>
            Atualizar
          </button>
        </div>
      </div>

      <div style="overflow-x: auto;">
        <table>
          <thead>
            <tr>
              <th onclick="sortTable('codigo')">Código</th>
              <th onclick="sortTable('tipo')">Tipo</th>
              <th onclick="sortTable('razaoSocial')">Razão Social</th>
              <th onclick="sortTable('cnpjCpf')">CNPJ/CPF</th>
              <th>Cidade/UF</th>
              <th>Status</th>
              <th>Situação</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="suppliersTableBody">
            <!-- Dados serão carregados via JavaScript -->
          </tbody>
        </table>
      </div>
    </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      addDoc, 
      getDocs, 
      doc, 
      getDoc,
      updateDoc, 
      deleteDoc,
      query,
      where
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let fornecedores = [];
    let selectedCategories = new Set();
    let sortDirection = 'asc';
    let currentSortColumn = '';
    let systemParams = {}; // Parâmetros do sistema carregados do config_parametros
    let lastUpdateTime = null; // Controle de cache
    let isLoading = false; // Prevenir múltiplos carregamentos simultâneos

    // Função para carregar parâmetros do sistema
    async function loadSystemParams() {
      try {
        const paramsDoc = await getDoc(doc(db, "parametros", "sistema"));
        if (paramsDoc.exists()) {
          systemParams = paramsDoc.data();
          console.log("📋 Parâmetros do sistema carregados:", systemParams);

          // Aplicar configurações baseadas nos parâmetros
          applySystemConfiguration();
        } else {
          console.warn("⚠️ Documento de parâmetros não encontrado, usando configurações padrão");
          systemParams = getDefaultParams();
        }
      } catch (error) {
        console.error("❌ Erro ao carregar parâmetros do sistema:", error);
        systemParams = getDefaultParams();
      }
    }

    // Configurações padrão caso não consiga carregar do Firebase
    function getDefaultParams() {
      return {
        moduloQualidadeAtivo: false,
        homologacaoFornecedores: false,
        metricasFornecedores: false,
        inspecaoRecebimento: false,
        armazemQualidade: false,
        rastreabilidadeLote: false
      };
    }

    // Aplicar configurações do sistema
    function applySystemConfiguration() {
      // Mostrar/ocultar atalhos de qualidade baseado nos parâmetros
      updateQualityShortcuts();

      // Configurar campos obrigatórios baseado nos parâmetros
      updateRequiredFields();

      // Atualizar opções de status de homologação
      updateHomologationOptions();
    }

    // Atualizar atalhos de qualidade
    function updateQualityShortcuts() {
      const qualityButton = document.querySelector('button[onclick="goToQualidade()"]');
      const homologationButton = document.querySelector('button[onclick="goToHomologacao()"]');

      if (qualityButton) {
        if (systemParams.moduloQualidadeAtivo) {
          qualityButton.style.display = 'block';
          qualityButton.innerHTML = '<i class="fas fa-search"></i> Módulo Qualidade Ativo';
        } else {
          qualityButton.style.display = 'none';
        }
      }

      if (homologationButton) {
        if (systemParams.homologacaoFornecedores) {
          homologationButton.innerHTML = '<i class="fas fa-industry"></i> Homologação (Ativa)';
          homologationButton.classList.add('btn-success');
          homologationButton.classList.remove('btn-primary');
        } else {
          homologationButton.innerHTML = '<i class="fas fa-industry"></i> Homologação (Inativa)';
          homologationButton.classList.add('btn-primary');
          homologationButton.classList.remove('btn-success');
        }
      }
    }

    // Atualizar campos obrigatórios
    function updateRequiredFields() {
      const statusHomologacao = document.getElementById('statusHomologacao');

      if (systemParams.homologacaoFornecedores) {
        // Se homologação está ativa, status é obrigatório
        statusHomologacao.required = true;
        statusHomologacao.parentElement.querySelector('label').classList.add('required');
      } else {
        // Se homologação não está ativa, status não é obrigatório
        statusHomologacao.required = false;
        statusHomologacao.parentElement.querySelector('label').classList.remove('required');
      }
    }

    // Atualizar opções de homologação
    function updateHomologationOptions() {
      const statusSelect = document.getElementById('statusHomologacao');

      if (systemParams.homologacaoFornecedores) {
        // Adicionar mais opções quando homologação está ativa
        statusSelect.innerHTML = `
          <option value="Pendente">Pendente</option>
          <option value="Em Análise">Em Análise</option>
          <option value="Documentação Incompleta">Documentação Incompleta</option>
          <option value="Aguardando Visita Técnica">Aguardando Visita Técnica</option>
          <option value="Homologado">Homologado</option>
          <option value="Rejeitado">Rejeitado</option>
          <option value="Suspenso">Suspenso</option>
        `;
      } else {
        // Opções básicas quando homologação não está ativa
        statusSelect.innerHTML = `
          <option value="Pendente">Pendente</option>
          <option value="Aprovado">Aprovado</option>
          <option value="Rejeitado">Rejeitado</option>
        `;
      }
    }

    // Função para configurar máscaras de entrada
    function setupMasks() {
      // Máscara para CNPJ/CPF
      const cnpjCpfInput = document.getElementById('cnpjCpf');
      if (cnpjCpfInput) {
        cnpjCpfInput.addEventListener('input', function(e) {
          let value = e.target.value.replace(/\D/g, '');
          const tipoPessoa = document.getElementById('tipoPessoa').value;

          if (tipoPessoa === 'Fisica') {
            // Máscara CPF: 000.000.000-00
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
          } else {
            // Máscara CNPJ: 00.000.000/0000-00
            value = value.replace(/(\d{2})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1/$2');
            value = value.replace(/(\d{4})(\d{1,2})$/, '$1-$2');
          }

          e.target.value = value;
        });
      }

      // Máscara para CEP
      const cepInput = document.getElementById('cep');
      if (cepInput) {
        cepInput.addEventListener('input', function(e) {
          let value = e.target.value.replace(/\D/g, '');
          value = value.replace(/(\d{5})(\d)/, '$1-$2');
          e.target.value = value;
        });
      }

      // Máscara para telefones
      const phoneInputs = ['telefone1', 'telefone2', 'celular1', 'fax', 'telefoneContato', 'telefoneFinanceiro'];
      phoneInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
          input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 10) {
              // Telefone fixo: (00) 0000-0000
              value = value.replace(/(\d{2})(\d)/, '($1) $2');
              value = value.replace(/(\d{4})(\d)/, '$1-$2');
            } else {
              // Celular: (00) 00000-0000
              value = value.replace(/(\d{2})(\d)/, '($1) $2');
              value = value.replace(/(\d{5})(\d)/, '$1-$2');
            }
            e.target.value = value;
          });
        }
      });

      // Máscara para valores monetários
      const moneyInputs = ['limiteCredito', 'valorMinimoCompra'];
      moneyInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
          input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = (value / 100).toFixed(2);
            e.target.value = value;
          });
        }
      });

      console.log("🎭 Máscaras de entrada configuradas");
    }

    // Função para atualizar máscara CNPJ/CPF baseada no tipo de pessoa
    window.updateCpfCnpjMask = function() {
      const cnpjCpfInput = document.getElementById('cnpjCpf');
      const tipoPessoa = document.getElementById('tipoPessoa').value;

      if (cnpjCpfInput) {
        cnpjCpfInput.value = ''; // Limpar campo ao trocar tipo

        if (tipoPessoa === 'Fisica') {
          cnpjCpfInput.placeholder = '000.000.000-00';
          cnpjCpfInput.maxLength = 14;
        } else {
          cnpjCpfInput.placeholder = '00.000.000/0000-00';
          cnpjCpfInput.maxLength = 18;
        }
      }

      console.log(`🔄 Máscara atualizada para: ${tipoPessoa}`);
    };

    // Função para alternar entre abas
    window.switchTab = function(tabName) {
      // Remover classe active de todas as abas
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });

      // Ocultar todo o conteúdo das abas
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });

      // Ativar a aba clicada
      document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');

      // Mostrar o conteúdo da aba correspondente
      const tabContent = document.getElementById(`${tabName}Tab`);
      if (tabContent) {
        tabContent.classList.add('active');
      }

      console.log(`🔄 Alternando para aba: ${tabName}`);
    };

    // Função para mostrar/ocultar campos de transportadora
    window.toggleTransportFields = function() {
      const categoria = document.getElementById('categoriaPrincipal').value;
      const transportSection = document.getElementById('transportSection');

      if (categoria === 'TRANSPORTADORA') {
        if (transportSection) {
          transportSection.style.display = 'block';
        }
        // Tornar campos obrigatórios
        const antt = document.getElementById('antt');
        if (antt) antt.required = true;
      } else {
        if (transportSection) {
          transportSection.style.display = 'none';
        }
        // Remover obrigatoriedade
        const antt = document.getElementById('antt');
        if (antt) antt.required = false;
        // Limpar campos
        clearTransportFields();
      }
    };

    // Função para limpar campos de transportadora
    function clearTransportFields() {
      // Verificar e limpar campos básicos
      const antt = document.getElementById('antt');
      if (antt) antt.value = '';
      
      const seguroTransporte = document.getElementById('seguroTransporte');
      if (seguroTransporte) seguroTransporte.value = 'false';
      
      const avaliacaoEntrega = document.getElementById('avaliacaoEntrega');
      if (avaliacaoEntrega) avaliacaoEntrega.value = '';
      
      // Campos que podem não existir
      const prazoMedioEntrega = document.getElementById('prazoMedioEntrega');
      if (prazoMedioEntrega) prazoMedioEntrega.value = '';
      
      const valorMinimoFrete = document.getElementById('valorMinimoFrete');
      if (valorMinimoFrete) valorMinimoFrete.value = '';
      
      const formaPagamentoFrete = document.getElementById('formaPagamentoFrete');
      if (formaPagamentoFrete) formaPagamentoFrete.value = 'A_VISTA';
      
      const observacoesTransporte = document.getElementById('observacoesTransporte');
      if (observacoesTransporte) observacoesTransporte.value = '';

      // Limpar checkboxes
      document.querySelectorAll('input[name="tiposVeiculo"]').forEach(cb => cb.checked = false);
      document.querySelectorAll('input[name="rotasAtendidas"]').forEach(cb => cb.checked = false);
    }

    // Função para coletar dados de transportadora
    function getTransportData() {
      if (document.getElementById('categoriaPrincipal').value !== 'TRANSPORTADORA') {
        return null;
      }

      const tiposVeiculo = Array.from(document.querySelectorAll('input[name="tiposVeiculo"]:checked'))
        .map(cb => cb.value);

      const rotasAtendidas = Array.from(document.querySelectorAll('input[name="rotasAtendidas"]:checked'))
        .map(cb => cb.value);

      return {
        antt: document.getElementById('antt').value,
        seguroTransporte: document.getElementById('seguroTransporte').value === 'true',
        avaliacaoEntrega: parseFloat(document.getElementById('avaliacaoEntrega').value) || null,
        tiposVeiculo: tiposVeiculo,
        rotasAtendidas: rotasAtendidas,
        prazoMedioEntrega: parseInt(document.getElementById('prazoMedioEntrega').value) || null,
        valorMinimoFrete: parseFloat(document.getElementById('valorMinimoFrete').value) || null,
        formaPagamentoFrete: document.getElementById('formaPagamentoFrete').value,
        observacoesTransporte: document.getElementById('observacoesTransporte').value
      };
    }

    // Função para preencher dados de transportadora na edição
    function setTransportData(dadosTransporte) {
      if (!dadosTransporte) return;

      // Verificar e preencher campos básicos
      const antt = document.getElementById('antt');
      if (antt) antt.value = dadosTransporte.antt || '';
      
      const seguroTransporte = document.getElementById('seguroTransporte');
      if (seguroTransporte) seguroTransporte.value = dadosTransporte.seguroTransporte ? 'true' : 'false';
      
      const avaliacaoEntrega = document.getElementById('avaliacaoEntrega');
      if (avaliacaoEntrega) avaliacaoEntrega.value = dadosTransporte.avaliacaoEntrega || '';
      
      // Campos que podem não existir
      const prazoMedioEntrega = document.getElementById('prazoMedioEntrega');
      if (prazoMedioEntrega) prazoMedioEntrega.value = dadosTransporte.prazoMedioEntrega || '';
      
      const valorMinimoFrete = document.getElementById('valorMinimoFrete');
      if (valorMinimoFrete) valorMinimoFrete.value = dadosTransporte.valorMinimoFrete || '';
      
      const formaPagamentoFrete = document.getElementById('formaPagamentoFrete');
      if (formaPagamentoFrete) formaPagamentoFrete.value = dadosTransporte.formaPagamentoFrete || 'A_VISTA';
      
      const observacoesTransporte = document.getElementById('observacoesTransporte');
      if (observacoesTransporte) observacoesTransporte.value = dadosTransporte.observacoesTransporte || '';

      // Marcar checkboxes
      if (dadosTransporte.tiposVeiculo) {
        dadosTransporte.tiposVeiculo.forEach(tipo => {
          const checkbox = document.querySelector(`input[name="tiposVeiculo"][value="${tipo}"]`);
          if (checkbox) checkbox.checked = true;
        });
      }

      if (dadosTransporte.rotasAtendidas) {
        dadosTransporte.rotasAtendidas.forEach(rota => {
          const checkbox = document.querySelector(`input[name="rotasAtendidas"][value="${rota}"]`);
          if (checkbox) checkbox.checked = true;
        });
      }
    }

    window.onload = async function() {
      $(document).ready(function() {
        updateCpfCnpjMask();
        $('#cep').mask('00000-000');
        $('#telefone1').mask('(00) 00000-0000');
        $('#telefone2').mask('(00) 00000-0000');
        $('#telefone3').mask('(00) 00000-0000');
        $('#telefone4').mask('(00) 00000-0000');
        $('#celular1').mask('(00) 00000-0000');
        $('#celular2').mask('(00) 00000-0000');
        $('#celular3').mask('(00) 00000-0000');
        $('#fax').mask('(00) 00000-0000');
        $('#cnpjCpf2').mask('00.000.000/0000-00');
        $('#cnpjCpf3').mask('00.000.000/0000-00');
      });

      await loadSuppliers();

      // Configurar busca em tempo real
      const searchInput = document.getElementById('searchInput');
      if (searchInput) {
        // Função de busca imediata
        const performSearch = function() {
          filterSuppliers();
        };

        // Event listeners para busca em tempo real
        searchInput.addEventListener('input', performSearch);
        searchInput.addEventListener('keyup', performSearch);
        searchInput.addEventListener('paste', function() {
          setTimeout(performSearch, 10); // Pequeno delay para paste
        });
      }

      // Event listeners para outros filtros
      const tipoFilter = document.getElementById('tipoFilter');
      const statusFilter = document.getElementById('statusFilter');
      const ativoFilter = document.getElementById('ativoFilter');

      if (tipoFilter) tipoFilter.addEventListener('change', filterSuppliers);
      if (statusFilter) statusFilter.addEventListener('change', filterSuppliers);
      if (ativoFilter) ativoFilter.addEventListener('change', filterSuppliers);
    };

    window.navigateBack = function() {
      try {
        window.location.href = 'index.html';
      } catch (error) {
        console.warn("Navegação não suportada:", error);
        alert("Funcionalidade de voltar não disponível no StackBlitz.");
      }
    };

    window.clearForm = function() {
      document.getElementById('supplierForm').reset();
      document.getElementById('editingId').value = '';
      document.getElementById('submitButton').textContent = 'Cadastrar Parceiro';
      selectedCategories.clear();
      updateCategoriesDisplay();

      // Scroll para o formulário
      const formElement = document.getElementById('supplierForm');
      if (formElement) {
        formElement.scrollIntoView({
          behavior: 'smooth'
        });
      }
    };

    // Função para forçar atualização manual
    window.forceRefresh = async function() {
      console.log("🔄 Forçando atualização manual...");

      try {
        // Aguardar um pouco para garantir que o Firebase processou as mudanças
        await new Promise(resolve => setTimeout(resolve, 500));

        // Recarregar dados forçadamente (ignora cache)
        await loadSuppliers(true);

        console.log("✅ Atualização manual concluída");

        // Mostrar feedback visual
        showSuccessMessage("Dados atualizados com sucesso!");

      } catch (error) {
        console.error("❌ Erro na atualização manual:", error);
        alert("Erro ao atualizar dados: " + error.message);
      }
    };

    // Função para mostrar mensagem de sucesso
    function showSuccessMessage(message) {
      // Criar elemento de notificação
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease-out;
      `;

      notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
      `;

      // Adicionar CSS da animação
      if (!document.getElementById('notificationStyles')) {
        const style = document.createElement('style');
        style.id = 'notificationStyles';
        style.textContent = `
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
          @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
          }
        `;
        document.head.appendChild(style);
      }

      document.body.appendChild(notification);

      // Remover após 3 segundos
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 3000);
    }

    window.updateCpfCnpjMask = function() {
      const tipoPessoa = document.getElementById('tipoPessoa').value;
      const $cnpjCpf = $('#cnpjCpf');
      $cnpjCpf.unmask();
      if (tipoPessoa === 'Juridica') {
        $cnpjCpf.mask('00.000.000/0000-00');
      } else {
        $cnpjCpf.mask('000.000.000-00');
      }
    };

    async function loadSuppliers(forceReload = false) {
      // Prevenir múltiplos carregamentos simultâneos
      if (isLoading && !forceReload) {
        console.log("⏳ Carregamento já em andamento, aguardando...");
        return;
      }

      // Verificar cache (não recarregar se foi carregado há menos de 30 segundos)
      const now = Date.now();
      if (!forceReload && lastUpdateTime && (now - lastUpdateTime) < 30000) {
        console.log("📋 Usando dados do cache (carregados há menos de 30s)");
        displaySuppliers();
        return;
      }

      isLoading = true;

      try {
        console.log("🔄 Carregando fornecedores do Firebase...");

        // Mostrar indicador de carregamento
        showLoadingIndicator();

        // Forçar reload sem cache
        const snapshot = await getDocs(collection(db, "fornecedores"));
        fornecedores = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          _loadedAt: now // Timestamp de quando foi carregado
        }));

        // Atualizar timestamp do cache
        lastUpdateTime = now;

        console.log(`✅ ${fornecedores.length} fornecedores carregados do Firestore`);

        // Limpar filtros apenas se for um reload forçado
        if (forceReload) {
          clearFilters();
        }

        // Atualizar display
        displaySuppliers();

        // Mostrar tabela após carregar dados
        const tableContainer = document.getElementById('tableContainer');
        if (tableContainer) {
          tableContainer.style.display = 'block';
          tableContainer.style.visibility = 'visible';
        }

        // Atualizar contadores
        updateSupplierCounts();

        // Ocultar indicador de carregamento
        hideLoadingIndicator();

      } catch (error) {
        console.error("❌ Erro ao carregar fornecedores:", error);
        hideLoadingIndicator();
        alert("Erro ao carregar fornecedores. Por favor, recarregue a página.");
      } finally {
        isLoading = false;
      }
    }

    // Mostrar indicador de carregamento
    function showLoadingIndicator() {
      const tableBody = document.getElementById('suppliersTableBody');
      if (tableBody) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px;">
              <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 32px; color: #3498db;"></i>
                <span style="color: #6c757d; font-weight: 500;">Carregando fornecedores...</span>
              </div>
            </td>
          </tr>
        `;
      }
    }

    // Ocultar indicador de carregamento
    function hideLoadingIndicator() {
      // O displaySuppliers() já vai substituir o conteúdo
    }

    // Função para limpar filtros
    function clearFilters() {
      document.getElementById('searchInput').value = '';
      document.getElementById('tipoFilter').value = '';
      document.getElementById('statusFilter').value = '';
      document.getElementById('ativoFilter').value = '';
      if (document.getElementById('categoriaFilter')) {
        document.getElementById('categoriaFilter').value = '';
      }
    }

    // Função para atualizar contadores
    function updateSupplierCounts() {
      const totalCount = fornecedores.length;
      const activeCount = fornecedores.filter(f => f.ativo !== false).length;
      const homologatedCount = fornecedores.filter(f => f.statusHomologacao === 'Homologado').length;

      console.log(`📊 Estatísticas: Total: ${totalCount}, Ativos: ${activeCount}, Homologados: ${homologatedCount}`);

      // Atualizar elementos na tela se existirem
      const totalElement = document.getElementById('totalSuppliers');
      if (totalElement) totalElement.textContent = totalCount;

      const activeElement = document.getElementById('activeSuppliers');
      if (activeElement) activeElement.textContent = activeCount;

      const homologatedElement = document.getElementById('homologatedSuppliers');
      if (homologatedElement) homologatedElement.textContent = homologatedCount;
    }

    function displaySuppliers(filteredSuppliers = fornecedores) {
      const tableBody = document.getElementById('suppliersTableBody');
      if (!tableBody) {
        return;
      }

      tableBody.innerHTML = '';

      filteredSuppliers.forEach(fornecedor => {
        // Status badges
        const tipoClass = fornecedor.tipo ? `status-${fornecedor.tipo.toLowerCase()}` : 'status-fornecedor';
        const statusClass = fornecedor.statusHomologacao === 'Homologado' ? 'status-ativo' : 'status-inativo';
        const ativoClass = fornecedor.ativo !== false ? 'status-ativo' : 'status-inativo';

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${fornecedor.codigo || 'N/A'}</td>
          <td>
            <span class="status-badge ${tipoClass}">
              ${fornecedor.tipo || 'Fornecedor'}
            </span>
          </td>
          <td>${fornecedor.razaoSocial || 'N/A'}</td>
          <td>${fornecedor.cnpjCpf || 'N/A'}</td>
          <td>${(fornecedor.cidade || 'N/A')} / ${(fornecedor.estado || 'N/A')}</td>
          <td>
            <span class="status-badge ${statusClass}">
              ${fornecedor.statusHomologacao || 'Pendente'}
            </span>
          </td>
          <td>
            <span class="status-badge ${ativoClass}">
              ${fornecedor.ativo !== false ? 'Ativo' : 'Inativo'}
            </span>
          </td>
          <td class="action-buttons">
            <button class="edit-btn" onclick="editSupplier('${fornecedor.id}')" title="Editar">
              <i class="fas fa-edit"></i>
            </button>
            <button class="delete-btn" onclick="deleteSupplier('${fornecedor.id}')" title="Excluir">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    function formatCategories(categories) {
      if (!categories || !Array.isArray(categories) || categories.length === 0) return 'Nenhuma categoria';
      return categories.join(', ');
    }

    window.sortTable = function(sortBy) {
      if (sortBy === currentSortColumn) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        currentSortColumn = sortBy;
        sortDirection = 'asc';
      }

      if (sortBy === 'codigo') {
        fornecedores.sort((a, b) => sortDirection === 'asc' 
          ? a.codigo.localeCompare(b.codigo) 
          : b.codigo.localeCompare(a.codigo));
      } else if (sortBy === 'tipo') {
        fornecedores.sort((a, b) => sortDirection === 'asc'
          ? (a.tipo || 'Fornecedor').localeCompare(b.tipo || 'Fornecedor')
          : (b.tipo|| 'Fornecedor').localeCompare(a.tipo || 'Fornecedor'));
      } else if (sortBy === 'razaoSocial') {
        fornecedores.sort((a, b) => sortDirection === 'asc' 
          ? a.razaoSocial.localeCompare(b.razaoSocial) 
          : b.razaoSocial.localeCompare(a.razaoSocial));
      } else if (sortBy === 'cnpjCpf') {
        fornecedores.sort((a, b) => sortDirection === 'asc' 
          ? a.cnpjCpf.localeCompare(b.cnpjCpf) 
          : b.cnpjCpf.localeCompare(a.cnpjCpf));
      } else if (sortBy === 'telefone1') {
        fornecedores.sort((a, b) => sortDirection === 'asc' 
          ? a.telefone1.localeCompare(b.telefone1) 
          : b.telefone1.localeCompare(a.telefone1));
      } else if (sortBy === 'statusHomologacao') {
        fornecedores.sort((a, b) => sortDirection === 'asc' 
          ? (a.statusHomologacao || 'Pendente').localeCompare(b.statusHomologacao || 'Pendente') 
          : (b.statusHomologacao || 'Pendente').localeCompare(a.statusHomologacao || 'Pendente'));
      }

      updateSortIndicators(sortBy, sortDirection);
      displaySuppliers();
    };

    function updateSortIndicators(column, direction) {
      document.getElementById('sortCodigo').innerHTML = '';
      document.getElementById('sortRazaoSocial').innerHTML = '';
      document.getElementById('sortCnpjCpf').innerHTML = '';
      document.getElementById('sortTelefone1').innerHTML = '';
      document.getElementById('sortTipo').innerHTML = '';
      document.getElementById('sortStatus').innerHTML = '';

      if (column === 'codigo') {
        document.getElementById('sortCodigo').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'razaoSocial') {
        document.getElementById('sortRazaoSocial').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'cnpjCpf') {
        document.getElementById('sortCnpjCpf').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'telefone1') {
        document.getElementById('sortTelefone1').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'tipo') {
        document.getElementById('sortTipo').innerHTML = direction === 'asc' ? '▲' : '▼';
      } else if (column === 'statusHomologacao') {
        document.getElementById('sortStatus').innerHTML = direction === 'asc' ? '▲' : '▼';
      }
    }

    window.addCategory = function() {
      const select = document.getElementById('categoriaSelect');
      const categoria = select.value;

      if (!categoria) {
        alert('Selecione uma categoria.');
        return;
      }

      if (selectedCategories.has(categoria)) {
        alert('Esta categoria já foi adicionada.');
        return;
      }

      selectedCategories.add(categoria);
      console.log("Categoria adicionada:", categoria, "Set atual:", Array.from(selectedCategories));
      updateCategoriesDisplay();
      select.value = '';
    };

    function updateCategoriesDisplay() {
      const container = document.getElementById('categoriasContainer');
      const categoriesInput = document.getElementById('categorias');

      // Verificar se os elementos existem antes de manipulá-los
      if (!container || !categoriesInput) {
        console.log("🔄 Elementos de categorias não encontrados, usando categoria principal");
        // Usar categoria principal como fallback
        const categoriaPrincipal = document.getElementById('categoriaPrincipal');
        if (categoriaPrincipal && categoriaPrincipal.value) {
          selectedCategories.clear();
          selectedCategories.add(categoriaPrincipal.value);
        }
        return;
      }

      container.innerHTML = '';
      const categoriesArray = Array.from(selectedCategories);
      categoriesInput.value = categoriesArray.join(',');

      categoriesArray.forEach(categoria => {
        const item = document.createElement('span');
        item.className = 'category-item';
        item.innerHTML = `
          ${categoria}
          <button type="button" onclick="removeCategory('${categoria}')">×</button>
        `;
        container.appendChild(item);
      });
      console.log("Categorias exibidas no container:", categoriesArray);
    }

    window.removeCategory = function(categoria) {
      selectedCategories.delete(categoria);
      console.log("Categoria removida:", categoria, "Set atual:", Array.from(selectedCategories));
      updateCategoriesDisplay();
    };

    window.editSupplier = function(supplierId) {
      const fornecedor = fornecedores.find(f => f.id === supplierId);
      if (fornecedor) {
        console.log("Fornecedor selecionado para edição:", fornecedor);
        document.getElementById('editingId').value = supplierId;
        document.getElementById('tipo').value = fornecedor.tipo || 'Fornecedor';
        document.getElementById('codigo').value = fornecedor.codigo;
        document.getElementById('categoriaPrincipal').value = fornecedor.categoriaPrincipal || '';
        document.getElementById('codigoClassificacao').value = fornecedor.codigoClassificacao || '';
        document.getElementById('codigoPais').value = fornecedor.codigoPais || '';
        document.getElementById('tipoPessoa').value = fornecedor.tipoPessoa || 'Juridica';
        updateCpfCnpjMask();
        document.getElementById('cnpjCpf').value = fornecedor.cnpjCpf;
        document.getElementById('razaoSocial').value = fornecedor.razaoSocial;
        document.getElementById('nomeFantasia').value = fornecedor.nomeFantasia;
        document.getElementById('inscricaoEstadual').value = fornecedor.inscricaoEstadual || '';
        document.getElementById('inscricaoMunicipal').value = fornecedor.inscricaoMunicipal || '';
        document.getElementById('nascimento').value = fornecedor.nascimento || '';
        document.getElementById('cep').value = fornecedor.cep || '';
        document.getElementById('endereco').value = fornecedor.endereco;
        document.getElementById('numero').value = fornecedor.numero || '';
        document.getElementById('complemento').value = fornecedor.complemento || '';
        document.getElementById('bairro').value = fornecedor.bairro || '';
        document.getElementById('cidade').value = fornecedor.cidade || '';
        document.getElementById('estado').value = fornecedor.estado || '';
        document.getElementById('pais').value = fornecedor.pais || 'Brasil';
        document.getElementById('email').value = fornecedor.email;
        document.getElementById('emailNfe').value = fornecedor.emailNfe || '';
        document.getElementById('telefone1').value = fornecedor.telefone1;
        document.getElementById('telefone2').value = fornecedor.telefone2 || '';
        document.getElementById('celular1').value = fornecedor.celular1 || '';
        document.getElementById('fax').value = fornecedor.fax || '';
        document.getElementById('contato1').value = fornecedor.contato1 || '';
        document.getElementById('cargo1').value = fornecedor.cargo1 || '';
        document.getElementById('departamento1').value = fornecedor.departamento1 || '';
        document.getElementById('telefone3').value = fornecedor.telefone3 || '';
        document.getElementById('celular2').value = fornecedor.celular2 || '';
        document.getElementById('email1').value = fornecedor.email1 || '';
        document.getElementById('autorizaXml1').value = fornecedor.autorizaXml1 ? 'true' : 'false';
        document.getElementById('contato2').value = fornecedor.contato2 || '';
        document.getElementById('cargo2').value = fornecedor.cargo2 || '';
        document.getElementById('departamento2').value = fornecedor.departamento2 || '';
        document.getElementById('telefone4').value = fornecedor.telefone4 || '';
        document.getElementById('celular3').value = fornecedor.celular3 || '';
        document.getElementById('email2').value = fornecedor.email2 || '';
        document.getElementById('autorizaXml2').value = fornecedor.autorizaXml2 ? 'true' : 'false';
        document.getElementById('cnpjCpf2').value = fornecedor.cnpjCpf2 || '';
        document.getElementById('cnpjCpf3').value = fornecedor.cnpjCpf3 || '';
        document.getElementById('codigoVendedor').value = fornecedor.codigoVendedor || '';
        document.getElementById('codigoRegiao').value = fornecedor.codigoRegiao || '';
        document.getElementById('codigoArea').value = fornecedor.codigoArea || '';
        document.getElementById('limite').value = fornecedor.limite || '';
        document.getElementById('indicacao').value = fornecedor.indicacao || '';
        document.getElementById('suframa').value = fornecedor.suframa || '';
        document.getElementById('im').value = fornecedor.im || '';
        document.getElementById('latitudeCLI').value = fornecedor.latitudeCLI || '';
        document.getElementById('longitudeCLI').value = fornecedor.longitudeCLI || '';
        document.getElementById('intervista').value = fornecedor.intervista || '';
        document.getElementById('acrescimoCLI').value = fornecedor.acrescimoCLI || '';
        document.getElementById('codCusto').value = fornecedor.codCusto || '';
        document.getElementById('cotacao').value = fornecedor.cotacao || '';
        document.getElementById('reducao').value = fornecedor.reducao || '';
        document.getElementById('contaContabil').value = fornecedor.contaContabil || '';
        document.getElementById('simplesNacional').value = fornecedor.simplesNacional ? 'true' : 'false';
        document.getElementById('temSubstituicao').value = fornecedor.temSubstituicao ? 'true' : 'false';
        document.getElementById('observacoes').value = fornecedor.observacoes || '';
        document.getElementById('statusHomologacao').value = fornecedor.statusHomologacao || 'Pendente';
        document.getElementById('ativo').value = fornecedor.ativo ? 'true' : 'false';
        document.getElementById('dataAtualizacao').value = fornecedor.dataAtualizacao && fornecedor.dataAtualizacao.seconds ? 
          new Date(fornecedor.dataAtualizacao.seconds * 1000).toLocaleDateString('pt-BR') : 
          new Date().toLocaleDateString('pt-BR');

        selectedCategories.clear();
        if (fornecedor.categorias && Array.isArray(fornecedor.categorias)) {
          fornecedor.categorias.forEach(categoria => selectedCategories.add(categoria));
        } else if (fornecedor.categoriaPrincipal) {
          selectedCategories.add(fornecedor.categoriaPrincipal);
        }
        updateCategoriesDisplay();

        // Carregar dados de transportadora se aplicável
        toggleTransportFields();
        if (fornecedor.categoriaPrincipal === 'TRANSPORTADORA' && fornecedor.dadosTransporte) {
          setTransportData(fornecedor.dadosTransporte);
        }

        document.getElementById('submitButton').textContent = 'Atualizar Parceiro';

        // Alternar para a aba de contatos para tornar campos obrigatórios visíveis
        switchTab('contatos');
        
        // Aguardar um momento e voltar para dados básicos
        setTimeout(() => {
          switchTab('basicos');
        }, 100);
      }
    };

    window.cancelEdit = function() {
      console.log("🔄 Cancelando edição e limpando formulário...");

      // Reset completo do formulário
      document.getElementById('supplierForm').reset();
      document.getElementById('editingId').value = '';

      // Limpar categorias selecionadas
      selectedCategories.clear();
      updateCategoriesDisplay();

      // Resetar campos específicos
      document.getElementById('submitButton').textContent = 'Cadastrar Parceiro';
      document.getElementById('tipoPessoa').value = 'Juridica';
      document.getElementById('ativo').value = 'true';
      document.getElementById('statusHomologacao').value = 'Pendente';
      document.getElementById('pais').value = 'Brasil';

      // Ocultar seção de transportadora
      const transportSection = document.getElementById('transportSection');
      if (transportSection) {
        transportSection.style.display = 'none';
      }

      // Voltar para a primeira aba
      switchTab('basicos');

      // Limpar máscaras
      updateCpfCnpjMask();

      console.log("✅ Formulário limpo com sucesso");
    };

    async function hasOpenDependencies(supplierId) {
      try {
        const quotesQuery = query(
          collection(db, "cotacoes"),
          where("fornecedorId", "==", supplierId),
          where("status", "==", "Aberta")
        );
        const quotesSnapshot = await getDocs(quotesQuery);

        const ordersQuery = query(
          collection(db, "pedidosCompra"),
          where("fornecedorId", "==", supplierId),
          where("status", "==", "Aberto")
        );
        const ordersSnapshot = await getDocs(ordersQuery);

        const requestsQuery = query(
          collection(db, "solicitacoesCompra"),
          where("fornecedorId", "==", supplierId),
          where("status", "==", "Aberta")
        );
        const requestsSnapshot = await getDocs(requestsQuery);

        return quotesSnapshot.size > 0 || ordersSnapshot.size > 0 || requestsSnapshot.size > 0;
      } catch (error) {
        console.error("Erro ao verificar dependências abertas:", error);
        throw error;
      }
    }

    window.deleteSupplier = async function(supplierId) {
      if (!confirm('Tem certeza que deseja excluir este parceiro?')) {
        return;
      }

      try {
        const hasDependencies = await hasOpenDependencies(supplierId);
        if (hasDependencies) {
          alert('Não é possível excluir este parceiro pois ele possui cotações, pedidos de compra ou solicitações de compra abertos.');
          return;
        }

        await deleteDoc(doc(db, "fornecedores", supplierId));
        await loadSuppliers();
        alert('Parceiro excluído com sucesso!');
      } catch (error) {
        console.error("Erro ao excluir parceiro:", error);
        alert("Erro ao excluir parceiro: " + error.message);
      }
    };

    window.filterSuppliers = function() {
        const searchText = document.getElementById('searchInput').value.toLowerCase().trim();
        const tipoFilter = document.getElementById('tipoFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const ativoFilter = document.getElementById('ativoFilter').value;

        // Verificar se o elemento categoriaFilter existe antes de tentar acessá-lo
        const categoriaFilterElement = document.getElementById('categoriaFilter');
        const categoriaFilter = categoriaFilterElement ? categoriaFilterElement.value : '';

        // Mostrar tabela sempre que houver busca ou dados
        const tableContainer = document.getElementById('tableContainer');
        if (tableContainer) {
          tableContainer.style.display = 'block';
          tableContainer.style.visibility = 'visible';
        }

        let filteredSuppliers = [...fornecedores]; // Criar cópia do array

        // Filtro de busca por texto (busca em tempo real)
        if (searchText) {
          filteredSuppliers = filteredSuppliers.filter(fornecedor => {
            const codigo = (fornecedor.codigo || '').toLowerCase();
            const razaoSocial = (fornecedor.razaoSocial || '').toLowerCase();
            const nomeFantasia = (fornecedor.nomeFantasia || '').toLowerCase();
            const cnpjCpf = (fornecedor.cnpjCpf || '').toLowerCase();
            const cidade = (fornecedor.cidade || '').toLowerCase();
            const email = (fornecedor.email || '').toLowerCase();

            return codigo.includes(searchText) ||
                   razaoSocial.includes(searchText) ||
                   nomeFantasia.includes(searchText) ||
                   cnpjCpf.includes(searchText) ||
                   cidade.includes(searchText) ||
                   email.includes(searchText);
          });
        }

        // Filtro por tipo
        if (tipoFilter) {
          filteredSuppliers = filteredSuppliers.filter(fornecedor => fornecedor.tipo === tipoFilter);
        }

        // Filtro por status de homologação
        if (statusFilter) {
          filteredSuppliers = filteredSuppliers.filter(fornecedor => fornecedor.statusHomologacao === statusFilter);
        }

        // Filtro por situação (ativo/inativo)
        if (ativoFilter !== '') {
          filteredSuppliers = filteredSuppliers.filter(fornecedor => fornecedor.ativo === (ativoFilter === 'true'));
        }

        // Filtro por categoria (se existir)
        if (categoriaFilter) {
          filteredSuppliers = filteredSuppliers.filter(fornecedor =>
            fornecedor.categoriaPrincipal === categoriaFilter
          );
        }

        // Ordenar por relevância (primeiro por código, depois por razão social)
        filteredSuppliers.sort((a, b) => {
          if (searchText) {
            const aCodigoMatch = (a.codigo || '').toLowerCase().includes(searchText);
            const bCodigoMatch = (b.codigo || '').toLowerCase().includes(searchText);

            if (aCodigoMatch && !bCodigoMatch) return -1;
            if (!aCodigoMatch && bCodigoMatch) return 1;
          }

          return (a.razaoSocial || '').localeCompare(b.razaoSocial || '');
        });

        // Limitar a 50 resultados para melhor performance
        filteredSuppliers = filteredSuppliers.slice(0, 50);

        // Exibir resultados
        displaySuppliers(filteredSuppliers);

        // Mostrar contador de resultados
        updateResultsCounter(filteredSuppliers.length, fornecedores.length);
      };

      // Função para atualizar contador de resultados
      function updateResultsCounter(filtered, total) {
        const tableTitle = document.querySelector('.table-title');
        if (tableTitle) {
          if (filtered === total) {
            tableTitle.innerHTML = `<i class="fas fa-table"></i> Parceiros Cadastrados (${total} total)`;
          } else {
            tableTitle.innerHTML = `<i class="fas fa-table"></i> Parceiros Cadastrados (${filtered} de ${total})`;
          }
        }
      }

      // Adiciona event listeners para filtros com verificação de existência
      const searchInput = document.getElementById('searchInput');
      const tipoFilter = document.getElementById('tipoFilter');
      const statusFilter = document.getElementById('statusFilter'); 
      const ativoFilter = document.getElementById('ativoFilter');
      
      if (searchInput) searchInput.addEventListener('input', filterSuppliers);
      if (tipoFilter) tipoFilter.addEventListener('change', filterSuppliers);
      if (statusFilter) statusFilter.addEventListener('change', filterSuppliers);
      if (ativoFilter) ativoFilter.addEventListener('change', filterSuppliers);

    // Funções dos atalhos de homologação
    window.goToHomologacao = function() {
      if (systemParams.homologacaoFornecedores) {
        window.location.href = 'PQ005-homologacao_fornecedores.html';
      } else {
        alert('⚠️ Módulo de Homologação não está ativo. Ative em Configurações > Parâmetros do Sistema.');
      }
    };

    window.goToDocumentos = function() {
      alert('📋 Funcionalidade de Gestão de Documentos será implementada em breve!');
      // window.location.href = 'gestao_documentos.html';
    };

    window.goToQualidade = function() {
      if (systemParams.moduloQualidadeAtivo) {
        // Mostrar menu de opções do módulo de qualidade
        showQualityMenu();
      } else {
        alert('⚠️ Módulo de Qualidade não está ativo. Ative em Configurações > Parâmetros do Sistema.');
      }
    };

    window.goToFinanceiro = function() {
      alert('💰 Funcionalidade de Análise Financeira será implementada em breve!');
      // window.location.href = 'analise_financeira.html';
    };

    // Menu do módulo de qualidade
    function showQualityMenu() {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
        align-items: center; justify-content: center;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2><i class="fas fa-search"></i> Módulo de Qualidade</h2>
            <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
          </div>

          <div style="display: grid; gap: 15px;">
            ${systemParams.inspecaoRecebimento ? `
              <button onclick="window.location.href='PQ001-inspecao_recebimento.html'; this.closest('.modal').remove();"
                      class="btn btn-primary" style="width: 100%; justify-content: flex-start;">
                <i class="fas fa-search"></i> PQ001 - Inspeção de Recebimento
              </button>
            ` : ''}

            ${systemParams.armazemQualidade ? `
              <button onclick="window.location.href='PCQ001-armazem_qualidade.html'; this.closest('.modal').remove();"
                      class="btn btn-primary" style="width: 100%; justify-content: flex-start;">
                <i class="fas fa-warehouse"></i> PCQ001 - Armazém de Qualidade
              </button>
            ` : ''}

            ${systemParams.metricasFornecedores ? `
              <button onclick="window.location.href='PQ006-metricas_fornecedores.html'; this.closest('.modal').remove();"
                      class="btn btn-primary" style="width: 100%; justify-content: flex-start;">
                <i class="fas fa-chart-line"></i> PQ006 - Métricas de Fornecedores
              </button>
            ` : ''}

            <button onclick="window.location.href='config_parametros.html'; this.closest('.modal').remove();"
                    class="btn btn-warning" style="width: 100%; justify-content: flex-start;">
              <i class="fas fa-cog"></i> Configurar Módulos de Qualidade
            </button>
          </div>
        </div>
      `;

      modal.className = 'modal';
      document.body.appendChild(modal);
    }

    window.toggleSummary = function() {
      const summary = document.getElementById('supplierSummary');
      if (summary.style.display === 'none') {
        summary.style.display = 'block';
      } else {
        summary.style.display = 'none';
      }
    };

    window.exportSuppliers = function() {
      const exportData = fornecedores.map(f => ({
        "Código": f.codigo,
        "Tipo": f.tipo,
        "Razão Social/Nome": f.razaoSocial,
        "Nome Fantasia": f.nomeFantasia,
        "Código Classificação": f.codigoClassificacao || '',
        "Código País": f.codigoPais || '',
        "Cidade": f.cidade || '',
        "Endereço": f.endereco,
        "Número": f.numero || '',
        "Complemento": f.complemento || '',
        "Bairro": f.bairro || '',
        "CEP": f.cep || '',
        "Estado": f.estado || '',
        "Telefone": f.telefone1,
        "Telefone 2": f.telefone2 || '',
        "Celular": f.celular1 || '',
        "Fax": f.fax || '',
        "Pessoa (Física/Jurídica)": f.tipoPessoa || 'Juridica',
        "CNPJ/CPF": f.cnpjCpf,
        "ID Inscrição Estadual": f.inscricaoEstadual || '',
        "Inscrição Estadual": f.inscricaoEstadual || '',
        "Nascimento": f.nascimento ? new Date(f.nascimento).toLocaleDateString('pt-BR') : '',
        "Última Compra": f.ultimaCompra ? new Date(f.ultimaCompra).toLocaleDateString('pt-BR') : '',
        "Código Vendedor": f.codigoVendedor || '',
        "Código Região": f.codigoRegiao || '',
        "Cliente Desde": f.dataCadastro ? new Date(f.dataCadastro).toLocaleDateString('pt-BR') : '',
        "Email": f.email,
        "Email NFe": f.emailNfe || '',
        "Home Page": f.homePage || '',
        "Tem Substituição": f.temSubstituicao ? 'Sim' : 'Não',
        "Código Área": f.codigoArea || '',
        "Simples Nacional?": f.simplesNacional ? 'Sim' : 'Não',
        "Limite": f.limite || '',
        "Contato 1": f.contato1 || '',
        "Departamento 1": f.departamento1 || '',
        "Telefone 1": f.telefone3 || '',
        "Celular 1": f.celular2 || '',
        "Email 1": f.email1 || '',
        "CNPJ/CPF 1": f.cnpjCpf2 || '',
        "Autoriza XML 1": f.autorizaXml1 ? 'Sim' : 'Não',
        "Pessoa (Física/Jurídica) 1": f.tipoPessoa || 'Juridica',
        "Contato 2": f.contato2 || '',
        "Contato 3": f.contato3 || '',
        "Cargo 2": f.cargo2 || '',
        "Cargo 3": f.cargo3 || '',
        "Departamento 2": f.departamento2 || '',
        "Departamento 3": f.departamento3 || '',
        "Telefone 2": f.telefone4 || '',
        "Telefone 3": f.telefone4 || '',
        "Celular 2": f.celular3 || '',
        "Celular 3": f.celular3 || '',
        "Email 2": f.email2 || '',
        "Email 3": f.email3 || '',
        "CNPJ/CPF 2": f.cnpjCpf2 || '',
        "CNPJ/CPF 3": f.cnpjCpf3 || '',
        "Autoriza XML 2": f.autorizaXml2 ? 'Sim' : 'Não',
        "Código do País": f.codigoPais || '',
        "SUFRAMA": f.suframa || '',
        "IM": f.im || '',
        "Indicação": f.indicacao || '',
        "Ativo": f.ativo ? 'Sim' : 'Não',
        "LatitudeCLI": f.latitudeCLI || '',
        "LongitudeCLI": f.longitudeCLI || '',
        "Intervista": f.intervista || '',
        "AcrescimoCLI": f.acrescimoCLI || '',
        "COD_CUSTO": f.codCusto || '',
        "COTACAO": f.cotacao || '',
        "COTACAO_2": f.cotacao || '',
        "REDUCAO": f.reducao || '',
        "CONTACONTABIL": f.contaContabil || '',
        "Status Homologação": f.statusHomologacao || 'Pendente',
        "Categorias": f.categorias ? f.categorias.join(',') : ''
      }));

      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Parceiros");

      const fileName = 'parceiros_' + new Date().toISOString().split('T')[0] + '.xlsx';
      XLSX.writeFile(workbook, fileName);
    };

    window.importSuppliers = async function(event) {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const reader = new FileReader();
        reader.onload = async function(e) {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array', cellDates: true });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const importedData = XLSX.utils.sheet_to_json(worksheet);

          const mappedData = importedData.map((row, index) => {
            let categorias = [];
            if (row['Categorias']) {
              const categoriasStr = row['Categorias'].toString().trim();
              categorias = categoriasStr ? categoriasStr.split(',').map(c => c.trim()) : ['SEM_CATEGORIA'];
            } else {
              categorias = ['SEM_CATEGORIA'];
            }

            const mapBoolean = (value) => {
              if (typeof value === 'string') {
                value = value.trim().toUpperCase();
                return value === 'SIM' || value === 'S';
              }
              return false;
            };

            return {
              tipo: row['Tipo'] || 'Fornecedor',
              codigo: row['Código']?.toString() || '',
              razaoSocial: row['Razão Social/Nome'] || '',
              nomeFantasia: row['Nome Fantasia'] || '',
              codigoClassificacao: row['Código Classificação']?.toString() || '',
              codigoPais: row['Código País']?.toString() || '',
              cidade: row['Cidade'] || '',
              endereco: row['Endereço'] || '',
              numero: row['Número']?.toString() || '',
              complemento: row['Complemento'] || '',
              bairro: row['Bairro'] || '',
              cep: row['CEP']?.toString() || '',
              estado: row['Estado'] || '',
              telefone1: row['Telefone']?.toString() || 'Não informado',
              telefone2: row['Telefone 2']?.toString() || '',
              celular1: row['Celular']?.toString() || '',
              fax: row['Fax']?.toString() || '',
              tipoPessoa: row['Pessoa (Física/Jurídica)'] || 'Juridica',
              cnpjCpf: row['CNPJ/CPF']?.toString() || '',
              inscricaoEstadual: row['Inscrição Estadual']?.toString() || '',
              nascimento: row['Nascimento'] ? new Date(row['Nascimento']) : null,
              ultimaCompra: row['Última Compra'] ? new Date(row['Última Compra']) : null,
              codigoVendedor: row['Código Vendedor']?.toString() || '',
              codigoRegiao: row['Código Região']?.toString() || '',
              dataCadastro: row['Cliente Desde'] ? new Date(row['Cliente Desde']) : new Date(),
              email: row['Email'] || '<EMAIL>',
              emailNfe: row['Email NFe'] || '',
              homePage: row['Home Page'] || '',
              temSubstituicao: mapBoolean(row['Tem Substituição']),
              codigoArea: row['Código Área']?.toString() || '',
              simplesNacional: mapBoolean(row['Simples Nacional?']),
              limite: row['Limite'] || 0,
              contato1: row['Contato 1'] || '',
              departamento1: row['Departamento 1'] || '',
              telefone3: row['Telefone 1']?.toString() || '',
              celular2: row['Celular 1']?.toString() || '',
              email1: row['Email 1'] || '',
              cnpjCpf2: row['CNPJ/CPF 1']?.toString() || '',
              autorizaXml1: mapBoolean(row['Autoriza XML 1']),
              contato2: row['Contato 2'] || '',
              contato3: '',
              cargo2: row['Cargo 2'] || '',
              cargo3: '',
              departamento2: row['Departamento 2'] || '',
              departamento3: '',
              telefone4: row['Telefone 2']?.toString() || '',
              celular3: row['Celular 2']?.toString() || '',
              email2: row['Email 2'] || '',
              email3: '',
              cnpjCpf3: row['CNPJ/CPF 3']?.toString() || '',
              autorizaXml2: mapBoolean(row['Autoriza XML 2']),
              suframa: row['SUFRAMA']?.toString() || '',
              im: row['IM']?.toString() || '',
              indicacao: row['Indicação'] || '',
              ativo: mapBoolean(row['Ativo']),
              latitudeCLI: row['LatitudeCLI']?.toString() || '',
              longitudeCLI: row['LongitudeCLI']?.toString() || '',
              intervista: row['Intervista']?.toString() || '',
              acrescimoCLI: row['AcrescimoCLI'] || 0,
              codCusto: row['COD_CUSTO']?.toString() || '',
              cotacao: row['COTACAO']?.toString() || '',
              reducao: row['REDUCAO'] || 0,
              contaContabil: row['CONTACONTABIL']?.toString() || '',
              categorias: categorias,
              observacoes: row['Observações'] || '',
              statusHomologacao: row['Status Homologação'] || 'Pendente',
              dataAtualizacao: row['Data Atualização'] ? new Date(row['Data Atualização']) : null,
              pais: row['País'] || 'Brasil'
            };
          });

          const requiredFields = ['codigo', 'tipo', 'tipoPessoa', 'cnpjCpf', 'razaoSocial', 'nomeFantasia', 'cep', 'endereco', 'bairro', 'cidade', 'estado', 'categorias', 'statusHomologacao'];
          const validationErrors = [];
          for (const [index, supplier] of mappedData.entries()) {
            for (const field of requiredFields) {
              if (!supplier[field] || (Array.isArray(supplier[field]) && supplier[field].length === 0)) {
                validationErrors.push(`Fornecedor inválido na linha ${index + 2}: campo "${field}" está vazio ou ausente`);
              }
            }
          }

          if (validationErrors.length > 0) {
            throw new Error(validationErrors.join('\n'));
          }

          if (!confirm(`Deseja importar ${mappedData.length} parceiros? Isso substituirá os dados existentes.`)) {
            return;
          }

          const fornecedoresRef = collection(db, "fornecedores");
          const snapshot = await getDocs(fornecedoresRef);
          const deletePromises = snapshot.docs.map(docSnapshot => deleteDoc(doc(fornecedoresRef, docSnapshot.id)));
          await Promise.all(deletePromises);

          const addPromises = mappedData.map(supplier => addDoc(fornecedoresRef, supplier));
          await Promise.all(addPromises);

          await loadSuppliers();
          alert('Dados importados com sucesso do Excel!');
          document.getElementById('importFile').value = '';
        };
        reader.readAsArrayBuffer(file);
      } catch (error) {
        console.error('Erro ao importar parceiros:', error);
        alert(`Erro ao importar dados do Excel: ${error.message}`);
      }
    };

    document.getElementById('supplierForm').addEventListener('submit', async function(event) {
      event.preventDefault();

      const statusHomologacao = document.getElementById('statusHomologacao').value;
      const configRef = doc(db, "parametros", "sistema");
      const configDoc = await getDoc(configRef);

      if (configDoc.exists() && configDoc.data().configuracaoSistema?.controleQualidade && statusHomologacao === 'Homologado') {
        const confirmManual = confirm('Atenção: Definir o status como "Homologado" manualmente pode ser sobrescrito por avaliações no sistema de homologação. Deseja continuar?');
        if (!confirmManual) {
          return;
        }
      }

      const categoriaPrincipal = document.getElementById('categoriaPrincipal').value;
      if (!categoriaPrincipal) {
        alert("Por favor, selecione uma categoria principal.");
        return;
      }

      const tipoPessoa = document.getElementById('tipoPessoa').value;
      const cnpjCpf = document.getElementById('cnpjCpf').value.replace(/\D/g, '');
      if (tipoPessoa === 'Juridica' && cnpjCpf.length !== 14) {
        alert("CNPJ inválido. Deve conter 14 dígitos.");
        return;
      } else if (tipoPessoa === 'Fisica' && cnpjCpf.length !== 11) {
        alert("CPF inválido. Deve conter 11 dígitos.");
        return;
      }

      const telefone1 = document.getElementById('telefone1').value.replace(/\D/g, '');
      if (telefone1.length < 10 || telefone1.length > 11) {
        alert("Telefone 1 inválido. Deve conter 10 ou 11 dígitos.");
        return;
      }

      const cep = document.getElementById('cep').value.replace(/\D/g, '');
      if (cep.length !== 8) {
        alert("CEP inválido. Deve conter 8 dígitos.");
        return;
      }

      const submitButton = document.getElementById('submitButton');
      submitButton.disabled = true;
      submitButton.textContent = 'Salvando...';

      let formData = {
        tipo: document.getElementById('tipo').value,
        codigo: document.getElementById('codigo').value,
        categoriaPrincipal: document.getElementById('categoriaPrincipal').value,
        razaoSocial: document.getElementById('razaoSocial').value,
        nomeFantasia: document.getElementById('nomeFantasia').value,
        codigoClassificacao: document.getElementById('codigoClassificacao').value,
        codigoPais: document.getElementById('codigoPais').value,
        cidade: document.getElementById('cidade').value,
        endereco: document.getElementById('endereco').value,
        numero: document.getElementById('numero').value,
        complemento: document.getElementById('complemento').value,
        bairro: document.getElementById('bairro').value,
        cep: document.getElementById('cep').value,
        estado: document.getElementById('estado').value,
        telefone1: document.getElementById('telefone1').value,
        telefone2: document.getElementById('telefone2').value,
        celular1: document.getElementById('celular1').value,
        fax: document.getElementById('fax').value,
        tipoPessoa: document.getElementById('tipoPessoa').value,
        cnpjCpf: document.getElementById('cnpjCpf').value,
        inscricaoEstadual: document.getElementById('inscricaoEstadual').value,
        inscricaoMunicipal: document.getElementById('inscricaoMunicipal').value,
        nascimento: document.getElementById('nascimento').value,
        ultimaCompra: null,
        codigoVendedor: '',
        codigoRegiao: '',
        email: document.getElementById('email').value,
        emailNfe: document.getElementById('emailNfe').value,
        homePage: document.getElementById('homePage').value,
        temSubstituicao: false,
        codigoArea: '',
        simplesNacional: false,
        limite: 0,
        contato1: document.getElementById('contato1').value,
        departamento1: document.getElementById('departamento1').value,
        telefone3: document.getElementById('telefone3').value,
        celular2: document.getElementById('celular2').value,
        email1: document.getElementById('email1').value,
        cnpjCpf2: '',
        autorizaXml1: document.getElementById('autorizaXml1').value === 'true',
        contato2: document.getElementById('contato2').value,
        contato3: '',
        cargo2: '',
        cargo3: '',
        departamento2: '',
        departamento3: '',
        telefone4: '',
        celular3: '',
        email2: '',
        email3: '',
        cnpjCpf3: '',
        autorizaXml2: document.getElementById('autorizaXml2').value === 'true',
        suframa: '',
        im: '',
        indicacao: '',
        ativo: document.getElementById('ativo').value === 'true',
        latitudeCLI: '',
        longitudeCLI: '',
        intervista: '',
        acrescimoCLI: 0,
        codCusto: '',
        cotacao: '',
        reducao: 0,
        contaContabil: '',
        categorias: [document.getElementById('categoriaPrincipal').value || 'SEM_CATEGORIA'],
        observacoes: '',
        statusHomologacao: document.getElementById('statusHomologacao').value,
        dataCadastro: new Date(),
        dataAtualizacao: new Date(),
        pais: document.getElementById('pais').value || 'Brasil',
      };

      // Adicionar dados de transportadora se aplicável
      const dadosTransporte = getTransportData();
      if (dadosTransporte) {
        formData.dadosTransporte = dadosTransporte;
      }

      console.log("Dados a serem salvos no Firestore:", formData);

      const editingId = document.getElementById('editingId').value;

      try {
        if (editingId) {
          console.log("🔄 Atualizando fornecedor existente...");
          await updateDoc(doc(db, "fornecedores", editingId), formData);
          console.log("✅ Fornecedor atualizado no Firebase");
          alert("Parceiro atualizado com sucesso!");
        } else {
          console.log("🔄 Verificando duplicatas...");
          const existingSupplier = fornecedores.find(f =>
            f.codigo === formData.codigo || f.cnpjCpf === formData.cnpjCpf
          );

          if (existingSupplier) {
            alert("Já existe um parceiro com este código ou CNPJ/CPF.");
            return;
          }

          console.log("🔄 Criando novo fornecedor...");
          await addDoc(collection(db, "fornecedores"), formData);
          console.log("✅ Fornecedor criado no Firebase");
          alert("Parceiro cadastrado com sucesso!");
        }

        // Forçar recarregamento dos dados (ignora cache)
        console.log("🔄 Recarregando lista de fornecedores...");
        await loadSuppliers(true);

        // Limpar formulário completamente
        event.target.reset();
        cancelEdit();

        // Scroll para o topo da lista para mostrar o novo/atualizado fornecedor
        const tableContainer = document.getElementById('tableContainer');
        if (tableContainer) {
          tableContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // Mostrar feedback visual de sucesso
        showSuccessMessage(editingId ? "Fornecedor atualizado com sucesso!" : "Fornecedor cadastrado com sucesso!");

        console.log("✅ Processo de salvamento concluído");

      } catch (error) {
        console.error("❌ Erro ao salvar parceiro:", error);
        alert("Erro ao salvar parceiro: " + error.message);
      } finally {
        submitButton.disabled = false;
        submitButton.textContent = editingId ? 'Atualizar Parceiro' : 'Cadastrar Parceiro';
      }
    });

    // Inicialização do sistema
    document.addEventListener('DOMContentLoaded', async function() {
      console.log("🚀 Inicializando sistema de cadastro de fornecedores...");

      try {
        // Carregar parâmetros do sistema primeiro
        await loadSystemParams();

        // Carregar fornecedores
        await loadSuppliers();

        // Configurar máscaras
        setupMasks();

        // Event listeners já foram configurados anteriormente

        console.log("✅ Sistema inicializado com sucesso!");
        console.log("📊 Parâmetros ativos:", {
          qualidade: systemParams.moduloQualidadeAtivo,
          homologacao: systemParams.homologacaoFornecedores,
          metricas: systemParams.metricasFornecedores
        });
      } catch (error) {
        console.error("❌ Erro na inicialização:", error);
        alert("Erro ao inicializar o sistema. Verifique o console para mais detalhes.");
      }
    });
  </script>
</body>
</html>