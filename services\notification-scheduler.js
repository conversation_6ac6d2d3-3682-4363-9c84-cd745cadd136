/**
 * NOTIFICATION SCHEDULER - SISTEMA NALITECK
 * Agendador de notificações automáticas
 */

import { db } from '../firebase-config.js';
import { collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

export class NotificationScheduler {
  static async checkNewInvoices() {
    try {
      console.log('🔍 Verificando novas faturas...');
      
      // Busca simples sem filtros complexos para evitar erro de índice
      const faturas = await getDocs(collection(db, "faturas"));
      const faturasData = faturas.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      // Filtra no cliente para evitar queries complexas
      const faturasNaoNotificadas = faturasData.filter(fatura => 
        !fatura.notificacaoEnviada && 
        fatura.dataEmissao
      );
      
      console.log(`📊 Encontradas ${faturasNaoNotificadas.length} faturas para notificar`);
      
      return faturasNaoNotificadas;
    } catch (error) {
      console.error('Erro ao verificar novas faturas:', error);
      return [];
    }
  }

  static async checkPendingApprovals() {
    try {
      console.log('🔍 Verificando aprovações pendentes...');
      
      const collections = ['orcamentos', 'pedidosCompra', 'solicitacoesCompra'];
      let totalPendentes = 0;
      
      for (const collectionName of collections) {
        try {
          const snapshot = await getDocs(collection(db, collectionName));
          const docs = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
          
          // Filtra no cliente
          const pendentes = docs.filter(doc => 
            doc.status && (
              doc.status.includes('Aguardando') || 
              doc.status.includes('Pendente') ||
              doc.status === 'Em Aprovação'
            )
          );
          
          totalPendentes += pendentes.length;
        } catch (error) {
          console.warn(`Coleção ${collectionName} não encontrada`);
        }
      }
      
      console.log(`📊 Total de aprovações pendentes: ${totalPendentes}`);
      return totalPendentes;
    } catch (error) {
      console.error('Erro ao verificar aprovações pendentes:', error);
      return 0;
    }
  }

  static async checkLowStock() {
    try {
      console.log('🔍 Verificando estoque baixo...');
      
      const produtos = await getDocs(collection(db, "produtos"));
      const estoques = await getDocs(collection(db, "estoques"));
      
      const produtosData = produtos.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const estoquesData = estoques.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      const produtosBaixoEstoque = [];
      
      produtosData.forEach(produto => {
        if (produto.controleEstoque && produto.estoqueMinimo) {
          const estoqueProduto = estoquesData.find(e => e.produtoId === produto.id);
          const saldoAtual = estoqueProduto ? estoqueProduto.saldo || 0 : 0;
          
          if (saldoAtual <= produto.estoqueMinimo) {
            produtosBaixoEstoque.push({
              produto: produto.codigo,
              saldoAtual,
              estoqueMinimo: produto.estoqueMinimo
            });
          }
        }
      });
      
      console.log(`📊 Produtos com estoque baixo: ${produtosBaixoEstoque.length}`);
      return produtosBaixoEstoque;
    } catch (error) {
      console.error('Erro ao verificar estoque baixo:', error);
      return [];
    }
  }

  static async runAllChecks() {
    try {
      console.log('🔄 Executando verificações automáticas...');
      
      const [faturas, aprovacoes, estoque] = await Promise.all([
        this.checkNewInvoices(),
        this.checkPendingApprovals(),
        this.checkLowStock()
      ]);
      
      return {
        faturas: faturas.length,
        aprovacoes,
        estoqueBaixo: estoque.length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Erro nas verificações automáticas:', error);
      return {
        faturas: 0,
        aprovacoes: 0,
        estoqueBaixo: 0,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
}