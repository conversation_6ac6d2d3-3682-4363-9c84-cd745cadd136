
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Consulta Ordens de Produção - Apenas O<PERSON></title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --text-secondary: #666;
      --success-color: #107e3e;
      --danger-color: #bb0000;
    }
    
    * { box-sizing: border-box; margin: 0; padding: 0; }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f7f7f7;
      color: var(--text-color);
      margin: 0;
      padding: 20px;
    }
    
    .container {
      width: 95%;
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      padding: 24px;
    }
    
    h1 {
      color: var(--primary-color);
      font-size: 26px;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .filters {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      flex-wrap: wrap;
      align-items: center;
    }
    
    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    
    label {
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 14px;
    }
    
    input[type="text"], select {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }
    
    button {
      background-color: var(--success-color);
      color: #fff;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      transition: background 0.2s;
    }
    
    button:hover {
      background-color: #0d6e36;
    }
    
    .ops-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    
    .ops-table th,
    .ops-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }
    
    .ops-table th {
      background-color: var(--secondary-color);
      font-weight: 600;
      color: var(--primary-color);
    }
    
    .ops-table tr:hover {
      background-color: #f9f9f9;
    }
    
    .status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status.pendente {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .status.em-producao {
      background-color: #d1ecf1;
      color: #0c5460;
    }
    
    .status.finalizada {
      background-color: #d4edda;
      color: #155724;
    }
    
    .loading {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
    }
    
    .no-results {
      text-align: center;
      padding: 40px;
      color: var(--text-secondary);
      font-style: italic;
    }
    
    .error {
      color: var(--danger-color);
      font-weight: bold;
      text-align: center;
      padding: 20px;
    }
    
    @media (max-width: 768px) {
      .filters {
        flex-direction: column;
        align-items: stretch;
      }
      
      .ops-table {
        font-size: 12px;
      }
      
      .ops-table th,
      .ops-table td {
        padding: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Lista de Ordens de Produção - OPs Pai</h1>
    
    <div class="filters">
      <div class="filter-group">
        <label>Código do Produto:</label>
        <input type="text" id="codigoProduto" placeholder="Digite o código...">
      </div>
      
      <div class="filter-group">
        <label>Número da OP:</label>
        <input type="text" id="numeroOP" placeholder="Digite o número...">
      </div>
      
      <div class="filter-group">
        <label>Status:</label>
        <select id="statusFilter">
          <option value="">Todos</option>
          <option value="PENDENTE">Pendente</option>
          <option value="EM_PRODUCAO">Em Produção</option>
          <option value="FINALIZADA">Finalizada</option>
        </select>
      </div>
      
      <button onclick="filtrarOPs()">Filtrar</button>
      <button onclick="limparFiltros()">Limpar</button>
    </div>
    
    <div id="resultado">
      <div class="loading">Carregando todas as OPs pai...</div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensProducao = [];
    let produtos = [];

    // Carregar dados ao inicializar
    document.addEventListener('DOMContentLoaded', async function() {
      console.log('Iniciando carregamento das OPs pai...');
      await carregarDados();
      console.log('Dados carregados, exibindo OPs pai...');
      exibirOPs(ordensProducao);
    });

    // Também carregar quando a página terminar de carregar
    window.addEventListener('load', async function() {
      if (ordensProducao.length === 0) {
        console.log('Carregamento adicional das OPs pai...');
        await carregarDados();
        exibirOPs(ordensProducao);
      }
    });

    async function carregarDados() {
      try {
        console.log('Carregando dados...');
        
        // Carregar produtos
        const produtosSnap = await getDocs(collection(db, 'produtos'));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Carregar apenas OPs pai (sem opPaiId ou nivel = 0)
        const ordensSnap = await getDocs(
          query(
            collection(db, 'ordensProducao'),
            orderBy('dataCriacao', 'desc')
          )
        );
        
        // Filtrar apenas OPs pai com status ativos (excluir Concluída e Cancelada)
        ordensProducao = ordensSnap.docs
          .map(doc => ({ id: doc.id, ...doc.data() }))
          .filter(op => {
            // Deve ser OP pai (nível 0 e sem pai)
            const isOpPai = !op.opPaiId && (op.nivel === 0 || op.nivel === undefined);
            
            // Deve ter status ativo (não concluída nem cancelada)
            const statusAtivo = !op.status || 
              !['CONCLUIDA', 'CONCLUÍDA', 'CANCELADA', 'FINALIZADA'].includes(op.status.toUpperCase());
            
            return isOpPai && statusAtivo;
          });
        
        console.log(`Carregadas ${ordensProducao.length} OPs pai`);
        
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        document.getElementById('resultado').innerHTML = 
          '<div class="error">Erro ao carregar dados. Verifique a conexão.</div>';
      }
    }

    function exibirOPs(ops) {
      const resultado = document.getElementById('resultado');
      
      if (ops.length === 0) {
        resultado.innerHTML = '<div class="no-results">Nenhuma OP pai encontrada no sistema</div>';
        return;
      }

      let html = `
        <div style="margin-bottom: 15px; padding: 10px; background-color: #e9ecef; border-radius: 5px;">
          <strong>Total de OPs Pai encontradas: ${ops.length}</strong>
        </div>
        <table class="ops-table">
          <thead>
            <tr>
              <th>Número OP</th>
              <th>Código Produto</th>
              <th>Descrição</th>
              <th>Quantidade</th>
              <th>Status</th>
              <th>Data Criação</th>
              <th>Data Entrega</th>
              <th>Qtd. Produzida</th>
            </tr>
          </thead>
          <tbody>
      `;

      ops.forEach(op => {
        const produto = produtos.find(p => p.id === op.produtoId);
        const dataCriacao = op.dataCriacao ? 
          new Date(op.dataCriacao.seconds * 1000).toLocaleDateString('pt-BR') : '-';
        const dataEntrega = op.dataEntrega ? 
          new Date(op.dataEntrega.seconds * 1000).toLocaleDateString('pt-BR') : '-';
        
        html += `
          <tr>
            <td><strong>${op.numero || '-'}</strong></td>
            <td>${produto?.codigo || '-'}</td>
            <td>${produto?.descricao || '-'}</td>
            <td>${op.quantidade || 0}</td>
            <td><span class="status ${(op.status || 'pendente').toLowerCase().replace('_', '-')}">${formatarStatus(op.status)}</span></td>
            <td>${dataCriacao}</td>
            <td>${dataEntrega}</td>
            <td>${op.quantidadeProduzida || 0}</td>
          </tr>
        `;
      });

      html += '</tbody></table>';
      resultado.innerHTML = html;
    }

    function formatarStatus(status) {
      const statusMap = {
        'PENDENTE': 'Pendente',
        'EM_PRODUCAO': 'Em Produção',
        'FINALIZADA': 'Finalizada'
      };
      return statusMap[status] || status || 'Pendente';
    }

    window.filtrarOPs = function() {
      const codigoProduto = document.getElementById('codigoProduto').value.toLowerCase().trim();
      const numeroOP = document.getElementById('numeroOP').value.toLowerCase().trim();
      const statusFilter = document.getElementById('statusFilter').value;

      let opsFiltradas = [...ordensProducao];

      if (codigoProduto) {
        opsFiltradas = opsFiltradas.filter(op => {
          const produto = produtos.find(p => p.id === op.produtoId);
          return produto?.codigo?.toLowerCase().includes(codigoProduto);
        });
      }

      if (numeroOP) {
        opsFiltradas = opsFiltradas.filter(op => 
          op.numero?.toLowerCase().includes(numeroOP)
        );
      }

      if (statusFilter) {
        opsFiltradas = opsFiltradas.filter(op => op.status === statusFilter);
      }

      exibirOPs(opsFiltradas);
    };

    window.limparFiltros = function() {
      document.getElementById('codigoProduto').value = '';
      document.getElementById('numeroOP').value = '';
      document.getElementById('statusFilter').value = '';
      exibirOPs(ordensProducao);
    };
  </script>
</body>
</html>
