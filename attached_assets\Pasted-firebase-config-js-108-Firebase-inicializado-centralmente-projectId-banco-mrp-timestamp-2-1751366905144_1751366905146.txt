firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T10:43:57.893Z', apps: 1}
recebimento_materiais_avancado.html:1051 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1127 Dados carregados: {pedidos: 39, produtos: 1668, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1137 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', atualizacoesEntrega: Array(1), criadoPor: 'Alex', condicaoPagamento: '60DIAS', status: 'RECEBIDO', …}
recebimento_materiais_avancado.html:1139 📦 Exemplo de item: {produtoId: '1sHECTgPNMjBw0ScLbbq', descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', codigo: '110732', quantidade: 1, unidade: 'PC', …}
recebimento_materiais_avancado.html:1143 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', armazemPadraoId: 'BtRauPc2d0XyLfeBOZFj', origem: '0', inspecaoRecebimento: 'nao', dataCadastro: {…}, …}
recebimento_materiais_avancado.html:1146 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf2: '', email1: '', tipoPessoa: '', temSubstituicao: false, …}
recebimento_materiais_avancado.html:957 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
recebimento_materiais_avancado.html:958 📋 Pedidos de Compra: 39
recebimento_materiais_avancado.html:959 🏷️ Produtos: 1668
recebimento_materiais_avancado.html:960 🏢 Fornecedores: 778
recebimento_materiais_avancado.html:961 🏪 Armazéns: 9
recebimento_materiais_avancado.html:965 📋 Estrutura do pedido: (22) ['id', 'atualizacoesEntrega', 'criadoPor', 'condicaoPagamento', 'status', 'numeroAnterior', 'numero', 'dataCriacao', 'valorTotal', 'solicitacaoId', 'itens', 'prazoEntrega', 'limpoPor', 'sincronizadoPor', 'fornecedorId', 'dataAprovacao', 'aprovadoPor', 'alteradoPor', 'cotacaoId', 'ultimaAtualizacao', 'ultimaLimpezaCritica', 'historico']
recebimento_materiais_avancado.html:966 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', atualizacoesEntrega: Array(1), criadoPor: 'Alex', condicaoPagamento: '60DIAS', status: 'RECEBIDO', …}
recebimento_materiais_avancado.html:969 📦 Estrutura do item: (7) ['produtoId', 'descricao', 'codigo', 'quantidade', 'unidade', 'valorTotal', 'valorUnitario']
recebimento_materiais_avancado.html:970 📦 Item exemplo: {produtoId: '1sHECTgPNMjBw0ScLbbq', descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', codigo: '110732', quantidade: 1, unidade: 'PC', …}
recebimento_materiais_avancado.html:975 🏢 Estrutura do fornecedor: (71) ['id', 'cnpjCpf2', 'email1', 'tipoPessoa', 'temSubstituicao', 'cep', 'codigoPais', 'codigo', 'celular1', 'celular2', 'codigoVendedor', 'nomeFantasia', 'razaoSocial', 'departamento3', 'homePage', 'contaContabil', 'telefone1', 'estado', 'reducao', 'telefone4', 'celular3', 'telefone3', 'statusHomologacao', 'inscricaoMunicipal', 'im', 'cidade', 'email3', 'codCusto', 'ativo', 'acrescimoCLI', 'email2', 'codigoRegiao', 'limite', 'contato1', 'cargo2', 'autorizaXml1', 'cargo3', 'codigoClassificacao', 'simplesNacional', 'inscricaoEstadual', 'endereco', 'ultimaCompra', 'categoriaPrincipal', 'cnpjCpf', 'dataAtualizacao', 'nascimento', 'cnpjCpf3', 'contato2', 'categorias', 'autorizaXml2', 'longitudeCLI', 'numero', 'contato3', 'latitudeCLI', 'intervista', 'departamento1', 'pais', 'dataCadastro', 'tipo', 'cotacao', 'fax', 'observacoes', 'emailNfe', 'suframa', 'codigoArea', 'email', 'departamento2', 'bairro', 'complemento', 'indicacao', 'telefone2']
recebimento_materiais_avancado.html:976 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', cnpjCpf2: '', email1: '', tipoPessoa: '', temSubstituicao: false, …}
recebimento_materiais_avancado.html:980 🏷️ Estrutura do produto: (31) ['id', 'armazemPadraoId', 'origem', 'inspecaoRecebimento', 'dataCadastro', 'ncm', 'fatorConversao', 'status', 'ultimoCusto', 'tipoItem', 'corredor', 'precoVenda', 'metodoCusteio', 'familia', 'margemLucro', 'descricao', 'unidade', 'tipo', 'cest', 'estoqueMaximo', 'rastreabilidadeLote', 'posicao', 'pontoPedido', 'loteCompra', 'centroCustoObrigatorio', 'unidadeSecundaria', 'codigo', 'custoMedio', 'estoqueMinimo', 'prateleira', 'grupo']
recebimento_materiais_avancado.html:981 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', armazemPadraoId: 'BtRauPc2d0XyLfeBOZFj', origem: '0', inspecaoRecebimento: 'nao', dataCadastro: {…}, …}
recebimento_materiais_avancado.html:1319 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
recebimento_materiais_avancado.html:1320 📋 DEBUG populateOrderSelect - Total de pedidos: 39
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'APROVADO', temItens: 2}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'APROVADO', temItens: 5}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1368 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1395 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1298 📊 Dashboard atualizado: {pendentes: 15, atrasados: 0, parciais: 0, completos: 22}
recebimento_materiais_avancado.html:1570 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1510 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1519 🔍 DEBUG selectOrderById - currentOrder encontrado: {id: 'STenDi3OPZy4ieeJIrxk', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', valorTotal: 1191, itens: Array(2), cotacaoNumero: '000063', …}
recebimento_materiais_avancado.html:1527 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1587 🔍 DEBUG loadSupplierInfo - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', valorTotal: 1191, itens: Array(2), cotacaoNumero: '000063', …}
recebimento_materiais_avancado.html:1588 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1589 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1592 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1593 📋 Campos do currentOrder: (22) ['id', 'fornecedorNome', 'valorTotal', 'itens', 'cotacaoNumero', 'fornecedorId', 'observacoes', 'status', 'dataAprovacao', 'numero', 'dataRecebimento', 'criadoPor', 'dataUltimaAtualizacao', 'historico', 'dataCriacao', 'ultimoRecebimento', 'uidAprovacao', 'recebidoPor', 'prazoEntrega', 'cotacaoId', 'condicoesPagamento', 'aprovadoPor']
recebimento_materiais_avancado.html:1594 📄 Dados do fornecedor no pedido: {fornecedorId: 'YqfHIA1xCUe30ndFcIAj', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', fornecedorCnpj: undefined, fornecedorDocumento: undefined, fornecedorContato: undefined, …}
recebimento_materiais_avancado.html:1626 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: {id: 'YqfHIA1xCUe30ndFcIAj', email: 'teste@teste', intervista: '0', temSubstituicao: false, email1: '', …}
recebimento_materiais_avancado.html:1630 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1631 📋 Campos disponíveis: (70) ['id', 'email', 'intervista', 'temSubstituicao', 'email1', 'simplesNacional', 'codigoRegiao', 'homePage', 'cnpjCpf', 'cargo3', 'nascimento', 'codigoClassificacao', 'razaoSocial', 'codigo', 'complemento', 'pais', 'reducao', 'latitudeCLI', 'departamento1', 'observacoes', 'celular3', 'tipo', 'autorizaXml2', 'email2', 'autorizaXml1', 'telefone2', 'cargo2', 'inscricaoEstadual', 'cidade', 'departamento2', 'statusHomologacao', 'cnpjCpf2', 'limite', 'bairro', 'cep', 'departamento3', 'endereco', 'cnpjCpf3', 'cotacao', 'telefone3', 'estado', 'inscricaoMunicipal', 'contaContabil', 'telefone4', 'suframa', 'longitudeCLI', 'ativo', 'tipoPessoa', 'dataAtualizacao', 'celular2', 'nomeFantasia', 'contato2', 'fax', 'emailNfe', 'contato1', 'codigoVendedor', 'codigoArea', 'indicacao', 'celular1', 'numero', 'dataCadastro', 'telefone1', 'acrescimoCLI', 'categorias', 'contato3', 'email3', 'codigoPais', 'im', 'codCusto', 'ultimaCompra']
recebimento_materiais_avancado.html:1632 📄 CNPJ/CPF campos: {cnpj: undefined, cpfCnpj: undefined, cnpjCpf: '07.686.277/0001-69', documento: undefined, cpf: undefined}
recebimento_materiais_avancado.html:1653 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:949 🔍 DEBUG extrairCnpjCpf - Tentativas: (17) [undefined, undefined, '07.686.277/0001-69', undefined, undefined, '07.686.277/0001-69', undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined]
recebimento_materiais_avancado.html:950 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1691 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1692 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1693 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1710 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1711 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1712 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1715 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: {fornecedor?.cnpj: undefined, fornecedor?.cpfCnpj: undefined, fornecedor?.cnpjCpf: '07.686.277/0001-69', fornecedor?.documento: undefined, fornecedor?.cpf: undefined, …}
recebimento_materiais_avancado.html:1540 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1849 🔍 DEBUG loadOrderItems - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', valorTotal: 1191, itens: Array(2), cotacaoNumero: '000063', …}
recebimento_materiais_avancado.html:1850 🔍 DEBUG loadOrderItems - itens: (2) [{…}, {…}]
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 0: {icms: 0, quantidade: 2, precoUnitario: 397, descricao: 'CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM', ipi: 0, …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: {id: '94M0qmNlWSZt9opark0E', estoqueMinimo: 0, descricao: 'CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM', unidade: 'PC', leadTime: 0, …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 1: {quantidade: 1, icms: 0, descricao: 'CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM', ipi: 0, codigo: '105240', …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: {id: 'UPqNBsHPmH3CAwc38Hrp', descricao: 'CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM', codigo: '105240', unidade: 'PC', dataCadastro: {…}, …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2166 Erro ao carregar histórico: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2166
await in loadDeliveryHistory
selectOrderById @ recebimento_materiais_avancado.html:1556
handleMouseUp_ @ unknown
await in handleMouseUp_
window.selectOrder @ recebimento_materiais_avancado.html:1583
onchange @ recebimento_materiais_avancado.html:599
handleMouseUp_ @ unknown
recebimento_materiais_avancado.html:2177 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2186 Dados encontrados: {movimentacoes: 3489, estoqueQualidade: 63, recebimentosDetalhes: 16}
recebimento_materiais_avancado.html:2301 Total de registros de histórico encontrados: 99
