
/**
 * SCRIPT DE NORMALIZAÇÃO DE CÓDIGOS DE PRODUTOS
 * Corrige inconsistências de case nos códigos de produtos
 */

import { db } from '../firebase-config.js';
import {
    collection,
    getDocs,
    updateDoc,
    doc,
    runTransaction
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

class CodigoNormalizationService {
    
    /**
     * 🔧 Normalizar código de produto
     */
    static normalizeCodigo(codigo) {
        if (!codigo || typeof codigo !== 'string') return '';
        return codigo.trim().toUpperCase();
    }
    
    /**
     * 📊 Analisar inconsistências nos códigos
     */
    static async analisarInconsistencias() {
        console.log('🔍 Iniciando análise de inconsistências...');
        
        try {
            const [
                produtosSnap,
                solicitacoesSnap,
                cotacoesSnap,
                pedidosSnap,
                movimentacoesSnap
            ] = await Promise.all([
                getDocs(collection(db, 'produtos')),
                getDocs(collection(db, 'solicitacoesCompra')),
                getDocs(collection(db, 'cotacoes')),
                getDocs(collection(db, 'pedidosCompra')),
                getDocs(collection(db, 'movimentacoesEstoque'))
            ]);

            const produtos = produtosSnap.docs.map(doc => ({ 
                id: doc.id, 
                ...doc.data() 
            }));

            const codigosProdutos = new Set(produtos.map(p => this.normalizeCodigo(p.codigo)));
            
            console.log(`📦 Total de produtos: ${produtos.length}`);
            console.log(`🏷️ Códigos únicos: ${codigosProdutos.size}`);
            
            const relatorio = {
                produtos: {
                    total: produtos.length,
                    comCase: produtos.filter(p => p.codigo !== p.codigo.toUpperCase()).length,
                    duplicados: produtos.length - codigosProdutos.size
                },
                solicitacoes: { inconsistencias: [] },
                cotacoes: { inconsistencias: [] },
                pedidos: { inconsistencias: [] },
                movimentacoes: { inconsistencias: [] }
            };

            // Analisar solicitações
            solicitacoesSnap.docs.forEach(doc => {
                const solicitacao = doc.data();
                if (solicitacao.itens) {
                    solicitacao.itens.forEach((item, index) => {
                        const codigoNormalizado = this.normalizeCodigo(item.codigo);
                        if (!codigosProdutos.has(codigoNormalizado)) {
                            relatorio.solicitacoes.inconsistencias.push({
                                docId: doc.id,
                                itemIndex: index,
                                codigoOriginal: item.codigo,
                                codigoNormalizado
                            });
                        }
                    });
                }
            });

            // Analisar cotações
            cotacoesSnap.docs.forEach(doc => {
                const cotacao = doc.data();
                if (cotacao.itens) {
                    cotacao.itens.forEach((item, index) => {
                        const codigoNormalizado = this.normalizeCodigo(item.codigo);
                        if (!codigosProdutos.has(codigoNormalizado)) {
                            relatorio.cotacoes.inconsistencias.push({
                                docId: doc.id,
                                itemIndex: index,
                                codigoOriginal: item.codigo,
                                codigoNormalizado
                            });
                        }
                    });
                }
            });

            // Analisar pedidos
            pedidosSnap.docs.forEach(doc => {
                const pedido = doc.data();
                if (pedido.itens) {
                    pedido.itens.forEach((item, index) => {
                        const codigoNormalizado = this.normalizeCodigo(item.codigo);
                        if (!codigosProdutos.has(codigoNormalizado)) {
                            relatorio.pedidos.inconsistencias.push({
                                docId: doc.id,
                                itemIndex: index,
                                codigoOriginal: item.codigo,
                                codigoNormalizado
                            });
                        }
                    });
                }
            });

            // Analisar movimentações
            movimentacoesSnap.docs.forEach(doc => {
                const mov = doc.data();
                const codigoNormalizado = this.normalizeCodigo(mov.codigoProduto || '');
                if (mov.codigoProduto && !codigosProdutos.has(codigoNormalizado)) {
                    relatorio.movimentacoes.inconsistencias.push({
                        docId: doc.id,
                        codigoOriginal: mov.codigoProduto,
                        codigoNormalizado
                    });
                }
            });

            return relatorio;
            
        } catch (error) {
            console.error('❌ Erro na análise:', error);
            throw error;
        }
    }
    
    /**
     * 🔧 Normalizar códigos de produtos
     */
    static async normalizarProdutos() {
        console.log('🔧 Normalizando códigos de produtos...');
        
        try {
            const produtosSnap = await getDocs(collection(db, 'produtos'));
            let atualizados = 0;
            
            for (const docSnap of produtosSnap.docs) {
                const produto = docSnap.data();
                const codigoNormalizado = this.normalizeCodigo(produto.codigo);
                
                if (produto.codigo !== codigoNormalizado) {
                    await updateDoc(doc(db, 'produtos', docSnap.id), {
                        codigo: codigoNormalizado
                    });
                    
                    console.log(`✅ Produto normalizado: ${produto.codigo} → ${codigoNormalizado}`);
                    atualizados++;
                }
            }
            
            console.log(`📊 Produtos atualizados: ${atualizados}`);
            return atualizados;
            
        } catch (error) {
            console.error('❌ Erro na normalização:', error);
            throw error;
        }
    }
    
    /**
     * 🔧 Corrigir códigos em solicitações
     */
    static async corrigirSolicitacoes(inconsistencias) {
        console.log('🔧 Corrigindo códigos em solicitações...');
        
        const produtosSnap = await getDocs(collection(db, 'produtos'));
        const mapaProdutos = new Map();
        
        produtosSnap.docs.forEach(doc => {
            const produto = doc.data();
            mapaProdutos.set(this.normalizeCodigo(produto.codigo), produto);
        });
        
        let corrigidos = 0;
        
        for (const inconsistencia of inconsistencias) {
            try {
                const solicitacaoRef = doc(db, 'solicitacoesCompra', inconsistencia.docId);
                const solicitacaoDoc = await solicitacaoRef.get();
                
                if (solicitacaoDoc.exists()) {
                    const solicitacao = solicitacaoDoc.data();
                    const itensCorrigidos = [...solicitacao.itens];
                    
                    const produto = mapaProdutos.get(inconsistencia.codigoNormalizado);
                    
                    if (produto) {
                        itensCorrigidos[inconsistencia.itemIndex] = {
                            ...itensCorrigidos[inconsistencia.itemIndex],
                            codigo: produto.codigo,
                            descricao: produto.descricao
                        };
                        
                        await updateDoc(solicitacaoRef, {
                            itens: itensCorrigidos
                        });
                        
                        console.log(`✅ Solicitação corrigida: ${inconsistencia.docId}`);
                        corrigidos++;
                    }
                }
            } catch (error) {
                console.error(`❌ Erro ao corrigir solicitação ${inconsistencia.docId}:`, error);
            }
        }
        
        console.log(`📊 Solicitações corrigidas: ${corrigidos}`);
        return corrigidos;
    }
    
    /**
     * 🎯 Executar normalização completa
     */
    static async executarNormalizacaoCompleta() {
        console.log('🚀 Iniciando normalização completa do sistema...');
        
        try {
            // 1. Analisar inconsistências
            const relatorio = await this.analisarInconsistencias();
            
            console.log('📊 RELATÓRIO DE INCONSISTÊNCIAS:');
            console.log('- Produtos com case incorreto:', relatorio.produtos.comCase);
            console.log('- Solicitações com problemas:', relatorio.solicitacoes.inconsistencias.length);
            console.log('- Cotações com problemas:', relatorio.cotacoes.inconsistencias.length);
            console.log('- Pedidos com problemas:', relatorio.pedidos.inconsistencias.length);
            console.log('- Movimentações com problemas:', relatorio.movimentacoes.inconsistencias.length);
            
            // 2. Normalizar produtos
            const produtosAtualizados = await this.normalizarProdutos();
            
            // 3. Corrigir solicitações
            const solicitacoesCorrigidas = await this.corrigirSolicitacoes(
                relatorio.solicitacoes.inconsistencias
            );
            
            const resultado = {
                produtosAtualizados,
                solicitacoesCorrigidas,
                relatorio
            };
            
            console.log('✅ NORMALIZAÇÃO CONCLUÍDA:');
            console.log(`- Produtos normalizados: ${produtosAtualizados}`);
            console.log(`- Solicitações corrigidas: ${solicitacoesCorrigidas}`);
            
            return resultado;
            
        } catch (error) {
            console.error('❌ Erro na normalização completa:', error);
            throw error;
        }
    }
}

// 🚀 Executar quando o script for carregado
window.CodigoNormalizationService = CodigoNormalizationService;

// Função para executar via console
window.executarNormalizacao = () => {
    return CodigoNormalizationService.executarNormalizacaoCompleta();
};

console.log('📋 Script de normalização carregado. Execute: executarNormalizacao()');
