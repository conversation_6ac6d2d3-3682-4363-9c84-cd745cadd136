<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Serviços - Sistema TOTVS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --totvs-primary: #0854a0;
            --totvs-secondary: #f0f3f6;
            --totvs-success: #107e3e;
            --totvs-warning: #e9730c;
            --totvs-danger: #bb0000;
            --totvs-border: #d4d4d4;
            --totvs-text: #333;
            --totvs-bg: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--totvs-bg);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--totvs-primary) 0%, #0a4d8c 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 30px;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-section h3 {
            color: var(--totvs-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            border-bottom: 2px solid var(--totvs-secondary);
            padding-bottom: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--totvs-text);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-group label.required::after {
            content: " *";
            color: var(--totvs-danger);
        }

        .form-control {
            padding: 12px;
            border: 2px solid var(--totvs-border);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--totvs-primary);
            box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--totvs-primary);
            color: white;
        }

        .btn-success {
            background: var(--totvs-success);
            color: white;
        }

        .btn-warning {
            background: var(--totvs-warning);
            color: white;
        }

        .btn-danger {
            background: var(--totvs-danger);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--totvs-border);
        }

        .table th {
            background: var(--totvs-secondary);
            font-weight: 600;
            color: var(--totvs-text);
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-ativo {
            background: var(--totvs-success);
            color: white;
        }

        .status-inativo {
            background: var(--totvs-danger);
            color: white;
        }

        .tipo-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .tipo-fixo {
            background: #17a2b8;
            color: white;
        }

        .tipo-hora {
            background: #ffc107;
            color: #000;
        }

        .tipo-projeto {
            background: #6f42c1;
            color: white;
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 12px;
            border: 2px solid var(--totvs-border);
            border-radius: 6px;
            font-size: 14px;
        }

        .search-box i {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: var(--totvs-primary);
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--totvs-border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            opacity: 0.7;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--totvs-success);
        }

        .notification-error {
            background: var(--totvs-danger);
        }

        .notification-info {
            background: var(--totvs-primary);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--totvs-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--totvs-border);
        }

        .currency-input {
            position: relative;
        }

        .currency-input::before {
            content: "R$";
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 600;
        }

        .currency-input input {
            padding-left: 35px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-tools"></i>
                Cadastro de Serviços
            </h1>
            <div>
                <button class="btn btn-success" onclick="openModal()">
                    <i class="fas fa-plus"></i> Novo Serviço
                </button>
                <a href="index.html" class="btn btn-warning">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h3><i class="fas fa-list"></i> Lista de Serviços</h3>
                
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="Buscar por código, descrição ou categoria..." onkeyup="searchServices()">
                    <i class="fas fa-search"></i>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Descrição</th>
                            <th>Categoria</th>
                            <th>Tipo Cobrança</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="servicesTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px;">
                                Carregando serviços...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="serviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">
                    <i class="fas fa-tools"></i> Novo Serviço
                </h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="serviceForm" onsubmit="saveService(event)">
                <div class="modal-body">
                    <!-- Dados Básicos -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> Dados Básicos</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="required"><i class="fas fa-barcode"></i> Código</label>
                                <input type="text" id="codigo" class="form-control" required maxlength="20">
                            </div>
                            <div class="form-group">
                                <label class="required"><i class="fas fa-tag"></i> Descrição</label>
                                <input type="text" id="descricao" class="form-control" required maxlength="100">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-folder"></i> Categoria</label>
                                <select id="categoria" class="form-control">
                                    <option value="">Selecione...</option>
                                    <option value="INSTALACAO">Instalação</option>
                                    <option value="MANUTENCAO">Manutenção</option>
                                    <option value="CONSULTORIA">Consultoria</option>
                                    <option value="TREINAMENTO">Treinamento</option>
                                    <option value="SUPORTE">Suporte Técnico</option>
                                    <option value="OUTROS">Outros</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="required"><i class="fas fa-calculator"></i> Tipo de Cobrança</label>
                                <select id="tipoCobranca" class="form-control" required onchange="togglePriceFields()">
                                    <option value="">Selecione...</option>
                                    <option value="FIXO">Valor Fixo</option>
                                    <option value="POR_HORA">Por Hora</option>
                                    <option value="POR_PROJETO">Por Projeto</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Valores -->
                    <div class="form-section">
                        <h3><i class="fas fa-dollar-sign"></i> Valores e Tempo</h3>
                        
                        <div class="form-row">
                            <div class="form-group currency-input" id="valorFixoGroup">
                                <label><i class="fas fa-money-bill"></i> Valor Fixo</label>
                                <input type="number" id="valorFixo" class="form-control" step="0.01" min="0">
                            </div>
                            <div class="form-group currency-input" id="valorHoraGroup">
                                <label><i class="fas fa-clock"></i> Valor por Hora</label>
                                <input type="number" id="valorHora" class="form-control" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-hourglass-half"></i> Tempo Estimado (horas)</label>
                                <input type="number" id="tempoEstimado" class="form-control" step="0.5" min="0">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-toggle-on"></i> Status</label>
                                <select id="ativo" class="form-control">
                                    <option value="true">Ativo</option>
                                    <option value="false">Inativo</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Detalhes Técnicos -->
                    <div class="form-section">
                        <h3><i class="fas fa-cogs"></i> Detalhes Técnicos</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-tools"></i> Equipamentos Necessários</label>
                                <textarea id="equipamentosNecessarios" class="form-control" rows="3" placeholder="Liste os equipamentos necessários..."></textarea>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-user-cog"></i> Competências Requeridas</label>
                                <textarea id="competenciasRequeridas" class="form-control" rows="3" placeholder="Liste as competências técnicas necessárias..."></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-comment"></i> Observações</label>
                                <textarea id="observacoes" class="form-control" rows="3" placeholder="Observações gerais sobre o serviço..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" onclick="closeModal()">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            deleteDoc,
            doc,
            Timestamp,
            query,
            orderBy
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

        // Variáveis globais
        let servicos = [];
        let editingId = null;

        // Inicialização
        document.addEventListener('DOMContentLoaded', async () => {
            await loadServices();
        });

        // Carregar serviços
        async function loadServices() {
            try {
                showLoading(true);

                const servicosSnap = await getDocs(query(collection(db, "servicos"), orderBy("codigo")));
                servicos = servicosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                displayServices();

                console.log('Serviços carregados:', servicos.length);

            } catch (error) {
                console.error('Erro ao carregar serviços:', error);
                showNotification('Erro ao carregar serviços: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Exibir serviços na tabela
        function displayServices(servicesToShow = servicos) {
            const tbody = document.getElementById('servicesTableBody');

            if (servicesToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                            Nenhum serviço encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            servicesToShow.forEach(servico => {
                const row = document.createElement('tr');

                // Determinar valor a exibir baseado no tipo de cobrança
                let valorDisplay = '';
                switch(servico.tipoCobranca) {
                    case 'FIXO':
                        valorDisplay = `R$ ${(servico.valorFixo || 0).toFixed(2)}`;
                        break;
                    case 'POR_HORA':
                        valorDisplay = `R$ ${(servico.valorHora || 0).toFixed(2)}/h`;
                        break;
                    case 'POR_PROJETO':
                        valorDisplay = 'Por projeto';
                        break;
                    default:
                        valorDisplay = 'N/A';
                }

                row.innerHTML = `
                    <td style="font-weight: 600; color: var(--totvs-primary);">${servico.codigo}</td>
                    <td>${servico.descricao}</td>
                    <td>${servico.categoria || 'N/A'}</td>
                    <td>
                        <span class="tipo-badge tipo-${servico.tipoCobranca?.toLowerCase() || 'fixo'}">
                            ${servico.tipoCobranca || 'FIXO'}
                        </span>
                    </td>
                    <td style="font-weight: 600;">${valorDisplay}</td>
                    <td>
                        <span class="status-badge status-${servico.ativo ? 'ativo' : 'inativo'}">
                            ${servico.ativo ? 'ATIVO' : 'INATIVO'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px; margin-right: 5px;"
                                onclick="editService('${servico.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                onclick="deleteService('${servico.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Buscar serviços
        window.searchServices = function() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            if (!searchTerm) {
                displayServices();
                return;
            }

            const filteredServices = servicos.filter(servico =>
                servico.codigo.toLowerCase().includes(searchTerm) ||
                servico.descricao.toLowerCase().includes(searchTerm) ||
                (servico.categoria && servico.categoria.toLowerCase().includes(searchTerm))
            );

            displayServices(filteredServices);
        };

        // Abrir modal
        window.openModal = function(serviceId = null) {
            editingId = serviceId;

            if (serviceId) {
                const servico = servicos.find(s => s.id === serviceId);
                if (servico) {
                    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit"></i> Editar Serviço';
                    fillForm(servico);
                }
            } else {
                document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus"></i> Novo Serviço';
                clearForm();
                generateServiceCode();
            }

            togglePriceFields();
            document.getElementById('serviceModal').style.display = 'block';
        };

        // Fechar modal
        window.closeModal = function() {
            document.getElementById('serviceModal').style.display = 'none';
            clearForm();
            editingId = null;
        };

        // Gerar código do serviço
        function generateServiceCode() {
            const nextNumber = (servicos.length + 1).toString().padStart(3, '0');
            document.getElementById('codigo').value = `SERV${nextNumber}`;
        }

        // Alternar campos de preço baseado no tipo de cobrança
        window.togglePriceFields = function() {
            const tipoCobranca = document.getElementById('tipoCobranca').value;
            const valorFixoGroup = document.getElementById('valorFixoGroup');
            const valorHoraGroup = document.getElementById('valorHoraGroup');

            // Reset visibility
            valorFixoGroup.style.display = 'flex';
            valorHoraGroup.style.display = 'flex';

            // Show/hide based on type
            switch(tipoCobranca) {
                case 'FIXO':
                    valorHoraGroup.style.display = 'none';
                    break;
                case 'POR_HORA':
                    valorFixoGroup.style.display = 'none';
                    break;
                case 'POR_PROJETO':
                    valorFixoGroup.style.display = 'none';
                    valorHoraGroup.style.display = 'none';
                    break;
            }
        };

        // Preencher formulário
        function fillForm(servico) {
            document.getElementById('codigo').value = servico.codigo || '';
            document.getElementById('descricao').value = servico.descricao || '';
            document.getElementById('categoria').value = servico.categoria || '';
            document.getElementById('tipoCobranca').value = servico.tipoCobranca || '';
            document.getElementById('valorFixo').value = servico.valorFixo || '';
            document.getElementById('valorHora').value = servico.valorHora || '';
            document.getElementById('tempoEstimado').value = servico.tempoEstimado || '';
            document.getElementById('ativo').value = servico.ativo !== false ? 'true' : 'false';
            document.getElementById('equipamentosNecessarios').value = servico.equipamentosNecessarios || '';
            document.getElementById('competenciasRequeridas').value = servico.competenciasRequeridas || '';
            document.getElementById('observacoes').value = servico.observacoes || '';
        }

        // Limpar formulário
        function clearForm() {
            document.getElementById('serviceForm').reset();
        }

        // Salvar serviço
        window.saveService = async function(event) {
            event.preventDefault();

            try {
                const codigo = document.getElementById('codigo').value.trim();
                const descricao = document.getElementById('descricao').value.trim();
                const tipoCobranca = document.getElementById('tipoCobranca').value;

                if (!codigo || !descricao || !tipoCobranca) {
                    showNotification('Preencha todos os campos obrigatórios', 'error');
                    return;
                }

                // Verificar código duplicado
                const existingService = servicos.find(s => s.codigo === codigo && s.id !== editingId);
                if (existingService) {
                    showNotification('Código já existe. Use um código diferente.', 'error');
                    return;
                }

                showLoading(true);

                const servicoData = {
                    codigo: codigo,
                    descricao: descricao,
                    categoria: document.getElementById('categoria').value,
                    tipoCobranca: tipoCobranca,
                    valorFixo: parseFloat(document.getElementById('valorFixo').value) || 0,
                    valorHora: parseFloat(document.getElementById('valorHora').value) || 0,
                    tempoEstimado: parseFloat(document.getElementById('tempoEstimado').value) || 0,
                    ativo: document.getElementById('ativo').value === 'true',
                    equipamentosNecessarios: document.getElementById('equipamentosNecessarios').value.trim(),
                    competenciasRequeridas: document.getElementById('competenciasRequeridas').value.trim(),
                    observacoes: document.getElementById('observacoes').value.trim(),
                    dataUltimaAlteracao: Timestamp.now()
                };

                if (editingId) {
                    await updateDoc(doc(db, "servicos", editingId), servicoData);
                    showNotification('Serviço atualizado com sucesso!', 'success');
                } else {
                    servicoData.dataCriacao = Timestamp.now();
                    await addDoc(collection(db, "servicos"), servicoData);
                    showNotification('Serviço criado com sucesso!', 'success');
                }

                closeModal();
                await loadServices();

            } catch (error) {
                console.error('Erro ao salvar serviço:', error);
                showNotification('Erro ao salvar serviço: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Editar serviço
        window.editService = function(serviceId) {
            openModal(serviceId);
        };

        // Excluir serviço
        window.deleteService = async function(serviceId) {
            const servico = servicos.find(s => s.id === serviceId);

            if (!confirm(`Deseja realmente excluir o serviço "${servico.codigo} - ${servico.descricao}"?`)) {
                return;
            }

            try {
                showLoading(true);

                await deleteDoc(doc(db, "servicos", serviceId));

                showNotification('Serviço excluído com sucesso!', 'success');
                await loadServices();

            } catch (error) {
                console.error('Erro ao excluir serviço:', error);
                showNotification('Erro ao excluir serviço: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Funções utilitárias
        function showLoading(show) {
            document.getElementById('loading').classList.toggle('active', show);
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Fechar modal ao clicar fora
        window.onclick = function(event) {
            const modal = document.getElementById('serviceModal');
            if (event.target === modal) {
                closeModal();
            }
        };
    </script>
</body>
</html>
