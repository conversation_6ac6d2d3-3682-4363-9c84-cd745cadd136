<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Sintaxe - Apontamentos</title>
</head>
<body>
    <h1>Teste de Sintaxe JavaScript</h1>
    <div id="resultado"></div>

    <script type="module">
        // Teste básico de sintaxe JavaScript
        console.log('🧪 Testando sintaxe JavaScript...');
        
        try {
            // Simular estrutura similar ao arquivo principal
            let ordensProducao = [];
            let produtos = [];
            let estoques = [];
            
            // Função de teste
            function testarSintaxe() {
                console.log('✅ Sintaxe JavaScript OK');
                return true;
            }
            
            // Função async de teste
            async function testarAsync() {
                console.log('✅ Funções async OK');
                return Promise.resolve(true);
            }
            
            // Teste de arrow functions
            const testarArrow = () => {
                console.log('✅ Arrow functions OK');
                return true;
            };
            
            // Teste de template literals
            const mensagem = `✅ Template literals OK`;
            console.log(mensagem);
            
            // Teste de destructuring
            const obj = { a: 1, b: 2 };
            const { a, b } = obj;
            console.log(`✅ Destructuring OK: ${a}, ${b}`);
            
            // Teste de spread operator
            const arr1 = [1, 2, 3];
            const arr2 = [...arr1, 4, 5];
            console.log('✅ Spread operator OK:', arr2);
            
            // Executar testes
            testarSintaxe();
            testarArrow();
            testarAsync().then(() => {
                console.log('✅ Todos os testes passaram!');
                document.getElementById('resultado').innerHTML = `
                    <div style="color: green; font-weight: bold;">
                        ✅ Sintaxe JavaScript está correta!
                    </div>
                `;
            });
            
        } catch (error) {
            console.error('❌ Erro de sintaxe:', error);
            document.getElementById('resultado').innerHTML = `
                <div style="color: red; font-weight: bold;">
                    ❌ Erro de sintaxe: ${error.message}
                </div>
            `;
        }
    </script>
</body>
</html>
