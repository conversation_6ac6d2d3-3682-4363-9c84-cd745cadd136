<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação de Estruturas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .summary-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .error {
            background-color: #ffe6e6;
            border-left: 4px solid #dc3545;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .filter-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .filter-section input, .filter-section select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.85em;
        }
        .status-error {
            background-color: #dc3545;
            color: white;
        }
        .status-warning {
            background-color: #ffc107;
            color: black;
        }
        .status-success {
            background-color: #28a745;
            color: white;
        }
        .status-info {
            background-color: #2196f3;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Verificação de Estruturas de Produtos</h1>

        <div class="filter-section">
            <input type="text" id="searchInput" placeholder="Buscar por código ou descrição...">
            <select id="tipoFilter">
                <option value="">Todos os tipos</option>
                <option value="PA">Produto Acabado</option>
                <option value="SP">Semi-Produto</option>
                <option value="MP">Matéria Prima</option>
            </select>
            <button class="btn btn-primary" onclick="verificarEstruturas()">Verificar Estruturas</button>
            <button class="btn btn-secondary" onclick="exportarRelatorio()">Exportar Relatório</button>
        </div>

        <div class="summary" id="summarySection">
            <!-- Resumo será preenchido aqui -->
        </div>

        <table id="resultsTable">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                    <th>Tipo</th>
                    <th>Status</th>
                    <th>Problemas Encontrados</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
                <!-- Resultados serão preenchidos aqui -->
            </tbody>
        </table>
    </div>

    <script type="module">
        import { 
            verificarTodasEstruturas,
            verificarEstruturaParaOP
        } from './js/verificar-estruturas.mjs';

        let problemas = [];

        window.onload = async function() {
            // Não precisa mais carregar dados aqui, pois será feito nas funções de verificação
        };

        window.verificarEstruturas = async function() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const tipoFilter = document.getElementById('tipoFilter').value;

                problemas = await verificarTodasEstruturas();

                // Aplicar filtros
                if (searchTerm || tipoFilter) {
                    problemas = problemas.filter(p => 
                        (!tipoFilter || p.produto.tipo === tipoFilter) &&
                        (!searchTerm || 
                         p.produto.codigo.toLowerCase().includes(searchTerm) || 
                         p.produto.descricao.toLowerCase().includes(searchTerm))
                    );
                }

                exibirResultados();
            } catch (error) {
                console.error("Erro ao verificar estruturas:", error);
                alert("Erro ao verificar estruturas: " + error.message);
            }
        };

        function exibirResultados() {
            const summarySection = document.getElementById('summarySection');
            const resultsBody = document.getElementById('resultsBody');

            // Limpar resultados anteriores
            summarySection.innerHTML = '';
            resultsBody.innerHTML = '';

            // Criar resumo
            const totalProdutos = problemas.length;
            const erros = problemas.filter(p => p.erros.length > 0).length;
            const warnings = problemas.filter(p => p.avisos.length > 0).length;
            const infos = problemas.filter(p => p.usoComoComponente.length > 0).length;

            summarySection.innerHTML = `
                <div class="summary-item ${erros > 0 ? 'error' : 'success'}">
                    Total de Produtos com Problemas: ${totalProdutos}
                </div>
                <div class="summary-item ${erros > 0 ? 'error' : 'success'}">
                    Erros Encontrados: ${erros}
                </div>
                <div class="summary-item ${warnings > 0 ? 'warning' : 'success'}">
                    Avisos: ${warnings}
                </div>
                <div class="summary-item info">
                    Produtos Utilizados como Componentes: ${infos}
                </div>
            `;

            // Preencher tabela
            problemas.forEach(problema => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${problema.produto.codigo}</td>
                    <td>${problema.produto.descricao}</td>
                    <td>${problema.produto.tipo}</td>
                    <td>
                        ${problema.erros.length > 0 ? 
                            '<span class="status-badge status-error">Erro</span>' : 
                            problema.avisos.length > 0 ?
                            '<span class="status-badge status-warning">Aviso</span>' :
                            '<span class="status-badge status-info">Info</span>'}
                    </td>
                    <td>
                        ${problema.erros.map(erro => `
                            <div class="error">${erro}</div>
                        `).join('')}
                        ${problema.avisos.map(aviso => `
                            <div class="warning">${aviso}</div>
                        `).join('')}
                        ${problema.usoComoComponente.length > 0 ? `
                            <div class="info">Este produto é utilizado como componente em: ${problema.usoComoComponente.map(p => 
                                `${p.codigo} (${p.descricao})`).join(', ')}</div>
                        ` : ''}
                    </td>
                `;
                resultsBody.appendChild(row);
            });
        }

        window.exportarRelatorio = function() {
            if (problemas.length === 0) {
                alert('Não há dados para exportar. Execute a verificação primeiro.');
                return;
            }

            let csv = 'Código,Descrição,Tipo,Status,Problemas\n';
            problemas.forEach(problema => {
                const status = problema.erros.length > 0 ? 'Erro' : 
                             problema.avisos.length > 0 ? 'Aviso' : 'Info';
                const problemasStr = [
                    ...problema.erros,
                    ...problema.avisos,
                    problema.usoComoComponente.length > 0 ? 
                        `Utilizado como componente em: ${problema.usoComoComponente.map(p => 
                            `${p.codigo} (${p.descricao})`).join(', ')}` : ''
                ].filter(Boolean).join('; ');

                csv += `"${problema.produto.codigo}","${problema.produto.descricao}","${problema.produto.tipo}","${status}","${problemasStr}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `verificacao_estruturas_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };
    </script>
</body>
</html> 