<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Teste do Accordion - Index.html</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
        }
        .btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #218838;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.info {
            background: #cce7ff;
            border: 1px solid #b3d9ff;
            color: #004085;
        }
        
        /* Estilos do accordion para teste */
        .sidebar {
            background: #1e2832;
            width: 300px;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .section-toggle {
            background: none;
            color: #9191a5;
            font-size: 14px;
            border: none;
            width: 100%;
            text-align: left;
            padding: 10px 15px;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            user-select: none;
        }
        .section-toggle:hover {
            background-color: #2a3441;
            color: #fff;
        }
        .section-toggle:focus {
            outline: none;
        }
        
        .accordion-section {
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
        }
        .accordion-content {
            display: none;
            padding-left: 0;
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .accordion-content.active {
            display: block;
            opacity: 1;
            max-height: 1000px;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 1000px;
            }
        }
        
        .menu-button {
            background: none;
            border: none;
            color: #9191a5;
            padding: 8px 15px;
            width: 100%;
            text-align: left;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        .menu-button:hover {
            background-color: #2a3441;
            color: #fff;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Teste do Accordion - Index.html</h1>
            <p>Verificação da funcionalidade de expandir/recolher seções</p>
        </div>

        <div class="test-section">
            <h3>📋 Status da Correção</h3>
            <div class="status success">
                <strong>✅ Correções Aplicadas:</strong>
                <ul>
                    <li>Melhorado CSS com transições suaves</li>
                    <li>Adicionado logs de debug no JavaScript</li>
                    <li>Corrigido event handling com preventDefault</li>
                    <li>Melhorado visual hover dos botões</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Teste do Accordion</h3>
            <p>Clique nas seções abaixo para testar a funcionalidade:</p>
            
            <div class="sidebar">
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li class="accordion-section">
                        <button class="section-toggle">Financeiro <i class="fas fa-chevron-down"></i></button>
                        <ul class="accordion-content" style="list-style: none; padding: 0; margin: 0;">
                            <li><button class="menu-button"><i class="fas fa-money-bill"></i> Contas a Pagar</button></li>
                            <li><button class="menu-button"><i class="fas fa-hand-holding-usd"></i> Contas a Receber</button></li>
                            <li><button class="menu-button"><i class="fas fa-receipt"></i> Faturamento</button></li>
                        </ul>
                    </li>
                    <li class="accordion-section">
                        <button class="section-toggle">Vendas <i class="fas fa-chevron-down"></i></button>
                        <ul class="accordion-content" style="list-style: none; padding: 0; margin: 0;">
                            <li><button class="menu-button"><i class="fas fa-chart-line"></i> Gestão de Vendas</button></li>
                            <li><button class="menu-button"><i class="fas fa-users"></i> Clientes</button></li>
                            <li><button class="menu-button"><i class="fas fa-percentage"></i> Condições Especiais</button></li>
                        </ul>
                    </li>
                    <li class="accordion-section">
                        <button class="section-toggle">Produção <i class="fas fa-chevron-down"></i></button>
                        <ul class="accordion-content" style="list-style: none; padding: 0; margin: 0;">
                            <li><button class="menu-button"><i class="fas fa-industry"></i> Ordens de Produção</button></li>
                            <li><button class="menu-button"><i class="fas fa-cogs"></i> Apontamentos</button></li>
                            <li><button class="menu-button"><i class="fas fa-boxes"></i> Materiais</button></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Log de Debug</h3>
            <p>Abra o console do navegador (F12) para ver os logs detalhados do accordion.</p>
            <div id="log" class="log">
                Aguardando interação com o accordion...
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Instruções de Teste</h3>
            <ol>
                <li><strong>Teste Básico:</strong> Clique em cada seção para expandir/recolher</li>
                <li><strong>Teste de Exclusividade:</strong> Verifique se apenas uma seção fica aberta por vez</li>
                <li><strong>Teste de Ícones:</strong> Confirme se os ícones mudam de ▼ para ▲</li>
                <li><strong>Teste de Animação:</strong> Observe se as transições são suaves</li>
                <li><strong>Console:</strong> Verifique os logs no console do navegador</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 Resultado Esperado</h3>
            <div class="status info">
                <strong>✅ Funcionamento Correto:</strong>
                <ul>
                    <li>Primeira seção abre automaticamente</li>
                    <li>Clique expande/recolhe a seção</li>
                    <li>Apenas uma seção fica aberta por vez</li>
                    <li>Ícones mudam corretamente</li>
                    <li>Animações suaves</li>
                    <li>Logs aparecem no console</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Mesmo código do accordion corrigido
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Inicializando accordion menu...');
            
            const toggles = document.querySelectorAll('.section-toggle');
            console.log(`📋 Encontrados ${toggles.length} botões de seção`);
            
            const logDiv = document.getElementById('log');
            function addLog(message) {
                const timestamp = new Date().toLocaleTimeString();
                logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            
            addLog('🔧 Accordion inicializado');
            addLog(`📋 Encontrados ${toggles.length} botões de seção`);
            
            toggles.forEach((btn, index) => {
                console.log(`🔘 Configurando botão ${index + 1}: ${btn.textContent.trim()}`);
                addLog(`🔘 Configurando: ${btn.textContent.trim()}`);
                
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log(`🖱️ Clique na seção: ${btn.textContent.trim()}`);
                    addLog(`🖱️ Clique: ${btn.textContent.trim()}`);
                    
                    const content = btn.nextElementSibling;
                    const isCurrentlyActive = content.classList.contains('active');
                    
                    // Fecha todas as outras seções
                    document.querySelectorAll('.accordion-content').forEach(ul => {
                        if (ul !== content) {
                            ul.classList.remove('active');
                        }
                    });
                    
                    // Alterna a seção clicada
                    if (isCurrentlyActive) {
                        content.classList.remove('active');
                        addLog(`📤 Seção recolhida: ${btn.textContent.trim()}`);
                    } else {
                        content.classList.add('active');
                        addLog(`📥 Seção expandida: ${btn.textContent.trim()}`);
                    }
                    
                    // Atualiza todos os ícones
                    toggles.forEach(otherBtn => {
                        const icon = otherBtn.querySelector('i');
                        const otherContent = otherBtn.nextElementSibling;
                        
                        if (otherContent.classList.contains('active')) {
                            icon.classList.remove('fa-chevron-down');
                            icon.classList.add('fa-chevron-up');
                        } else {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });
                    
                    console.log(`✅ Seção ${content.classList.contains('active') ? 'expandida' : 'recolhida'}`);
                });
            });
            
            // Abre a primeira seção por padrão
            if (toggles.length > 0) {
                console.log('🎯 Abrindo primeira seção por padrão...');
                addLog('🎯 Abrindo primeira seção por padrão...');
                
                const firstContent = toggles[0].nextElementSibling;
                const firstIcon = toggles[0].querySelector('i');
                
                firstContent.classList.add('active');
                firstIcon.classList.remove('fa-chevron-down');
                firstIcon.classList.add('fa-chevron-up');
                
                console.log('✅ Primeira seção aberta');
                addLog('✅ Primeira seção aberta');
            }
            
            console.log('🎉 Accordion menu inicializado com sucesso!');
            addLog('🎉 Accordion inicializado com sucesso!');
        });
    </script>
</body>
</html>
