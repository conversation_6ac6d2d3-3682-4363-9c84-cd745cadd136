firebase-config.js:80 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-03T14:17:36.285Z', apps: 1}
cadastro_fornecedores.html:3046 🚀 Inicializando sistema de cadastro de fornecedores...
cadastro_fornecedores.html:2064 🔄 Carregando fornecedores do Firebase...
cadastro_fornecedores.html:1604 📋 Parâmetros do sistema carregados: {toleranciaPreco: 5, tipoFretePadrao: 'CIF', controlarHistoricoEntregas: false, configuracaoSistema: {…}, diasAlerteAtraso: 3, …}
cadastro_fornecedores.html:2049 ⏳ Carregamento já em andamento, aguardando...
cadastro_fornecedores.html:1779 🎭 Máscaras de entrada configuradas
cadastro_fornecedores.html:3064 ✅ Sistema inicializado com sucesso!
cadastro_fornecedores.html:3065 📊 Parâmetros ativos: {qualidade: false, homologacao: false, metricas: false}
cadastro_fornecedores.html:2080 ✅ 779 fornecedores carregados do Firestore
cadastro_fornecedores.html:2144 📊 Estatísticas: Total: 779, Ativos: 34, Homologados: 32
cadastro_fornecedores.html:2512 Uncaught TypeError: Cannot read properties of null (reading 'value')
    at HTMLInputElement.filterSuppliers (cadastro_fornecedores.html:2512:75)
filterSuppliers @ cadastro_fornecedores.html:2512
cadastro_fornecedores.html:2512 Uncaught TypeError: Cannot read properties of null (reading 'value')
    at HTMLInputElement.filterSuppliers (cadastro_fornecedores.html:2512:75)
filterSuppliers @ cadastro_fornecedores.html:2512
cadastro_fornecedores.html:2512 Uncaught TypeError: Cannot read properties of null (reading 'value')
    at HTMLInputElement.filterSuppliers (cadastro_fornecedores.html:2512:75)
filterSuppliers @ cadastro_fornecedores.html:2512
cadastro_fornecedores.html:2512 Uncaught TypeError: Cannot read properties of null (reading 'value')
    at HTMLInputElement.filterSuppliers (cadastro_fornecedores.html:2512:75)
filterSuppliers @ cadastro_fornecedores.html:2512
cadastro_fornecedores.html:1530 Uncaught ReferenceError: filterSuppliers is not defined
    at HTMLButtonElement.onclick (cadastro_fornecedores.html:1530:71)
onclick @ cadastro_fornecedores.html:1530
