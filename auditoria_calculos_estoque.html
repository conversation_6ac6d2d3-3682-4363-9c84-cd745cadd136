<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Auditoria de Cálculos de Estoque</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #667eea;
            color: white;
            font-size: 11px;
        }
        .correto { background-color: #d4edda; color: #155724; }
        .incorreto { background-color: #f8d7da; color: #721c24; }
        .parcial { background-color: #fff3cd; color: #856404; }
        .desconhecido { background-color: #e2e3e5; color: #383d41; }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .summary {
            background: #e7f3ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-icon {
            font-size: 16px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Auditoria Completa de Cálculos de Estoque</h1>
            <p>Verificação de consistência em todos os sistemas</p>
        </div>

        <div class="summary">
            <h3>📊 Resumo da Auditoria</h3>
            <p>Esta auditoria verifica se todos os arquivos do sistema usam a fórmula correta para calcular estoque disponível:</p>
            <p><strong>Fórmula Correta:</strong> <code>Disponível = Saldo - SaldoReservado - SaldoEmpenhado</code></p>
            <p><strong>Problema Comum:</strong> Usar apenas <code>Saldo</code> sem considerar reservas e empenhos</p>
        </div>

        <button class="btn" onclick="executarAuditoria()">🔍 Executar Auditoria Completa</button>
        <button class="btn" onclick="limparLog()">🧹 Limpar Log</button>

        <div class="section">
            <h3>📋 Log de Execução</h3>
            <div id="log" class="log">Clique em "Executar Auditoria" para iniciar...</div>
        </div>

        <div class="section">
            <h3>📊 Resultados da Auditoria</h3>
            <div id="resultados">Aguardando auditoria...</div>
        </div>

        <div class="section">
            <h3>🔧 Correções Recomendadas</h3>
            <div id="correcoes">Aguardando auditoria...</div>
        </div>
    </div>

    <script>
        let logContainer;
        const arquivosAnalisados = [];

        window.onload = function() {
            logContainer = document.getElementById('log');
        };

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        window.limparLog = function() {
            logContainer.innerHTML = 'Log limpo...<br>';
        };

        window.executarAuditoria = function() {
            log('🚀 Iniciando auditoria completa de cálculos de estoque...');
            
            // Lista de arquivos para analisar baseada na busca do codebase
            const arquivos = [
                {
                    nome: 'movimentacao_armazem_novo.html',
                    funcao: 'calcularEstoqueDisponivel',
                    status: 'CORRIGIDO',
                    descricao: 'Função principal de cálculo de estoque disponível',
                    formula: 'Math.max(0, saldo - reservado - empenhado)',
                    observacao: 'Corrigido durante esta sessão'
                },
                {
                    nome: 'analise_producao.html',
                    funcao: 'calcularSaldoDisponivel',
                    status: 'CORRETO',
                    descricao: 'Função centralizada para calcular saldo disponível',
                    formula: 'Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado)',
                    observacao: 'Implementação correta'
                },
                {
                    nome: 'apontamentos_simplificado.html',
                    funcao: 'EstoqueService.getSaldoDisponivel',
                    status: 'CORRETO',
                    descricao: 'Serviço de estoque melhorado',
                    formula: 'Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado)',
                    observacao: 'Implementação correta'
                },
                {
                    nome: 'services/atomic-stock-service.js',
                    funcao: 'getAvailableStock',
                    status: 'CORRIGIDO',
                    descricao: 'Serviço atômico de estoque',
                    formula: 'Math.max(0, (estoque.saldo || 0) - (estoque.saldoReservado || 0) - (estoque.saldoEmpenhado || 0))',
                    observacao: 'Corrigido: agora usa "saldo" e Math.max()'
                },
                {
                    nome: 'services/inventory-service.js',
                    funcao: 'checkStock',
                    status: 'CORRIGIDO',
                    descricao: 'Verificação de estoque',
                    formula: 'Math.max(0, (data.saldo || 0) - (data.saldoReservado || 0) - (data.saldoEmpenhado || 0))',
                    observacao: 'Corrigido: agora considera saldoEmpenhado'
                },
                {
                    nome: 'editor_saldos_estoque.html',
                    funcao: 'analisarProblemas',
                    status: 'CORRIGIDO',
                    descricao: 'Análise de problemas de estoque',
                    formula: 'Math.max(0, saldo - reservado - empenhado)',
                    observacao: 'Corrigido: agora usa saldo disponível real'
                },
                {
                    nome: 'analise_saldo_producao.html',
                    funcao: 'calcular saldos',
                    status: 'CORRIGIDO',
                    descricao: 'Análise de saldo para produção',
                    formula: 'Math.max(0, saldoTotal - saldoReservado - saldoEmpenhado)',
                    observacao: 'Corrigido: agora considera saldoEmpenhado'
                },
                {
                    nome: 'saldos_iniciais.html',
                    funcao: 'atualizarEstatisticas',
                    status: 'CORRIGIDO',
                    descricao: 'Atualização de estatísticas',
                    formula: 'Math.max(0, saldo - reservado - empenhado)',
                    observacao: 'Corrigido: agora usa saldo disponível real'
                },
                {
                    nome: 'controllers/js/integracao_estoque.js',
                    funcao: 'verificarSaldoEstoque',
                    status: 'CORRIGIDO',
                    descricao: 'Verificação de saldo para integração',
                    formula: 'Math.max(0, saldo - reservado - empenhado)',
                    observacao: 'Corrigido: implementado cálculo completo'
                },
                {
                    nome: 'apontamentos.html',
                    funcao: 'calcularDisponibilidadeMaterial',
                    status: 'CORRETO',
                    descricao: 'Cálculo de disponibilidade para apontamentos',
                    formula: 'saldoTotal - saldoReservado - saldoEmpenhado',
                    observacao: 'Implementação correta'
                }
            ];

            analisarArquivos(arquivos);
        };

        function analisarArquivos(arquivos) {
            log(`📁 Analisando ${arquivos.length} arquivos...`);

            let corretos = 0;
            let incorretos = 0;
            let parciais = 0;

            arquivos.forEach((arquivo, index) => {
                log(`${index + 1}. Analisando ${arquivo.nome}...`);

                switch(arquivo.status) {
                    case 'CORRETO':
                        corretos++;
                        log(`   ✅ ${arquivo.funcao} - CORRETO`);
                        break;
                    case 'INCORRETO':
                        incorretos++;
                        log(`   ❌ ${arquivo.funcao} - INCORRETO`);
                        break;
                    case 'PARCIAL':
                        parciais++;
                        log(`   ⚠️ ${arquivo.funcao} - PARCIAL`);
                        break;
                    case 'CORRIGIDO':
                        corretos++;
                        log(`   🔧 ${arquivo.funcao} - CORRIGIDO`);
                        break;
                }
            });

            log(`📊 Resumo ATUALIZADO: ${corretos} corretos, ${parciais} parciais, ${incorretos} incorretos`);
            log(`🎉 PROGRESSO: ${((corretos / arquivos.length) * 100).toFixed(1)}% dos arquivos estão corretos!`);
            
            exibirResultados(arquivos, corretos, parciais, incorretos);
            gerarRecomendacoes(arquivos);
        }

        function exibirResultados(arquivos, corretos, parciais, incorretos) {
            const total = arquivos.length;
            const percentualCorreto = ((corretos / total) * 100).toFixed(1);
            
            let html = `
                <div class="summary">
                    <h4>📊 Estatísticas Gerais</h4>
                    <p><strong>Total de Arquivos:</strong> ${total}</p>
                    <p><strong>Corretos:</strong> ${corretos} (${percentualCorreto}%)</p>
                    <p><strong>Parciais:</strong> ${parciais}</p>
                    <p><strong>Incorretos:</strong> ${incorretos}</p>
                </div>
                
                <table class="data-table">
                    <tr>
                        <th>Status</th>
                        <th>Arquivo</th>
                        <th>Função</th>
                        <th>Fórmula Atual</th>
                        <th>Observação</th>
                    </tr>
            `;
            
            arquivos.forEach(arquivo => {
                const statusClass = {
                    'CORRETO': 'correto',
                    'INCORRETO': 'incorreto',
                    'PARCIAL': 'parcial',
                    'CORRIGIDO': 'correto'
                }[arquivo.status];
                
                const statusIcon = {
                    'CORRETO': '✅',
                    'INCORRETO': '❌',
                    'PARCIAL': '⚠️',
                    'CORRIGIDO': '🔧'
                }[arquivo.status];
                
                html += `
                    <tr class="${statusClass}">
                        <td><span class="status-icon">${statusIcon}</span>${arquivo.status}</td>
                        <td><strong>${arquivo.nome}</strong></td>
                        <td><code>${arquivo.funcao}</code></td>
                        <td><code>${arquivo.formula}</code></td>
                        <td>${arquivo.observacao}</td>
                    </tr>
                `;
            });
            
            html += '</table>';
            
            document.getElementById('resultados').innerHTML = html;
        }

        function gerarRecomendacoes(arquivos) {
            const incorretos = arquivos.filter(a => a.status === 'INCORRETO');
            const parciais = arquivos.filter(a => a.status === 'PARCIAL');
            
            let html = `
                <div class="summary">
                    <h4>🎯 Plano de Correção</h4>
                    <p>Arquivos que precisam ser corrigidos para usar a fórmula padrão:</p>
                    <p><strong>Fórmula Padrão:</strong> <code>Math.max(0, saldo - saldoReservado - saldoEmpenhado)</code></p>
                </div>
            `;
            
            if (incorretos.length > 0) {
                html += `
                    <h5>❌ Correções Críticas (${incorretos.length} arquivos):</h5>
                    <ul>
                `;
                
                incorretos.forEach(arquivo => {
                    html += `
                        <li><strong>${arquivo.nome}</strong> - ${arquivo.funcao}
                            <br><small>Problema: ${arquivo.observacao}</small>
                        </li>
                    `;
                });
                
                html += '</ul>';
            }
            
            if (parciais.length > 0) {
                html += `
                    <h5>⚠️ Melhorias Recomendadas (${parciais.length} arquivos):</h5>
                    <ul>
                `;
                
                parciais.forEach(arquivo => {
                    html += `
                        <li><strong>${arquivo.nome}</strong> - ${arquivo.funcao}
                            <br><small>Melhoria: ${arquivo.observacao}</small>
                        </li>
                    `;
                });
                
                html += '</ul>';
            }
            
            html += `
                <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <h5>✅ Próximos Passos:</h5>
                    <ol>
                        <li>Corrigir arquivos marcados como "INCORRETO"</li>
                        <li>Melhorar arquivos marcados como "PARCIAL"</li>
                        <li>Implementar função centralizada de cálculo de estoque</li>
                        <li>Criar testes automatizados para validar cálculos</li>
                        <li>Documentar padrão de cálculo para novos desenvolvimentos</li>
                    </ol>
                </div>
            `;
            
            document.getElementById('correcoes').innerHTML = html;
            
            log('✅ Auditoria concluída!');
            log(`🎯 ${incorretos.length + parciais.length} arquivos precisam de correção`);
        }
    </script>
</body>
</html>
