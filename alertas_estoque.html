<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alertas de Estoque</title>
    <link rel="stylesheet" href="styles/styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Alertas de Estoque</h1>
            <div class="header-buttons">
                <button class="btn-secondary" onclick="window.location.href='index.html'">Voltar</button>
                <button class="btn-primary" onclick="verificarEstoqueMinimo()">Verificar Estoque Mínimo</button>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>Alertas Pendentes</h2>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Estoque Mínimo</th>
                                <th>Saldo Atual</th>
                                <th>Data Alerta</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="alertasTableBody"></tbody>
                    </table>
                </div>
            </div>

            <div id="sugestaoCompra" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Sugestão de Compra</h2>
                        <span class="close-button" onclick="fecharModal()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div id="sugestaoContent"></div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-primary" onclick="criarSolicitacaoCompra()">Criar Solicitação</button>
                        <button class="btn-secondary" onclick="fecharModal()">Fechar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { AlertasEstoque } from './js/alertas_estoque.js';
        const alertasEstoque = new AlertasEstoque();

        // Carrega alertas ao iniciar
        window.onload = async function() {
            await carregarAlertas();
        };

        // Função para carregar alertas pendentes
        async function carregarAlertas() {
            try {
                const alertas = await alertasEstoque.buscarAlertasPendentes();
                const tbody = document.getElementById('alertasTableBody');
                tbody.innerHTML = '';

                alertas.forEach(alerta => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${alerta.produtoCodigo}</td>
                        <td>${alerta.produtoDescricao}</td>
                        <td class="text-right">${alerta.estoqueMinimo}</td>
                        <td class="text-right">${alerta.saldoAtual}</td>
                        <td>${new Date(alerta.dataAlerta.toDate()).toLocaleDateString()}</td>
                        <td>
                            <button class="btn-primary" onclick="verSugestaoCompra('${alerta.produtoId}')">
                                Ver Sugestão
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                console.error('Erro ao carregar alertas:', error);
                alert('Erro ao carregar alertas: ' + error.message);
            }
        }

        // Função para verificar estoque mínimo
        window.verificarEstoqueMinimo = async function() {
            try {
                const alertas = await alertasEstoque.verificarEstoqueMinimo();
                await carregarAlertas();
                alert(`Verificação concluída! ${alertas.length} novos alertas gerados.`);
            } catch (error) {
                console.error('Erro na verificação:', error);
                alert('Erro na verificação: ' + error.message);
            }
        };

        // Função para ver sugestão de compra
        window.verSugestaoCompra = async function(produtoId) {
            try {
                const sugestao = await alertasEstoque.gerarSugestaoCompra(produtoId);
                const content = document.getElementById('sugestaoContent');
                content.innerHTML = `
                    <div class="detail-section">
                        <h3>Produto</h3>
                        <p><strong>Código:</strong> ${sugestao.produtoCodigo}</p>
                        <p><strong>Descrição:</strong> ${sugestao.produtoDescricao}</p>
                    </div>
                    <div class="detail-section">
                        <h3>Análise de Consumo</h3>
                        <p><strong>Consumo Médio Mensal:</strong> ${sugestao.consumoMedioPorMes.toFixed(2)}</p>
                        <p><strong>Quantidade Sugerida:</strong> ${sugestao.quantidadeSugerida}</p>
                        <p><small>Baseado em ${sugestao.baseadoEm.numeroMovimentacoes} movimentações nos últimos ${sugestao.baseadoEm.periodo}</small></p>
                    </div>
                `;
                document.getElementById('sugestaoCompra').style.display = 'flex';
            } catch (error) {
                console.error('Erro ao gerar sugestão:', error);
                alert('Erro ao gerar sugestão: ' + error.message);
            }
        };

        // Função para fechar modal
        window.fecharModal = function() {
            document.getElementById('sugestaoCompra').style.display = 'none';
        };

        // Função para criar solicitação de compra
        window.criarSolicitacaoCompra = function() {
            // Implementar integração com o módulo de solicitação de compras
            alert('Funcionalidade em desenvolvimento');
            fecharModal();
        };
    </script>
</body>
</html>
