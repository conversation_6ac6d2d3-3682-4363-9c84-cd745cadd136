const express = require('express');
const path = require('path');
const app = express();

// Serve static files from the root directory
app.use(express.static(__dirname));

// Handle specific routes for HTML files
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/*.html', (req, res) => {
  const fileName = req.params[0] + '.html';
  res.sendFile(path.join(__dirname, fileName));
});

const PORT = 8000;
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando em http://127.0.0.1:${PORT}/`);
  console.log(`📁 Servindo arquivos de: ${__dirname}`);
  console.log(`⏰ Iniciado em: ${new Date().toLocaleString('pt-BR')}`);
  console.log('📋 Páginas disponíveis:');
  console.log(`   🏠 Principal: http://127.0.0.1:${PORT}/`);
  console.log(`   📋 Necessidades: http://127.0.0.1:${PORT}/controle_baixa_necessidades.html`);
  console.log(`   👥 Fornecedores: http://127.0.0.1:${PORT}/cadastro_fornecedores.html`);
  console.log(`   🏗️ Estruturas: http://127.0.0.1:${PORT}/estrutura_nova.html`);
  console.log(`   🏭 Produção: http://127.0.0.1:${PORT}/ordens_producao.html`);
  console.log('');
  console.log('💡 Para parar o servidor, pressione Ctrl+C');
});
