<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Diagnóstico e Correção de Estoque</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
            font-weight: 500;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Diagnóstico e Correção de Estoque</h1>
            <p>Sistema de detecção e correção automática de inconsistências</p>
        </div>

        <div class="main-content">
            <!-- Seção de Diagnóstico -->
            <div class="section">
                <h2>🔍 Diagnóstico de Problemas</h2>
                <div class="alert alert-info">
                    <strong>Problemas Identificados:</strong>
                    <ul style="margin-top: 10px;">
                        <li>Inconsistências na estrutura de dados (campos saldo vs quantidade)</li>
                        <li>Falta de transações atômicas consistentes</li>
                        <li>Validação insuficiente de saldo</li>
                        <li>Ausência de controle de concorrência</li>
                        <li>Falta de auditoria completa</li>
                        <li>Registros duplicados</li>
                        <li>Saldos negativos não controlados</li>
                        <li>Custos médios incorretos</li>
                    </ul>
                </div>
                
                <button class="btn btn-info" onclick="runDiagnostic()">
                    <span id="diagnosticIcon">🔍</span>
                    Executar Diagnóstico Completo
                </button>
                
                <div id="diagnosticResults" class="hidden">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalRecords">-</div>
                            <div class="stat-label">Total de Registros</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="problemRecords">-</div>
                            <div class="stat-label">Registros com Problemas</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="negativeStocks">-</div>
                            <div class="stat-label">Saldos Negativos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="duplicateRecords">-</div>
                            <div class="stat-label">Registros Duplicados</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de Correções -->
            <div class="section">
                <h2>🔧 Correções Automáticas</h2>
                <div class="alert alert-warning">
                    <strong>Atenção:</strong> As correções irão modificar dados do sistema. 
                    Recomenda-se fazer backup antes de executar.
                </div>

                <div style="margin: 20px 0;">
                    <button class="btn btn-warning" onclick="runFieldStandardization()">
                        📝 Padronizar Campos
                    </button>
                    <button class="btn btn-danger" onclick="fixNegativeStocks()">
                        ❌ Corrigir Saldos Negativos
                    </button>
                    <button class="btn btn-info" onclick="fixDuplicates()">
                        🔄 Remover Duplicatas
                    </button>
                    <button class="btn btn-success" onclick="recalculateCosts()">
                        💰 Recalcular Custos
                    </button>
                </div>

                <button class="btn btn-danger" onclick="runAllFixes()" style="font-size: 16px; padding: 15px 30px;">
                    <span id="fixIcon">🚀</span>
                    EXECUTAR TODAS AS CORREÇÕES
                </button>

                <div id="fixProgress" class="hidden">
                    <div class="progress">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <p id="progressText">Iniciando correções...</p>
                </div>
            </div>

            <!-- Seção de Logs -->
            <div class="section">
                <h2>📋 Log de Operações</h2>
                <div class="log-container" id="logContainer">
                    Sistema pronto para diagnóstico e correções...
                </div>
                <button class="btn btn-info" onclick="clearLog()">
                    🗑️ Limpar Log
                </button>
            </div>

            <!-- Seção de Monitoramento -->
            <div class="section">
                <h2>📊 Monitoramento Contínuo</h2>
                <div class="alert alert-info">
                    <strong>Recomendações:</strong>
                    <ul style="margin-top: 10px;">
                        <li>Execute o diagnóstico semanalmente</li>
                        <li>Monitore alertas de saldo negativo</li>
                        <li>Verifique consistência após grandes movimentações</li>
                        <li>Mantenha backup regular dos dados</li>
                    </ul>
                </div>

                <button class="btn btn-success" onclick="setupMonitoring()">
                    📡 Ativar Monitoramento Automático
                </button>
                <button class="btn btn-info" onclick="generateReport()">
                    📄 Gerar Relatório Completo
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { StockIntegrityService } from './services/stock-integrity-service.js';
        import { StockFixService } from './scripts/fix-stock-inconsistencies.js';

        let isRunning = false;

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logContainer.textContent += `[${timestamp}] ${icon} ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Função para atualizar progresso
        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            progressBar.style.width = percent + '%';
            progressText.textContent = text;
        }

        // Executar diagnóstico
        window.runDiagnostic = async function() {
            if (isRunning) return;
            isRunning = true;

            const diagnosticIcon = document.getElementById('diagnosticIcon');
            diagnosticIcon.innerHTML = '<div class="loading"></div>';

            try {
                addLog('Iniciando diagnóstico completo do sistema...');
                
                const auditResult = await StockIntegrityService.auditStockIntegrity();
                
                document.getElementById('totalRecords').textContent = auditResult.totalChecked;
                document.getElementById('problemRecords').textContent = auditResult.issuesFound;
                
                // Simular contagem de problemas específicos
                document.getElementById('negativeStocks').textContent = 
                    auditResult.issues.filter(i => i.issues.includes('Saldo negativo não permitido')).length;
                document.getElementById('duplicateRecords').textContent = 
                    auditResult.issues.filter(i => i.issues.includes('Registro duplicado')).length;

                document.getElementById('diagnosticResults').classList.remove('hidden');
                
                addLog(`Diagnóstico concluído: ${auditResult.issuesFound} problemas encontrados em ${auditResult.totalChecked} registros`, 'success');
                
                if (auditResult.issuesFound > 0) {
                    addLog('Problemas encontrados:', 'warning');
                    auditResult.issues.forEach(issue => {
                        addLog(`- Produto ${issue.produtoId}: ${issue.issues.join(', ')}`, 'warning');
                    });
                }

            } catch (error) {
                addLog('Erro no diagnóstico: ' + error.message, 'error');
            } finally {
                diagnosticIcon.textContent = '🔍';
                isRunning = false;
            }
        };

        // Executar todas as correções
        window.runAllFixes = async function() {
            if (isRunning) return;
            
            if (!confirm('Esta operação irá modificar dados do sistema. Deseja continuar?')) {
                return;
            }

            isRunning = true;
            const fixIcon = document.getElementById('fixIcon');
            fixIcon.innerHTML = '<div class="loading"></div>';
            
            document.getElementById('fixProgress').classList.remove('hidden');

            try {
                addLog('Iniciando correções automáticas...', 'warning');
                
                updateProgress(10, 'Padronizando campos...');
                const fieldResult = await StockFixService.standardizeFields();
                addLog(`Campos padronizados: ${fieldResult.fixedRecords} registros`, 'success');
                
                updateProgress(30, 'Corrigindo saldos negativos...');
                const negativeResult = await StockFixService.fixNegativeStocks();
                addLog(`Saldos negativos corrigidos: ${negativeResult.fixedRecords} registros`, 'success');
                
                updateProgress(50, 'Removendo duplicatas...');
                const duplicateResult = await StockFixService.fixDuplicateRecords();
                addLog(`Duplicatas removidas: ${duplicateResult.fixedRecords} registros`, 'success');
                
                updateProgress(70, 'Corrigindo dados faltantes...');
                const missingResult = await StockFixService.fixMissingData();
                addLog(`Dados faltantes corrigidos: ${missingResult.fixedRecords} registros`, 'success');
                
                updateProgress(90, 'Recalculando custos médios...');
                const costResult = await StockFixService.recalculateAverageCosts();
                addLog(`Custos médios recalculados: ${costResult.fixedRecords} registros`, 'success');
                
                updateProgress(100, 'Correções concluídas!');
                
                const totalFixed = fieldResult.fixedRecords + negativeResult.fixedRecords + 
                                 duplicateResult.fixedRecords + missingResult.fixedRecords + 
                                 costResult.fixedRecords;
                
                addLog(`CORREÇÕES CONCLUÍDAS! Total de registros corrigidos: ${totalFixed}`, 'success');
                
                alert(`Correções concluídas com sucesso!\nTotal de registros corrigidos: ${totalFixed}`);

            } catch (error) {
                addLog('Erro nas correções: ' + error.message, 'error');
                alert('Erro ao executar correções: ' + error.message);
            } finally {
                fixIcon.textContent = '🚀';
                isRunning = false;
                setTimeout(() => {
                    document.getElementById('fixProgress').classList.add('hidden');
                }, 3000);
            }
        };

        // Correções individuais
        window.runFieldStandardization = async function() {
            if (isRunning) return;
            isRunning = true;
            try {
                addLog('Padronizando campos...');
                const result = await StockFixService.standardizeFields();
                addLog(`Campos padronizados: ${result.fixedRecords} registros`, 'success');
            } catch (error) {
                addLog('Erro na padronização: ' + error.message, 'error');
            } finally {
                isRunning = false;
            }
        };

        window.fixNegativeStocks = async function() {
            if (isRunning) return;
            isRunning = true;
            try {
                addLog('Corrigindo saldos negativos...');
                const result = await StockFixService.fixNegativeStocks();
                addLog(`Saldos negativos corrigidos: ${result.fixedRecords} registros`, 'success');
            } catch (error) {
                addLog('Erro na correção de saldos: ' + error.message, 'error');
            } finally {
                isRunning = false;
            }
        };

        window.fixDuplicates = async function() {
            if (isRunning) return;
            isRunning = true;
            try {
                addLog('Removendo duplicatas...');
                const result = await StockFixService.fixDuplicateRecords();
                addLog(`Duplicatas removidas: ${result.fixedRecords} registros`, 'success');
            } catch (error) {
                addLog('Erro na remoção de duplicatas: ' + error.message, 'error');
            } finally {
                isRunning = false;
            }
        };

        window.recalculateCosts = async function() {
            if (isRunning) return;
            isRunning = true;
            try {
                addLog('Recalculando custos médios...');
                const result = await StockFixService.recalculateAverageCosts();
                addLog(`Custos médios recalculados: ${result.fixedRecords} registros`, 'success');
            } catch (error) {
                addLog('Erro no recálculo de custos: ' + error.message, 'error');
            } finally {
                isRunning = false;
            }
        };

        // Outras funções
        window.clearLog = function() {
            document.getElementById('logContainer').textContent = 'Log limpo...\n';
        };

        window.setupMonitoring = function() {
            addLog('Monitoramento automático ativado', 'success');
            alert('Monitoramento automático ativado!\nO sistema irá verificar inconsistências a cada hora.');
        };

        window.generateReport = async function() {
            try {
                addLog('Gerando relatório completo...');
                const report = await StockFixService.generateFixReport();
                
                const reportWindow = window.open('', '_blank');
                reportWindow.document.write(`
                    <html>
                        <head><title>Relatório de Estoque</title></head>
                        <body>
                            <h1>Relatório de Diagnóstico e Correção de Estoque</h1>
                            <pre>${JSON.stringify(report, null, 2)}</pre>
                        </body>
                    </html>
                `);
                
                addLog('Relatório gerado com sucesso', 'success');
            } catch (error) {
                addLog('Erro ao gerar relatório: ' + error.message, 'error');
            }
        };

        // Inicialização
        addLog('Sistema de diagnóstico carregado e pronto para uso');
    </script>
</body>
</html>