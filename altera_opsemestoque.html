<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FYRON MRP - Alterar Ordem de Produção</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .main-content {
      padding: 30px;
    }

    .card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      margin-bottom: 25px;
      overflow: hidden;
    }

    .card-header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 20px 25px;
      border-bottom: 3px solid #2c3e50;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-body {
      padding: 25px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 14px;
    }

    input[type="text"], input[type="number"], input[type="date"], select, textarea {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      background: #fff;
      transition: all 0.3s ease;
      font-family: inherit;
    }

    input:focus, select:focus, textarea:focus {
      border-color: #3498db;
      outline: none;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }

    .search-row {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
    }

    .search-row input {
      flex: 1;
    }

    .op-list {
      background: #f8f9fa;
      border-radius: 8px;
      margin-top: 10px;
      max-height: 200px;
      overflow-y: auto;
      border: 2px solid #e9ecef;
    }

    .op-item {
      padding: 15px 20px;
      cursor: pointer;
      border-bottom: 1px solid #e9ecef;
      transition: all 0.2s ease;
    }

    .op-item:last-child {
      border-bottom: none;
    }

    .op-item:hover, .op-item.selected {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      color: #1976d2;
    }

    .op-item.disabled {
      background: #f8d7da;
      color: #721c24;
      cursor: not-allowed;
      opacity: 0.7;
    }

    .op-item.disabled:hover {
      background: #f8d7da;
      color: #721c24;
    }

    .status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-align: center;
      display: inline-block;
      margin-left: 10px;
    }

    .status-pendente {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status-em-producao {
      background: #cce5ff;
      color: #004085;
      border: 1px solid #b3d7ff;
    }

    .status-concluida {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-cancelada {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    /* Estilos para Card de Produção */
    #productionCard .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .status-badge {
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .production-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 25px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .info-item label {
      display: block;
      font-weight: 600;
      color: #495057;
      margin-bottom: 5px;
      font-size: 14px;
    }

    .info-value {
      background: white;
      padding: 10px;
      border-radius: 5px;
      border: 1px solid #dee2e6;
      min-height: 20px;
      font-size: 14px;
    }

    .materials-section, .extra-materials-section {
      margin-bottom: 25px;
    }

    .section-header {
      margin-bottom: 15px;
    }

    .section-header h4 {
      margin: 0 0 5px 0;
      color: #495057;
      font-size: 16px;
    }

    .section-subtitle {
      color: #6c757d;
      font-size: 13px;
      font-style: italic;
    }

    .materials-list {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      min-height: 60px;
    }

    .empty-state {
      text-align: center;
      color: #6c757d;
      padding: 20px;
    }

    .empty-state i {
      font-size: 32px;
      margin-bottom: 10px;
      display: block;
    }

    .empty-state p {
      margin: 0;
      font-style: italic;
    }

    .componentes-section {
      margin-top: 20px;
    }

    .component-list {
      margin-top: 15px;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 0;
      background: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .component-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 10px;
      overflow: hidden;
    }

    .component-table thead {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .component-table th {
      color: white;
      padding: 12px 10px;
      text-align: left;
      font-weight: 600;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #1a252f;
    }

    .component-table tbody tr {
      border-bottom: 1px solid #f1f3f4;
      transition: all 0.2s ease;
    }

    .component-table tbody tr:nth-child(even) {
      background: #f8f9fa;
    }

    .component-table tbody tr:nth-child(odd) {
      background: white;
    }

    .component-table tbody tr:hover {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      transform: scale(1.01);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .component-table td {
      padding: 10px;
      vertical-align: middle;
      border-bottom: 1px solid #f1f3f4;
    }

    .component-table input[type="text"],
    .component-table input[type="number"],
    .component-table select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid #d4d4d4;
      border-radius: 4px;
      font-size: 13px;
      background: white;
      transition: all 0.2s ease;
    }

    .component-table input:focus,
    .component-table select:focus {
      border-color: #3498db;
      outline: none;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    .component-table .desc-readonly {
      background: #f8f9fa !important;
      color: #6c757d;
      cursor: not-allowed;
    }

    .component-item input[type="text"] {
      width: 120px;
      padding: 8px 10px;
      font-size: 13px;
    }

    .component-item input[type="number"] {
      width: 90px;
      padding: 8px 10px;
      font-size: 13px;
    }

    .component-item select {
      width: 70px;
      padding: 8px 6px;
      font-size: 13px;
    }

    .component-item label {
      font-size: 11px;
      color: #6c757d;
      margin-bottom: 2px;
      font-weight: 500;
      line-height: 1;
    }

    .remove-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 8px 10px;
      cursor: pointer;
      font-size: 11px;
      font-weight: 600;
      transition: all 0.3s ease;
      min-width: 36px;
      height: 36px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .remove-btn:hover {
      transform: translateY(-1px) scale(1.05);
      box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    }

    .remove-btn:active {
      transform: translateY(0) scale(0.95);
    }

    .add-btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 20px;
      cursor: pointer;
      margin-top: 15px;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .add-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(52, 152, 219, 0.3);
    }

    .actions {
      display: flex;
      gap: 15px;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .actions .btn {
      padding: 12px 25px;
      font-size: 16px;
      font-weight: 600;
    }

    .alert {
      padding: 15px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid;
      font-weight: 500;
    }

    .alert-warning {
      background: #fff3cd;
      color: #856404;
      border-left-color: #ffc107;
    }

    .alert-danger {
      background: #f8d7da;
      color: #721c24;
      border-left-color: #dc3545;
    }

    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border-left-color: #17a2b8;
    }

    /* Modal styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 0;
      border-radius: 15px;
      width: 90%;
      max-width: 900px;
      max-height: 85vh;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .modal-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 3px solid #2c3e50;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .close {
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .close:hover {
      color: #e74c3c;
      transform: scale(1.1);
    }

    .modal-body {
      padding: 25px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .revision-item {
      padding: 20px;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      margin-bottom: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;
    }

    .revision-item:hover {
      background: #f8f9fa;
      border-color: #3498db;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .revision-item.selected {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-color: #2196f3;
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(33, 150, 243, 0.2);
    }

    .revision-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .revision-number {
      font-weight: 700;
      color: #2c3e50;
      font-size: 18px;
    }

    .revision-date {
      color: #7f8c8d;
      font-size: 14px;
    }

    .revision-details {
      font-size: 14px;
      color: #34495e;
      line-height: 1.5;
    }

    .revision-changes {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      font-size: 13px;
      border-left: 4px solid #3498db;
    }

    .current-revision {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
      border-color: #4caf50 !important;
      cursor: not-allowed !important;
      opacity: 0.7;
    }

    .current-revision:hover {
      transform: none !important;
      box-shadow: none !important;
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
      border-color: #4caf50 !important;
    }

    /* Comparison styles */
    .comparison-section {
      display: none;
      margin-bottom: 25px;
    }

    .comparison-header {
      text-align: center;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 10px;
      border: 2px solid #dee2e6;
    }

    .comparison-title {
      font-size: 20px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .comparison-subtitle {
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
    }

    .comparison-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
      max-height: 600px;
      overflow-y: auto;
    }

    .comparison-side {
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 20px;
      background: white;
    }

    .comparison-side.old {
      background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
      border-color: #feb2b2;
    }

    .comparison-side.new {
      background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
      border-color: #9ae6b4;
    }

    .side-title {
      font-weight: 700;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #e9ecef;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .side-title.old {
      color: #e53e3e;
      border-bottom-color: #feb2b2;
    }

    .side-title.new {
      color: #38a169;
      border-bottom-color: #9ae6b4;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
    }

    .summary-card {
      text-align: center;
      padding: 15px 10px;
      border-radius: 8px;
      border: 2px solid #e9ecef;
      background: white;
    }

    .summary-card.old {
      background: #fff5f5;
      border-color: #feb2b2;
    }

    .summary-card.new {
      background: #f0fff4;
      border-color: #9ae6b4;
    }

    .summary-number {
      font-size: 24px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .summary-label {
      font-size: 11px;
      color: #7f8c8d;
      font-weight: 600;
      text-transform: uppercase;
    }

    .materials-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
    }

    .materials-table th {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 8px 6px;
      text-align: left;
      font-weight: 600;
      font-size: 11px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #1a252f;
    }

    .materials-table td {
      padding: 6px 6px;
      border-bottom: 1px solid #f1f3f4;
      vertical-align: middle;
      font-size: 12px;
      line-height: 1.3;
    }

    .materials-table tr:hover {
      background: #f8f9fa;
    }

    .materials-table tr:last-child td {
      border-bottom: none;
    }

    .materials-table .codigo-col {
      font-weight: 600;
      color: #2c3e50;
      min-width: 80px;
    }

    .materials-table .desc-col {
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .materials-table .tipo-col {
      text-align: center;
      min-width: 40px;
    }

    .materials-table .qtde-col {
      text-align: right;
      font-weight: 600;
      min-width: 60px;
    }

    .materials-table .unidade-col {
      text-align: center;
      min-width: 40px;
      font-size: 11px;
    }

    .materials-table .status-col {
      text-align: center;
      min-width: 70px;
    }

    .quantity-change {
      font-weight: 600;
      padding: 1px 4px;
      border-radius: 3px;
      font-size: 10px;
      display: inline-block;
      min-width: 45px;
      text-align: center;
    }

    .quantity-increase {
      background: #c6f6d5;
      color: #22543d;
      border: 1px solid #9ae6b4;
    }

    .quantity-decrease {
      background: #fed7d7;
      color: #742a2a;
      border: 1px solid #feb2b2;
    }

    .quantity-same {
      background: #e2e8f0;
      color: #4a5568;
      border: 1px solid #cbd5e0;
    }

    .material-new {
      background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    }

    .material-removed {
      background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
      text-decoration: line-through;
      opacity: 0.8;
    }

    .comparison-actions {
      text-align: center;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 2px solid #e9ecef;
    }

    .approve-btn {
      background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
      color: white;
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      margin-right: 15px;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .approve-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(56, 161, 105, 0.3);
    }

    .reject-btn {
      background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
      color: white;
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .reject-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(229, 62, 62, 0.3);
    }

    .cascade-section {
      margin-top: 25px;
    }

    .cascade-header {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      border: 2px solid #ffeaa7;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .cascade-title {
      color: #856404;
      margin: 0 0 10px 0;
      font-size: 18px;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .cascade-subtitle {
      color: #856404;
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .cascade-ops {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }

    .cascade-op {
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .cascade-op-header {
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 15px;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .cascade-op-details {
      font-size: 14px;
      color: #7f8c8d;
      line-height: 1.5;
    }

    .cascade-sp-list {
      margin-top: 15px;
    }

    .cascade-sp-item {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 8px;
      font-size: 13px;
      border: 1px solid #90caf9;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        border-radius: 10px;
      }

      .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header-actions {
        flex-wrap: wrap;
        justify-content: center;
      }

      .main-content {
        padding: 20px;
      }

      .component-item {
        flex-direction: column;
        gap: 6px;
        padding: 8px;
      }

      .component-item > div {
        flex: none !important;
        min-width: auto !important;
        width: 100%;
      }

      .component-item input,
      .component-item select {
        width: 100%;
        font-size: 14px;
      }

      .component-item label {
        font-size: 12px;
        margin-bottom: 3px;
      }

      .remove-btn {
        width: 100%;
        margin-top: 5px;
      }

      .component-table {
        font-size: 11px;
      }

      .component-table th,
      .component-table td {
        padding: 6px 4px;
      }

      .component-table input,
      .component-table select {
        font-size: 11px;
        padding: 4px 6px;
      }

      .actions {
        flex-direction: column;
      }

      .modal-content {
        width: 95%;
        margin: 5% auto;
      }

      .comparison-container {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .summary-cards {
        grid-template-columns: repeat(2, 1fr);
      }

      .materials-table {
        font-size: 10px;
      }

      .materials-table th,
      .materials-table td {
        padding: 4px 3px;
      }

      .materials-table .desc-col {
        max-width: 100px;
      }

      .materials-table .codigo-col {
        min-width: 60px;
      }

      .materials-table .qtde-col {
        min-width: 50px;
      }

      .comparison-actions {
        flex-direction: column;
        gap: 10px;
      }

      .approve-btn,
      .reject-btn {
        width: 100%;
        margin: 0;
      }
    }

    /* Estilos específicos para o modal de alteração de status */
    #statusChangeModal .form-group {
      margin-bottom: 20px;
    }

    #statusChangeModal .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #2c3e50;
    }

    #statusChangeModal select,
    #statusChangeModal textarea {
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    #statusChangeModal select:focus,
    #statusChangeModal textarea:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    #statusChangeModal .btn-warning {
      background: linear-gradient(135deg, #f39c12, #e67e22);
      border: none;
      color: white;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    #statusChangeModal .btn-warning:hover {
      background: linear-gradient(135deg, #e67e22, #d35400);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
    }

    /* Estilos para controle rápido de status */
    .status-change-item {
      grid-column: span 2;
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 15px;
    }

    .status-change-item label {
      color: #495057;
      font-weight: 600;
      margin-bottom: 10px;
      display: block;
    }

    .status-controls {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    #quickStatusSelect {
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    #quickStatusSelect:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    #quickChangeStatusBtn {
      transition: all 0.3s ease;
    }

    #quickChangeStatusBtn:enabled {
      opacity: 1;
      cursor: pointer;
    }

    #quickChangeStatusBtn:enabled:hover {
      background: #218838;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    #quickChangeStatusBtn:disabled {
      cursor: not-allowed;
    }

    #currentStatusDisplay {
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Responsividade para o controle de status */
    @media (max-width: 768px) {
      .status-change-item {
        grid-column: span 1;
      }
    }

    /* Estilos para controle de flags/etapas */
    .flags-control-item {
      grid-column: span 2;
      background: #e8f5e8;
      border: 3px solid #28a745;
      border-radius: 10px;
      padding: 15px;
    }

    .flags-control-item label {
      color: #155724;
      font-weight: 600;
      margin-bottom: 10px;
      display: block;
    }

    .flag-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      transition: all 0.3s ease;
      margin: 2px;
      position: relative;
    }

    .flag-button:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .flag-button.completed {
      background: #28a745;
      color: white;
    }

    .flag-button.pending {
      background: #ffc107;
      color: #212529;
    }

    .flag-button.blocked {
      background: #6c757d;
      color: white;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .flag-button.clickable {
      background: #007bff;
      color: white;
    }

    .flag-label {
      font-size: 11px;
      font-weight: 600;
      text-align: center;
      margin-top: 5px;
      color: #495057;
    }

    /* Responsividade para flags */
    @media (max-width: 768px) {
      .flags-control-item {
        grid-column: span 1;
      }

      .flag-button {
        width: 35px;
        height: 35px;
        font-size: 12px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-edit"></i> Alterar Ordem de Produção</h1>
      <div class="header-actions">
        <button class="btn btn-warning" onclick="diagnosticarOPAtual()" title="Verificar histórico de transferências da OP selecionada">
          <i class="fas fa-search"></i> Diagnóstico
        </button>
        <button class="btn btn-info" onclick="openRecalculateModal()">
          <i class="fas fa-sync-alt"></i> Recalcular por Revisão
        </button>
        <button class="btn btn-primary" onclick="window.location.href='ordens_producao.html'">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- Alerta de Status -->
      <div id="statusAlert" class="alert alert-warning" style="display: none;">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>Atenção:</strong> Apenas ordens de produção com status <strong>"Pendente"</strong> podem ser alteradas.
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-search"></i> Buscar Ordem de Produção
          </h3>
        </div>
        <div class="card-body">
          <div id="solicitacaoInfo" style="margin-bottom: 20px; font-weight: 500; color: #2c3e50;"></div>
          <div class="search-row">
            <input type="text" id="buscaProduto" placeholder="🔍 Buscar por código do produto..." autocomplete="off" oninput="filtrarOPsPorProduto()">
          </div>
          <div id="opList" class="op-list" style="display:none;"></div>
        </div>
      </div>

      <!-- Card para Controles da OP (Status + Flags) - SEMPRE VISÍVEL QUANDO OP SELECIONADA -->
      <div class="card" id="productionCard" style="display:none;">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-cog"></i> Ordem de Produção em Andamento
          </h3>
          <div class="status-badge" style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
            <i class="fas fa-play"></i> EM PRODUÇÃO
          </div>
        </div>
        <div class="card-body">
          <div class="production-info">
            <div class="info-grid">
              <div class="info-item">
                <label><i class="fas fa-hashtag"></i> Número da OP</label>
                <div id="prodNumeroOP" class="info-value"></div>
              </div>
              <div class="info-item">
                <label><i class="fas fa-box"></i> Produto</label>
                <div id="prodProduto" class="info-value"></div>
              </div>
              <div class="info-item">
                <label><i class="fas fa-calculator"></i> Quantidade</label>
                <div id="prodQuantidade" class="info-value"></div>
              </div>
              <div class="info-item">
                <label><i class="fas fa-calendar-alt"></i> Data de Entrega</label>
                <div id="prodDataEntrega" class="info-value"></div>
              </div>
              <div class="info-item">
                <label><i class="fas fa-flag"></i> Prioridade</label>
                <div id="prodPrioridade" class="info-value"></div>
              </div>
              <div class="info-item">
                <label><i class="fas fa-sticky-note"></i> Observações</label>
                <div id="prodObservacoes" class="info-value"></div>
              </div>

              <!-- CONTROLE DE STATUS DA OP -->
              <div class="info-item status-change-item" style="display: block !important; visibility: visible !important; background: #fff3cd !important; border: 3px solid #ffc107 !important; grid-column: span 2;">
                <label style="color: #856404 !important; font-size: 16px !important; font-weight: bold !important;">
                  <i class="fas fa-flag"></i> 🔄 ALTERAR STATUS DA OP
                </label>
                <div class="status-controls">
                  <select id="quickStatusSelect" style="width: 100%; padding: 12px; border: 3px solid #007bff; border-radius: 8px; font-size: 16px; margin-bottom: 15px; background: white;">
                    <option value="">📋 Selecione novo status...</option>
                    <option value="Pendente">⚠️ Pendente (Permite edição)</option>
                    <option value="Em Produção">✅ Em Produção</option>
                    <option value="Concluída">🏁 Concluída</option>
                    <option value="Cancelada">❌ Cancelada</option>
                  </select>
                  <button id="quickChangeStatusBtn" onclick="quickChangeStatus()" disabled
                          style="width: 100%; padding: 15px; background: #28a745; color: white; border: none; border-radius: 8px; font-weight: 600; cursor: pointer; opacity: 0.5; font-size: 16px;">
                    <i class="fas fa-sync-alt"></i> 🔄 ALTERAR STATUS AGORA
                  </button>
                  <div id="currentStatusDisplay" style="margin-top: 12px; padding: 10px 15px; background: #e9ecef; border-radius: 20px; text-align: center; font-weight: 600; font-size: 14px; border: 2px solid #6c757d;">
                    📊 Status Atual: <span id="currentStatusText" style="color: #007bff; font-weight: bold;">Selecione uma OP</span>
                  </div>
                </div>
              </div>

              <!-- CONTROLE DE FLAGS/ETAPAS DA OP -->
              <div class="info-item flags-control-item" style="display: block !important; visibility: visible !important; background: #e8f5e8 !important; border: 3px solid #28a745 !important; grid-column: span 2;">
                <label style="color: #155724 !important; font-size: 16px !important; font-weight: bold !important;">
                  <i class="fas fa-tasks"></i> 🎯 CONTROLE DE ETAPAS DA OP
                </label>
                <div id="flagsControls" style="margin-top: 15px;">
                  <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center; justify-content: center;">
                    <!-- Os flags serão inseridos aqui via JavaScript -->
                  </div>
                  <div style="margin-top: 10px; padding: 8px; background: rgba(255,255,255,0.7); border-radius: 6px; text-align: center; font-size: 12px; color: #155724;">
                    💡 Clique nos círculos para alterar o status das etapas
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="materials-section">
            <div class="section-header">
              <h4><i class="fas fa-boxes"></i> Materiais Necessários</h4>
            </div>
            <div id="materialsTable"></div>
          </div>

          <div class="extra-materials-section">
            <div class="section-header">
              <h4><i class="fas fa-plus-circle"></i> Materiais Extras</h4>
              <button onclick="openExtraMaterialModal()" class="add-material-btn">
                <i class="fas fa-plus"></i> Adicionar Material Extra
              </button>
            </div>
            <div id="extraMaterialsTable"></div>
          </div>
        </div>
      </div>

      <!-- Card para OPs Pendentes (Edição Completa) -->
      <div class="card" id="editCard" style="display:none;">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-edit"></i> Dados da Ordem de Produção
          </h3>
        </div>
        <div class="card-body">
          <form id="editOpForm">
            <div class="form-group">
              <label for="numeroOP"><i class="fas fa-hashtag"></i> Número da OP</label>
              <input type="text" id="numeroOP" readonly>
            </div>
            <div class="form-group">
              <label for="produto"><i class="fas fa-box"></i> Produto</label>
              <input type="text" id="produto" readonly>
            </div>
            <div class="form-group">
              <label for="quantidade"><i class="fas fa-calculator"></i> Quantidade Necessária</label>
              <input type="number" id="quantidade" min="0.001" step="0.001" required>
            </div>
            <div class="form-group">
              <label for="dataEntrega"><i class="fas fa-calendar-alt"></i> Data de Entrega</label>
              <input type="date" id="dataEntrega" required>
            </div>
            <div class="form-group">
              <label for="prioridade"><i class="fas fa-flag"></i> Prioridade</label>
              <select id="prioridade" required>
                <option value="Baixa">Baixa</option>
                <option value="Média">Média</option>
                <option value="Alta">Alta</option>
              </select>
            </div>
            <div class="form-group">
              <label for="observacoes"><i class="fas fa-sticky-note"></i> Observações</label>
              <textarea id="observacoes" placeholder="Observações adicionais..."></textarea>
            </div>
            <div class="componentes-section">
              <label><i class="fas fa-cogs"></i> Componentes e Consumo</label>
              <div id="componentList" class="component-list"></div>
              <button type="button" class="add-btn" onclick="addComponent()">
                <i class="fas fa-plus"></i> Adicionar Componente
              </button>
            </div>
            <div class="actions">
              <button type="submit" class="btn btn-success">
                <i class="fas fa-save"></i> Salvar Alterações
              </button>
              <button type="button" class="btn btn-danger" onclick="window.location.reload()">
                <i class="fas fa-times"></i> Cancelar
              </button>
            </div>
          </form>
        </div>
      </div>



    </div>
  </div>

  <!-- Modal de Recálculo por Revisão -->
  <div id="recalculateModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-sync-alt"></i> Recalcular OPs por Revisão de Estrutura
        </h2>
        <span class="close" onclick="closeRecalculateModal()">&times;</span>
      </div>

      <div class="modal-body">
        <div class="op-selection">
          <h3><i class="fas fa-clipboard-list"></i> Ordem de Produção Selecionada</h3>
          <div id="currentOPInfo" style="padding: 20px; background: #f8f9fa; border-radius: 10px; border: 2px solid #e9ecef; margin-bottom: 25px;">
            <div style="color: #7f8c8d; text-align: center;">
              <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
              Nenhuma OP selecionada. Busque e selecione uma OP primeiro.
            </div>
          </div>
        </div>

        <div class="revision-selection" style="margin-bottom: 25px;">
          <h3><i class="fas fa-code-branch"></i> Selecione a Revisão da Estrutura</h3>
          <p style="color: #7f8c8d; margin-bottom: 20px; line-height: 1.5;">
            Escolha qual revisão da estrutura deve ser usada para recalcular a OP atual:
          </p>
          <div id="revisionList"></div>
        </div>

        <div class="comparison-section" id="comparisonSection">
          <div class="comparison-header">
            <div class="comparison-title">
              <i class="fas fa-balance-scale"></i> Comparação: Estrutura Atual vs Nova Revisão
            </div>
            <div class="comparison-subtitle">Analise as diferenças antes de aprovar o recálculo</div>
          </div>

          <div id="comparisonContent"></div>

          <!-- Seção de Análise de Cascata -->
          <div id="cascadeAnalysisSection" style="display: none;" class="cascade-section">
            <div class="cascade-header">
              <h4 class="cascade-title">
                <i class="fas fa-exclamation-triangle"></i> Impacto em Cascata Detectado
              </h4>
              <p class="cascade-subtitle">
                Esta alteração afeta Sub-Produtos (SP) que são usados em outras OPs abertas.
                O recálculo será expandido automaticamente para manter a consistência.
              </p>
            </div>
            <div id="cascadeContent"></div>
          </div>

          <div class="comparison-actions">
            <button class="approve-btn" onclick="approveRecalculation()">
              <i class="fas fa-check"></i> Aprovar Recálculo
            </button>
            <button class="reject-btn" onclick="rejectComparison()">
              <i class="fas fa-times"></i> Cancelar
            </button>
          </div>
        </div>

      <div class="comparison-section" id="comparisonSection">
        <div class="comparison-header">
          <div class="comparison-title">📊 Comparação: Estrutura Atual vs Nova Revisão</div>
          <div class="comparison-subtitle">Analise as diferenças antes de aprovar o recálculo</div>
        </div>

        <div id="comparisonContent"></div>

        <!-- Seção de Análise de Cascata -->
        <div id="cascadeAnalysisSection" style="display: none; margin-top: 20px;">
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 15px;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ Impacto em Cascata Detectado</h4>
            <p style="color: #856404; margin: 0; font-size: 14px;">
              Esta alteração afeta Sub-Produtos (SP) que são usados em outras OPs abertas.
              O recálculo será expandido automaticamente para manter a consistência.
            </p>
          </div>
          <div id="cascadeContent"></div>
        </div>

        <div class="comparison-actions">
          <button class="approve-btn" onclick="approveRecalculation()">✅ Aprovar Recálculo</button>
          <button class="reject-btn" onclick="rejectComparison()">❌ Cancelar</button>
        </div>
      </div>

      <div class="progress-section" style="display: none;">
        <h3>3. Progresso do Recálculo</h3>
        <div class="progress-bar">
          <div id="progressFill" class="progress-fill" style="width: 0%;"></div>
        </div>
        <div id="progressText" style="text-align: center; margin-top: 10px; color: #666;"></div>
        <div id="progressLog" style="margin-top: 15px; max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
      </div>

      <div class="modal-actions" style="margin-top: 20px; text-align: right;">
        <button id="showComparison" onclick="showComparison()" class="save-btn" disabled>Ver Comparação</button>
        <button id="startRecalculate" onclick="startRecalculation()" class="save-btn" disabled style="display: none;">Recalcular OP</button>
        <button onclick="closeRecalculateModal()" class="cancel-btn">Cancelar</button>
      </div>
    </div>
  </div>

  <!-- Modal de Materiais Extras -->
  <div id="extraMaterialsModal" class="modal">
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-plus-circle"></i> Adicionar Materiais Extras
        </h2>
        <span class="close" onclick="closeExtraMaterialsModal()">&times;</span>
      </div>

      <div class="modal-body">
        <!-- Informações da OP -->
        <div id="extraMaterialsOPInfo" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <!-- Será preenchido dinamicamente -->
        </div>

        <!-- Busca de Produtos -->
        <div class="search-section" style="margin-bottom: 20px;">
          <h4><i class="fas fa-search"></i> Buscar Produto</h4>
          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <input type="text" id="productSearch" placeholder="Digite o código ou descrição do produto..."
                   style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
            <button onclick="searchProducts()" class="btn btn-primary">
              <i class="fas fa-search"></i> Buscar
            </button>
          </div>
          <div id="productSearchResults" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; display: none;">
            <!-- Resultados da busca -->
          </div>
        </div>

        <!-- Produto Selecionado -->
        <div id="selectedProductSection" style="display: none; margin-bottom: 20px;">
          <h4><i class="fas fa-check-circle"></i> Produto Selecionado</h4>
          <div id="selectedProductInfo" style="background: #e8f5e8; padding: 15px; border-radius: 8px; border: 1px solid #28a745;">
            <!-- Informações do produto selecionado -->
          </div>
        </div>

        <!-- Configuração da Quantidade -->
        <div id="quantitySection" style="display: none; margin-bottom: 20px;">
          <h4><i class="fas fa-calculator"></i> Quantidade e Justificativa</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
            <div>
              <label for="extraQuantity">Quantidade:</label>
              <input type="number" id="extraQuantity" min="0.001" step="0.001"
                     style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
            </div>
            <div>
              <label for="extraUnit">Unidade:</label>
              <input type="text" id="extraUnit" readonly
                     style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
            </div>
          </div>
          <div>
            <label for="extraJustification">Justificativa (obrigatória):</label>
            <textarea id="extraJustification" placeholder="Explique o motivo da adição deste material extra..."
                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; min-height: 80px; resize: vertical;"></textarea>
          </div>
        </div>

        <!-- Verificação de Estoque -->
        <div id="stockVerification" style="display: none; margin-bottom: 20px;">
          <h4><i class="fas fa-warehouse"></i> Verificação de Estoque</h4>
          <div id="stockInfo" style="padding: 15px; border-radius: 8px;">
            <!-- Informações de estoque -->
          </div>
        </div>
      </div>

      <div class="modal-actions">
        <button id="addExtraMaterialBtn" onclick="addExtraMaterial()" class="save-btn" disabled>
          <i class="fas fa-plus"></i> Adicionar Material
        </button>
        <button onclick="closeExtraMaterialsModal()" class="cancel-btn">Cancelar</button>
      </div>
    </div>
  </div>

  <!-- Modal de Alteração de Status - Versão Simplificada -->
  <div id="statusChangeModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 10000; padding: 20px; box-sizing: border-box;">
    <div style="background: white; max-width: 600px; margin: 50px auto; border-radius: 10px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">

      <!-- Header -->
      <div style="border-bottom: 2px solid #eee; padding-bottom: 15px; margin-bottom: 25px;">
        <h2 style="margin: 0; color: #2c3e50; display: flex; align-items: center; gap: 10px;">
          <i class="fas fa-edit" style="color: #f39c12;"></i>
          Alterar Status da OP
        </h2>
        <button onclick="closeStatusChangeModal()" style="position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
      </div>

      <!-- Aviso -->
      <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #856404;">
          <i class="fas fa-exclamation-triangle"></i> Atenção
        </h4>
        <p style="margin: 0; color: #856404; font-size: 14px;">
          Esta funcionalidade permite alterar o status de uma OP. Use com cuidado.
        </p>
      </div>

      <!-- Info da OP -->
      <div id="currentOPInfo" style="margin-bottom: 25px;">
        <!-- Será preenchido via JavaScript -->
      </div>

      <!-- Novo Status -->
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">
          <i class="fas fa-flag"></i> Novo Status:
        </label>
        <select id="newStatus" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
          <option value="">Selecione o novo status...</option>
          <option value="Pendente">Pendente (Permite edição completa)</option>
          <option value="Em Produção">Em Produção (Permite materiais extras)</option>
          <option value="Concluída">Concluída (Somente leitura)</option>
          <option value="Cancelada">Cancelada (Somente leitura)</option>
        </select>
      </div>

      <!-- Motivo -->
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">
          <i class="fas fa-comment"></i> Motivo da Alteração:
        </label>
        <textarea id="statusChangeReason" placeholder="Descreva o motivo da alteração de status..."
                  style="width: 100%; height: 80px; padding: 12px; border: 2px solid #ddd; border-radius: 6px; resize: vertical; font-family: inherit; font-size: 14px;"></textarea>
      </div>

      <!-- Aviso dinâmico -->
      <div id="statusChangeWarning" style="display: none; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
        <h5 style="margin: 0 0 10px 0; color: #721c24;">
          <i class="fas fa-exclamation-circle"></i> Aviso Importante
        </h5>
        <div id="warningText" style="color: #721c24; font-size: 14px;"></div>
      </div>

      <!-- Botões -->
      <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
        <button onclick="closeStatusChangeModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 600;">
          Cancelar
        </button>
        <button id="confirmStatusChange" onclick="confirmStatusChange()" disabled style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 600; opacity: 0.5;">
          <i class="fas fa-check"></i> Confirmar Alteração
        </button>
      </div>

    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, doc, updateDoc, addDoc, Timestamp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let ordensProducao = [];
    let produtos = [];
    let produtosMap = {};
    let opSelecionada = null;
    let estruturas = [];
    let selectedOPs = [];
    let selectedRevision = null;
    let cascadeAnalysis = null;

    // Função para determinar a unidade correta baseada no produto
    function getCorrectUnit(op) {
      const produto = produtosMap[op.produtoId];

      // Se o produto tem unidade definida, usar ela
      if (produto && produto.unidade) {
        return produto.unidade;
      }

      // Se a OP tem unidade definida, verificar se faz sentido
      if (op.unidade) {
        // Para produtos acabados (PA), geralmente é UN ou PC
        if (produto && produto.tipo === 'PA') {
          // Se está como KG mas é produto acabado, provavelmente deveria ser UN
          if (op.unidade === 'KG' && produto.descricao &&
              (produto.descricao.includes('ESTEIRA') ||
               produto.descricao.includes('EQUIPAMENTO') ||
               produto.descricao.includes('MÁQUINA'))) {
            return 'UN';
          }
        }
        return op.unidade;
      }

      // Padrão baseado no tipo de produto
      if (produto) {
        switch (produto.tipo) {
          case 'PA': // Produto Acabado
            return 'UN';
          case 'SP': // Sub-Produto
            return 'UN';
          case 'MP': // Matéria Prima
            return 'KG';
          default:
            return 'UN';
        }
      }

      return 'UN'; // Padrão
    }

    window.onload = async function() {
      try {
        const [opsSnap, produtosSnap, estruturasSnap] = await Promise.all([
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estruturas"))
        ]);
        ordensProducao = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtosMap = Object.fromEntries(produtos.map(p => [p.id, p]));
      } catch (error) {
        alert("Erro ao carregar dados: " + error.message);
      }
    };

    window.filtrarOPsPorProduto = function() {
      const termo = document.getElementById('buscaProduto').value.trim().toUpperCase();
      const opList = document.getElementById('opList');
      const statusAlert = document.getElementById('statusAlert');

      if (!termo) {
        opList.style.display = 'none';
        statusAlert.style.display = 'none';
        return;
      }

      // Filtra produtos
      const produtosFiltrados = produtos.filter(p => p.codigo?.toUpperCase().includes(termo));
      if (produtosFiltrados.length === 0) {
        opList.innerHTML = '<div class="op-item">❌ Nenhum produto encontrado.</div>';
        opList.style.display = 'block';
        statusAlert.style.display = 'none';
        return;
      }

      // Filtra OPs abertas relacionadas
      let opsRelacionadas = [];
      produtosFiltrados.forEach(prod => {
        opsRelacionadas = opsRelacionadas.concat(ordensProducao.filter(op =>
          op.produtoId === prod.id && op.status !== 'Concluída' && op.status !== 'Cancelada'
        ));
      });

      if (opsRelacionadas.length === 0) {
        opList.innerHTML = '<div class="op-item">❌ Nenhuma OP aberta para este produto.</div>';
        opList.style.display = 'block';
        statusAlert.style.display = 'none';
        return;
      }

      // Verificar se há OPs pendentes
      const opsPendentes = opsRelacionadas.filter(op => op.status === 'Pendente');
      const hasNonPendingOPs = opsRelacionadas.some(op => op.status !== 'Pendente');

      // Mostrar alerta se há OPs não pendentes
      if (hasNonPendingOPs) {
        statusAlert.style.display = 'block';
      } else {
        statusAlert.style.display = 'none';
      }

      opList.innerHTML = opsRelacionadas.map(op => {
        const prod = produtosMap[op.produtoId];
        const numero = op.numeroOP || op.numero || 'Sem número';
        const status = op.status;
        const quantidade = op.quantidade;
        const dataEntrega = op.dataEntrega ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : '';

        // Determinar classe de status
        let statusClass = 'status-pendente';
        let isDisabled = false;

        switch(status.toLowerCase()) {
          case 'pendente':
            statusClass = 'status-pendente';
            break;
          case 'em produção':
          case 'em_producao':
            statusClass = 'status-em-producao';
            isDisabled = false; // Permitir seleção para materiais extras
            break;
          case 'concluída':
          case 'concluida':
            statusClass = 'status-concluida';
            isDisabled = true;
            break;
          case 'cancelada':
            statusClass = 'status-cancelada';
            isDisabled = true;
            break;
          default:
            statusClass = 'status-pendente';
            isDisabled = true;
        }

        const itemClass = isDisabled ? 'op-item disabled' : 'op-item';
        const clickAction = isDisabled ? '' : `onclick='window.selecionarOP("${op.id}")'`;

        // Ícones diferentes para cada tipo de ação
        let statusIcon = '✅';
        let actionText = '';
        if (isDisabled) {
          statusIcon = '🔒';
        } else if (status.toLowerCase() === 'pendente') {
          statusIcon = '✏️';
          actionText = 'Clique para editar';
        } else if (status.toLowerCase() === 'em produção' || status.toLowerCase() === 'em_producao') {
          statusIcon = '➕';
          actionText = 'Clique para adicionar materiais extras';
        }

        return `
          <div class='${itemClass}' ${clickAction}>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <strong>${statusIcon} ${numero}</strong> - ${prod?.codigo || ''} - ${prod?.descricao || ''}
                <br>
                <small>Qtde: ${quantidade} | Entrega: ${dataEntrega}</small>
              </div>
              <span class="status ${statusClass}">${status}</span>
            </div>
            ${isDisabled ?
              '<small style="color: #721c24; margin-top: 5px; display: block;"><i class="fas fa-lock"></i> OP não pode ser alterada</small>' :
              actionText ? `<small style="color: #28a745; margin-top: 5px; display: block;"><i class="fas fa-info-circle"></i> ${actionText}</small>` : ''
            }
          </div>
        `;
      }).join('');
      opList.style.display = 'block';
    };

    window.selecionarOP = async function(opId) {
      console.log('🔍 DEBUG: Tentando selecionar OP com ID:', opId);

      opSelecionada = ordensProducao.find(op => op.id === opId);
      if (!opSelecionada) {
        console.error('❌ OP não encontrada no array ordensProducao');
        console.log('📊 Total de OPs carregadas:', ordensProducao.length);
        console.log('🔍 IDs disponíveis:', ordensProducao.map(op => op.id));
        return;
      }

      console.log('✅ OP selecionada:', {
        id: opSelecionada.id,
        numero: opSelecionada.numeroOP || opSelecionada.numero,
        status: opSelecionada.status,
        produtoId: opSelecionada.produtoId
      });

      document.getElementById('opList').style.display = 'none';
      document.getElementById('statusAlert').style.display = 'none';

      // Atualizar botão de alteração de status
      updateStatusChangeButton();

      // Verificar status da OP e mostrar interface apropriada
      console.log('🔍 Status da OP:', opSelecionada.status);

      if (opSelecionada.status === 'Pendente') {
        // OP Pendente - Mostrar AMBAS as interfaces (edição + controles)
        console.log('📝 OP Pendente - Mostrando editCard + productionCard');
        document.getElementById('editCard').style.display = 'block';
        document.getElementById('productionCard').style.display = 'block';

        console.log('✅ Cards configurados:', {
          editCard: document.getElementById('editCard').style.display,
          productionCard: document.getElementById('productionCard').style.display
        });

        // Forçar visibilidade do productionCard
        const productionCard = document.getElementById('productionCard');
        productionCard.style.display = 'block';
        productionCard.style.visibility = 'visible';
        productionCard.style.opacity = '1';

        const prod = produtosMap[opSelecionada.produtoId];
        document.getElementById('numeroOP').value = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
        document.getElementById('produto').value = `${prod?.codigo || ''} - ${prod?.descricao || ''}`;
        document.getElementById('quantidade').value = opSelecionada.quantidade;
        document.getElementById('dataEntrega').value = opSelecionada.dataEntrega ? new Date(opSelecionada.dataEntrega.seconds * 1000).toISOString().split('T')[0] : '';
        document.getElementById('prioridade').value = opSelecionada.prioridade || 'Média';
        document.getElementById('observacoes').value = opSelecionada.observacoes || '';
        renderComponentes(opSelecionada.materiaisNecessarios || []);

        // Buscar solicitação de compra vinculada
        const solicitacaoInfo = document.getElementById('solicitacaoInfo');
        solicitacaoInfo.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando solicitação de compra...';
        try {
          const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
          const q = query(collection(db, 'solicitacoesCompra'), where('opId', '==', opSelecionada.id));
          const snap = await getDocs(q);
          if (!snap.empty) {
            const docData = snap.docs[0].data();
            solicitacaoInfo.innerHTML = `<i class="fas fa-shopping-cart"></i> <strong>Solicitação de Compra:</strong> ${docData.numero || snap.docs[0].id}`;
          } else {
            solicitacaoInfo.innerHTML = '<i class="fas fa-info-circle"></i> Nenhuma solicitação de compra gerada para esta OP.';
          }
        } catch (e) {
          solicitacaoInfo.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Erro ao buscar solicitação de compra.';
        }

        // Preencher também o productionCard para OPs Pendentes (para mostrar os controles)
        // Reutilizar a variável prod já declarada acima
        document.getElementById('prodNumeroOP').textContent = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
        document.getElementById('prodProduto').textContent = `${prod?.codigo || ''} - ${prod?.descricao || ''}`;
        document.getElementById('prodQuantidade').textContent = `${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}`;
        document.getElementById('prodDataEntrega').textContent = opSelecionada.dataEntrega ?
          new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Não definida';
        document.getElementById('prodPrioridade').textContent = opSelecionada.prioridade || 'Normal';
        document.getElementById('prodObservacoes').textContent = opSelecionada.observacoes || 'Nenhuma observação';

      } else if (opSelecionada.status === 'Em Produção' || opSelecionada.status === 'em_producao') {
        // OP em Produção - Mostrar interface de visualização + materiais extras
        document.getElementById('editCard').style.display = 'none';
        document.getElementById('productionCard').style.display = 'block';

        const prod = produtosMap[opSelecionada.produtoId];
        document.getElementById('prodNumeroOP').textContent = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
        document.getElementById('prodProduto').textContent = `${prod?.codigo || ''} - ${prod?.descricao || ''}`;
        document.getElementById('prodQuantidade').textContent = `${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}`;
        document.getElementById('prodDataEntrega').textContent = opSelecionada.dataEntrega ? new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';
        document.getElementById('prodPrioridade').textContent = opSelecionada.prioridade || 'Média';
        document.getElementById('prodObservacoes').textContent = opSelecionada.observacoes || 'Sem observações';

        // Renderizar materiais originais e extras (com verificação de segurança)
        try {
          renderOriginalMaterials(opSelecionada.materiaisNecessarios || []);
          renderExtraMaterials(opSelecionada.materiaisExtras || []);
        } catch (error) {
          console.warn('⚠️ Erro ao renderizar materiais:', error.message);
        }

        // Carregar histórico de materiais extras (com verificação de segurança)
        try {
          loadExtraMaterialsHistory();
        } catch (error) {
          console.warn('⚠️ Erro ao carregar histórico de materiais extras:', error.message);
        }

      } else {
        // OP com outros status - Mostrar apenas visualização e controle de status
        document.getElementById('editCard').style.display = 'none';
        document.getElementById('productionCard').style.display = 'block';

        const prod = produtosMap[opSelecionada.produtoId];
        document.getElementById('prodNumeroOP').textContent = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
        document.getElementById('prodProduto').textContent = `${prod?.codigo || ''} - ${prod?.descricao || ''}`;
        document.getElementById('prodQuantidade').textContent = `${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}`;
        document.getElementById('prodDataEntrega').textContent = opSelecionada.dataEntrega ?
          new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Não definida';
        document.getElementById('prodPrioridade').textContent = opSelecionada.prioridade || 'Normal';
        document.getElementById('prodObservacoes').textContent = opSelecionada.observacoes || 'Nenhuma observação';

        // Ocultar seção de materiais extras para status que não permitem
        const materialsSection = document.querySelector('.materials-section');
        if (materialsSection) {
          if (opSelecionada.status === 'Concluída' || opSelecionada.status === 'Cancelada') {
            materialsSection.style.display = 'none';
          } else {
            materialsSection.style.display = 'block';
          }
        }
      }
    };

    // Funções para renderizar materiais no card de produção
    window.renderOriginalMaterials = function(materiais) {
      const container = document.getElementById('originalMaterialsList');

      if (!container) {
        console.warn('⚠️ Elemento originalMaterialsList não encontrado no DOM');
        return;
      }

      if (!materiais || materiais.length === 0) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>Nenhum material na estrutura original</p>
          </div>
        `;
        return;
      }

      const materialsHTML = materiais.map(material => {
        const produto = produtosMap[material.produtoId];
        return `
          <div class="material-item" style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 5px; margin-bottom: 8px; border: 1px solid #dee2e6;">
            <div>
              <strong>${produto?.codigo || 'N/A'}</strong> - ${produto?.descricao || 'N/A'}
              <br>
              <small style="color: #6c757d;">Tipo: ${produto?.tipo || 'N/A'}</small>
            </div>
            <div style="text-align: right;">
              <div style="font-weight: 600;">${material.quantidade.toFixed(3)} ${material.unidade || 'UN'}</div>
              <small style="color: #6c757d;">Unitário: ${material.quantidadeUnitaria?.toFixed(3) || '0.000'}</small>
            </div>
          </div>
        `;
      }).join('');

      container.innerHTML = materialsHTML;
    };

    window.renderExtraMaterials = function(materiaisExtras) {
      const container = document.getElementById('extraMaterialsList');

      if (!container) {
        console.warn('⚠️ Elemento extraMaterialsList não encontrado no DOM');
        return;
      }

      if (!materiaisExtras || materiaisExtras.length === 0) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>Nenhum material extra adicionado ainda</p>
          </div>
        `;
        return;
      }

      const materialsHTML = materiaisExtras.map((material, index) => {
        const produto = produtosMap[material.produtoId];
        return `
          <div class="material-item" style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #e8f5e8; border-radius: 5px; margin-bottom: 8px; border: 1px solid #28a745;">
            <div>
              <strong>${produto?.codigo || 'N/A'}</strong> - ${produto?.descricao || 'N/A'}
              <br>
              <small style="color: #6c757d;">
                Tipo: ${produto?.tipo || 'N/A'} |
                Adicionado: ${material.dataAdicao ? new Date(material.dataAdicao.seconds * 1000).toLocaleDateString() : 'N/A'} |
                Por: ${material.adicionadoPor || 'N/A'}
              </small>
              ${material.justificativa ? `<br><small style="color: #495057; font-style: italic;">"${material.justificativa}"</small>` : ''}
            </div>
            <div style="text-align: right;">
              <div style="font-weight: 600; color: #28a745;">${material.quantidade.toFixed(3)} ${material.unidade || 'UN'}</div>
              <button onclick="removeExtraMaterial(${index})" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-top: 5px;">
                <i class="fas fa-trash"></i> Remover
              </button>
            </div>
          </div>
        `;
      }).join('');

      container.innerHTML = materialsHTML;
    };

    window.renderComponentes = function(componentes) {
      const compList = document.getElementById('componentList');

      if (componentes.length === 0) {
        compList.innerHTML = `
          <div style="text-align: center; padding: 40px; color: #7f8c8d;">
            <i class="fas fa-plus-circle" style="font-size: 48px; margin-bottom: 15px; color: #3498db;"></i>
            <div style="font-size: 16px; margin-bottom: 10px;">Nenhum componente adicionado</div>
            <div style="font-size: 14px;">Clique em "Adicionar Componente" para começar</div>
          </div>
        `;
        return;
      }

      let tableHTML = `
        <table class="component-table">
          <thead>
            <tr>
              <th style="width: 120px;">Código</th>
              <th style="width: 200px;">Descrição</th>
              <th style="width: 250px;">Produto</th>
              <th style="width: 100px;">Quantidade</th>
              <th style="width: 80px;">Unidade</th>
              <th style="width: 80px;">Ações</th>
            </tr>
          </thead>
          <tbody>
      `;

      componentes.forEach((comp, idx) => {
        const prod = produtosMap[comp.produtoId];
        tableHTML += `
          <tr>
            <td>
              <input type="text" class="busca-comp" placeholder="Código..." value="${prod?.codigo || ''}" oninput="window.buscarComponentePorCodigo(this, ${idx})">
            </td>
            <td>
              <input type="text" class="desc-comp desc-readonly" value="${prod?.descricao || ''}" readonly>
            </td>
            <td>
              <select onchange="window.atualizarComponenteSelect(this, ${idx})">
                ${produtos.map(p => `<option value="${p.id}" ${p.id === comp.produtoId ? 'selected' : ''}>${p.codigo} - ${p.descricao}</option>`).join('')}
              </select>
            </td>
            <td>
              <input type="number" min="0.001" step="0.001" value="${comp.quantidade}">
            </td>
            <td>
              <select class="unidade">
                <option value="PC" ${comp.unidade === 'PC' ? 'selected' : ''}>PC</option>
                <option value="KG" ${comp.unidade === 'KG' ? 'selected' : ''}>KG</option>
              </select>
            </td>
            <td style="text-align: center;">
              <button type="button" class="remove-btn" onclick="window.removerComponente(${idx})" title="Remover componente">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;

      compList.innerHTML = tableHTML;
    };

    window.addComponent = function() {
      // Obter componentes atuais
      const componentes = [];
      const compList = document.getElementById('componentList');
      const table = compList.querySelector('.component-table tbody');

      if (table) {
        Array.from(table.rows).forEach((row, i) => {
          const select = row.querySelector('select');
          const qtdInput = row.querySelector('input[type="number"]');
          const unidadeSelect = row.querySelector('select.unidade');

          if (select && qtdInput && unidadeSelect) {
            componentes.push({
              produtoId: select.value,
              quantidade: parseFloat(qtdInput.value) || 1,
              unidade: unidadeSelect.value
            });
          }
        });
      }

      // Adicionar novo componente vazio
      componentes.push({
        produtoId: '',
        quantidade: 1,
        unidade: 'KG'
      });

      // Re-renderizar a tabela
      renderComponentes(componentes);
    };

    window.buscarComponentePorCodigo = function(input, idx) {
      const valor = input.value.trim().toUpperCase();
      const row = input.closest('tr');
      const select = row.querySelector('select');
      const descInput = row.querySelector('.desc-comp');

      if (!valor) {
        descInput.value = '';
        return;
      }

      const option = Array.from(select.options).find(opt =>
        opt.textContent.toUpperCase().includes(valor)
      );

      if (option) {
        select.value = option.value;
        const prod = produtosMap[option.value];
        if (prod) {
          descInput.value = prod.descricao;
        }
      } else {
        descInput.value = '';
      }
    };

    window.atualizarComponenteSelect = function(select, idx) {
      const row = select.closest('tr');
      const codigoInput = row.querySelector('.busca-comp');
      const descInput = row.querySelector('.desc-comp');
      const prod = produtosMap[select.value];

      if (prod) {
        codigoInput.value = prod.codigo;
        descInput.value = prod.descricao;
      } else {
        codigoInput.value = '';
        descInput.value = '';
      }
    };

    window.removerComponente = function(idx) {
      // Obter componentes atuais
      const componentes = [];
      const compList = document.getElementById('componentList');
      const table = compList.querySelector('.component-table tbody');

      if (table) {
        Array.from(table.rows).forEach((row, i) => {
          if (i !== idx) { // Pular o índice a ser removido
            const select = row.querySelector('select');
            const qtdInput = row.querySelector('input[type="number"]');
            const unidadeSelect = row.querySelector('select.unidade');

            if (select && qtdInput && unidadeSelect) {
              componentes.push({
                produtoId: select.value,
                quantidade: parseFloat(qtdInput.value) || 1,
                unidade: unidadeSelect.value
              });
            }
          }
        });
      }

      // Re-renderizar a tabela sem o componente removido
      renderComponentes(componentes);
    };

    document.getElementById('editOpForm').addEventListener('submit', async function(event) {
      event.preventDefault();

      if (!opSelecionada) {
        alert('❌ Nenhuma OP selecionada. Selecione uma OP primeiro.');
        return;
      }

      // Validação de status com flexibilidade
      const statusNormalizado = opSelecionada.status?.trim?.() || '';
      const statusesPermitidos = ['Pendente', 'pendente', 'PENDENTE'];

      if (!statusesPermitidos.includes(statusNormalizado)) {
        const confirmarAlteracao = confirm(`⚠️ ATENÇÃO - STATUS DA OP\n\nStatus atual: "${opSelecionada.status}"\n\nNormalmente apenas OPs "Pendente" podem ser alteradas.\n\nDeseja continuar mesmo assim?\n\n⚠️ CUIDADO: Alterar OPs em produção pode causar inconsistências!`);

        if (!confirmarAlteracao) {
          return;
        }
      }

      const novaQuantidade = parseFloat(document.getElementById('quantidade').value);
      const dataEntrega = document.getElementById('dataEntrega').value;
      const prioridade = document.getElementById('prioridade').value;
      const observacoes = document.getElementById('observacoes').value;

      // Validações
      if (novaQuantidade <= 0) {
        alert('❌ Quantidade deve ser maior que zero!');
        return;
      }

      if (!dataEntrega) {
        alert('❌ Data de entrega é obrigatória!');
        return;
      }

      // Confirmar alteração se quantidade mudou significativamente
      const quantidadeOriginal = opSelecionada.quantidade;
      const percentualMudanca = Math.abs((novaQuantidade - quantidadeOriginal) / quantidadeOriginal) * 100;

      if (percentualMudanca > 20) {
        if (!confirm(`ATENÇÃO: A quantidade está sendo alterada em ${percentualMudanca.toFixed(1)}%.\n\nIsso irá:\n• Recalcular todos os materiais necessários\n• Atualizar solicitações de compra vinculadas\n• Recalcular empenhos de estoque\n\nConfirma a alteração?`)) {
          return;
        }
      }

      try {
        // Mostrar progresso
        const btnSalvar = document.querySelector('.save-btn');
        const textoOriginal = btnSalvar.textContent;
        btnSalvar.textContent = 'Processando...';
        btnSalvar.disabled = true;

        // 1. Buscar estrutura do produto para recalcular materiais
        const estruturaSnap = await getDocs(collection(db, "estruturasProduto"));
        const estrutura = estruturaSnap.docs.find(doc => doc.data().produtoId === opSelecionada.produtoId);

        let materiaisRecalculados = [];

        if (estrutura) {
          // Recalcular materiais baseado na estrutura e nova quantidade
          btnSalvar.textContent = 'Recalculando materiais...';
          materiaisRecalculados = await recalcularMateriaisNecessarios(estrutura.data(), novaQuantidade);
        } else {
          // Usar componentes manuais se não há estrutura
          const compList = document.getElementById('componentList');
          const table = compList.querySelector('.component-table tbody');

          if (table) {
            Array.from(table.rows).forEach(row => {
              const select = row.querySelector('select');
              const qtdInput = row.querySelector('input[type="number"]');
              const unidadeSelect = row.querySelector('select.unidade');

              if (select && qtdInput && unidadeSelect && select.value) {
                const qtdUnitaria = parseFloat(qtdInput.value) || 0;
                const qtdTotal = qtdUnitaria * novaQuantidade;

                materiaisRecalculados.push({
                  produtoId: select.value,
                  quantidade: qtdTotal,
                  quantidadeUnitaria: qtdUnitaria,
                  unidade: unidadeSelect.value
                });
              }
            });
          }
        }

        // 2. Verificar disponibilidade de estoque para novos materiais
        btnSalvar.textContent = 'Verificando estoque...';
        const verificacaoEstoque = await verificarDisponibilidadeEstoque(materiaisRecalculados, opSelecionada.armazemProducaoId);

        // 3. Liberar empenhos antigos
        btnSalvar.textContent = 'Atualizando empenhos...';
        if (opSelecionada.materiaisNecessarios) {
          await liberarEmpenhosAntigos(opSelecionada.materiaisNecessarios, opSelecionada.armazemProducaoId);
        }

        // 4. Criar novos empenhos
        let novosEmpenhos = await criarNovosEmpenhos(materiaisRecalculados, opSelecionada.armazemProducaoId);

        // 4.1. 🔄 PRESERVAR HISTÓRICO DE TRANSFERÊNCIAS
        btnSalvar.textContent = 'Preservando histórico de transferências...';
        novosEmpenhos = await preservarHistoricoTransferencias(
          opSelecionada.id,
          opSelecionada.materiaisNecessarios || [],
          novosEmpenhos
        );

        // 5. Atualizar ordem de produção
        btnSalvar.textContent = 'Salvando alterações...';
        const opRef = doc(db, "ordensProducao", opSelecionada.id);
        await updateDoc(opRef, {
          quantidade: novaQuantidade,
          unidade: getCorrectUnit(opSelecionada),
          dataEntrega: new Date(dataEntrega),
          prioridade,
          observacoes,
          materiaisNecessarios: novosEmpenhos,
          dataAlteracao: new Date(),
          alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          historicoAlteracoes: [...(opSelecionada.historicoAlteracoes || []), {
            data: new Date(),
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
            alteracoes: {
              quantidadeAnterior: quantidadeOriginal,
              quantidadeNova: novaQuantidade,
              materiaisRecalculados: materiaisRecalculados.length
            }
          }]
        });

        // 6. Atualizar solicitações de compra vinculadas
        btnSalvar.textContent = 'Atualizando solicitações...';
        await atualizarSolicitacoesVinculadas(opSelecionada.id, materiaisRecalculados);

        // 7. Registrar log de alteração
        await registrarLogAlteracao(opSelecionada, {
          quantidadeAnterior: quantidadeOriginal,
          quantidadeNova: novaQuantidade,
          materiaisRecalculados: materiaisRecalculados.length,
          verificacaoEstoque
        });

        // Mostrar notificação de sucesso melhorada
        showSuccessNotification(`✅ Ordem de produção atualizada com sucesso!\n\n📊 Resumo das alterações:\n• Quantidade: ${quantidadeOriginal} → ${novaQuantidade}\n• Materiais recalculados: ${materiaisRecalculados.length}\n• Empenhos atualizados: ${novosEmpenhos.length}\n• Solicitações atualizadas: Sim`);

        // Aguardar um pouco antes de recarregar para mostrar a notificação
        setTimeout(() => {
          window.location.reload();
        }, 3000);

      } catch (error) {
        console.error('Erro ao atualizar OP:', error);

        const errorMessage = `❌ Erro ao salvar alterações\n\nOP: ${opSelecionada?.numero || 'N/A'}\nErro: ${error.message}\n\nTente novamente ou contate o suporte.`;
        showErrorNotification(errorMessage);

        // Restaurar botão
        const btnSalvar = document.querySelector('.save-btn');
        if (btnSalvar) {
          btnSalvar.textContent = 'Salvar Alterações';
          btnSalvar.disabled = false;
        }
      }
    });



    // ===== FUNÇÃO PARA PRESERVAR HISTÓRICO DE TRANSFERÊNCIAS =====
    async function preservarHistoricoTransferencias(opId, materiaisAtuais, novosMateriais) {
      try {
        console.log('🔄 Preservando histórico de transferências para OP:', opId);

        // Buscar transferências já realizadas para esta OP
        const transferenciasQuery = query(
          collection(db, "transferenciasArmazem"),
          where("ordemProducaoId", "==", opId),
          where("tipo", "==", "OP")
        );

        const transferenciasSnapshot = await getDocs(transferenciasQuery);
        const transferenciasRealizadas = {};

        // Agrupar transferências por produto
        transferenciasSnapshot.docs.forEach(doc => {
          const transfer = doc.data();
          if (!transferenciasRealizadas[transfer.produtoId]) {
            transferenciasRealizadas[transfer.produtoId] = 0;
          }
          transferenciasRealizadas[transfer.produtoId] += transfer.quantidade;
        });

        console.log('📊 Transferências encontradas:', transferenciasRealizadas);

        // Aplicar transferências aos novos materiais
        const materiaisComHistorico = novosMateriais.map(novoMaterial => {
          const transferido = transferenciasRealizadas[novoMaterial.produtoId] || 0;

          if (transferido > 0) {
            console.log(`🔄 Preservando ${transferido} transferidos para produto ${novoMaterial.produtoId}`);
            return {
              ...novoMaterial,
              saldoReservado: transferido,
              historicoPreservado: true
            };
          }

          return novoMaterial;
        });

        return materiaisComHistorico;

      } catch (error) {
        console.error('❌ Erro ao preservar histórico de transferências:', error);
        // Em caso de erro, retornar materiais originais
        return novosMateriais;
      }
    }

    // ===== FUNÇÃO DE DIAGNÓSTICO DO HISTÓRICO =====
    window.diagnosticarHistoricoTransferencias = async function(opId) {
      try {
        console.log('🔍 Diagnóstico do histórico de transferências para OP:', opId);

        // Buscar OP atual
        const opDoc = await getDoc(doc(db, "ordensProducao", opId));
        if (!opDoc.exists()) {
          console.error('❌ OP não encontrada:', opId);
          return;
        }

        const op = opDoc.data();
        console.log('📋 OP encontrada:', {
          numero: op.numero || op.numeroOP,
          materiaisCount: op.materiaisNecessarios?.length || 0
        });

        // Buscar transferências
        const transferenciasQuery = query(
          collection(db, "transferenciasArmazem"),
          where("ordemProducaoId", "==", opId),
          where("tipo", "==", "OP")
        );

        const transferenciasSnapshot = await getDocs(transferenciasQuery);
        console.log('📊 Transferências encontradas:', transferenciasSnapshot.docs.length);

        // Analisar cada material
        if (op.materiaisNecessarios) {
          op.materiaisNecessarios.forEach(material => {
            const transferenciasDoMaterial = transferenciasSnapshot.docs.filter(doc =>
              doc.data().produtoId === material.produtoId
            );

            const totalTransferido = transferenciasDoMaterial.reduce((total, doc) =>
              total + (doc.data().quantidade || 0), 0
            );

            console.log(`📦 Material ${material.produtoId}:`, {
              saldoReservado: material.saldoReservado || 0,
              totalTransferido: totalTransferido,
              historicoPreservado: material.historicoPreservado || false,
              status: (material.saldoReservado || 0) === totalTransferido ? '✅ OK' : '⚠️ DIVERGÊNCIA'
            });
          });
        }

        return {
          op: op,
          transferencias: transferenciasSnapshot.docs.map(doc => doc.data())
        };

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
      }
    };

    // ===== FUNÇÃO PARA DIAGNOSTICAR OP ATUAL =====
    async function diagnosticarOPAtual() {
      if (!opSelecionada) {
        alert('❌ Nenhuma OP selecionada!\n\nSelecione uma OP primeiro para fazer o diagnóstico.');
        return;
      }

      try {
        console.log('🔍 Iniciando diagnóstico da OP:', opSelecionada.id);

        const resultado = await diagnosticarHistoricoTransferencias(opSelecionada.id);

        if (resultado) {
          let relatorio = `🔍 DIAGNÓSTICO DE TRANSFERÊNCIAS\n\n`;
          relatorio += `OP: ${opSelecionada.numero || opSelecionada.numeroOP}\n`;
          relatorio += `ID: ${opSelecionada.id}\n`;
          relatorio += `Materiais: ${resultado.op.materiaisNecessarios?.length || 0}\n`;
          relatorio += `Transferências: ${resultado.transferencias.length}\n\n`;

          if (resultado.op.materiaisNecessarios) {
            relatorio += `📦 ANÁLISE POR MATERIAL:\n\n`;

            resultado.op.materiaisNecessarios.forEach(material => {
              const transferenciasDoMaterial = resultado.transferencias.filter(t =>
                t.produtoId === material.produtoId
              );

              const totalTransferido = transferenciasDoMaterial.reduce((total, t) =>
                total + (t.quantidade || 0), 0
              );

              const status = (material.saldoReservado || 0) === totalTransferido ? '✅' : '⚠️';

              relatorio += `${status} Produto: ${material.produtoId}\n`;
              relatorio += `   Saldo Reservado: ${material.saldoReservado || 0}\n`;
              relatorio += `   Total Transferido: ${totalTransferido}\n`;
              relatorio += `   Histórico Preservado: ${material.historicoPreservado ? 'Sim' : 'Não'}\n\n`;
            });
          }

          alert(relatorio);
        }

      } catch (error) {
        console.error('❌ Erro no diagnóstico:', error);
        alert(`❌ Erro no diagnóstico:\n\n${error.message}`);
      }
    }

    // Funções auxiliares para o processo de alteração
    async function recalcularMateriaisNecessarios(estrutura, novaQuantidade) {
      const materiais = [];

      for (const componente of estrutura.componentes) {
        const quantidadeNecessaria = componente.quantidade * novaQuantidade;

        // Buscar dados do produto componente
        const produtoSnap = await getDocs(collection(db, "produtos"));
        const produto = produtoSnap.docs.find(doc => doc.id === componente.componentId);

        if (produto) {
          materiais.push({
            produtoId: componente.componentId,
            quantidade: quantidadeNecessaria,
            quantidadeUnitaria: componente.quantidade,
            unidade: componente.unidade || produto.data().unidade || 'PC',
            codigo: produto.data().codigo,
            descricao: produto.data().descricao
          });
        }
      }

      return materiais;
    }

    async function verificarDisponibilidadeEstoque(materiais, armazemId) {
      const verificacao = {
        totalItens: materiais.length,
        itensDisponiveis: 0,
        itensInsuficientes: 0,
        detalhes: []
      };

      for (const material of materiais) {
        try {
          const estoqueSnap = await getDocs(collection(db, "estoques"));
          const estoque = estoqueSnap.docs.find(doc =>
            doc.data().produtoId === material.produtoId &&
            doc.data().armazemId === armazemId
          );

          const saldoDisponivel = estoque ? estoque.data().saldo : 0;
          const suficiente = saldoDisponivel >= material.quantidade;

          if (suficiente) {
            verificacao.itensDisponiveis++;
          } else {
            verificacao.itensInsuficientes++;
          }

          verificacao.detalhes.push({
            produtoId: material.produtoId,
            codigo: material.codigo,
            necessario: material.quantidade,
            disponivel: saldoDisponivel,
            suficiente
          });

        } catch (error) {
          console.error(`Erro ao verificar estoque do produto ${material.produtoId}:`, error);
        }
      }

      return verificacao;
    }

    async function liberarEmpenhosAntigos(materiaisAntigos, armazemId) {
      for (const material of materiaisAntigos) {
        if (material.quantidadeReservada > 0) {
          try {
            // Buscar estoque atual
            const estoqueSnap = await getDocs(collection(db, "estoques"));
            const estoqueDoc = estoqueSnap.docs.find(doc =>
              doc.data().produtoId === material.produtoId &&
              doc.data().armazemId === armazemId
            );

            if (estoqueDoc) {
              const estoqueAtual = estoqueDoc.data();
              const novoSaldoReservado = Math.max(0, (estoqueAtual.saldoReservado || 0) - material.quantidadeReservada);

              await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                saldoReservado: novoSaldoReservado
              });
            }
          } catch (error) {
            console.error(`Erro ao liberar empenho do produto ${material.produtoId}:`, error);
          }
        }
      }
    }

    async function criarNovosEmpenhos(materiais, armazemId) {
      const empenhosAtualizados = [];

      for (const material of materiais) {
        try {
          // Buscar estoque atual
          const estoqueSnap = await getDocs(collection(db, "estoques"));
          const estoqueDoc = estoqueSnap.docs.find(doc =>
            doc.data().produtoId === material.produtoId &&
            doc.data().armazemId === armazemId
          );

          let saldoDisponivel = 0;
          let quantidadeReservada = 0;

          if (estoqueDoc) {
            const estoqueAtual = estoqueDoc.data();
            saldoDisponivel = estoqueAtual.saldo || 0;
            quantidadeReservada = Math.min(saldoDisponivel, material.quantidade);

            // Atualizar reserva no estoque
            if (quantidadeReservada > 0) {
              const novoSaldoReservado = (estoqueAtual.saldoReservado || 0) + quantidadeReservada;
              await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                saldoReservado: novoSaldoReservado
              });
            }
          }

          const necessidade = Math.max(0, material.quantidade - quantidadeReservada);

          empenhosAtualizados.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            quantidadeUnitaria: material.quantidadeUnitaria,
            unidade: material.unidade,
            saldoEstoque: saldoDisponivel,
            quantidadeReservada,
            necessidade,
            tipo: 'MP'
          });

        } catch (error) {
          console.error(`Erro ao criar empenho do produto ${material.produtoId}:`, error);

          // Adicionar mesmo com erro para não perder o material
          empenhosAtualizados.push({
            produtoId: material.produtoId,
            quantidade: material.quantidade,
            quantidadeUnitaria: material.quantidadeUnitaria,
            unidade: material.unidade,
            saldoEstoque: 0,
            quantidadeReservada: 0,
            necessidade: material.quantidade,
            tipo: 'MP',
            erro: error.message
          });
        }
      }

      return empenhosAtualizados;
    }

    async function atualizarSolicitacoesVinculadas(opId, materiaisRecalculados) {
      try {
        // Buscar solicitações de compra vinculadas a esta OP
        const solicitacoesSnap = await getDocs(collection(db, "solicitacoesCompra"));
        const solicitacoesVinculadas = solicitacoesSnap.docs.filter(doc =>
          doc.data().opId === opId || doc.data().origem === `OP-${opId}`
        );

        for (const solicitacaoDoc of solicitacoesVinculadas) {
          const solicitacao = solicitacaoDoc.data();

          // Recalcular itens da solicitação baseado nos novos materiais
          const itensAtualizados = [];

          for (const material of materiaisRecalculados) {
            if (material.necessidade > 0) {
              // Buscar dados do produto
              const produtoSnap = await getDocs(collection(db, "produtos"));
              const produto = produtoSnap.docs.find(doc => doc.id === material.produtoId);

              if (produto) {
                const produtoData = produto.data();
                itensAtualizados.push({
                  produtoId: material.produtoId,
                  codigo: produtoData.codigo,
                  descricao: produtoData.descricao,
                  quantidade: material.necessidade,
                  unidade: material.unidade,
                  observacoes: `Recalculado por alteração da OP`
                });
              }
            }
          }

          // Atualizar solicitação se há itens
          if (itensAtualizados.length > 0) {
            await updateDoc(doc(db, "solicitacoesCompra", solicitacaoDoc.id), {
              itens: itensAtualizados,
              dataAlteracao: new Date(),
              alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
              observacoes: (solicitacao.observacoes || '') + `\n[${new Date().toLocaleString()}] Recalculada por alteração da OP`
            });
          }
        }

      } catch (error) {
        console.error('Erro ao atualizar solicitações vinculadas:', error);
        // Não falhar o processo principal por erro nas solicitações
      }
    }

    async function registrarLogAlteracao(opOriginal, alteracoes) {
      try {
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'ALTERACAO_OP',
          opId: opOriginal.id,
          numeroOP: opOriginal.numeroOP || opOriginal.numero,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataHora: new Date(),
          alteracoes: {
            quantidadeAnterior: alteracoes.quantidadeAnterior,
            quantidadeNova: alteracoes.quantidadeNova,
            percentualMudanca: ((alteracoes.quantidadeNova - alteracoes.quantidadeAnterior) / alteracoes.quantidadeAnterior * 100).toFixed(2),
            materiaisRecalculados: alteracoes.materiaisRecalculados,
            verificacaoEstoque: alteracoes.verificacaoEstoque
          },
          dadosOriginais: {
            quantidade: opOriginal.quantidade,
            materiaisNecessarios: opOriginal.materiaisNecessarios?.length || 0,
            status: opOriginal.status
          }
        });
      } catch (error) {
        console.error('Erro ao registrar log de alteração:', error);
        // Não falhar o processo principal por erro no log
      }
    }

    // ===== FUNÇÕES DO MODAL DE RECÁLCULO POR REVISÃO =====

    window.openRecalculateModal = function() {
      if (!opSelecionada) {
        alert('❌ Selecione uma OP primeiro através da busca.');
        return;
      }

      // Verificar se a OP pode ser recalculada (apenas status Pendente)
      if (opSelecionada.status !== 'Pendente') {
        alert(`❌ RECÁLCULO NÃO PERMITIDO\n\nApenas ordens de produção com status "Pendente" podem ser recalculadas.\n\nStatus atual: ${opSelecionada.status}\n\n💡 Para recalcular esta OP, ela deve estar com status "Pendente".`);
        return;
      }

      selectedRevision = null;
      document.getElementById('recalculateModal').style.display = 'block';
      showCurrentOPInfo();
      loadRevisionsForCurrentOP();
      document.getElementById('showComparison').disabled = true;
    };

    window.closeRecalculateModal = function() {
      document.getElementById('recalculateModal').style.display = 'none';

      // Reset all sections
      document.querySelector('.op-selection').style.display = 'block';
      document.querySelector('.revision-selection').style.display = 'block';
      document.querySelector('.comparison-section').style.display = 'none';
      document.getElementById('cascadeAnalysisSection').style.display = 'none';
      document.querySelector('.progress-section').style.display = 'none';

      // Reset buttons
      document.getElementById('showComparison').style.display = 'inline-block';
      document.getElementById('startRecalculate').style.display = 'none';
      document.getElementById('showComparison').textContent = 'Ver Comparação';
      document.getElementById('showComparison').disabled = true;

      // Reset progress
      document.getElementById('progressFill').style.width = '0%';
      document.getElementById('progressText').textContent = '';
      document.getElementById('progressLog').innerHTML = '';

      // Clear selections
      selectedRevision = null;
      cascadeAnalysis = null;

      // Clear visual selections
      document.querySelectorAll('.revision-item.selected').forEach(item => {
        item.classList.remove('selected');
      });
    };

    function showCurrentOPInfo() {
      const currentOPInfo = document.getElementById('currentOPInfo');

      if (!opSelecionada) {
        currentOPInfo.innerHTML = '<div style="color: #666;">Nenhuma OP selecionada. Busque e selecione uma OP primeiro.</div>';
        return;
      }

      const produto = produtosMap[opSelecionada.produtoId];
      const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';
      const dataEntrega = opSelecionada.dataEntrega ?
        new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';
      const revisaoAtual = opSelecionada.revisaoEstrutura || 0;

      currentOPInfo.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <div>
            <strong style="color: #1761a0;">Número da OP:</strong><br>
            <span style="font-size: 16px; font-weight: 600;">${numeroOP}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Produto:</strong><br>
            <span>${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Quantidade:</strong><br>
            <span style="font-size: 16px; font-weight: 600;">${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Status:</strong><br>
            <span style="padding: 2px 8px; background: #e3f2fd; border-radius: 12px; font-size: 12px;">${opSelecionada.status}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Entrega:</strong><br>
            <span>${dataEntrega}</span>
          </div>
          <div>
            <strong style="color: #1761a0;">Revisão Atual:</strong><br>
            <span style="padding: 2px 8px; background: #f3e5f5; border-radius: 12px; font-size: 12px;">REV${String(revisaoAtual).padStart(3, '0')}</span>
          </div>
        </div>
      `;
    }

    function loadRevisionsForCurrentOP() {
      const revisionList = document.getElementById('revisionList');

      if (!opSelecionada) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma OP selecionada.</div>';
        return;
      }

      // Buscar todas as revisões do produto da OP atual
      const revisoesProduct = estruturas
        .filter(est => est.produtoPaiId === opSelecionada.produtoId)
        .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

      if (revisoesProduct.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma revisão de estrutura encontrada para este produto.</div>';
        return;
      }

      const produto = produtosMap[opSelecionada.produtoId];
      let html = `<h4 style="margin: 15px 0 10px 0; color: #1761a0;">${produto?.codigo} - ${produto?.descricao}</h4>`;

      revisoesProduct.forEach(revisao => {
        const revNumber = String(revisao.revisaoAtual || 0).padStart(3, '0');
        const dataAlteracao = revisao.dataUltimaAlteracao ?
          new Date(revisao.dataUltimaAlteracao).toLocaleString() :
          'Data não disponível';
        const usuario = revisao.usuarioUltimaAlteracao?.nome || 'Usuário não identificado';
        const componentes = revisao.componentes?.length || 0;
        const operacoes = revisao.operacoes?.length || 0;
        const isCurrentRevision = (revisao.revisaoAtual || 0) === (opSelecionada.revisaoEstrutura || 0);

        html += `
          <div class="revision-item ${isCurrentRevision ? 'current-revision' : ''}" ${!isCurrentRevision ? `onclick="selectRevisionForCurrentOP('${revisao.id}')"` : ''}>
            <div class="revision-header">
              <span class="revision-number">REV${revNumber}</span>
              <span class="revision-date">${dataAlteracao}</span>
              ${isCurrentRevision ? '<span style="background: #4caf50; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">EM USO</span>' : ''}
            </div>
            <div class="revision-details">
              <strong>Usuário:</strong> ${usuario}<br>
              <strong>Componentes:</strong> ${componentes} | <strong>Operações:</strong> ${operacoes}
              ${isCurrentRevision ? '<br><em style="color: #4caf50; font-weight: 600;">Esta é a revisão atualmente utilizada pela OP</em>' : ''}
            </div>
            ${revisao.motivoRevisao ? `<div class="revision-changes"><strong>Motivo:</strong> ${revisao.motivoRevisao}</div>` : ''}
          </div>
        `;
      });

      revisionList.innerHTML = html;
    }

    function loadOPsForRecalculation() {
      const opGrid = document.getElementById('opGrid');

      // Filtrar OPs abertas que têm estrutura
      const opsComEstrutura = ordensProducao.filter(op => {
        if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

        // Verificar se existe estrutura para o produto
        const temEstrutura = estruturas.some(est => est.produtoPaiId === op.produtoId);
        return temEstrutura;
      });

      if (opsComEstrutura.length === 0) {
        opGrid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; color: #666; padding: 20px;">Nenhuma OP aberta com estrutura encontrada.</div>';
        return;
      }

      opGrid.innerHTML = opsComEstrutura.map(op => {
        const produto = produtosMap[op.produtoId];
        const numeroOP = op.numeroOP || op.numero || 'Sem número';
        const dataEntrega = op.dataEntrega ? new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

        return `
          <div class="op-card" onclick="toggleOPSelection('${op.id}')">
            <div class="op-number">${numeroOP}</div>
            <div class="op-product">${produto?.codigo || ''} - ${produto?.descricao || ''}</div>
            <div class="op-status">Status: ${op.status} | Qtde: ${op.quantidade} | Entrega: ${dataEntrega}</div>
          </div>
        `;
      }).join('');
    }

    window.toggleOPSelection = function(opId) {
      const opCard = event.currentTarget;
      const index = selectedOPs.indexOf(opId);

      if (index > -1) {
        selectedOPs.splice(index, 1);
        opCard.classList.remove('selected');
      } else {
        selectedOPs.push(opId);
        opCard.classList.add('selected');
      }

      // Carregar revisões quando pelo menos uma OP for selecionada
      if (selectedOPs.length > 0) {
        loadRevisionsForSelectedOPs();
      } else {
        document.getElementById('revisionList').innerHTML = '';
        selectedRevision = null;
      }

      updateRecalculateButton();
    };

    function loadRevisionsForSelectedOPs() {
      const revisionList = document.getElementById('revisionList');

      // Obter produtos únicos das OPs selecionadas
      const produtosUnicos = [...new Set(selectedOPs.map(opId => {
        const op = ordensProducao.find(o => o.id === opId);
        return op?.produtoId;
      }).filter(Boolean))];

      if (produtosUnicos.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhum produto válido encontrado.</div>';
        return;
      }

      // Buscar todas as revisões dos produtos selecionados
      const revisoesDisponiveis = [];

      produtosUnicos.forEach(produtoId => {
        const produto = produtosMap[produtoId];
        const revisoesProduct = estruturas
          .filter(est => est.produtoPaiId === produtoId)
          .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));

        revisoesProduct.forEach(revisao => {
          revisoesDisponiveis.push({
            ...revisao,
            produtoInfo: produto
          });
        });
      });

      if (revisoesDisponiveis.length === 0) {
        revisionList.innerHTML = '<div style="color: #666;">Nenhuma revisão de estrutura encontrada para os produtos selecionados.</div>';
        return;
      }

      // Agrupar por produto
      const revisoesPorProduto = {};
      revisoesDisponiveis.forEach(rev => {
        const produtoId = rev.produtoPaiId;
        if (!revisoesPorProduto[produtoId]) {
          revisoesPorProduto[produtoId] = [];
        }
        revisoesPorProduto[produtoId].push(rev);
      });

      let html = '';
      Object.entries(revisoesPorProduto).forEach(([produtoId, revisoes]) => {
        const produto = produtosMap[produtoId];
        html += `<h4 style="margin: 15px 0 10px 0; color: #1761a0;">${produto?.codigo} - ${produto?.descricao}</h4>`;

        revisoes.forEach(revisao => {
          const revNumber = String(revisao.revisaoAtual || 0).padStart(3, '0');
          const dataAlteracao = revisao.dataUltimaAlteracao ?
            new Date(revisao.dataUltimaAlteracao).toLocaleString() :
            'Data não disponível';
          const usuario = revisao.usuarioUltimaAlteracao?.nome || 'Usuário não identificado';
          const componentes = revisao.componentes?.length || 0;
          const operacoes = revisao.operacoes?.length || 0;

          html += `
            <div class="revision-item" onclick="selectRevision('${revisao.id}', '${produtoId}')">
              <div class="revision-header">
                <span class="revision-number">REV${revNumber}</span>
                <span class="revision-date">${dataAlteracao}</span>
              </div>
              <div class="revision-details">
                <strong>Usuário:</strong> ${usuario}<br>
                <strong>Componentes:</strong> ${componentes} | <strong>Operações:</strong> ${operacoes}
              </div>
              ${revisao.motivoRevisao ? `<div class="revision-changes"><strong>Motivo:</strong> ${revisao.motivoRevisao}</div>` : ''}
            </div>
          `;
        });
      });

      revisionList.innerHTML = html;
    }

    window.selectRevisionForCurrentOP = function(revisionId) {
      const revisaoSelecionada = estruturas.find(e => e.id === revisionId);

      // Verificar se está tentando selecionar a mesma revisão que a OP já usa
      if (opSelecionada.revisaoEstrutura !== undefined &&
          opSelecionada.revisaoEstrutura !== null &&
          (revisaoSelecionada.revisaoAtual || 0) === opSelecionada.revisaoEstrutura) {
        showWarningNotification(
          `⚠️ Revisão já em uso\n\nA OP já está utilizando a revisão REV${String(opSelecionada.revisaoEstrutura).padStart(3, '0')}.\n\n❌ Selecione uma revisão diferente para comparar.`
        );
        return;
      }

      // Remover seleção anterior
      document.querySelectorAll('.revision-item').forEach(item => {
        item.classList.remove('selected');
      });

      // Selecionar nova revisão
      event.currentTarget.classList.add('selected');
      selectedRevision = {
        id: revisionId,
        produtoId: opSelecionada.produtoId,
        data: revisaoSelecionada
      };

      updateRecalculateButton();

      // Mostrar comparação automaticamente
      showComparison();
    };

    // Manter função antiga para compatibilidade
    window.selectRevision = function(revisionId, produtoId) {
      return selectRevisionForCurrentOP(revisionId);
    };

    function updateRecalculateButton() {
      const showComparisonBtn = document.getElementById('showComparison');
      const startRecalculateBtn = document.getElementById('startRecalculate');

      const hasSelections = opSelecionada && selectedRevision;

      showComparisonBtn.disabled = !hasSelections;

      if (hasSelections) {
        const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'OP';
        showComparisonBtn.textContent = `Ver Comparação - ${numeroOP}`;
      } else {
        showComparisonBtn.textContent = 'Ver Comparação';
      }
    }

    window.showComparison = async function() {
      if (!opSelecionada || !selectedRevision) {
        showWarningNotification('Selecione uma OP e uma revisão de estrutura.');
        return;
      }

      // Garantir que a OP atual esteja no array selectedOPs para o processo de aprovação
      if (selectedOPs.length === 0) {
        selectedOPs = [opSelecionada.id];
      }

      try {
        // Mostrar loading
        document.getElementById('showComparison').textContent = 'Calculando...';
        document.getElementById('showComparison').disabled = true;

        // Debug: Log das informações da OP
        console.log('=== DEBUG COMPARAÇÃO ===');
        console.log('OP Selecionada:', {
          id: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          produtoId: opSelecionada.produtoId,
          revisaoEstrutura: opSelecionada.revisaoEstrutura,
          materiaisNecessarios: opSelecionada.materiaisNecessarios?.length || 0
        });
        console.log('Revisão Selecionada:', {
          id: selectedRevision.id,
          revisaoAtual: selectedRevision.data.revisaoAtual,
          produtoPaiId: selectedRevision.data.produtoPaiId,
          componentes: selectedRevision.data.componentes?.length || 0
        });
        console.log('Total de estruturas disponíveis:', estruturas.length);
        console.log('Estruturas do produto:', estruturas.filter(e => e.produtoPaiId === opSelecionada.produtoId));

        // Buscar estrutura atual da OP com múltiplas estratégias
        let estruturaAtual = null;

        // Estratégia 1: Buscar pela revisão específica da OP (se diferente da selecionada)
        if (opSelecionada.revisaoEstrutura !== undefined && opSelecionada.revisaoEstrutura !== null) {
          estruturaAtual = estruturas.find(est =>
            est.produtoPaiId === opSelecionada.produtoId &&
            (est.revisaoAtual || 0) === opSelecionada.revisaoEstrutura &&
            est.id !== selectedRevision.id // Garantir que não seja a mesma revisão selecionada
          );

          if (estruturaAtual) {
            console.log(`Usando estrutura da OP: REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')}`);
          }
        }

        // Estratégia 2: Buscar estrutura diferente da selecionada
        if (!estruturaAtual) {
          const estruturasProduct = estruturas.filter(est =>
            est.produtoPaiId === opSelecionada.produtoId &&
            est.id !== selectedRevision.id // Excluir a revisão selecionada
          );

          if (estruturasProduct.length > 0) {
            // Ordenar por revisão (mais recente primeiro)
            estruturasProduct.sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));
            estruturaAtual = estruturasProduct[0];

            console.log(`Usando estrutura atual: REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')} vs Nova: REV${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')}`);
          }
        }

        // Estratégia 3: Criar estrutura baseada nos materiais atuais da OP
        if (!estruturaAtual && opSelecionada.materiaisNecessarios && opSelecionada.materiaisNecessarios.length > 0) {
          console.log('Criando estrutura virtual baseada nos materiais da OP');
          estruturaAtual = {
            id: 'virtual-' + opSelecionada.id,
            produtoPaiId: opSelecionada.produtoId,
            revisaoAtual: opSelecionada.revisaoEstrutura || 0,
            componentes: opSelecionada.materiaisNecessarios.map(mat => ({
              componentId: mat.produtoId,
              quantidade: mat.quantidadeUnitaria || mat.quantidade,
              unidade: mat.unidade || 'UN'
            })),
            virtual: true
          };
        }

        if (!estruturaAtual) {
          showErrorNotification(`Estrutura não encontrada para o produto ${produtosMap[opSelecionada.produtoId]?.codigo || opSelecionada.produtoId}.\n\nVerifique se existe uma estrutura cadastrada para este produto.`);
          document.getElementById('showComparison').textContent = 'Ver Comparação';
          document.getElementById('showComparison').disabled = false;
          return;
        }

        // Verificar se há diferenças entre as estruturas
        const diferencas = verificarDiferencasEstrutura(estruturaAtual, selectedRevision.data);

        if (!diferencas.temDiferencas) {
          showWarningNotification(
            `⚠️ Estruturas idênticas detectadas\n\n🔍 Comparação realizada:\n• Estrutura da OP: REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')}\n• Revisão selecionada: REV${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')}\n\n✅ As estruturas possuem componentes e quantidades idênticas.\n\n❌ Não há necessidade de recálculo.\n\n💡 Selecione uma revisão diferente para comparar.`
          );
          document.getElementById('showComparison').textContent = 'Ver Comparação';
          document.getElementById('showComparison').disabled = false;
          return;
        }

        // Mostrar seção de comparação
        document.getElementById('comparisonSection').style.display = 'block';

        // Gerar conteúdo da comparação lado a lado
        generateComparisonContent(estruturaAtual, selectedRevision.data, diferencas);

        // Mostrar botão de recálculo apenas se há diferenças
        const btnRecalcular = document.getElementById('startRecalculate');
        if (diferencas.temDiferencas) {
          btnRecalcular.style.display = 'inline-block';
          btnRecalcular.disabled = false;
        } else {
          btnRecalcular.style.display = 'none';
          btnRecalcular.disabled = true;
        }

        // Calcular comparação para a OP atual (processo original)
        const comparison = await calculateComparison(opSelecionada, selectedRevision.data);

        // Analisar impacto em cascata
        cascadeAnalysis = await analyzeCascadeImpact(opSelecionada, selectedRevision.data, comparison);

        // Mostrar análise de cascata se necessário
        if (cascadeAnalysis && cascadeAnalysis.hasImpact) {
          document.getElementById('cascadeAnalysisSection').style.display = 'block';
          renderCascadeAnalysis(cascadeAnalysis);
        }

        // Scroll para a comparação
        setTimeout(() => {
          document.getElementById('comparisonSection').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);

        // Restaurar botão
        document.getElementById('showComparison').textContent = 'Ver Comparação';
        document.getElementById('showComparison').disabled = false;

      } catch (error) {
        console.error('Erro ao calcular comparação:', error);
        showErrorNotification('Erro ao calcular comparação: ' + error.message);
        document.getElementById('showComparison').textContent = 'Ver Comparação';
        document.getElementById('showComparison').disabled = false;
      }
    };

    function verificarDiferencasEstrutura(estruturaAtual, novaEstrutura) {
      console.log('🔍 VERIFICANDO DIFERENÇAS ENTRE ESTRUTURAS');
      console.log('📋 Estrutura Atual:', estruturaAtual);
      console.log('📋 Nova Estrutura:', novaEstrutura);

      const materiaisAtuais = estruturaAtual.componentes || [];
      const materiaisNovos = novaEstrutura.componentes || [];

      console.log(`📊 Materiais Atuais: ${materiaisAtuais.length}, Materiais Novos: ${materiaisNovos.length}`);

      // Criar mapas para comparação eficiente (usando quantidades UNITÁRIAS)
      const mapAtual = new Map();
      const mapNovo = new Map();

      materiaisAtuais.forEach(mat => {
        const key = mat.componentId || mat.produtoId;
        const quantidade = parseFloat(mat.quantidadeUnitaria || mat.quantidade) || 0;
        mapAtual.set(key, {
          quantidade: quantidade,
          unidade: mat.unidade || 'UN'
        });
        console.log(`📦 Atual: ${produtosMap[key]?.codigo || key} = ${quantidade} ${mat.unidade || 'UN'}`);
      });

      materiaisNovos.forEach(mat => {
        const key = mat.componentId || mat.produtoId;
        const quantidade = parseFloat(mat.quantidade) || 0;
        mapNovo.set(key, {
          quantidade: quantidade,
          unidade: mat.unidade || 'UN'
        });
        console.log(`🆕 Novo: ${produtosMap[key]?.codigo || key} = ${quantidade} ${mat.unidade || 'UN'}`);
      });

      // Verificar diferenças
      let temDiferencas = false;
      const detalhes = {
        adicionados: [],
        removidos: [],
        alterados: []
      };

      // Verificar materiais removidos
      mapAtual.forEach((dados, produtoId) => {
        if (!mapNovo.has(produtoId)) {
          temDiferencas = true;
          detalhes.removidos.push(produtoId);
          console.log(`❌ Removido: ${produtosMap[produtoId]?.codigo || produtoId}`);
        }
      });

      // Verificar materiais adicionados e alterados
      mapNovo.forEach((dadosNovo, produtoId) => {
        if (!mapAtual.has(produtoId)) {
          temDiferencas = true;
          detalhes.adicionados.push(produtoId);
          console.log(`➕ Adicionado: ${produtosMap[produtoId]?.codigo || produtoId}`);
        } else {
          const dadosAtual = mapAtual.get(produtoId);
          // Comparar com tolerância para números de ponto flutuante
          const tolerance = 0.001;
          const qtdDiff = Math.abs(dadosAtual.quantidade - dadosNovo.quantidade);
          
          if (qtdDiff > tolerance || dadosAtual.unidade !== dadosNovo.unidade) {
            temDiferencas = true;
            detalhes.alterados.push({
              produtoId,
              quantidadeAtual: dadosAtual.quantidade,
              quantidadeNova: dadosNovo.quantidade,
              unidadeAtual: dadosAtual.unidade,
              unidadeNova: dadosNovo.unidade
            });
            console.log(`🔄 Alterado: ${produtosMap[produtoId]?.codigo || produtoId} - ${dadosAtual.quantidade} → ${dadosNovo.quantidade}`);
          }
        }
      });

      console.log('=== RESULTADO DA VERIFICAÇÃO ===');
      console.log('✅ Tem diferenças:', temDiferencas);
      console.log('📊 Detalhes:', detalhes);

      return {
        temDiferencas,
        detalhes,
        totalAtuais: materiaisAtuais.length,
        totalNovos: materiaisNovos.length
      };
    }

    function generateComparisonContent(estruturaAtual, novaEstrutura, diferencas = null) {
      const comparisonContent = document.getElementById('comparisonContent');

      console.log('🎨 Gerando conteúdo de comparação...');
      console.log('📋 Quantidade da OP:', opSelecionada.quantidade);

      // Preparar dados para comparação - MULTIPLICAR PELA QUANTIDADE DA OP
      const materiaisAtuais = (estruturaAtual.componentes || []).map(comp => ({
        ...comp,
        quantidadeTotal: (comp.quantidadeUnitaria || comp.quantidade || 0) * opSelecionada.quantidade,
        quantidadeUnitaria: comp.quantidadeUnitaria || comp.quantidade || 0
      }));

      const materiaisNovos = (novaEstrutura.componentes || []).map(comp => ({
        ...comp,
        quantidadeTotal: (comp.quantidade || 0) * opSelecionada.quantidade,
        quantidadeUnitaria: comp.quantidade || 0
      }));

      console.log('📊 Materiais Atuais processados:', materiaisAtuais.length);
      console.log('📊 Materiais Novos processados:', materiaisNovos.length);

      // Verificar se não há diferenças
      if (diferencas && !diferencas.temDiferencas) {
        const produto = produtosMap[opSelecionada.produtoId];
        const infoProduto = `${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}`;

        comparisonContent.innerHTML = `
          <!-- Informações do Produto -->
          <div style="text-align: center; margin-bottom: 25px; padding: 15px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 10px; border: 2px solid #90caf9;">
            <h4 style="margin: 0 0 5px 0; color: #1976d2; font-size: 18px;">
              <i class="fas fa-box"></i> ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'}
            </h4>
            <div style="color: #1565c0; font-weight: 600;">${infoProduto}</div>
            <div style="color: #1976d2; font-size: 14px; margin-top: 5px;">
              Quantidade: ${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}
            </div>
          </div>

          <!-- Mensagem de Estruturas Idênticas -->
          <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-radius: 15px; border: 2px solid #f39c12; margin: 20px 0;">
            <div style="font-size: 64px; color: #f39c12; margin-bottom: 20px;">
              <i class="fas fa-equals"></i>
            </div>
            <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 24px;">
              Estruturas Idênticas
            </h3>
            <div style="color: #856404; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
              A estrutura atual <strong>REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')}</strong> é idêntica à nova revisão <strong>REV${String(novaEstrutura.revisaoAtual || 0).padStart(3, '0')}</strong>
            </div>
            <div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px; color: #856404; font-size: 14px;">
              <i class="fas fa-info-circle"></i> Não há alterações para aplicar. Selecione uma revisão diferente.
            </div>
          </div>
        `;
        return;
      }

      // Calcular estatísticas
      const stats = calculateComparisonStats(materiaisAtuais, materiaisNovos);

      // Determinar títulos baseado no tipo de estrutura
      const tituloAtual = estruturaAtual.virtual
        ? `<i class="fas fa-cog"></i> Estrutura Atual da OP (Materiais Empenhados)`
        : `<i class="fas fa-history"></i> Estrutura Atual (REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')})`;

      const produto = produtosMap[opSelecionada.produtoId];
      const infoProduto = `${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}`;

      // Gerar HTML da comparação
      comparisonContent.innerHTML = `
        <!-- Informações do Produto -->
        <div style="text-align: center; margin-bottom: 25px; padding: 15px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 10px; border: 2px solid #90caf9;">
          <h4 style="margin: 0 0 5px 0; color: #1976d2; font-size: 18px;">
            <i class="fas fa-box"></i> ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'}
          </h4>
          <div style="color: #1565c0; font-weight: 600;">${infoProduto}</div>
          <div style="color: #1976d2; font-size: 14px; margin-top: 5px;">
            Quantidade: ${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}
          </div>
        </div>

        <div class="comparison-container">
          <!-- Lado Esquerdo - Estrutura Atual -->
          <div class="comparison-side old">
            <div class="side-title old">
              ${tituloAtual}
              ${estruturaAtual.virtual ? '<span style="font-size: 11px; background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 4px; margin-left: 8px;">VIRTUAL</span>' : ''}
            </div>

            <div class="summary-cards">
              <div class="summary-card old">
                <div class="summary-number">${stats.atual.total}</div>
                <div class="summary-label">Materiais Atuais</div>
              </div>
              <div class="summary-card old">
                <div class="summary-number">${stats.atual.removidos}</div>
                <div class="summary-label">Serão Removidos</div>
              </div>
              <div class="summary-card old">
                <div class="summary-number">${stats.atual.alterados}</div>
                <div class="summary-label">Serão Alterados</div>
              </div>
            </div>

            ${generateMaterialsTable(materiaisAtuais, materiaisNovos, 'atual')}
          </div>

          <!-- Lado Direito - Nova Estrutura -->
          <div class="comparison-side new">
            <div class="side-title new">
              <i class="fas fa-star"></i> Nova Revisão (REV${String(novaEstrutura.revisaoAtual || 0).padStart(3, '0')})
            </div>

            <div class="summary-cards">
              <div class="summary-card new">
                <div class="summary-number">${stats.nova.total}</div>
                <div class="summary-label">Novos Materiais</div>
              </div>
              <div class="summary-card new">
                <div class="summary-number">${stats.nova.adicionados}</div>
                <div class="summary-label">Serão Adicionados</div>
              </div>
              <div class="summary-card new">
                <div class="summary-number">${stats.nova.alterados}</div>
                <div class="summary-label">Serão Alterados</div>
              </div>
            </div>

            ${generateMaterialsTable(materiaisNovos, materiaisAtuais, 'nova')}
          </div>
        </div>
      `;
    }

    function calculateComparisonStats(materiaisAtuais, materiaisNovos) {
      const stats = {
        atual: { total: materiaisAtuais.length, removidos: 0, alterados: 0 },
        nova: { total: materiaisNovos.length, adicionados: 0, alterados: 0 }
      };

      // Mapear materiais por ID para comparação (usando mesma lógica da verificação)
      const atuaisMap = new Map(materiaisAtuais.map(m => [m.componentId || m.produtoId, m]));
      const novosMap = new Map(materiaisNovos.map(m => [m.componentId || m.produtoId, m]));

      // Contar removidos e alterados (atual)
      materiaisAtuais.forEach(material => {
        const key = material.componentId || material.produtoId;
        const novoMaterial = novosMap.get(key);
        if (!novoMaterial) {
          stats.atual.removidos++;
        } else if (parseFloat(material.quantidade) !== parseFloat(novoMaterial.quantidade)) {
          stats.atual.alterados++;
        }
      });

      // Contar adicionados e alterados (nova)
      materiaisNovos.forEach(material => {
        const key = material.componentId || material.produtoId;
        const atualMaterial = atuaisMap.get(key);
        if (!atualMaterial) {
          stats.nova.adicionados++;
        } else if (parseFloat(material.quantidade) !== parseFloat(atualMaterial.quantidade)) {
          stats.nova.alterados++;
        }
      });

      return stats;
    }

    function generateMaterialsTable(materiais, materiaisComparacao, tipo) {
      if (materiais.length === 0) {
        return `
          <div style="text-align: center; padding: 40px; color: #7f8c8d;">
            <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px;"></i>
            <div>Nenhum material encontrado</div>
          </div>
        `;
      }

      console.log(`🔧 Gerando tabela ${tipo} com ${materiais.length} materiais`);

      // Mapear materiais de comparação por ID
      const comparacaoMap = new Map(materiaisComparacao.map(m => [m.componentId || m.produtoId, m]));

      let tableHTML = `
        <table class="materials-table">
          <thead>
            <tr>
              <th class="codigo-col">Código</th>
              <th class="desc-col">Descrição</th>
              <th class="tipo-col">Tipo</th>
              <th class="qtde-col">Qtde Total</th>
              <th class="qtde-col">Qtde Unit.</th>
              <th class="unidade-col">Un</th>
              <th class="status-col">Status</th>
            </tr>
          </thead>
          <tbody>
      `;

      materiais.forEach(material => {
        const key = material.componentId || material.produtoId;
        const produto = produtosMap[key];
        const materialComparacao = comparacaoMap.get(key);

        // Usar quantidades corretas
        const quantidadeTotal = material.quantidadeTotal || (material.quantidadeUnitaria || material.quantidade || 0) * opSelecionada.quantidade;
        const quantidadeUnitaria = material.quantidadeUnitaria || material.quantidade || 0;

        let statusClass = '';
        let statusText = '';
        let rowClass = '';

        if (!materialComparacao) {
          // Material não existe na outra estrutura
          if (tipo === 'atual') {
            statusClass = 'quantity-decrease';
            statusText = 'REMOVER';
            rowClass = 'material-removed';
          } else {
            statusClass = 'quantity-increase';
            statusText = 'NOVO';
            rowClass = 'material-new';
          }
        } else {
          // Comparar quantidades unitárias
          const qtdUnitariaAtual = quantidadeUnitaria;
          const qtdUnitariaComparacao = materialComparacao.quantidadeUnitaria || materialComparacao.quantidade || 0;
          const tolerance = 0.001;

          if (Math.abs(qtdUnitariaAtual - qtdUnitariaComparacao) > tolerance) {
            // Quantidade alterada
            statusClass = 'quantity-change';
            const diff = qtdUnitariaAtual - qtdUnitariaComparacao;
            statusText = diff > 0 ? `+${diff.toFixed(3)}` : `${diff.toFixed(3)}`;
            if (diff > 0) statusClass += ' quantity-increase';
            else statusClass += ' quantity-decrease';
          } else {
            // Sem alteração
            statusClass = 'quantity-same';
            statusText = 'IGUAL';
          }
        }

        console.log(`📊 ${produto?.codigo}: Total=${quantidadeTotal.toFixed(3)}, Unit=${quantidadeUnitaria.toFixed(3)}, Status=${statusText}`);

        tableHTML += `
          <tr class="${rowClass}">
            <td class="codigo-col">${produto?.codigo || 'N/A'}</td>
            <td class="desc-col" title="${produto?.descricao || 'N/A'}">${produto?.descricao || 'N/A'}</td>
            <td class="tipo-col"><span style="background: #e2e8f0; padding: 1px 4px; border-radius: 3px; font-size: 10px; font-weight: 600;">${produto?.tipo || 'N/A'}</span></td>
            <td class="qtde-col" style="font-weight: 600; color: #2c3e50;">${quantidadeTotal.toFixed(3)}</td>
            <td class="qtde-col" style="font-size: 11px; color: #6c757d;">${quantidadeUnitaria.toFixed(3)}</td>
            <td class="unidade-col">${material.unidade || produto?.unidade || 'UN'}</td>
            <td class="status-col"><span class="${statusClass}">${statusText}</span></td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;

      return tableHTML;
    }

    async function calculateComparison(op, novaRevisao) {
      const produto = produtosMap[op.produtoId];

      // Materiais atuais da OP
      const materiaisAtuais = op.materiaisNecessarios || [];

      // Calcular novos materiais baseado na revisão
      const novosMateriaisCalculados = await calcularMateriaisComRevisao(novaRevisao, op.quantidade);

      // Buscar estrutura atual (se existir)
      const estruturaAtual = estruturas
        .filter(e => e.produtoPaiId === op.produtoId)
        .find(e => e.revisaoAtual === (op.revisaoEstrutura || 0));

      return {
        op: op,
        produto: produto,
        estruturaAtual: estruturaAtual,
        novaRevisao: novaRevisao,
        materiaisAtuais: materiaisAtuais,
        novosMateriaisCalculados: novosMateriaisCalculados,
        diferencas: calculateDifferences(materiaisAtuais, novosMateriaisCalculados)
      };
    }

    function calculateDifferences(materiaisAtuais, novosMateriaisCalculados) {
      const diferencas = {
        adicionados: [],
        removidos: [],
        alterados: [],
        mantidos: []
      };

      // Criar maps para facilitar comparação
      const atuaisMap = new Map();
      materiaisAtuais.forEach(m => {
        atuaisMap.set(m.produtoId, m);
      });

      const novosMap = new Map();
      novosMateriaisCalculados.forEach(m => {
        novosMap.set(m.produtoId, m);
      });

      // Verificar materiais novos e alterados
      novosMateriaisCalculados.forEach(novoMaterial => {
        const materialAtual = atuaisMap.get(novoMaterial.produtoId);

        if (!materialAtual) {
          // Material adicionado
          diferencas.adicionados.push({
            ...novoMaterial,
            produto: produtosMap[novoMaterial.produtoId]
          });
        } else {
          // Verificar se quantidade mudou
          const qtdAtual = materialAtual.quantidade || 0;
          const qtdNova = novoMaterial.quantidade || 0;

          if (Math.abs(qtdAtual - qtdNova) > 0.001) {
            diferencas.alterados.push({
              ...novoMaterial,
              produto: produtosMap[novoMaterial.produtoId],
              quantidadeAnterior: qtdAtual,
              quantidadeNova: qtdNova,
              diferenca: qtdNova - qtdAtual
            });
          } else {
            diferencas.mantidos.push({
              ...novoMaterial,
              produto: produtosMap[novoMaterial.produtoId],
              quantidadeAnterior: qtdAtual
            });
          }
        }
      });

      // Verificar materiais removidos
      materiaisAtuais.forEach(materialAtual => {
        if (!novosMap.has(materialAtual.produtoId)) {
          diferencas.removidos.push({
            ...materialAtual,
            produto: produtosMap[materialAtual.produtoId]
          });
        }
      });

      return diferencas;
    }

    function renderComparison(comparisons) {
      const comparisonContent = document.getElementById('comparisonContent');

      let html = '';

      comparisons.forEach((comparison, index) => {
        const { op, produto, estruturaAtual, novaRevisao, materiaisAtuais, novosMateriaisCalculados, diferencas } = comparison;

        const numeroOP = op.numeroOP || op.numero || 'Sem número';
        const revisaoAtual = estruturaAtual?.revisaoAtual || 0;
        const revisaoNova = novaRevisao.revisaoAtual || 0;

        html += `
          <div style="margin-bottom: 30px; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px;">
            <h4 style="margin: 0 0 15px 0; color: #1761a0; text-align: center;">
              ${numeroOP} - ${produto?.codigo} - ${produto?.descricao}
            </h4>

            <div class="summary-cards">
              <div class="summary-card old">
                <div class="summary-number">${materiaisAtuais.length}</div>
                <div class="summary-label">Materiais Atuais<br>REV${String(revisaoAtual).padStart(3, '0')}</div>
              </div>
              <div class="summary-card new">
                <div class="summary-number">${novosMateriaisCalculados.length}</div>
                <div class="summary-label">Novos Materiais<br>REV${String(revisaoNova).padStart(3, '0')}</div>
              </div>
              <div class="summary-card" style="background: #fef3c7; border-color: #fbbf24;">
                <div class="summary-number">${diferencas.adicionados.length}</div>
                <div class="summary-label">Adicionados</div>
              </div>
              <div class="summary-card" style="background: #fee2e2; border-color: #f87171;">
                <div class="summary-number">${diferencas.removidos.length}</div>
                <div class="summary-label">Removidos</div>
              </div>
              <div class="summary-card" style="background: #dbeafe; border-color: #60a5fa;">
                <div class="summary-number">${diferencas.alterados.length}</div>
                <div class="summary-label">Alterados</div>
              </div>
            </div>

            <div class="comparison-container">
              <div class="comparison-side old">
                <div class="side-title old">📋 Estrutura Atual (REV${String(revisaoAtual).padStart(3, '0')})</div>
                ${renderMaterialsTable(materiaisAtuais, 'old')}
              </div>

              <div class="comparison-side new">
                <div class="side-title new">🔄 Nova Revisão (REV${String(revisaoNova).padStart(3, '0')})</div>
                ${renderMaterialsTable(novosMateriaisCalculados, 'new', diferencas)}
              </div>
            </div>
          </div>
        `;
      });

      comparisonContent.innerHTML = html;
    }

    async function analyzeCascadeImpact(opAtual, novaRevisao, comparison) {
      const analysis = {
        hasImpact: false,
        affectedSPs: [],
        affectedOPs: [],
        totalOPsImpacted: 0,
        newOPsNeeded: [],
        opsToCancel: [],
        summary: {
          newSPs: 0,
          modifiedSPs: 0,
          removedSPs: 0,
          newOPsCount: 0,
          cancelOPsCount: 0
        }
      };

      try {
        // Identificar Sub-Produtos (SP) na nova estrutura
        const spsNaNovaEstrutura = [];
        if (novaRevisao.componentes) {
          for (const componente of novaRevisao.componentes) {
            const produto = produtosMap[componente.componentId];
            if (produto && produto.tipo === 'SP') {
              spsNaNovaEstrutura.push({
                produto: produto,
                quantidade: componente.quantidade,
                unidade: componente.unidade
              });
            }
          }
        }

        // Identificar SPs na estrutura atual
        const spsNaEstruturaAtual = [];
        if (opAtual.materiaisNecessarios) {
          for (const material of opAtual.materiaisNecessarios) {
            const produto = produtosMap[material.produtoId];
            if (produto && produto.tipo === 'SP') {
              spsNaEstruturaAtual.push({
                produto: produto,
                quantidade: material.quantidade,
                unidade: material.unidade
              });
            }
          }
        }

        // Analisar mudanças nos SPs
        const spsAdicionados = [];
        const spsModificados = [];
        const spsRemovidos = [];

        // Verificar SPs adicionados ou modificados
        for (const spNovo of spsNaNovaEstrutura) {
          const spAtual = spsNaEstruturaAtual.find(sp => sp.produto.id === spNovo.produto.id);

          if (!spAtual) {
            // SP adicionado
            spsAdicionados.push(spNovo);
            analysis.summary.newSPs++;
          } else if (Math.abs((spAtual.quantidade || 0) - (spNovo.quantidade || 0)) > 0.001) {
            // SP modificado
            spsModificados.push({
              ...spNovo,
              quantidadeAnterior: spAtual.quantidade,
              diferenca: spNovo.quantidade - spAtual.quantidade
            });
            analysis.summary.modifiedSPs++;
          }
        }

        // Verificar SPs removidos
        for (const spAtual of spsNaEstruturaAtual) {
          const spNovo = spsNaNovaEstrutura.find(sp => sp.produto.id === spAtual.produto.id);
          if (!spNovo) {
            spsRemovidos.push(spAtual);
            analysis.summary.removedSPs++;
          }
        }

        // Se há mudanças em SPs, buscar outras OPs afetadas
        const todosSPsAfetados = [...spsAdicionados, ...spsModificados, ...spsRemovidos];

        if (todosSPsAfetados.length > 0) {
          analysis.hasImpact = true;
          analysis.affectedSPs = todosSPsAfetados;

          // Buscar outras OPs abertas que usam estes SPs
          for (const spAfetado of todosSPsAfetados) {
            const opsQueUsamSP = ordensProducao.filter(op => {
              if (op.id === opAtual.id) return false; // Excluir OP atual
              if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

              // Verificar se a OP usa este SP
              return op.materiaisNecessarios && op.materiaisNecessarios.some(material =>
                material.produtoId === spAfetado.produto.id
              );
            });

            for (const opAfetada of opsQueUsamSP) {
              if (!analysis.affectedOPs.find(op => op.id === opAfetada.id)) {
                const produto = produtosMap[opAfetada.produtoId];
                analysis.affectedOPs.push({
                  ...opAfetada,
                  produto: produto,
                  spsAfetados: [spAfetado]
                });
              } else {
                // Adicionar SP à lista de SPs afetados desta OP
                const opExistente = analysis.affectedOPs.find(op => op.id === opAfetada.id);
                if (!opExistente.spsAfetados.find(sp => sp.produto.id === spAfetado.produto.id)) {
                  opExistente.spsAfetados.push(spAfetado);
                }
              }
            }
          }

          // Analisar necessidade de novas OPs para SPs adicionados
          for (const spAdicionado of spsAdicionados) {
            const quantidadeNecessaria = spAdicionado.quantidade * opAtual.quantidade;

            // Verificar se já existe OP aberta para este SP
            const opExistente = ordensProducao.find(op =>
              op.produtoId === spAdicionado.produto.id &&
              op.status !== 'Concluída' &&
              op.status !== 'Cancelada'
            );

            if (!opExistente) {
              analysis.newOPsNeeded.push({
                produto: spAdicionado.produto,
                quantidadeNecessaria: quantidadeNecessaria,
                unidade: spAdicionado.unidade || 'PC',
                motivo: 'Novo sub-produto adicionado à estrutura',
                opOrigem: opAtual.numeroOP || opAtual.numero || 'OP'
              });
              analysis.summary.newOPsCount++;
            }
          }

          // Analisar OPs que podem ser canceladas para SPs removidos
          for (const spRemovido of spsRemovidos) {
            // Buscar OPs abertas que produzem este SP
            const opsDoSPRemovido = ordensProducao.filter(op =>
              op.produtoId === spRemovido.produto.id &&
              op.status !== 'Concluída' &&
              op.status !== 'Cancelada'
            );

            for (const opSP of opsDoSPRemovido) {
              // Verificar se este SP é usado em outras OPs abertas (além da atual)
              const outrasOPsQueUsamSP = ordensProducao.filter(op => {
                if (op.id === opAtual.id || op.id === opSP.id) return false;
                if (op.status === 'Concluída' || op.status === 'Cancelada') return false;

                return op.materiaisNecessarios && op.materiaisNecessarios.some(material =>
                  material.produtoId === spRemovido.produto.id
                );
              });

              // Se não há outras OPs que usam este SP, pode ser candidata ao cancelamento
              if (outrasOPsQueUsamSP.length === 0) {
                analysis.opsToCancel.push({
                  ...opSP,
                  produto: produtosMap[opSP.produtoId],
                  motivo: 'Sub-produto removido da estrutura e não é usado em outras OPs',
                  spRemovido: spRemovido.produto,
                  opOrigem: opAtual.numeroOP || opAtual.numero || 'OP'
                });
                analysis.summary.cancelOPsCount++;
              }
            }
          }

          analysis.totalOPsImpacted = analysis.affectedOPs.length;
        }

      } catch (error) {
        console.error('Erro ao analisar impacto em cascata:', error);
      }

      return analysis;
    }

    function renderCascadeAnalysis(analysis) {
      const cascadeContent = document.getElementById('cascadeContent');

      let html = `
        <div class="cascade-section">
          <h4 style="color: #1761a0; margin-bottom: 15px;">📊 Resumo do Impacto</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
            <div class="summary-card" style="background: #e8f5e8; border-color: #4caf50;">
              <div class="summary-number">${analysis.summary.newSPs}</div>
              <div class="summary-label">SPs Adicionados</div>
            </div>
            <div class="summary-card" style="background: #e3f2fd; border-color: #2196f3;">
              <div class="summary-number">${analysis.summary.modifiedSPs}</div>
              <div class="summary-label">SPs Modificados</div>
            </div>
            <div class="summary-card" style="background: #ffebee; border-color: #f44336;">
              <div class="summary-number">${analysis.summary.removedSPs}</div>
              <div class="summary-label">SPs Removidos</div>
            </div>
            <div class="summary-card" style="background: #fff3e0; border-color: #ff9800;">
              <div class="summary-number">${analysis.totalOPsImpacted}</div>
              <div class="summary-label">OPs Afetadas</div>
            </div>
            <div class="summary-card" style="background: #f0f9ff; border-color: #0ea5e9;">
              <div class="summary-number">${analysis.summary.newOPsCount}</div>
              <div class="summary-label">Novas OPs</div>
            </div>
            <div class="summary-card" style="background: #fef2f2; border-color: #ef4444;">
              <div class="summary-number">${analysis.summary.cancelOPsCount}</div>
              <div class="summary-label">OPs p/ Cancelar</div>
            </div>
          </div>
      `;

      if (analysis.affectedOPs.length > 0) {
        html += `
          <h4 style="color: #1761a0; margin-bottom: 15px;">🔄 Ordens de Produção que serão Recalculadas</h4>
          <div class="cascade-ops">
        `;

        analysis.affectedOPs.forEach(op => {
          const numeroOP = op.numeroOP || op.numero || 'Sem número';
          const dataEntrega = op.dataEntrega ?
            new Date(op.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

          html += `
            <div class="cascade-op">
              <div class="cascade-op-header">${numeroOP}</div>
              <div class="cascade-op-details">
                <strong>Produto:</strong> ${op.produto?.codigo || 'N/A'} - ${op.produto?.descricao || 'N/A'}<br>
                <strong>Quantidade:</strong> ${op.quantidade} ${getCorrectUnit(op)}<br>
                <strong>Status:</strong> ${op.status}<br>
                <strong>Entrega:</strong> ${dataEntrega}
              </div>
              <div class="cascade-sp-list">
                <strong>Sub-Produtos Afetados:</strong>
          `;

          op.spsAfetados.forEach(sp => {
            html += `
              <div class="cascade-sp-item">
                ${sp.produto.codigo} - ${sp.produto.descricao}
              </div>
            `;
          });

          html += `
              </div>
            </div>
          `;
        });

        html += `</div>`;
      }

      // Mostrar novas OPs necessárias
      if (analysis.newOPsNeeded && analysis.newOPsNeeded.length > 0) {
        html += `
          <h4 style="color: #0ea5e9; margin: 20px 0 15px 0;">🆕 Novas Ordens de Produção Necessárias</h4>
          <div class="new-ops-section" style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <p style="color: #0c4a6e; margin-bottom: 15px; font-weight: 500;">
              ⚠️ Os seguintes sub-produtos foram adicionados à estrutura e precisam de novas OPs:
            </p>
        `;

        analysis.newOPsNeeded.forEach(newOP => {
          html += `
            <div style="background: white; border: 1px solid #bae6fd; border-radius: 6px; padding: 12px; margin-bottom: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong style="color: #0c4a6e;">${newOP.produto.codigo} - ${newOP.produto.descricao}</strong><br>
                  <span style="color: #64748b; font-size: 14px;">
                    Quantidade necessária: <strong>${newOP.quantidadeNecessaria.toFixed(3)} ${newOP.unidade}</strong>
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Origem: ${newOP.opOrigem} | ${newOP.motivo}
                  </span>
                </div>
                <div style="background: #0ea5e9; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                  NOVA OP
                </div>
              </div>
            </div>
          `;
        });

        html += `</div>`;
      }

      // Mostrar OPs candidatas ao cancelamento
      if (analysis.opsToCancel && analysis.opsToCancel.length > 0) {
        html += `
          <h4 style="color: #ef4444; margin: 20px 0 15px 0;">🗑️ Ordens de Produção Candidatas ao Cancelamento</h4>
          <div class="cancel-ops-section" style="background: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <p style="color: #7f1d1d; margin-bottom: 15px; font-weight: 500;">
              ⚠️ Os seguintes sub-produtos foram removidos da estrutura e suas OPs podem ser canceladas:
            </p>
        `;

        analysis.opsToCancel.forEach(cancelOP => {
          const numeroOP = cancelOP.numeroOP || cancelOP.numero || 'Sem número';
          const dataEntrega = cancelOP.dataEntrega ?
            new Date(cancelOP.dataEntrega.seconds * 1000).toLocaleDateString() : 'Sem data';

          html += `
            <div style="background: white; border: 1px solid #fecaca; border-radius: 6px; padding: 12px; margin-bottom: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong style="color: #7f1d1d;">${numeroOP}</strong><br>
                  <span style="color: #64748b; font-size: 14px;">
                    Produto: <strong>${cancelOP.produto?.codigo} - ${cancelOP.produto?.descricao}</strong>
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Qtde: ${cancelOP.quantidade} ${cancelOP.unidade || 'UN'} | Entrega: ${dataEntrega}
                  </span><br>
                  <span style="color: #64748b; font-size: 13px;">
                    Origem: ${cancelOP.opOrigem} | ${cancelOP.motivo}
                  </span>
                </div>
                <div style="background: #ef4444; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                  CANCELAR?
                </div>
              </div>
            </div>
          `;
        });

        html += `
            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 10px; margin-top: 15px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: #92400e; font-size: 16px;">⚠️</span>
                <span style="color: #92400e; font-size: 14px; font-weight: 500;">
                  ATENÇÃO: Verifique manualmente se essas OPs podem ser canceladas antes de prosseguir.
                </span>
              </div>
            </div>
          </div>
        `;
      }

      html += `</div>`;
      cascadeContent.innerHTML = html;
    }

    function renderMaterialsTable(materiais, type, diferencas = null) {
      if (materiais.length === 0) {
        return '<div style="text-align: center; color: #666; padding: 20px;">Nenhum material encontrado</div>';
      }

      let html = `
        <table class="materials-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Tipo</th>
              <th>Qtde</th>
              <th>Unidade</th>
              ${type === 'new' && diferencas ? '<th>Alteração</th>' : ''}
            </tr>
          </thead>
          <tbody>
      `;

      materiais.forEach(material => {
        const produto = produtosMap[material.produtoId] || {};
        const codigo = produto.codigo || material.codigo || 'N/A';
        const descricao = produto.descricao || material.descricao || 'N/A';
        const tipo = produto.tipo || material.tipo || 'N/A';
        const quantidade = (material.quantidade || 0).toFixed(3).replace('.', ',');
        const unidade = material.unidade || produto.unidade || 'PC';

        // Cor do tipo baseada no tipo de produto
        let tipoColor = '#6b7280';
        let tipoBg = '#f3f4f6';
        switch (tipo) {
          case 'MP':
            tipoColor = '#dc2626';
            tipoBg = '#fee2e2';
            break;
          case 'SP':
            tipoColor = '#2563eb';
            tipoBg = '#dbeafe';
            break;
          case 'PA':
            tipoColor = '#16a34a';
            tipoBg = '#dcfce7';
            break;
        }

        let rowClass = '';
        let changeInfo = '';

        if (type === 'new' && diferencas) {
          // Verificar se é novo, alterado ou removido
          const isAdicionado = diferencas.adicionados.some(m => m.produtoId === material.produtoId);
          const alterado = diferencas.alterados.find(m => m.produtoId === material.produtoId);

          if (isAdicionado) {
            rowClass = 'material-new';
            changeInfo = '<span style="color: #16a34a; font-weight: 600;">NOVO</span>';
          } else if (alterado) {
            const diferenca = alterado.diferenca;
            const sinal = diferenca > 0 ? '+' : '';
            const cor = diferenca > 0 ? '#16a34a' : '#dc2626';
            changeInfo = `<span style="color: ${cor}; font-weight: 600;">${sinal}${diferenca.toFixed(3).replace('.', ',')}</span>`;
          } else {
            changeInfo = '<span style="color: #6b7280;">-</span>';
          }
        }

        html += `
          <tr class="${rowClass}">
            <td>${codigo}</td>
            <td>${descricao}</td>
            <td><span style="background: ${tipoBg}; color: ${tipoColor}; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">${tipo}</span></td>
            <td>${quantidade}</td>
            <td>${unidade}</td>
            ${type === 'new' && diferencas ? `<td>${changeInfo}</td>` : ''}
          </tr>
        `;
      });

      // Adicionar materiais removidos na tabela da direita
      if (type === 'new' && diferencas && diferencas.removidos.length > 0) {
        diferencas.removidos.forEach(material => {
          const produto = produtosMap[material.produtoId] || {};
          const codigo = produto.codigo || material.codigo || 'N/A';
          const descricao = produto.descricao || material.descricao || 'N/A';
          const tipo = produto.tipo || material.tipo || 'N/A';
          const quantidade = (material.quantidade || 0).toFixed(3).replace('.', ',');
          const unidade = material.unidade || produto.unidade || 'PC';

          // Cor do tipo baseada no tipo de produto
          let tipoColor = '#6b7280';
          let tipoBg = '#f3f4f6';
          switch (tipo) {
            case 'MP':
              tipoColor = '#dc2626';
              tipoBg = '#fee2e2';
              break;
            case 'SP':
              tipoColor = '#2563eb';
              tipoBg = '#dbeafe';
              break;
            case 'PA':
              tipoColor = '#16a34a';
              tipoBg = '#dcfce7';
              break;
          }

          html += `
            <tr class="material-removed">
              <td>${codigo}</td>
              <td>${descricao}</td>
              <td><span style="background: ${tipoBg}; color: ${tipoColor}; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">${tipo}</span></td>
              <td>${quantidade}</td>
              <td>${unidade}</td>
              <td><span style="color: #dc2626; font-weight: 600;">REMOVIDO</span></td>
            </tr>
          `;
        });
      }

      html += `
          </tbody>
        </table>
      `;

      return html;
    }

    window.approveRecalculation = function() {
      // Garantir que a OP atual esteja selecionada se não há OPs selecionadas
      if (selectedOPs.length === 0 && opSelecionada) {
        selectedOPs = [opSelecionada.id];
      }

      // Esconder comparação e mostrar progresso
      document.querySelector('.comparison-section').style.display = 'none';
      startRecalculation();
    };

    window.rejectComparison = function() {
      // Voltar para seleção
      document.querySelector('.comparison-section').style.display = 'none';
      document.querySelector('.op-selection').style.display = 'block';
      document.querySelector('.revision-selection').style.display = 'block';
      document.getElementById('showComparison').style.display = 'inline-block';
      document.getElementById('startRecalculate').style.display = 'none';

      // Resetar botões
      updateRecalculateButton();
    };

    window.startRecalculation = async function() {
      // Garantir que temos uma OP selecionada (seja do array ou da OP atual)
      const opsParaRecalcular = selectedOPs.length > 0 ? selectedOPs : (opSelecionada ? [opSelecionada.id] : []);

      if (opsParaRecalcular.length === 0 || !selectedRevision) {
        alert('Erro: Não foi possível identificar a OP ou revisão de estrutura para recálculo.');
        return;
      }

      // Atualizar selectedOPs se necessário
      if (selectedOPs.length === 0 && opSelecionada) {
        selectedOPs = [opSelecionada.id];
      }

      const confirmMessage = `
🔄 RECÁLCULO DE ORDENS DE PRODUÇÃO

Você está prestes a recalcular ${selectedOPs.length} OP(s) usando a revisão ${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')} da estrutura.

⚠️ ATENÇÃO: Esta operação irá:
• Recalcular todos os materiais necessários
• Liberar empenhos antigos de estoque
• Criar novos empenhos baseados na nova estrutura
• Atualizar solicitações de compra vinculadas
• Registrar logs de auditoria

Esta ação NÃO PODE ser desfeita automaticamente.

Deseja continuar?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Mostrar seção de progresso
        document.querySelector('.progress-section').style.display = 'block';
        document.getElementById('startRecalculate').disabled = true;

        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressLog = document.getElementById('progressLog');

        progressLog.innerHTML = '';

        // Calcular total de OPs a processar (OP atual + OPs em cascata)
        const totalOPs = 1 + (cascadeAnalysis?.totalOPsImpacted || 0);
        let processedOPs = 0;

        function updateProgress(current, total, message) {
          const percentage = (current / total) * 100;
          progressFill.style.width = percentage + '%';
          progressText.textContent = `${current}/${total} OPs processadas (${percentage.toFixed(1)}%)`;

          const timestamp = new Date().toLocaleTimeString();
          progressLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
          progressLog.scrollTop = progressLog.scrollHeight;
        }

        updateProgress(0, totalOPs, 'Iniciando recálculo das ordens de produção...');

        const resultados = {
          sucesso: 0,
          erro: 0,
          detalhes: []
        };

        // Processar a OP atual
        try {
          const produto = produtosMap[opSelecionada.produtoId];
          const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'Sem número';

          updateProgress(processedOPs, totalOPs, `Processando OP Principal: ${numeroOP} - ${produto?.codigo}...`);

          // Usar a revisão selecionada
          await recalcularOPComRevisao(opSelecionada, selectedRevision.data);

          resultados.sucesso++;
          resultados.detalhes.push({
            op: numeroOP,
            produto: produto?.codigo,
            status: 'Sucesso',
            tipo: 'Principal'
          });

          processedOPs++;
          updateProgress(processedOPs, totalOPs, `✅ OP Principal ${numeroOP} recalculada com sucesso`);

        } catch (error) {
          console.error(`Erro ao recalcular OP ${opSelecionada.id}:`, error);

          resultados.erro++;
          resultados.detalhes.push({
            op: opSelecionada.numeroOP || opSelecionada.numero || 'OP',
            produto: produtosMap[opSelecionada.produtoId]?.codigo || 'N/A',
            status: 'Erro',
            erro: error.message,
            tipo: 'Principal'
          });

          processedOPs++;
          updateProgress(processedOPs, totalOPs, `❌ Erro ao processar OP principal: ${error.message}`);
        }

        // Processar OPs em cascata se houver
        if (cascadeAnalysis && cascadeAnalysis.hasImpact && cascadeAnalysis.affectedOPs.length > 0) {
          updateProgress(processedOPs, totalOPs, `Iniciando recálculo em cascata de ${cascadeAnalysis.affectedOPs.length} OPs...`);

          for (const opAfetada of cascadeAnalysis.affectedOPs) {
            try {
              const produto = produtosMap[opAfetada.produtoId];
              const numeroOP = opAfetada.numeroOP || opAfetada.numero || 'Sem número';

              updateProgress(processedOPs, totalOPs, `Processando OP em Cascata: ${numeroOP} - ${produto?.codigo}...`);

              // Buscar a estrutura mais recente para esta OP
              const estruturaAtualizada = estruturas
                .filter(e => e.produtoPaiId === opAfetada.produtoId)
                .sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0))[0];

              if (estruturaAtualizada) {
                await recalcularOPComRevisao(opAfetada, estruturaAtualizada);

                resultados.sucesso++;
                resultados.detalhes.push({
                  op: numeroOP,
                  produto: produto?.codigo,
                  status: 'Sucesso',
                  tipo: 'Cascata'
                });

                updateProgress(processedOPs + 1, totalOPs, `✅ OP Cascata ${numeroOP} recalculada com sucesso`);
              } else {
                throw new Error('Estrutura não encontrada para recálculo em cascata');
              }

            } catch (error) {
              console.error(`Erro ao recalcular OP em cascata ${opAfetada.id}:`, error);

              resultados.erro++;
              resultados.detalhes.push({
                op: opAfetada.numeroOP || opAfetada.numero || 'OP',
                produto: produtosMap[opAfetada.produtoId]?.codigo || 'N/A',
                status: 'Erro',
                erro: error.message,
                tipo: 'Cascata'
              });

              updateProgress(processedOPs + 1, totalOPs, `❌ Erro ao processar OP cascata: ${error.message}`);
            }

            processedOPs++;
          }
        }

        // Mostrar resultado final
        updateProgress(totalOPs, totalOPs, '🎉 Recálculo concluído!');

        const opsPrincipais = resultados.detalhes.filter(d => d.tipo === 'Principal');
        const opsCascata = resultados.detalhes.filter(d => d.tipo === 'Cascata');

        const resumo = `
✅ RECÁLCULO CONCLUÍDO

📊 Resumo:
• OP Principal: ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'} - ${opsPrincipais[0]?.status || 'N/A'}
• OPs em Cascata: ${opsCascata.length} processadas
• Total de Sucessos: ${resultados.sucesso}
• Total de Erros: ${resultados.erro}

${cascadeAnalysis?.hasImpact ? '🔄 Recálculo em cascata executado devido a alterações em Sub-Produtos (SP)' : ''}
${resultados.erro > 0 ? '⚠️ Verifique o log para detalhes dos erros.' : '🎉 Todas as OPs foram recalculadas com sucesso!'}`;

        alert(resumo);

        // Recarregar dados
        await window.onload();
        closeRecalculateModal();

      } catch (error) {
        console.error('Erro no processo de recálculo:', error);
        alert('❌ Erro no processo de recálculo: ' + error.message);
        document.getElementById('startRecalculate').disabled = false;
      }
    };

    async function recalcularOPComRevisao(op, revisaoEstrutura) {
      try {
        // 1. Calcular novos materiais baseado na revisão
        const novosMateriais = await calcularMateriaisComRevisao(revisaoEstrutura, op.quantidade);

        // 2. Liberar empenhos antigos
        if (op.materiaisNecessarios && op.materiaisNecessarios.length > 0) {
          await liberarEmpenhosAntigos(op.materiaisNecessarios, op.armazemProducaoId || 'PROD1');
        }

        // 3. Criar novos empenhos
        let novosEmpenhos = await criarNovosEmpenhos(novosMateriais, op.armazemProducaoId || 'PROD1');

        // 3.1. 🔄 PRESERVAR HISTÓRICO DE TRANSFERÊNCIAS
        novosEmpenhos = await preservarHistoricoTransferencias(
          op.id,
          op.materiaisNecessarios || [],
          novosEmpenhos
        );

        // 4. Atualizar OP
        const opRef = doc(db, "ordensProducao", op.id);
        await updateDoc(opRef, {
          materiaisNecessarios: novosEmpenhos,
          dataUltimaRevisao: new Date(),
          revisaoEstrutura: revisaoEstrutura.revisaoAtual || 0,
          recalculadoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          historicoRevisoes: [...(op.historicoRevisoes || []), {
            data: new Date(),
            revisaoAnterior: op.revisaoEstrutura || 0,
            revisaoNova: revisaoEstrutura.revisaoAtual || 0,
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
            materiaisRecalculados: novosEmpenhos.length
          }]
        });

        // 5. Atualizar solicitações vinculadas
        await atualizarSolicitacoesVinculadas(op.id, novosMateriais);

        // 6. Registrar log
        await registrarLogRevisao(op, revisaoEstrutura, novosMateriais);

      } catch (error) {
        console.error(`Erro ao recalcular OP ${op.id} com revisão:`, error);
        throw error;
      }
    }

    async function calcularMateriaisComRevisao(revisaoEstrutura, quantidade) {
      const materiais = [];

      if (!revisaoEstrutura.componentes || revisaoEstrutura.componentes.length === 0) {
        console.log('⚠️ Revisão sem componentes:', revisaoEstrutura);
        return materiais;
      }

      console.log(`🔢 Calculando materiais para quantidade: ${quantidade}`);
      console.log(`📋 Componentes na estrutura: ${revisaoEstrutura.componentes.length}`);

      for (const componente of revisaoEstrutura.componentes) {
        const quantidadeUnitaria = parseFloat(componente.quantidade) || 0;
        const quantidadeNecessaria = quantidadeUnitaria * quantidade;
        const produto = produtosMap[componente.componentId || componente.produtoId];

        if (produto && quantidadeUnitaria > 0) {
          console.log(`➕ Material: ${produto.codigo} - Qtd Unit: ${quantidadeUnitaria} x ${quantidade} = ${quantidadeNecessaria} ${componente.unidade || produto.unidade}`);

          materiais.push({
            produtoId: componente.componentId || componente.produtoId,
            quantidade: quantidadeNecessaria,
            quantidadeUnitaria: quantidadeUnitaria,
            unidade: componente.unidade || produto.unidade || 'PC',
            codigo: produto.codigo,
            descricao: produto.descricao,
            tipo: produto.tipo
          });
        } else {
          console.log(`⚠️ Componente ignorado: ${componente.componentId} - Produto: ${produto?.codigo || 'N/A'} - Qtd: ${quantidadeUnitaria}`);
        }
      }

      console.log(`✅ Total de materiais calculados: ${materiais.length}`);
      console.log('📊 Materiais por tipo:', materiais.reduce((acc, m) => {
        acc[m.tipo] = (acc[m.tipo] || 0) + 1;
        return acc;
      }, {}));

      return materiais;
    }

    async function registrarLogRevisao(op, revisaoEstrutura, materiais) {
      try {
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'RECALCULO_REVISAO',
          opId: op.id,
          numeroOP: op.numeroOP || op.numero,
          produtoId: op.produtoId,
          revisaoAnterior: op.revisaoEstrutura || 0,
          revisaoNova: revisaoEstrutura.revisaoAtual || 0,
          materiaisRecalculados: materiais.length,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataHora: new Date(),
          detalhes: {
            quantidadeOP: op.quantidade,
            componentesNaRevisao: revisaoEstrutura.componentes?.length || 0,
            operacoesNaRevisao: revisaoEstrutura.operacoes?.length || 0
          }
        });
      } catch (error) {
        console.error('Erro ao registrar log de revisão:', error);
        // Não falhar o processo principal
      }
    }

    // ===== FUNÇÕES DE APROVAÇÃO E REJEIÇÃO =====

    window.approveRecalculation = function() {
      if (!opSelecionada || !selectedRevision) {
        showWarningNotification('Nenhuma revisão selecionada para aprovação.');
        return;
      }

      // Verificar se há diferenças antes de permitir o recálculo
      try {
        // Buscar estrutura atual da OP
        let estruturaAtual = null;

        // Estratégia 1: Buscar pela revisão específica da OP
        if (opSelecionada.revisaoEstrutura !== undefined && opSelecionada.revisaoEstrutura !== null) {
          estruturaAtual = estruturas.find(est =>
            est.produtoPaiId === opSelecionada.produtoId &&
            (est.revisaoAtual || 0) === opSelecionada.revisaoEstrutura
          );
        }

        // Estratégia 2: Buscar a revisão mais recente do produto
        if (!estruturaAtual) {
          const estruturasProduct = estruturas.filter(est => est.produtoPaiId === opSelecionada.produtoId);
          if (estruturasProduct.length > 0) {
            estruturasProduct.sort((a, b) => (b.revisaoAtual || 0) - (a.revisaoAtual || 0));
            estruturaAtual = estruturasProduct[0];
          }
        }

        // Estratégia 3: Criar estrutura baseada nos materiais atuais da OP
        if (!estruturaAtual && opSelecionada.materiaisNecessarios && opSelecionada.materiaisNecessarios.length > 0) {
          estruturaAtual = {
            id: 'virtual-' + opSelecionada.id,
            produtoPaiId: opSelecionada.produtoId,
            revisaoAtual: opSelecionada.revisaoEstrutura || 0,
            componentes: opSelecionada.materiaisNecessarios.map(mat => ({
              componentId: mat.produtoId,
              quantidade: mat.quantidadeUnitaria || mat.quantidade,
              unidade: mat.unidade || 'UN'
            })),
            virtual: true
          };
        }

        if (estruturaAtual) {
          const diferencas = verificarDiferencasEstrutura(estruturaAtual, selectedRevision.data);

          if (!diferencas.temDiferencas) {
            showWarningNotification(
              `⚠️ Recálculo desnecessário\n\n🔍 Análise realizada:\n• Estrutura da OP: REV${String(estruturaAtual.revisaoAtual || 0).padStart(3, '0')}\n• Revisão selecionada: REV${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')}\n\n✅ As estruturas são idênticas em componentes e quantidades.\n\n❌ Não há alterações para aplicar.\n\n💡 Selecione uma revisão diferente para recalcular.`
            );
            return;
          }
        }
      } catch (error) {
        console.error('Erro ao verificar diferenças:', error);
        // Continuar com o recálculo em caso de erro na verificação
      }

      const confirmMessage = `🔄 CONFIRMAR RECÁLCULO\n\nVocê está prestes a recalcular a OP:\n• ${opSelecionada.numeroOP || opSelecionada.numero}\n• Produto: ${produtosMap[opSelecionada.produtoId]?.codigo}\n• Nova Revisão: REV${String(selectedRevision.data.revisaoAtual || 0).padStart(3, '0')}\n\nEsta ação irá:\n✓ Recalcular todos os materiais necessários\n✓ Atualizar empenhos de estoque\n✓ Atualizar solicitações de compra vinculadas\n\nConfirma o recálculo?`;

      if (confirm(confirmMessage)) {
        startRecalculation();
      }
    };

    window.rejectComparison = function() {
      // Esconder seção de comparação
      document.getElementById('comparisonSection').style.display = 'none';
      document.getElementById('cascadeAnalysisSection').style.display = 'none';

      // Limpar seleção
      document.querySelectorAll('.revision-item.selected').forEach(item => {
        item.classList.remove('selected');
      });

      selectedRevision = null;

      // Resetar botões
      document.getElementById('showComparison').disabled = true;
      document.getElementById('showComparison').textContent = 'Ver Comparação';

      showInfoNotification('Comparação cancelada. Selecione outra revisão se desejar.');
    };

    // ===== FUNÇÕES DE NOTIFICAÇÃO =====

    function showSuccessNotification(message) {
      showNotification(message, 'success');
    }

    function showErrorNotification(message) {
      showNotification(message, 'error');
    }

    function showWarningNotification(message) {
      showNotification(message, 'warning');
    }

    function showInfoNotification(message) {
      showNotification(message, 'info');
    }

    function showNotification(message, type = 'info') {
      // Remover notificação existente se houver
      const existingNotification = document.querySelector('.notification');
      if (existingNotification) {
        existingNotification.remove();
      }

      // Criar nova notificação
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.style.display = 'block';

      // Adicionar ícone baseado no tipo
      let icon = '';
      switch(type) {
        case 'success':
          icon = '<i class="fas fa-check-circle"></i>';
          break;
        case 'error':
          icon = '<i class="fas fa-exclamation-circle"></i>';
          break;
        case 'warning':
          icon = '<i class="fas fa-exclamation-triangle"></i>';
          break;
        case 'info':
          icon = '<i class="fas fa-info-circle"></i>';
          break;
      }

      notification.innerHTML = `
        <div style="display: flex; align-items: flex-start; gap: 10px;">
          <div style="font-size: 18px;">${icon}</div>
          <div style="flex: 1; white-space: pre-line;">${message}</div>
          <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin-left: 10px;">×</button>
        </div>
      `;

      // Adicionar estilos CSS para a notificação
      const style = document.createElement('style');
      style.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 15px 20px;
          border-radius: 8px;
          color: white;
          font-weight: 600;
          z-index: 1000;
          max-width: 400px;
          box-shadow: 0 8px 20px rgba(0,0,0,0.2);
          animation: slideInRight 0.3s ease;
        }

        .notification-success {
          background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .notification-error {
          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .notification-warning {
          background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .notification-info {
          background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `;

      if (!document.querySelector('style[data-notification-styles]')) {
        style.setAttribute('data-notification-styles', 'true');
        document.head.appendChild(style);
      }

      // Adicionar ao DOM
      document.body.appendChild(notification);

      // Auto-remover após 5 segundos
      setTimeout(() => {
        if (notification.parentElement) {
          notification.style.animation = 'slideInRight 0.3s ease reverse';
          setTimeout(() => {
            if (notification.parentElement) {
              notification.remove();
            }
          }, 300);
        }
      }, 5000);
    }

    // ===== FUNÇÕES PARA MATERIAIS EXTRAS =====

    let selectedProduct = null;

    window.openExtraMaterialsModal = function() {
      if (!opSelecionada) {
        alert('❌ Erro: Nenhuma OP selecionada.');
        return;
      }

      if (opSelecionada.status !== 'Em Produção' && opSelecionada.status !== 'em_producao') {
        alert('❌ Erro: Apenas OPs em produção podem receber materiais extras.');
        return;
      }

      // Resetar modal
      selectedProduct = null;
      document.getElementById('productSearch').value = '';
      document.getElementById('productSearchResults').style.display = 'none';
      document.getElementById('selectedProductSection').style.display = 'none';
      document.getElementById('quantitySection').style.display = 'none';
      document.getElementById('stockVerification').style.display = 'none';
      document.getElementById('addExtraMaterialBtn').disabled = true;

      // Mostrar informações da OP
      const prod = produtosMap[opSelecionada.produtoId];
      document.getElementById('extraMaterialsOPInfo').innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <h4 style="margin: 0 0 5px 0; color: #495057;">
              <i class="fas fa-cog"></i> ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'}
            </h4>
            <div style="color: #6c757d;">${prod?.codigo || 'N/A'} - ${prod?.descricao || 'N/A'}</div>
            <small style="color: #6c757d;">Quantidade: ${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}</small>
          </div>
          <div class="status-badge" style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
            <i class="fas fa-play"></i> EM PRODUÇÃO
          </div>
        </div>
      `;

      document.getElementById('extraMaterialsModal').style.display = 'block';
    };

    window.closeExtraMaterialsModal = function() {
      document.getElementById('extraMaterialsModal').style.display = 'none';
    };

    window.searchProducts = function() {
      const searchTerm = document.getElementById('productSearch').value.trim().toLowerCase();
      if (searchTerm.length < 2) {
        alert('Digite pelo menos 2 caracteres para buscar.');
        return;
      }

      const results = produtos.filter(produto =>
        produto.codigo?.toLowerCase().includes(searchTerm) ||
        produto.descricao?.toLowerCase().includes(searchTerm)
      ).slice(0, 10); // Limitar a 10 resultados

      const resultsContainer = document.getElementById('productSearchResults');

      if (results.length === 0) {
        resultsContainer.innerHTML = '<div style="padding: 15px; text-align: center; color: #6c757d;">Nenhum produto encontrado</div>';
      } else {
        resultsContainer.innerHTML = results.map(produto => `
          <div class="product-result" onclick="selectProduct('${produto.id}')"
               style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
            <div>
              <strong>${produto.codigo}</strong> - ${produto.descricao}
              <br>
              <small style="color: #6c757d;">Tipo: ${produto.tipo} | Unidade: ${produto.unidade}</small>
            </div>
            <i class="fas fa-plus-circle" style="color: #28a745;"></i>
          </div>
        `).join('');
      }

      resultsContainer.style.display = 'block';
    };

    window.selectProduct = function(productId) {
      selectedProduct = produtos.find(p => p.id === productId);
      if (!selectedProduct) return;

      // Mostrar produto selecionado
      document.getElementById('selectedProductInfo').innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <h5 style="margin: 0 0 5px 0;">${selectedProduct.codigo} - ${selectedProduct.descricao}</h5>
            <small style="color: #6c757d;">Tipo: ${selectedProduct.tipo} | Unidade: ${selectedProduct.unidade}</small>
          </div>
          <button onclick="clearSelectedProduct()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">
            <i class="fas fa-times"></i> Trocar
          </button>
        </div>
      `;

      // Configurar unidade
      document.getElementById('extraUnit').value = selectedProduct.unidade || 'UN';

      // Mostrar seções
      document.getElementById('productSearchResults').style.display = 'none';
      document.getElementById('selectedProductSection').style.display = 'block';
      document.getElementById('quantitySection').style.display = 'block';

      // Focar na quantidade
      document.getElementById('extraQuantity').focus();

      // Verificar campos para habilitar botão
      checkExtraMaterialForm();
    };

    window.clearSelectedProduct = function() {
      selectedProduct = null;
      document.getElementById('selectedProductSection').style.display = 'none';
      document.getElementById('quantitySection').style.display = 'none';
      document.getElementById('stockVerification').style.display = 'none';
      document.getElementById('addExtraMaterialBtn').disabled = true;
      document.getElementById('productSearch').focus();
    };

    // Verificar formulário e estoque quando quantidade ou justificativa mudam
    document.getElementById('extraQuantity').addEventListener('input', checkExtraMaterialForm);
    document.getElementById('extraJustification').addEventListener('input', checkExtraMaterialForm);

    function checkExtraMaterialForm() {
      const quantity = parseFloat(document.getElementById('extraQuantity').value);
      const justification = document.getElementById('extraJustification').value.trim();

      if (selectedProduct && quantity > 0 && justification.length >= 10) {
        // Verificar estoque
        verifyExtraStock(selectedProduct.id, quantity);
      } else {
        document.getElementById('stockVerification').style.display = 'none';
        document.getElementById('addExtraMaterialBtn').disabled = true;
      }
    }

    async function verifyExtraStock(productId, quantity) {
      try {
        const stockContainer = document.getElementById('stockInfo');
        const stockSection = document.getElementById('stockVerification');

        stockContainer.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando estoque...';
        stockSection.style.display = 'block';

        // Buscar estoque do produto no armazém de produção
        const armazemId = opSelecionada.armazemProducaoId || 'PROD1';
        const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        const q = query(collection(db, 'estoque'),
          where('produtoId', '==', productId),
          where('armazemId', '==', armazemId)
        );

        const snapshot = await getDocs(q);
        let disponivel = 0;

        if (!snapshot.empty) {
          disponivel = snapshot.docs[0].data().quantidade || 0;
        }

        const produto = produtosMap[productId];
        const isAvailable = disponivel >= quantity;

        stockContainer.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <strong>Estoque Disponível:</strong> ${disponivel.toFixed(3)} ${produto.unidade}
              <br>
              <strong>Quantidade Solicitada:</strong> ${quantity.toFixed(3)} ${produto.unidade}
            </div>
            <div style="text-align: right;">
              ${isAvailable ?
                '<span style="color: #28a745; font-weight: 600;"><i class="fas fa-check-circle"></i> Disponível</span>' :
                '<span style="color: #dc3545; font-weight: 600;"><i class="fas fa-exclamation-triangle"></i> Insuficiente</span>'
              }
            </div>
          </div>
          ${!isAvailable ?
            `<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ffeaa7;">
              <i class="fas fa-exclamation-triangle" style="color: #856404;"></i>
              <strong style="color: #856404;">Atenção:</strong> Estoque insuficiente.
              Faltam ${(quantity - disponivel).toFixed(3)} ${produto.unidade}.
            </div>` : ''
          }
        `;

        // Habilitar botão apenas se há estoque suficiente
        document.getElementById('addExtraMaterialBtn').disabled = !isAvailable;

      } catch (error) {
        console.error('Erro ao verificar estoque:', error);
        document.getElementById('stockInfo').innerHTML = `
          <div style="color: #dc3545;">
            <i class="fas fa-exclamation-triangle"></i> Erro ao verificar estoque: ${error.message}
          </div>
        `;
        document.getElementById('addExtraMaterialBtn').disabled = true;
      }
    }

    window.addExtraMaterial = async function() {
      if (!selectedProduct || !opSelecionada) {
        alert('❌ Erro: Produto ou OP não selecionados.');
        return;
      }

      const quantity = parseFloat(document.getElementById('extraQuantity').value);
      const justification = document.getElementById('extraJustification').value.trim();

      if (!quantity || quantity <= 0) {
        alert('❌ Erro: Quantidade deve ser maior que zero.');
        return;
      }

      if (justification.length < 10) {
        alert('❌ Erro: Justificativa deve ter pelo menos 10 caracteres.');
        return;
      }

      const btnAdd = document.getElementById('addExtraMaterialBtn');
      const originalText = btnAdd.innerHTML;

      try {
        btnAdd.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adicionando...';
        btnAdd.disabled = true;

        // 1. Criar empenho do material extra
        const armazemId = opSelecionada.armazemProducaoId || 'PROD1';
        await criarEmpenhoMaterialExtra(selectedProduct.id, quantity, armazemId);

        // 2. Adicionar material extra à OP
        const materialExtra = {
          produtoId: selectedProduct.id,
          quantidade: quantity,
          unidade: selectedProduct.unidade || 'UN',
          justificativa: justification,
          dataAdicao: new Date(),
          adicionadoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          armazemId: armazemId
        };

        // Atualizar OP no Firebase
        const { doc, updateDoc, arrayUnion } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const opRef = doc(db, "ordensProducao", opSelecionada.id);

        await updateDoc(opRef, {
          materiaisExtras: arrayUnion(materialExtra),
          dataUltimaAlteracao: new Date(),
          alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
        });

        // 3. Atualizar dados locais
        if (!opSelecionada.materiaisExtras) {
          opSelecionada.materiaisExtras = [];
        }
        opSelecionada.materiaisExtras.push(materialExtra);

        // 4. Atualizar interface
        renderExtraMaterials(opSelecionada.materiaisExtras);

        // 5. Registrar log
        await registrarLogMaterialExtra(opSelecionada, materialExtra, 'adicao');

        showSuccessNotification(`✅ Material extra adicionado com sucesso!\n\n${selectedProduct.codigo} - ${quantity.toFixed(3)} ${selectedProduct.unidade}\n\nEmpenho criado no estoque.`);

        closeExtraMaterialsModal();

      } catch (error) {
        console.error('Erro ao adicionar material extra:', error);
        showErrorNotification('❌ Erro ao adicionar material extra: ' + error.message);
        btnAdd.innerHTML = originalText;
        btnAdd.disabled = false;
      }
    };

    async function criarEmpenhoMaterialExtra(produtoId, quantidade, armazemId) {
      try {
        const { doc, updateDoc, increment } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        // Buscar documento de estoque
        const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const q = query(collection(db, 'estoque'),
          where('produtoId', '==', produtoId),
          where('armazemId', '==', armazemId)
        );

        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          throw new Error(`Produto não encontrado no estoque do armazém ${armazemId}`);
        }

        const estoqueDoc = snapshot.docs[0];
        const estoqueAtual = estoqueDoc.data().quantidade || 0;

        if (estoqueAtual < quantidade) {
          throw new Error(`Estoque insuficiente. Disponível: ${estoqueAtual.toFixed(3)}, Solicitado: ${quantidade.toFixed(3)}`);
        }

        // Reduzir estoque (criar empenho)
        const estoqueRef = doc(db, 'estoque', estoqueDoc.id);
        await updateDoc(estoqueRef, {
          quantidade: increment(-quantidade),
          dataUltimaMovimentacao: new Date()
        });

        // Registrar movimentação de estoque
        const { addDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        await addDoc(collection(db, 'movimentacoesEstoque'), {
          produtoId: produtoId,
          armazemId: armazemId,
          tipo: 'saida',
          quantidade: quantidade,
          motivo: 'Empenho para material extra',
          opId: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          data: new Date(),
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
        });

        console.log(`Empenho criado: ${quantidade} unidades do produto ${produtoId}`);

      } catch (error) {
        console.error('Erro ao criar empenho:', error);
        throw error;
      }
    }

    window.removeExtraMaterial = async function(index) {
      if (!opSelecionada || !opSelecionada.materiaisExtras || !opSelecionada.materiaisExtras[index]) {
        alert('❌ Erro: Material extra não encontrado.');
        return;
      }

      const material = opSelecionada.materiaisExtras[index];
      const produto = produtosMap[material.produtoId];

      if (!confirm(`❌ REMOVER MATERIAL EXTRA\n\nDeseja realmente remover este material?\n\n${produto?.codigo || 'N/A'} - ${material.quantidade.toFixed(3)} ${material.unidade}\n\n⚠️ O estoque será liberado automaticamente.`)) {
        return;
      }

      try {
        // 1. Liberar empenho (devolver ao estoque)
        await liberarEmpenhoMaterialExtra(material);

        // 2. Remover da OP no Firebase
        const materiaisAtualizados = opSelecionada.materiaisExtras.filter((_, i) => i !== index);

        const { doc, updateDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const opRef = doc(db, "ordensProducao", opSelecionada.id);

        await updateDoc(opRef, {
          materiaisExtras: materiaisAtualizados,
          dataUltimaAlteracao: new Date(),
          alteradoPor: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
        });

        // 3. Atualizar dados locais
        opSelecionada.materiaisExtras = materiaisAtualizados;

        // 4. Atualizar interface
        renderExtraMaterials(opSelecionada.materiaisExtras);

        // 5. Registrar log
        await registrarLogMaterialExtra(opSelecionada, material, 'remocao');

        showSuccessNotification(`✅ Material extra removido com sucesso!\n\n${produto?.codigo || 'N/A'} - ${material.quantidade.toFixed(3)} ${material.unidade}\n\nEstoque liberado.`);

      } catch (error) {
        console.error('Erro ao remover material extra:', error);
        showErrorNotification('❌ Erro ao remover material extra: ' + error.message);
      }
    };

    async function liberarEmpenhoMaterialExtra(material) {
      try {
        const { doc, updateDoc, increment } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        // Buscar documento de estoque
        const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const q = query(collection(db, 'estoque'),
          where('produtoId', '==', material.produtoId),
          where('armazemId', '==', material.armazemId || 'PROD1')
        );

        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          throw new Error(`Produto não encontrado no estoque do armazém ${material.armazemId || 'PROD1'}`);
        }

        // Devolver ao estoque
        const estoqueRef = doc(db, 'estoque', snapshot.docs[0].id);
        await updateDoc(estoqueRef, {
          quantidade: increment(material.quantidade),
          dataUltimaMovimentacao: new Date()
        });

        // Registrar movimentação de estoque
        const { addDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        await addDoc(collection(db, 'movimentacoesEstoque'), {
          produtoId: material.produtoId,
          armazemId: material.armazemId || 'PROD1',
          tipo: 'entrada',
          quantidade: material.quantidade,
          motivo: 'Liberação de material extra removido',
          opId: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          data: new Date(),
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
        });

        console.log(`Empenho liberado: ${material.quantidade} unidades do produto ${material.produtoId}`);

      } catch (error) {
        console.error('Erro ao liberar empenho:', error);
        throw error;
      }
    }

    async function registrarLogMaterialExtra(op, material, acao) {
      try {
        const { addDoc, collection } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const produto = produtosMap[material.produtoId];

        await addDoc(collection(db, 'logsAlteracoes'), {
          tipo: 'material_extra',
          acao: acao,
          opId: op.id,
          numeroOP: op.numeroOP || op.numero,
          produtoId: op.produtoId,
          materialExtra: {
            produtoId: material.produtoId,
            codigo: produto?.codigo || 'N/A',
            descricao: produto?.descricao || 'N/A',
            quantidade: material.quantidade,
            unidade: material.unidade,
            justificativa: material.justificativa
          },
          data: new Date(),
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
        });
      } catch (error) {
        console.error('Erro ao registrar log:', error);
        // Não falhar o processo principal por erro no log
      }
    }

    window.loadExtraMaterialsHistory = async function() {
      if (!opSelecionada) return;

      const historyContainer = document.getElementById('extraMaterialsHistory');
      if (!historyContainer) {
        console.warn('⚠️ Elemento extraMaterialsHistory não encontrado no DOM');
        return;
      }

      historyContainer.innerHTML = '<div class="empty-state"><i class="fas fa-spinner fa-spin"></i><p>Carregando histórico...</p></div>';

      try {
        const { getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        const q = query(
          collection(db, 'logsAlteracoes'),
          where('tipo', '==', 'material_extra'),
          where('opId', '==', opSelecionada.id)
        );

        const snapshot = await getDocs(q);

        if (snapshot.empty) {
          historyContainer.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-history"></i>
              <p>Nenhum histórico de materiais extras encontrado</p>
            </div>
          `;
          return;
        }

        // Ordenar manualmente por data (mais recente primeiro)
        const sortedDocs = snapshot.docs.sort((a, b) => {
          const dateA = a.data().data.toDate();
          const dateB = b.data().data.toDate();
          return dateB - dateA; // Ordem decrescente
        });

        const historyHTML = sortedDocs.map(doc => {
          const log = doc.data();
          const data = log.data.toDate();
          const material = log.materialExtra;

          const isAdicao = log.acao === 'adicao';
          const iconClass = isAdicao ? 'fas fa-plus-circle' : 'fas fa-minus-circle';
          const iconColor = isAdicao ? '#28a745' : '#dc3545';
          const actionText = isAdicao ? 'ADICIONADO' : 'REMOVIDO';
          const bgColor = isAdicao ? '#e8f5e8' : '#f8d7da';
          const borderColor = isAdicao ? '#28a745' : '#dc3545';

          return `
            <div class="history-item" style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: ${bgColor}; border-radius: 5px; margin-bottom: 8px; border: 1px solid ${borderColor};">
              <div style="flex: 1;">
                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                  <i class="${iconClass}" style="color: ${iconColor}; margin-right: 8px;"></i>
                  <strong style="color: ${iconColor};">${actionText}</strong>
                  <span style="margin-left: 10px; background: rgba(0,0,0,0.1); padding: 2px 6px; border-radius: 3px; font-size: 11px;">${data.toLocaleDateString()} ${data.toLocaleTimeString()}</span>
                </div>
                <div style="margin-bottom: 5px;">
                  <strong>${material.codigo}</strong> - ${material.descricao}
                  <span style="margin-left: 10px; font-weight: 600;">${material.quantidade.toFixed(3)} ${material.unidade}</span>
                </div>
                <div style="font-size: 12px; color: #6c757d;">
                  <strong>Por:</strong> ${log.usuario} |
                  <strong>Justificativa:</strong> "${material.justificativa}"
                </div>
              </div>
            </div>
          `;
        }).join('');

        historyContainer.innerHTML = historyHTML;

      } catch (error) {
        console.error('Erro ao carregar histórico:', error);
        historyContainer.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
            <p style="color: #dc3545;">Erro ao carregar histórico: ${error.message}</p>
          </div>
        `;
      }
    };

    // ===== FUNÇÕES PARA ALTERAÇÃO DE STATUS =====

    function updateStatusChangeButton() {
      const btn = document.getElementById('statusChangeBtn');
      if (!btn) return;

      if (opSelecionada) {
        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        btn.title = `Alterar status da OP: ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'} (Status atual: ${opSelecionada.status})`;
        btn.innerHTML = '<i class="fas fa-edit"></i> Alterar Status ✓';
      } else {
        btn.style.background = '';
        btn.title = 'Selecione uma OP primeiro para alterar o status';
        btn.innerHTML = '<i class="fas fa-edit"></i> Alterar Status';
      }
    }

    window.openStatusChangeModal = function() {
      console.log('🔄 Abrindo modal de alteração de status...');

      if (!opSelecionada) {
        alert(`❌ NENHUMA OP SELECIONADA

Para alterar o status de uma OP, você precisa:

1️⃣ Buscar por um produto no campo de busca
2️⃣ Selecionar uma OP da lista que aparecer
3️⃣ Depois clicar em "Alterar Status"

💡 Digite o código de um produto no campo de busca para começar.`);
        return;
      }

      // Limpar formulário
      document.getElementById('newStatus').value = '';
      document.getElementById('statusChangeReason').value = '';
      document.getElementById('statusChangeWarning').style.display = 'none';
      document.getElementById('confirmStatusChange').disabled = true;
      document.getElementById('confirmStatusChange').style.opacity = '0.5';

      // Mostrar informações da OP atual
      const prod = produtosMap[opSelecionada.produtoId];
      const numeroOP = opSelecionada.numeroOP || opSelecionada.numero || 'OP sem número';
      const dataEntrega = opSelecionada.dataEntrega ?
        new Date(opSelecionada.dataEntrega.seconds * 1000).toLocaleDateString() : 'Não definida';

      document.getElementById('currentOPInfo').innerHTML = `
        <div style="background: #e3f2fd; border: 1px solid #90caf9; border-radius: 8px; padding: 15px;">
          <h3 style="margin: 0 0 15px 0; color: #1976d2;">
            <i class="fas fa-cog"></i> ${numeroOP}
          </h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
            <div><strong>Produto:</strong> ${prod?.codigo || 'N/A'}</div>
            <div><strong>Descrição:</strong> ${prod?.descricao || 'N/A'}</div>
            <div><strong>Quantidade:</strong> ${opSelecionada.quantidade} ${getCorrectUnit(opSelecionada)}</div>
            <div><strong>Status Atual:</strong> <span style="background: #fff; padding: 4px 12px; border-radius: 15px; font-weight: 600;">${opSelecionada.status}</span></div>
            <div><strong>Entrega:</strong> ${dataEntrega}</div>
          </div>
        </div>
      `;

      // Configurar eventos
      document.getElementById('newStatus').onchange = updateStatusChangeWarning;
      document.getElementById('statusChangeReason').oninput = validateStatusChangeForm;

      // Mostrar modal com força total
      const modal = document.getElementById('statusChangeModal');
      modal.style.display = 'block';
      modal.style.position = 'fixed';
      modal.style.top = '0';
      modal.style.left = '0';
      modal.style.width = '100vw';
      modal.style.height = '100vh';
      modal.style.zIndex = '999999';
      modal.style.backgroundColor = 'rgba(0,0,0,0.8)';

      // Forçar o modal para frente
      document.body.style.overflow = 'hidden';

      console.log('✅ Modal exibido com força total!');
      console.log('Modal element:', modal);
      console.log('Modal computed style:', window.getComputedStyle(modal));
    };

    function updateStatusChangeWarning() {
      const newStatus = document.getElementById('newStatus').value;
      const warningDiv = document.getElementById('statusChangeWarning');
      const warningText = document.getElementById('warningText');

      if (!newStatus) {
        warningDiv.style.display = 'none';
        validateStatusChangeForm();
        return;
      }

      let warning = '';
      let showWarning = false;

      switch (newStatus) {
        case 'Pendente':
          if (opSelecionada.status === 'Em Produção') {
            warning = `
              <p><strong>Atenção:</strong> Alterar de "Em Produção" para "Pendente" pode:</p>
              <ul style="margin: 10px 0 0 20px;">
                <li>Permitir alterações nos materiais necessários</li>
                <li>Afetar empenhos já criados</li>
                <li>Impactar o controle de produção</li>
              </ul>
            `;
            showWarning = true;
          }
          break;

        case 'Em Produção':
          if (opSelecionada.status === 'Pendente') {
            warning = `
              <p><strong>Informação:</strong> Alterar para "Em Produção" irá:</p>
              <ul style="margin: 10px 0 0 20px;">
                <li>Restringir edições aos materiais básicos</li>
                <li>Permitir apenas adição de materiais extras</li>
                <li>Indicar que a produção foi iniciada</li>
              </ul>
            `;
            showWarning = true;
          } else if (opSelecionada.status === 'Concluída') {
            warning = `
              <p><strong>Atenção:</strong> Alterar de "Concluída" para "Em Produção":</p>
              <ul style="margin: 10px 0 0 20px;">
                <li>Pode afetar relatórios de produção</li>
                <li>Requer justificativa detalhada</li>
                <li>Deve ser aprovado pela supervisão</li>
              </ul>
            `;
            showWarning = true;
          }
          break;

        case 'Concluída':
          warning = `
            <p><strong>Atenção:</strong> Marcar como "Concluída" irá:</p>
            <ul style="margin: 10px 0 0 20px;">
              <li>Impedir qualquer alteração futura</li>
              <li>Finalizar o controle de produção</li>
              <li>Afetar relatórios e estatísticas</li>
            </ul>
          `;
          showWarning = true;
          break;

        case 'Cancelada':
          warning = `
            <p><strong>Atenção:</strong> Cancelar a OP irá:</p>
            <ul style="margin: 10px 0 0 20px;">
              <li>Liberar todos os empenhos de material</li>
              <li>Impedir qualquer alteração futura</li>
              <li>Afetar solicitações de compra vinculadas</li>
            </ul>
          `;
          showWarning = true;
          break;
      }

      if (showWarning) {
        warningText.innerHTML = warning;
        warningDiv.style.display = 'block';
      } else {
        warningDiv.style.display = 'none';
      }

      validateStatusChangeForm();
    }

    function validateStatusChangeForm() {
      const newStatus = document.getElementById('newStatus').value;
      const reason = document.getElementById('statusChangeReason').value.trim();
      const confirmBtn = document.getElementById('confirmStatusChange');

      const isValid = newStatus && reason.length >= 10;
      confirmBtn.disabled = !isValid;
      confirmBtn.style.opacity = isValid ? '1' : '0.5';
      confirmBtn.style.cursor = isValid ? 'pointer' : 'not-allowed';

      if (!isValid && newStatus && reason.length < 10) {
        confirmBtn.title = 'O motivo deve ter pelo menos 10 caracteres';
      } else {
        confirmBtn.title = '';
      }
    }

    window.confirmStatusChange = async function() {
      const newStatus = document.getElementById('newStatus').value;
      const reason = document.getElementById('statusChangeReason').value.trim();

      if (!newStatus || !reason) {
        alert('❌ Preencha todos os campos obrigatórios.');
        return;
      }

      if (reason.length < 10) {
        alert('❌ O motivo deve ter pelo menos 10 caracteres.');
        return;
      }

      const confirmMessage = `
🔄 CONFIRMAR ALTERAÇÃO DE STATUS

OP: ${opSelecionada.numeroOP || opSelecionada.numero || 'OP'}
Status Atual: ${opSelecionada.status}
Novo Status: ${newStatus}

Motivo: ${reason}

⚠️ Esta ação será registrada no histórico da OP.

Deseja continuar?
      `;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        const confirmBtn = document.getElementById('confirmStatusChange');
        const originalText = confirmBtn.textContent;
        confirmBtn.textContent = 'Processando...';
        confirmBtn.disabled = true;

        // Atualizar status da OP no banco
        const opRef = doc(db, "ordensProducao", opSelecionada.id);
        await updateDoc(opRef, {
          status: newStatus,
          ultimaAlteracao: Timestamp.now(),
          alteracaoStatus: {
            statusAnterior: opSelecionada.status,
            novoStatus: newStatus,
            motivo: reason,
            dataAlteracao: Timestamp.now(),
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
          }
        });

        // Registrar log da alteração
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'ALTERACAO_STATUS_OP',
          ordemProducaoId: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          statusAnterior: opSelecionada.status,
          novoStatus: newStatus,
          motivo: reason,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataAlteracao: Timestamp.now(),
          detalhes: {
            produtoId: opSelecionada.produtoId,
            quantidade: opSelecionada.quantidade
          }
        });

        // Atualizar OP local
        opSelecionada.status = newStatus;

        // Mostrar notificação de sucesso
        showSuccessNotification(`
✅ STATUS ALTERADO COM SUCESSO

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
Novo Status: ${newStatus}

A alteração foi registrada no histórico.
        `);

        // Fechar modal
        closeStatusChangeModal();

        // Recarregar a página para refletir as mudanças
        setTimeout(() => {
          window.location.reload();
        }, 2000);

      } catch (error) {
        console.error('Erro ao alterar status:', error);
        alert(`❌ Erro ao alterar status da OP:\n\n${error.message}\n\nTente novamente ou contate o suporte.`);

        // Restaurar botão
        const confirmBtn = document.getElementById('confirmStatusChange');
        confirmBtn.textContent = originalText;
        confirmBtn.disabled = false;
      }
    };

    window.closeStatusChangeModal = function() {
      document.getElementById('statusChangeModal').style.display = 'none';
      document.body.style.overflow = 'auto';
      console.log('❌ Modal fechado');
    };

    // Função de teste para verificar se o modal funciona
    window.testModal = function() {
      console.log('🧪 Teste do modal...');

      // Criar OP fake para teste
      if (!opSelecionada) {
        window.opSelecionada = {
          id: 'teste',
          numeroOP: 'OP-TESTE',
          numero: 'OP-TESTE',
          status: 'Em Produção',
          quantidade: 100,
          produtoId: 'teste',
          dataEntrega: { seconds: Date.now() / 1000 }
        };

        window.produtosMap = {
          'teste': {
            codigo: 'PROD-TESTE',
            descricao: 'Produto de Teste'
          }
        };

        window.getCorrectUnit = () => 'UN';
      }

      openStatusChangeModal();
    };

    // ===== CONTROLE RÁPIDO DE STATUS =====

    function setupQuickStatusControls() {
      console.log('🔧 Configurando controles rápidos de status...');
      const statusSelect = document.getElementById('quickStatusSelect');
      const changeBtn = document.getElementById('quickChangeStatusBtn');

      console.log('Elementos encontrados:', {
        statusSelect: !!statusSelect,
        changeBtn: !!changeBtn,
        opSelecionada: !!opSelecionada
      });

      if (statusSelect && changeBtn) {
        // Limpar listeners anteriores
        statusSelect.removeEventListener('change', statusSelectHandler);

        // Adicionar novo listener
        statusSelect.addEventListener('change', statusSelectHandler);

        // Habilitar controles se há OP selecionada
        if (opSelecionada) {
          statusSelect.disabled = false;
          statusSelect.style.opacity = '1';
          console.log('✅ Controles habilitados para OP:', opSelecionada.numeroOP || opSelecionada.numero);
        } else {
          statusSelect.disabled = true;
          statusSelect.style.opacity = '0.5';
          console.log('⚠️ Controles desabilitados - nenhuma OP selecionada');
        }

        console.log('✅ Event listeners configurados');
      } else {
        console.log('❌ Elementos não encontrados!');
      }
    }

    function statusSelectHandler() {
      const hasSelection = this.value !== '';
      const changeBtn = document.getElementById('quickChangeStatusBtn');

      if (changeBtn) {
        changeBtn.disabled = !hasSelection || !opSelecionada;
        changeBtn.style.opacity = (hasSelection && opSelecionada) ? '1' : '0.5';
        changeBtn.style.cursor = (hasSelection && opSelecionada) ? 'pointer' : 'not-allowed';
        console.log('🔄 Status selecionado:', this.value, 'OP disponível:', !!opSelecionada);
      }
    }

    function updateCurrentStatusDisplay() {
      const statusText = document.getElementById('currentStatusText');
      const statusDisplay = document.getElementById('currentStatusDisplay');

      if (opSelecionada && statusText && statusDisplay) {
        statusText.textContent = opSelecionada.status;

        // Colorir baseado no status
        let bgColor = '#e9ecef';
        let textColor = '#495057';

        switch (opSelecionada.status) {
          case 'Pendente':
            bgColor = '#fff3cd';
            textColor = '#856404';
            break;
          case 'Em Produção':
            bgColor = '#d4edda';
            textColor = '#155724';
            break;
          case 'Concluída':
            bgColor = '#d1ecf1';
            textColor = '#0c5460';
            break;
          case 'Cancelada':
            bgColor = '#f8d7da';
            textColor = '#721c24';
            break;
        }

        statusDisplay.style.background = bgColor;
        statusDisplay.style.color = textColor;
      }
    }

    window.quickChangeStatus = async function() {
      const newStatus = document.getElementById('quickStatusSelect').value;

      if (!opSelecionada) {
        alert('❌ Nenhuma OP selecionada!');
        return;
      }

      if (!newStatus) {
        alert('❌ Selecione um novo status!');
        return;
      }

      if (newStatus === opSelecionada.status) {
        alert('ℹ️ O status selecionado é o mesmo atual!');
        return;
      }

      // Solicitar motivo
      const motivo = prompt(`🔄 ALTERAR STATUS DA OP

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
Status Atual: ${opSelecionada.status}
Novo Status: ${newStatus}

Digite o motivo da alteração:`);

      if (!motivo || motivo.trim().length < 5) {
        alert('❌ Motivo é obrigatório e deve ter pelo menos 5 caracteres!');
        return;
      }

      const confirmMessage = `🔄 CONFIRMAR ALTERAÇÃO

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
${opSelecionada.status} → ${newStatus}

Motivo: ${motivo}

⚠️ Esta ação será registrada no histórico.

Confirmar alteração?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        const btn = document.getElementById('quickChangeStatusBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Alterando...';
        btn.disabled = true;

        // Atualizar no banco
        const { doc, updateDoc, Timestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const { addDoc, collection } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        const opRef = doc(db, "ordensProducao", opSelecionada.id);
        await updateDoc(opRef, {
          status: newStatus,
          ultimaAlteracao: Timestamp.now(),
          alteracaoStatus: {
            statusAnterior: opSelecionada.status,
            novoStatus: newStatus,
            motivo: motivo.trim(),
            dataAlteracao: Timestamp.now(),
            usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema'
          }
        });

        // Registrar log
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'ALTERACAO_STATUS_OP_RAPIDA',
          ordemProducaoId: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          statusAnterior: opSelecionada.status,
          novoStatus: newStatus,
          motivo: motivo.trim(),
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataAlteracao: Timestamp.now(),
          origem: 'CONTROLE_RAPIDO'
        });

        // Atualizar OP local
        opSelecionada.status = newStatus;

        // Atualizar interface
        updateCurrentStatusDisplay();
        document.getElementById('quickStatusSelect').value = '';
        btn.disabled = true;
        btn.style.opacity = '0.5';

        // Restaurar botão
        btn.innerHTML = originalText;

        // Notificação de sucesso
        showSuccessNotification(`✅ STATUS ALTERADO!

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
Novo Status: ${newStatus}

A alteração foi registrada no histórico.`);

        // Recarregar dados se necessário
        setTimeout(() => {
          if (newStatus === 'Pendente') {
            showSuccessNotification('💡 OP agora está editável! Você pode modificar materiais e quantidades.');
          }
        }, 2000);

      } catch (error) {
        console.error('Erro ao alterar status:', error);
        alert(`❌ Erro ao alterar status:\n\n${error.message}`);

        // Restaurar botão
        const btn = document.getElementById('quickChangeStatusBtn');
        btn.innerHTML = '<i class="fas fa-sync-alt"></i> Alterar Status';
        btn.disabled = false;
      }
    };

    // Inicializar controles quando OP for selecionada
    const originalSelecionarOP = window.selecionarOP;
    window.selecionarOP = async function(opId) {
      console.log('🔄 Selecionando OP e configurando controles de status...');
      await originalSelecionarOP(opId);

      // Configurar controles de status
      setupQuickStatusControls();
      updateCurrentStatusDisplay();

      // Configurar controles de flags
      setupFlagsControls();
      updateFlagsDisplay();

      console.log('✅ Controles de status e flags configurados para OP:', opSelecionada?.numeroOP);
    };

    // ===== CONTROLE DE FLAGS/ETAPAS =====

    function setupFlagsControls() {
      console.log('🎯 Configurando controles de flags...');

      if (!opSelecionada) {
        console.log('❌ Nenhuma OP selecionada para configurar flags');

        // Mostrar mensagem no container de flags
        const flagsContainer = document.querySelector('#flagsControls > div:first-child');
        if (flagsContainer) {
          flagsContainer.innerHTML = `
            <div style="text-align: center; color: #666; padding: 20px;">
              <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
              Clique nos círculos para alterar o status das etapas
            </div>
          `;
        }
        return;
      }

      const flagsContainer = document.querySelector('#flagsControls > div:first-child');
      if (!flagsContainer) {
        console.log('❌ Container de flags não encontrado');
        console.log('🔍 Estrutura DOM:', document.querySelector('#flagsControls'));
        return;
      }

      console.log('✅ Container de flags encontrado, OP:', opSelecionada.numeroOP || opSelecionada.numero);

      // Definir etapas do fluxo
      const etapas = [
        {
          id: 'materialTransferido',
          nome: 'Material',
          icone: 'exchange-alt',
          titulo: 'Material Transferido para Produção'
        },
        {
          id: 'saldoValidado',
          nome: 'Saldo',
          icone: 'search',
          titulo: 'Saldo Validado'
        },
        {
          id: 'impressa',
          nome: 'Impressa',
          icone: 'print',
          titulo: 'OP Impressa'
        },
        {
          id: 'enviadaFabrica',
          nome: 'Fábrica',
          icone: 'industry',
          titulo: 'Enviada para Fábrica'
        },
        {
          id: 'podeApontar',
          nome: 'Apontar',
          icone: 'clipboard',
          titulo: 'Pode Apontar Produção'
        }
      ];

      let flagsHTML = '';

      etapas.forEach((etapa, index) => {
        const isCompleted = opSelecionada[etapa.id] || false;
        const canExecute = index === 0 || etapas[index - 1] && opSelecionada[etapas[index - 1].id];

        let buttonClass = 'flag-button ';
        let iconClass = 'fas fa-' + etapa.icone;

        if (isCompleted) {
          buttonClass += 'completed';
          iconClass = 'fas fa-check';
        } else if (canExecute) {
          buttonClass += 'clickable';
        } else {
          buttonClass += 'blocked';
        }

        const clickAction = (isCompleted || canExecute) ? `onclick="toggleFlag('${etapa.id}', '${etapa.nome}')"` : '';

        flagsHTML += `
          <div style="text-align: center;">
            <button class="${buttonClass}" ${clickAction} title="${etapa.titulo}">
              <i class="${iconClass}"></i>
            </button>
            <div class="flag-label">${etapa.nome}</div>
          </div>
        `;
      });

      flagsContainer.innerHTML = flagsHTML;
      console.log('✅ Flags configurados para OP:', opSelecionada.numeroOP);
    }

    function updateFlagsDisplay() {
      // Atualizar apenas se já foi configurado
      if (opSelecionada && document.querySelector('#flagsControls .flag-button')) {
        setupFlagsControls();
      }
    }

    window.toggleFlag = async function(flagId, flagName) {
      if (!opSelecionada) {
        alert('❌ Nenhuma OP selecionada!');
        return;
      }

      const currentValue = opSelecionada[flagId] || false;
      const newValue = !currentValue;
      const action = newValue ? 'ATIVAR' : 'DESATIVAR';

      const confirmMessage = `🎯 ${action} FLAG

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
Flag: ${flagName}
Status Atual: ${currentValue ? '✅ Ativo' : '❌ Inativo'}
Novo Status: ${newValue ? '✅ Ativo' : '❌ Inativo'}

Confirmar alteração?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Atualizar no banco
        const { doc, updateDoc, Timestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
        const { addDoc, collection } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        const opRef = doc(db, "ordensProducao", opSelecionada.id);
        const updateData = {
          [flagId]: newValue,
          ultimaAlteracao: Timestamp.now()
        };

        await updateDoc(opRef, updateData);

        // Registrar log
        await addDoc(collection(db, "logsAlteracoes"), {
          tipo: 'ALTERACAO_FLAG_OP',
          ordemProducaoId: opSelecionada.id,
          numeroOP: opSelecionada.numeroOP || opSelecionada.numero,
          flagAlterado: flagId,
          flagNome: flagName,
          valorAnterior: currentValue,
          novoValor: newValue,
          usuario: JSON.parse(localStorage.getItem('currentUser'))?.nome || 'Sistema',
          dataAlteracao: Timestamp.now(),
          origem: 'CONTROLE_FLAGS'
        });

        // Atualizar OP local
        opSelecionada[flagId] = newValue;

        // Atualizar interface
        updateFlagsDisplay();

        // Notificação de sucesso
        showSuccessNotification(`✅ FLAG ALTERADO!

OP: ${opSelecionada.numeroOP || opSelecionada.numero}
${flagName}: ${newValue ? '✅ Ativado' : '❌ Desativado'}

A alteração foi registrada no histórico.`);

      } catch (error) {
        console.error('Erro ao alterar flag:', error);
        alert(`❌ Erro ao alterar flag:\n\n${error.message}`);
      }
    };

  </script>
</body>
</html>