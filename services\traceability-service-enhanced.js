
/**
 * Serviço de Rastreabilidade Aprimorado
 * Garante sincronismo entre SC, CT e PC
 */

class TraceabilityService {
    constructor() {
        this.db = null;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;
        
        // Aguardar Firebase estar disponível
        while (!window.db) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        this.db = window.db;
        this.initialized = true;
        console.log('🔗 Serviço de Rastreabilidade Inicializado');
    }

    /**
     * Criar cadeia de rastreamento para nova solicitação
     */
    async createTraceabilityChain(solicitacaoId, solicitacaoData) {
        await this.initialize();
        
        try {
            const traceData = {
                solicitacaoId: solicitacaoId,
                numeroSolicitacao: solicitacaoData.numero,
                status: 'CRIADA',
                dataCriacao: new Date(),
                itens: solicitacaoData.itens?.map(item => ({
                    produtoId: item.produtoId,
                    codigo: item.codigo,
                    descricao: item.descricao,
                    quantidade: item.quantidade,
                    status: 'PENDENTE'
                })) || [],
                historico: [{
                    data: new Date(),
                    acao: 'CRIACAO',
                    usuario: localStorage.getItem('currentUser') || 'Sistema',
                    detalhes: 'Solicitação criada'
                }],
                cotacoes: [],
                pedidosCompra: []
            };

            await this.db.collection('rastreabilidade').doc(solicitacaoId).set(traceData);
            
            console.log(`✅ Cadeia de rastreamento criada para SC: ${solicitacaoData.numero}`);
            return traceData;
            
        } catch (error) {
            console.error('❌ Erro ao criar cadeia de rastreamento:', error);
            throw error;
        }
    }

    /**
     * Vincular cotação à solicitação
     */
    async linkCotacaoToSolicitacao(cotacaoId, cotacaoData, solicitacaoId) {
        await this.initialize();
        
        try {
            const traceRef = this.db.collection('rastreabilidade').doc(solicitacaoId);
            const traceDoc = await traceRef.get();
            
            if (!traceDoc.exists) {
                throw new Error('Cadeia de rastreamento não encontrada');
            }

            const traceData = traceDoc.data();
            
            // Adicionar cotação
            const cotacaoInfo = {
                cotacaoId: cotacaoId,
                numeroCotacao: cotacaoData.numero,
                fornecedorId: cotacaoData.fornecedorId,
                fornecedorNome: cotacaoData.fornecedorNome,
                dataAbertura: cotacaoData.dataAbertura || new Date(),
                status: cotacaoData.status || 'ABERTA',
                valorTotal: cotacaoData.valorTotal || 0
            };

            traceData.cotacoes.push(cotacaoInfo);
            
            // Adicionar ao histórico
            traceData.historico.push({
                data: new Date(),
                acao: 'COTACAO_VINCULADA',
                usuario: localStorage.getItem('currentUser') || 'Sistema',
                detalhes: `Cotação ${cotacaoData.numero} vinculada`
            });

            await traceRef.update(traceData);
            
            console.log(`✅ Cotação ${cotacaoData.numero} vinculada à SC`);
            return traceData;
            
        } catch (error) {
            console.error('❌ Erro ao vincular cotação:', error);
            throw error;
        }
    }

    /**
     * Vincular pedido de compra à solicitação
     */
    async linkPedidoToSolicitacao(pedidoId, pedidoData, solicitacaoId) {
        await this.initialize();
        
        try {
            const traceRef = this.db.collection('rastreabilidade').doc(solicitacaoId);
            const traceDoc = await traceRef.get();
            
            if (!traceDoc.exists) {
                throw new Error('Cadeia de rastreamento não encontrada');
            }

            const traceData = traceDoc.data();
            
            // Adicionar pedido
            const pedidoInfo = {
                pedidoId: pedidoId,
                numeroPedido: pedidoData.numero,
                fornecedorId: pedidoData.fornecedorId,
                fornecedorNome: pedidoData.fornecedorNome,
                dataEmissao: pedidoData.dataEmissao || new Date(),
                status: pedidoData.status || 'APROVADO',
                valorTotal: pedidoData.valorTotal || 0,
                cotacaoOrigemId: pedidoData.cotacaoOrigemId
            };

            traceData.pedidosCompra.push(pedidoInfo);
            
            // Atualizar status dos itens
            if (pedidoData.itens) {
                pedidoData.itens.forEach(item => {
                    const itemTrace = traceData.itens.find(i => i.produtoId === item.produtoId);
                    if (itemTrace) {
                        itemTrace.status = 'PEDIDO_GERADO';
                        itemTrace.pedidoId = pedidoId;
                    }
                });
            }
            
            // Adicionar ao histórico
            traceData.historico.push({
                data: new Date(),
                acao: 'PEDIDO_VINCULADO',
                usuario: localStorage.getItem('currentUser') || 'Sistema',
                detalhes: `Pedido ${pedidoData.numero} gerado`
            });

            // Atualizar status geral
            traceData.status = 'PEDIDO_GERADO';

            await traceRef.update(traceData);
            
            console.log(`✅ Pedido ${pedidoData.numero} vinculado à SC`);
            return traceData;
            
        } catch (error) {
            console.error('❌ Erro ao vincular pedido:', error);
            throw error;
        }
    }

    /**
     * Buscar rastreabilidade completa
     */
    async getFullTraceability(solicitacaoId) {
        await this.initialize();
        
        try {
            const traceDoc = await this.db.collection('rastreabilidade').doc(solicitacaoId).get();
            
            if (!traceDoc.exists) {
                return null;
            }

            const traceData = traceDoc.data();
            
            // Buscar detalhes das cotações
            for (let cotacao of traceData.cotacoes) {
                try {
                    const cotacaoDoc = await this.db.collection('cotacoes').doc(cotacao.cotacaoId).get();
                    if (cotacaoDoc.exists) {
                        cotacao.detalhes = cotacaoDoc.data();
                    }
                } catch (error) {
                    console.warn(`Cotação ${cotacao.cotacaoId} não encontrada`);
                }
            }

            // Buscar detalhes dos pedidos
            for (let pedido of traceData.pedidosCompra) {
                try {
                    const pedidoDoc = await this.db.collection('pedidosCompra').doc(pedido.pedidoId).get();
                    if (pedidoDoc.exists) {
                        pedido.detalhes = pedidoDoc.data();
                    }
                } catch (error) {
                    console.warn(`Pedido ${pedido.pedidoId} não encontrado`);
                }
            }

            return traceData;
            
        } catch (error) {
            console.error('❌ Erro ao buscar rastreabilidade:', error);
            throw error;
        }
    }

    /**
     * Atualizar status de item específico
     */
    async updateItemStatus(solicitacaoId, produtoId, novoStatus, detalhes = '') {
        await this.initialize();
        
        try {
            const traceRef = this.db.collection('rastreabilidade').doc(solicitacaoId);
            const traceDoc = await traceRef.get();
            
            if (!traceDoc.exists) {
                throw new Error('Cadeia de rastreamento não encontrada');
            }

            const traceData = traceDoc.data();
            
            // Atualizar item específico
            const item = traceData.itens.find(i => i.produtoId === produtoId);
            if (item) {
                item.status = novoStatus;
                item.ultimaAtualizacao = new Date();
                
                // Adicionar ao histórico
                traceData.historico.push({
                    data: new Date(),
                    acao: 'ITEM_ATUALIZADO',
                    usuario: localStorage.getItem('currentUser') || 'Sistema',
                    detalhes: `Item ${item.codigo} - Status: ${novoStatus}. ${detalhes}`
                });

                await traceRef.update(traceData);
                
                console.log(`✅ Status do item ${item.codigo} atualizado para ${novoStatus}`);
                return traceData;
            }
            
        } catch (error) {
            console.error('❌ Erro ao atualizar status do item:', error);
            throw error;
        }
    }

    /**
     * Gerar relatório de rastreabilidade
     */
    async generateTraceabilityReport(solicitacaoId) {
        const traceData = await this.getFullTraceability(solicitacaoId);
        
        if (!traceData) {
            return null;
        }

        return {
            solicitacao: {
                numero: traceData.numeroSolicitacao,
                status: traceData.status,
                dataCriacao: traceData.dataCriacao,
                totalItens: traceData.itens.length
            },
            cotacoes: traceData.cotacoes.map(c => ({
                numero: c.numeroCotacao,
                fornecedor: c.fornecedorNome,
                status: c.status,
                valor: c.valorTotal
            })),
            pedidos: traceData.pedidosCompra.map(p => ({
                numero: p.numeroPedido,
                fornecedor: p.fornecedorNome,
                status: p.status,
                valor: p.valorTotal
            })),
            historico: traceData.historico.sort((a, b) => new Date(b.data) - new Date(a.data)),
            resumo: {
                totalCotacoes: traceData.cotacoes.length,
                totalPedidos: traceData.pedidosCompra.length,
                itensPendentes: traceData.itens.filter(i => i.status === 'PENDENTE').length,
                itensAtendidos: traceData.itens.filter(i => i.status === 'PEDIDO_GERADO').length
            }
        };
    }
}

// Instância global
window.TraceabilityService = new TraceabilityService();
