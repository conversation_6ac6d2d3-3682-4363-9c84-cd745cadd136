/* 🏗️ ESTILOS BASE - FYRON MRP */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--totvs-gray);
    color: var(--text-color);
    overflow-x: hidden;
    font-size: var(--font-size-base);
    line-height: 1.5;
}

/* 🔄 LOADING STATE */
body.loading::before {
    content: 'Carregando sistema...';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    background: white;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 9999;
}

/* 🎯 UTILITÁRIOS */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

.p-0 { padding: 0; }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.border-radius { border-radius: var(--border-radius); }
.box-shadow { box-shadow: var(--box-shadow); }

.transition { transition: var(--transition); }

/* 🎨 CORES */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

/* 🔘 BOTÕES BASE */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: var(--warning-color);
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* 📱 RESPONSIVIDADE BASE */
@media (max-width: 768px) {
    body {
        font-size: var(--font-size-sm);
    }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-lg);
    }
}

/* 🎭 ANIMAÇÕES */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 6px 20px rgba(108, 92, 231, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 🎯 CLASSES DE ANIMAÇÃO */
.animate-fadeIn { animation: fadeIn var(--animation-normal) ease; }
.animate-slideInRight { animation: slideInRight var(--animation-normal) ease; }
.animate-slideOutRight { animation: slideOutRight var(--animation-normal) ease; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-spin { animation: spin 1s linear infinite; }
