<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Configuração de Saldos Iniciais</title>
    <script type="module" src="js/main.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .warning-banner {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .control-group h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .stats-section {
            padding: 30px;
            background: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: #3498db;
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .upload-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px dashed #3498db;
            text-align: center;
        }

        .upload-section.dragover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .progress-section {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-section {
            padding: 20px;
            background: #2c3e50;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="central_estoque.html" class="back-btn">← Central Estoque</a>

    <div class="container">
        <div class="header">
            <h1>📊 Configuração de Saldos Iniciais</h1>
            <p>Ferramenta especializada para configuração inicial de estoque em novos clientes</p>
        </div>

        <div class="warning-banner">
            ⚠️ ATENÇÃO: Esta ferramenta é específica para NOVOS CLIENTES. Use com cuidado em sistemas já em produção!
        </div>

        <div class="controls">
            <div class="controls-grid">
                <!-- Configuração Manual -->
                <div class="control-group">
                    <h3>🎛️ Configuração Manual</h3>
                    <div class="input-group">
                        <label>Armazém Padrão:</label>
                        <select id="armazemPadrao">
                            <option value="">Selecione um armazém...</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Data de Referência:</label>
                        <input type="date" id="dataReferencia" value="">
                    </div>
                    <button class="btn btn-primary" onclick="abrirEditorManual()">
                        🎛️ Abrir Editor Manual
                    </button>
                </div>

                <!-- Importação de Arquivo -->
                <div class="control-group">
                    <h3>📁 Importação de Arquivo</h3>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">
                        Formatos aceitos: CSV, Excel (.xlsx)
                    </p>
                    <button class="btn btn-success" onclick="document.getElementById('fileInput').click()">
                        📁 Selecionar Arquivo
                    </button>
                    <input type="file" id="fileInput" class="file-input" accept=".csv,.xlsx" onchange="processarArquivo(this)">
                </div>

                <!-- Configuração Rápida -->
                <div class="control-group">
                    <h3>⚡ Configuração Rápida</h3>
                    <button class="btn btn-warning" onclick="zerarTodosEstoques()">
                        🗑️ Zerar Todos os Estoques
                    </button>
                    <button class="btn btn-success" onclick="criarEstoquesBasicos()">
                        ✨ Criar Estoques Básicos
                    </button>
                </div>

                <!-- Validação -->
                <div class="control-group">
                    <h3>✅ Validação</h3>
                    <button class="btn btn-primary" onclick="validarConfiguracao()">
                        🔍 Validar Configuração
                    </button>
                    <button class="btn btn-success" onclick="gerarRelatorioInicial()">
                        📊 Relatório Inicial
                    </button>
                </div>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="stats-section">
            <h3 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">📈 Status da Configuração</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalProdutos">-</div>
                    <div class="stat-label">Produtos Cadastrados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="produtosComEstoque">-</div>
                    <div class="stat-label">Com Saldo Inicial</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalArmazens">-</div>
                    <div class="stat-label">Armazéns Ativos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="valorTotalEstoque">-</div>
                    <div class="stat-label">Valor Total</div>
                </div>
            </div>
        </div>

        <!-- Seção de Upload -->
        <div class="upload-section" id="uploadSection" style="display: none;">
            <h3>📁 Processamento de Arquivo</h3>
            <p>Arraste e solte o arquivo aqui ou clique para selecionar</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                Selecionar Arquivo
            </button>
        </div>

        <!-- Barra de Progresso -->
        <div class="progress-section" id="progressSection">
            <h4>Processando...</h4>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Preparando...</div>
        </div>

        <!-- Log de Atividades -->
        <div class="log-section" id="logSection">
            <div id="logContent">Sistema iniciado. Aguardando comandos...</div>
        </div>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            addDoc, 
            updateDoc, 
            deleteDoc,
            doc,
            query,
            where,
            Timestamp,
            writeBatch
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Variáveis globais
        let produtos = [];
        let armazens = [];
        let estoques = [];

        // Inicializar
        document.addEventListener('DOMContentLoaded', () => {
            carregarDados();
            configurarDataReferencia();
        });

        // Configurar data de referência como hoje
        function configurarDataReferencia() {
            const hoje = new Date();
            document.getElementById('dataReferencia').value = hoje.toISOString().split('T')[0];
        }

        // Carregar dados iniciais
        async function carregarDados() {
            try {
                log('📊 Carregando dados do sistema...', 'info');
                
                const [produtosSnap, armazensSnap, estoquesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "armazens")),
                    getDocs(collection(db, "estoques"))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Preencher select de armazéns
                const selectArmazem = document.getElementById('armazemPadrao');
                selectArmazem.innerHTML = '<option value="">Selecione um armazém...</option>';
                armazens.forEach(armazem => {
                    selectArmazem.innerHTML += `<option value="${armazem.id}">${armazem.nome}</option>`;
                });

                atualizarEstatisticas();
                log(`✅ Dados carregados: ${produtos.length} produtos, ${armazens.length} armazéns`, 'success');

            } catch (error) {
                log(`❌ Erro ao carregar dados: ${error.message}`, 'error');
            }
        }

        // Atualizar estatísticas
        function atualizarEstatisticas() {
            const produtosComEstoque = new Set(estoques.map(e => e.produtoId)).size;
            const valorTotal = estoques.reduce((sum, e) => {
                const produto = produtos.find(p => p.id === e.produtoId);
                // Corrigido: usar saldo disponível real para cálculo de valor
                const saldo = e.saldo || 0;
                const reservado = e.saldoReservado || 0;
                const empenhado = e.saldoEmpenhado || 0;
                const saldoDisponivel = Math.max(0, saldo - reservado - empenhado);
                return sum + (saldoDisponivel * (produto?.valorUnitario || 0));
            }, 0);

            document.getElementById('totalProdutos').textContent = produtos.length.toLocaleString();
            document.getElementById('produtosComEstoque').textContent = produtosComEstoque.toLocaleString();
            document.getElementById('totalArmazens').textContent = armazens.length.toLocaleString();
            document.getElementById('valorTotalEstoque').textContent = 
                'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
        }

        // Abrir editor manual
        window.abrirEditorManual = function() {
            const armazemId = document.getElementById('armazemPadrao').value;
            if (!armazemId) {
                alert('⚠️ Selecione um armazém padrão primeiro!');
                return;
            }

            const url = `editor_saldos_estoque.html?modo=inicial&armazem=${armazemId}`;
            window.open(url, '_blank');
            log('🎛️ Editor manual aberto para configuração inicial', 'info');
        };

        // Zerar todos os estoques
        window.zerarTodosEstoques = async function() {
            if (!confirm('⚠️ ATENÇÃO: Isso irá ZERAR todos os saldos de estoque!\n\nEsta ação é IRREVERSÍVEL!\n\nDeseja continuar?')) {
                return;
            }

            if (!confirm('🚨 CONFIRMAÇÃO FINAL: Tem certeza absoluta que deseja zerar todos os estoques?')) {
                return;
            }

            try {
                log('🗑️ Iniciando processo de zeragem de estoques...', 'warning');
                mostrarProgresso(true);

                const batch = writeBatch(db);
                let processados = 0;

                for (const estoque of estoques) {
                    batch.update(doc(db, "estoques", estoque.id), {
                        saldo: 0,
                        saldoReservado: 0,
                        ultimaMovimentacao: Timestamp.now(),
                        observacoes: 'Zerado para configuração inicial'
                    });
                    
                    processados++;
                    atualizarProgresso((processados / estoques.length) * 100, 
                        `Zerando estoque ${processados}/${estoques.length}`);
                }

                await batch.commit();
                
                log('✅ Todos os estoques foram zerados com sucesso!', 'success');
                mostrarProgresso(false);
                carregarDados();

            } catch (error) {
                log(`❌ Erro ao zerar estoques: ${error.message}`, 'error');
                mostrarProgresso(false);
            }
        };

        // Criar estoques básicos
        window.criarEstoquesBasicos = async function() {
            const armazemId = document.getElementById('armazemPadrao').value;
            if (!armazemId) {
                alert('⚠️ Selecione um armazém padrão primeiro!');
                return;
            }

            try {
                log('✨ Criando registros básicos de estoque...', 'info');
                mostrarProgresso(true);

                const batch = writeBatch(db);
                let criados = 0;

                for (const produto of produtos) {
                    // Verificar se já existe estoque para este produto neste armazém
                    const estoqueExistente = estoques.find(e => 
                        e.produtoId === produto.id && e.armazemId === armazemId
                    );

                    if (!estoqueExistente) {
                        const novoEstoqueRef = doc(collection(db, "estoques"));
                        batch.set(novoEstoqueRef, {
                            produtoId: produto.id,
                            armazemId: armazemId,
                            saldo: 0,
                            saldoReservado: 0,
                            ultimaMovimentacao: Timestamp.now(),
                            observacoes: 'Criado automaticamente para configuração inicial'
                        });
                        criados++;
                    }

                    atualizarProgresso((produtos.indexOf(produto) / produtos.length) * 100, 
                        `Processando produto ${produtos.indexOf(produto) + 1}/${produtos.length}`);
                }

                if (criados > 0) {
                    await batch.commit();
                    log(`✅ ${criados} registros de estoque criados com sucesso!`, 'success');
                } else {
                    log('ℹ️ Todos os produtos já possuem registros de estoque', 'info');
                }

                mostrarProgresso(false);
                carregarDados();

            } catch (error) {
                log(`❌ Erro ao criar estoques básicos: ${error.message}`, 'error');
                mostrarProgresso(false);
            }
        };

        // Validar configuração
        window.validarConfiguracao = function() {
            log('🔍 Iniciando validação da configuração...', 'info');
            
            const problemas = [];
            
            // Verificar produtos sem estoque
            const produtosSemEstoque = produtos.filter(produto => 
                !estoques.some(estoque => estoque.produtoId === produto.id)
            );
            
            if (produtosSemEstoque.length > 0) {
                problemas.push(`❌ ${produtosSemEstoque.length} produtos sem registro de estoque`);
            }
            
            // Verificar saldos negativos
            const saldosNegativos = estoques.filter(estoque => (estoque.saldo || 0) < 0);
            if (saldosNegativos.length > 0) {
                problemas.push(`⚠️ ${saldosNegativos.length} produtos com saldo negativo`);
            }
            
            // Verificar armazéns sem produtos
            const armazensSemProdutos = armazens.filter(armazem => 
                !estoques.some(estoque => estoque.armazemId === armazem.id)
            );
            
            if (armazensSemProdutos.length > 0) {
                problemas.push(`ℹ️ ${armazensSemProdutos.length} armazéns sem produtos`);
            }
            
            if (problemas.length === 0) {
                log('✅ Configuração validada com sucesso! Nenhum problema encontrado.', 'success');
            } else {
                log('⚠️ Problemas encontrados na validação:', 'warning');
                problemas.forEach(problema => log(`  • ${problema}`, 'warning'));
            }
        };

        // Gerar relatório inicial
        window.gerarRelatorioInicial = function() {
            const relatorioWindow = window.open('', '_blank', 'width=1000,height=700');
            
            const produtosComEstoque = produtos.filter(produto => 
                estoques.some(estoque => estoque.produtoId === produto.id && (estoque.saldo || 0) > 0)
            );
            
            relatorioWindow.document.write(`
                <html>
                <head>
                    <title>Relatório de Configuração Inicial</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #2c3e50; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .summary { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>📊 Relatório de Configuração Inicial</h1>
                    <p><strong>Data:</strong> ${new Date().toLocaleString()}</p>
                    
                    <div class="summary">
                        <h3>📈 Resumo</h3>
                        <p><strong>Total de Produtos:</strong> ${produtos.length}</p>
                        <p><strong>Produtos com Estoque:</strong> ${produtosComEstoque.length}</p>
                        <p><strong>Total de Armazéns:</strong> ${armazens.length}</p>
                        <p><strong>Registros de Estoque:</strong> ${estoques.length}</p>
                    </div>
                    
                    <h3>📦 Produtos com Saldo Inicial</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Saldo Total</th>
                                <th>Unidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${produtosComEstoque.map(produto => {
                                const saldoTotal = estoques
                                    .filter(e => e.produtoId === produto.id)
                                    .reduce((sum, e) => sum + (e.saldo || 0), 0);
                                
                                return `
                                    <tr>
                                        <td>${produto.codigo}</td>
                                        <td>${produto.descricao}</td>
                                        <td>${saldoTotal.toLocaleString()}</td>
                                        <td>${produto.unidade || 'UN'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </body>
                </html>
            `);
            
            relatorioWindow.document.close();
            log('📊 Relatório inicial gerado com sucesso!', 'success');
        };

        // Processar arquivo importado
        window.processarArquivo = function(input) {
            const file = input.files[0];
            if (!file) return;

            log(`📁 Processando arquivo: ${file.name}`, 'info');
            
            // Aqui você implementaria a lógica de processamento do arquivo
            // Por enquanto, apenas um placeholder
            alert('🚧 Funcionalidade de importação em desenvolvimento!\n\nPor enquanto, use o Editor Manual para configurar os saldos.');
        };

        // Funções auxiliares
        function log(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            
            logContent.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        function mostrarProgresso(show) {
            document.getElementById('progressSection').style.display = show ? 'block' : 'none';
            if (!show) {
                atualizarProgresso(0, 'Preparando...');
            }
        }

        function atualizarProgresso(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
    </script>
</body>
</html>
