/* 📄 CONTEÚDO PRINCIPAL - FYRON MRP */

.main-content {
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
    min-height: 100vh;
    transition: var(--transition);
    background: var(--totvs-gray);
    position: relative;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #0a4d8c, var(--primary-color));
}

.main-content h1 {
    text-align: center;
    margin-bottom: 40px;
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 🏠 HEADER PRINCIPAL */
.main-header {
    background: white;
    padding: var(--spacing-lg) var(--spacing-xl);
    margin: calc(-1 * var(--spacing-xl)) calc(-1 * var(--spacing-xl)) var(--spacing-xl) calc(-1 * var(--spacing-xl));
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-content-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-section h1 {
    margin: 0;
    font-size: var(--font-size-xxl);
    color: var(--primary-color);
    font-weight: 600;
}

.welcome-section p {
    margin: 5px 0 0 0;
    color: var(--text-muted);
    font-size: var(--font-size-base);
}

.system-info {
    text-align: right;
    font-size: 13px;
    color: var(--text-muted);
}

.system-info .status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: #e8f5e8;
    color: #2e7d32;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-top: var(--spacing-xs);
}

/* 🖼️ IMAGENS */
.center-image {
    display: block !important;
    max-width: 100% !important;
    height: auto !important;
    margin: var(--spacing-lg) auto 0 auto !important;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
}

#companyLogo {
    display: block !important;
    max-width: 100% !important;
    height: auto !important;
}

#centerImage {
    display: block !important;
    max-width: 100% !important;
    height: auto !important;
    margin: var(--spacing-lg) auto 0 auto !important;
}

/* ℹ️ INFORMAÇÕES DE VERSÃO */
.version-info {
    text-align: center;
    color: #666;
    font-size: 0.8em;
    margin-top: 40px;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #eee;
}

/* 📤 BOTÃO DE EXPORTAÇÃO */
.export-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.export-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

/* 🌟 BOTÕES DESTACADOS */
.highlight-btn {
    background-color: #ff9800;
    color: white;
}

.highlight-btn:hover {
    background-color: #e68a00;
}

/* 📱 RESPONSIVIDADE */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .main-header {
        margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-md)) var(--spacing-lg) calc(-1 * var(--spacing-md));
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .header-content-main {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .welcome-section h1 {
        font-size: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-md) var(--spacing-sm);
    }

    .main-header {
        margin: calc(-1 * var(--spacing-md)) calc(-1 * var(--spacing-sm)) var(--spacing-md) calc(-1 * var(--spacing-sm));
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .welcome-section h1 {
        font-size: var(--font-size-xl);
    }

    .center-image {
        max-width: 90% !important;
    }
}

/* 🎨 CARDS E CONTAINERS */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-muted);
    margin: var(--spacing-xs) 0 0 0;
}

/* 📊 GRID SYSTEM */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

@media (max-width: 768px) {
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
}

/* 🎯 DASHBOARD WIDGETS */
.widget {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.widget-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
}

.widget-content {
    text-align: center;
}

.widget-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.widget-label {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 🎨 CORES DOS WIDGETS */
.widget-primary .widget-icon { background: var(--primary-color); }
.widget-success .widget-icon { background: var(--success-color); }
.widget-warning .widget-icon { background: var(--warning-color); }
.widget-danger .widget-icon { background: var(--danger-color); }
.widget-info .widget-icon { background: var(--info-color); }
