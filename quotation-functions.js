// Função para gerar cotação
async function createQuotation(
  requestId,
  selectedItems,
  solicitacoes,
  fornecedores,
  getDocs,
  collection,
  db,
  generateQuotationNumber,
  Timestamp,
  currentUser,
  addDoc,
  updateDoc,
  doc,
  showNotification,
  loadInitialData,
  loadRequests,
) {
  try {
    if (!requestId) {
      throw new Error("ID da solicitação inválido ou ausente")
    }

    const solicitacao = solicitacoes.find((s) => s.id === requestId)
    if (!solicitacao) {
      throw new Error("Solicitação não encontrada")
    }

    // Validar fornecedor
    if (!solicitacao.fornecedorId) {
      throw new Error(
        "Fornecedor não definido na solicitação. Por favor, edite a solicitação e selecione um fornecedor.",
      )
    }

    // Verificar se o fornecedor existe
    const fornecedor = fornecedores.find((f) => f.id === solicitacao.fornecedorId)
    if (!fornecedor) {
      throw new Error("Fornecedor selecionado não foi encontrado na base de dados.")
    }

    // Verificar se o fornecedor está homologado
    if (fornecedor.statusHomologacao !== "Homologado") {
      throw new Error("Apenas fornecedores homologados podem receber cotações.")
    }

    // Verificar se já existe cotação
    const cotacoesSnap = await getDocs(collection(db, "cotacoes"))
    const hasQuotation = cotacoesSnap.docs.some((doc) => doc.data().solicitacaoId === requestId)
    if (hasQuotation) {
      throw new Error("Esta solicitação já possui uma cotação")
    }

    // Processar itens com fallback para dados faltantes
    const itensParaCotacao = selectedItems.map((item) => {
      // Determine the final quantity for the quotation, using fallbacks
      const finalQuantidade =
        item.quantidadeCompra !== undefined && item.quantidadeCompra !== null
          ? item.quantidadeCompra
          : item.quantidadeInterna !== undefined && item.quantidadeInterna !== null
            ? item.quantidadeInterna
            : item.quantidade !== undefined && item.quantidade !== null
              ? item.quantidade
              : undefined

      // Determine the final unit for the quotation, using fallbacks
      const finalUnidade =
        item.unidadeCompra !== undefined && item.unidadeCompra !== null
          ? item.unidadeCompra
          : item.unidadeInterna !== undefined && item.unidadeInterna !== null
            ? item.unidadeInterna
            : item.unidade !== undefined && item.unidade !== null
              ? item.unidade
              : undefined

      // Validar se temos todos os dados necessários
      if (
        finalQuantidade === undefined ||
        finalQuantidade === null ||
        finalUnidade === undefined ||
        finalUnidade === null
      ) {
        console.error(`Dados incompletos para item ${item.codigo}:`, item)
        throw new Error(`O item ${item.codigo} (${item.descricao}) não possui informações completas para cotação.`)
      }

      return {
        codigo: item.codigo,
        descricao: item.descricao,
        quantidade: finalQuantidade,
        unidade: finalUnidade,
        produtoId: item.produtoId,
        fatorConversao: item.fatorConversao || 1,
      }
    })

    // Transferir datas da solicitação para a cotação
    let dataLimite = null;
    if (solicitacao.dataLimiteAprovacao) {
      dataLimite = solicitacao.dataLimiteAprovacao;
    } else if (solicitacao.dataNecessidade) {
      dataLimite = solicitacao.dataNecessidade;
    }

    const cotacaoData = {
      numero: await generateQuotationNumber(),
      solicitacaoId: requestId,
      centroCustoId: solicitacao.centroCustoId,
      fornecedorId: solicitacao.fornecedorId,
      fornecedorNome: fornecedor.razaoSocial, // Adicionar nome do fornecedor para facilitar consultas
      dataLimite: dataLimite,
      itens: itensParaCotacao,
      status: "PENDENTE",
      dataCriacao: Timestamp.now(),
      criadoPor: currentUser.nome,
    }

    console.log("Dados da cotação:", cotacaoData) // Debug log

    await addDoc(collection(db, "cotacoes"), cotacaoData)
    await updateDoc(doc(db, "solicitacoesCompra", requestId), {
      status: "COTACAO",
      geradoPor: currentUser.nome,
    })

    showNotification("Cotação gerada com sucesso!", "success")
    await loadInitialData()
    await loadRequests()
    return true
  } catch (error) {
    console.error("Erro ao gerar cotação:", error)
    showNotification(`Erro ao gerar cotação: ${error.message}`, "error")
    throw error
  }
}
