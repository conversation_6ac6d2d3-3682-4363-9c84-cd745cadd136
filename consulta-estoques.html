<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>📦 Consulta de Estoques</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
    .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .filters { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px; }
    .form-group { display: flex; flex-direction: column; }
    .form-group label { font-weight: bold; margin-bottom: 5px; }
    .form-control { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; margin: 5px; }
    .btn-primary { background: #3498db; color: white; }
    .btn-success { background: #27ae60; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
    .table th, .table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
    .table th { background: #34495e; color: white; }
    .table tbody tr:hover { background: #f8f9fa; }
    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
    .stat-card { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 8px; text-align: center; }
    .stat-number { font-size: 2em; font-weight: bold; }
    .stat-label { font-size: 0.9em; opacity: 0.9; }
    .alert { padding: 15px; border-radius: 4px; margin: 10px 0; }
    .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📦 Consulta de Estoques</h1>
      <p>Visualização de saldos e movimentações</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats">
      <div class="stat-card">
        <div class="stat-number" id="totalProdutos">-</div>
        <div class="stat-label">Total Produtos</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954);">
        <div class="stat-number" id="produtosComEstoque">-</div>
        <div class="stat-label">Com Estoque</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
        <div class="stat-number" id="produtosSemEstoque">-</div>
        <div class="stat-label">Sem Estoque</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
        <div class="stat-number" id="valorTotalEstoque">-</div>
        <div class="stat-label">Valor Total</div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="filters">
      <div class="filter-row">
        <div class="form-group">
          <label>Buscar Produto:</label>
          <input type="text" id="searchProduct" class="form-control" placeholder="Código ou descrição..." oninput="filterProducts()">
        </div>
        <div class="form-group">
          <label>Armazém:</label>
          <select id="armazemFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todos os armazéns</option>
          </select>
        </div>
        <div class="form-group">
          <label>Tipo de Produto:</label>
          <select id="tipoFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todos os tipos</option>
            <option value="MP">Matéria Prima</option>
            <option value="SP">Semi Produto</option>
            <option value="PA">Produto Acabado</option>
            <option value="AC">Acessório</option>
          </select>
        </div>
        <div class="form-group">
          <label>Situação:</label>
          <select id="situacaoFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todas</option>
            <option value="COM_ESTOQUE">Com Estoque</option>
            <option value="SEM_ESTOQUE">Sem Estoque</option>
            <option value="ESTOQUE_BAIXO">Estoque Baixo</option>
          </select>
        </div>
      </div>
      <div style="text-align: center;">
        <button class="btn btn-primary" onclick="exportToExcel()">📊 Exportar Excel</button>
        <button class="btn btn-info" onclick="window.location.href='movimentacao_armazem.html'">🔄 Movimentar Estoque</button>
        <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 Voltar</button>
      </div>
    </div>

    <!-- Tabela de Estoques -->
    <div class="alert alert-info">
      <strong>ℹ️ Informação:</strong> Esta tela é apenas para consulta. Para movimentar estoque, use o botão "Movimentar Estoque" acima.
    </div>

    <table class="table" id="estoqueTable">
      <thead>
        <tr>
          <th>Código</th>
          <th>Descrição</th>
          <th>Tipo</th>
          <th>Unidade</th>
          <th>Armazém</th>
          <th>Saldo Atual</th>
          <th>Valor Unit.</th>
          <th>Valor Total</th>
          <th>Última Mov.</th>
        </tr>
      </thead>
      <tbody id="estoqueTableBody">
        <tr>
          <td colspan="9" style="text-align: center; padding: 40px;">
            Carregando dados...
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estoques = [];
    let armazens = [];
    let allData = [];

    document.addEventListener('DOMContentLoaded', async () => {
      await loadData();
      updateStats();
      renderTable();
    });

    async function loadData() {
      try {
        const [produtosSnap, estoquesSnap, armazensSnap] = await Promise.all([
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "estoques")),
          getDocs(collection(db, "armazens"))
        ]);

        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Combinar dados
        allData = [];
        produtos.forEach(produto => {
          const produtoEstoques = estoques.filter(e => e.produtoId === produto.id);
          
          if (produtoEstoques.length === 0) {
            allData.push({
              ...produto,
              armazemId: null,
              armazemNome: 'Sem estoque',
              saldo: 0,
              valorUnitario: produto.precoVenda || produto.custoMedio || 0,
              valorTotal: 0,
              ultimaMovimentacao: null
            });
          } else {
            produtoEstoques.forEach(estoque => {
              const armazem = armazens.find(a => a.id === estoque.armazemId);
              allData.push({
                ...produto,
                armazemId: estoque.armazemId,
                armazemNome: armazem ? `${armazem.codigo} - ${armazem.nome}` : 'Armazém não encontrado',
                saldo: estoque.saldo || 0,
                valorUnitario: produto.precoVenda || produto.custoMedio || 0,
                valorTotal: (estoque.saldo || 0) * (produto.precoVenda || produto.custoMedio || 0),
                ultimaMovimentacao: estoque.ultimaMovimentacao
              });
            });
          }
        });

        // Popular filtros
        populateFilters();

      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        document.getElementById('estoqueTableBody').innerHTML = 
          '<tr><td colspan="9" style="text-align: center; color: red;">Erro ao carregar dados</td></tr>';
      }
    }

    function populateFilters() {
      const armazemFilter = document.getElementById('armazemFilter');
      armazemFilter.innerHTML = '<option value="">Todos os armazéns</option>';
      
      armazens.forEach(armazem => {
        armazemFilter.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
      });
    }

    function updateStats() {
      const totalProdutos = produtos.length;
      const produtosComEstoque = allData.filter(item => item.saldo > 0).length;
      const produtosSemEstoque = totalProdutos - produtosComEstoque;
      const valorTotal = allData.reduce((sum, item) => sum + item.valorTotal, 0);

      document.getElementById('totalProdutos').textContent = totalProdutos;
      document.getElementById('produtosComEstoque').textContent = produtosComEstoque;
      document.getElementById('produtosSemEstoque').textContent = produtosSemEstoque;
      document.getElementById('valorTotalEstoque').textContent = 
        'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
    }

    function renderTable(data = allData) {
      const tbody = document.getElementById('estoqueTableBody');
      tbody.innerHTML = '';

      if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">Nenhum produto encontrado</td></tr>';
        return;
      }

      data.forEach(item => {
        const row = document.createElement('tr');
        
        // Cor da linha baseada no saldo
        if (item.saldo === 0) {
          row.style.backgroundColor = '#ffebee';
        } else if (item.saldo < 10) {
          row.style.backgroundColor = '#fff3e0';
        }

        const ultimaMovFormatada = item.ultimaMovimentacao ? 
          (item.ultimaMovimentacao.toDate ? 
            item.ultimaMovimentacao.toDate().toLocaleDateString('pt-BR') : 
            new Date(item.ultimaMovimentacao.seconds * 1000).toLocaleDateString('pt-BR')
          ) : '-';

        row.innerHTML = `
          <td><strong>${item.codigo || 'N/A'}</strong></td>
          <td>${item.descricao || 'N/A'}</td>
          <td><span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">${item.tipo || 'N/A'}</span></td>
          <td>${item.unidade || 'UN'}</td>
          <td>${item.armazemNome}</td>
          <td style="text-align: right; font-weight: bold; ${item.saldo === 0 ? 'color: #d32f2f;' : item.saldo < 10 ? 'color: #f57c00;' : 'color: #388e3c;'}">${item.saldo.toFixed(3)}</td>
          <td style="text-align: right;">R$ ${item.valorUnitario.toFixed(2)}</td>
          <td style="text-align: right; font-weight: bold;">R$ ${item.valorTotal.toFixed(2)}</td>
          <td>${ultimaMovFormatada}</td>
        `;
        
        tbody.appendChild(row);
      });
    }

    window.filterProducts = function() {
      const searchTerm = document.getElementById('searchProduct').value.toLowerCase();
      const armazemFilter = document.getElementById('armazemFilter').value;
      const tipoFilter = document.getElementById('tipoFilter').value;
      const situacaoFilter = document.getElementById('situacaoFilter').value;

      let filteredData = allData.filter(item => {
        const matchesSearch = !searchTerm || 
          (item.codigo && item.codigo.toLowerCase().includes(searchTerm)) ||
          (item.descricao && item.descricao.toLowerCase().includes(searchTerm));
        
        const matchesArmazem = !armazemFilter || item.armazemId === armazemFilter;
        const matchesTipo = !tipoFilter || item.tipo === tipoFilter;
        
        let matchesSituacao = true;
        if (situacaoFilter === 'COM_ESTOQUE') {
          matchesSituacao = item.saldo > 0;
        } else if (situacaoFilter === 'SEM_ESTOQUE') {
          matchesSituacao = item.saldo === 0;
        } else if (situacaoFilter === 'ESTOQUE_BAIXO') {
          matchesSituacao = item.saldo > 0 && item.saldo < 10;
        }

        return matchesSearch && matchesArmazem && matchesTipo && matchesSituacao;
      });

      renderTable(filteredData);
    };

    window.exportToExcel = function() {
      const data = allData.map(item => ({
        'Código': item.codigo || '',
        'Descrição': item.descricao || '',
        'Tipo': item.tipo || '',
        'Unidade': item.unidade || '',
        'Armazém': item.armazemNome || '',
        'Saldo': item.saldo,
        'Valor Unitário': item.valorUnitario,
        'Valor Total': item.valorTotal,
        'Última Movimentação': item.ultimaMovimentacao ? 
          (item.ultimaMovimentacao.toDate ? 
            item.ultimaMovimentacao.toDate().toLocaleDateString('pt-BR') : 
            new Date(item.ultimaMovimentacao.seconds * 1000).toLocaleDateString('pt-BR')
          ) : ''
      }));

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Estoques");
      XLSX.writeFile(wb, `estoques_${new Date().toISOString().split('T')[0]}.xlsx`);
    };
  </script>
</body>
</html>