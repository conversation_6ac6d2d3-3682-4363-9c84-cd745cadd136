// 🔒 SISTEMA DE AUTENTICAÇÃO - FYRON MRP

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 horas
        this.init();
    }

    init() {
        this.loadCurrentUser();
        this.setupSessionCheck();
        this.setupLogoutHandler();
    }

    // 👤 CARREGAR USUÁRIO ATUAL
    loadCurrentUser() {
        try {
            const userData = localStorage.getItem('currentUser');
            const loginTime = localStorage.getItem('loginTime');

            if (!userData || !loginTime) {
                this.redirectToLogin('Usuário não autenticado');
                return;
            }

            const user = JSON.parse(userData);
            
            // Validar dados do usuário
            if (!this.isValidUser(user)) {
                this.redirectToLogin('Dados de usuário inválidos');
                return;
            }

            // Verificar se sessão não expirou
            if (this.isSessionExpired(parseInt(loginTime))) {
                this.redirectToLogin('Sessão expirada');
                return;
            }

            this.currentUser = user;
            this.updateUserDisplay();
            console.log('✅ Usuário autenticado:', user.nome);

        } catch (error) {
            console.error('🔒 Erro ao carregar usuário:', error);
            this.redirectToLogin('Erro na autenticação');
        }
    }

    // ✅ VALIDAR DADOS DO USUÁRIO
    isValidUser(user) {
        return user && 
               typeof user.id === 'string' && user.id.length > 0 &&
               typeof user.nome === 'string' && user.nome.length > 0 &&
               typeof user.email === 'string' && user.email.includes('@');
    }

    // ⏰ VERIFICAR SE SESSÃO EXPIROU
    isSessionExpired(loginTime) {
        return (Date.now() - loginTime) > this.sessionTimeout;
    }

    // 🔄 REDIRECIONAR PARA LOGIN
    redirectToLogin(reason) {
        console.log('🔒', reason, '- redirecionando para login');
        this.clearSession();
        window.location.href = 'login.html';
    }

    // 🗑️ LIMPAR SESSÃO
    clearSession() {
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
        localStorage.removeItem('userPermissions');
        localStorage.removeItem('menuState');
    }

    // 🖥️ ATUALIZAR DISPLAY DO USUÁRIO
    updateUserDisplay() {
        if (!this.currentUser) return;

        const userNameElement = document.getElementById('currentUserName');
        const userLevelElement = document.getElementById('currentUserLevel');

        if (userNameElement) {
            userNameElement.textContent = this.currentUser.nome;
        }

        if (userLevelElement) {
            userLevelElement.textContent = this.getUserLevel();
        }
    }

    // 📊 OBTER NÍVEL DO USUÁRIO
    getUserLevel() {
        if (!this.currentUser) return 'Visitante';
        
        const level = this.currentUser.nivel || this.currentUser.level || 1;
        const levels = {
            1: 'Operador',
            2: 'Supervisor',
            3: 'Coordenador',
            9: 'Administrador'
        };
        
        return levels[level] || `Nível ${level}`;
    }

    // 🔐 VERIFICAR PERMISSÃO
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        // Administradores têm todas as permissões
        if (this.currentUser.nivel === 9 || this.currentUser.level === 9) {
            return true;
        }

        // Verificar permissões específicas
        const permissions = this.currentUser.permissions || [];
        return permissions.includes(permission);
    }

    // 🔍 VERIFICAR NÍVEL MÍNIMO
    hasMinLevel(minLevel) {
        if (!this.currentUser) return false;
        
        const userLevel = this.currentUser.nivel || this.currentUser.level || 1;
        return userLevel >= minLevel;
    }

    // ⏰ CONFIGURAR VERIFICAÇÃO DE SESSÃO
    setupSessionCheck() {
        // Verificar sessão a cada 5 minutos
        setInterval(() => {
            const loginTime = localStorage.getItem('loginTime');
            if (loginTime && this.isSessionExpired(parseInt(loginTime))) {
                this.showNotification('⏰ Sua sessão expirou. Você será redirecionado para o login.', 'warning');
                setTimeout(() => {
                    this.redirectToLogin('Sessão expirada automaticamente');
                }, 3000);
            }
        }, 5 * 60 * 1000);

        // Atualizar timestamp de atividade
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, () => {
                localStorage.setItem('lastActivity', Date.now().toString());
            }, { passive: true });
        });
    }

    // 🚪 CONFIGURAR HANDLER DE LOGOUT
    setupLogoutHandler() {
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    // 🚪 FAZER LOGOUT
    logout() {
        if (confirm('Deseja realmente sair do sistema?')) {
            console.log('🚪 Fazendo logout do usuário:', this.currentUser?.nome);
            this.showNotification('👋 Logout realizado com sucesso!', 'success');
            
            setTimeout(() => {
                this.clearSession();
                window.location.href = 'login.html';
            }, 1000);
        }
    }

    // 📢 MOSTRAR NOTIFICAÇÃO
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;

        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        notification.style.background = colors[type] || colors.info;
        if (type === 'warning') {
            notification.style.color = '#212529';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }

    // 📊 OBTER ESTATÍSTICAS DE USO
    getUsageStats() {
        const stats = JSON.parse(localStorage.getItem('usageStats') || '{}');
        return {
            loginCount: stats.loginCount || 0,
            lastLogin: stats.lastLogin || null,
            modulesUsed: stats.modulesUsed || [],
            totalTime: stats.totalTime || 0
        };
    }

    // 📈 REGISTRAR USO DE MÓDULO
    trackModuleUsage(moduleName) {
        const stats = this.getUsageStats();
        
        if (!stats.modulesUsed.includes(moduleName)) {
            stats.modulesUsed.push(moduleName);
        }
        
        const moduleStats = JSON.parse(localStorage.getItem('moduleStats') || '{}');
        moduleStats[moduleName] = (moduleStats[moduleName] || 0) + 1;
        
        localStorage.setItem('usageStats', JSON.stringify(stats));
        localStorage.setItem('moduleStats', JSON.stringify(moduleStats));
        
        console.log(`📊 Módulo acessado: ${moduleName}`);
    }
}

// 🚀 INICIALIZAR SISTEMA DE AUTENTICAÇÃO
const authManager = new AuthManager();

// 🌐 EXPORTAR PARA USO GLOBAL
window.authManager = authManager;

// 🔧 FUNÇÕES GLOBAIS PARA COMPATIBILIDADE
window.logout = () => authManager.logout();
window.hasPermission = (permission) => authManager.hasPermission(permission);
window.hasMinLevel = (level) => authManager.hasMinLevel(level);
window.trackModuleUsage = (module) => authManager.trackModuleUsage(module);
