// ===== APROVAÇÃO DA QUALIDADE - VERSÃO MELHORADA =====

import { db } from '../firebase-config.js';
import { 
    collection, 
    query, 
    where, 
    getDocs, 
    doc, 
    updateDoc, 
    addDoc, 
    Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// ===== VARIÁVEIS GLOBAIS =====
let itensQualidade = [];
let produtos = [];
let armazens = [];
let currentUser = JSON.parse(localStorage.getItem('currentUser'));

// ===== INICIALIZAÇÃO =====
window.onload = function() {
    if (!currentUser) {
        window.location.href = 'login.html';
        return;
    }
    log('Sistema de aprovação da qualidade carregado', 'info');
};

// ===== FUNÇÃO DE LOG =====
function log(message, type = 'info') {
    const logArea = document.getElementById('logArea');
    if (!logArea) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const className = type;
    logArea.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
    logArea.scrollTop = logArea.scrollHeight;
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// ===== CARREGAR ITENS DA QUALIDADE =====
window.carregarItensQualidade = async function() {
    const container = document.getElementById('itemsContainer');
    container.innerHTML = '<div class="loading"><p>🔄 Carregando itens da qualidade...</p></div>';
    
    log('🔍 Carregando itens da qualidade...', 'info');

    try {
        // Carregar dados necessários
        const [estoqueQualidadeSnap, produtosSnap, armazensSnap] = await Promise.all([
            getDocs(collection(db, "estoqueQualidade")),
            getDocs(collection(db, "produtos")),
            getDocs(collection(db, "armazens"))
        ]);

        itensQualidade = estoqueQualidadeSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        log(`✅ Carregados: ${itensQualidade.length} itens, ${produtos.length} produtos, ${armazens.length} armazéns`, 'success');

        // Enriquecer dados dos itens
        itensQualidade.forEach(item => {
            const produto = produtos.find(p => p.id === item.produtoId);
            if (produto) {
                item.codigo = produto.codigo;
                item.descricao = produto.descricao;
                item.unidade = produto.unidade;
                item.armazemPadraoId = produto.armazemPadraoId;
            }
        });

        renderizarItens();

    } catch (error) {
        log(`❌ Erro ao carregar itens: ${error.message}`, 'error');
        container.innerHTML = `<div style="color: #dc3545; text-align: center; padding: 20px;">❌ Erro: ${error.message}</div>`;
    }
};

// ===== RENDERIZAR ITENS =====
function renderizarItens() {
    const container = document.getElementById('itemsContainer');
    const filtrocodigo = document.getElementById('filtrocodigo')?.value.toLowerCase() || '';
    const filtroStatus = document.getElementById('filtroStatus')?.value || 'all';

    // Aplicar filtros
    let itensFiltrados = itensQualidade.filter(item => {
        const matchcodigo = !filtrocodigo || (item.codigo && item.codigo.toLowerCase().includes(filtrocodigo));
        const matchStatus = filtroStatus === 'all' || item.status === filtroStatus;
        return matchcodigo && matchStatus;
    });

    if (itensFiltrados.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">Nenhum item encontrado com os filtros aplicados.</div>';
        return;
    }

    let html = '<div class="items-grid">';

    itensFiltrados.forEach(item => {
        let cardClass = 'item-card';
        let statusBadgeClass = 'status-pendente';
        let statusText = 'Pendente';
        let statusIcon = '⏳';

        switch (item.status) {
            case 'APROVADO':
                cardClass += ' aprovado';
                statusBadgeClass = 'status-aprovado';
                statusText = 'Aprovado';
                statusIcon = '✅';
                break;
            case 'REJEITADO':
                cardClass += ' rejeitado';
                statusBadgeClass = 'status-rejeitado';
                statusText = 'Rejeitado';
                statusIcon = '❌';
                break;
            default:
                statusText = 'Pendente';
                statusIcon = '⏳';
        }

        const dataEntrada = item.dataEntrada ? new Date(item.dataEntrada.seconds * 1000).toLocaleString() : 'N/A';
        const dataAprovacao = item.dataAprovacao ? new Date(item.dataAprovacao.seconds * 1000).toLocaleString() : '';

        html += `
            <div class="${cardClass}">
                <div class="item-header">
                    <div>
                        <div class="item-code">${statusIcon} ${item.codigo || 'Sem código'}</div>
                        <div class="item-description">${item.descricao || 'Sem descrição'}</div>
                    </div>
                    <span class="status-badge ${statusBadgeClass}">${statusText}</span>
                </div>

                <div class="item-details">
                    <div class="detail-row">
                        <span class="detail-label">Saldo:</span>
                        <span class="detail-value">${item.saldo || 0} ${item.unidade || 'UN'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Data Entrada:</span>
                        <span class="detail-value">${dataEntrada}</span>
                    </div>
                    ${dataAprovacao ? `
                    <div class="detail-row">
                        <span class="detail-label">Data Aprovação:</span>
                        <span class="detail-value">${dataAprovacao}</span>
                    </div>
                    ` : ''}
                    ${item.observacoes ? `
                    <div class="detail-row">
                        <span class="detail-label">Observações:</span>
                        <span class="detail-value">${item.observacoes}</span>
                    </div>
                    ` : ''}
                    ${item.motivoRejeicao ? `
                    <div class="detail-row">
                        <span class="detail-label">Motivo Rejeição:</span>
                        <span class="detail-value" style="color: #dc3545;">${item.motivoRejeicao}</span>
                    </div>
                    ` : ''}
                </div>

                ${item.status === 'PENDENTE' ? `
                <div class="approval-section">
                    <h4>🎯 Ações de Aprovação</h4>
                    
                    <label><strong>Armazém de Destino:</strong></label>
                    <select class="warehouse-select" id="armazem_${item.id}">
                        <option value="">Selecione o armazém...</option>
        `;

        armazens.forEach(arm => {
            const selected = arm.id === item.armazemPadraoId ? 'selected' : '';
            html += `<option value="${arm.id}" ${selected}>${arm.codigo || arm.nome} - ${arm.nome || arm.codigo}</option>`;
        });

        html += `
                    </select>

                    <label><strong>Observações:</strong></label>
                    <textarea class="observation-input" id="obs_${item.id}" placeholder="Observações sobre a aprovação/rejeição..."></textarea>

                    <div class="approval-buttons">
                        <button onclick="aprovarItem('${item.id}')" class="btn-approve">
                            ✅ Aprovar e Transferir
                        </button>
                        <button onclick="rejeitarItem('${item.id}')" class="btn-reject">
                            ❌ Rejeitar
                        </button>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    log(`📋 Exibindo ${itensFiltrados.length} itens`, 'info');
}

// ===== APROVAR ITEM =====
window.aprovarItem = async function(itemId) {
    const armazemSelect = document.getElementById(`armazem_${itemId}`);
    const obsInput = document.getElementById(`obs_${itemId}`);

    const armazemDestino = armazemSelect.value;
    const observacoes = obsInput.value.trim();

    if (!armazemDestino) {
        alert('Selecione o armazém de destino!');
        return;
    }

    if (!confirm('Confirma a aprovação deste item?')) {
        return;
    }

    const item = itensQualidade.find(i => i.id === itemId);
    if (!item) {
        log(`❌ Item ${itemId} não encontrado`, 'error');
        return;
    }

    log(`🔄 Aprovando item ${item.codigo}...`, 'info');

    try {
        const agora = Timestamp.now();

        // 1. Atualizar status no estoqueQualidade
        await updateDoc(doc(db, "estoqueQualidade", itemId), {
            status: 'APROVADO',
            dataAprovacao: agora,
            aprovadoPor: currentUser.nome,
            armazemDestino: armazemDestino,
            observacoes: observacoes
        });

        // 2. Criar/atualizar registro no estoque principal
        const estoqueExistente = await getDocs(query(
            collection(db, "estoques"),
            where("produtoId", "==", item.produtoId),
            where("armazemId", "==", armazemDestino)
        ));

        if (estoqueExistente.docs.length > 0) {
            // Atualizar estoque existente
            const estoqueDoc = estoqueExistente.docs[0];
            const saldoAtual = estoqueDoc.data().saldo || 0;
            const novoSaldo = saldoAtual + item.saldo;

            await updateDoc(doc(db, "estoques", estoqueDoc.id), {
                saldo: novoSaldo,
                ultimaMovimentacao: agora
            });

            log(`📦 Estoque atualizado: ${saldoAtual} + ${item.saldo} = ${novoSaldo}`, 'success');
        } else {
            // Criar novo registro de estoque
            await addDoc(collection(db, "estoques"), {
                produtoId: item.produtoId,
                armazemId: armazemDestino,
                saldo: item.saldo,
                saldoReservado: 0,
                ultimaMovimentacao: agora
            });

            log(`📦 Novo estoque criado: ${item.saldo} ${item.unidade}`, 'success');
        }

        // 3. Registrar movimentação
        await addDoc(collection(db, "movimentacoesEstoque"), {
            produtoId: item.produtoId,
            armazemId: armazemDestino,
            tipo: 'ENTRADA',
            quantidade: item.saldo,
            unidade: item.unidade,
            tipoDocumento: 'APROVACAO_QUALIDADE',
            numeroDocumento: itemId,
            dataHora: agora,
            usuarioId: currentUser.uid,
            usuario: currentUser.nome,
            observacoes: `Aprovado da qualidade - ${observacoes}`,
            valorUnitario: 0,
            valorTotal: 0
        });

        // 4. Zerar saldo na qualidade
        await updateDoc(doc(db, "estoqueQualidade", itemId), {
            saldo: 0
        });

        log(`✅ Item ${item.codigo} aprovado com sucesso!`, 'success');

        // Atualizar item local
        item.status = 'APROVADO';
        item.dataAprovacao = agora;
        item.aprovadoPor = currentUser.nome;
        item.armazemDestino = armazemDestino;
        item.observacoes = observacoes;

        // Re-renderizar
        renderizarItens();

    } catch (error) {
        log(`❌ Erro ao aprovar item: ${error.message}`, 'error');
        console.error('Erro completo:', error);
    }
};

// ===== REJEITAR ITEM =====
window.rejeitarItem = async function(itemId) {
    const obsInput = document.getElementById(`obs_${itemId}`);
    const motivoRejeicao = obsInput.value.trim();

    if (!motivoRejeicao) {
        alert('Informe o motivo da rejeição!');
        return;
    }

    if (!confirm('Confirma a rejeição deste item?')) {
        return;
    }

    const item = itensQualidade.find(i => i.id === itemId);
    if (!item) {
        log(`❌ Item ${itemId} não encontrado`, 'error');
        return;
    }

    log(`🔄 Rejeitando item ${item.codigo}...`, 'info');

    try {
        const agora = Timestamp.now();

        // Atualizar status no estoqueQualidade
        await updateDoc(doc(db, "estoqueQualidade", itemId), {
            status: 'REJEITADO',
            dataRejeicao: agora,
            rejeitadoPor: currentUser.nome,
            motivoRejeicao: motivoRejeicao
        });

        // Registrar movimentação de rejeição
        await addDoc(collection(db, "movimentacoesEstoque"), {
            produtoId: item.produtoId,
            armazemId: 'QUALIDADE',
            tipo: 'SAIDA',
            quantidade: item.saldo,
            unidade: item.unidade,
            tipoDocumento: 'REJEICAO_QUALIDADE',
            numeroDocumento: itemId,
            dataHora: agora,
            usuarioId: currentUser.uid,
            usuario: currentUser.nome,
            observacoes: `Rejeitado na qualidade - ${motivoRejeicao}`,
            valorUnitario: 0,
            valorTotal: 0
        });

        log(`❌ Item ${item.codigo} rejeitado: ${motivoRejeicao}`, 'warning');

        // Atualizar item local
        item.status = 'REJEITADO';
        item.dataRejeicao = agora;
        item.rejeitadoPor = currentUser.nome;
        item.motivoRejeicao = motivoRejeicao;

        // Re-renderizar
        renderizarItens();

    } catch (error) {
        log(`❌ Erro ao rejeitar item: ${error.message}`, 'error');
        console.error('Erro completo:', error);
    }
};

// ===== APROVAR TODOS SELECIONADOS =====
window.aprovarTodosSelecionados = function() {
    const itensPendentes = itensQualidade.filter(item => item.status === 'PENDENTE');

    if (itensPendentes.length === 0) {
        alert('Não há itens pendentes para aprovar');
        return;
    }

    if (!confirm(`Deseja aprovar todos os ${itensPendentes.length} itens pendentes?`)) {
        return;
    }

    log(`🔄 Aprovando ${itensPendentes.length} itens em lote...`, 'info');

    // Aprovar cada item (usando armazém padrão)
    itensPendentes.forEach(async (item, index) => {
        setTimeout(async () => {
            const armazemDestino = item.armazemPadraoId || armazens[0]?.id;
            if (armazemDestino) {
                // Simular seleção do armazém
                const armazemSelect = document.getElementById(`armazem_${item.id}`);
                if (armazemSelect) {
                    armazemSelect.value = armazemDestino;
                }
                await aprovarItem(item.id);
            }
        }, index * 1000); // Delay de 1 segundo entre aprovações
    });
};

// ===== SETUP DE EVENT LISTENERS =====
document.addEventListener('DOMContentLoaded', function() {
    // Filtros em tempo real
    const filtrocodigo = document.getElementById('filtrocodigo');
    const filtroStatus = document.getElementById('filtroStatus');

    if (filtrocodigo) {
        filtrocodigo.addEventListener('input', renderizarItens);
    }

    if (filtroStatus) {
        filtroStatus.addEventListener('change', renderizarItens);
    }
});
