html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>📦 Consulta de Estoques</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
    .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .filters { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px; }
    .form-group { display: flex; flex-direction: column; }
    .form-group label { font-weight: bold; margin-bottom: 5px; }
    .form-control { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; margin: 5px; }
    .btn-primary { background: #3498db; color: white; }
    .btn-success { background: #27ae60; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
    .table th, .table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
    .table th { background: #34495e; color: white; cursor: pointer; }
    .table th:hover { background: #2c3e50; }
    .table tbody tr:hover { background: #f8f9fa; }
    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
    .stat-card { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 8px; text-align: center; }
    .stat-number { font-size: 2em; font-weight: bold; }
    .stat-label { font-size: 0.9em; opacity: 0.9; }
    .alert { padding: 15px; border-radius: 4px; margin: 10px 0; }
    .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .sort-icon { margin-left: 5px; font-size: 0.8em; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📦 Consulta de Estoques</h1>
      <p>Visualização de saldos e movimentações</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats">
      <div class="stat-card">
        <div class="stat-number" id="totalProdutos">-</div>
        <div class="stat-label">Total Produtos</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954);">
        <div class="stat-number" id="produtosComEstoque">-</div>
        <div class="stat-label">Com Estoque</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
        <div class="stat-number" id="produtosSemEstoque">-</div>
        <div class="stat-label">Sem Estoque</div>
      </div>
      <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
        <div class="stat-number" id="valorTotalEstoque">-</div>
        <div class="stat-label">Valor Total</div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="filters">
      <div class="filter-row">
        <div class="form-group">
          <label>Buscar Produto:</label>
          <input type="text" id="searchProduct" class="form-control" placeholder="Código ou descrição..." oninput="filterProducts()">
        </div>
        <div class="form-group">
          <label>Armazém:</label>
          <select id="armazemFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todos os armazéns</option>
          </select>
        </div>
        <div class="form-group">
          <label>Tipo de Produto:</label>
          <select id="tipoFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todos os tipos</option>
            <option value="MP">Matéria Prima</option>
            <option value="SP">Semi Produto</option>
            <option value="PA">Produto Acabado</option>
            <option value="AC">Acessório</option>
          </select>
        </div>
        <div class="form-group">
          <label>Situação:</label>
          <select id="situacaoFilter" class="form-control" onchange="filterProducts()">
            <option value="">Todas</option>
            <option value="COM_ESTOQUE">Com Estoque</option>
            <option value="SEM_ESTOQUE">Sem Estoque</option>
            <option value="ESTOQUE_BAIXO">Estoque Baixo</option>
          </select>
        </div>
      </div>
      <div style="text-align: center;">
        <button class="btn btn-primary" onclick="exportToExcel()">📊 Exportar Excel</button>
        <button class="btn btn-info" onclick="window.location.href='movimentacao_armazem.html'">🔄 Movimentar Estoque</button>

        <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 Voltar</button>
      </div>
    </div>

    <!-- Tabela de Estoques -->
    <div class="alert alert-info">
      <strong>ℹ️ Informação:</strong> Esta tela é apenas para consulta. Para movimentar estoque, use o botão "Movimentar Estoque" acima.
    </div>

    <!-- Alerta de Inconsistências (será preenchido via JavaScript) -->
    <div id="alertaInconsistencias" style="display: none;"></div>

    <table class="table" id="estoqueTable">
      <thead>
        <tr>
          <th onclick="sortTable('codigo')">Código <span class="sort-icon">↕</span></th>
          <th onclick="sortTable('descricao')">Descrição <span class="sort-icon">↕</span></th>
          <th onclick="sortTable('tipo')">Tipo <span class="sort-icon">↕</span></th>
          <th>Unidade</th>
          <th>Armazém</th>
          <th onclick="sortTable('saldo')">Saldo Atual <span class="sort-icon">↕</span></th>
          <th onclick="sortTable('valorUnitario')">Valor Unit. <span class="sort-icon">↕</span></th>
          <th onclick="sortTable('valorTotal')">Valor Total <span class="sort-icon">↕</span></th>
          <th onclick="sortTable('ultimaMovimentacao')">Última Mov. <span class="sort-icon">↕</span></th>
        </tr>
      </thead>
      <tbody id="estoqueTableBody">
        <tr>
          <td colspan="9" style="text-align: center; padding: 40px;">
            Carregando dados...
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, getDoc, query, where, orderBy, limit, updateDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let produtos = [];
    let estoques = [];
    let armazens = [];
    let allData = [];
    let systemConfig = {};
    let currentSort = { field: 'ultimaMovimentacao', direction: 'desc' };

    document.addEventListener('DOMContentLoaded', async () => {
      await loadSystemConfig();
      await loadData();
      updateStats();
      renderTable();
    });

    // Função para carregar configuração do sistema
    async function loadSystemConfig() {
        try {
            const configDoc = await getDoc(doc(db, "parametros", "sistema"));
            if (configDoc.exists()) {
                const data = configDoc.data();
                systemConfig = {
                    armazemPadrao: data.armazemPadrao || '',
                    // Outras configurações podem ser adicionadas aqui
                };
                console.log('🔧 Configuração do sistema carregada:', systemConfig);
            } else {
                systemConfig = { armazemPadrao: '' };
                console.warn('⚠️ Configuração do sistema não encontrada, usando valores padrão');
            }
        } catch (error) {
            console.error('❌ Erro ao carregar configuração do sistema:', error);
            systemConfig = { armazemPadrao: '' };
        }
    }

    async function loadData() {
            try {
                const [produtosSnap, estoquesSnap, armazensSnap, movimentacoesSnap] = await Promise.all([
                    getDocs(collection(db, "produtos")),
                    getDocs(collection(db, "estoques")),
                    getDocs(collection(db, "armazens")),
                    getDocs(query(collection(db, "movimentacoesEstoque"), orderBy("dataHora", "desc"), limit(1000)))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar movimentações para recalcular saldos
                const movimentacoes = movimentacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Combinar dados com validação e normalização
                allData = [];
                console.log('🔍 Processando dados:', { 
                    produtos: produtos.length, 
                    estoques: estoques.length, 
                    armazens: armazens.length,
                    movimentacoes: movimentacoes.length 
                });

                // Recalcular saldos baseado em movimentações
                const saldosCalculados = calcularSaldosReais(movimentacoes, produtos, armazens);
                console.log('📊 Saldos recalculados:', saldosCalculados.size);

                produtos.forEach(produto => {
                    // Buscar estoques do produto e verificar saldos calculados
                    const produtoEstoques = estoques.filter(e => {
                        const estoqueProductId = (e.produtoId || '').toString().toLowerCase();
                        const produtoId = (produto.id || '').toString().toLowerCase();
                        return estoqueProductId === produtoId;
                    });

                    // Verificar se há saldo calculado através das movimentações
                    const saldoCalculado = saldosCalculados.get(`${produto.id}_${armazens[0]?.id || 'ALM01'}`) || null;

                    if (produtoEstoques.length === 0) {
                        // Verificar se existe saldo calculado mesmo sem registro formal de estoque
                        const saldoEncontrado = Array.from(saldosCalculados.entries()).find(([chave, dados]) => 
                            chave.startsWith(produto.id + '_')
                        );

                        if (saldoEncontrado) {
                            const [chave, dadosCalculados] = saldoEncontrado;
                            const armazemId = chave.split('_')[1];
                            const armazem = armazens.find(a => a.id === armazemId);

                            allData.push({
                                ...produto,
                                armazemId: armazemId,
                                armazemNome: armazem ? `${armazem.codigo} - ${armazem.nome}` : '❓ Armazém calculado',
                                saldo: dadosCalculados.saldo,
                                saldoReservado: 0,
                                saldoDisponivel: dadosCalculados.saldo,
                                valorUnitario: dadosCalculados.valorUnitario,
                                valorTotal: dadosCalculados.valorTotal,
                                ultimaMovimentacao: dadosCalculados.ultimaMovimentacao,
                                status: dadosCalculados.saldo > 0 ? 'OK' : 'SEM_ESTOQUE',
                                origemValor: 'Calculado das Movimentações'
                            });
                        } else {
                            // Produto sem estoque registrado nem movimentações
                            // Tentar usar armazém padrão do produto ou do sistema
                            let armazemId = null;
                            let armazemNome = '⚠️ Sem estoque cadastrado';
                            let origemArmazem = 'Sem Estoque';

                            // Tentar armazém padrão do produto
                            if (produto.armazemPadraoId) {
                                const armazemPadrao = armazens.find(a => a.id === produto.armazemPadraoId);
                                if (armazemPadrao) {
                                    armazemId = produto.armazemPadraoId;
                                    armazemNome = `${armazemPadrao.codigo} - ${armazemPadrao.nome} 🎯`;
                                    origemArmazem = 'Armazém Padrão do Produto (Sem Estoque)';
                                }
                            }

                            // Se não tem armazém padrão no produto, tentar do sistema
                            if (!armazemId && systemConfig?.armazemPadrao) {
                                const armazemSistema = armazens.find(a => a.id === systemConfig.armazemPadrao);
                                if (armazemSistema) {
                                    armazemId = systemConfig.armazemPadrao;
                                    armazemNome = `${armazemSistema.codigo} - ${armazemSistema.nome} ⚙️`;
                                    origemArmazem = 'Armazém Padrão do Sistema (Sem Estoque)';
                                }
                            }

                            allData.push({
                                ...produto,
                                armazemId: armazemId,
                                armazemNome: armazemNome,
                                armazemCodigo: armazemId ? armazens.find(a => a.id === armazemId)?.codigo : 'N/A',
                                origemArmazem: origemArmazem,
                                saldo: 0,
                                saldoReservado: 0,
                                saldoDisponivel: 0,
                                valorUnitario: produto.precoVenda || produto.custoMedio || produto.precoCompra || 0,
                                valorTotal: 0,
                                ultimaMovimentacao: null,
                                status: 'SEM_ESTOQUE'
                            });
                        }
                    } else {
                        // Processar cada estoque do produto
                        produtoEstoques.forEach(estoque => {
                            // Buscar armazém correspondente com fallback para armazém padrão
                            let armazem = armazens.find(a => a.id === estoque.armazemId);
                            let armazemIdFinal = estoque.armazemId;
                            let origemArmazem = 'Registro de Estoque';

                            // Se armazém não foi encontrado, tentar armazém padrão do produto
                            if (!armazem && produto.armazemPadraoId) {
                                armazem = armazens.find(a => a.id === produto.armazemPadraoId);
                                if (armazem) {
                                    armazemIdFinal = produto.armazemPadraoId;
                                    origemArmazem = 'Armazém Padrão do Produto';
                                    console.warn(`🔄 Usando armazém padrão do produto ${produto.codigo}: ${armazem.codigo}`);
                                }
                            }

                            // Se ainda não encontrou, tentar armazém padrão do sistema
                            if (!armazem && systemConfig?.armazemPadrao) {
                                armazem = armazens.find(a => a.id === systemConfig.armazemPadrao);
                                if (armazem) {
                                    armazemIdFinal = systemConfig.armazemPadrao;
                                    origemArmazem = 'Armazém Padrão do Sistema';
                                    console.warn(`🔄 Usando armazém padrão do sistema para ${produto.codigo}: ${armazem.codigo}`);
                                }
                            }

                            // Se ainda não encontrou, usar o primeiro armazém ativo disponível
                            if (!armazem && armazens.length > 0) {
                                armazem = armazens.find(a => a.ativo !== false) || armazens[0];
                                if (armazem) {
                                    armazemIdFinal = armazem.id;
                                    origemArmazem = 'Primeiro Armazém Disponível';
                                    console.warn(`🔄 Usando primeiro armazém disponível para ${produto.codigo}: ${armazem.codigo}`);
                                }
                            }

                            // Verificar se há saldo calculado para este produto/armazém
                            const chaveCalculo = `${produto.id}_${armazemIdFinal}`;
                            const saldoCalculado = saldosCalculados.get(chaveCalculo);

                            // Priorizar saldo calculado se divergir significativamente do registrado
                            let saldoAtual = 0;
                            let origemSaldo = 'Registro Estoque';

                            if (estoque.saldo !== undefined) {
                                saldoAtual = parseFloat(estoque.saldo) || 0;
                            } else if (estoque.quantidade !== undefined) {
                                saldoAtual = parseFloat(estoque.quantidade) || 0;
                            }

                            // Se há saldo calculado e difere do registrado, usar o calculado
                            if (saldoCalculado && Math.abs(saldoCalculado.saldo - saldoAtual) > 0.001) {
                                console.warn(`📊 Divergência detectada - Produto ${produto.codigo}: 
                                    Registrado: ${saldoAtual} | Calculado: ${saldoCalculado.saldo}`);
                                saldoAtual = saldoCalculado.saldo;
                                origemSaldo = 'Calculado das Movimentações';
                            }

                            const saldoReservado = parseFloat(estoque.saldoReservado) || 0;
                            const saldoEmpenhado = parseFloat(estoque.saldoEmpenhado) || 0;
                            const saldoDisponivel = saldoAtual - saldoReservado - saldoEmpenhado;

                            // Calcular valor unitário com priorização melhorada e debugging
                            let valorUnitario = 0;
                            let origemValor = 'N/A';

                            // 1ª Prioridade: Valor calculado das movimentações (se disponível)
                            if (saldoCalculado && saldoCalculado.valorUnitario > 0) {
                                valorUnitario = saldoCalculado.valorUnitario;
                                origemValor = 'Custo Médio Calculado';
                            }
                            // 2ª Prioridade: Custo médio do estoque (mais atual)
                            else if (estoque.custoMedio && parseFloat(estoque.custoMedio) > 0) {
                                valorUnitario = parseFloat(estoque.custoMedio);
                                origemValor = 'Custo Médio Estoque';
                            }
                            // 3ª Prioridade: Valor unitário do estoque
                            else if (estoque.valorUnitario && parseFloat(estoque.valorUnitario) > 0) {
                                valorUnitario = parseFloat(estoque.valorUnitario);
                                origemValor = 'Valor Unitário Estoque';
                            }
                            // 4ª Prioridade: Calcular do valor total do estoque
                            else if (estoque.valorTotal && parseFloat(estoque.valorTotal) > 0 && saldoAtual > 0) {
                                valorUnitario = parseFloat(estoque.valorTotal) / saldoAtual;
                                origemValor = 'Calculado do Total';
                            }
                            // 5ª Prioridade: Preço de compra do produto
                            else if (produto.precoCompra && parseFloat(produto.precoCompra) > 0) {
                                valorUnitario = parseFloat(produto.precoCompra);
                                origemValor = 'Preço Compra Produto';
                            }
                            // 6ª Prioridade: Custo médio do produto
                            else if (produto.custoMedio && parseFloat(produto.custoMedio) > 0) {
                                valorUnitario = parseFloat(produto.custoMedio);
                                origemValor = 'Custo Médio Produto';
                            }
                            // 7ª Prioridade: Preço de venda do produto (70% do preço para estoque)
                            else if (produto.precoVenda && parseFloat(produto.precoVenda) > 0) {
                                valorUnitario = parseFloat(produto.precoVenda) * 0.7; // Estimativa de custo
                                origemValor = 'Estimado do Preço Venda';
                            }
                            // 8ª Prioridade: Valor unitário do produto
                            else if (produto.valorUnitario && parseFloat(produto.valorUnitario) > 0) {
                                valorUnitario = parseFloat(produto.valorUnitario);
                                origemValor = 'Valor Unitário Produto';
                            }
                            // 9ª Prioridade: Campos alternativos no produto
                            else if (produto.preco && parseFloat(produto.preco) > 0) {
                                valorUnitario = parseFloat(produto.preco);
                                origemValor = 'Campo Preco Produto';
                            }
                            else if (produto.valor && parseFloat(produto.valor) > 0) {
                                valorUnitario = parseFloat(produto.valor);
                                origemValor = 'Campo Valor Produto';
                            }

                            // Log para debugging valores não encontrados
                            if (valorUnitario === 0) {
                                console.log(`⚠️ Produto sem valor definido: ${produto.codigo} - ${produto.descricao}`, {
                                    produto: {
                                        precoCompra: produto.precoCompra,
                                        precoVenda: produto.precoVenda,
                                        custoMedio: produto.custoMedio,
                                        valorUnitario: produto.valorUnitario,
                                        preco: produto.preco,
                                        valor: produto.valor
                                    },
                                    estoque: {
                                        custoMedio: estoque.custoMedio,
                                        valorUnitario: estoque.valorUnitario,
                                        valorTotal: estoque.valorTotal
                                    }
                                });
                            }

                            // Determinar status do estoque
                            let status = 'OK';
                            if (saldoAtual <= 0) {
                                status = 'SEM_ESTOQUE';
                            } else if (saldoDisponivel <= 0) {
                                status = 'TOTALMENTE_EMPENHADO';
                            } else if (saldoAtual < (produto.estoqueMinimo || 10)) {
                                status = 'ESTOQUE_BAIXO';
                            }

                            // Definir nome do armazém com indicação da origem se necessário
                            let armazemNome = '❌ Armazém não encontrado';
                            if (armazem) {
                                armazemNome = `${armazem.codigo} - ${armazem.nome}`;
                                // Adicionar indicador se não for o armazém original do estoque
                                if (origemArmazem !== 'Registro de Estoque') {
                                    armazemNome += ` 🔄`;
                                }
                            }

                            allData.push({
                                ...produto,
                                // Dados do estoque
                                estoqueId: estoque.id,
                                armazemId: armazemIdFinal,
                                armazemIdOriginal: estoque.armazemId, // Para auditoria
                                armazemNome: armazemNome,
                                armazemCodigo: armazem?.codigo || 'N/A',
                                origemArmazem: origemArmazem, // Para auditoria

                                // Saldos normalizados
                                saldo: saldoAtual,
                                saldoReservado: saldoReservado,
                                saldoEmpenhado: saldoEmpenhado,
                                saldoDisponivel: saldoDisponivel,

                                // Valores financeiros
                                valorUnitario: valorUnitario,
                                valorTotal: parseFloat((saldoAtual * valorUnitario).toFixed(2)),
                                custoMedio: estoque.custoMedio || valorUnitario,
                                valorEstoque: parseFloat((saldoAtual * valorUnitario).toFixed(2)), // Alias para compatibilidade
                                origemValor: origemValor, // Para auditoria

                                // Metadados
                                ultimaMovimentacao: estoque.ultimaMovimentacao,
                                ultimaAtualizacao: estoque.ultimaAtualizacao,
                                versao: estoque.versao || 1,
                                status: status,

                                // Controles de qualidade
                                temControleQualidade: produto.controleQualidade || false,
                                loteControle: estoque.loteControle || null,
                                dataValidade: estoque.dataValidade || null,
                                origemSaldo: origemSaldo
                            });
                        });
                    }
                });

                const estatisticas = {
                    totalItens: allData.length, 
                    comEstoque: allData.filter(i => i.saldo > 0).length,
                    comValor: allData.filter(i => i.valorUnitario > 0).length,
                    valorTotalCalculado: allData.reduce((sum, i) => sum + (i.valorTotal || 0), 0),
                    semValor: allData.filter(i => i.valorUnitario === 0).length,
                    valoresZerados: allData.filter(i => i.saldo > 0 && i.valorUnitario === 0).length
                };

                console.log('✅ Dados processados:', estatisticas);

                if (estatisticas.semValor > 0) {
                    console.warn(`⚠️ ${estatisticas.semValor} produtos sem valor unitário definido`);
                    console.warn(`⚠️ ${estatisticas.valoresZerados} produtos com estoque mas sem valor`);
                }

                populateFilters();
                verificarInconsistenciasArmazens();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                document.getElementById('estoqueTableBody').innerHTML = 
                    '<tr><td colspan="9" style="text-align: center; color: red;">Erro ao carregar dados</td></tr>';
            }
        }

        // Função para calcular saldos reais baseado em movimentações
        function calcularSaldosReais(movimentacoes, produtos, armazens) {
            const saldos = new Map();

            console.log('🔄 Recalculando saldos a partir de movimentações...');

            movimentacoes.forEach(mov => {
                if (!mov.produtoId || !mov.armazemId) return;

                const chave = `${mov.produtoId}_${mov.armazemId}`;
                const quantidade = parseFloat(mov.quantidade || 0);
                const tipo = mov.tipo || '';

                if (!saldos.has(chave)) {
                    saldos.set(chave, {
                        saldo: 0,
                        valorUnitario: 0,
                        valorTotal: 0,
                        ultimaMovimentacao: null,
                        movimentacoes: 0
                    });
                }

                const item = saldos.get(chave);

                // Aplicar movimentação
                if (tipo === 'ENTRADA') {
                    item.saldo += quantidade;
                    // Atualizar custo médio ponderado
                    const valorUnitarioMov = parseFloat(mov.valorUnitario || 0);
                    if (valorUnitarioMov > 0 && quantidade > 0) {
                        const valorTotalAnterior = item.saldo * item.valorUnitario;
                        const valorTotalMovimentacao = quantidade * valorUnitarioMov;
                        const novoSaldoTotal = item.saldo + quantidade;

                        if (novoSaldoTotal > 0) {
                            item.valorUnitario = (valorTotalAnterior + valorTotalMovimentacao) / novoSaldoTotal;
                        }
                    }
                } else if (tipo === 'SAIDA') {
                    item.saldo -= quantidade;
                }

                item.valorTotal = item.saldo * item.valorUnitario;
                item.ultimaMovimentacao = mov.dataHora;
                item.movimentacoes++;

                saldos.set(chave, item);
            });

            console.log(`✅ Saldos calculados para ${saldos.size} itens`);
            return saldos;
        }

        // Função para verificar inconsistências de armazéns
        function verificarInconsistenciasArmazens() {
            let inconsistencias = [];

            // Verificar armazéns não encontrados
            const armazensNaoEncontrados = allData.filter(item => 
                item.armazemNome.includes('❌ Armazém não encontrado') ||
                item.armazemNome.includes('🔄')
            );

            if (armazensNaoEncontrados.length > 0) {
                inconsistencias.push({
                    tipo: 'ARMAZEM_NAO_ENCONTRADO',
                    count: armazensNaoEncontrados.length,
                    descricao: `${armazensNaoEncontrados.length} produtos com armazéns não encontrados ou usando fallback`
                });
            }

            // Verificar produtos sem estoque cadastrado mas com movimentações
            const semEstoqueCadastrado = allData.filter(item => 
                item.armazemNome.includes('⚠️ Sem estoque cadastrado')
            );

            if (semEstoqueCadastrado.length > 0) {
                inconsistencias.push({
                    tipo: 'SEM_ESTOQUE_CADASTRADO',
                    count: semEstoqueCadastrado.length,
                    descricao: `${semEstoqueCadastrado.length} produtos sem estoque cadastrado`
                });
            }

            // Verificar saldos negativos
            const saldosNegativos = allData.filter(item => item.saldo < 0);

            if (saldosNegativos.length > 0) {
                inconsistencias.push({
                    tipo: 'SALDO_NEGATIVO',
                    count: saldosNegativos.length,
                    descricao: `${saldosNegativos.length} produtos com saldos negativos`
                });
            }

            exibirAlertaInconsistencias(inconsistencias);
        }

        function exibirAlertaInconsistencias(inconsistencias) {
            const alertContainer = document.getElementById('alertaInconsistencias');

            if (inconsistencias.length === 0) {
                alertContainer.style.display = 'none';
                return;
            }

            const totalProblemas = inconsistencias.reduce((sum, inc) => sum + inc.count, 0);

            let alertHtml = `
                <div class="alert" style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 1.2em;"></i>
                    <div style="flex: 1;">
                        <strong>⚠️ ${totalProblemas} Inconsistências Detectadas no Sistema de Armazéns</strong>
                        <ul style="margin: 10px 0 0 20px; padding: 0;">
            `;

            inconsistencias.forEach(inc => {
                alertHtml += `<li>${inc.descricao}</li>`;
            });

            alertHtml += `
                        </ul>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-warning" onclick="window.open('padronizar_armazens.html', '_blank')" style="padding: 8px 16px; background: #f39c12; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🔧 Corrigir Agora
                            </button>
                            <button onclick="document.getElementById('alertaInconsistencias').style.display='none'" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                                ❌ Ocultar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            alertContainer.innerHTML = alertHtml;
            alertContainer.style.display = 'block';
        }

    function populateFilters() {
      const armazemFilter = document.getElementById('armazemFilter');
      armazemFilter.innerHTML = '<option value="">Todos os armazéns</option>';

      armazens.forEach(armazem => {
        armazemFilter.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
      });
    }

    function updateStats() {
      const totalProdutos = produtos.length;
      const itensComEstoque = allData.filter(item => item.saldo > 0);
      const produtosComEstoque = new Set(itensComEstoque.map(item => item.id)).size;
      const produtosSemEstoque = totalProdutos - produtosComEstoque;
      const valorTotal = allData.reduce((sum, item) => sum + (item.valorTotal || 0), 0);

      // Estatísticas adicionais
      const itensComProblemas = allData.filter(item => 
        item.status === 'ESTOQUE_BAIXO' || 
        item.status === 'TOTALMENTE_EMPENHADO'
      ).length;

      document.getElementById('totalProdutos').textContent = totalProdutos;
      document.getElementById('produtosComEstoque').textContent = produtosComEstoque;
      document.getElementById('produtosSemEstoque').textContent = produtosSemEstoque;
      document.getElementById('valorTotalEstoque').textContent = 
        'R$ ' + valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 });

      // Adicionar alerta se houver problemas
      if (itensComProblemas > 0) {
        console.warn(`⚠️ ${itensComProblemas} itens com problemas de estoque detectados`);
      }

      console.log('📊 Estatísticas atualizadas:', {
        totalProdutos,
        produtosComEstoque,
        produtosSemEstoque,
        valorTotal: valorTotal.toFixed(2),
        itensComProblemas
      });
    }

    function sortData(data, field, direction) {
      return [...data].sort((a, b) => {
        let valueA = a[field];
        let valueB = b[field];

        // Tratamento especial para data
        if (field === 'ultimaMovimentacao') {
          valueA = valueA ? 
            (valueA.toDate ? valueA.toDate() : new Date(valueA.seconds * 1000)) : 
            new Date(0);
          valueB = valueB ? 
            (valueB.toDate ? valueB.toDate() : new Date(valueB.seconds * 1000)) : 
            new Date(0);
        }

        // Tratamento para números
        if (typeof valueA === 'number' && typeof valueB === 'number') {
          return direction === 'asc' ? valueA - valueB : valueB - valueA;
        }

        // Tratamento para strings
        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return direction === 'asc' ? 
            valueA.localeCompare(valueB) : 
            valueB.localeCompare(valueA);
        }

        // Tratamento para datas
        if (valueA instanceof Date && valueB instanceof Date) {
          return direction === 'asc' ? valueA - valueB : valueB - valueA;
        }

        return 0;
      });
    }

    function renderTable(data = allData) {
      const tbody = document.getElementById('estoqueTableBody');
      tbody.innerHTML = '';

      if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">Nenhum produto encontrado</td></tr>';
        return;
      }

      // Aplicar ordenação atual
      const sortedData = sortData(data, currentSort.field, currentSort.direction);

      sortedData.forEach(item => {
        const row = document.createElement('tr');

        if (item.saldo === 0) {
          row.style.backgroundColor = '#ffebee';
        } else if (item.saldo < 10) {
          row.style.backgroundColor = '#fff3e0';
        }

        const ultimaMovFormatada = item.ultimaMovimentacao ? 
          (item.ultimaMovimentacao.toDate ? 
            item.ultimaMovimentacao.toDate().toLocaleDateString('pt-BR') : 
            new Date(item.ultimaMovimentacao.seconds * 1000).toLocaleDateString('pt-BR')
          ) : '-';

        // Cores baseadas no status
        let corSaldo = '#388e3c'; // Verde padrão
        let iconStatus = '✅';

        if (item.status === 'SEM_ESTOQUE') {
          corSaldo = '#d32f2f';
          iconStatus = '❌';
        } else if (item.status === 'ESTOQUE_BAIXO') {
          corSaldo = '#f57c00';
          iconStatus = '⚠️';
        } else if (item.status === 'TOTALMENTE_EMPENHADO') {
          corSaldo = '#9c27b0';
          iconStatus = '🔒';
        }

        // Título com detalhes do saldo para hover
        const tituloSaldo = `Saldo Total: ${item.saldo.toFixed(3)}
Reservado: ${(item.saldoReservado || 0).toFixed(3)}
Empenhado: ${(item.saldoEmpenhado || 0).toFixed(3)}
Disponível: ${(item.saldoDisponivel || 0).toFixed(3)}`;

        const tituloValor = `Valor Unitário: R$ ${(item.valorUnitario || 0).toFixed(2)}
Origem: ${item.origemValor || 'N/A'}
Valor Total: R$ ${(item.valorTotal || 0).toFixed(2)}`;

        const tituloArmazem = item.origemArmazem && item.origemArmazem !== 'Registro de Estoque' ? 
          `Armazém: ${item.armazemNome}
Origem: ${item.origemArmazem}
${item.armazemIdOriginal ? `Armazém Original: ${item.armazemIdOriginal}` : ''}` : 
          `Armazém: ${item.armazemNome}`;

        row.innerHTML = `
          <td><strong>${item.codigo || 'N/A'}</strong></td>
          <td>${item.descricao || 'N/A'}</td>
          <td><span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">${item.tipo || 'N/A'}</span></td>
          <td>${item.unidade || 'UN'}</td>
          <td title="${tituloArmazem}">${item.armazemNome}</td>
          <td style="text-align: right; font-weight: bold; color: ${corSaldo};" title="${tituloSaldo}">
            ${item.saldo.toFixed(3)}
            ${(item.saldoReservado > 0 || item.saldoEmpenhado > 0) ? 
              `<br><small style="font-weight: normal;">Disp: ${(item.saldoDisponivel || 0).toFixed(3)}</small>` : ''}
          </td>
          <td style="text-align: right;" title="${tituloValor}">
            R$ ${(item.valorUnitario || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            ${item.valorUnitario === 0 ? ' <small style="color: #e74c3c;">⚠️</small>' : ''}
          </td>
          <td style="text-align: right; font-weight: bold;" title="${tituloValor}">
            R$ ${(item.valorTotal || 0).toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
          </td>
          <td>${ultimaMovFormatada}</td>
        `;

        tbody.appendChild(row);
      });

      updateSortIcons();
    }

    function updateSortIcons() {
      // Reset all icons
      document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.textContent = '↕';
      });

      // Update current sort icon
      const currentHeader = document.querySelector(`th[onclick="sortTable('${currentSort.field}')"] .sort-icon`);
      if (currentHeader) {
        currentHeader.textContent = currentSort.direction === 'asc' ? '↑' : '↓';
      }
    }

    window.sortTable = function(field) {
      if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
      } else {
        currentSort.field = field;
        currentSort.direction = field === 'ultimaMovimentacao' ? 'desc' : 'asc';
      }

      renderTable();
    };

    window.filterProducts = function() {
      const searchTerm = document.getElementById('searchProduct').value.toLowerCase();
      const armazemFilter = document.getElementById('armazemFilter').value;
      const tipoFilter = document.getElementById('tipoFilter').value;
      const situacaoFilter = document.getElementById('situacaoFilter').value;

      let filteredData = allData.filter(item => {
        const matchesSearch = !searchTerm || 
          (item.codigo && item.codigo.toLowerCase().includes(searchTerm)) ||
          (item.descricao && item.descricao.toLowerCase().includes(searchTerm));

        const matchesArmazem = !armazemFilter || item.armazemId === armazemFilter;
        const matchesTipo = !tipoFilter || item.tipo === tipoFilter;

        let matchesSituacao = true;
        if (situacaoFilter === 'COM_ESTOQUE') {
          matchesSituacao = item.saldo > 0;
        } else if (situacaoFilter === 'SEM_ESTOQUE') {
          matchesSituacao = item.saldo === 0;
        } else if (situacaoFilter === 'ESTOQUE_BAIXO') {
          matchesSituacao = item.saldo > 0 && item.saldo < 10;
        }

        return matchesSearch && matchesArmazem && matchesTipo && matchesSituacao;
      });

      renderTable(filteredData);
    };

    window.exportToExcel = function() {
      const data = allData.map(item => ({
        'Código': item.codigo || '',
        'Descrição': item.descricao || '',
        'Tipo': item.tipo || '',
        'Unidade': item.unidade || '',
        'Armazém': item.armazemNome || '',
        'Saldo': item.saldo,
        'Valor Unitário': (item.valorUnitario || 0).toFixed(2),
        'Valor Total': (item.valorTotal || 0).toFixed(2),
        'Última Movimentação': item.ultimaMovimentacao ? 
          (item.ultimaMovimentacao.toDate ? 
            itemultimaMovimentacao.toDate().toLocaleDateString('pt-BR') : 
            new Date(item.ultimaMovimentacao.seconds * 1000).toLocaleDateString('pt-BR')
          ) : ''
      }));

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Estoques");
      XLSX.writeFile(wb, `estoques_${new Date().toISOString().split('T')[0]}.xlsx`);
    };
  </script>
</body>
</html>