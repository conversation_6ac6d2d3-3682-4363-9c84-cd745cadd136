firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T10:39:14.521Z', apps: 1}
recebimento_materiais_avancado.html:1051 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1127 Dados carregados: {pedidos: 39, produtos: 1668, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1137 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', itens: Array(2), solicitacaoId: 'ADDObHfp1fw0SC4Rz3sS', condicaoPagamento: '60DIAS', aprovadoPor: 'Alex', …}
recebimento_materiais_avancado.html:1139 📦 Exemplo de item: {unidade: 'PC', quantidade: 1, produtoId: '1sHECTgPNMjBw0ScLbbq', codigo: '110732', valorTotal: 1805.17, …}
recebimento_materiais_avancado.html:1143 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', status: 'ativo', dataCadastro: {…}, unidadeSecundaria: null, metodoCusteio: 'padrao', …}
recebimento_materiais_avancado.html:1146 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', suframa: '', cargo3: '', statusHomologacao: 'Pendente', endereco: 'AV GOV ADHEMAR P DE BARROS, 1700', …}
 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
 📋 Pedidos de Compra: 39
 🏷️ Produtos: 1668
 🏢 Fornecedores: 778
 🏪 Armazéns: 9
 📋 Estrutura do pedido: (22) ['id', 'itens', 'solicitacaoId', 'condicaoPagamento', 'aprovadoPor', 'alteradoPor', 'dataAprovacao', 'numeroAnterior', 'ultimaLimpezaCritica', 'historico', 'numero', 'atualizacoesEntrega', 'sincronizadoPor', 'dataCriacao', 'status', 'limpoPor', 'fornecedorId', 'valorTotal', 'criadoPor', 'prazoEntrega', 'cotacaoId', 'ultimaAtualizacao']
 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', itens: Array(2), solicitacaoId: 'ADDObHfp1fw0SC4Rz3sS', condicaoPagamento: '60DIAS', aprovadoPor: 'Alex', …}
 📦 Estrutura do item: (7) ['unidade', 'quantidade', 'produtoId', 'codigo', 'valorTotal', 'valorUnitario', 'descricao']
 📦 Item exemplo: {unidade: 'PC', quantidade: 1, produtoId: '1sHECTgPNMjBw0ScLbbq', codigo: '110732', valorTotal: 1805.17, …}
 🏢 Estrutura do fornecedor: (71) ['id', 'suframa', 'cargo3', 'statusHomologacao', 'endereco', 'tipo', 'longitudeCLI', 'telefone2', 'nascimento', 'cotacao', 'ativo', 'cep', 'telefone3', 'cnpjCpf2', 'estado', 'bairro', 'email1', 'telefone1', 'latitudeCLI', 'autorizaXml1', 'contato1', 'codigoVendedor', 'departamento1', 'tipoPessoa', 'autorizaXml2', 'inscricaoEstadual', 'razaoSocial', 'celular3', 'reducao', 'celular2', 'cnpjCpf', 'homePage', 'contato3', 'nomeFantasia', 'observacoes', 'email2', 'celular1', 'contato2', 'dataAtualizacao', 'codCusto', 'email3', 'numero', 'complemento', 'contaContabil', 'pais', 'departamento3', 'codigoRegiao', 'inscricaoMunicipal', 'im', 'temSubstituicao', 'telefone4', 'indicacao', 'intervista', 'cidade', 'limite', 'simplesNacional', 'fax', 'codigoClassificacao', 'ultimaCompra', 'cnpjCpf3', 'cargo2', 'departamento2', 'categorias', 'codigoArea', 'categoriaPrincipal', 'codigoPais', 'acrescimoCLI', 'emailNfe', 'dataCadastro', 'email', 'codigo']
 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', suframa: '', cargo3: '', statusHomologacao: 'Pendente', endereco: 'AV GOV ADHEMAR P DE BARROS, 1700', …}
 🏷️ Estrutura do produto: (31) ['id', 'status', 'dataCadastro', 'unidadeSecundaria', 'metodoCusteio', 'precoVenda', 'cest', 'familia', 'tipo', 'margemLucro', 'ncm', 'unidade', 'inspecaoRecebimento', 'ultimoCusto', 'prateleira', 'grupo', 'rastreabilidadeLote', 'descricao', 'centroCustoObrigatorio', 'fatorConversao', 'posicao', 'armazemPadraoId', 'origem', 'tipoItem', 'pontoPedido', 'estoqueMinimo', 'corredor', 'loteCompra', 'custoMedio', 'codigo', 'estoqueMaximo']
 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', status: 'ativo', dataCadastro: {…}, unidadeSecundaria: null, metodoCusteio: 'padrao', …}
 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
 📋 DEBUG populateOrderSelect - Total de pedidos: 39
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'APROVADO', temItens: 2}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'APROVADO', temItens: 5}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1350 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1355 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1327 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1335 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1368 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1392 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1395 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1298 📊 Dashboard atualizado: {pendentes: 15, atrasados: 0, parciais: 0, completos: 22}
recebimento_materiais_avancado.html:1570 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1510 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1519 🔍 DEBUG selectOrderById - currentOrder encontrado: {id: 'STenDi3OPZy4ieeJIrxk', cotacaoNumero: '000063', cotacaoId: '4NSB4YyxR5qnj6TphjVl', condicoesPagamento: '30DIAS', observacoes: 'Pedido gerado automaticamente da análise da cotação 000063', …}
recebimento_materiais_avancado.html:1527 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1587 🔍 DEBUG loadSupplierInfo - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', cotacaoNumero: '000063', cotacaoId: '4NSB4YyxR5qnj6TphjVl', condicoesPagamento: '30DIAS', observacoes: 'Pedido gerado automaticamente da análise da cotação 000063', …}
recebimento_materiais_avancado.html:1588 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1589 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1592 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1593 📋 Campos do currentOrder: (22) ['id', 'cotacaoNumero', 'cotacaoId', 'condicoesPagamento', 'observacoes', 'fornecedorId', 'fornecedorNome', 'dataUltimaAtualizacao', 'status', 'uidAprovacao', 'aprovadoPor', 'valorTotal', 'criadoPor', 'ultimoRecebimento', 'recebidoPor', 'dataRecebimento', 'dataCriacao', 'numero', 'itens', 'historico', 'prazoEntrega', 'dataAprovacao']
recebimento_materiais_avancado.html:1594 📄 Dados do fornecedor no pedido: {fornecedorId: 'YqfHIA1xCUe30ndFcIAj', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', fornecedorCnpj: undefined, fornecedorDocumento: undefined, fornecedorContato: undefined, …}
recebimento_materiais_avancado.html:1626 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: {id: 'YqfHIA1xCUe30ndFcIAj', dataCadastro: Timestamp, simplesNacional: true, cnpjCpf3: '0', celular3: '0', …}
recebimento_materiais_avancado.html:1630 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1631 📋 Campos disponíveis: (70) ['id', 'dataCadastro', 'simplesNacional', 'cnpjCpf3', 'celular3', 'reducao', 'email', 'telefone2', 'statusHomologacao', 'nomeFantasia', 'razaoSocial', 'categorias', 'bairro', 'departamento3', 'departamento2', 'codigoPais', 'autorizaXml2', 'telefone1', 'telefone4', 'acrescimoCLI', 'codCusto', 'ultimaCompra', 'codigoVendedor', 'tipo', 'fax', 'contato3', 'codigoRegiao', 'latitudeCLI', 'endereco', 'cargo3', 'ativo', 'limite', 'numero', 'email2', 'pais', 'im', 'longitudeCLI', 'tipoPessoa', 'email3', 'departamento1', 'autorizaXml1', 'cnpjCpf', 'contaContabil', 'intervista', 'codigoClassificacao', 'observacoes', 'celular2', 'inscricaoMunicipal', 'cargo2', 'homePage', 'nascimento', 'celular1', 'dataAtualizacao', 'contato1', 'suframa', 'estado', 'temSubstituicao', 'complemento', 'inscricaoEstadual', 'cidade', 'indicacao', 'contato2', 'emailNfe', 'cep', 'codigoArea', 'cotacao', 'telefone3', 'email1', 'cnpjCpf2', 'codigo']
recebimento_materiais_avancado.html:1632 📄 CNPJ/CPF campos: {cnpj: undefined, cpfCnpj: undefined, cnpjCpf: '07.686.277/0001-69', documento: undefined, cpf: undefined}
recebimento_materiais_avancado.html:1653 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:949 🔍 DEBUG extrairCnpjCpf - Tentativas: (17) [undefined, undefined, '07.686.277/0001-69', undefined, undefined, '07.686.277/0001-69', undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined]
recebimento_materiais_avancado.html:950 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1691 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1692 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1693 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1710 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1711 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1712 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1715 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: {fornecedor?.cnpj: undefined, fornecedor?.cpfCnpj: undefined, fornecedor?.cnpjCpf: '07.686.277/0001-69', fornecedor?.documento: undefined, fornecedor?.cpf: undefined, …}
recebimento_materiais_avancado.html:1540 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1849 🔍 DEBUG loadOrderItems - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', cotacaoNumero: '000063', cotacaoId: '4NSB4YyxR5qnj6TphjVl', condicoesPagamento: '30DIAS', observacoes: 'Pedido gerado automaticamente da análise da cotação 000063', …}
recebimento_materiais_avancado.html:1850 🔍 DEBUG loadOrderItems - itens: (2) [{…}, {…}]
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 0: {codigo: '105239', icms: 0, ipi: 0, unidade: 'PC', precoUnitario: 397, …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: {id: '94M0qmNlWSZt9opark0E', familia: null, estoqueMaximo: 0, tipo: 'MP', codigo: '105239', …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1863 🔍 DEBUG loadOrderItems - Item 1: {icms: 0, precoUnitario: 397, unidade: 'PC', ipi: 0, valorTotal: 397, …}
recebimento_materiais_avancado.html:1866 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: {id: 'UPqNBsHPmH3CAwc38Hrp', tipo: 'MP', unidade: 'PC', dataCadastro: {…}, codigo: '105240', …}
recebimento_materiais_avancado.html:1873 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1885 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2166 Erro ao carregar histórico: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2166
await in loadDeliveryHistory
selectOrderById @ recebimento_materiais_avancado.html:1556
handleMouseUp_ @ unknown
await in handleMouseUp_
window.selectOrder @ recebimento_materiais_avancado.html:1583
onchange @ recebimento_materiais_avancado.html:599
handleMouseUp_ @ unknown
