<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitação de Compras - Totvs Style</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --success-color: #107e3e;
            --success-hover: #0d6e36;
            --secondary-color: #6c757d;
            --secondary-hover: #5a6268;
            --border-color: #d4d4d4;
            --text-color: #333;
            --text-secondary: #666;
            --header-bg: #354a5f;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            color: var(--text-color);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 30px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: var(--header-bg);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
        }

        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .form-col {
            flex: 1;
            min-width: 200px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
        }

        select, input, button {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            color: var(--text-color);
            background-color: #fff;
            transition: border-color 0.2s, background-color 0.2s;
        }

        select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(8, 84, 160, 0.1);
        }

        button {
            cursor: pointer;
            font-weight: 500;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: #fff;
            border: none;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--success-color);
            color: #fff;
            border: none;
        }

        .btn-success:hover {
            background-color: var(--success-hover);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: #fff;
            border: none;
        }

        .btn-secondary:hover {
            background-color: var(--secondary-hover);
        }

        .btn-back {
            background-color: var(--secondary-color);
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-back:hover {
            background-color: var(--secondary-hover);
        }

        .btn-back i {
            font-size: 16px;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
            background-color: #fff;
            table-layout: fixed;
        }

        .data-table th {
            background-color: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .data-table td {
            padding: 10px 8px;
            border: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .form-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .modal-content {
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: 500;
            font-size: 12px;
        }

        .badge-pendente { background-color: #ffc107; color: #000; }
        .badge-aprovado { background-color: #107e3e; color: #fff; }
        .badge-reprovado { background-color: #dc3545; color: #fff; }
        .badge-em-analise { background-color: #17a2b8; color: #fff; }
        .badge-cancelado { background-color: #6c757d; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Solicitação de Compras</h1>
            <div class="header-buttons">
                <button class="btn btn-primary" onclick="openNewRequestModal()">
                    <i class="fas fa-plus"></i> Nova Solicitação
                </button>
                <button class="btn-back" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Voltar
                </button>
            </div>
        </div>

        <div class="filters">
            <div class="form-col">
                <label>Filtrar por Status</label>
                <select onchange="filterByStatus(this.value)">
                    <option value="">Todos</option>
                    <option value="pendente">Pendente</option>
                    <option value="aprovado">Aprovado</option>
                    <option value="reprovado">Reprovado</option>
                    <option value="em-analise">Em Análise</option>
                    <option value="cancelado">Cancelado</option>
                </select>
            </div>
            <div class="form-col">
                <label>Filtrar por Solicitante</label>
                <input type="text" onkeyup="filterBySolicitante(this.value)" placeholder="Nome do solicitante">
            </div>
            <div class="form-col">
                <label>Filtrar por Data</label>
                <input type="date" onchange="filterByDate(this.value)">
            </div>
        </div>

        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Número</th>
                        <th>Data</th>
                        <th>Solicitante</th>
                        <th>Departamento</th>
                        <th>Centro de Custo</th>
                        <th>Itens</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="requestsTableBody">
                    <!-- Dados serão carregados via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal de Nova Solicitação -->
    <div class="modal fade" id="requestModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Nova Solicitação de Compra</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="requestForm">
                        <div class="form-section">
                            <h3 class="section-title">Informações Básicas</h3>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="required-field">Solicitante</label>
                                    <input type="text" id="solicitante" class="form-control" required readonly>
                                </div>
                                <div class="form-col">
                                    <label class="required-field">Departamento</label>
                                    <input type="text" id="departamento" class="form-control" required readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="required-field">Centro de Custo</label>
                                    <select id="centroCusto" class="form-control" required>
                                        <option value="">Selecione o centro de custo...</option>
                                    </select>
                                </div>
                                <div class="form-col">
                                    <label>Data Limite</label>
                                    <input type="date" id="dataLimite" class="form-control">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="required-field">Justificativa</label>
                                    <textarea id="justificativa" class="form-control" rows="3" required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">Itens da Solicitação</h3>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Código</th>
                                            <th>Descrição</th>
                                            <th>Quantidade</th>
                                            <th>Unidade</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsTableBody">
                                        <!-- Itens serão adicionados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="addItem()">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveRequest()">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funções JavaScript serão implementadas aqui
        function openNewRequestModal() {
            $('#requestModal').modal('show');
        }

        window.addItem = function(item = {}) {
            try {
                const tableBody = document.getElementById('itemsTableBody');
                if (!tableBody) {
                    console.error('Elemento itemsTableBody não encontrado');
                    return null;
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="text" class="form-control item-codigo" value="${item.codigo || ''}" 
                               placeholder="Código do produto" onblur="searchProduct(this)">
                    </td>
                    <td>
                        <input type="text" class="form-control item-descricao" value="${item.descricao || ''}" 
                               readonly>
                    </td>
                    <td>
                        <input type="number" class="form-control item-quantidade" 
                               value="${item.quantidade || ''}" min="0" step="0.001" 
                               oninput="updateConversion(this.closest('tr'))">
                    </td>
                    <td>
                        <span class="item-unidade">${item.unidade || ''}</span>
                    </td>
                    <td>
                        <select class="form-control item-unidade-compra" 
                                onchange="updateConversion(this.closest('tr'))">
                            <!-- Opções serão preenchidas dinamicamente -->
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control item-quantidade-compra-input" 
                               value="${item.quantidadeCompra || ''}" min="0" step="0.001" 
                               readonly>
                    </td>
                    <td>
                        <input type="text" class="form-control item-observacoes" 
                               value="${item.observacoes || ''}" placeholder="Observações">
                    </td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="removeItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tableBody.appendChild(row);

                // Preencher opções de unidade de compra
                const unidadeCompraSelect = row.querySelector('.item-unidade-compra');
                if (unidadeCompraSelect) {
                    unidadeCompraSelect.innerHTML = `
                        <option value="">Selecione a unidade de compra...</option>
                        <option value="KG">KG</option>
                        <option value="PC">PC</option>
                        <option value="M">M</option>
                        <option value="CX">CX</option>
                    `;
                }

                return row;
            } catch (error) {
                console.error('Erro ao adicionar item:', error);
                alert('Ocorreu um erro ao adicionar o item. Por favor, tente novamente.');
                return null;
            }
        }

        window.saveRequest = async function() {
            try {
                // Validar campos obrigatórios
                const requiredFields = [
                    { id: 'solicitante', name: 'Solicitante' },
                    { id: 'departamento', name: 'Departamento' },
                    { id: 'centroCusto', name: 'Centro de Custo' },
                    { id: 'justificativa', name: 'Justificativa' }
                ];

                let isValid = true;
                let errorMessages = [];

                // Verificar campos obrigatórios
                for (const field of requiredFields) {
                    const element = document.getElementById(field.id);
                    if (!element || !element.value.trim()) {
                        errorMessages.push(`O campo ${field.name} é obrigatório.`);
                        isValid = false;
                    }
                }

                // Verificar itens
                const itens = [];
                const itensTableBody = document.getElementById('itemsTableBody');
                const rows = itensTableBody.querySelectorAll('tr');

                if (rows.length === 0) {
                    errorMessages.push('Adicione pelo menos um item à solicitação.');
                    isValid = false;
                }

                // Validar duplicidade de itens
                const codigosItens = new Set();
                let hasDuplicates = false;
                let hasInvalidProductType = false;

                rows.forEach(row => {
                    const codigo = row.querySelector('.item-codigo').value;
                    if (codigosItens.has(codigo)) {
                        hasDuplicates = true;
                    }
                    codigosItens.add(codigo);

                    const produto = produtos.find(p => p.codigo === codigo);
                    if (produto && (produto.tipo === 'PA' || produto.tipo === 'SP')) {
                        hasInvalidProductType = true;
                    }

                    const quantidade = parseFloat(row.querySelector('.item-quantidade').value) || 0;
                    const descricao = row.querySelector('.item-descricao').value;
                    const unidade = row.querySelector('.item-unidade').textContent;
                    const unidadeCompra = row.querySelector('.item-unidade-compra').value;
                    const quantidadeCompra = parseFloat(row.querySelector('.item-quantidade-compra-input').value) || 0;
                    const observacoes = row.querySelector('.item-observacoes').value;

                    if (quantidade > 0 && descricao) {
                        itens.push({
                            codigo,
                            descricao,
                            quantidade,
                            unidade,
                            quantidadeCompra,
                            unidadeCompra,
                            observacoes,
                            quantidadeOriginal: quantidade,
                            unidadeOriginal: unidade
                        });
                    }
                });

                if (hasDuplicates) {
                    errorMessages.push('Não é permitido incluir o mesmo item mais de uma vez.');
                    isValid = false;
                }

                if (hasInvalidProductType) {
                    errorMessages.push('Não é possível solicitar compra de produtos do tipo PA ou SP.');
                    isValid = false;
                }

                // Verificar centro de custo
                const centroCustoId = document.getElementById('centroCusto').value;
                const centroCusto = centrosCusto.find(cc => cc.id === centroCustoId);
                if (!centroCusto) {
                    errorMessages.push('Centro de custo não encontrado.');
                    isValid = false;
                }

                // Calcular valor total
                let valorTotalSolicitacao = 0;
                itens.forEach(item => {
                    const produto = produtos.find(p => p.codigo === item.codigo);
                    if (produto && produto.precoMedio) {
                        valorTotalSolicitacao += item.quantidade * produto.precoMedio;
                    }
                });

                // Verificar orçamento do centro de custo
                const inicioMes = new Date();
                inicioMes.setDate(1);
                inicioMes.setHours(0, 0, 0, 0);

                const solicitacoesDoMes = solicitacoes.filter(s =>
                    s.centroCustoId === centroCustoId &&
                    s.dataCriacao && s.dataCriacao.seconds &&
                    s.dataCriacao.seconds >= inicioMes.getTime() / 1000 &&
                    s.status !== 'REJEITADA'
                );

                const valorUtilizado = solicitacoesDoMes.reduce((total, s) => {
                    return total + (typeof s.valorTotal === 'number' ? s.valorTotal : 0);
                }, 0);

                const limiteDisponivel = (centroCusto.orcamentoMensal || 0) - valorUtilizado;

                if (valorTotalSolicitacao > limiteDisponivel) {
                    if (!confirm(`Atenção: Esta solicitação excederá o orçamento mensal do centro de custo!\n\n` +
                              `Orçamento Mensal: R$ ${centroCusto.orcamentoMensal?.toFixed(2)}\n` +
                              `Já Utilizado: R$ ${valorUtilizado.toFixed(2)}\n` +
                              `Disponível: R$ ${limiteDisponivel.toFixed(2)}\n` +
                              `Valor Solicitação: R$ ${valorTotalSolicitacao.toFixed(2)}\n\n` +
                              `Deseja continuar mesmo assim?`)) {
                        return;
                    }
                }

                if (!isValid) {
                    alert(errorMessages.join('\n'));
                    return;
                }

                // Preparar dados da solicitação
                const solicitacaoData = {
                    solicitante: document.getElementById('solicitante').value,
                    departamento: document.getElementById('departamento').value,
                    centroCustoId: centroCustoId,
                    fornecedorId: '', // Fornecedor não é mais obrigatório
                    tipo: document.getElementById('tipoSolicitacao').value,
                    prioridade: document.getElementById('prioridade').value,
                    itens: itens,
                    justificativa: document.getElementById('justificativa').value,
                    valorTotal: valorTotalSolicitacao,
                    status: 'PENDENTE',
                    dataCriacao: Timestamp.now(),
                    historico: []
                };

                try {
                    const editingId = document.getElementById('editingId')?.value;
                    if (editingId) {
                        // Atualizar solicitação existente
                        await updateDoc(doc(db, "solicitacoesCompra", editingId), solicitacaoData);
                        alert('Solicitação atualizada com sucesso!');
                    } else {
                        // Criar nova solicitação
                        solicitacaoData.numero = await generateRequestNumber();
                        await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);
                        alert('Solicitação criada com sucesso!');
                    }

                    // Fechar modal e recarregar dados
                    $('#requestModal').modal('hide');
                    await loadInitialData();
                } catch (error) {
                    console.error("Erro ao salvar solicitação:", error);
                    alert('Erro ao salvar solicitação: ' + error.message);
                }
            } catch (error) {
                console.error("Erro no processo de validação:", error);
                alert('Erro ao processar solicitação. Por favor, tente novamente.');
            }
        }

        function filterByStatus(status) {
            // Implementar filtro por status
        }

        function filterBySolicitante(solicitante) {
            // Implementar filtro por solicitante
        }

        function filterByDate(date) {
            // Implementar filtro por data
        }

        async function loadRequests() {
            try {
                const tbody = document.getElementById('requestsTableBody');
                if (!tbody) {
                    console.error('Elemento requestsTableBody não encontrado');
                    return;
                }

                tbody.innerHTML = '';

                // Ordenar solicitações por data de criação (mais recentes primeiro)
                const sortedSolicitacoes = [...solicitacoes].sort((a, b) => {
                    if (!a.dataCriacao || !b.dataCriacao) return 0;
                    return b.dataCriacao.seconds - a.dataCriacao.seconds;
                });

                for (const solicitacao of sortedSolicitacoes) {
                    const row = document.createElement('tr');
                    row.setAttribute('data-id', solicitacao.id);
                    row.ondblclick = () => {
                        viewRequest(solicitacao.id);
                    };

                    // Definir hasCotacao para cada solicitação
                    const hasCotacao = cotacoes.some(c => c.solicitacaoId === solicitacao.id);

                    const isSystemGenerated = solicitacao.origem === 'MRP';
                    const solicitanteDisplay = isSystemGenerated ? 'Sistema' : (solicitacao.solicitante || 'N/A');
                    const departamentoDisplay = isSystemGenerated ? 'PRODUCAO' : (solicitacao.departamento || 'N/A');
                    const centroCustoDisplay = isSystemGenerated ? 'PROD' : 
                        (centrosCusto.find(cc => cc.id === solicitacao.centroCustoId) ? 
                            `${centrosCusto.find(cc => cc.id === solicitacao.centroCustoId).codigo} - ${centrosCusto.find(cc => cc.id === solicitacao.centroCustoId).descricao}` : 
                            'N/A');

                    // Obter status da solicitação
                    const status = solicitacao.status || 'PENDENTE';
                    const statusClass = getStatusClass(status);

                    // Obter família dos produtos
                    const familiasProdutos = getProductFamilies(solicitacao.itens || []);

                    row.innerHTML = `
                        <td>${solicitacao.numero || 'N/A'}</td>
                        <td>${solicitacao.dataCriacao ? new Date(solicitacao.dataCriacao.seconds * 1000).toLocaleDateString() : 'N/A'}</td>
                        <td>${solicitanteDisplay}</td>
                        <td>${departamentoDisplay}</td>
                        <td>${centroCustoDisplay}</td>
                        <td style="text-align: center;">${solicitacao.itens?.length || 0}</td>
                        <td>${familiasProdutos}</td>
                        <td class="text-center">
                            <span class="badge ${statusClass}">${status}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm ${hasCotacao ? 'btn-success' : 'btn-secondary'}" 
                                    onclick="viewCotacoes('${solicitacao.id}')">
                                ${hasCotacao ? 'Cotação' : 'Sem Cotação'}
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="editRequest('${solicitacao.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteRequest('${solicitacao.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;

                    tbody.appendChild(row);
                }

                // Atualizar contadores de status
                updateStatusCounters();

            } catch (error) {
                console.error('Erro ao carregar solicitações:', error);
                alert('Erro ao carregar solicitações. Por favor, tente novamente.');
            }
        }

        function getStatusClass(status) {
            const statusClasses = {
                'PENDENTE': 'badge-warning',
                'APROVADA': 'badge-success',
                'REJEITADA': 'badge-danger',
                'EM_COTACAO': 'badge-info',
                'EM_COMPRA': 'badge-primary',
                'FINALIZADA': 'badge-secondary'
            };
            return statusClasses[status] || 'badge-secondary';
        }

        function getProductFamilies(itens) {
            if (!itens || itens.length === 0) return 'N/A';

            const familiasEncontradas = new Set();
            itens.forEach(item => {
                const produto = produtos.find(p => p.codigo === item.codigo);
                if (produto && produto.familia) {
                    familiasEncontradas.add(produto.familia);
                }
            });

            return Array.from(familiasEncontradas).join(', ') || 'N/A';
        }

        function updateStatusCounters() {
            const statusCounts = {
                PENDENTE: 0,
                APROVADA: 0,
                REJEITADA: 0,
                EM_COTACAO: 0,
                EM_COMPRA: 0,
                FINALIZADA: 0
            };

            solicitacoes.forEach(s => {
                const status = s.status || 'PENDENTE';
                if (status in statusCounts) {
                    statusCounts[status]++;
                }
            });

            // Atualizar badges
            document.querySelectorAll('.status-badge').forEach(badge => {
                const status = badge.dataset.status;
                if (status && status in statusCounts) {
                    badge.textContent = statusCounts[status];
                }
            });
        }

        window.updateConversion = function(row) {
            try {
                const quantidadeInput = row.querySelector('.item-quantidade');
                const unidadeCompraSelect = row.querySelector('.item-unidade-compra');
                const quantidadeCompraInput = row.querySelector('.item-quantidade-compra-input');
                const unidadeSpan = row.querySelector('.item-unidade');

                if (!quantidadeInput || !unidadeCompraSelect || !quantidadeCompraInput || !unidadeSpan) {
                    console.error('Elementos necessários para a conversão não encontrados');
                    return;
                }

                const quantidade = parseFloat(quantidadeInput.value) || 0;
                const unidadeCompra = unidadeCompraSelect.value;
                const unidade = unidadeSpan.textContent;

                // Definir fatores de conversão
                const conversionFactors = {
                    'KG': { 'PC': 1000, 'M': 1, 'CX': 10 },
                    'PC': { 'KG': 0.001, 'M': 0.001, 'CX': 0.01 },
                    'M': { 'KG': 1, 'PC': 1000, 'CX': 10 },
                    'CX': { 'KG': 0.1, 'PC': 100, 'M': 0.1 }
                };

                // Calcular quantidade de compra
                let quantidadeCompra = quantidade;
                if (unidadeCompra && unidade) {
                    const factor = conversionFactors[unidade]?.[unidadeCompra] || 1;
                    quantidadeCompra = quantidade * factor;
                }

                // Arredondar para 3 casas decimais
                quantidadeCompraInput.value = quantidadeCompra.toFixed(3);

                // Atualizar dataset
                row.dataset.quantidade = quantidade;
                row.dataset.unidade = unidade;
                row.dataset.quantidadeCompra = quantidadeCompra;
                row.dataset.unidadeCompra = unidadeCompra;

                // Atualizar valor total da solicitação
                updateTotalValue();

            } catch (error) {
                console.error('Erro ao atualizar conversão:', error);
            }
        }

        function updateTotalValue() {
            try {
                const itensTableBody = document.getElementById('itemsTableBody');
                if (!itensTableBody) return;

                let total = 0;
                itensTableBody.querySelectorAll('tr').forEach(row => {
                    const codigo = row.querySelector('.item-codigo').value;
                    const quantidade = parseFloat(row.dataset.quantidade) || 0;
                    const produto = produtos.find(p => p.codigo === codigo);
                    if (produto && produto.precoMedio) {
                        total += quantidade * produto.precoMedio;
                    }
                });

                const totalElement = document.getElementById('valorTotal');
                if (totalElement) {
                    totalElement.textContent = `R$ ${total.toFixed(2)}`;
                }
            } catch (error) {
                console.error('Erro ao atualizar valor total:', error);
            }
        }

        window.removeItem = function(button) {
            const row = button.closest('tr');
            if (row) {
                row.remove();
            }
        }

        window.searchProduct = async function(input) {
            try {
                const codigo = input.value.trim();
                if (!codigo) {
                    input.parentElement.classList.remove('has-error');
                    return;
                }

                const produto = produtos.find(p => p.codigo === codigo);
                if (!produto) {
                    input.parentElement.classList.add('has-error');
                    alert('Produto não encontrado.');
                    return;
                }

                input.parentElement.classList.remove('has-error');

                // Atualizar campos do item
                const row = input.closest('tr');
                if (row) {
                    const descricaoInput = row.querySelector('.item-descricao');
                    const unidadeSpan = row.querySelector('.item-unidade');
                    const unidadeCompraSelect = row.querySelector('.item-unidade-compra');

                    if (descricaoInput) {
                        descricaoInput.value = produto.descricao;
                    }
                    if (unidadeSpan) {
                        unidadeSpan.textContent = produto.unidade;
                    }
                    if (unidadeCompraSelect) {
                        // Selecionar unidade de compra padrão
                        const unidadeCompra = produto.unidadeCompra || produto.unidade;
                        const option = Array.from(unidadeCompraSelect.options).find(opt => opt.value === unidadeCompra);
                        if (option) {
                            option.selected = true;
                        }
                    }
                }
            } catch (error) {
                console.error('Erro ao buscar produto:', error);
                alert('Erro ao buscar produto. Por favor, tente novamente.');
            }
        }
    </script>
</body>
</html>
