<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sistema de Orçamentos Integrado - Padrão TOTVS</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --totvs-primary: #0066cc;
      --totvs-secondary: #f5f5f5;
      --totvs-accent: #ff6b35;
      --totvs-success: #28a745;
      --totvs-warning: #ffc107;
      --totvs-danger: #dc3545;
      --totvs-border: #dee2e6;
    }

    * { box-sizing: border-box; margin: 0; padding: 0; }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f8f9fa;
      font-size: 14px;
    }

    .totvs-header {
      background: linear-gradient(135deg, var(--totvs-primary), #004a99);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .totvs-logo {
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 20px rgba(0,0,0,0.1);
      padding: 25px;
    }

    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 2px solid var(--totvs-border);
    }

    .module-title {
      color: var(--totvs-primary);
      font-size: 28px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .tabs {
      display: flex;
      border-bottom: 2px solid var(--totvs-border);
      margin-bottom: 20px;
    }

    .tab {
      padding: 15px 25px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }

    .tab.active {
      background: var(--totvs-primary);
      color: white;
      transform: translateY(2px);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .form-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      border-left: 4px solid var(--totvs-primary);
    }

    .form-section h3 {
      color: var(--totvs-primary);
      margin-bottom: 15px;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      margin-bottom: 5px;
      color: #495057;
      font-size: 13px;
    }

    .form-group label.required::after {
      content: " *";
      color: var(--totvs-danger);
    }

    .form-control {
      padding: 10px 12px;
      border: 2px solid var(--totvs-border);
      border-radius: 6px;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--totvs-primary);
      box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: var(--totvs-primary);
      color: white;
    }

    .btn-primary:hover {
      background: #0052a3;
      transform: translateY(-2px);
    }

    .btn-success {
      background: var(--totvs-success);
      color: white;
    }

    .btn-warning {
      background: var(--totvs-warning);
      color: #212529;
    }

    .btn-danger {
      background: var(--totvs-danger);
      color: white;
    }

    .btn-outline {
      background: transparent;
      border: 2px solid var(--totvs-primary);
      color: var(--totvs-primary);
    }

    .table-responsive {
      overflow-x: auto;
      margin-top: 20px;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table th {
      background: var(--totvs-primary);
      color: white;
      padding: 15px 12px;
      text-align: left;
      font-weight: 600;
      font-size: 13px;
    }

    .table td {
      padding: 12px;
      border-bottom: 1px solid var(--totvs-border);
      font-size: 13px;
    }

    .table tr:hover {
      background: #f8f9fa;
    }

    .item-type-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .badge-produto {
      background: #e3f2fd;
      color: #1976d2;
    }

    .badge-servico {
      background: #fff3e0;
      color: #f57c00;
    }

    .badge-kit {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .price-table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 15px;
    }

    .price-table-header {
      background: var(--totvs-primary);
      color: white;
      padding: 12px 15px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.6);
      z-index: 1000;
    }

    .modal-content {
      background: white;
      margin: 2% auto;
      padding: 0;
      width: 95%;
      max-width: 1200px;
      border-radius: 8px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .modal-header {
      background: var(--totvs-primary);
      color: white;
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-body {
      padding: 25px;
    }

    .modal-footer {
      padding: 20px 25px;
      background: #f8f9fa;
      border-top: 1px solid var(--totvs-border);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .close-modal {
      background: none;
      border: none;
      font-size: 24px;
      color: white;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
    }

    .close-modal:hover {
      background: rgba(255,255,255,0.1);
    }

    .calculation-panel {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
      border: 2px solid var(--totvs-border);
    }

    .calculation-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding: 8px 0;
    }

    .calculation-row.total {
      border-top: 2px solid var(--totvs-primary);
      padding-top: 15px;
      margin-top: 15px;
      font-weight: 600;
      font-size: 16px;
      color: var(--totvs-primary);
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 2000;
      display: flex;
      align-items: center;
      gap: 10px;
      min-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .notification.success {
      background: var(--totvs-success);
    }

    .notification.error {
      background: var(--totvs-danger);
    }

    .notification.warning {
      background: var(--totvs-warning);
      color: #212529;
    }

    .quick-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        padding: 15px;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .module-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
      }
      
      .tabs {
        flex-wrap: wrap;
      }
      
      .tab {
        flex: 1;
        min-width: 120px;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="totvs-header">
    <div class="totvs-logo">
      <i class="fas fa-chart-line"></i>
      SISTEMA INTEGRADO DE VENDAS
    </div>
    <div id="userInfo">
      <i class="fas fa-user"></i>
      <span id="userName">Admin</span> | Empresa: 1000
    </div>
  </div>

  <div class="container">
    <div class="module-header">
      <h1 class="module-title">
        <i class="fas fa-file-invoice-dollar"></i>
        Sistema de Orçamentos
      </h1>
      <div class="quick-actions">
        <button class="btn btn-primary" onclick="openQuoteModal()">
          <i class="fas fa-plus"></i> Novo Orçamento
        </button>
        <button class="btn btn-outline" onclick="openPriceTableModal()">
          <i class="fas fa-table"></i> Tabelas de Preço
        </button>
        <button class="btn btn-outline" onclick="generateReport()">
          <i class="fas fa-chart-bar"></i> Relatórios
        </button>
      </div>
    </div>

    <div class="tabs">
      <button class="tab active" onclick="switchTab('quotes')">
        <i class="fas fa-file-invoice"></i> Orçamentos
      </button>
      <button class="tab" onclick="switchTab('products')">
        <i class="fas fa-cube"></i> Produtos
      </button>
      <button class="tab" onclick="switchTab('services')">
        <i class="fas fa-tools"></i> Serviços
      </button>
      <button class="tab" onclick="switchTab('kits')">
        <i class="fas fa-box"></i> Kits/Pacotes
      </button>
      <button class="tab" onclick="switchTab('analytics')">
        <i class="fas fa-analytics"></i> Análises
      </button>
    </div>

    <!-- Aba Orçamentos -->
    <div id="quotes" class="tab-content active">
      <div class="form-section">
        <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>Buscar Orçamento</label>
            <input type="text" id="searchQuote" class="form-control" placeholder="Número, cliente, vendedor...">
          </div>
          <div class="form-group">
            <label>Status</label>
            <select id="filterStatus" class="form-control">
              <option value="">Todos</option>
              <option value="Rascunho">Rascunho</option>
              <option value="Enviado">Enviado</option>
              <option value="Aprovado">Aprovado</option>
              <option value="Negociação">Em Negociação</option>
              <option value="Perdido">Perdido</option>
              <option value="Cancelado">Cancelado</option>
            </select>
          </div>
          <div class="form-group">
            <label>Período</label>
            <input type="date" id="filterDateFrom" class="form-control">
          </div>
          <div class="form-group">
            <label>até</label>
            <input type="date" id="filterDateTo" class="form-control">
          </div>
          <div class="form-group">
            <label>Vendedor</label>
            <select id="filterSeller" class="form-control">
              <option value="">Todos</option>
            </select>
          </div>
          <div class="form-group">
            <label>Valor Mínimo</label>
            <input type="number" id="filterMinValue" class="form-control" placeholder="0,00">
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Número</th>
              <th>Cliente</th>
              <th>Vendedor</th>
              <th>Data</th>
              <th>Validade</th>
              <th>Valor Total</th>
              <th>Status</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody id="quotesTableBody">
            <!-- Dados serão carregados aqui -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Aba Produtos -->
    <div id="products" class="tab-content">
      <div class="form-section">
        <h3><i class="fas fa-cube"></i> Gestão de Produtos</h3>
        <p>Produtos físicos que compõem o estoque e são vendidos aos clientes.</p>
      </div>
      
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Família</th>
              <th>Unidade</th>
              <th>Preço Base</th>
              <th>Margem</th>
              <th>Estoque</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody id="productsTableBody">
            <!-- Dados serão carregados aqui -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Aba Serviços -->
    <div id="services" class="tab-content">
      <div class="form-section">
        <h3><i class="fas fa-tools"></i> Gestão de Serviços</h3>
        <p>Serviços oferecidos pela empresa, calculados por hora ou por projeto.</p>
      </div>
      
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Categoria</th>
              <th>Tipo Cobrança</th>
              <th>Valor/Hora</th>
              <th>Tempo Estimado</th>
              <th>Valor Fixo</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody id="servicesTableBody">
            <!-- Dados serão carregados aqui -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Aba Kits -->
    <div id="kits" class="tab-content">
      <div class="form-section">
        <h3><i class="fas fa-box"></i> Kits e Pacotes</h3>
        <p>Combinações de produtos e serviços vendidos em conjunto com preços especiais.</p>
      </div>
      
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Itens Inclusos</th>
              <th>Preço Individual</th>
              <th>Preço do Kit</th>
              <th>Desconto</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody id="kitsTableBody">
            <!-- Dados serão carregados aqui -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Aba Análises -->
    <div id="analytics" class="tab-content">
      <div class="form-section">
        <h3><i class="fas fa-analytics"></i> Dashboard de Vendas</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>Período de Análise</label>
            <select class="form-control">
              <option>Últimos 30 dias</option>
              <option>Últimos 90 dias</option>
              <option>Este ano</option>
              <option>Personalizado</option>
            </select>
          </div>
        </div>
      </div>
      
      <div id="analyticsContent">
        <!-- Gráficos e métricas serão carregados aqui -->
      </div>
    </div>
  </div>

  <!-- Modal de Orçamento -->
  <div id="quoteModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitle">Novo Orçamento</h3>
        <button class="close-modal" onclick="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <form id="quoteForm">
          <div class="form-section">
            <h3><i class="fas fa-info-circle"></i> Informações Gerais</h3>
            <div class="form-grid">
              <div class="form-group">
                <label class="required">Número</label>
                <input type="text" id="quoteNumber" class="form-control" readonly>
              </div>
              <div class="form-group">
                <label class="required">Cliente</label>
                <select id="quoteClient" class="form-control" required>
                  <option value="">Selecione o cliente...</option>
                </select>
              </div>
              <div class="form-group">
                <label class="required">Vendedor</label>
                <select id="quoteSeller" class="form-control" required>
                  <option value="">Selecione o vendedor...</option>
                </select>
              </div>
              <div class="form-group">
                <label class="required">Data Validade</label>
                <input type="date" id="quoteValidity" class="form-control" required>
              </div>
              <div class="form-group">
                <label>Condição de Pagamento</label>
                <select id="quotePaymentTerm" class="form-control">
                  <option value="">Selecione...</option>
                  <option value="VISTA">À Vista</option>
                  <option value="30DD">30 Dias</option>
                  <option value="PARCELADO">Parcelado</option>
                </select>
              </div>
              <div class="form-group">
                <label>Desconto Geral (%)</label>
                <input type="number" id="quoteDiscount" class="form-control" min="0" max="100" step="0.01">
              </div>
            </div>
          </div>

          <div class="form-section">
            <h3><i class="fas fa-list"></i> Itens do Orçamento</h3>
            <div id="quoteItems">
              <!-- Itens serão adicionados aqui -->
            </div>
            <div style="margin-top: 15px;">
              <button type="button" class="btn btn-primary" onclick="addQuoteItem()">
                <i class="fas fa-plus"></i> Adicionar Item
              </button>
            </div>
          </div>

          <div class="calculation-panel">
            <div class="calculation-row">
              <span>Subtotal de Produtos:</span>
              <span id="subtotalProducts">R$ 0,00</span>
            </div>
            <div class="calculation-row">
              <span>Subtotal de Serviços:</span>
              <span id="subtotalServices">R$ 0,00</span>
            </div>
            <div class="calculation-row">
              <span>Desconto:</span>
              <span id="totalDiscount">R$ 0,00</span>
            </div>
            <div class="calculation-row">
              <span>Impostos:</span>
              <span id="totalTaxes">R$ 0,00</span>
            </div>
            <div class="calculation-row total">
              <span>TOTAL GERAL:</span>
              <span id="totalValue">R$ 0,00</span>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline" onclick="closeModal()">
          <i class="fas fa-times"></i> Cancelar
        </button>
        <button type="button" class="btn btn-primary" onclick="saveQuote()">
          <i class="fas fa-save"></i> Salvar
        </button>
        <button type="button" class="btn btn-success" onclick="generateProposal()">
          <i class="fas fa-file-pdf"></i> Gerar Proposta
        </button>
      </div>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      addDoc, 
      updateDoc, 
      deleteDoc, 
      doc,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    // Variáveis globais
    let quotes = [];
    let products = [];
    let services = [];
    let kits = [];
    let clients = [];
    let sellers = [];
    let currentUser = null;
    let editingQuoteId = null;

    // Inicialização
    window.onload = async function() {
      currentUser = JSON.parse(localStorage.getItem('currentUser'));
      if (!currentUser) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userName').textContent = currentUser.nome;
      await loadAllData();
    };

    // Carregar todos os dados
    async function loadAllData() {
      try {
        const [quotesSnap, productsSnap, servicesSnap, clientsSnap] = await Promise.all([
          getDocs(collection(db, "orcamentos")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "servicos")),
          getDocs(collection(db, "clientes"))
        ]);

        quotes = quotesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        products = productsSnap.docs.map(doc => ({ id: doc.id, ...doc.data(), tipo: 'PRODUTO' }));
        services = servicesSnap.docs.map(doc => ({ id: doc.id, ...doc.data(), tipo: 'SERVICO' }));
        clients = clientsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        populateSelects();
        renderTables();
        
        showNotification('Dados carregados com sucesso!', 'success');
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        showNotification('Erro ao carregar dados', 'error');
      }
    }

    // Funções de interface
    window.switchTab = function(tabName) {
      // Ocultar todas as abas
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // Remover classe ativa de todas as tabs
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // Mostrar aba selecionada
      document.getElementById(tabName).classList.add('active');
      
      // Ativar tab
      event.target.classList.add('active');
    };

    window.openQuoteModal = function() {
      editingQuoteId = null;
      document.getElementById('modalTitle').textContent = 'Novo Orçamento';
      document.getElementById('quoteNumber').value = generateQuoteNumber();
      document.getElementById('quoteForm').reset();
      document.getElementById('quoteItems').innerHTML = '';
      addQuoteItem(); // Adicionar primeiro item
      document.getElementById('quoteModal').style.display = 'block';
    };

    window.closeModal = function() {
      document.getElementById('quoteModal').style.display = 'none';
    };

    window.addQuoteItem = function() {
      const itemsContainer = document.getElementById('quoteItems');
      const itemIndex = itemsContainer.children.length;
      
      const itemDiv = document.createElement('div');
      itemDiv.className = 'form-section';
      itemDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <h4>Item ${itemIndex + 1}</h4>
          <button type="button" class="btn btn-danger" onclick="removeQuoteItem(this)">
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div class="form-grid">
          <div class="form-group">
            <label class="required">Tipo</label>
            <select class="form-control item-type" onchange="updateItemOptions(this)" required>
              <option value="">Selecione...</option>
              <option value="PRODUTO">Produto</option>
              <option value="SERVICO">Serviço</option>
              <option value="KIT">Kit/Pacote</option>
            </select>
          </div>
          <div class="form-group">
            <label class="required">Item</label>
            <select class="form-control item-select" required disabled>
              <option value="">Primeiro selecione o tipo...</option>
            </select>
          </div>
          <div class="form-group">
            <label class="required">Quantidade</label>
            <input type="number" class="form-control item-quantity" min="0.01" step="0.01" required>
          </div>
          <div class="form-group">
            <label class="required">Valor Unitário</label>
            <input type="number" class="form-control item-price" min="0.01" step="0.01" required>
          </div>
          <div class="form-group">
            <label>Desconto (%)</label>
            <input type="number" class="form-control item-discount" min="0" max="100" step="0.01">
          </div>
          <div class="form-group">
            <label>Total</label>
            <input type="number" class="form-control item-total" readonly>
          </div>
        </div>
      `;
      
      itemsContainer.appendChild(itemDiv);
      setupItemEvents(itemDiv);
    };

    window.removeQuoteItem = function(button) {
      const itemDiv = button.closest('.form-section');
      itemDiv.remove();
      updateItemNumbers();
      calculateTotals();
    };

    function updateItemOptions(typeSelect) {
      const itemSelect = typeSelect.closest('.form-section').querySelector('.item-select');
      const type = typeSelect.value;
      
      itemSelect.innerHTML = '<option value="">Selecione o item...</option>';
      itemSelect.disabled = !type;
      
      if (type === 'PRODUTO') {
        products.forEach(product => {
          itemSelect.innerHTML += `<option value="${product.id}" data-price="${product.precoVenda || 0}">${product.codigo} - ${product.descricao}</option>`;
        });
      } else if (type === 'SERVICO') {
        services.forEach(service => {
          itemSelect.innerHTML += `<option value="${service.id}" data-price="${service.valorHora || service.valorFixo || 0}">${service.codigo} - ${service.descricao}</option>`;
        });
      }
    }

    function setupItemEvents(itemDiv) {
      const typeSelect = itemDiv.querySelector('.item-type');
      const itemSelect = itemDiv.querySelector('.item-select');
      const quantityInput = itemDiv.querySelector('.item-quantity');
      const priceInput = itemDiv.querySelector('.item-price');
      const discountInput = itemDiv.querySelector('.item-discount');
      const totalInput = itemDiv.querySelector('.item-total');

      // Atualizar preço quando item é selecionado
      itemSelect.addEventListener('change', function() {
        const selectedOption = this.selectedOptions[0];
        const price = selectedOption?.dataset.price || 0;
        priceInput.value = price;
        calculateItemTotal(itemDiv);
      });

      // Recalcular total quando valores mudam
      [quantityInput, priceInput, discountInput].forEach(input => {
        input.addEventListener('input', () => calculateItemTotal(itemDiv));
      });
    }

    function calculateItemTotal(itemDiv) {
      const quantity = parseFloat(itemDiv.querySelector('.item-quantity').value) || 0;
      const price = parseFloat(itemDiv.querySelector('.item-price').value) || 0;
      const discount = parseFloat(itemDiv.querySelector('.item-discount').value) || 0;
      
      const subtotal = quantity * price;
      const discountAmount = subtotal * (discount / 100);
      const total = subtotal - discountAmount;
      
      itemDiv.querySelector('.item-total').value = total.toFixed(2);
      calculateTotals();
    }

    function calculateTotals() {
      let subtotalProducts = 0;
      let subtotalServices = 0;
      let totalDiscount = 0;
      
      document.querySelectorAll('#quoteItems .form-section').forEach(itemDiv => {
        const type = itemDiv.querySelector('.item-type').value;
        const total = parseFloat(itemDiv.querySelector('.item-total').value) || 0;
        const quantity = parseFloat(itemDiv.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemDiv.querySelector('.item-price').value) || 0;
        const discount = parseFloat(itemDiv.querySelector('.item-discount').value) || 0;
        
        if (type === 'PRODUTO') {
          subtotalProducts += total;
        } else if (type === 'SERVICO') {
          subtotalServices += total;
        }
        
        totalDiscount += (quantity * price) * (discount / 100);
      });
      
      const generalDiscount = parseFloat(document.getElementById('quoteDiscount')?.value) || 0;
      const subtotal = subtotalProducts + subtotalServices;
      const generalDiscountAmount = subtotal * (generalDiscount / 100);
      const taxes = subtotal * 0.1; // 10% de impostos (exemplo)
      const total = subtotal - generalDiscountAmount + taxes;
      
      document.getElementById('subtotalProducts').textContent = `R$ ${subtotalProducts.toFixed(2)}`;
      document.getElementById('subtotalServices').textContent = `R$ ${subtotalServices.toFixed(2)}`;
      document.getElementById('totalDiscount').textContent = `R$ ${(totalDiscount + generalDiscountAmount).toFixed(2)}`;
      document.getElementById('totalTaxes').textContent = `R$ ${taxes.toFixed(2)}`;
      document.getElementById('totalValue').textContent = `R$ ${total.toFixed(2)}`;
    }

    function updateItemNumbers() {
      document.querySelectorAll('#quoteItems .form-section').forEach((itemDiv, index) => {
        const header = itemDiv.querySelector('h4');
        header.textContent = `Item ${index + 1}`;
      });
    }

    function populateSelects() {
      // Popular select de clientes
      const clientSelect = document.getElementById('quoteClient');
      if (clientSelect) {
        clientSelect.innerHTML = '<option value="">Selecione o cliente...</option>';
        clients.forEach(client => {
          clientSelect.innerHTML += `<option value="${client.id}">${client.nome || client.razaoSocial}</option>`;
        });
      }
    }

    function renderTables() {
      renderQuotesTable();
      renderProductsTable();
      renderServicesTable();
    }

    function renderQuotesTable() {
      const tbody = document.getElementById('quotesTableBody');
      tbody.innerHTML = '';
      
      if (quotes.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Nenhum orçamento encontrado</td></tr>';
        return;
      }
      
      quotes.forEach(quote => {
        const client = clients.find(c => c.id === quote.clienteId);
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${quote.numero}</td>
          <td>${client?.nome || client?.razaoSocial || 'Desconhecido'}</td>
          <td>${quote.vendedor || 'N/A'}</td>
          <td>${formatDate(quote.dataCriacao)}</td>
          <td>${formatDate(quote.dataValidade)}</td>
          <td style="text-align: right;">R$ ${(quote.valorTotal || 0).toFixed(2)}</td>
          <td><span class="item-type-badge badge-${quote.status?.toLowerCase() || 'rascunho'}">${quote.status || 'Rascunho'}</span></td>
          <td>
            <button class="btn btn-primary" onclick="editQuote('${quote.id}')">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-danger" onclick="deleteQuote('${quote.id}')">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    function renderProductsTable() {
      const tbody = document.getElementById('productsTableBody');
      tbody.innerHTML = '';
      
      products.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${product.codigo}</td>
          <td>${product.descricao}</td>
          <td>${product.familia || 'N/A'}</td>
          <td>${product.unidade || 'UN'}</td>
          <td style="text-align: right;">R$ ${(product.precoVenda || 0).toFixed(2)}</td>
          <td style="text-align: right;">${(product.margem || 0).toFixed(1)}%</td>
          <td style="text-align: right;">${product.estoque || 0}</td>
          <td><span class="item-type-badge badge-produto">Ativo</span></td>
        `;
        tbody.appendChild(row);
      });
    }

    function renderServicesTable() {
      const tbody = document.getElementById('servicesTableBody');
      tbody.innerHTML = '';
      
      services.forEach(service => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${service.codigo}</td>
          <td>${service.descricao}</td>
          <td>${service.categoria || 'N/A'}</td>
          <td>${service.tipoCobranca || 'Por Hora'}</td>
          <td style="text-align: right;">R$ ${(service.valorHora || 0).toFixed(2)}</td>
          <td style="text-align: right;">${service.tempoEstimado || 0}h</td>
          <td style="text-align: right;">R$ ${(service.valorFixo || 0).toFixed(2)}</td>
          <td><span class="item-type-badge badge-servico">Ativo</span></td>
        `;
        tbody.appendChild(row);
      });
    }

    // Funções utilitárias
    function generateQuoteNumber() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
      return `ORC${year}${month}${day}${time}`;
    }

    function formatDate(timestamp) {
      if (!timestamp) return 'N/A';
      try {
        const date = timestamp.seconds ? new Date(timestamp.seconds * 1000) : new Date(timestamp);
        return date.toLocaleDateString('pt-BR');
      } catch (error) {
        return 'N/A';
      }
    }

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
      `;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 4000);
    }

    // Funções globais
    window.openPriceTableModal = function() {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    window.generateReport = function() {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    window.saveQuote = function() {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    window.generateProposal = function() {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    window.editQuote = function(id) {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    window.deleteQuote = function(id) {
      showNotification('Funcionalidade em desenvolvimento', 'warning');
    };

    // Eventos de desconto geral
    document.addEventListener('DOMContentLoaded', function() {
      const discountInput = document.getElementById('quoteDiscount');
      if (discountInput) {
        discountInput.addEventListener('input', calculateTotals);
      }
    });
  </script>
</body>
</html>