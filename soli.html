<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Solicitações de Compra</title>
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --border-color: #d4d4d4;
      --text-color: #333;
      --header-bg: #354a5f;
      --success-color: #107e3e;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
      color: var(--text-color);
    }

    .container {
      width: 95%;
      max-width: 1200px;
      margin: 30px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }

    .requests-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }

    .requests-table th, .requests-table td {
      padding: 12px;
      border: 1px solid var(--border-color);
    }

    .requests-table th {
      background-color: #f0f3f6;
      font-weight: 600;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 800px;
      border-radius: 8px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .close-button {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    input[readonly] {
      background-color: #f0f3f6;
      cursor: not-allowed;
    }

    .item-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .item-table th, .item-table td {
      padding: 10px;
      border: 1px solid var(--border-color);
    }

    .item-table th {
      background-color: #f0f3f6;
      font-weight: 600;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-hover);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Solicitações de Compra</h1>
      <button onclick="window.location.href='index.html'" class="btn-primary">Voltar</button>
    </div>

    <div style="padding: 20px;">
      <button onclick="openRequestModal()" class="btn-primary">Nova Solicitação</button>
    </div>

    <table class="requests-table">
      <thead>
        <tr>
          <th>Número</th>
          <th>Data</th>
          <th>Centro de Custo</th>
          <th>Fornecedor</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="requestsTableBody"></tbody>
    </table>
  </div>

  <div id="requestModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal('requestModal')">×</span>
      <h2>Nova Solicitação de Compra</h2>
      <div class="form-group">
        <label for="requestNumber">Número da Solicitação</label>
        <input id="requestNumber" type="text" readonly>
      </div>
      <div class="form-group">
        <label for="centroCusto">Centro de Custo</label>
        <select id="centroCusto" required>
          <option value="">Selecione...</option>
          <option value="CC001">CC001 - Produção</option>
          <option value="CC002">CC002 - Administração</option>
        </select>
      </div>
      <div class="form-group">
        <label for="fornecedor">Fornecedor</label>
        <select id="fornecedor" required>
          <option value="">Selecione...</option>
          <option value="F001">F001 - Fornecedor A</option>
          <option value="F002">F002 - Fornecedor B</option>
        </select>
      </div>
      <div class="form-group">
        <h3>Itens</h3>
        <table class="item-table">
          <thead>
            <tr>
              <th>Código</th>
              <th>Descrição</th>
              <th>Quantidade</th>
              <th>Unidade</th>
              <th>Ação</th>
            </tr>
          </thead>
          <tbody id="itemsTableBody">
            <tr>
              <td><input type="text" class="item-codigo" placeholder="Código"></td>
              <td><input type="text" class="item-descricao" placeholder="Descrição"></td>
              <td><input type="number" class="item-quantidade" min="1" value="1"></td>
              <td><input type="text" class="item-unidade" placeholder="Un"></td>
              <td><button onclick="addItemRow()">Adicionar</button></td>
            </tr>
          </tbody>
        </table>
      </div>
      <button onclick="createRequest()" class="btn-primary">Criar Solicitação</button>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, addDoc, getDocs, doc, updateDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

    let currentUser = null;
    let solicitacoes = [];

    window.onload = async function() {
      const userSession = localStorage.getItem('currentUser');
      if (!userSession) {
        window.location.href = 'login.html';
        return;
      }
      currentUser = JSON.parse(userSession);
      await loadRequests();
    };

    async function loadRequests() {
      try {
        const solicitacoesSnap = await getDocs(collection(db, 'solicitacoesCompra'));
        solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const tableBody = document.getElementById('requestsTableBody');
        tableBody.innerHTML = '';
        solicitacoes.forEach(solicitacao => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${solicitacao.numero}</td>
            <td>${new Date(solicitacao.dataCriacao.seconds * 1000).toLocaleDateString()}</td>
            <td>${solicitacao.centroCustoId}</td>
            <td>${solicitacao.fornecedorId}</td>
            <td>${solicitacao.status}</td>
            <td>
              ${solicitacao.status === 'PENDENTE' ? `
                <button class="btn-success create-quotation-button" data-solicitacao-id="${solicitacao.id}" onclick="createQuotation('${solicitacao.id}')">Gerar Cotação</button>
              ` : ''}
            </td>
          `;
          tableBody.appendChild(row);
        });
      } catch (error) {
        console.error('Erro ao carregar solicitações:', error);
        alert('Erro ao carregar solicitações.');
      }
    }

    window.openRequestModal = function() {
      const modal = document.getElementById('requestModal');
      const requestNumberInput = document.getElementById('requestNumber');
      
      // Gerar número da solicitação
      const date = new Date();
      const yearMonth = date.toISOString().slice(2, 7).replace('-', '');
      const docRef = doc(collection(db, 'solicitacoesCompra'));
      const sequence = docRef.id.slice(0, 6);
      requestNumberInput.value = `SL${yearMonth}${sequence}`;
      
      // Limpar campos
      document.getElementById('centroCusto').value = '';
      document.getElementById('fornecedor').value = '';
      document.getElementById('itemsTableBody').innerHTML = `
        <tr>
          <td><input type="text" class="item-codigo" placeholder="Código"></td>
          <td><input type="text" class="item-descricao" placeholder="Descrição"></td>
          <td><input type="number" class="item-quantidade" min="1" value="1"></td>
          <td><input type="text" class="item-unidade" placeholder="Un"></td>
          <td><button onclick="addItemRow()">Adicionar</button></td>
        </tr>
      `;
      
      modal.style.display = 'block';
    };

    window.closeModal = function(modalId) {
      document.getElementById(modalId).style.display = 'none';
    };

    window.addItemRow = function() {
      const tbody = document.getElementById('itemsTableBody');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td><input type="text" class="item-codigo" placeholder="Código"></td>
        <td><input type="text" class="item-descricao" placeholder="Descrição"></td>
        <td><input type="number" class="item-quantidade" min="1" value="1"></td>
        <td><input type="text" class="item-unidade" placeholder="Un"></td>
        <td><button onclick="this.parentElement.parentElement.remove()">Remover</button></td>
      `;
      tbody.appendChild(newRow);
    };

    window.createRequest = async function() {
      const button = event.target;
      button.disabled = true;
      button.textContent = 'Processando...';

      try {
        const requestNumber = document.getElementById('requestNumber').value;
        const centroCusto = document.getElementById('centroCusto').value;
        const fornecedor = document.getElementById('fornecedor').value;
        const items = Array.from(document.querySelectorAll('#itemsTableBody tr')).map(row => ({
          codigo: row.querySelector('.item-codigo').value,
          descricao: row.querySelector('.item-descricao').value,
          quantidade: parseInt(row.querySelector('.item-quantidade').value),
          unidade: row.querySelector('.item-unidade').value
        }));

        if (!centroCusto || !fornecedor || items.some(item => !item.codigo || !item.descricao || !item.unidade)) {
          throw new Error('Preencha todos os campos obrigatórios.');
        }

        const maxRetries = 3;
        let retryCount = 0;
        let delay = 1000;

        while (retryCount < maxRetries) {
          try {
            const docRef = await addDoc(collection(db, 'solicitacoesCompra'), {
              numero: requestNumber,
              centroCustoId: centroCusto,
              fornecedorId: fornecedor,
              itens: items,
              status: 'PENDENTE',
              dataCriacao: Timestamp.now(),
              criadoPor: currentUser.nome
            });

            alert('Solicitação criada com sucesso!');
            await loadRequests();
            closeModal('requestModal');
            return;
          } catch (error) {
            if (error.code === 'resource-exhausted') {
              retryCount++;
              if (retryCount >= maxRetries) {
                throw new Error('Limite de requisições excedido. Tente novamente mais tarde.');
              }
              console.warn(`Erro 429 detectado. Tentativa ${retryCount}/${maxRetries} após ${delay}ms`);
              await new Promise(resolve => setTimeout(resolve, delay));
              delay *= 2;
            } else {
              throw error;
            }
          }
        }
      } catch (error) {
        console.error('Erro ao criar solicitação:', error);
        alert(`Erro ao criar solicitação: ${error.message}`);
      } finally {
        button.disabled = false;
        button.textContent = 'Criar Solicitação';
      }
    };

    async function generateQuotationNumber() {
      const date = new Date();
      const yearMonth = date.toISOString().slice(2, 7).replace('-', '');
      const docRef = doc(collection(db, 'cotacoes'));
      const sequence = docRef.id.slice(0, 6);
      const numero = `CT${yearMonth}${sequence}`;
      console.log(`Número da cotação gerado: ${numero}`);
      return numero;
    }

    window.createQuotation = async function(solicitacaoId) {
      const button = document.querySelector(`.create-quotation-button[data-solicitacao-id="${solicitacaoId}"]`);
      if (!button) {
        console.error('Botão não encontrado para solicitação:', solicitacaoId);
        return;
      }
      button.disabled = true;
      button.textContent = 'Processando...';

      try {
        const solicitacao = solicitacoes.find(s => s.id === solicitacaoId);
        if (!solicitacao) {
          throw new Error('Solicitação não encontrada');
        }

        const maxRetries = 3;
        let retryCount = 0;
        let delay = 1000;

        while (retryCount < maxRetries) {
          try {
            const cotacaoData = {
              numero: await generateQuotationNumber(),
              solicitacaoId: solicitacaoId,
              centroCustoId: solicitacao.centroCustoId,
              fornecedorId: solicitacao.fornecedorId,
              itens: solicitacao.itens.map(item => ({
                codigo: item.codigo,
                descricao: item.descricao,
                quantidade: item.quantidade,
                unidade: item.unidade
              })),
              status: 'PENDENTE',
              dataCriacao: Timestamp.now(),
              criadoPor: currentUser.nome
            };

            await addDoc(collection(db, 'cotacoes'), cotacaoData);
            await updateDoc(doc(db, 'solicitacoesCompra', solicitacaoId), {
              status: 'COTACAO',
              geradoPor: currentUser.nome
            });

            alert('Cotação gerada com sucesso!');
            await loadRequests();
            return;
          } catch (error) {
            if (error.code === 'resource-exhausted') {
              retryCount++;
              if (retryCount >= maxRetries) {
                throw new Error('Limite de requisições excedido. Tente novamente mais tarde.');
              }
              console.warn(`Erro 429 detectado. Tentativa ${retryCount}/${maxRetries} após ${delay}ms`);
              await new Promise(resolve => setTimeout(resolve, delay));
              delay *= 2;
            } else {
              throw error;
            }
          }
        }
      } catch (error) {
        console.error('Erro ao gerar cotação:', error);
        alert(`Erro ao gerar cotação: ${error.message}`);
      } finally {
        button.disabled = false;
        button.textContent = 'Gerar Cotação';
      }
    };
  </script>
</body>
</html>