// 🏢 SISTEMA DE LOGO - FYRON MRP

class LogoManager {
    constructor() {
        this.logoContainer = null;
        this.logoInput = null;
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml'];
        this.init();
    }

    init() {
        this.setupElements();
        this.loadSavedLogo();
        this.setupEventListeners();
    }

    // 🔧 CONFIGURAR ELEMENTOS
    setupElements() {
        this.logoContainer = document.getElementById('companyLogo');
        this.logoInput = document.getElementById('logoFileInput');
    }

    // 📂 CARREGAR LOGO SALVO
    loadSavedLogo() {
        const savedLogo = localStorage.getItem('fyronLogo');
        if (savedLogo && this.logoContainer) {
            this.displayLogo(savedLogo);
            console.log('✅ Logo salvo carregado com sucesso!');
        }
    }

    // 🎧 CONFIGURAR EVENT LISTENERS
    setupEventListeners() {
        // Fechar menu ao clicar fora
        document.addEventListener('click', (event) => {
            const menu = document.getElementById('logoMenu');
            const button = document.getElementById('logoConfigBtn');

            if (menu && button && !menu.contains(event.target) && !button.contains(event.target)) {
                menu.style.display = 'none';
            }
        });

        // Drag & Drop
        if (this.logoContainer) {
            this.setupDragAndDrop();
        }
    }

    // 🎯 CONFIGURAR DRAG & DROP
    setupDragAndDrop() {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.logoContainer.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            this.logoContainer.addEventListener(eventName, () => {
                this.logoContainer.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            this.logoContainer.addEventListener(eventName, () => {
                this.logoContainer.classList.remove('drag-over');
            }, false);
        });

        this.logoContainer.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelection(files[0]);
            }
        }, false);
    }

    // 🚫 PREVENIR COMPORTAMENTO PADRÃO
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 📂 TOGGLE MENU DO LOGO
    toggleLogoMenu() {
        const menu = document.getElementById('logoMenu');
        if (menu) {
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }
    }

    // 📁 SELECIONAR LOGO
    selectLogo() {
        if (this.logoInput) {
            this.logoInput.click();
        }
    }

    // 🗑️ RESETAR LOGO
    resetLogo() {
        if (confirm('Deseja remover o logo atual e voltar ao placeholder?')) {
            localStorage.removeItem('fyronLogo');
            this.restoreDefaultLogo();
            this.showNotification('✅ Logo removido com sucesso!', 'success');
            this.toggleLogoMenu();
        }
    }

    // 📥 BAIXAR LOGO
    downloadLogo() {
        const savedLogo = localStorage.getItem('fyronLogo');
        if (savedLogo) {
            const link = document.createElement('a');
            link.href = savedLogo;
            link.download = 'fyron_logo.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            this.showNotification('✅ Logo baixado com sucesso!', 'success');
        } else {
            this.showNotification('❌ Nenhum logo carregado para baixar.', 'error');
        }
        this.toggleLogoMenu();
    }

    // 📁 CARREGAR LOGO SELECIONADO
    loadSelectedLogo(input) {
        if (input.files && input.files[0]) {
            this.handleFileSelection(input.files[0]);
        }
    }

    // 🔍 PROCESSAR ARQUIVO SELECIONADO
    handleFileSelection(file) {
        // Verificar se é uma imagem
        if (!this.allowedTypes.includes(file.type)) {
            this.showNotification('❌ Por favor, selecione um arquivo de imagem válido (PNG, JPG, GIF, SVG)', 'error');
            return;
        }

        // Verificar tamanho do arquivo
        if (file.size > this.maxFileSize) {
            this.showNotification('❌ O arquivo é muito grande. Por favor, selecione uma imagem menor que 5MB.', 'error');
            return;
        }

        const reader = new FileReader();

        reader.onload = (e) => {
            try {
                this.displayLogo(e.target.result);
                localStorage.setItem('fyronLogo', e.target.result);
                this.showNotification('✅ Logo carregado com sucesso!', 'success');
                console.log('✅ Logo FYRON MRP carregado:', file.name);
            } catch (error) {
                console.error('❌ Erro ao processar logo:', error);
                this.showNotification('❌ Erro ao processar a imagem.', 'error');
            }
        };

        reader.onerror = () => {
            this.showNotification('❌ Erro ao carregar a imagem. Tente novamente.', 'error');
        };

        reader.readAsDataURL(file);
    }

    // 🖼️ EXIBIR LOGO
    displayLogo(logoData) {
        if (!this.logoContainer) return;

        // Limpar placeholder
        this.logoContainer.innerHTML = '';
        this.logoContainer.style.background = 'none';
        this.logoContainer.style.boxShadow = 'none';
        this.logoContainer.style.borderRadius = '0';
        this.logoContainer.style.cursor = 'default';
        this.logoContainer.onclick = null;

        // Criar imagem
        const img = document.createElement('img');
        img.src = logoData;
        img.alt = 'FYRON MRP - Sistema de Gestão Empresarial';
        img.style.width = '280px';
        img.style.height = 'auto';
        img.style.maxHeight = '120px';
        img.style.objectFit = 'contain';
        img.className = 'logo';

        // Adicionar efeito de hover
        img.addEventListener('mouseenter', () => {
            img.style.transform = 'scale(1.02)';
            img.style.transition = 'transform 0.3s ease';
        });

        img.addEventListener('mouseleave', () => {
            img.style.transform = 'scale(1)';
        });

        this.logoContainer.appendChild(img);
    }

    // 🔄 RESTAURAR LOGO PADRÃO
    restoreDefaultLogo() {
        if (!this.logoContainer) return;

        this.logoContainer.innerHTML = '';
        this.logoContainer.style.background = 'none';
        this.logoContainer.style.boxShadow = 'none';
        this.logoContainer.style.borderRadius = '0';
        this.logoContainer.style.cursor = 'pointer';
        this.logoContainer.onclick = () => this.selectLogo();

        // Criar logo padrão
        const img = document.createElement('img');
        img.src = 'assets/fyron_logo_compact.svg';
        img.alt = 'FYRON MRP - Sistema de Gestão Empresarial';
        img.style.width = '100%';
        img.style.height = 'auto';
        img.style.maxHeight = '120px';
        img.style.objectFit = 'contain';
        img.className = 'logo';

        this.logoContainer.appendChild(img);
    }

    // 📢 MOSTRAR NOTIFICAÇÃO
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;

        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        notification.style.background = colors[type] || colors.info;
        if (type === 'warning') {
            notification.style.color = '#212529';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }

    // 📊 OBTER INFORMAÇÕES DO LOGO
    getLogoInfo() {
        const savedLogo = localStorage.getItem('fyronLogo');
        if (!savedLogo) return null;

        return {
            hasCustomLogo: true,
            size: new Blob([savedLogo]).size,
            lastModified: localStorage.getItem('logoLastModified') || 'Desconhecido'
        };
    }

    // 🔄 ATUALIZAR TIMESTAMP
    updateLogoTimestamp() {
        localStorage.setItem('logoLastModified', new Date().toISOString());
    }
}

// 🚀 INICIALIZAR SISTEMA DE LOGO
document.addEventListener('DOMContentLoaded', () => {
    const logoManager = new LogoManager();
    window.logoManager = logoManager;
    
    // Funções globais para compatibilidade
    window.toggleLogoMenu = () => logoManager.toggleLogoMenu();
    window.selectLogo = () => logoManager.selectLogo();
    window.resetLogo = () => logoManager.resetLogo();
    window.downloadLogo = () => logoManager.downloadLogo();
    window.loadSelectedLogo = (input) => logoManager.loadSelectedLogo(input);
    window.showNotification = (message, type) => logoManager.showNotification(message, type);
});

// 🎨 CSS ADICIONAL PARA DRAG & DROP
const style = document.createElement('style');
style.textContent = `
    .drag-over {
        border: 2px dashed #007bff !important;
        background: rgba(0, 123, 255, 0.1) !important;
    }
    
    .logo-container {
        transition: all 0.3s ease;
    }
    
    .logo-container:hover {
        transform: scale(1.02);
    }
`;
document.head.appendChild(style);
