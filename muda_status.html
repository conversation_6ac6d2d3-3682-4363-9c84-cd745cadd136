<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Aprovação em Lote de Solicitações de Compras</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
    .container { max-width: 1200px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 30px; }
    h1 { margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
    th { background: #f0f0f0; }
    tr.approved { background: #e8f5e9; }
    .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
    .btn-primary { background: #0854a0; color: #fff; }
    .btn-success { background: #28a745; color: #fff; }
    .btn-secondary { background: #6c757d; color: #fff; }
    .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
    .status-pendente { background: #ffc107; color: #000; }
    .status-aprovada { background: #28a745; color: #fff; }
    .status-em-cotacao { background: #17a2b8; color: #fff; }
    .status-finalizada { background: #6c757d; color: #fff; }
    .actions { display: flex; gap: 10px; }
    select { padding: 6px; border-radius: 4px; border: 1px solid #ddd; }
    .status-select { min-width: 120px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Gerenciamento de Solicitações de Compras</h1>
    <button class="btn btn-primary" onclick="loadSolicitacoes()">Recarregar Solicitações</button>
    <form id="batchForm">
      <table>
        <thead>
          <tr>
            <th><input type="checkbox" id="selectAll" onclick="toggleAll(this)"></th>
            <th>Número</th>
            <th>Solicitante</th>
            <th>Departamento</th>
            <th>Status Atual</th>
            <th>Novo Status</th>
            <th>Data</th>
          </tr>
        </thead>
        <tbody id="solicitacoesTableBody">
          <tr><td colspan="7" style="text-align:center; color:#888;">Carregando...</td></tr>
        </tbody>
      </table>
      <div class="actions">
        <button type="button" class="btn btn-success" onclick="salvarAlteracoes()">Salvar Alterações</button>
        <button type="button" class="btn btn-primary" onclick="gerarCotacoesSelecionadas()">Gerar Cotações</button>
      </div>
    </form>
  </div>
  <script type="module">
    import { db } from './firebase-config.js';
    import { collection, getDocs, updateDoc, doc, Timestamp, addDoc, getDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let solicitacoes = [];
    let cotacoes = [];

    async function loadSolicitacoes() {
      const tbody = document.getElementById('solicitacoesTableBody');
      tbody.innerHTML = '<tr><td colspan="7" style="text-align:center; color:#888;">Carregando...</td></tr>';
      const solicitacoesSnap = await getDocs(collection(db, 'solicitacoesCompra'));
      solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      cotacoes = (await getDocs(collection(db, 'cotacoes'))).docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      tbody.innerHTML = '';
      if (solicitacoes.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align:center; color:#888;">Nenhuma solicitação encontrada.</td></tr>';
        return;
      }

      for (const s of solicitacoes) {
        const data = s.dataCriacao ? new Date(s.dataCriacao.seconds * 1000).toLocaleDateString() : '-';
        tbody.innerHTML += `
          <tr data-id="${s.id}">
            <td><input type="checkbox" class="selectSolicitacao" value="${s.id}"></td>
            <td>${s.numero || '-'}</td>
            <td>${s.solicitante || '-'}</td>
            <td>${s.departamento || '-'}</td>
            <td><span class="status-badge status-${s.status?.toLowerCase() || 'pendente'}">${s.status || 'PENDENTE'}</span></td>
            <td>
              <select class="status-select" data-id="${s.id}">
                <option value="PENDENTE" ${s.status === 'PENDENTE' ? 'selected' : ''}>Pendente</option>
                <option value="APROVADA" ${s.status === 'APROVADA' ? 'selected' : ''}>Aprovada</option>
                <option value="EM COTAÇÃO" ${s.status === 'EM COTAÇÃO' ? 'selected' : ''}>Em Cotação</option>
                <option value="FINALIZADA" ${s.status === 'FINALIZADA' ? 'selected' : ''}>Finalizada</option>
              </select>
            </td>
            <td>${data}</td>
          </tr>
        `;
      }
    }

    window.loadSolicitacoes = loadSolicitacoes;
    window.toggleAll = function(checkbox) {
      document.querySelectorAll('.selectSolicitacao').forEach(cb => cb.checked = checkbox.checked);
    };

    window.salvarAlteracoes = async function() {
      const selecionadas = Array.from(document.querySelectorAll('.selectSolicitacao:checked')).map(cb => cb.value);
      if (selecionadas.length === 0) {
        showToast('Selecione pelo menos uma solicitação.', 'warning');
        return;
      }

      for (const id of selecionadas) {
        const select = document.querySelector(`.status-select[data-id="${id}"]`);
        const novoStatus = select.value;
        const ref = doc(db, 'solicitacoesCompra', id);
        await updateDoc(ref, {
          status: novoStatus,
          ultimaAtualizacao: Timestamp.now()
        });
      }
      showToast('Alterações salvas com sucesso!', 'success');
      loadSolicitacoes();
    }

    window.gerarCotacoesSelecionadas = async function() {
      const selecionadas = Array.from(document.querySelectorAll('.selectSolicitacao:checked')).map(cb => cb.value);
      if (selecionadas.length === 0) {
        showToast('Selecione pelo menos uma solicitação.', 'warning');
        return;
      }

      for (const id of selecionadas) {
        const solicitacao = solicitacoes.find(s => s.id === id);
        if (!solicitacao) continue;

        // Verifica se já existe cotação
        const jaTemCotacao = cotacoes.some(c => c.solicitacaoId === id);
        if (jaTemCotacao) continue;

        // Gerar número da cotação
        const counterRef = doc(db, "contadores", "cotacoes");
        const counterDoc = await getDoc(counterRef);
        let nextNumber = 1;
        if (counterDoc.exists()) {
          nextNumber = counterDoc.data().valor + 1;
          await updateDoc(counterRef, { valor: nextNumber });
        } else {
          await setDoc(counterRef, { valor: nextNumber });
        }
        const numeroCotacao = `CT${nextNumber.toString().padStart(6, '0')}`;

        // Criar cotação
        // Transferir datas da solicitação para a cotação
        let dataLimite = null;
        if (solicitacao.dataLimiteAprovacao) {
          dataLimite = solicitacao.dataLimiteAprovacao;
        } else if (solicitacao.dataNecessidade) {
          dataLimite = solicitacao.dataNecessidade;
        }

        const cotacaoData = {
          numero: numeroCotacao,
          solicitacaoId: id,
          centroCustoId: solicitacao.centroCustoId || '',
          dataLimite: dataLimite,
          fornecedores: [],
          itens: solicitacao.itens.map(item => ({
            produtoId: item.produtoId || '',
            codigo: item.codigo || '',
            descricao: item.descricao || '',
            quantidade: item.quantidadeCompra || item.quantidadeInterna || 0,
            unidade: item.unidadeCompra || item.unidadeInterna || 'UN'
          })),
          status: 'ABERTA',
          dataCriacao: Timestamp.now(),
          criadoPor: solicitacao.solicitante || 'Sistema'
        };
        await addDoc(collection(db, 'cotacoes'), cotacaoData);
        await updateDoc(doc(db, 'solicitacoesCompra', id), {
          status: 'EM_COTACAO',
          cotacaoId: numeroCotacao,
          ultimaAtualizacao: Timestamp.now()
        });
      }
      showToast('Cotações geradas com sucesso!', 'success');
      loadSolicitacoes();
    }

    function showToast(message, type = 'success') {
      Toastify({
        text: message,
        duration: 3000,
        close: true,
        gravity: 'top',
        position: 'right',
        backgroundColor: type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#dc3545',
        stopOnFocus: true
      }).showToast();
    }

    // Carregar solicitações ao abrir a página
    loadSolicitacoes();
  </script>
</body>
</html> 