/**
 * SCHEDULER - DESABILITADO
 * Agendador simplificado sem queries complexas
 */

export class Scheduler {
  static async startScheduledTasks() {
    try {
      console.log('📅 Agendador de tarefas desabilitado temporariamente');
      return true;
    } catch (error) {
      console.error('Erro no agendador:', error);
      return false;
    }
  }

  static async runTasks() {
    console.log('📋 Tarefas agendadas desabilitadas');
    return { success: true, message: 'Tarefas desabilitadas' };
  }

  static async checkCriticalAlerts() {
    console.log('🚨 Verificação de alertas desabilitada');
    return [];
  }
}