<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Histórico de Transferências - FYRON MRP</title>
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0854a0;
            --primary-hover: #0a4d8c;
            --secondary-color: #f8f9fa;
            --text-color: #212529;
            --text-muted: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --border-color: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--secondary-color);
            color: var(--text-color);
            line-height: 1.5;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .filters-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
        }

        .filters-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-col {
            display: flex;
            flex-direction: column;
        }

        .form-col label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .form-col input,
        .form-col select {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-col input:focus,
        .form-col select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(8, 84, 160, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .items-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
            vertical-align: middle;
        }

        .items-table tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completa {
            background: #d4edda;
            color: #155724;
        }

        .status-parcial {
            background: #fff3cd;
            color: #856404;
        }

        .status-pendente {
            background: #f8d7da;
            color: #721c24;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 20px 0;
        }

        .pagination button {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .results-info {
            color: var(--text-muted);
            font-weight: 500;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-muted);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-history"></i> Histórico de Transferências</h1>
            <p>Consulte e gerencie o histórico completo de movimentações entre armazéns</p>
        </div>

        <div class="content">
            <!-- Filtros de Busca -->
            <div class="filters-container">
                <div class="filters-title">
                    <i class="fas fa-filter"></i>
                    Filtros de Busca
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <label>Buscar Produto:</label>
                        <input type="text" id="searchProduct" placeholder="Digite código ou descrição do produto..." onchange="searchHistory()">
                    </div>
                    <div class="form-col">
                        <label>Período:</label>
                        <select id="periodFilter" onchange="searchHistory()">
                            <option value="7">Últimos 7 dias</option>
                            <option value="15">Últimos 15 dias</option>
                            <option value="30">Últimos 30 dias</option>
                            <option value="90">Últimos 3 meses</option>
                            <option value="365">Último ano</option>
                            <option value="">Todos os períodos</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label>Tipo de Transferência:</label>
                        <select id="typeFilter" onchange="searchHistory()">
                            <option value="">Todos os tipos</option>
                            <option value="OP">Via Ordem de Produção</option>
                            <option value="LIVRE">Transferência Livre</option>
                            <option value="RETORNO_SOBRAS">Retorno de Sobras</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label>Armazém:</label>
                        <select id="warehouseFilter" onchange="searchHistory()">
                            <option value="">Todos os armazéns</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <button type="button" class="btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-eraser"></i> Limpar Filtros
                        </button>
                    </div>
                    <div class="form-col" style="text-align: right;">
                        <span id="resultsCount" class="results-info">Use os filtros para buscar</span>
                    </div>
                </div>
            </div>

            <!-- Tabela de Resultados -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Data/Hora</th>
                        <th>OP</th>
                        <th>Produto</th>
                        <th>Qtd Solicitada</th>
                        <th>Qtd Transferida</th>
                        <th>Status</th>
                        <th>Origem</th>
                        <th>Destino</th>
                        <th>Funcionário</th>
                        <th>Motivo</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <tr>
                        <td colspan="11" class="empty-state">
                            <i class="fas fa-search"></i>
                            <div><strong>Use os filtros acima para buscar transferências</strong></div>
                            <div style="font-size: 14px; margin-top: 10px;">Exemplo: últimos 7 dias, produto específico, etc.</div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Paginação -->
            <div class="pagination">
                <div class="results-info">
                    <span id="historyPageInfo"></span>
                </div>
                <div>
                    <button id="prevPageBtn" onclick="previousPage()" disabled>
                        <i class="fas fa-chevron-left"></i> Anterior
                    </button>
                    <button id="nextPageBtn" onclick="nextPage()" disabled>
                        Próxima <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { db } from './firebase-config.js';
        import { collection, query, where, orderBy, limit, getDocs, doc, getDoc, updateDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        console.log('🔥 Firebase inicializado via firebase-config.js');

        // Variáveis globais
        let produtos = [];
        let armazens = [];
        let filteredTransfers = [];
        let historyPage = 1;
        const historyPageSize = 20;
        let isSearching = false;

        // Inicializar página
        document.addEventListener('DOMContentLoaded', async function() {
            await loadInitialData();
            populateWarehouseFilter();
        });

        // Carregar dados iniciais
        async function loadInitialData() {
            try {
                // Carregar produtos
                const produtosSnap = await getDocs(collection(db, "produtos"));
                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Carregar armazéns
                const armazensSnap = await getDocs(collection(db, "armazens"));
                armazens = armazensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                console.log('✅ Dados iniciais carregados:', { produtos: produtos.length, armazens: armazens.length });
            } catch (error) {
                console.error('❌ Erro ao carregar dados iniciais:', error);
            }
        }

        // Popular filtro de armazéns
        function populateWarehouseFilter() {
            const warehouseFilter = document.getElementById('warehouseFilter');
            if (!warehouseFilter) return;

            warehouseFilter.innerHTML = '<option value="">Todos os armazéns</option>';
            armazens.forEach(armazem => {
                warehouseFilter.innerHTML += `<option value="${armazem.id}">${armazem.codigo} - ${armazem.nome}</option>`;
            });
        }

        // Buscar histórico
        window.searchHistory = async function() {
            if (isSearching) return;
            isSearching = true;

            const searchProduct = document.getElementById('searchProduct')?.value.toLowerCase().trim() || '';
            const periodFilter = document.getElementById('periodFilter')?.value || '7';
            const typeFilter = document.getElementById('typeFilter')?.value || '';
            const warehouseFilter = document.getElementById('warehouseFilter')?.value || '';

            try {
                showLoading();

                // Construir query base
                let transfersQuery = collection(db, "transferenciasArmazem");
                
                // Aplicar filtro de período se especificado
                if (periodFilter) {
                    const daysAgo = parseInt(periodFilter);
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - daysAgo);
                    
                    transfersQuery = query(
                        transfersQuery,
                        where("dataHora", ">=", Timestamp.fromDate(startDate)),
                        orderBy("dataHora", "desc"),
                        limit(1000)
                    );
                } else {
                    transfersQuery = query(
                        transfersQuery,
                        orderBy("dataHora", "desc"),
                        limit(500)
                    );
                }

                const transfersSnap = await getDocs(transfersQuery);
                let transfers = transfersSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                // Aplicar filtros adicionais
                if (searchProduct) {
                    transfers = transfers.filter(transfer => {
                        const produto = produtos.find(p => p.id === transfer.produtoId);
                        if (!produto) return false;
                        
                        const searchText = `${produto.codigo} ${produto.descricao}`.toLowerCase();
                        return searchText.includes(searchProduct);
                    });
                }

                if (typeFilter) {
                    transfers = transfers.filter(transfer => transfer.tipo === typeFilter);
                }

                if (warehouseFilter) {
                    transfers = transfers.filter(transfer =>
                        transfer.armazemOrigemId === warehouseFilter ||
                        transfer.armazemDestinoId === warehouseFilter
                    );
                }

                filteredTransfers = transfers;
                historyPage = 1;
                renderHistoryPage();

            } catch (error) {
                console.error("❌ Erro ao buscar histórico:", error);
                showError();
            } finally {
                isSearching = false;
            }
        };

        // Limpar filtros
        window.clearFilters = function() {
            document.getElementById('searchProduct').value = '';
            document.getElementById('periodFilter').value = '7';
            document.getElementById('typeFilter').value = '';
            document.getElementById('warehouseFilter').value = '';
            
            showEmptyState();
        };

        // Renderizar página do histórico
        function renderHistoryPage() {
            const tableBody = document.getElementById('historyTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            const startIndex = (historyPage - 1) * historyPageSize;
            const endIndex = startIndex + historyPageSize;
            const pageTransfers = filteredTransfers.slice(startIndex, endIndex);

            if (pageTransfers.length === 0) {
                showEmptyState();
                return;
            }

            pageTransfers.forEach(transfer => {
                const produto = produtos.find(p => p.id === transfer.produtoId);
                const armazemOrigem = armazens.find(a => a.id === transfer.armazemOrigemId);
                const armazemDestino = armazens.find(a => a.id === transfer.armazemDestinoId);

                const dataFormatada = transfer.dataHora?.toDate?.() ? 
                    transfer.dataHora.toDate().toLocaleString('pt-BR') : 
                    new Date(transfer.dataHora?.seconds * 1000).toLocaleString('pt-BR');

                const statusClass = transfer.statusTransferencia === 'COMPLETA' ? 'status-completa' :
                                  transfer.statusTransferencia === 'PARCIAL' ? 'status-parcial' : 'status-pendente';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${dataFormatada}</td>
                    <td>${transfer.numeroOP || 'N/A'}</td>
                    <td title="${produto?.descricao || 'N/A'}">${produto?.codigo || 'N/A'}</td>
                    <td>${transfer.quantidadeSolicitada?.toFixed(3) || '0.000'}</td>
                    <td>${transfer.quantidade?.toFixed(3) || '0.000'}</td>
                    <td><span class="status-badge ${statusClass}">${transfer.statusTransferencia || 'PENDENTE'}</span></td>
                    <td>${armazemOrigem?.codigo || 'N/A'}</td>
                    <td>${armazemDestino?.codigo || 'N/A'}</td>
                    <td>${transfer.funcionarioResponsavel || 'N/A'}</td>
                    <td title="${transfer.observacoesTransferencia || ''}">${transfer.motivo || 'N/A'}</td>
                    <td>
                        <button class="btn-secondary" onclick="verDetalhes('${transfer.id}')" title="Ver detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            updatePagination();
        }

        // Atualizar paginação
        function updatePagination() {
            const totalPages = Math.ceil(filteredTransfers.length / historyPageSize);
            const pageInfo = document.getElementById('historyPageInfo');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const resultsCount = document.getElementById('resultsCount');

            if (pageInfo) {
                pageInfo.textContent = filteredTransfers.length > 0 ? `Página ${historyPage} de ${totalPages}` : '';
            }

            if (resultsCount) {
                resultsCount.textContent = `${filteredTransfers.length} transferência(s) encontrada(s)`;
            }

            if (prevBtn) {
                prevBtn.disabled = historyPage <= 1;
            }

            if (nextBtn) {
                nextBtn.disabled = historyPage >= totalPages;
            }
        }

        // Navegação de páginas
        window.previousPage = function() {
            if (historyPage > 1) {
                historyPage--;
                renderHistoryPage();
            }
        };

        window.nextPage = function() {
            const totalPages = Math.ceil(filteredTransfers.length / historyPageSize);
            if (historyPage < totalPages) {
                historyPage++;
                renderHistoryPage();
            }
        };

        // Ver detalhes da transferência
        window.verDetalhes = function(transferId) {
            const transfer = filteredTransfers.find(t => t.id === transferId);
            if (!transfer) return;

            const produto = produtos.find(p => p.id === transfer.produtoId);
            const armazemOrigem = armazens.find(a => a.id === transfer.armazemOrigemId);
            const armazemDestino = armazens.find(a => a.id === transfer.armazemDestinoId);

            const detalhes = `
                📋 DETALHES DA TRANSFERÊNCIA
                
                🆔 ID: ${transfer.id}
                📅 Data/Hora: ${transfer.dataHora?.toDate?.()?.toLocaleString('pt-BR') || 'N/A'}
                🏭 OP: ${transfer.numeroOP || 'N/A'}
                📦 Produto: ${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}
                📊 Quantidade Solicitada: ${transfer.quantidadeSolicitada?.toFixed(3) || '0.000'}
                ✅ Quantidade Transferida: ${transfer.quantidade?.toFixed(3) || '0.000'}
                🔄 Status: ${transfer.statusTransferencia || 'PENDENTE'}
                📤 Origem: ${armazemOrigem?.codigo || 'N/A'} - ${armazemOrigem?.nome || 'N/A'}
                📥 Destino: ${armazemDestino?.codigo || 'N/A'} - ${armazemDestino?.nome || 'N/A'}
                👤 Funcionário: ${transfer.funcionarioResponsavel || 'N/A'}
                📝 Motivo: ${transfer.motivo || 'N/A'}
                💬 Observações: ${transfer.observacoesTransferencia || 'N/A'}
            `;

            alert(detalhes);
        };

        // Estados da interface
        function showLoading() {
            const tableBody = document.getElementById('historyTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="11" class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <div>Carregando transferências...</div>
                        </td>
                    </tr>
                `;
            }
        }

        function showEmptyState() {
            const tableBody = document.getElementById('historyTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="11" class="empty-state">
                            <i class="fas fa-search"></i>
                            <div><strong>Nenhuma transferência encontrada</strong></div>
                            <div style="font-size: 14px; margin-top: 10px;">Tente ajustar os filtros de busca</div>
                        </td>
                    </tr>
                `;
            }

            document.getElementById('resultsCount').textContent = 'Nenhum resultado encontrado';
            document.getElementById('historyPageInfo').textContent = '';
            document.getElementById('prevPageBtn').disabled = true;
            document.getElementById('nextPageBtn').disabled = true;
        }

        function showError() {
            const tableBody = document.getElementById('historyTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="11" style="text-align: center; color: var(--danger-color); padding: 40px;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <div><strong>Erro ao carregar transferências</strong></div>
                            <div style="font-size: 14px; margin-top: 10px;">Tente novamente em alguns instantes</div>
                        </td>
                    </tr>
                `;
            }
        }

        // Expor funções globalmente
        window.db = db;
        window.produtos = produtos;
        window.armazens = armazens;
    </script>
</body>
</html>
