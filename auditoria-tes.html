<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Auditoria TES</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .filters { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { font-weight: bold; margin-bottom: 5px; }
        .form-control { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .table th, .table td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        .table th { background: #34495e; color: white; }
        .alert { padding: 15px; border-radius: 4px; margin: 10px 0; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Auditoria TES - Tipos de Entrada e Saída</h1>
            <p>Sistema de auditoria e validação de TES nas movimentações de estoque</p>
        </div>

        <!-- Estatísticas -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMovimentos">-</div>
                <div class="stat-label">Total Movimentos</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                <div class="stat-number" id="semTES">-</div>
                <div class="stat-label">Sem TES</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                <div class="stat-number" id="tesInvalidas">-</div>
                <div class="stat-label">TES Inválidas</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954);">
                <div class="stat-number" id="conformidade">-</div>
                <div class="stat-label">% Conformidade</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <div class="form-group">
                <label>Data Início:</label>
                <input type="date" id="startDate" class="form-control">
            </div>
            <div class="form-group">
                <label>Data Fim:</label>
                <input type="date" id="endDate" class="form-control">
            </div>
            <div class="form-group">
                <label>TES:</label>
                <select id="tesFilter" class="form-control">
                    <option value="">Todas</option>
                    <option value="001">001 - Compra para estoque</option>
                    <option value="500">500 - Venda de produto</option>
                    <option value="200">200 - Entrada por produção</option>
                    <option value="400">400 - Requisição para consumo</option>
                    <option value="900">900 - Ajuste de inventário</option>
                </select>
            </div>
            <div class="form-group">
                <label>Usuário:</label>
                <input type="text" id="userFilter" class="form-control" placeholder="Nome do usuário">
            </div>
            <div class="form-group">
                <label>&nbsp;</label>
                <button class="btn btn-primary" onclick="loadAuditData()">🔍 Buscar</button>
            </div>
            <div class="form-group">
                <label>&nbsp;</label>
                <button class="btn btn-success" onclick="exportReport()">📊 Exportar</button>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-danger" onclick="validateConsistency()">⚠️ Validar Consistência</button>
            <button class="btn btn-success" onclick="fixMissingTES()">🔧 Corrigir TES Faltantes</button>
        </div>

        <!-- Resultados -->
        <div id="results">
            <div class="alert alert-warning">
                <strong>ℹ️ Instruções:</strong>
                <ul>
                    <li>Use os filtros para buscar movimentações específicas</li>
                    <li>Clique em "Validar Consistência" para verificar problemas</li>
                    <li>Use "Corrigir TES Faltantes" para aplicar TES automaticamente</li>
                </ul>
            </div>
        </div>

        <!-- Tabela de Auditoria -->
        <table class="table" id="auditTable" style="display: none;">
            <thead>
                <tr>
                    <th>Data/Hora</th>
                    <th>Produto</th>
                    <th>Tipo</th>
                    <th>Quantidade</th>
                    <th>TES</th>
                    <th>Documento</th>
                    <th>Usuário</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="auditTableBody"></tbody>
        </table>
    </div>

    <script type="module">
        import { db } from './firebase-config.js';
        import { 
            collection, 
            getDocs, 
            query, 
            where, 
            orderBy,
            updateDoc,
            doc,
            Timestamp 
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        let allMovements = [];
        let produtos = [];

        // Carregar dados iniciais
        document.addEventListener('DOMContentLoaded', async () => {
            await loadInitialData();
            updateStats();
        });

        async function loadInitialData() {
            try {
                const [movSnapshot, prodSnapshot] = await Promise.all([
                    getDocs(collection(db, "movimentacoesEstoque")),
                    getDocs(collection(db, "produtos"))
                ]);

                allMovements = movSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                produtos = prodSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
            }
        }

        function updateStats() {
            const total = allMovements.length;
            const semTES = allMovements.filter(m => !m.tes).length;
            const tesValidas = ['001', '002', '003', '500', '501', '502', '200', '201', '202', '400', '401', '402', '900', '901', '902', '100', '101', '102'];
            const tesInvalidas = allMovements.filter(m => m.tes && !tesValidas.includes(m.tes)).length;
            const conformidade = total > 0 ? Math.round(((total - semTES - tesInvalidas) / total) * 100) : 0;

            document.getElementById('totalMovimentos').textContent = total;
            document.getElementById('semTES').textContent = semTES;
            document.getElementById('tesInvalidas').textContent = tesInvalidas;
            document.getElementById('conformidade').textContent = conformidade + '%';
        }

        window.loadAuditData = async function() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const tesFilter = document.getElementById('tesFilter').value;
            const userFilter = document.getElementById('userFilter').value;

            let filteredMovements = [...allMovements];

            // Aplicar filtros
            if (startDate) {
                const start = new Date(startDate);
                filteredMovements = filteredMovements.filter(m => {
                    const movDate = m.dataHora?.toDate ? m.dataHora.toDate() : new Date(m.dataHora?.seconds * 1000);
                    return movDate >= start;
                });
            }

            if (endDate) {
                const end = new Date(endDate);
                end.setHours(23, 59, 59);
                filteredMovements = filteredMovements.filter(m => {
                    const movDate = m.dataHora?.toDate ? m.dataHora.toDate() : new Date(m.dataHora?.seconds * 1000);
                    return movDate <= end;
                });
            }

            if (tesFilter) {
                filteredMovements = filteredMovements.filter(m => m.tes === tesFilter);
            }

            if (userFilter) {
                filteredMovements = filteredMovements.filter(m => 
                    m.usuario?.toLowerCase().includes(userFilter.toLowerCase())
                );
            }

            renderAuditTable(filteredMovements);
        };

        function renderAuditTable(movements) {
            const table = document.getElementById('auditTable');
            const tbody = document.getElementById('auditTableBody');
            
            tbody.innerHTML = '';

            if (movements.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Nenhuma movimentação encontrada</td></tr>';
                table.style.display = 'table';
                return;
            }

            movements.forEach(mov => {
                const produto = produtos.find(p => p.id === mov.produtoId);
                const dataHora = mov.dataHora?.toDate ? 
                    mov.dataHora.toDate().toLocaleString('pt-BR') : 
                    new Date(mov.dataHora?.seconds * 1000).toLocaleString('pt-BR');

                const status = !mov.tes ? 
                    '<span style="color: red;">❌ Sem TES</span>' : 
                    '<span style="color: green;">✅ OK</span>';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${dataHora}</td>
                    <td>${produto?.codigo || 'N/A'} - ${produto?.descricao || 'N/A'}</td>
                    <td>${mov.tipo}</td>
                    <td>${mov.quantidade}</td>
                    <td>${mov.tes || '<span style="color: red;">FALTANTE</span>'}</td>
                    <td>${mov.tipoDocumento || 'N/A'} ${mov.numeroDocumento || ''}</td>
                    <td>${mov.usuario || 'N/A'}</td>
                    <td>${status}</td>
                `;
                tbody.appendChild(row);
            });

            table.style.display = 'table';
        }

        window.validateConsistency = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="alert alert-warning">🔍 Validando consistência...</div>';

            const semTES = allMovements.filter(m => !m.tes);
            const tesValidas = ['001', '002', '003', '500', '501', '502', '200', '201', '202', '400', '401', '402', '900', '901', '902', '100', '101', '102'];
            const tesInvalidas = allMovements.filter(m => m.tes && !tesValidas.includes(m.tes));

            let html = '<h3>📋 Resultado da Validação:</h3>';

            if (semTES.length > 0) {
                html += `<div class="alert alert-danger">
                    <strong>❌ ${semTES.length} movimentações sem TES encontradas</strong>
                    <ul>`;
                semTES.slice(0, 5).forEach(mov => {
                    const produto = produtos.find(p => p.id === mov.produtoId);
                    html += `<li>${produto?.codigo || 'N/A'} - ${mov.tipo} - ${mov.quantidade}</li>`;
                });
                if (semTES.length > 5) html += `<li>... e mais ${semTES.length - 5} registros</li>`;
                html += '</ul></div>';
            }

            if (tesInvalidas.length > 0) {
                html += `<div class="alert alert-warning">
                    <strong>⚠️ ${tesInvalidas.length} TES inválidas encontradas</strong>
                </div>`;
            }

            if (semTES.length === 0 && tesInvalidas.length === 0) {
                html += '<div class="alert alert-success"><strong>✅ Todas as movimentações estão em conformidade!</strong></div>';
            }

            results.innerHTML = html;
        };

        window.fixMissingTES = async function() {
            const semTES = allMovements.filter(m => !m.tes);
            
            if (semTES.length === 0) {
                alert('✅ Não há movimentações sem TES para corrigir!');
                return;
            }

            if (!confirm(`Deseja corrigir ${semTES.length} movimentações sem TES?\nSerão aplicadas TES padrão baseadas no tipo de operação.`)) {
                return;
            }

            const results = document.getElementById('results');
            results.innerHTML = '<div class="alert alert-warning">🔧 Corrigindo TES faltantes...</div>';

            let corrected = 0;
            for (const mov of semTES) {
                try {
                    // Determinar TES baseada no tipo
                    let tes = '001'; // Padrão
                    if (mov.tipoDocumento === 'VENDA') tes = '500';
                    else if (mov.tipoDocumento === 'PRODUCAO') tes = mov.tipo === 'ENTRADA' ? '200' : '201';
                    else if (mov.tipoDocumento === 'TRANSFERENCIA') tes = '100';
                    else if (mov.tipoDocumento === 'AJUSTE') tes = mov.tipo === 'ENTRADA' ? '900' : '901';

                    await updateDoc(doc(db, "movimentacoesEstoque", mov.id), {
                        tes: tes,
                        tesCorrigida: true,
                        dataCorrecao: Timestamp.now()
                    });

                    corrected++;
                } catch (error) {
                    console.error('Erro ao corrigir:', error);
                }
            }

            results.innerHTML = `<div class="alert alert-success">
                <strong>✅ ${corrected} movimentações corrigidas com sucesso!</strong>
            </div>`;

            // Recarregar dados
            await loadInitialData();
            updateStats();
        };

        window.exportReport = function() {
            const data = allMovements.map(mov => {
                const produto = produtos.find(p => p.id === mov.produtoId);
                return {
                    'Data': mov.dataHora?.toDate ? mov.dataHora.toDate().toLocaleDateString('pt-BR') : 'N/A',
                    'Produto': produto?.codigo || 'N/A',
                    'Tipo': mov.tipo,
                    'Quantidade': mov.quantidade,
                    'TES': mov.tes || 'FALTANTE',
                    'Documento': mov.tipoDocumento || 'N/A',
                    'Usuário': mov.usuario || 'N/A'
                };
            });

            const csv = [
                Object.keys(data[0]).join(','),
                ...data.map(row => Object.values(row).join(','))
            ].join('\n');

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `auditoria-tes-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
        };
    </script>
</body>
</html>