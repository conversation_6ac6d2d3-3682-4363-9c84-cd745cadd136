<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Relatório de Caminho Crítico e Gargalo</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
    .container { max-width: 900px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 24px; }
    h1 { color: #0854a0; }
    .input-row { display: flex; gap: 10px; margin-bottom: 20px; }
    input[type=text] { flex: 1; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    button { padding: 8px 18px; background: #0854a0; color: #fff; border: none; border-radius: 4px; font-weight: bold; cursor: pointer; }
    button:hover { background: #0a4d8c; }
    .tree { margin-top: 30px; }
    .bom-node { margin-left: 24px; border-left: 2px solid #eee; padding-left: 12px; margin-bottom: 10px; }
    .op-status { font-size: 12px; padding: 2px 6px; border-radius: 4px; margin-left: 8px; }
    .status-concluida { background: #107e3e; color: #fff; }
    .status-em-producao { background: #2196F3; color: #fff; }
    .status-pendente { background: #FFC107; color: #000; }
    .critico { font-weight: bold; color: #bb0000; }
    .gargalo { background: #e9730c; color: #fff; font-weight: bold; padding: 2px 6px; border-radius: 4px; margin-left: 8px; }
    .op-table { border-collapse: collapse; width: 100%; margin-top: 8px; }
    .op-table th, .op-table td { border: 1px solid #ccc; padding: 4px 8px; font-size: 13px; }
    .op-table th { background: #f0f0f0; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Relatório de Caminho Crítico e Gargalo</h1>
    <div class="input-row">
      <input type="text" id="codigoProduto" placeholder="Digite o código do produto final...">
      <button onclick="gerarRelatorio()">Gerar Relatório</button>
    </div>
    <div id="relatorio"></div>
  </div>
  <script type="module">
    import { db } from '/js/firebase-config.js';
    import { getDocs, collection } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    async function gerarRelatorio() {
      const codigo = document.getElementById('codigoProduto').value.trim();
      const relatorioDiv = document.getElementById('relatorio');
      relatorioDiv.innerHTML = 'Buscando dados...';
      if (!codigo) {
        relatorioDiv.innerHTML = '<span style="color:red">Digite o código do produto.</span>';
        return;
      }
      // Carregar dados
      const [produtosSnap, estruturasSnap, opsSnap] = await Promise.all([
        getDocs(collection(db, 'produtos')),
        getDocs(collection(db, 'estruturas')),
        getDocs(collection(db, 'ordensProducao'))
      ]);
      const produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const ops = opsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const produtoRaiz = produtos.find(p => p.codigo === codigo);
      if (!produtoRaiz) {
        relatorioDiv.innerHTML = '<span style="color:red">Produto não encontrado.</span>';
        return;
      }
      // Montar árvore BOM e calcular caminho crítico
      let caminhoCritico = { tempo: 0, caminho: [] };
      let gargalo = { tempo: 0, label: '' };
      const somaOperacoes = {};
      function montarBOM(produtoId, nivel = 0) {
        const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
        const op = ops.find(o => o.produtoId === produtoId);
        let tempoTotal = 0;
        let caminhoLocal = [];
        let html = '';
        if (estrutura && estrutura.componentes.length > 0) {
          html += `<div class='bom-node'><strong>${produtos.find(p=>p.id===produtoId)?.codigo || produtoId}</strong> - ${produtos.find(p=>p.id===produtoId)?.descricao || ''}`;
          if (op) html += statusBadge(op.status);
          html += `<div style='margin-top:6px;'>`;
          for (const comp of estrutura.componentes) {
            const res = montarBOM(comp.componentId, nivel+1);
            html += res.html;
            if (res.tempoTotal > tempoTotal) {
              tempoTotal = res.tempoTotal;
              caminhoLocal = res.caminhoLocal;
            }
          }
          html += `</div></div>`;
        } else {
          // Folha: calcular tempo das operações
          let tempoFolha = 0;
          let opHtml = '';
          const estruturaFolha = estruturas.find(e => e.produtoPaiId === produtoId);
          if (estruturaFolha && estruturaFolha.operacoes && estruturaFolha.operacoes.length > 0) {
            opHtml += `<table class='op-table'><thead><tr><th>Seq</th><th>Operação</th><th>Tempo (min)</th><th>Status</th></tr></thead><tbody>`;
            for (const oper of estruturaFolha.operacoes) {
              tempoFolha += Number(oper.tempo || 0);
              const opStatus = op ? op.status : 'Pendente';
              opHtml += `<tr><td>${oper.sequencia}</td><td>${oper.operacao || ''}</td><td>${oper.tempo || 0}</td><td>${statusBadge(opStatus)}</td></tr>`;
              if (Number(oper.tempo || 0) > gargalo.tempo) {
                gargalo = { tempo: Number(oper.tempo), label: `${produtos.find(p=>p.id===produtoId)?.codigo || produtoId} - ${oper.operacao}` };
              }
              // Somar tempo por tipo de operação
              const tipo = oper.operacao || 'Outro';
              somaOperacoes[tipo] = (somaOperacoes[tipo] || 0) + Number(oper.tempo || 0);
            }
            opHtml += `</tbody></table>`;
          }
          html += `<div class='bom-node'><strong>${produtos.find(p=>p.id===produtoId)?.codigo || produtoId}</strong> - ${produtos.find(p=>p.id===produtoId)?.descricao || ''}`;
          if (op) html += statusBadge(op.status);
          if (opHtml) html += opHtml;
          html += `</div>`;
          tempoTotal = tempoFolha;
          caminhoLocal = [{ produtoId, tempo: tempoFolha }];
        }
        // Atualizar caminho crítico global
        if (tempoTotal > caminhoCritico.tempo) {
          caminhoCritico = { tempo: tempoTotal, caminho: [...caminhoLocal, { produtoId, tempo: tempoTotal }] };
        }
        return { html, tempoTotal, caminhoLocal: [...caminhoLocal, { produtoId, tempo: tempoTotal }] };
      }
      function statusBadge(status) {
        if (!status) return '';
        if (status === 'Concluída') return `<span class='op-status status-concluida'>Concluída</span>`;
        if (status === 'Em Produção') return `<span class='op-status status-em-producao'>Em Produção</span>`;
        return `<span class='op-status status-pendente'>Pendente</span>`;
      }
      const bom = montarBOM(produtoRaiz.id);
      // Montar tabela de soma de horas por operação
      let tabelaOperacoes = `<h3>Tempo total por tipo de operação (horas)</h3><table class='op-table'><thead><tr><th>Operação</th><th>Tempo Total (h)</th></tr></thead><tbody>`;
      for (const tipo in somaOperacoes) {
        tabelaOperacoes += `<tr><td>${tipo}</td><td>${(somaOperacoes[tipo]/60).toFixed(2)}</td></tr>`;
      }
      tabelaOperacoes += '</tbody></table>';
      relatorioDiv.innerHTML = `
        <h2>Produto: ${produtoRaiz.codigo} - ${produtoRaiz.descricao}</h2>
        <div><strong>Caminho Crítico:</strong> <span class='critico'>${caminhoCritico.tempo} min</span></div>
        <div><strong>Gargalo:</strong> <span class='gargalo'>${gargalo.label} (${gargalo.tempo} min)</span></div>
        ${tabelaOperacoes}
        <div class='tree'>${bom.html}</div>
      `;
    }
    window.gerarRelatorio = gerarRelatorio;
  </script>
</body>
</html> 