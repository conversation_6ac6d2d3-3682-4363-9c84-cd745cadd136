/**
 * SERVIÇO DE AUDITORIA TES
 * Implementa TES em todos os sistemas e facilita auditorias
 */

import { db } from '../firebase-config.js';
import { 
    collection, 
    addDoc, 
    getDocs,
    query,
    where,
    orderBy,
    Timestamp 
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

export class TESAuditService {
    
    /**
     * Registrar movimentação com TES obrigatória
     */
    static async registerMovement(movementData) {
        // Validar TES obrigatória
        if (!movementData.tes) {
            throw new Error('TES é obrigatória para movimentações');
        }

        // Enriquecer dados com auditoria
        const enrichedData = {
            ...movementData,
            dataHora: Timestamp.now(),
            usuario: movementData.usuario || 'Sistema',
            auditoria: {
                ip: movementData.ip || 'unknown',
                origem: movementData.origem || 'SISTEMA',
                timestamp: Timestamp.now(),
                tesValidada: true
            }
        };

        // Registrar movimentação
        const docRef = await addDoc(collection(db, "movimentacoesEstoque"), enrichedData);
        
        // Registrar log de auditoria
        await this.logAuditoria({
            acao: 'MOVIMENTACAO_ESTOQUE',
            tesUtilizada: movementData.tes,
            produtoId: movementData.produtoId,
            quantidade: movementData.quantidade,
            tipo: movementData.tipo,
            movimentacaoId: docRef.id,
            usuario: enrichedData.usuario,
            timestamp: Timestamp.now()
        });

        return docRef.id;
    }

    /**
     * Registrar log de auditoria
     */
    static async logAuditoria(auditData) {
        await addDoc(collection(db, "auditoriaMovimentacoes"), auditData);
    }

    /**
     * Gerar relatório de auditoria TES
     */
    static async generateTESAuditReport(filters = {}) {
        const { startDate, endDate, tes, usuario } = filters;
        
        let queryConstraints = [orderBy("timestamp", "desc")];
        
        if (startDate) {
            queryConstraints.push(where("timestamp", ">=", startDate));
        }
        if (endDate) {
            queryConstraints.push(where("timestamp", "<=", endDate));
        }
        if (tes) {
            queryConstraints.push(where("tesUtilizada", "==", tes));
        }
        if (usuario) {
            queryConstraints.push(where("usuario", "==", usuario));
        }

        const auditQuery = query(collection(db, "auditoriaMovimentacoes"), ...queryConstraints);
        const snapshot = await getDocs(auditQuery);
        
        return snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        }));
    }

    /**
     * Validar consistência TES
     */
    static async validateTESConsistency() {
        // Buscar movimentações sem TES
        const movSemTES = await getDocs(
            query(collection(db, "movimentacoesEstoque"), 
                  where("tes", "==", null))
        );

        // Buscar TES inválidas
        const tesValidas = ['001', '002', '003', '500', '501', '502', '200', '201', '202', '400', '401', '402', '900', '901', '902', '100', '101', '102'];
        const allMovements = await getDocs(collection(db, "movimentacoesEstoque"));
        
        const tesInvalidas = allMovements.docs.filter(doc => {
            const data = doc.data();
            return data.tes && !tesValidas.includes(data.tes);
        });

        return {
            movimentacoesSemTES: movSemTES.docs.length,
            tesInvalidas: tesInvalidas.length,
            detalhes: {
                semTES: movSemTES.docs.map(doc => ({ id: doc.id, ...doc.data() })),
                invalidas: tesInvalidas.map(doc => ({ id: doc.id, ...doc.data() }))
            }
        };
    }
}

// Funções auxiliares para implementação rápida
export const TESHelper = {
    // TES por tipo de operação
    getTESByOperation: (operation) => {
        const tesMap = {
            'COMPRA': '001',
            'VENDA': '500',
            'PRODUCAO_ENTRADA': '200',
            'PRODUCAO_CONSUMO': '201',
            'TRANSFERENCIA': '100',
            'AJUSTE_ENTRADA': '900',
            'AJUSTE_SAIDA': '901'
        };
        return tesMap[operation] || '001';
    },

    // Validar TES
    validateTES: (tes) => {
        const tesValidas = ['001', '002', '003', '500', '501', '502', '200', '201', '202', '400', '401', '402', '900', '901', '902', '100', '101', '102'];
        return tesValidas.includes(tes);
    },

    // Adicionar TES a movimentação existente
    addTESToMovement: (movementData, operation) => {
        return {
            ...movementData,
            tes: movementData.tes || TESHelper.getTESByOperation(operation),
            tipoDocumento: movementData.tipoDocumento || operation,
            auditoria: {
                tesAdicionada: true,
                timestamp: Timestamp.now()
            }
        };
    }
};

// Exportar para uso global
if (typeof window !== 'undefined') {
    window.TESAuditService = TESAuditService;
    window.TESHelper = TESHelper;
}