<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Controle de Baixa de Necessidades</title>
  <style>
    /* ===== CONTROLE DE BAIXA DE NECESSIDADES - ESTILOS MODERNOS ===== */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    /* ===== HEADER ===== */
    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 25px 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      font-size: 2rem;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .header p {
      font-size: 0.9rem;
      opacity: 0.9;
      margin-top: 5px;
    }

    /* ===== BOTÕES ===== */
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
    }

    .btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      color: white;
    }

    .btn-sm {
      padding: 5px 12px;
      font-size: 0.8rem;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    /* ===== ESTATÍSTICAS ===== */
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 30px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      border-left: 5px solid;
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-card.pendente {
      border-left-color: #e74c3c;
    }

    .stat-card.solicitada {
      border-left-color: #f39c12;
    }

    .stat-card.pedido {
      border-left-color: #3498db;
    }

    .stat-card.atendida {
      border-left-color: #27ae60;
    }

    .stat-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .stat-label {
      font-size: 1rem;
      color: #7f8c8d;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
    }

    /* ===== FILTROS ===== */
    .filters {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 25px;
      margin: 30px;
      margin-bottom: 25px;
      border-left: 4px solid #3498db;
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 8px;
      font-size: 0.9rem;
    }

    .filter-group input,
    .filter-group select {
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
    }

    .filter-group input:focus,
    .filter-group select:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    /* ===== TABELA ===== */
    .table-container {
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      margin: 30px;
    }

    .table-header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #6c757d;
    }

    .loading-spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    table th {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
      color: white !important;
      padding: 15px;
      text-align: left;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: sticky !important;
      top: 0 !important;
      z-index: 999 !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
      border-bottom: 2px solid #2c3e50;
      /* Força nova camada de composição */
      transform: translateZ(0);
      will-change: transform;
      /* Garante opacidade total */
      opacity: 1 !important;
      /* Remove qualquer filtro que possa interferir */
      filter: none !important;
      backdrop-filter: none !important;
    }

    table th i {
      margin-right: 6px;
      opacity: 0.9;
    }

    table th small {
      display: block;
      font-size: 10px;
      margin-top: 2px;
      opacity: 0.8;
      text-transform: none;
      letter-spacing: normal;
    }

    table td {
      padding: 12px 15px;
      border-bottom: 1px solid #e9ecef;
      vertical-align: middle;
      transition: all 0.2s ease;
    }

    table tbody tr:hover {
      background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    table tbody tr:last-child td {
      border-bottom: none;
    }

    .familia-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
      font-weight: bold;
      color: #2c3e50 !important;
      border-left: 4px solid #3498db;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .familia-header:hover {
      background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
      transform: none;
    }

    .familia-checkbox {
      transform: scale(1.2);
      cursor: pointer;
    }

    /* Estilos específicos para células melhoradas */
    .checkbox-cell {
      width: 40px;
      text-align: center;
      padding: 8px !important;
    }

    .saldo-positivo {
      color: #27ae60 !important;
      font-weight: bold;
    }

    .saldo-negativo {
      color: #e74c3c !important;
      font-weight: bold;
    }

    .saldo-zero {
      color: #95a5a6 !important;
    }

    .progress-bar {
      background: #ecf0f1;
      border-radius: 10px;
      height: 8px;
      overflow: hidden;
      margin-bottom: 4px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #27ae60, #2ecc71);
      border-radius: 10px;
      transition: width 0.3s ease;
    }

    .action-buttons {
      display: flex;
      gap: 4px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .action-buttons button {
      padding: 4px 8px;
      border-radius: 4px;
      border: none;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .action-buttons button:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* Container da tabela com cabeçalho fixo */
    #tabelaContainer {
      position: relative;
      overflow-y: auto;
      max-height: 600px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background: white;
      /* Força o contexto de stacking */
      transform: translateZ(0);
    }

    /* Garantir que o cabeçalho fique fixo - SOLUÇÃO ROBUSTA */
    #tabelaNecessidades {
      position: relative;
      width: 100%;
    }

    #tabelaNecessidades thead {
      position: sticky;
      top: 0;
      z-index: 999;
      /* Força nova camada de composição */
      transform: translateZ(0);
      will-change: transform;
    }

    #tabelaNecessidades thead th {
      position: sticky;
      top: 0;
      z-index: 999;
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
      /* Força opacidade total */
      opacity: 1 !important;
      /* Garante que não seja transparente */
      backdrop-filter: none;
      /* Força nova camada de composição */
      transform: translateZ(0);
      will-change: transform;
      /* Sombra mais forte para destacar */
      box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    }

    /* CSS adicional para forçar sticky em todos os navegadores */
    #tabelaNecessidades thead tr {
      position: sticky;
      top: 0;
      z-index: 999;
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    }

    /* Fallback para navegadores mais antigos */
    @supports not (position: sticky) {
      #tabelaContainer {
        position: relative;
      }

      #tabelaNecessidades thead {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 999;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      }
    }

    /* Força o cabeçalho a ficar sempre visível */
    .tabela-cabecalho-fixo {
      position: sticky !important;
      top: 0 !important;
      z-index: 999 !important;
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
    }

    .familia-checkbox:indeterminate {
      background-color: #ffc107;
      border-color: #ffc107;
    }

    .checkbox-cell {
      width: 40px;
      text-align: center;
    }

    /* ===== STATUS E BADGES ===== */
    .status-badge {
      padding: 5px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-pendente {
      background: #ffebee;
      color: #c62828;
    }

    .status-solicitada {
      background: #fff3e0;
      color: #ef6c00;
    }

    .status-cotando {
      background: #e3f2fd;
      color: #1565c0;
    }

    .status-pedido {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-recebendo {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .status-atendida {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-cancelada {
      background: #ffebee;
      color: #c62828;
      text-decoration: line-through;
    }

    .empenhado-badge {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: bold;
    }

    .progress-bar {
      background: #e9ecef;
      border-radius: 10px;
      height: 8px;
      width: 80px;
      overflow: hidden;
      display: inline-block;
      margin-right: 8px;
    }

    .progress-fill {
      background: linear-gradient(90deg, #27ae60, #2ecc71);
      height: 100%;
      transition: width 0.3s ease;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .saldo-positivo {
      color: #27ae60;
      font-weight: bold;
    }

    .saldo-negativo {
      color: #e74c3c;
      font-weight: bold;
    }

    .saldo-zero {
      color: #6c757d;
    }

    .text-muted {
      color: #6c757d !important;
    }

    /* ===== MODAIS ===== */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background-color: white;
      margin: 2% auto;
      padding: 0;
      border-radius: 15px;
      width: 90%;
      max-width: 800px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 25px 50px rgba(0,0,0,0.2);
      animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-50px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .modal-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 20px 30px;
      border-radius: 15px 15px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .close {
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .close:hover {
      color: #f39c12;
      transform: scale(1.1);
    }

    #modalConteudo {
      padding: 30px;
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .header h1 {
        font-size: 1.5rem;
      }

      .stats,
      .filters,
      .table-container {
        margin: 20px;
      }

      .stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .filters-grid {
        grid-template-columns: 1fr;
      }

      .stat-value {
        font-size: 2rem;
      }

      .btn {
        padding: 8px 16px;
        font-size: 0.8rem;
      }

      .action-buttons {
        flex-direction: column;
      }

      table {
        font-size: 0.8rem;
      }

      table th,
      table td {
        padding: 10px 8px;
      }
    }

    @media (max-width: 480px) {
      .stats {
        grid-template-columns: 1fr;
      }

      .header h1 {
        font-size: 1.2rem;
      }

      .stat-value {
        font-size: 1.8rem;
      }

      .btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
      }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <div>
        <h1><i class="fas fa-tasks"></i> Controle de Baixa de Necessidades</h1>
        <p>Acompanhamento do fluxo de atendimento das necessidades de MP - Do MRP ao Recebimento</p>
      </div>
      <div>
        <a href="index.html" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Voltar
        </a>
      </div>
    </div>

    <!-- Estatísticas -->
    <div class="stats">
      <div class="stat-card pendente">
        <div class="stat-value" id="totalPendentes">-</div>
        <div class="stat-label">🔴 Pendentes</div>
      </div>
      <div class="stat-card solicitada">
        <div class="stat-value" id="totalSolicitadas">-</div>
        <div class="stat-label">🟡 Solicitadas</div>
      </div>
      <div class="stat-card pedido">
        <div class="stat-value" id="totalPedidos">-</div>
        <div class="stat-label">🔵 Em Pedido</div>
      </div>
      <div class="stat-card atendida">
        <div class="stat-value" id="totalAtendidas">-</div>
        <div class="stat-label">🟢 Atendidas</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="totalSelecionaveis">-</div>
        <div class="stat-label">📋 Selecionáveis</div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="filters">
      <div class="filters-grid">
        <div class="filter-group">
          <label for="filtroStatus">Status da Necessidade:</label>
          <select id="filtroStatus">
            <option value="">Todos os Status</option>
            <option value="PENDENTE">🔴 Pendente</option>
            <option value="SOLICITADA">🟡 Solicitada</option>
            <option value="COTANDO">🔵 Cotando</option>
            <option value="PEDIDO">🟠 Em Pedido</option>
            <option value="RECEBENDO">⚪ Recebendo</option>
            <option value="ATENDIDA">🟢 Atendida</option>
            <option value="CANCELADA">🚫 Cancelada</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroProdutoPai">Produto Pai:</label>
          <select id="filtroProdutoPai">
            <option value="">Todos os Produtos Pai</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroFamilia">Família:</label>
          <select id="filtroFamilia">
            <option value="">Todas as Famílias</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filtroBusca">Buscar:</label>
          <input type="text" id="filtroBusca" placeholder="Código MP, descrição...">
        </div>
        <div class="filter-group">
          <label for="ocultarSolicitadas">Visualização:</label>
          <select id="ocultarSolicitadas">
            <option value="true">Ocultar Já Solicitadas</option>
            <option value="false">Mostrar Todas</option>
            <option value="pendentes">Apenas Pendentes</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="selecionarFamilia">Seleção por Família:</label>
          <select id="selecionarFamilia">
            <option value="">Selecione uma família...</option>
          </select>
        </div>
      </div>
      <div style="text-align: right;">
        <button class="btn btn-secondary" onclick="limparFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button class="btn btn-primary" onclick="aplicarFiltros()">
          <i class="fas fa-search"></i> Filtrar
        </button>

        <button class="btn btn-success" onclick="gerarSolicitacoesSelecionadas()">
          <i class="fas fa-shopping-cart"></i> Gerar Solicitações Selecionadas
        </button>
        <button class="btn btn-warning" onclick="empenharSelecionados()">
          <i class="fas fa-lock"></i> Empenhar Selecionados
        </button>
        <button class="btn btn-danger" onclick="cancelarSelecionados()">
          <i class="fas fa-times-circle"></i> Cancelar Selecionados
        </button>
        <button class="btn btn-info" onclick="analisarConsolidacaoInteligente()">
          <i class="fas fa-brain"></i> Análise Inteligente
        </button>






      </div>
    </div>

    <!-- Tabela de Necessidades -->
    <div class="table-container">
      <div class="table-header">
        <span><i class="fas fa-list"></i> Necessidades de MP</span>
        <span id="contadorNecessidades">Carregando...</span>
      </div>

      <div id="loadingContainer" class="loading">
        <div class="loading-spinner"></div>
        <p>Carregando necessidades...</p>
      </div>

      <div id="tabelaContainer" style="max-height: 600px; overflow-y: auto; position: relative;">
        <table id="tabelaNecessidades" style="display: none; position: relative;">
          <thead class="tabela-cabecalho-fixo">
            <tr class="tabela-cabecalho-fixo">
              <th class="tabela-cabecalho-fixo" style="width: 40px; text-align: center;">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 120px; text-align: left;">
                <i class="fas fa-barcode"></i> Código MP
              </th>
              <th class="tabela-cabecalho-fixo" style="min-width: 200px; text-align: left;">
                <i class="fas fa-box"></i> Descrição
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 150px; text-align: left;">
                <i class="fas fa-industry"></i> Produto Pai
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 120px; text-align: center;">
                <i class="fas fa-calculator"></i> Necessidade
                <br><small style="font-weight: normal; opacity: 0.8;">Qtd / Pendente</small>
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 120px; text-align: center;">
                <i class="fas fa-warehouse"></i> Saldo
                <br><small style="font-weight: normal; opacity: 0.8;">Disponível / Atual</small>
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 100px; text-align: center;">
                <i class="fas fa-lock"></i> Empenhado
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 100px; text-align: center;">
                <i class="fas fa-flag"></i> Status
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 120px; text-align: center;">
                <i class="fas fa-chart-line"></i> Progresso
              </th>
              <th class="tabela-cabecalho-fixo" style="width: 140px; text-align: center;">
                <i class="fas fa-cogs"></i> Ações
              </th>
            </tr>
          </thead>
          <tbody id="corpoTabela">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Modal para Ações -->
  <div id="modalAcoes" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitulo">Ação</h3>
        <span class="close" onclick="fecharModal()">&times;</span>
      </div>
      <div id="modalConteudo">
        <!-- Conteúdo será preenchido dinamicamente -->
      </div>
    </div>
  </div>

  <script type="module">
    // Firebase imports
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
    import {
      getFirestore,
      collection,
      getDocs,
      getDoc,
      doc,
      updateDoc,
      addDoc,
      query,
      where,
      orderBy,
      Timestamp
    } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

    // Importar serviço de numeração
    import { NumberGeneratorService } from './services/number-generator-service.js';

    // Firebase config
    const firebaseConfig = {
      apiKey: "AIzaSyDud1ir9Ta-0_jSB8dAaBCQau99RJY66RQ",
      authDomain: "banco-mrp.firebaseapp.com",
      projectId: "banco-mrp",
      storageBucket: "banco-mrp.firebasestorage.app",
      messagingSenderId: "740147152218",
      appId: "1:740147152218:web:2d301340bf314e68d75f63",
      measurementId: "G-YNNQ1VX1EH"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Estado global
    let necessidades = [];
    let necessidadesFiltradas = [];
    let produtos = [];
    let solicitacoes = [];
    let pedidos = [];
    let recebimentos = [];
    let cancelamentosCache = new Map(); // Cache dos cancelamentos

    // Usuário padrão (pode ser substituído por sistema de autenticação)
    const currentUser = {
      id: 'sistema',
      nome: 'Sistema',
      email: '<EMAIL>'
    };

    // Inicialização
    document.addEventListener('DOMContentLoaded', async () => {
      await carregarDados();
      await aplicarFiltros();
    });

    // Função para carregar cancelamentos do Firebase
    async function carregarCancelamentos() {
      try {
        const cancelamentosSnap = await getDocs(collection(db, "logsCancelamentos"));
        cancelamentosCache.clear();

        cancelamentosSnap.docs.forEach(doc => {
          const cancelamento = doc.data();
          // Usar uma chave única baseada no produto e ordens de produção
          const chave = `${cancelamento.produtoId}_${cancelamento.ordensProducao?.join('_') || ''}`;
          cancelamentosCache.set(chave, {
            id: doc.id,
            ...cancelamento
          });
        });

        console.log(`📋 Carregados ${cancelamentosCache.size} cancelamentos`);
      } catch (error) {
        console.warn('⚠️ Erro ao carregar cancelamentos:', error);
        // Não impede o carregamento se os cancelamentos falharem
      }
    }

    // Função para carregar todos os dados
    async function carregarDados() {
      try {
        document.getElementById('loadingContainer').style.display = 'block';

        console.log('🔄 Carregando dados...');

        // Carregar dados em paralelo (incluindo cancelamentos)
        const [
          ordensSnap,
          produtosSnap,
          solicitacoesSnap,
          pedidosSnap,
          estoquesSnap
        ] = await Promise.all([
          getDocs(collection(db, "ordensProducao")),
          getDocs(collection(db, "produtos")),
          getDocs(collection(db, "solicitacoesCompra")),
          getDocs(collection(db, "pedidosCompra")),
          getDocs(collection(db, "estoques"))
        ]);

        // Carregar cancelamentos
        await carregarCancelamentos();

        // Processar dados
        const ordens = ordensSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        solicitacoes = solicitacoesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        pedidos = pedidosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const estoques = estoquesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        console.log(`📊 Dados carregados: ${ordens.length} OPs, ${produtos.length} produtos, ${solicitacoes.length} solicitações`);

        // Gerar necessidades a partir das ordens pendentes
        necessidades = await gerarNecessidades(ordens, produtos, estoques);

        // Preencher dropdowns
        preencherDropdowns();

        // Atualizar estatísticas
        atualizarEstatisticas();

        console.log(`✅ ${necessidades.length} necessidades processadas`);

        // 🔧 CORREÇÃO CRÍTICA: Detectar e corrigir necessidades órfãs
        await corrigirNecessidadesOrfas();

        // Forçar atualização da interface
        if (necessidades.length === 0) {
          console.log('⚠️ Nenhuma necessidade encontrada, criando dados de exemplo...');
          necessidades = criarDadosExemplo();
        }

      } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
        document.getElementById('loadingContainer').innerHTML = `
          <div style="text-align: center; padding: 40px;">
            <h3>Erro ao carregar dados</h3>
            <p>${error.message}</p>
            <button class="btn btn-primary" onclick="location.reload()">Tentar Novamente</button>
          </div>
        `;
      } finally {
        document.getElementById('loadingContainer').style.display = 'none';
      }
    }

    // 🔧 FUNÇÃO CRÍTICA: Corrigir necessidades órfãs de solicitações excluídas
    async function corrigirNecessidadesOrfas() {
      try {
        console.log('🔍 Verificando necessidades órfãs...');

        let necessidadesCorrigidas = 0;
        const solicitacoesValidas = new Set();

        // Mapear solicitações válidas (não excluídas)
        solicitacoes.forEach(sol => {
          if (sol.status !== 'EXCLUIDA') {
            solicitacoesValidas.add(sol.id);
          }
        });

        // Verificar cada necessidade
        for (const necessidade of necessidades) {
          // Se necessidade tem solicitacaoId mas a solicitação não existe ou está excluída
          if (necessidade.solicitacaoId && !solicitacoesValidas.has(necessidade.solicitacaoId)) {

            console.log(`🔧 Corrigindo necessidade órfã: ${necessidade.produto.codigo} (Solicitação: ${necessidade.solicitacaoId})`);

            // Reverter necessidade para PENDENTE
            necessidade.status = 'PENDENTE';
            necessidade.solicitacaoId = null;
            necessidade.quantidadeSolicitada = 0;
            necessidade.ultimaAtualizacao = new Date();
            necessidade.observacoes = (necessidade.observacoes || '') +
              `\n[${new Date().toLocaleString('pt-BR')}] Revertida automaticamente - solicitação excluída`;

            necessidadesCorrigidas++;
          }
        }

        if (necessidadesCorrigidas > 0) {
          console.log(`✅ ${necessidadesCorrigidas} necessidades órfãs corrigidas e revertidas para PENDENTE`);

          // Mostrar notificação ao usuário
          mostrarNotificacaoCorrecao(necessidadesCorrigidas);
        }

      } catch (error) {
        console.error('❌ Erro ao corrigir necessidades órfãs:', error);
      }
    }

    // Função para mostrar notificação de correção
    function mostrarNotificacaoCorrecao(quantidade) {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 9999;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white; padding: 15px 20px; border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-weight: bold; max-width: 400px;
        animation: slideInRight 0.3s ease-out;
      `;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="fas fa-check-circle" style="font-size: 20px;"></i>
          <div>
            <div>🔧 Correção Automática</div>
            <div style="font-size: 14px; opacity: 0.9;">
              ${quantidade} necessidades órfãs foram revertidas para PENDENTE
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(notification);

      // Remover notificação após 5 segundos
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.opacity = '0';
          notification.style.transform = 'translateX(100%)';
          setTimeout(() => notification.remove(), 300);
        }
      }, 5000);
    }

    // Função para criar dados de exemplo caso não haja necessidades
    function criarDadosExemplo() {
      return [
        {
          id: 'EXEMPLO_1',
          produtoId: 'PROD_001',
          produto: {
            codigo: 'MAT001',
            descricao: 'Material de Exemplo 1',
            unidade: 'UN',
            familia: 'Exemplo',
            grupo: 'Teste'
          },
          produtoPaiId: 'PROD_PAI_001',
          produtoPai: {
            codigo: 'PROD001',
            descricao: 'Produto Pai Exemplo'
          },
          ordensProducao: ['OP001'],
          quantidadeNecessaria: 100,
          quantidadeSolicitada: 0,
          quantidadeRecebida: 0,
          quantidadeEmpenhada: 0,
          saldoAtual: 50,
          saldoDisponivel: 50,
          saldoPendente: 50,
          status: 'PENDENTE',
          prioridade: 'ALTA',
          dataCriacao: Timestamp.now(),
          ultimaAtualizacao: Timestamp.now()
        }
      ];
    }

    // Função para gerar necessidades com explosão recursiva completa das estruturas BOM
    async function gerarNecessidades(ordens, produtos, estoques) {
      console.log('🔄 Iniciando geração de necessidades com explosão completa...');
      
      const necessidadesMap = new Map();

      // Filtrar apenas OPs pai pendentes (mesmo filtro do consulta_ops_pai.html)
      const ordensPai = ordens.filter(op => {
        // Deve ser OP pai (nível 0 e sem pai)
        const isOpPai = !op.opPaiId && (op.nivel === 0 || op.nivel === undefined);
        
        // Deve ter status ativo (não concluída nem cancelada)
        const statusAtivo = !op.status || 
          !['CONCLUIDA', 'CONCLUÍDA', 'CANCELADA', 'FINALIZADA'].includes(op.status.toUpperCase());
        
        return isOpPai && statusAtivo;
      });

      console.log(`📊 Encontradas ${ordensPai.length} OPs pai para processamento`);

      if (ordensPai.length === 0) {
        console.log('⚠️ Nenhuma OP pai encontrada, retornando dados de exemplo');
        return criarDadosExemplo();
      }

      // Buscar estruturas se existirem
      let estruturas = [];
      try {
        const estruturasSnap = await getDocs(collection(db, "estruturas"));
        estruturas = estruturasSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        console.log(`📋 ${estruturas.length} estruturas carregadas`);
      } catch (error) {
        console.warn('Estruturas não encontradas, usando apenas materiais diretos');
      }

      // Processar cada OP pai
      for (const op of ordensPai) {
        const produtoPai = produtos.find(p => p.id === op.produtoId);
        if (!produtoPai) {
          console.warn(`❌ Produto pai não encontrado: ${op.produtoId}`);
          continue;
        }

        console.log(`🔍 Processando OP ${op.numero || op.id} - ${produtoPai.codigo}`);

        // Fazer explosão recursiva completa da estrutura
        await explodirEstrutura(
          op.produtoId, 
          op.quantidade || 1, 
          op, 
          produtoPai, 
          produtos, 
          estruturas, 
          estoques, 
          necessidadesMap,
          0, // nível inicial
          new Set() // controle de ciclos
        );
      }

      console.log(`📊 Total de necessidades geradas: ${necessidadesMap.size}`);

      // Converter para array e processar
      const necessidadesArray = Array.from(necessidadesMap.values())
        .filter(n => n.quantidadeNecessaria > 0)
        .map(n => ({
          ...n,
          ordensProducao: Array.from(n.ordensProducao),
          saldoPendente: Math.max(0, n.quantidadeNecessaria - (n.quantidadeRecebida || 0))
        }))
        .filter(n => {
          // Filtrar necessidades canceladas
          const chave = `${n.produtoId}_${n.ordensProducao.join('_')}`;
          const cancelamento = cancelamentosCache.get(chave);

          if (cancelamento) {
            console.log(`🚫 Necessidade cancelada filtrada: ${n.produto.codigo} - ${cancelamento.motivoCancelamento}`);
            return false; // Remove da lista
          }
          return true; // Mantém na lista
        })
        .sort((a, b) => a.produto.codigo.localeCompare(b.produto.codigo));

      console.log(`✅ Necessidades após filtrar cancelamentos: ${necessidadesArray.length}`);
      return necessidadesArray;
    }

    // Função recursiva para explodir estrutura BOM completamente
    async function explodirEstrutura(
      produtoId, 
      quantidade, 
      ordemOriginal, 
      produtoRaiz, 
      produtos, 
      estruturas, 
      estoques, 
      necessidadesMap,
      nivel = 0,
      processados = new Set()
    ) {
      // Prevenir loops infinitos
      if (processados.has(produtoId) || nivel > 10) {
        console.warn(`⚠️ Produto ${produtoId} já processado ou nível muito profundo (${nivel})`);
        return;
      }

      const produto = produtos.find(p => p.id === produtoId);
      if (!produto) {
        console.warn(`❌ Produto não encontrado: ${produtoId}`);
        return;
      }

      // Marcar como processado
      processados.add(produtoId);

      console.log(`${'  '.repeat(nivel)}🔍 Nível ${nivel}: ${produto.codigo} (${produto.tipo}) - Qty: ${quantidade}`);

      // Se é matéria-prima, adicionar às necessidades
      if (produto.tipo === 'MP') {
        const materialVirtual = {
          produtoId: produto.id,
          quantidade: quantidade
        };
        adicionarNecessidade(materialVirtual, produto, ordemOriginal, produtoRaiz, estoques, necessidadesMap);
        return;
      }

      // Se é produto acabado (PA) ou semi-produto (SP), buscar sua estrutura
      if (produto.tipo === 'PA' || produto.tipo === 'SP') {
        const estrutura = estruturas.find(e => e.produtoPaiId === produtoId);
        
        if (estrutura && estrutura.componentes && Array.isArray(estrutura.componentes)) {
          console.log(`${'  '.repeat(nivel)}📋 Estrutura encontrada com ${estrutura.componentes.length} componentes`);
          
          // Processar cada componente da estrutura
          for (const componente of estrutura.componentes) {
            const componenteId = componente.componenteId || componente.componentId;
            const quantidadeComponente = quantidade * (componente.quantidade || 1);
            
            console.log(`${'  '.repeat(nivel)}  ➤ Componente: ${componenteId} - Qty: ${quantidadeComponente}`);
            
            // Recursão para processar o componente
            await explodirEstrutura(
              componenteId,
              quantidadeComponente,
              ordemOriginal,
              produtoRaiz,
              produtos,
              estruturas,
              estoques,
              necessidadesMap,
              nivel + 1,
              new Set(processados) // Nova instância do Set para evitar interferência entre ramos
            );
          }
        } else {
          console.log(`${'  '.repeat(nivel)}⚠️ Estrutura não encontrada para ${produto.codigo} (${produto.tipo})`);
          
          // Se não tem estrutura mas é SP/PA, pode ter materiais diretos na OP
          if (ordemOriginal.materiaisNecessarios && Array.isArray(ordemOriginal.materiaisNecessarios)) {
            console.log(`${'  '.repeat(nivel)}📦 Usando materiais diretos da OP`);
            for (const material of ordemOriginal.materiaisNecessarios) {
              const materialProduto = produtos.find(p => p.id === material.produtoId);
              if (materialProduto && materialProduto.tipo === 'MP') {
                const materialVirtual = {
                  produtoId: materialProduto.id,
                  quantidade: material.quantidade * quantidade
                };
                adicionarNecessidade(materialVirtual, materialProduto, ordemOriginal, produtoRaiz, estoques, necessidadesMap);
              }
            }
          }
        }
      }

      // Remover da lista de processados ao sair da recursão (permite reprocessamento em outros ramos)
      processados.delete(produtoId);
    }

    

    // Função simplificada para adicionar necessidade de MP
    function adicionarNecessidade(material, produto, op, produtoRaiz, estoques, necessidadesMap) {
      const chave = `${material.produtoId}_${produtoRaiz.id}`;

      if (!necessidadesMap.has(chave)) {
        // Buscar dados do estoque
        const estoque = estoques.find(e => e.produtoId === material.produtoId);
        const saldoAtual = estoque?.saldo || 0;
        const saldoReservado = estoque?.saldoReservado || 0;
        const saldoEmpenhado = estoque?.saldoEmpenhado || 0;
        const saldoDisponivel = Math.max(0, saldoAtual - saldoReservado - saldoEmpenhado);

        // Determinar status
        const status = determinarStatusNecessidade(produto.codigo, solicitacoes, pedidos);

        // Criar nova necessidade
        necessidadesMap.set(chave, {
          id: `NECESSIDADE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          produtoId: material.produtoId,
          produto: {
            codigo: produto.codigo,
            descricao: produto.descricao,
            unidade: produto.unidade || 'UN',
            familia: produto.familia || 'Sem Família',
            grupo: produto.grupo || 'Sem Grupo',
            precoUnitario: produto.precoUnitario || 0
          },
          produtoPaiId: produtoRaiz.id,
          produtoPai: {
            codigo: produtoRaiz.codigo,
            descricao: produtoRaiz.descricao
          },
          ordensProducao: new Set(),

          // Quantidades
          quantidadeNecessaria: 0,
          quantidadeSolicitada: 0,
          quantidadeEmPedido: 0,
          quantidadeRecebida: 0,
          quantidadeEmpenhada: saldoEmpenhado,

          // Estoque
          saldoAtual,
          saldoReservado,
          saldoEmpenhado,
          saldoDisponivel,

          // Status e controle
          status,
          prioridade: calcularPrioridade(op),

          // Auditoria
          dataCriacao: Timestamp.now(),
          ultimaAtualizacao: Timestamp.now()
        });
      }

      // Adicionar quantidade e OP
      const necessidade = necessidadesMap.get(chave);
      necessidade.quantidadeNecessaria += material.quantidade || 0;
      necessidade.ordensProducao.add(op.numero || op.id);

      console.log(`✅ Adicionado ${produto.codigo}: ${material.quantidade} (Total: ${necessidade.quantidadeNecessaria})`);
    }

    // Função auxiliar para converter datas para Timestamp
    function converterParaTimestamp(data) {
      try {
        if (!data) {
          return Timestamp.now();
        }

        // Se já é Timestamp, retornar como está
        if (data && typeof data.toDate === 'function') {
          return data;
        }

        // Se é objeto com seconds (Timestamp serializado)
        if (data && data.seconds) {
          return Timestamp.fromDate(new Date(data.seconds * 1000));
        }

        // Se é string ou Date, converter
        if (typeof data === 'string' || data instanceof Date) {
          return Timestamp.fromDate(new Date(data));
        }

        // Fallback para agora
        return Timestamp.now();
      } catch (error) {
        console.warn('Erro ao converter data:', error);
        return Timestamp.now();
      }
    }

    // Função para determinar status da necessidade baseado em solicitações/pedidos
    function determinarStatusNecessidade(codigoProduto, solicitacoes, pedidos) {
      // Verificar se existe solicitação para este produto (incluir mais estados)
      const solicitacao = solicitacoes.find(s =>
        s.itens?.some(item => 
          item.codigo === codigoProduto || 
          item.produtoId === codigoProduto
        ) &&
        !['REJEITADA', 'CANCELADA'].includes(s.status?.toUpperCase())
      );

      if (!solicitacao) return 'PENDENTE';

      // Verificar se existe pedido
      const pedido = pedidos.find(p =>
        p.solicitacaoId === solicitacao.id ||
        p.itens?.some(item => 
          item.codigo === codigoProduto ||
          item.produtoId === codigoProduto
        )
      );

      if (!pedido) {
        const statusSolicitacao = (solicitacao.status || 'PENDENTE').toUpperCase();
        switch (statusSolicitacao) {
          case 'APROVADA':
          case 'APPROVED':
            return 'COTANDO';
          case 'PENDENTE':
          case 'PENDING':
            return 'SOLICITADA';
          default:
            return 'SOLICITADA';
        }
      }

      // Determinar status baseado no pedido
      const statusPedido = (pedido.status || 'PENDENTE').toUpperCase();
      switch (statusPedido) {
        case 'PENDENTE':
        case 'APROVADO':
        case 'APPROVED':
          return 'PEDIDO';
        case 'RECEBIDO':
        case 'RECEBIDO_PARCIAL':
        case 'RECEIVED':
          return 'ATENDIDA';
        default:
          return 'PEDIDO';
      }
    }

    // Função simplificada para calcular prioridade
    function calcularPrioridade(op) {
      try {
        // Se não tem data de entrega, prioridade média
        if (!op.dataEntrega && !op.dataPrevisaoFim) {
          return 'MEDIA';
        }

        let dataLimite = op.dataEntrega || op.dataPrevisaoFim;
        
        // Converter timestamp para Date
        if (dataLimite && typeof dataLimite.toDate === 'function') {
          dataLimite = dataLimite.toDate();
        } else if (dataLimite && dataLimite.seconds) {
          dataLimite = new Date(dataLimite.seconds * 1000);
        } else if (typeof dataLimite === 'string') {
          dataLimite = new Date(dataLimite);
        } else {
          return 'MEDIA';
        }

        const agora = new Date();
        const diasRestantes = Math.ceil((dataLimite - agora) / (1000 * 60 * 60 * 24));

        if (diasRestantes <= 7) return 'CRITICA';
        if (diasRestantes <= 15) return 'ALTA';
        if (diasRestantes <= 30) return 'MEDIA';
        return 'BAIXA';
        
      } catch (error) {
        console.warn('Erro ao calcular prioridade:', error);
        return 'MEDIA';
      }
    }

    // Função para preencher dropdowns
    function preencherDropdowns() {
      // Dropdown de Produto Pai
      const dropdownProdutoPai = document.getElementById('filtroProdutoPai');
      const produtosPai = [...new Set(necessidades.map(n => n.produtoPaiId))]
        .map(id => {
          const necessidade = necessidades.find(n => n.produtoPaiId === id);
          return {
            id,
            codigo: necessidade.produtoPai.codigo,
            descricao: necessidade.produtoPai.descricao
          };
        })
        .sort((a, b) => a.codigo.localeCompare(b.codigo));

      dropdownProdutoPai.innerHTML = '<option value="">Todos os Produtos Pai</option>';
      produtosPai.forEach(produto => {
        dropdownProdutoPai.innerHTML += `
          <option value="${produto.id}">${produto.codigo} - ${produto.descricao}</option>
        `;
      });

      // Dropdown de Família
      const dropdownFamilia = document.getElementById('filtroFamilia');
      const familias = [...new Set(necessidades.map(n => n.produto.familia))]
        .filter(f => f && f !== 'Sem Família')
        .sort();

      dropdownFamilia.innerHTML = '<option value="">Todas as Famílias</option>';
      familias.forEach(familia => {
        dropdownFamilia.innerHTML += `<option value="${familia}">${familia}</option>`;
      });

      // Dropdown de Seleção por Família
      const dropdownSelecionarFamilia = document.getElementById('selecionarFamilia');
      const familiasPendentes = [...new Set(necessidades
        .filter(n => n.status === 'PENDENTE')
        .map(n => n.produto.familia))]
        .filter(f => f && f !== 'Sem Família')
        .sort();

      dropdownSelecionarFamilia.innerHTML = '<option value="">Selecione uma família...</option>';
      familiasPendentes.forEach(familia => {
        const quantidadeItens = necessidades.filter(n => n.produto.familia === familia && n.status === 'PENDENTE').length;
        dropdownSelecionarFamilia.innerHTML += `<option value="${familia}">${familia} (${quantidadeItens} itens)</option>`;
      });
    }

    // Função para atualizar estatísticas
    function atualizarEstatisticas() {
      const stats = {
        pendentes: necessidades.filter(n => n.status === 'PENDENTE').length,
        solicitadas: necessidades.filter(n => ['SOLICITADA', 'COTANDO'].includes(n.status)).length,
        pedidos: necessidades.filter(n => n.status === 'PEDIDO').length,
        atendidas: necessidades.filter(n => ['RECEBIDA', 'EMPENHADA', 'CONSUMIDA'].includes(n.status)).length
      };

      document.getElementById('totalPendentes').textContent = stats.pendentes;
      document.getElementById('totalSolicitadas').textContent = stats.solicitadas;
      document.getElementById('totalPedidos').textContent = stats.pedidos;
      document.getElementById('totalAtendidas').textContent = stats.atendidas;
    }

    // Função para aplicar filtros
    window.aplicarFiltros = async function() {
      const filtroStatus = document.getElementById('filtroStatus').value;
      const filtroProdutoPai = document.getElementById('filtroProdutoPai').value;
      const filtroFamilia = document.getElementById('filtroFamilia').value;
      const filtroBusca = document.getElementById('filtroBusca').value.toLowerCase();
      const ocultarSolicitadas = document.getElementById('ocultarSolicitadas').value;

      necessidadesFiltradas = necessidades.filter(necessidade => {
        // Filtro inteligente para ocultar solicitadas
        if (ocultarSolicitadas === 'true' && ['SOLICITADA', 'COTANDO', 'PEDIDO', 'RECEBENDO', 'RECEBIDA', 'EMPENHADA', 'CONSUMIDA', 'ATENDIDA', 'CANCELADA'].includes(necessidade.status)) {
            return false;
        }

        // Filtro para mostrar apenas pendentes
        if (ocultarSolicitadas === 'pendentes' && necessidade.status !== 'PENDENTE') {
            return false;
        }

        // Filtro por status
        if (filtroStatus && necessidade.status !== filtroStatus) {
          return false;
        }

        // Filtro por produto pai
        if (filtroProdutoPai && necessidade.produtoPaiId !== filtroProdutoPai) {
          return false;
        }

        // Filtro por família
        if (filtroFamilia && necessidade.produto.familia !== filtroFamilia) {
          return false;
        }

        // Filtro por busca
        if (filtroBusca) {
          const textoBusca = `${necessidade.produto.codigo} ${necessidade.produto.descricao} ${necessidade.produtoPai.codigo}`.toLowerCase();
          if (!textoBusca.includes(filtroBusca)) {
            return false;
          }
        }

        return true;
      });

      await exibirNecessidades();
    };

    // Função para limpar filtros
    window.limparFiltros = function() {
      document.getElementById('filtroStatus').value = '';
      document.getElementById('filtroProdutoPai').value = '';
      document.getElementById('filtroFamilia').value = '';
      document.getElementById('filtroBusca').value = '';
      document.getElementById('ocultarSolicitadas').value = 'true';
      document.getElementById('selecionarFamilia').value = '';
      aplicarFiltros();
    };



    // Função para desmarcar todos os checkboxes
    window.desmarcarTodos = function() {
      const checkboxes = document.querySelectorAll('.necessidade-checkbox');
      const selectAll = document.getElementById('selectAll');
      const familiaCheckboxes = document.querySelectorAll('.familia-checkbox');

      checkboxes.forEach(checkbox => {
        checkbox.checked = false;
      });

      familiaCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        checkbox.indeterminate = false;
      });

      if (selectAll) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
      }

      atualizarContadorSelecionados();
    };

    // Função para toggle do select all
    window.toggleSelectAll = function(selectAllCheckbox) {
      const checkboxes = document.querySelectorAll('.necessidade-checkbox');

      checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
      });

      // Atualizar todos os checkboxes de família
      const familiaCheckboxes = document.querySelectorAll('.familia-checkbox');
      familiaCheckboxes.forEach(familiaCheckbox => {
        familiaCheckbox.checked = selectAllCheckbox.checked;
      });

      atualizarContadorSelecionados();
    };

    // Função para selecionar/desselecionar toda uma família
    window.toggleFamiliaSelection = function(familiaId, checked) {
      const checkboxesFamilia = document.querySelectorAll(`.familia-item-${familiaId}`);

      checkboxesFamilia.forEach(checkbox => {
        checkbox.checked = checked;
      });

      atualizarContadorSelecionados();
      atualizarSelectAll();
    };

    // Função para atualizar o checkbox da família baseado nos itens
    window.atualizarCheckboxFamilia = function(familiaId) {
      const checkboxesFamilia = document.querySelectorAll(`.familia-item-${familiaId}`);
      const checkboxFamilia = document.querySelector(`[data-familia="${familiaId}"]`);

      if (!checkboxFamilia || checkboxesFamilia.length === 0) return;

      const selecionados = Array.from(checkboxesFamilia).filter(cb => cb.checked).length;
      const total = checkboxesFamilia.length;

      if (selecionados === 0) {
        checkboxFamilia.checked = false;
        checkboxFamilia.indeterminate = false;
      } else if (selecionados === total) {
        checkboxFamilia.checked = true;
        checkboxFamilia.indeterminate = false;
      } else {
        checkboxFamilia.checked = false;
        checkboxFamilia.indeterminate = true;
      }

      atualizarSelectAll();
    };

    // Função para atualizar o select all baseado no estado geral
    function atualizarSelectAll() {
      const selectAll = document.getElementById('selectAll');
      const todosCheckboxes = document.querySelectorAll('.necessidade-checkbox');

      if (!selectAll || todosCheckboxes.length === 0) return;

      const selecionados = Array.from(todosCheckboxes).filter(cb => cb.checked).length;
      const total = todosCheckboxes.length;

      if (selecionados === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
      } else if (selecionados === total) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
      } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
      }
    }



    // Função para atualizar contador de selecionados
    window.atualizarContadorSelecionados = function() {
      const checkboxes = document.querySelectorAll('.necessidade-checkbox:checked');
      const total = checkboxes.length;

      // Atualizar texto nos botões
      const btnGerar = document.querySelector('button[onclick="gerarSolicitacoesSelecionadas()"]');
      const btnEmpenhar = document.querySelector('button[onclick="empenharSelecionados()"]');
      const btnCancelar = document.querySelector('button[onclick="cancelarSelecionados()"]');

      if (btnGerar) {
        btnGerar.innerHTML = `<i class="fas fa-shopping-cart"></i> Gerar Solicitações (${total})`;
        btnGerar.disabled = total === 0;
      }

      if (btnEmpenhar) {
        btnEmpenhar.innerHTML = `<i class="fas fa-lock"></i> Empenhar Selecionados (${total})`;
        btnEmpenhar.disabled = total === 0;
      }

      if (btnCancelar) {
        btnCancelar.innerHTML = `<i class="fas fa-times-circle"></i> Cancelar Selecionados (${total})`;
        btnCancelar.disabled = total === 0;
      }

      // Atualizar select all
      const selectAll = document.getElementById('selectAll');
      const totalCheckboxes = document.querySelectorAll('.necessidade-checkbox').length;

      if (selectAll && totalCheckboxes > 0) {
        selectAll.checked = total === totalCheckboxes;
        selectAll.indeterminate = total > 0 && total < totalCheckboxes;
      }
    };

    // Função para exibir necessidades na tabela agrupadas por família
    async function exibirNecessidades() {
      const tabela = document.getElementById('tabelaNecessidades');
      const corpoTabela = document.getElementById('corpoTabela');
      const contador = document.getElementById('contadorNecessidades');

      if (necessidadesFiltradas.length === 0) {
        tabela.style.display = 'none';
        contador.textContent = 'Nenhuma necessidade encontrada';
        return;
      }

      contador.textContent = `${necessidadesFiltradas.length} necessidades encontradas`;

            // Agrupar por família
      const necessidadesPorFamilia = {};
      necessidadesFiltradas.forEach(necessidade => {
        const familia = necessidade.produto.familia || 'Sem Família';
        if (!necessidadesPorFamilia[familia]) {
          necessidadesPorFamilia[familia] = [];
        }
        necessidadesPorFamilia[familia].push(necessidade);
      });

      // Ordenar famílias alfabeticamente
      const familiasOrdenadas = Object.keys(necessidadesPorFamilia).sort();

      let html = '';
      let totalSelecionaveis = 0;

      familiasOrdenadas.forEach(familia => {
        const necessidadesFamilia = necessidadesPorFamilia[familia];
        const totalFamilia = necessidadesFamilia.length;
        const valorTotalFamilia = necessidadesFamilia.reduce((total, n) =>
          total + (n.quantidadeNecessaria * (n.produto.precoUnitario || 0)), 0
        );

        // Cabeçalho da família
        const familiaId = familia.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '');
        const temItensSelecionaveis = necessidadesFamilia.some(n => ['PENDENTE', 'SOLICITADA'].includes(n.status));

        html += `
          <tr class="familia-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-left: 4px solid #3498db;">
            <td style="width: 40px; text-align: center; vertical-align: middle; padding: 12px 8px;">
              ${temItensSelecionaveis ?
                `<input type="checkbox" class="familia-checkbox" data-familia="${familiaId}" onchange="toggleFamiliaSelection('${familiaId}', this.checked)" style="transform: scale(1.2);">` :
                '<span class="text-muted">-</span>'
              }
            </td>
            <td colspan="9" style="padding: 12px 15px; vertical-align: middle;">
              <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap;">
                <div style="display: flex; align-items: center; gap: 10px;">
                  <i class="fas fa-folder" style="color: #3498db; font-size: 16px;"></i>
                  <span style="font-weight: bold; font-size: 16px; color: #2c3e50;">
                    ${familia.toUpperCase()}
                  </span>
                  <div style="display: flex; gap: 15px; align-items: center; margin-left: 10px;">
                    <span style="background: #3498db; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                      <i class="fas fa-list"></i> ${totalFamilia} itens
                    </span>
                    <span style="background: #27ae60; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                      <i class="fas fa-dollar-sign"></i> R$ ${valorTotalFamilia.toLocaleString('pt-BR', {minimumFractionDigits: 2})}
                    </span>
                  </div>
                </div>
                ${temItensSelecionaveis ?
                  `<div style="font-size: 12px; color: #7f8c8d; font-style: italic;">
                     <i class="fas fa-mouse-pointer"></i> Clique para selecionar toda a família
                   </div>` :
                  ''
                }
              </div>
            </td>
          </tr>
        `;

        // Itens da família
        necessidadesFamilia.forEach(necessidade => {
          const progresso = necessidade.quantidadeNecessaria > 0
            ? (necessidade.quantidadeRecebida / necessidade.quantidadeNecessaria) * 100
            : 0;

          const saldoDisponivel = necessidade.saldoDisponivel || 0;
          const saldoClass = saldoDisponivel > 0 ? 'saldo-positivo' :
                           saldoDisponivel < 0 ? 'saldo-negativo' : 'saldo-zero';

          const podeSelecionar = ['PENDENTE', 'SOLICITADA'].includes(necessidade.status);
          if (podeSelecionar) totalSelecionaveis++;

          html += `
            <tr class="item-familia" data-necessidade-id="${necessidade.id}" data-familia="${familiaId}">
              <td class="checkbox-cell" style="text-align: center; vertical-align: middle;">
                ${podeSelecionar ?
                  `<input type="checkbox" class="necessidade-checkbox familia-item-${familiaId}" value="${necessidade.id}" onchange="atualizarContadorSelecionados(); atualizarCheckboxFamilia('${familiaId}')">` :
                  '<span class="text-muted">-</span>'
                }
              </td>
              <td style="text-align: left; vertical-align: middle;">
                <div style="font-weight: bold; font-size: 14px; color: #2c3e50;">
                  ${necessidade.produto.codigo}
                </div>
              </td>
              <td style="text-align: left; vertical-align: middle;">
                <div style="font-weight: 500; color: #34495e; line-height: 1.3;">
                  ${necessidade.produto.descricao}
                </div>
                <div style="font-size: 11px; color: #7f8c8d; margin-top: 2px;">
                  <i class="fas fa-ruler"></i> ${necessidade.produto.unidade}
                </div>
              </td>
              <td style="text-align: left; vertical-align: middle;">
                <div style="font-weight: bold; color: #2980b9; font-size: 13px;">
                  ${necessidade.produtoPai.codigo}
                </div>
                <div style="font-size: 11px; color: #7f8c8d; line-height: 1.2; margin-top: 2px;">
                  ${necessidade.produtoPai.descricao.length > 30 ?
                    necessidade.produtoPai.descricao.substring(0, 30) + '...' :
                    necessidade.produtoPai.descricao}
                </div>
              </td>
              <td style="text-align: center; vertical-align: middle;">
                <div style="font-weight: bold; font-size: 16px; color: #e74c3c;">
                  ${necessidade.quantidadeNecessaria.toFixed(2)}
                </div>
                <div style="font-size: 11px; color: #95a5a6; margin-top: 2px;">
                  <i class="fas fa-clock"></i> Pendente: ${necessidade.saldoPendente.toFixed(2)}
                </div>
              </td>
              <td class="${saldoClass}" style="text-align: center; vertical-align: middle;">
                <div style="font-weight: bold; font-size: 16px;">
                  ${saldoDisponivel.toFixed(2)}
                </div>
                <div style="font-size: 11px; color: #95a5a6; margin-top: 2px;">
                  <i class="fas fa-warehouse"></i> Atual: ${necessidade.saldoAtual.toFixed(2)}
                </div>
              </td>
              <td style="text-align: center; vertical-align: middle;">
                ${necessidade.quantidadeEmpenhada > 0 ?
                  `<div style="background: #3498db; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                     ${necessidade.quantidadeEmpenhada.toFixed(2)}
                   </div>` :
                  '<span style="color: #bdc3c7; font-size: 14px;">0,00</span>'
                }
              </td>
              <td style="text-align: center; vertical-align: middle;">
                <span class="status-badge status-${necessidade.status.toLowerCase()}" style="padding: 6px 12px; border-radius: 15px; font-size: 11px; font-weight: bold;">
                  ${getStatusText(necessidade.status)}
                </span>
              </td>
              <td style="text-align: center; vertical-align: middle;">
                <div class="progress-bar" style="background: #ecf0f1; border-radius: 10px; height: 8px; margin-bottom: 4px;">
                  <div class="progress-fill" style="width: ${progresso.toFixed(1)}%; background: linear-gradient(90deg, #27ae60, #2ecc71); border-radius: 10px; height: 100%;"></div>
                </div>
                <div style="font-size: 11px; color: #7f8c8d; font-weight: 500;">
                  ${progresso.toFixed(1)}%
                </div>
              </td>
              <td style="text-align: center; vertical-align: middle;">
                <div class="action-buttons" style="display: flex; gap: 4px; justify-content: center; flex-wrap: wrap;">
                  ${getActionButtons(necessidade)}
                </div>
              </td>
            </tr>
          `;
        });
      });

      corpoTabela.innerHTML = html;
      tabela.style.display = 'table';

      // Atualizar contador de selecionáveis
      document.getElementById('totalSelecionaveis').textContent = totalSelecionaveis;
      atualizarContadorSelecionados();
    }

    // Função para obter texto do status
    function getStatusText(status) {
      const statusMap = {
        'PENDENTE': 'Pendente',
        'SOLICITADA': 'Solicitada',
        'COTANDO': 'Cotando',
        'PEDIDO': 'Em Pedido',
        'RECEBENDO': 'Recebendo',
        'RECEBIDA': 'Recebida',
        'EMPENHADA': 'Empenhada',
        'CONSUMIDA': 'Consumida',
        'ATENDIDA': 'Atendida',
        'CANCELADA': 'Cancelada'
      };
      return statusMap[status] || status;
    }

    // Função auxiliar para formatar datas
    function formatarData(data) {
      try {
        if (!data) return '-';

        let dataFormatada;

        // Se é Timestamp do Firebase
        if (data && typeof data.toDate === 'function') {
          dataFormatada = data.toDate();
        } else if (data && data.seconds) {
          // Se é objeto com seconds (Timestamp serializado)
          dataFormatada = new Date(data.seconds * 1000);
        } else if (typeof data === 'string' || data instanceof Date) {
          dataFormatada = new Date(data);
        } else {
          return '-';
        }

        return dataFormatada.toLocaleString('pt-BR');
      } catch (error) {
        console.warn('Erro ao formatar data:', error);
        return '-';
      }
    }

    // Função para obter botões de ação baseados no status
    function getActionButtons(necessidade) {
      let buttons = '';

      switch (necessidade.status) {
        case 'PENDENTE':
          buttons += `
            <button class="btn btn-sm btn-warning" onclick="criarSolicitacao('${necessidade.id}')">
              <i class="fas fa-plus"></i> Solicitar
            </button>
            <button class="btn btn-sm btn-danger" onclick="cancelarNecessidade('${necessidade.id}')">
              <i class="fas fa-times"></i> Cancelar
            </button>
          `;
          break;

        case 'SOLICITADA':
          buttons += `
            <button class="btn btn-sm btn-info" onclick="verSolicitacao('${necessidade.solicitacaoId}')">
              <i class="fas fa-eye"></i> Ver SC
            </button>
          `;
          break;

        case 'PEDIDO':
          buttons += `
            <button class="btn btn-sm btn-primary" onclick="verPedido('${necessidade.pedidoId}')">
              <i class="fas fa-eye"></i> Ver PC
            </button>
          `;
          break;

        case 'RECEBIDA':
          buttons += `
            <button class="btn btn-sm btn-info" onclick="empenharMaterial('${necessidade.id}')">
              <i class="fas fa-lock"></i> Empenhar
            </button>
          `;
          break;

        case 'CANCELADA':
          buttons += `
            <button class="btn btn-sm btn-outline-danger" onclick="verDetalhesCancelamento('${necessidade.id}')" title="Ver detalhes do cancelamento">
              <i class="fas fa-info-circle"></i> Detalhes
            </button>
          `;
          break;
      }

      // Botão de histórico sempre disponível
      buttons += `
        <button class="btn btn-sm btn-secondary" onclick="verHistorico('${necessidade.id}')">
          <i class="fas fa-history"></i> Histórico
        </button>
      `;

      return buttons;
    }

    // Função para gerar solicitações das necessidades pendentes
    window.gerarSolicitacoes = async function() {
      const necessidadesPendentes = necessidadesFiltradas.filter(n => n.status === 'PENDENTE');

      if (necessidadesPendentes.length === 0) {
        alert('Não há necessidades pendentes para gerar solicitações.');
        return;
      }

      if (!confirm(`Gerar solicitações para ${necessidadesPendentes.length} necessidades pendentes?`)) {
        return;
      }

      try {
        // Agrupar por família
        const grupos = {};
        necessidadesPendentes.forEach(necessidade => {
          const familia =necessidade.produto.familia || 'Sem Família';
          if (!grupos[familia]) {
            grupos[familia] = [];
          }
          grupos[familia].push(necessidade);
        });

        let solicitacoesCriadas = 0;

        for (const [familia, necessidadesGrupo] of Object.entries(grupos)) {
          const numeroSolicitacao = await NumberGeneratorService.generateSolicitacaoNumber();

          // ✅ CALCULAR DATA DE NECESSIDADE MAIS PRÓXIMA
          const datasNecessidade = necessidadesGrupo
            .map(n => n.dataNecessidade)
            .filter(data => data && data.seconds)
            .sort((a, b) => a.seconds - b.seconds);

          const dataNecessidadeMaisProxima = datasNecessidade.length > 0 ?
            datasNecessidade[0] : // Primeira data (mais próxima)
            Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)); // 7 dias a partir de hoje como fallback

          console.log(`📅 Data de necessidade para família ${familia}:`,
            dataNecessidadeMaisProxima ? new Date(dataNecessidadeMaisProxima.seconds * 1000).toLocaleDateString('pt-BR') : 'Fallback +7 dias');

          const solicitacaoData = {
            numero: numeroSolicitacao,
            dataCriacao: Timestamp.now(),
            status: 'PENDENTE',
            tipo: 'PLANEJADA',
            origem: 'CONTROLE_NECESSIDADES',
            familia: familia,
            solicitante: 'Sistema de Controle',
            departamento: 'PRODUCAO',
            prioridade: 'NORMAL',
            dataNecessidade: dataNecessidadeMaisProxima, // ✅ ADICIONADO
            itens: necessidadesGrupo.map(n => ({
              produtoId: n.produtoId,
              codigo: n.produto.codigo,
              descricao: n.produto.descricao,
              quantidade: n.saldoPendente,
              unidade: n.produto.unidade,
              necessidadeOrigemId: n.id,
              ordensOrigem: n.ordensProducao
            })),
            necessidadesAtendidas: necessidadesGrupo.map(n => n.id),
            justificativa: `Solicitação gerada automaticamente pelo controle de necessidades.\nFamília: ${familia}\nOrdens: ${[...new Set(necessidadesGrupo.flatMap(n => n.ordensProducao))].join(', ')}`
          };

          // Salvar solicitação
          const docRef = await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);
          console.log(`📝 Solicitação ${numeroSolicitacao} criada com ID: ${docRef.id}`);

          // Atualizar necessidades em memória (não no Firebase)
          for (const necessidade of necessidadesGrupo) {
            // Atualizar objeto em memória
            necessidade.status = 'SOLICITADA';
            necessidade.solicitacaoId = docRef.id;
            necessidade.quantidadeSolicitada = necessidade.saldoPendente;
            necessidade.ultimaAtualizacao = Timestamp.now();
            console.log(`🔄 Necessidade ${necessidade.produto.codigo} atualizada para SOLICITADA`);
          }

          solicitacoesCriadas++;
        }

        alert(`✅ ${solicitacoesCriadas} solicitações criadas com sucesso!\n\nAs necessidades foram atualizadas para status SOLICITADA e não aparecerão mais na lista quando o filtro "Ocultar Já Solicitadas" estiver ativo.`);

        // Recarregar dados do Firebase para sincronizar
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('Erro ao gerar solicitações:', error);
        alert('Erro ao gerar solicitações: ' + error.message);
      }
    };

    // Função para gerar solicitações das necessidades selecionadas
    window.gerarSolicitacoesSelecionadas = async function() {
      const checkboxesSelecionados = document.querySelectorAll('.necessidade-checkbox:checked');

      if (checkboxesSelecionados.length === 0) {
        alert('Selecione pelo menos uma necessidade para gerar solicitações.');
        return;
      }

      const necessidadesSelecionadas = Array.from(checkboxesSelecionados).map(checkbox => {
        return necessidades.find(n => n.id === checkbox.value);
      }).filter(n => n && ['PENDENTE'].includes(n.status));

      if (necessidadesSelecionadas.length === 0) {
        alert('Nenhuma necessidade selecionada está pendente para gerar solicitações.');
        return;
      }

      if (!confirm(`Gerar solicitações para ${necessidadesSelecionadas.length} necessidades selecionadas?`)) {
        return;
      }

      try {
        // Aglutinar produtos iguais somando quantidades
        const produtosAglutinados = new Map();
        const necessidadesOriginais = [];

        necessidadesSelecionadas.forEach(necessidade => {
          const chave = necessidade.produtoId;

          if (produtosAglutinados.has(chave)) {
            // Produto já existe, somar quantidade
            const itemExistente = produtosAglutinados.get(chave);
            itemExistente.quantidade += necessidade.saldoPendente;
            itemExistente.necessidadesOriginais.push(necessidade);
            itemExistente.ordensOrigem = [...new Set([...itemExistente.ordensOrigem, ...necessidade.ordensProducao])];
          } else {
            // Novo produto
            produtosAglutinados.set(chave, {
              produtoId: necessidade.produtoId,
              codigo: necessidade.produto.codigo,
              descricao: necessidade.produto.descricao,
              quantidade: necessidade.saldoPendente,
              unidade: necessidade.produto.unidade,
              necessidadesOriginais: [necessidade],
              ordensOrigem: [...necessidade.ordensProducao]
            });
          }

          necessidadesOriginais.push(necessidade);
        });

        // Criar UMA única solicitação com todos os itens aglutinados
        const numeroSolicitacao = await NumberGeneratorService.generateSolicitacaoNumber();

        // Calcular data de necessidade mais próxima de TODAS as necessidades
        const datasNecessidade = necessidadesOriginais
          .map(n => n.dataNecessidade)
          .filter(data => data && data.seconds)
          .sort((a, b) => a.seconds - b.seconds);

        const dataNecessidadeMaisProxima = datasNecessidade.length > 0 ?
          datasNecessidade[0] : // Primeira data (mais próxima)
          Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)); // 7 dias a partir de hoje como fallback

        // Obter famílias envolvidas
        const familias = [...new Set(necessidadesOriginais.map(n => n.produto.familia || 'Sem Família'))];
        const familiaTexto = familias.length === 1 ? familias[0] : `${familias.length} famílias`;

        console.log(`📅 Data de necessidade para solicitação consolidada:`,
          dataNecessidadeMaisProxima ? new Date(dataNecessidadeMaisProxima.seconds * 1000).toLocaleDateString('pt-BR') : 'Fallback +7 dias');
        console.log(`📦 Produtos aglutinados: ${produtosAglutinados.size} itens únicos de ${necessidadesOriginais.length} necessidades`);

        const solicitacaoData = {
          numero: numeroSolicitacao,
          dataCriacao: Timestamp.now(),
          status: 'PENDENTE',
          tipo: 'SELECIONADA_CONSOLIDADA',
          origem: 'CONTROLE_NECESSIDADES_SELECIONADAS',
          familia: familiaTexto,
          solicitante: 'Usuário',
          departamento: 'PRODUCAO',
          prioridade: 'NORMAL',
          dataNecessidade: dataNecessidadeMaisProxima,
          itens: Array.from(produtosAglutinados.values()).map(item => ({
            produtoId: item.produtoId,
            codigo: item.codigo,
            descricao: item.descricao,
            quantidade: item.quantidade,
            unidade: item.unidade,
            necessidadesOriginais: item.necessidadesOriginais.map(n => n.id),
            ordensOrigem: item.ordensOrigem
          })),
          necessidadesAtendidas: necessidadesOriginais.map(n => n.id),
          consolidacao: {
            itensOriginais: necessidadesOriginais.length,
            itensConsolidados: produtosAglutinados.size,
            familias: familias,
            ordensEnvolvidas: [...new Set(necessidadesOriginais.flatMap(n => n.ordensProducao))]
          },
          justificativa: `Solicitação CONSOLIDADA gerada a partir de ${necessidadesOriginais.length} necessidades selecionadas.\n` +
                        `Itens únicos: ${produtosAglutinados.size}\n` +
                        `Famílias: ${familias.join(', ')}\n` +
                        `Ordens: ${[...new Set(necessidadesOriginais.flatMap(n => n.ordensProducao))].join(', ')}`
        };

        // Salvar solicitação
        const docRef = await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);

        // Atualizar necessidades em memória
        for (const necessidade of necessidadesOriginais) {
          necessidade.status = 'SOLICITADA';
          necessidade.solicitacaoId = docRef.id;
          necessidade.quantidadeSolicitada = necessidade.saldoPendente;
          necessidade.ultimaAtualizacao = Timestamp.now();
        }

        const solicitacoesCriadas = 1;

        alert(`✅ Solicitação consolidada criada com sucesso!\n\n` +
              `📋 Solicitação: ${numeroSolicitacao}\n` +
              `📦 ${necessidadesOriginais.length} necessidades → ${produtosAglutinados.size} itens únicos\n` +
              `👥 Famílias: ${familias.join(', ')}\n\n` +
              `As necessidades foram atualizadas para status SOLICITADA e não aparecerão mais na lista quando o filtro "Ocultar Já Solicitadas" estiver ativo.`);

        // Desmarcar todos os checkboxes
        desmarcarTodos();

        // Recarregar dados do Firebase para sincronizar
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('Erro ao gerar solicitações selecionadas:', error);
        alert('Erro ao gerar solicitações: ' + error.message);
      }
    };

    // Função para empenhar materiais selecionados
    window.empenharSelecionados = async function() {
      const checkboxesSelecionados = document.querySelectorAll('.necessidade-checkbox:checked');

      if (checkboxesSelecionados.length === 0) {
        alert('Selecione pelo menos uma necessidade para empenhar.');
        return;
      }

      const necessidadesSelecionadas = Array.from(checkboxesSelecionados).map(checkbox => {
        return necessidades.find(n => n.id === checkbox.value);
      }).filter(n => n && ['RECEBIDA', 'ATENDIDA'].includes(n.status));

      if (necessidadesSelecionadas.length === 0) {
        alert('Nenhuma necessidade selecionada está disponível para empenho (status RECEBIDA ou ATENDIDA).');
        return;
      }

      // Mostrar modal de confirmação com detalhes
      const modal = document.getElementById('modalAcoes');
      const titulo = document.getElementById('modalTitulo');
      const conteudo = document.getElementById('modalConteudo');

      titulo.textContent = 'Empenhar Materiais Selecionados';

      let htmlItens = '';
      let totalItens = 0;
      let valorTotal = 0;

      necessidadesSelecionadas.forEach(necessidade => {
        const quantidadeDisponivel = necessidade.quantidadeRecebida - necessidade.quantidadeEmpenhada;
        const quantidadeEmpenhar = Math.min(quantidadeDisponivel, necessidade.saldoPendente);
        const valorItem = quantidadeEmpenhar * (necessidade.produto.precoUnitario || 0);

        if (quantidadeEmpenhar > 0) {
          htmlItens += `
            <tr>
              <td>${necessidade.produto.codigo}</td>
              <td>${necessidade.produto.descricao}</td>
              <td>${quantidadeDisponivel.toFixed(2)} ${necessidade.produto.unidade}</td>
              <td>
                <input type="number"
                       id="empenho_${necessidade.id}"
                       value="${quantidadeEmpenhar.toFixed(2)}"
                       max="${quantidadeDisponivel}"
                       min="0"
                       step="0.01"
                       style="width: 80px;">
              </td>
              <td>R$ ${valorItem.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
            </tr>
          `;
          totalItens++;
          valorTotal += valorItem;
        }
      });

      conteudo.innerHTML = `
        <div style="margin-bottom: 20px;">
          <h4>Materiais Disponíveis para Empenho:</h4>
          <p><strong>${totalItens}</strong> itens selecionados - Valor Total: <strong>R$ ${valorTotal.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</strong></p>
        </div>

        <div style="max-height: 300px; overflow-y: auto; margin-bottom: 20px;">
          <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
              <tr style="background: #f8f9fa;">
                <th style="padding: 8px; border: 1px solid #dee2e6;">Código</th>
                <th style="padding: 8px; border: 1px solid #dee2e6;">Descrição</th>
                <th style="padding: 8px; border: 1px solid #dee2e6;">Disponível</th>
                <th style="padding: 8px; border: 1px solid #dee2e6;">Empenhar</th>
                <th style="padding: 8px; border: 1px solid #dee2e6;">Valor</th>
              </tr>
            </thead>
            <tbody>
              ${htmlItens}
            </tbody>
          </table>
        </div>

        <div style="margin-bottom: 20px;">
          <label for="observacoesEmpenho">Observações do Empenho:</label>
          <textarea id="observacoesEmpenho" rows="3" style="width: 100%; padding: 8px; margin-top: 5px;"
                    placeholder="Observações sobre o empenho dos materiais..."></textarea>
        </div>

        <div style="text-align: right;">
          <button class="btn btn-secondary" onclick="fecharModal()">Cancelar</button>
          <button class="btn btn-warning" onclick="confirmarEmpenho()">Confirmar Empenho</button>
        </div>
      `;

      modal.style.display = 'block';
    };

    // Função para confirmar empenho
    window.confirmarEmpenho = async function() {
      try {
        const observacoes = document.getElementById('observacoesEmpenho').value;
        const checkboxesSelecionados = document.querySelectorAll('.necessidade-checkbox:checked');

        const necessidadesSelecionadas = Array.from(checkboxesSelecionados).map(checkbox => {
          return necessidades.find(n => n.id === checkbox.value);
        }).filter(n => n && ['RECEBIDA', 'ATENDIDA'].includes(n.status));

        let empenhosRealizados = 0;
        let valorTotalEmpenhado = 0;

        for (const necessidade of necessidadesSelecionadas) {
          const inputEmpenho = document.getElementById(`empenho_${necessidade.id}`);
          if (!inputEmpenho) continue;

          const quantidadeEmpenhar = parseFloat(inputEmpenho.value);
          if (!quantidadeEmpenhar || quantidadeEmpenhar <= 0) continue;

          const quantidadeDisponivel = necessidade.quantidadeRecebida - necessidade.quantidadeEmpenhada;
          if (quantidadeEmpenhar > quantidadeDisponivel) {
            alert(`Quantidade para empenho de ${necessidade.produto.codigo} excede o disponível (${quantidadeDisponivel.toFixed(2)}).`);
            return;
          }

          // Criar registro de empenho
          const empenhoData = {
            necessidadeOrigemId: necessidade.id,
            produtoId: necessidade.produtoId,
            produtoRaizId: necessidade.produtoPaiId,
            ordensProducao: necessidade.ordensProducao,
            quantidade: quantidadeEmpenhar,
            dataEmpenho: Timestamp.now(),
            status: 'ATIVO',
            tipo: 'MANUAL',
            origem: 'CONTROLE_NECESSIDADES',
            observacoes: observacoes,
            criadoPor: 'Sistema de Controle',
            produto: {
              codigo: necessidade.produto.codigo,
              descricao: necessidade.produto.descricao,
              unidade: necessidade.produto.unidade
            }
          };

          // Salvar empenho no Firebase
          const empenhoRef = await addDoc(collection(db, "empenhos"), empenhoData);

          // Atualizar necessidade em memória
          necessidade.quantidadeEmpenhada += quantidadeEmpenhar;
          necessidade.status = necessidade.quantidadeEmpenhada >= necessidade.quantidadeNecessaria ? 'EMPENHADA' : 'PARCIAL_EMPENHADA';
          necessidade.empenhoId = empenhoRef.id;
          necessidade.ultimaAtualizacao = Timestamp.now();

          // Atualizar estoque (reduzir saldo disponível)
          // Nota: Em um sistema real, isso seria feito através de uma transação
          console.log(`Empenho criado: ${necessidade.produto.codigo} - ${quantidadeEmpenhar} ${necessidade.produto.unidade}`);

          empenhosRealizados++;
          valorTotalEmpenhado += quantidadeEmpenhar * (necessidade.produto.precoUnitario || 0);
        }

        fecharModal();

        if (empenhosRealizados > 0) {
          alert(`${empenhosRealizados} empenhos realizados com sucesso!\nValor total empenhado: R$ ${valorTotalEmpenhado.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`);

          // Desmarcar todos os checkboxes
          desmarcarTodos();

          // Atualizar estatísticas e tabela
          atualizarEstatisticas();
          await aplicarFiltros();
        } else {
          alert('Nenhum empenho foi realizado.');
        }

      } catch (error) {
        console.error('Erro ao confirmar empenho:', error);
        alert('Erro ao confirmar empenho: ' + error.message);
      }
    };

    // Função para análise inteligente de consolidação
    window.analisarConsolidacaoInteligente = function() {
      const necessidadesPendentes = necessidades.filter(n => ['PENDENTE'].includes(n.status));

      if (necessidadesPendentes.length === 0) {
        alert('Não há necessidades pendentes para análise de consolidação.');
        return;
      }

      const analise = identificarOportunidadesConsolidacao(necessidadesPendentes);

      // Armazenar análise para uso posterior
      window.analiseAtual = analise;

      mostrarAnaliseConsolidacao(analise);
    };

    // Função para identificar oportunidades de consolidação
    function identificarOportunidadesConsolidacao(necessidades) {
      const oportunidades = [];
      let economiaTotal = 0;
      let reducaoSCs = 0;

      // Oportunidade 1: Consolidação por produto exato
      const gruposPorProduto = agruparPorProdutoExato(necessidades);
      Object.values(gruposPorProduto).forEach(grupo => {
        if (grupo.necessidades.length > 1) {
          const economia = calcularEconomiaConsolidacao(grupo);
          oportunidades.push({
            id: `produto_${grupo.produto.codigo}`,
            tipo: 'PRODUTO_EXATO',
            titulo: `Consolidar ${grupo.produto.codigo}`,
            descricao: `${grupo.necessidades.length} OPs diferentes solicitando o mesmo produto`,
            itens: grupo.necessidades.map(n => ({
              codigo: n.produto.codigo,
              descricao: n.produto.descricao,
              qtd: n.quantidadeNecessaria,
              op: n.produtoPai.codigo
            })),
            quantidadeTotal: grupo.quantidadeTotal,
            economia: economia,
            prioridade: economia > 200 ? 'alta' : 'media',
            necessidadesIds: grupo.necessidades.map(n => n.id)
          });
          economiaTotal += economia;
          reducaoSCs += grupo.necessidades.length - 1;
        }
      });

      // Oportunidade 2: Consolidação por família
      const gruposPorFamilia = agruparPorFamilia(necessidades);
      Object.values(gruposPorFamilia).forEach(grupo => {
        if (grupo.necessidades.length > 2 && grupo.valorTotal > 500) {
          const economia = calcularEconomiaFamilia(grupo);
          oportunidades.push({
            id: `familia_${grupo.familia}`,
            tipo: 'FAMILIA',
            titulo: `Consolidar Família ${grupo.familia}`,
            descricao: `${grupo.necessidades.length} itens da mesma família`,
            itens: grupo.necessidades.map(n => ({
              codigo: n.produto.codigo,
              descricao: n.produto.descricao,
              qtd: n.quantidadeNecessaria,
              op: n.produtoPai.codigo
            })),
            valorTotal: grupo.valorTotal,
            economia: economia,
            prioridade: economia > 300 ? 'alta' : 'media',
            necessidadesIds: grupo.necessidades.map(n => n.id)
          });
          economiaTotal += economia;
          reducaoSCs += Math.floor(grupo.necessidades.length / 3);
        }
      });

      // Oportunidade 3: Aguardar mais itens (valor mínimo)
      const gruposValorMinimo = identificarGruposValorMinimo(necessidades);
      gruposValorMinimo.forEach(grupo => {
        oportunidades.push({
          id: `valor_minimo_${grupo.familia}`,
          tipo: 'VALOR_MINIMO',
          titulo: `Aguardar Mais Itens - ${grupo.familia}`,
          descricao: `Valor atual R$ ${grupo.valorAtual.toFixed(2)} - Aguardar para atingir R$ ${grupo.valorMinimo.toFixed(2)}`,
          itens: grupo.necessidades.map(n => ({
            codigo: n.produto.codigo,
            descricao: n.produto.descricao,
            qtd: n.quantidadeNecessaria,
            op: n.produtoPai.codigo
          })),
          valorAtual: grupo.valorAtual,
          valorMinimo: grupo.valorMinimo,
          economia: grupo.economiaEsperada,
          prioridade: 'baixa',
          necessidadesIds: grupo.necessidades.map(n => n.id),
          diasEspera: 3
        });
      });

      return {
        oportunidades: oportunidades.sort((a, b) => b.economia - a.economia),
        economiaTotal,
        reducaoSCs,
        totalNecessidades: necessidades.length
      };
    }

    // Função para agrupar por produto exato
    function agruparPorProdutoExato(necessidades) {
      const grupos = {};

      necessidades.forEach(necessidade => {
        const chave = `${necessidade.produto.codigo}_${necessidade.produto.fornecedorPreferencial || 'SEM_FORNECEDOR'}`;

        if (!grupos[chave]) {
          grupos[chave] = {
            produto: necessidade.produto,
            fornecedor: necessidade.produto.fornecedorPreferencial,
            necessidades: [],
            quantidadeTotal: 0,
            valorTotal: 0,
            opsEnvolvidas: new Set()
          };
        }

        grupos[chave].necessidades.push(necessidade);
        grupos[chave].quantidadeTotal += necessidade.quantidadeNecessaria;
        grupos[chave].valorTotal += necessidade.quantidadeNecessaria * (necessidade.produto.precoUnitario || 0);
        grupos[chave].opsEnvolvidas.add(necessidade.produtoPai.codigo);
      });

      return grupos;
    }

    // Função para agrupar por família
    function agruparPorFamilia(necessidades) {
      const grupos = {};

      necessidades.forEach(necessidade => {
        const familia = necessidade.produto.familia || 'SEM_FAMILIA';

        if (!grupos[familia]) {
          grupos[familia] = {
            familia: familia,
            necessidades: [],
            valorTotal: 0,
            opsEnvolvidas: new Set()
          };
        }

        grupos[familia].necessidades.push(necessidade);
        grupos[familia].valorTotal += necessidade.quantidadeNecessaria * (necessidade.produto.precoUnitario || 0);
        grupos[familia].opsEnvolvidas.add(necessidade.produtoPai.codigo);
      });

      return grupos;
    }

    // Função para identificar grupos com valor mínimo
    function identificarGruposValorMinimo(necessidades) {
      const grupos = [];
      const familias = agruparPorFamilia(necessidades);

      Object.values(familias).forEach(grupo => {
        const valorMinimo = 500.00; // Valor mínimo padrão
        if (grupo.valorTotal < valorMinimo && grupo.valorTotal > 100) {
          grupos.push({
            familia: grupo.familia,
            necessidades: grupo.necessidades,
            valorAtual: grupo.valorTotal,
            valorMinimo: valorMinimo,
            economiaEsperada: 80.00 // Economia estimada de frete
          });
        }
      });

      return grupos;
    }

    // Função para calcular economia de consolidação
    function calcularEconomiaConsolidacao(grupo) {
      const custoAdministrativo = 50.00; // Custo por SC
      const economiaAdministrativa = (grupo.necessidades.length - 1) * custoAdministrativo;

      const economiaFrete = grupo.necessidades.length > 2 ? 120.00 : 60.00;

      const descontoVolume = grupo.valorTotal > 1000 ? grupo.valorTotal * 0.05 : 0;

      return economiaAdministrativa + economiaFrete + descontoVolume;
    }

    // Função para calcular economia por família
    function calcularEconomiaFamilia(grupo) {
      const custoAdministrativo = 50.00;
      const numeroSCs = Math.ceil(grupo.necessidades.length / 5); // Máximo 5 itens por SC
      const economiaAdministrativa = (grupo.necessidades.length - numeroSCs) * custoAdministrativo;

      const economiaFrete = numeroSCs > 1 ? (numeroSCs - 1) * 80.00 : 0;

      return economiaAdministrativa + economiaFrete;
    }

    // Função para mostrar análise de consolidação
    function mostrarAnaliseConsolidacao(analise) {
      const modal = document.getElementById('modalAcoes');
      const titulo = document.getElementById('modalTitulo');
      const conteudo = document.getElementById('modalConteudo');

      titulo.textContent = '🤖 Análise Inteligente de Consolidação';

      if (analise.oportunidades.length === 0) {
        conteudo.innerHTML = `
          <div style="text-align: center; padding: 40px;">
            <i class="fas fa-check-circle" style="font-size: 48px; color: #28a745; margin-bottom: 20px;"></i>
            <h4>Parabéns! Suas solicitações já estão otimizadas</h4>
            <p>Não foram encontradas oportunidades significativas de consolidação.</p>
            <button class="btn btn-primary" onclick="fecharModal()">Fechar</button>
          </div>
        `;
      } else {
        conteudo.innerHTML = `
          <div class="analise-consolidacao">
            <!-- Resumo do Impacto -->
            <div class="resumo-impacto" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h4>📊 Impacto Total da Otimização:</h4>
              <div class="metricas-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 15px;">
                <div class="metrica" style="text-align: center; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6;">
                  <div style="font-size: 24px; font-weight: bold; color: #28a745;">R$ ${analise.economiaTotal.toFixed(2)}</div>
                  <div style="font-size: 12px; color: #6c757d;">Economia Total</div>
                </div>
                <div class="metrica" style="text-align: center; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6;">
                  <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">-${analise.reducaoSCs}</div>
                  <div style="font-size: 12px; color: #6c757d;">Menos SCs</div>
                </div>
                <div class="metrica" style="text-align: center; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6;">
                  <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${analise.oportunidades.length}</div>
                  <div style="font-size: 12px; color: #6c757d;">Oportunidades</div>
                </div>
              </div>
            </div>

            <!-- Lista de Oportunidades -->
            <div class="oportunidades-lista" style="max-height: 400px; overflow-y: auto;">
              ${analise.oportunidades.map((op, index) => `
                <div class="oportunidade-card ${op.prioridade}" style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 15px; padding: 15px; background: ${op.prioridade === 'alta' ? '#fff3cd' : op.prioridade === 'media' ? '#d1ecf1' : '#f8f9fa'};">
                  <div class="oportunidade-header" style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                    <h5 style="margin: 0; color: #495057;">
                      ${op.tipo === 'PRODUTO_EXATO' ? '🎯' : op.tipo === 'FAMILIA' ? '📦' : '⏳'} ${op.titulo}
                    </h5>
                    <span class="economia" style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                      💰 R$ ${op.economia.toFixed(2)}
                    </span>
                  </div>

                  <div class="oportunidade-descricao" style="margin-bottom: 15px; color: #6c757d;">
                    ${op.descricao}
                  </div>

                  <div class="itens-envolvidos" style="margin-bottom: 15px;">
                    <strong>Itens Envolvidos:</strong>
                    <div style="margin-top: 8px;">
                      ${op.itens.slice(0, 3).map(item => `
                        <span style="display: inline-block; background: #e9ecef; padding: 4px 8px; border-radius: 12px; font-size: 11px; margin: 2px;">
                          ${item.codigo} (${item.qtd.toFixed(2)}) - OP: ${item.op}
                        </span>
                      `).join('')}
                      ${op.itens.length > 3 ? `<span style="color: #6c757d; font-size: 11px;">... e mais ${op.itens.length - 3} itens</span>` : ''}
                    </div>
                  </div>

                  ${op.tipo === 'VALOR_MINIMO' ? `
                    <div style="background: #fff; padding: 10px; border-radius: 4px; margin-bottom: 15px; border-left: 4px solid #ffc107;">
                      <small>
                        <strong>Valor Atual:</strong> R$ ${op.valorAtual.toFixed(2)} |
                        <strong>Valor Mínimo:</strong> R$ ${op.valorMinimo.toFixed(2)} |
                        <strong>Aguardar:</strong> ${op.diasEspera} dias
                      </small>
                    </div>
                  ` : ''}

                  <div class="oportunidade-acoes" style="text-align: right;">
                    <button class="btn btn-success btn-sm" onclick="aplicarConsolidacao('${op.id}', ${index})" style="margin-right: 5px;">
                      ✅ Aplicar
                    </button>
                    <button class="btn btn-info btn-sm" onclick="verDetalhesConsolidacao('${op.id}', ${index})" style="margin-right: 5px;">
                      👁️ Detalhes
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="ignorarSugestao('${op.id}', ${index})">
                      ❌ Ignorar
                    </button>
                  </div>
                </div>
              `).join('')}
            </div>

            <!-- Ações Gerais -->
            <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
              <button class="btn btn-success" onclick="aplicarTodasSugestoes()" style="margin-right: 10px;">
                🚀 Aplicar Todas as Sugestões
              </button>
              <button class="btn btn-secondary" onclick="fecharModal()">
                Fechar
              </button>
            </div>
          </div>
        `;
      }

      modal.style.display = 'block';
    }

    // Função para aplicar consolidação específica
    window.aplicarConsolidacao = async function(oportunidadeId, index) {
      try {
        const analise = window.analiseAtual;
        if (!analise || !analise.oportunidades[index]) {
          alert('Erro: Dados da análise não encontrados.');
          return;
        }

        const oportunidade = analise.oportunidades[index];

        if (!confirm(`Aplicar consolidação: ${oportunidade.titulo}?\n\nEconomia estimada: R$ ${oportunidade.economia.toFixed(2)}`)) {
          return;
        }

        // Criar solicitação consolidada
        await criarSolicitacaoConsolidada(oportunidade);

        alert(`Consolidação aplicada com sucesso!\nEconomia: R$ ${oportunidade.economia.toFixed(2)}`);

        fecharModal();
        await aplicarFiltros();

      } catch (error) {
        console.error('Erro ao aplicar consolidação:', error);
        alert('Erro ao aplicar consolidação: ' + error.message);
      }
    };

    // Função para criar solicitação consolidada
    async function criarSolicitacaoConsolidada(oportunidade) {
      const numeroSolicitacao = await NumberGeneratorService.generateSolicitacaoNumber();

      // Buscar necessidades originais
      const necessidadesOriginais = necessidades.filter(n =>
        oportunidade.necessidadesIds.includes(n.id)
      );

      const solicitacaoData = {
        numero: numeroSolicitacao,
        dataCriacao: Timestamp.now(),
        status: 'PENDENTE',
        tipo: 'CONSOLIDADA',
        origem: 'CONSOLIDACAO_INTELIGENTE',
        tipoConsolidacao: oportunidade.tipo,
        solicitante: 'Sistema de Consolidação',
        departamento: 'PRODUCAO',
        prioridade: oportunidade.prioridade === 'alta' ? 'ALTA' : 'NORMAL',

        // Agrupar itens por produto
        itens: agruparItensConsolidados(necessidadesOriginais),

        // Metadados da consolidação
        consolidacao: {
          oportunidadeId: oportunidade.id,
          economiaEstimada: oportunidade.economia,
          necessidadesOriginais: oportunidade.necessidadesIds,
          opsEnvolvidas: [...new Set(necessidadesOriginais.map(n => n.produtoPai.codigo))],
          criterioConsolidacao: oportunidade.tipo
        },

        justificativa: `Solicitação consolidada automaticamente.\n` +
                      `Tipo: ${oportunidade.titulo}\n` +
                      `Economia estimada: R$ ${oportunidade.economia.toFixed(2)}\n` +
                      `OPs envolvidas: ${[...new Set(necessidadesOriginais.map(n => n.produtoPai.codigo))].join(', ')}`
      };

      // Salvar solicitação consolidada
      const docRef = await addDoc(collection(db, "solicitacoesCompra"), solicitacaoData);

      // Atualizar necessidades originais
      for (const necessidade of necessidadesOriginais) {
        necessidade.status = 'SOLICITADA';
        necessidade.solicitacaoId = docRef.id;
        necessidade.tipoSolicitacao = 'CONSOLIDADA';
        necessidade.quantidadeSolicitada = necessidade.saldoPendente;
        necessidade.ultimaAtualizacao = Timestamp.now();
      }

      return docRef.id;
    }

    // Função para agrupar itens consolidados
    function agruparItensConsolidados(necessidadesOriginais) {
      const itensMap = new Map();

      necessidadesOriginais.forEach(necessidade => {
        const chave = necessidade.produtoId;

        if (!itensMap.has(chave)) {
          itensMap.set(chave, {
            produtoId: necessidade.produtoId,
            codigo: necessidade.produto.codigo,
            descricao: necessidade.produto.descricao,
            unidade: necessidade.produto.unidade,
            quantidade: 0,
            necessidadesOriginais: [],
            ordensOrigem: new Set()
          });
        }

        const item = itensMap.get(chave);
        item.quantidade += necessidade.saldoPendente;
        item.necessidadesOriginais.push(necessidade.id);
        necessidade.ordensProducao.forEach(op => item.ordensOrigem.add(op));
      });

      return Array.from(itensMap.values()).map(item => ({
        ...item,
        ordensOrigem: Array.from(item.ordensOrigem)
      }));
    }

    // Função para aplicar todas as sugestões
    window.aplicarTodasSugestoes = async function() {
      try {
        const analise = window.analiseAtual;
        if (!analise || analise.oportunidades.length === 0) {
          alert('Nenhuma sugestão disponível para aplicar.');
          return;
        }

        if (!confirm(`Aplicar todas as ${analise.oportunidades.length} sugestões?\n\nEconomia total estimada: R$ ${analise.economiaTotal.toFixed(2)}`)) {
          return;
        }

        let sucessos = 0;
        let erros = 0;

        for (let i = 0; i < analise.oportunidades.length; i++) {
          try {
            await criarSolicitacaoConsolidada(analise.oportunidades[i]);
            sucessos++;
          } catch (error) {
            console.error(`Erro ao aplicar sugestão ${i + 1}:`, error);
            erros++;
          }
        }

        alert(`Aplicação concluída!\n\nSucessos: ${sucessos}\nErros: ${erros}\nEconomia total: R$ ${analise.economiaTotal.toFixed(2)}`);

        fecharModal();
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('Erro ao aplicar todas as sugestões:', error);
        alert('Erro ao aplicar sugestões: ' + error.message);
      }
    };

    // Função para ver detalhes da consolidação
    window.verDetalhesConsolidacao = function(oportunidadeId, index) {
      const analise = window.analiseAtual;
      if (!analise || !analise.oportunidades[index]) {
        alert('Dados da oportunidade não encontrados.');
        return;
      }

      const oportunidade = analise.oportunidades[index];

      const modal = document.getElementById('modalAcoes');
      const titulo = document.getElementById('modalTitulo');
      const conteudo = document.getElementById('modalConteudo');

      titulo.textContent = `📋 Detalhes: ${oportunidade.titulo}`;

      conteudo.innerHTML = `
        <div style="margin-bottom: 20px;">
          <h4>Informações da Oportunidade:</h4>
          <p><strong>Tipo:</strong> ${oportunidade.tipo}</p>
          <p><strong>Descrição:</strong> ${oportunidade.descricao}</p>
          <p><strong>Economia Estimada:</strong> R$ ${oportunidade.economia.toFixed(2)}</p>
          <p><strong>Prioridade:</strong> ${oportunidade.prioridade.toUpperCase()}</p>
        </div>

        <div style="margin-bottom: 20px;">
          <h4>Itens Envolvidos (${oportunidade.itens.length}):</h4>
          <div style="max-height: 300px; overflow-y: auto;">
            <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
              <thead>
                <tr style="background: #f8f9fa;">
                  <th style="padding: 8px; border: 1px solid #dee2e6;">Código</th>
                  <th style="padding: 8px; border: 1px solid #dee2e6;">Descrição</th>
                  <th style="padding: 8px; border: 1px solid #dee2e6;">Quantidade</th>
                  <th style="padding: 8px; border: 1px solid #dee2e6;">OP Origem</th>
                </tr>
              </thead>
              <tbody>
                ${oportunidade.itens.map(item => `
                  <tr>
                    <td style="padding: 6px; border: 1px solid #dee2e6;">${item.codigo}</td>
                    <td style="padding: 6px; border: 1px solid #dee2e6;">${item.descricao}</td>
                    <td style="padding: 6px; border: 1px solid #dee2e6;">${item.qtd.toFixed(2)}</td>
                    <td style="padding: 6px; border: 1px solid #dee2e6;">${item.op}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <div style="text-align: right;">
          <button class="btn btn-success" onclick="aplicarConsolidacao('${oportunidade.id}', ${index})" style="margin-right: 10px;">
            ✅ Aplicar Esta Sugestão
          </button>
          <button class="btn btn-secondary" onclick="fecharModal()">
            Fechar
          </button>
        </div>
      `;

      modal.style.display = 'block';
    };

    // Função para ignorar sugestão
    window.ignorarSugestao = function(oportunidadeId, index) {
      if (!confirm('Ignorar esta sugestão?')) {
        return;
      }

      const analise = window.analiseAtual;
      if (analise && analise.oportunidades[index]) {
        analise.oportunidades.splice(index, 1);
        mostrarAnaliseConsolidacao(analise);
      }
    };

    // Funções auxiliares do modal e outras ações
    window.fecharModal = function() {
      document.getElementById('modalAcoes').style.display = 'none';
    };

    window.criarSolicitacao = function(necessidadeId) {
      alert(`Função de criar solicitação individual para necessidade ${necessidadeId} será implementada.`);
    };

    // ✅ FUNÇÃO PARA CANCELAR MÚLTIPLAS NECESSIDADES SELECIONADAS
    window.cancelarSelecionados = function() {
      const checkboxesSelecionados = document.querySelectorAll('.necessidade-checkbox:checked');

      if (checkboxesSelecionados.length === 0) {
        alert('Selecione pelo menos uma necessidade para cancelar.');
        return;
      }

      const necessidadesSelecionadas = Array.from(checkboxesSelecionados).map(checkbox => {
        return necessidades.find(n => n.id === checkbox.value);
      }).filter(n => n && n.status === 'PENDENTE');

      if (necessidadesSelecionadas.length === 0) {
        alert('Nenhuma necessidade selecionada está pendente para cancelamento.');
        return;
      }

      if (necessidadesSelecionadas.length !== checkboxesSelecionados.length) {
        const pendentes = necessidadesSelecionadas.length;
        const total = checkboxesSelecionados.length;
        if (!confirm(`Apenas ${pendentes} de ${total} necessidades selecionadas podem ser canceladas (status PENDENTE).\n\nDeseja continuar com o cancelamento em lote?`)) {
          return;
        }
      }

      // Abrir modal de cancelamento em lote
      abrirModalCancelamentoLote(necessidadesSelecionadas);
    };

    // ✅ FUNÇÃO PARA CANCELAR NECESSIDADE INDIVIDUAL COM MODAL
    window.cancelarNecessidade = function(necessidadeId) {
      const necessidade = necessidades.find(n => n.id === necessidadeId);
      if (!necessidade) {
        alert('Necessidade não encontrada!');
        return;
      }

      // Verificar se pode cancelar
      if (necessidade.status !== 'PENDENTE') {
        alert('Apenas necessidades pendentes podem ser canceladas!');
        return;
      }

      // Preencher dados da necessidade no modal
      document.getElementById('dadosNecessidadeCancelamento').innerHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
          <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0;">
            <div style="margin-bottom: 12px;">
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                📦 Produto
              </label>
              <div style="font-size: 14px; color: #3a3b45; line-height: 1.4;">
                <strong>${necessidade.produto.codigo}</strong><br>
                ${necessidade.produto.descricao}
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                📊 Quantidade
              </label>
              <div style="font-size: 16px; color: #e74a3b; font-weight: bold;">
                ${necessidade.saldoPendente} ${necessidade.produto.unidade}
              </div>
            </div>
          </div>

          <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0;">
            <div style="margin-bottom: 12px;">
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                📅 Data Necessidade
              </label>
              <div style="font-size: 14px; color: #3a3b45;">
                ${necessidade.dataNecessidade ? new Date(necessidade.dataNecessidade.seconds * 1000).toLocaleDateString('pt-BR') : 'N/A'}
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                🏭 Ordens de Produção
              </label>
              <div style="font-size: 14px; color: #3a3b45; line-height: 1.4;">
                ${necessidade.ordensProducao ? necessidade.ordensProducao.join(', ') : 'N/A'}
              </div>
            </div>
          </div>
        </div>

        <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0;">
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                👥 Família
              </label>
              <div style="font-size: 14px; color: #3a3b45;">
                ${necessidade.produto.familia || 'Sem Família'}
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                📋 Status
              </label>
              <div style="font-size: 14px; color: #28a745; font-weight: bold;">
                ${necessidade.status}
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                💰 Valor Estimado
              </label>
              <div style="font-size: 14px; color: #3a3b45;">
                R$ ${((necessidade.saldoPendente * (necessidade.produto.precoUnitario || 0))).toFixed(2)}
              </div>
            </div>
          </div>
        </div>
      `;

      // Preencher informações do cancelamento
      document.getElementById('canceladoPor').textContent = currentUser.nome || 'Sistema';
      document.getElementById('dataCancelamento').textContent = new Date().toLocaleString('pt-BR');

      // Armazenar ID da necessidade para uso posterior
      window.necessidadeParaCancelar = necessidadeId;

      // Limpar formulário
      document.getElementById('formCancelamento').reset();

      // Abrir modal
      document.getElementById('modalCancelamento').style.display = 'block';
    };

    window.verSolicitacao = function(solicitacaoId) {
      alert(`Visualizar solicitação ${solicitacaoId}`);
    };

    window.verPedido = function(pedidoId) {
      alert(`Visualizar pedido ${pedidoId}`);
    };

    window.empenharMaterial = function(necessidadeId) {
      alert(`Empenhar material da necessidade ${necessidadeId}`);
    };

    window.verHistorico = function(necessidadeId) {
      alert(`Histórico da necessidade ${necessidadeId}`);
    };





    // ✅ FUNÇÃO PARA ABRIR MODAL DE CANCELAMENTO EM LOTE
    function abrirModalCancelamentoLote(necessidadesSelecionadas) {
      // Calcular estatísticas
      const totalItens = necessidadesSelecionadas.length;
      const familias = [...new Set(necessidadesSelecionadas.map(n => n.produto.familia || 'Sem Família'))];
      const valorTotal = necessidadesSelecionadas.reduce((total, n) =>
        total + (n.saldoPendente * (n.produto.precoUnitario || 0)), 0
      );
      const ordensEnvolvidas = [...new Set(necessidadesSelecionadas.flatMap(n => n.ordensProducao))];

      // Preencher dados do cancelamento em lote
      document.getElementById('dadosNecessidadeCancelamentoLote').innerHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
          <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0;">
            <div style="margin-bottom: 12px;">
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                📦 Total de Itens
              </label>
              <div style="font-size: 24px; color: #e74a3b; font-weight: bold;">
                ${totalItens} necessidades
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                💰 Valor Total Estimado
              </label>
              <div style="font-size: 18px; color: #e74a3b; font-weight: bold;">
                R$ ${valorTotal.toFixed(2)}
              </div>
            </div>
          </div>

          <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0;">
            <div style="margin-bottom: 12px;">
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                👥 Famílias Envolvidas
              </label>
              <div style="font-size: 14px; color: #3a3b45; line-height: 1.4;">
                ${familias.join(', ')}
              </div>
            </div>
            <div>
              <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 5px; display: block;">
                🏭 Ordens de Produção
              </label>
              <div style="font-size: 14px; color: #3a3b45; line-height: 1.4;">
                ${ordensEnvolvidas.length} ordens: ${ordensEnvolvidas.slice(0, 5).join(', ')}${ordensEnvolvidas.length > 5 ? '...' : ''}
              </div>
            </div>
          </div>
        </div>

        <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e3e6f0; max-height: 200px; overflow-y: auto;">
          <label style="font-weight: 600; color: #5a5c69; font-size: 12px; text-transform: uppercase; margin-bottom: 10px; display: block;">
            📋 Lista de Itens a Cancelar
          </label>
          <div style="font-size: 13px;">
            ${necessidadesSelecionadas.map((n, index) => `
              <div style="padding: 8px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                <span><strong>${n.produto.codigo}</strong> - ${n.produto.descricao}</span>
                <span style="color: #e74a3b; font-weight: bold;">${n.saldoPendente} ${n.produto.unidade}</span>
              </div>
            `).join('')}
          </div>
        </div>
      `;

      // Armazenar necessidades para cancelamento
      window.necessidadesParaCancelarLote = necessidadesSelecionadas;

      // Preencher informações do cancelamento
      document.getElementById('canceladoPorLote').textContent = currentUser.nome || 'Sistema';
      document.getElementById('dataCancelamentoLote').textContent = new Date().toLocaleString('pt-BR');

      // Limpar formulário
      document.getElementById('formCancelamentoLote').reset();

      // Abrir modal
      document.getElementById('modalCancelamentoLote').style.display = 'block';
    }

    // ✅ FUNÇÃO PARA FECHAR MODAL DE CANCELAMENTO
    window.fecharModalCancelamento = function() {
      document.getElementById('modalCancelamento').style.display = 'none';
      window.necessidadeParaCancelar = null;
    };

    // ✅ FUNÇÃO PARA FECHAR MODAL DE CANCELAMENTO EM LOTE
    window.fecharModalCancelamentoLote = function() {
      document.getElementById('modalCancelamentoLote').style.display = 'none';
      window.necessidadesParaCancelarLote = null;
    };

    // ✅ FUNÇÃO PARA CONFIRMAR CANCELAMENTO EM LOTE
    window.confirmarCancelamentoLote = async function() {
      const motivo = document.getElementById('motivoCancelamentoLote').value;
      const observacoes = document.getElementById('observacoesCancelamentoLote').value;

      if (!motivo) {
        alert('Por favor, selecione o motivo do cancelamento!');
        return;
      }

      if (!window.necessidadesParaCancelarLote || window.necessidadesParaCancelarLote.length === 0) {
        alert('Erro: Nenhuma necessidade identificada para cancelamento!');
        return;
      }

      const totalItens = window.necessidadesParaCancelarLote.length;

      if (!confirm(`Confirma o cancelamento de ${totalItens} necessidades?\n\nEsta ação não pode ser desfeita facilmente.`)) {
        return;
      }

      try {
        let sucessos = 0;
        let erros = 0;
        const errosDetalhados = [];

        // Processar cada necessidade
        for (const necessidadeLocal of window.necessidadesParaCancelarLote) {
          try {
            // Preparar dados do cancelamento
            const dadosCancelamento = {
              status: 'CANCELADA',
              motivoCancelamento: motivo,
              observacoesCancelamento: observacoes,
              canceladoPor: currentUser.nome || 'Sistema',
              canceladoPorId: currentUser.id || 'sistema',
              dataCancelamento: new Date(),
              ultimaAtualizacao: new Date()
            };

            // Atualizar dados locais
            Object.assign(necessidadeLocal, dadosCancelamento);

            // Salvar log do cancelamento no Firebase
            const cancelamentoData = {
              necessidadeId: necessidadeLocal.id,
              produtoId: necessidadeLocal.produtoId,
              produtoCodigo: necessidadeLocal.produto.codigo,
              produtoDescricao: necessidadeLocal.produto.descricao,
              quantidadeNecessaria: necessidadeLocal.quantidadeNecessaria,
              ordensProducao: necessidadeLocal.ordensProducao,
              cancelamentoLote: true,
              loteInfo: {
                totalItens: totalItens,
                processadoEm: new Date()
              },
              ...dadosCancelamento
            };

            const docRef = await addDoc(collection(db, "logsCancelamentos"), cancelamentoData);

            // Atualizar cache local
            const chave = `${necessidadeLocal.produtoId}_${necessidadeLocal.ordensProducao.join('_')}`;
            cancelamentosCache.set(chave, {
              id: docRef.id,
              ...cancelamentoData
            });

            sucessos++;
          } catch (error) {
            erros++;
            errosDetalhados.push(`${necessidadeLocal.produto.codigo}: ${error.message}`);
            console.error(`❌ Erro ao cancelar ${necessidadeLocal.produto.codigo}:`, error);
          }
        }

        // Fechar modal
        fecharModalCancelamentoLote();

        // Mostrar resultado
        let mensagem = `✅ Cancelamento em lote concluído!\n\n`;
        mensagem += `✅ Sucessos: ${sucessos}\n`;
        if (erros > 0) {
          mensagem += `❌ Erros: ${erros}\n\n`;
          mensagem += `Detalhes dos erros:\n${errosDetalhados.join('\n')}`;
        }

        alert(mensagem);

        // Desmarcar checkboxes
        document.querySelectorAll('.necessidade-checkbox:checked').forEach(cb => cb.checked = false);
        atualizarContadorSelecionados();

        // Recarregar dados e aplicar filtros
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('❌ Erro geral no cancelamento em lote:', error);
        alert('❌ Erro no cancelamento em lote: ' + error.message);
      }
    };

    // ✅ FUNÇÃO PARA CONFIRMAR CANCELAMENTO INDIVIDUAL
    window.confirmarCancelamento = async function() {
      const motivo = document.getElementById('motivoCancelamento').value;
      const observacoes = document.getElementById('observacoesCancelamento').value;

      if (!motivo) {
        alert('Por favor, selecione o motivo do cancelamento.');
        return;
      }

      if (!window.necessidadeParaCancelar) {
        alert('Erro: Necessidade não identificada!');
        return;
      }

      try {
        // Buscar a necessidade nos dados locais
        const necessidadeLocal = necessidades.find(n => n.id === window.necessidadeParaCancelar);
        if (!necessidadeLocal) {
          alert('Necessidade não encontrada!');
          return;
        }

        // Preparar dados do cancelamento
        const dadosCancelamento = {
          status: 'CANCELADA',
          motivoCancelamento: motivo,
          observacoesCancelamento: observacoes,
          canceladoPor: currentUser.nome || 'Sistema',
          canceladoPorId: currentUser.id || 'sistema',
          dataCancelamento: new Date(),
          ultimaAtualizacao: new Date()
        };

        // Atualizar dados locais (as necessidades são geradas dinamicamente, não salvas no Firebase)
        Object.assign(necessidadeLocal, dadosCancelamento);

        // Salvar log do cancelamento no Firebase e atualizar cache local
        try {
          const cancelamentoData = {
            necessidadeId: window.necessidadeParaCancelar,
            produtoId: necessidadeLocal.produtoId,
            produtoCodigo: necessidadeLocal.produto.codigo,
            produtoDescricao: necessidadeLocal.produto.descricao,
            quantidadeNecessaria: necessidadeLocal.quantidadeNecessaria,
            ordensProducao: necessidadeLocal.ordensProducao,
            ...dadosCancelamento
          };

          const docRef = await addDoc(collection(db, "logsCancelamentos"), cancelamentoData);

          // Atualizar cache local para filtrar imediatamente
          const chave = `${necessidadeLocal.produtoId}_${necessidadeLocal.ordensProducao.join('_')}`;
          cancelamentosCache.set(chave, {
            id: docRef.id,
            ...cancelamentoData
          });

          console.log(`✅ Cancelamento salvo e adicionado ao cache: ${chave}`);
        } catch (logError) {
          console.error('❌ Erro ao salvar log de cancelamento:', logError);
          alert('Erro ao salvar cancelamento: ' + logError.message);
          return;
        }

        // Fechar modal
        fecharModalCancelamento();

        // Mostrar mensagem de sucesso
        alert('✅ Necessidade cancelada com sucesso!');

        // Recarregar dados e aplicar filtros
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('Erro ao cancelar necessidade:', error);
        alert('❌ Erro ao cancelar necessidade: ' + error.message);
      }
    };

    // ✅ FUNÇÃO PARA VER DETALHES DO CANCELAMENTO
    window.verDetalhesCancelamento = function(necessidadeId) {
      const necessidade = necessidades.find(n => n.id === necessidadeId);
      if (!necessidade) {
        alert('Necessidade não encontrada!');
        return;
      }

      if (necessidade.status !== 'CANCELADA') {
        alert('Esta necessidade não foi cancelada!');
        return;
      }

      const motivosMap = {
        'ERRO_PLANEJAMENTO': 'Erro no Planejamento',
        'MUDANCA_PRODUTO': 'Mudança de Produto',
        'CANCELAMENTO_OP': 'Cancelamento da OP',
        'MATERIAL_DISPONIVEL': 'Material Já Disponível',
        'FORNECEDOR_INDISPONIVEL': 'Fornecedor Indisponível',
        'ORCAMENTO_INSUFICIENTE': 'Orçamento Insuficiente',
        'PRAZO_INADEQUADO': 'Prazo Inadequado',
        'SUBSTITUICAO_MATERIAL': 'Substituição de Material',
        'OUTRO': 'Outro Motivo'
      };

      const motivoTexto = motivosMap[necessidade.motivoCancelamento] || necessidade.motivoCancelamento || 'Não informado';
      const dataCancelamento = necessidade.dataCancelamento ?
        new Date(necessidade.dataCancelamento.seconds * 1000).toLocaleString('pt-BR') : 'Não informado';

      alert(`🚫 NECESSIDADE CANCELADA

📦 Produto: ${necessidade.produto.codigo} - ${necessidade.produto.descricao}
📊 Quantidade: ${necessidade.saldoPendente} ${necessidade.produto.unidade}

❌ Motivo: ${motivoTexto}
📝 Observações: ${necessidade.observacoesCancelamento || 'Nenhuma observação'}

👤 Cancelado por: ${necessidade.canceladoPor || 'Não informado'}
📅 Data/Hora: ${dataCancelamento}`);
    };



    window.verNecessidadesCanceladas = function() {
      // Mostrar necessidades canceladas carregando do cache
      const canceladas = Array.from(cancelamentosCache.values());

      if (canceladas.length === 0) {
        alert('Nenhuma necessidade cancelada encontrada.');
        return;
      }

      let html = '<h3>📋 Necessidades Canceladas</h3><table border="1" style="width: 100%; margin-top: 10px;"><thead><tr><th>Produto</th><th>Descrição</th><th>Quantidade</th><th>Motivo</th><th>Data</th><th>Ações</th></tr></thead><tbody>';

      canceladas.forEach(cancelamento => {
        const data = cancelamento.dataCancelamento ?
          (cancelamento.dataCancelamento.toDate ? cancelamento.dataCancelamento.toDate() : new Date(cancelamento.dataCancelamento))
          : new Date();

        html += `
          <tr>
            <td>${cancelamento.produtoCodigo}</td>
            <td>${cancelamento.produtoDescricao}</td>
            <td>${cancelamento.quantidadeNecessaria}</td>
            <td>${cancelamento.motivoCancelamento}</td>
            <td>${data.toLocaleString('pt-BR')}</td>
            <td>
              <button onclick="reativarNecessidade('${cancelamento.id}')" class="btn btn-sm btn-success">
                <i class="fas fa-undo"></i> Reativar
              </button>
            </td>
          </tr>
        `;
      });

      html += '</tbody></table>';

      const modal = document.getElementById('modalAcoes');
      modal.querySelector('.modal-body').innerHTML = html;
      modal.style.display = 'block';
    };

    // Função para reativar uma necessidade cancelada
    window.reativarNecessidade = async function(cancelamentoId) {
      if (!confirm('Tem certeza que deseja reativar esta necessidade?')) {
        return;
      }

      try {
        // Remover do Firebase
        await updateDoc(doc(db, "logsCancelamentos", cancelamentoId), {
          status: 'REATIVADA',
          reativadoPor: currentUser.nome,
          dataReativacao: new Date()
        });

        // Remover do cache local
        for (let [chave, cancelamento] of cancelamentosCache.entries()) {
          if (cancelamento.id === cancelamentoId) {
            cancelamentosCache.delete(chave);
            break;
          }
        }

        alert('✅ Necessidade reativada com sucesso!');
        fecharModal();

        // Recarregar dados
        await carregarDados();
        await aplicarFiltros();

      } catch (error) {
        console.error('❌ Erro ao reativar necessidade:', error);
        alert('❌ Erro ao reativar necessidade: ' + error.message);
      }
    };

    // Fechar modal ao clicar fora
    window.onclick = function(event) {
      const modal = document.getElementById('modalAcoes');
      if (event.target === modal) {
        fecharModal();
      }

      const modalCancelamento = document.getElementById('modalCancelamento');
      if (event.target === modalCancelamento) {
        fecharModalCancelamento();
      }

      const modalCancelamentoLote = document.getElementById('modalCancelamentoLote');
      if (event.target === modalCancelamentoLote) {
        fecharModalCancelamentoLote();
      }
    };
  </script>

  <!-- Modal de Cancelamento de Necessidade -->
  <div id="modalCancelamento" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 900px; width: 95%; max-height: 90vh; overflow-y: auto;">
      <div class="modal-header" style="background: #dc3545; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h3 style="margin: 0; font-size: 24px;"><i class="fas fa-times-circle"></i> Cancelar Necessidade</h3>
        <span class="close" onclick="fecharModalCancelamento()" style="font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
      </div>

      <div class="modal-body" style="padding: 25px; line-height: 1.6;">
        <!-- Dados da Necessidade -->
        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; margin-bottom: 25px; border-left: 5px solid #007bff;">
          <h4 style="color: #495057; margin-bottom: 15px; font-size: 18px;">
            <i class="fas fa-info-circle" style="color: #007bff;"></i> Informações da Necessidade
          </h4>
          <div id="dadosNecessidadeCancelamento" style="font-size: 15px;">
            <!-- Dados da necessidade serão inseridos aqui -->
          </div>
        </div>

        <form id="formCancelamento">
          <!-- Motivo do Cancelamento -->
          <div style="margin-bottom: 25px;">
            <label for="motivoCancelamento" style="display: block; font-weight: bold; color: #dc3545; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-exclamation-triangle"></i> Motivo do Cancelamento *
            </label>
            <select id="motivoCancelamento" required
                    style="width: 100%; padding: 12px; border: 2px solid #dc3545; border-radius: 8px; font-size: 15px; background: white;">
              <option value="">Selecione o motivo...</option>
              <option value="ERRO_PLANEJAMENTO">Erro no Planejamento</option>
              <option value="MUDANCA_PRODUTO">Mudança de Produto</option>
              <option value="CANCELAMENTO_OP">Cancelamento da OP</option>
              <option value="MATERIAL_DISPONIVEL">Material Já Disponível</option>
              <option value="FORNECEDOR_INDISPONIVEL">Fornecedor Indisponível</option>
              <option value="ORCAMENTO_INSUFICIENTE">Orçamento Insuficiente</option>
              <option value="PRAZO_INADEQUADO">Prazo Inadequado</option>
              <option value="SUBSTITUICAO_MATERIAL">Substituição de Material</option>
              <option value="OUTRO">Outro Motivo</option>
            </select>
          </div>

          <!-- Observações Detalhadas -->
          <div style="margin-bottom: 25px;">
            <label for="observacoesCancelamento" style="display: block; font-weight: bold; color: #495057; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-comment"></i> Observações Detalhadas
            </label>
            <textarea id="observacoesCancelamento" rows="5"
                      style="width: 100%; padding: 12px; border: 2px solid #ced4da; border-radius: 8px; font-size: 14px; resize: vertical; font-family: inherit;"
                      placeholder="Descreva detalhadamente o motivo do cancelamento...&#10;&#10;• Explique as circunstâncias&#10;• Mencione impactos no planejamento&#10;• Indique ações alternativas se houver"></textarea>
          </div>

          <!-- Informações do Cancelamento -->
          <div style="margin-bottom: 20px;">
            <label style="display: block; font-weight: bold; color: #6c757d; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-user-clock"></i> Informações do Cancelamento
            </label>
            <div style="background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%); padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 14px;">
                <div>
                  <strong style="color: #495057;">👤 Cancelado por:</strong><br>
                  <span id="canceladoPor" style="color: #6c757d;"></span>
                </div>
                <div>
                  <strong style="color: #495057;">📅 Data/Hora:</strong><br>
                  <span id="dataCancelamento" style="color: #6c757d;"></span>
                </div>
              </div>
            </div>
          </div>

          <!-- Aviso Importante -->
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; color: #856404;">
              <i class="fas fa-exclamation-triangle" style="font-size: 20px; margin-right: 10px;"></i>
              <div>
                <strong>Atenção:</strong> Esta ação não pode ser desfeita facilmente. O cancelamento será registrado permanentemente no sistema.
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer" style="padding: 20px; background: #f8f9fa; border-radius: 0 0 8px 8px; display: flex; justify-content: space-between; gap: 15px;">
        <button type="button" onclick="fecharModalCancelamento()"
                style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
        <button type="button" onclick="confirmarCancelamento()"
                style="padding: 12px 24px; background: #dc3545; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
          <i class="fas fa-times-circle"></i> Confirmar Cancelamento
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de Cancelamento em Lote -->
  <div id="modalCancelamentoLote" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 1000px; width: 95%; max-height: 90vh; overflow-y: auto;">
      <div class="modal-header" style="background: #dc3545; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h3 style="margin: 0; font-size: 24px;"><i class="fas fa-times-circle"></i> Cancelamento em Lote</h3>
        <span class="close" onclick="fecharModalCancelamentoLote()" style="font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
      </div>

      <div class="modal-body" style="padding: 25px; line-height: 1.6;">
        <!-- Dados do Cancelamento em Lote -->
        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; margin-bottom: 25px; border-left: 5px solid #dc3545;">
          <h4 style="color: #495057; margin-bottom: 15px; font-size: 18px;">
            <i class="fas fa-list" style="color: #dc3545;"></i> Resumo do Cancelamento em Lote
          </h4>
          <div id="dadosNecessidadeCancelamentoLote" style="font-size: 15px;">
            <!-- Dados do lote serão inseridos aqui -->
          </div>
        </div>

        <form id="formCancelamentoLote">
          <!-- Motivo do Cancelamento -->
          <div style="margin-bottom: 25px;">
            <label for="motivoCancelamentoLote" style="display: block; font-weight: bold; color: #dc3545; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-exclamation-triangle"></i> Motivo do Cancelamento em Lote *
            </label>
            <select id="motivoCancelamentoLote" required
                    style="width: 100%; padding: 12px; border: 2px solid #dc3545; border-radius: 8px; font-size: 15px; background: white;">
              <option value="">Selecione o motivo...</option>
              <option value="ERRO_PLANEJAMENTO">Erro no Planejamento</option>
              <option value="MUDANCA_PRODUTO">Mudança de Produto</option>
              <option value="CANCELAMENTO_OP">Cancelamento da OP</option>
              <option value="MATERIAL_DISPONIVEL">Material Já Disponível</option>
              <option value="FORNECEDOR_INDISPONIVEL">Fornecedor Indisponível</option>
              <option value="ORCAMENTO_INSUFICIENTE">Orçamento Insuficiente</option>
              <option value="PRAZO_INADEQUADO">Prazo Inadequado</option>
              <option value="SUBSTITUICAO_MATERIAL">Substituição de Material</option>
              <option value="CANCELAMENTO_LOTE">Cancelamento em Lote</option>
              <option value="OUTRO">Outro Motivo</option>
            </select>
          </div>

          <!-- Observações Detalhadas -->
          <div style="margin-bottom: 25px;">
            <label for="observacoesCancelamentoLote" style="display: block; font-weight: bold; color: #495057; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-comment"></i> Observações Detalhadas do Lote
            </label>
            <textarea id="observacoesCancelamentoLote" rows="5"
                      style="width: 100%; padding: 12px; border: 2px solid #ced4da; border-radius: 8px; font-size: 14px; resize: vertical; font-family: inherit;"
                      placeholder="Descreva o motivo do cancelamento em lote...&#10;&#10;• Explique as circunstâncias que levaram ao cancelamento múltiplo&#10;• Mencione impactos no planejamento geral&#10;• Indique ações alternativas se houver"></textarea>
          </div>

          <!-- Informações do Cancelamento -->
          <div style="margin-bottom: 20px;">
            <label style="display: block; font-weight: bold; color: #6c757d; margin-bottom: 10px; font-size: 16px;">
              <i class="fas fa-user-clock"></i> Informações do Cancelamento
            </label>
            <div style="background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%); padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 14px;">
                <div>
                  <strong style="color: #495057;">👤 Cancelado por:</strong><br>
                  <span id="canceladoPorLote" style="color: #6c757d;"></span>
                </div>
                <div>
                  <strong style="color: #495057;">📅 Data/Hora:</strong><br>
                  <span id="dataCancelamentoLote" style="color: #6c757d;"></span>
                </div>
              </div>
            </div>
          </div>

          <!-- Aviso Importante -->
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; color: #856404;">
              <i class="fas fa-exclamation-triangle" style="font-size: 20px; margin-right: 10px;"></i>
              <div>
                <strong>Atenção:</strong> Esta ação cancelará MÚLTIPLAS necessidades de uma só vez. O cancelamento será registrado permanentemente no sistema para cada item.
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer" style="padding: 20px; background: #f8f9fa; border-radius: 0 0 8px 8px; display: flex; justify-content: space-between; gap: 15px;">
        <button type="button" onclick="fecharModalCancelamentoLote()"
                style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
          <i class="fas fa-arrow-left"></i> Voltar
        </button>
        <button type="button" onclick="confirmarCancelamentoLote()"
                style="padding: 12px 24px; background: #dc3545; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
          <i class="fas fa-times-circle"></i> Confirmar Cancelamento em Lote
        </button>
      </div>
    </div>
  </div>

</body>
</html>