firebase-config.js:108 🔥 Firebase inicializado centralmente: {projectId: 'banco-mrp', timestamp: '2025-07-01T11:06:40.758Z', apps: 1}
recebimento_materiais_avancado.html:1101 Configurações carregadas: {controleQualidade: false, armazemQualidade: false, inspecaoRecebimento: 'todos', controleQualidadeObrigatorio: false, armazemPadrao: '', …}
recebimento_materiais_avancado.html:1177 Dados carregados: {pedidos: 39, produtos: 1669, armazens: 9, fornecedores: 778, tesConfig: 'Usando configuração existente'}
recebimento_materiais_avancado.html:1187 📋 Exemplo de pedido: {id: '1KP4vCuA8dDapMhmiDBL', aprovadoPor: 'Alex', fornecedorId: 'xJrwvEHQor3ASxHYxUAE', criadoPor: 'Alex', atualizacoesEntrega: Array(1), …}
recebimento_materiais_avancado.html:1189 📦 Exemplo de item: {descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', valorTotal: 1805.17, codigo: '110732', unidade: 'PC', quantidade: 1, …}
recebimento_materiais_avancado.html:1193 🏷️ Exemplo de produto: {id: '090MA115juj45fsZ1OMy', fatorConversao: null, prateleira: null, status: 'ativo', pontoPedido: 0, …}
recebimento_materiais_avancado.html:1196 🏢 Exemplo de fornecedor: {id: '00u42UzvuaUBxiJt7z1x', autorizaXml2: false, departamento2: '', email3: '', cnpjCpf: '19.046.302/0001-29', …}
recebimento_materiais_avancado.html:1007 🔍 DIAGNÓSTICO COMPLETO DOS DADOS:
recebimento_materiais_avancado.html:1008 📋 Pedidos de Compra: 39
recebimento_materiais_avancado.html:1009 🏷️ Produtos: 1669
recebimento_materiais_avancado.html:1010 🏢 Fornecedores: 778
recebimento_materiais_avancado.html:1011 🏪 Armazéns: 9
recebimento_materiais_avancado.html:1015 📋 Estrutura do pedido: (22) ['id', 'aprovadoPor', 'fornecedorId', 'criadoPor', 'atualizacoesEntrega', 'ultimaLimpezaCritica', 'prazoEntrega', 'alteradoPor', 'valorTotal', 'condicaoPagamento', 'itens', 'dataCriacao', 'ultimaAtualizacao', 'historico', 'status', 'solicitacaoId', 'limpoPor', 'numero', 'cotacaoId', 'dataAprovacao', 'sincronizadoPor', 'numeroAnterior']
recebimento_materiais_avancado.html:1016 📋 Pedido exemplo: {id: '1KP4vCuA8dDapMhmiDBL', aprovadoPor: 'Alex', fornecedorId: 'xJrwvEHQor3ASxHYxUAE', criadoPor: 'Alex', atualizacoesEntrega: Array(1), …}
recebimento_materiais_avancado.html:1019 📦 Estrutura do item: (7) ['descricao', 'valorTotal', 'codigo', 'unidade', 'quantidade', 'valorUnitario', 'produtoId']
recebimento_materiais_avancado.html:1020 📦 Item exemplo: {descricao: 'ECM-B3M-EM1310RS1 MOTOR 1,0KW SEM FREIO', valorTotal: 1805.17, codigo: '110732', unidade: 'PC', quantidade: 1, …}
recebimento_materiais_avancado.html:1025 🏢 Estrutura do fornecedor: (71) ['id', 'autorizaXml2', 'departamento2', 'email3', 'cnpjCpf', 'endereco', 'observacoes', 'email', 'celular2', 'inscricaoMunicipal', 'bairro', 'email2', 'limite', 'pais', 'reducao', 'telefone2', 'cnpjCpf2', 'homePage', 'telefone1', 'cep', 'email1', 'estado', 'ultimaCompra', 'contato1', 'dataCadastro', 'telefone4', 'codigo', 'acrescimoCLI', 'celular1', 'numero', 'emailNfe', 'codigoPais', 'inscricaoEstadual', 'codigoRegiao', 'departamento3', 'tipoPessoa', 'autorizaXml1', 'suframa', 'codigoVendedor', 'cargo2', 'categoriaPrincipal', 'tipo', 'razaoSocial', 'latitudeCLI', 'codCusto', 'contaContabil', 'codigoArea', 'complemento', 'im', 'codigoClassificacao', 'intervista', 'contato3', 'departamento1', 'ativo', 'categorias', 'simplesNacional', 'cotacao', 'indicacao', 'telefone3', 'celular3', 'dataAtualizacao', 'contato2', 'nascimento', 'cnpjCpf3', 'cargo3', 'nomeFantasia', 'fax', 'statusHomologacao', 'cidade', 'temSubstituicao', 'longitudeCLI']
recebimento_materiais_avancado.html:1026 🏢 Fornecedor exemplo: {id: '00u42UzvuaUBxiJt7z1x', autorizaXml2: false, departamento2: '', email3: '', cnpjCpf: '19.046.302/0001-29', …}
recebimento_materiais_avancado.html:1030 🏷️ Estrutura do produto: (31) ['id', 'fatorConversao', 'prateleira', 'status', 'pontoPedido', 'descricao', 'precoVenda', 'margemLucro', 'dataCadastro', 'cest', 'inspecaoRecebimento', 'estoqueMinimo', 'custoMedio', 'origem', 'codigo', 'posicao', 'estoqueMaximo', 'unidadeSecundaria', 'tipo', 'ultimoCusto', 'loteCompra', 'metodoCusteio', 'grupo', 'rastreabilidadeLote', 'armazemPadraoId', 'familia', 'corredor', 'centroCustoObrigatorio', 'ncm', 'tipoItem', 'unidade']
recebimento_materiais_avancado.html:1031 🏷️ Produto exemplo: {id: '090MA115juj45fsZ1OMy', fatorConversao: null, prateleira: null, status: 'ativo', pontoPedido: 0, …}
recebimento_materiais_avancado.html:1369 🔄 DEBUG populateOrderSelect - Iniciando atualização da lista de pedidos
recebimento_materiais_avancado.html:1370 📋 DEBUG populateOrderSelect - Total de pedidos: 39
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0003: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0003 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-007593: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-007593 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0015: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0015 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0007: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0007 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0018: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0018 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873716: {status: 'RECEBIDO', temItens: 10}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873716 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0019: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0019 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-628134: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-628134 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0002: {status: 'RECEBIDO', temItens: 6}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0002 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0005: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0005 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-255150: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 112936: 0/25.2 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-255150 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0016: {status: 'APROVADO', temItens: 18}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 000635: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0016 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-558250: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item MPA165: 0/4.25 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-558250 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0009: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0009 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0008: {status: 'APROVADO', temItens: 8}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 108730: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0008 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873648: {status: 'RECEBIDO', temItens: 11}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873648 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-553477: {status: 'APROVADO', temItens: 6}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 101441: 0/4 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-553477 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0013: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 109005: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0013 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0004: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 110570: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0004 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-341813: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item MRB002: 0/10 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-341813 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-949381: {status: 'APROVADO', temItens: 2}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 105239: 0/2 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-949381 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0012: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0012 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-873530: {status: 'RECEBIDO', temItens: 21}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-873530 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-691594: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 104478: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-691594 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-809580: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-809580 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0001: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0001 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0014: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0014 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-332250: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-332250 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-242570: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-242570 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-707010: {status: 'CANCELADO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-707010 rejeitado por status: CANCELADO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-018265: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item MTS001: 0/18 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-018265 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-958454: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2025-958454 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0017: {status: 'RECEBIDO', temItens: 1}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0017 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-970481: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 112201: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-970481 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0010: {status: 'RECEBIDO', temItens: 2}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0010 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0020: {status: 'APROVADO', temItens: 1}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 110389: 0/1 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0020 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0011: {status: 'APROVADO', temItens: 5}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item mcc042: 0/35 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2506-0011 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2025-398609: {status: 'APROVADO', temItens: 3}
recebimento_materiais_avancado.html:1400 📦 DEBUG populateOrderSelect - Item 109651: 0/6 - PENDENTE
recebimento_materiais_avancado.html:1405 ✅ DEBUG populateOrderSelect - Pedido PC-2025-398609 INCLUÍDO na lista
recebimento_materiais_avancado.html:1377 🔍 DEBUG populateOrderSelect - Analisando pedido PC-2506-0006: {status: 'RECEBIDO', temItens: 3}
recebimento_materiais_avancado.html:1385 ❌ DEBUG populateOrderSelect - Pedido PC-2506-0006 rejeitado por status: RECEBIDO (CORRETO: pedidos RECEBIDOS não devem aparecer)
recebimento_materiais_avancado.html:1418 📊 DEBUG populateOrderSelect - Pedidos disponíveis: 15 de 39 total
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-255150 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0016 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-558250 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0008 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-553477 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0013 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0004 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-341813 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-949381 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-691594 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-018265 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-970481 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0020 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2506-0011 (APROVADO)
recebimento_materiais_avancado.html:1442 ✅ DEBUG populateOrderSelect - Adicionado à lista: PC-PC-2025-398609 (APROVADO)
recebimento_materiais_avancado.html:1445 🎯 DEBUG populateOrderSelect - Lista atualizada com 15 pedidos
recebimento_materiais_avancado.html:1348 📊 Dashboard atualizado: {pendentes: 15, atrasados: 0, parciais: 0, completos: 22}
recebimento_materiais_avancado.html:2417 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2426 Dados encontrados: {movimentacoes: 3489, estoqueQualidade: 63, recebimentosDetalhes: 16}
recebimento_materiais_avancado.html:2540 Total de registros de histórico encontrados: 99
recebimento_materiais_avancado.html:1620 🔍 DEBUG selectOrder - orderId selecionado: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1560 🔍 DEBUG selectOrderById - Selecionando pedido por ID: STenDi3OPZy4ieeJIrxk
recebimento_materiais_avancado.html:1569 🔍 DEBUG selectOrderById - currentOrder encontrado: {id: 'STenDi3OPZy4ieeJIrxk', condicoesPagamento: '30DIAS', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', cotacaoId: '4NSB4YyxR5qnj6TphjVl', historico: Array(1), …}
recebimento_materiais_avancado.html:1577 🔍 DEBUG selectOrderById - Iniciando carregamento das informações...
recebimento_materiais_avancado.html:1637 🔍 DEBUG loadSupplierInfo - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', condicoesPagamento: '30DIAS', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', cotacaoId: '4NSB4YyxR5qnj6TphjVl', historico: Array(1), …}
recebimento_materiais_avancado.html:1638 🔍 DEBUG loadSupplierInfo - fornecedorId: YqfHIA1xCUe30ndFcIAj
recebimento_materiais_avancado.html:1639 🔍 DEBUG loadSupplierInfo - fornecedores disponíveis: 778
recebimento_materiais_avancado.html:1642 🔍 DEBUG loadSupplierInfo - CAMPOS DO PEDIDO RELACIONADOS AO FORNECEDOR:
recebimento_materiais_avancado.html:1643 📋 Campos do currentOrder: (22) ['id', 'condicoesPagamento', 'fornecedorNome', 'cotacaoId', 'historico', 'dataUltimaAtualizacao', 'numero', 'cotacaoNumero', 'observacoes', 'status', 'fornecedorId', 'dataRecebimento', 'dataAprovacao', 'ultimoRecebimento', 'recebidoPor', 'itens', 'dataCriacao', 'aprovadoPor', 'valorTotal', 'uidAprovacao', 'criadoPor', 'prazoEntrega']
recebimento_materiais_avancado.html:1644 📄 Dados do fornecedor no pedido: {fornecedorId: 'YqfHIA1xCUe30ndFcIAj', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', fornecedorCnpj: undefined, fornecedorDocumento: undefined, fornecedorContato: undefined, …}
recebimento_materiais_avancado.html:1676 🔍 DEBUG loadSupplierInfo - fornecedor encontrado: {id: 'YqfHIA1xCUe30ndFcIAj', tipoPessoa: 'Juridica', codCusto: '0', statusHomologacao: 'Homologado', autorizaXml2: false, …}
recebimento_materiais_avancado.html:1680 🔍 DEBUG loadSupplierInfo - CAMPOS DO FORNECEDOR:
recebimento_materiais_avancado.html:1681 📋 Campos disponíveis: (70) ['id', 'tipoPessoa', 'codCusto', 'statusHomologacao', 'autorizaXml2', 'cargo2', 'tipo', 'simplesNacional', 'cnpjCpf2', 'celular1', 'emailNfe', 'email3', 'ativo', 'estado', 'email1', 'pais', 'temSubstituicao', 'indicacao', 'telefone2', 'cargo3', 'celular3', 'dataAtualizacao', 'numero', 'observacoes', 'email2', 'departamento2', 'complemento', 'cotacao', 'cnpjCpf3', 'codigoClassificacao', 'endereco', 'codigoPais', 'cidade', 'dataCadastro', 'razaoSocial', 'im', 'telefone3', 'inscricaoMunicipal', 'contato1', 'reducao', 'fax', 'nomeFantasia', 'departamento1', 'contaContabil', 'homePage', 'categorias', 'ultimaCompra', 'acrescimoCLI', 'suframa', 'contato2', 'telefone1', 'codigoVendedor', 'autorizaXml1', 'email', 'codigoRegiao', 'celular2', 'codigo', 'codigoArea', 'cep', 'intervista', 'nascimento', 'telefone4', 'longitudeCLI', 'latitudeCLI', 'inscricaoEstadual', 'bairro', 'departamento3', 'limite', 'cnpjCpf', 'contato3']
recebimento_materiais_avancado.html:1682 📄 CNPJ/CPF campos: {cnpj: undefined, cpfCnpj: undefined, cnpjCpf: '07.686.277/0001-69', documento: undefined, cpf: undefined}
recebimento_materiais_avancado.html:1703 🔍 DEBUG loadSupplierInfo - nome do fornecedor: NOVOPLAST COMERCIO DE PLASTICOS LTDA
recebimento_materiais_avancado.html:999 🔍 DEBUG extrairCnpjCpf - Tentativas: (17) [undefined, undefined, '07.686.277/0001-69', undefined, undefined, '07.686.277/0001-69', undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined]
recebimento_materiais_avancado.html:1000 🔍 DEBUG extrairCnpjCpf - Resultado: 07.686.277/0001-69
recebimento_materiais_avancado.html:1741 ✅ DEBUG loadSupplierInfo - CNPJ definido no elemento HTML: 07.686.277/0001-69
recebimento_materiais_avancado.html:1742 ✅ DEBUG loadSupplierInfo - Conteúdo atual do elemento: 07.686.277/0001-69
recebimento_materiais_avancado.html:1743 ✅ DEBUG loadSupplierInfo - Elemento visível? false
recebimento_materiais_avancado.html:1760 🔍 DEBUG loadSupplierInfo - CNPJ FINAL: 07.686.277/0001-69
recebimento_materiais_avancado.html:1761 🔍 DEBUG loadSupplierInfo - Contato FINAL: teste@teste
recebimento_materiais_avancado.html:1762 🔍 DEBUG loadSupplierInfo - Solicitante FINAL: Alex
recebimento_materiais_avancado.html:1765 🔍 DEBUG loadSupplierInfo - TENTATIVAS CNPJ: {fornecedor?.cnpj: undefined, fornecedor?.cpfCnpj: undefined, fornecedor?.cnpjCpf: '07.686.277/0001-69', fornecedor?.documento: undefined, fornecedor?.cpf: undefined, …}
recebimento_materiais_avancado.html:1590 ✅ DEBUG selectOrderById - Seção do fornecedor mostrada
recebimento_materiais_avancado.html:1899 🔍 DEBUG loadOrderItems - currentOrder: {id: 'STenDi3OPZy4ieeJIrxk', condicoesPagamento: '30DIAS', fornecedorNome: 'NOVOPLAST COMERCIO DE PLASTICOS LTDA', cotacaoId: '4NSB4YyxR5qnj6TphjVl', historico: Array(1), …}
recebimento_materiais_avancado.html:1900 🔍 DEBUG loadOrderItems - itens: (2) [{…}, {…}]
recebimento_materiais_avancado.html:1913 🔍 DEBUG loadOrderItems - Item 0: {precoUnitario: 397, unidade: 'PC', ipi: 0, icms: 0, valorTotal: 794, …}
recebimento_materiais_avancado.html:1916 🔍 DEBUG loadOrderItems - Produto encontrado para item 0: {id: '94M0qmNlWSZt9opark0E', armazemPadraoId: 'BtRauPc2d0XyLfeBOZFj', estoqueMinimo: 0, descricao: 'CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM', pontoPedido: 0, …}
recebimento_materiais_avancado.html:1923 🔍 DEBUG loadOrderItems - Item 0 - Qtd Pedida: 2, Qtd Recebida: 0, Saldo: 2, Valor: 397
recebimento_materiais_avancado.html:1935 🔍 DEBUG loadOrderItems - Item 0 - Código: 105239, Descrição: CHAPA DE POLIPROPILENO 80MM X 100MM X 190MM, Valor: 397
recebimento_materiais_avancado.html:1913 🔍 DEBUG loadOrderItems - Item 1: {valorTotal: 397, precoUnitario: 397, descricao: 'CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM', unidade: 'PC', codigo: '105240', …}
recebimento_materiais_avancado.html:1916 🔍 DEBUG loadOrderItems - Produto encontrado para item 1: {id: 'UPqNBsHPmH3CAwc38Hrp', dataCadastro: {…}, tipo: 'MP', unidade: 'PC', descricao: 'CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM', …}
recebimento_materiais_avancado.html:1923 🔍 DEBUG loadOrderItems - Item 1 - Qtd Pedida: 1, Qtd Recebida: 0, Saldo: 1, Valor: 397
recebimento_materiais_avancado.html:1935 🔍 DEBUG loadOrderItems - Item 1 - Código: 105240, Descrição: CHAPA DE POLIPROPILENO 90MM X 110MM X 210MM, Valor: 397
recebimento_materiais_avancado.html:2299 === CARREGANDO HISTÓRICO DE RECEBIMENTOS ===
recebimento_materiais_avancado.html:2315 Consulta com índice falhou, usando consulta alternativa: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/banco-mrp/firestore/indexes?create_composite=ClZwcm9qZWN0cy9iYW5jby1tcnAvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL21vdmltZW50YWNvZXNFc3RvcXVlL2luZGV4ZXMvXxABGggKBHRpcG8QARoMCghkYXRhSG9yYRACGgwKCF9fbmFtZV9fEAI
loadDeliveryHistory @ recebimento_materiais_avancado.html:2315
await in loadDeliveryHistory
selectOrderById @ recebimento_materiais_avancado.html:1606
handleMouseUp_ @ unknown
await in handleMouseUp_
window.selectOrder @ recebimento_materiais_avancado.html:1633
onchange @ recebimento_materiais_avancado.html:646
handleMouseUp_ @ unknown
recebimento_materiais_avancado.html:2327 Dados encontrados: {movimentacoes: 0, estoqueQualidade: 0, recebimentosDetalhes: 0}
recebimento_materiais_avancado.html:2387 Total de registros de histórico encontrados: 0
recebimento_materiais_avancado.html:1261 TES selecionado: {codigo: '001', descricao: undefined, tipo: 'ENTRADA', atualizaEstoque: true}
recebimento_materiais_avancado.html:2089 Uncaught ReferenceError: updateCurrentReceiptSummary is not defined
    at window.validateQuantity (recebimento_materiais_avancado.html:2089:13)
    at HTMLInputElement.onkeyup (recebimento_materiais_avancado.html:1:1)
window.validateQuantity @ recebimento_materiais_avancado.html:2089
onkeyup @ recebimento_materiais_avancado.html:1
recebimento_materiais_avancado.html:2089 Uncaught ReferenceError: updateCurrentReceiptSummary is not defined
    at window.validateQuantity (recebimento_materiais_avancado.html:2089:13)
    at HTMLInputElement.onchange (recebimento_materiais_avancado.html:1:1)
window.validateQuantity @ recebimento_materiais_avancado.html:2089
onchange @ recebimento_materiais_avancado.html:1
recebimento_materiais_avancado.html:2089 Uncaught ReferenceError: updateCurrentReceiptSummary is not defined
    at window.validateQuantity (recebimento_materiais_avancado.html:2089:13)
    at HTMLInputElement.onkeyup (recebimento_materiais_avancado.html:1:1)
window.validateQuantity @ recebimento_materiais_avancado.html:2089
onkeyup @ recebimento_materiais_avancado.html:1
recebimento_materiais_avancado.html:2089 Uncaught ReferenceError: updateCurrentReceiptSummary is not defined
    at window.validateQuantity (recebimento_materiais_avancado.html:2089:13)
    at HTMLInputElement.onchange (recebimento_materiais_avancado.html:1:1)
window.validateQuantity @ recebimento_materiais_avancado.html:2089
onchange @ recebimento_materiais_avancado.html:1
recebimento_materiais_avancado.html:852 Uncaught ReferenceError: processReceipt is not defined
    at HTMLButtonElement.onclick (recebimento_materiais_avancado.html:852:76)
onclick @ recebimento_materiais_avancado.html:852
