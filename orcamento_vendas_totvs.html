<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orçamento de Vendas - Sistema TOTVS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --totvs-primary: #0854a0;
            --totvs-secondary: #f0f3f6;
            --totvs-success: #107e3e;
            --totvs-warning: #e9730c;
            --totvs-danger: #bb0000;
            --totvs-border: #d4d4d4;
            --totvs-text: #333;
            --totvs-bg: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: var(--totvs-bg);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--totvs-primary) 0%, #0a4d8c 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 140px);
        }

        .sidebar {
            width: 300px;
            background: var(--totvs-secondary);
            border-right: 1px solid var(--totvs-border);
            padding: 20px;
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-section h3 {
            color: var(--totvs-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--totvs-text);
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-control {
            padding: 12px;
            border: 1px solid var(--totvs-border);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--totvs-primary);
            box-shadow: 0 0 0 3px rgba(8, 84, 160, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--totvs-primary);
            color: white;
        }

        .btn-success {
            background: var(--totvs-success);
            color: white;
        }

        .btn-warning {
            background: var(--totvs-warning);
            color: white;
        }

        .btn-danger {
            background: var(--totvs-danger);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .tabs {
            display: flex;
            background: var(--totvs-secondary);
            border-radius: 10px 10px 0 0;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: var(--totvs-secondary);
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: var(--totvs-text);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab.active {
            background: var(--totvs-primary);
            color: white;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 0 0 10px 10px;
            padding: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .item-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .item-table th,
        .item-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--totvs-border);
        }

        .item-table th {
            background: var(--totvs-secondary);
            font-weight: 600;
            color: var(--totvs-text);
        }

        .item-table tbody tr:hover {
            background: #f8f9fa;
        }

        .summary-card {
            background: linear-gradient(135deg, var(--totvs-primary) 0%, #0a4d8c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .summary-row:last-child {
            margin-bottom: 0;
            font-size: 18px;
            font-weight: 600;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 10px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-rascunho {
            background: #ffc107;
            color: #000;
        }

        .status-ativo {
            background: #28a745;
            color: white;
        }

        .status-aprovado {
            background: #17a2b8;
            color: white;
        }

        .status-rejeitado {
            background: #dc3545;
            color: white;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--totvs-success);
        }

        .notification-error {
            background: var(--totvs-danger);
        }

        .notification-info {
            background: var(--totvs-primary);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--totvs-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-box {
            position: relative;
            margin-bottom: 15px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 12px;
            border: 1px solid var(--totvs-border);
            border-radius: 6px;
        }

        .search-box i {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .item-card {
            background: white;
            border: 1px solid var(--totvs-border);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .item-card:hover {
            border-color: var(--totvs-primary);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .item-card.selected {
            border-color: var(--totvs-primary);
            background: rgba(8, 84, 160, 0.05);
        }

        .item-code {
            font-weight: 600;
            color: var(--totvs-primary);
            margin-bottom: 5px;
        }

        .item-description {
            color: var(--totvs-text);
            margin-bottom: 5px;
        }

        .item-price {
            font-weight: 600;
            color: var(--totvs-success);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-calculator"></i>
                Orçamento de Vendas
            </h1>
            <div>
                <button class="btn btn-success" onclick="saveQuote()">
                    <i class="fas fa-save"></i> Salvar
                </button>
                <button class="btn btn-primary" onclick="generatePDF()">
                    <i class="fas fa-file-pdf"></i> PDF
                </button>
                <a href="index.html" class="btn btn-warning">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Sidebar com busca de produtos/serviços -->
            <div class="sidebar">
                <div class="form-section">
                    <h3><i class="fas fa-search"></i> Buscar Itens</h3>
                    
                    <div class="tabs">
                        <button class="tab active" onclick="switchTab('produtos')">
                            <i class="fas fa-box"></i> Produtos
                        </button>
                        <button class="tab" onclick="switchTab('servicos')">
                            <i class="fas fa-tools"></i> Serviços
                        </button>
                    </div>

                    <div id="produtos-tab" class="tab-content active">
                        <div class="search-box">
                            <input type="text" id="searchProdutos" placeholder="Buscar produtos..." onkeyup="searchItems('produtos')">
                            <i class="fas fa-search"></i>
                        </div>
                        <div id="produtosList"></div>
                    </div>

                    <div id="servicos-tab" class="tab-content">
                        <div class="search-box">
                            <input type="text" id="searchServicos" placeholder="Buscar serviços..." onkeyup="searchItems('servicos')">
                            <i class="fas fa-search"></i>
                        </div>
                        <div id="servicosList"></div>
                    </div>
                </div>
            </div>

            <!-- Área principal do orçamento -->
            <div class="content-area">
                <!-- Dados do orçamento -->
                <div class="form-section">
                    <h3><i class="fas fa-file-alt"></i> Dados do Orçamento</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label><i class="fas fa-hashtag"></i> Número</label>
                            <input type="text" id="numeroOrcamento" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-user"></i> Cliente *</label>
                            <select id="clienteSelect" class="form-control" required>
                                <option value="">Selecione o cliente...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-calendar"></i> Data Validade *</label>
                            <input type="date" id="dataValidade" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-info-circle"></i> Status</label>
                            <select id="statusOrcamento" class="form-control">
                                <option value="RASCUNHO">Rascunho</option>
                                <option value="ATIVO">Ativo</option>
                                <option value="APROVADO">Aprovado</option>
                                <option value="REJEITADO">Rejeitado</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label><i class="fas fa-comment"></i> Observações</label>
                            <textarea id="observacoes" class="form-control" rows="3" placeholder="Observações gerais do orçamento..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Itens do orçamento -->
                <div class="form-section">
                    <h3><i class="fas fa-list"></i> Itens do Orçamento</h3>
                    
                    <table class="item-table">
                        <thead>
                            <tr>
                                <th>Tipo</th>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th>Qtd</th>
                                <th>Valor Unit.</th>
                                <th>Total</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="itensTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                                    <i class="fas fa-plus-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                                    Adicione produtos ou serviços ao orçamento
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Resumo financeiro -->
                <div class="summary-card">
                    <h3><i class="fas fa-calculator"></i> Resumo Financeiro</h3>
                    <div class="summary-row">
                        <span>Subtotal Produtos:</span>
                        <span id="subtotalProdutos">R$ 0,00</span>
                    </div>
                    <div class="summary-row">
                        <span>Subtotal Serviços:</span>
                        <span id="subtotalServicos">R$ 0,00</span>
                    </div>
                    <div class="summary-row">
                        <span>Desconto:</span>
                        <span id="desconto">R$ 0,00</span>
                    </div>
                    <div class="summary-row">
                        <span>VALOR TOTAL:</span>
                        <span id="valorTotal">R$ 0,00</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <!-- Scripts -->
    <script type="module">
        // Importações Firebase
        import { db } from './firebase-config.js';
        import {
            collection,
            getDocs,
            addDoc,
            updateDoc,
            doc,
            Timestamp,
            query,
            orderBy
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

        // Variáveis globais
        let produtos = [];
        let servicos = [];
        let clientes = [];
        let itensOrcamento = [];
        let currentQuoteId = null;

        // Inicialização
        document.addEventListener('DOMContentLoaded', async () => {
            await loadInitialData();
            generateQuoteNumber();
            setDefaultDate();
        });

        // Carregar dados iniciais
        async function loadInitialData() {
            try {
                showLoading(true);

                const [produtosSnap, servicosSnap, clientesSnap] = await Promise.all([
                    getDocs(query(collection(db, "produtos"), orderBy("codigo"))),
                    getDocs(query(collection(db, "servicos"), orderBy("codigo"))),
                    getDocs(query(collection(db, "clientes"), orderBy("razaoSocial")))
                ]);

                produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                servicos = servicosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                clientes = clientesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

                populateClientes();
                displayItems('produtos');
                displayItems('servicos');

                console.log('Dados carregados:', {
                    produtos: produtos.length,
                    servicos: servicos.length,
                    clientes: clientes.length
                });

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                showNotification('Erro ao carregar dados do sistema', 'error');
            } finally {
                showLoading(false);
            }
        }

        // Gerar número do orçamento
        function generateQuoteNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const time = now.getTime().toString().slice(-4);

            document.getElementById('numeroOrcamento').value = `ORC${year}${month}${day}${time}`;
        }

        // Definir data padrão (30 dias)
        function setDefaultDate() {
            const today = new Date();
            const futureDate = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
            document.getElementById('dataValidade').value = futureDate.toISOString().split('T')[0];
        }

        // Popular clientes
        function populateClientes() {
            const select = document.getElementById('clienteSelect');
            select.innerHTML = '<option value="">Selecione o cliente...</option>';

            clientes.forEach(cliente => {
                const option = document.createElement('option');
                option.value = cliente.id;
                option.textContent = `${cliente.codigo || ''} - ${cliente.razaoSocial}`;
                select.appendChild(option);
            });
        }

        // Alternar abas
        window.switchTab = function(tabName) {
            // Atualizar abas
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        };

        // Exibir itens (produtos ou serviços)
        function displayItems(type) {
            const container = document.getElementById(`${type}List`);
            const items = type === 'produtos' ? produtos : servicos;

            container.innerHTML = '';

            if (items.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Nenhum ${type.slice(0, -1)} cadastrado
                    </div>
                `;
                return;
            }

            items.forEach(item => {
                const card = document.createElement('div');
                card.className = 'item-card';
                card.onclick = () => addItemToQuote(item, type);

                card.innerHTML = `
                    <div class="item-code">${item.codigo}</div>
                    <div class="item-description">${item.descricao}</div>
                    <div class="item-price">R$ ${(item.preco || 0).toFixed(2)}</div>
                    <small style="color: #666;">
                        <i class="fas fa-cube"></i> ${item.unidade || 'UN'}
                        ${type === 'produtos' ? `| Estoque: ${item.estoque || 0}` : ''}
                    </small>
                `;

                container.appendChild(card);
            });
        }

        // Buscar itens
        window.searchItems = function(type) {
            const searchTerm = document.getElementById(`search${type.charAt(0).toUpperCase() + type.slice(1)}`).value.toLowerCase();
            const items = type === 'produtos' ? produtos : servicos;

            const filteredItems = items.filter(item =>
                item.codigo.toLowerCase().includes(searchTerm) ||
                item.descricao.toLowerCase().includes(searchTerm)
            );

            const container = document.getElementById(`${type}List`);
            container.innerHTML = '';

            filteredItems.forEach(item => {
                const card = document.createElement('div');
                card.className = 'item-card';
                card.onclick = () => addItemToQuote(item, type);

                card.innerHTML = `
                    <div class="item-code">${item.codigo}</div>
                    <div class="item-description">${item.descricao}</div>
                    <div class="item-price">R$ ${(item.preco || 0).toFixed(2)}</div>
                    <small style="color: #666;">
                        <i class="fas fa-cube"></i> ${item.unidade || 'UN'}
                        ${type === 'produtos' ? `| Estoque: ${item.estoque || 0}` : ''}
                    </small>
                `;

                container.appendChild(card);
            });
        };

        // Adicionar item ao orçamento
        function addItemToQuote(item, type) {
            // Verificar se item já existe
            const existingIndex = itensOrcamento.findIndex(i => i.id === item.id && i.tipo === type);

            if (existingIndex >= 0) {
                // Incrementar quantidade
                itensOrcamento[existingIndex].quantidade += 1;
                itensOrcamento[existingIndex].total = itensOrcamento[existingIndex].quantidade * itensOrcamento[existingIndex].valorUnitario;
            } else {
                // Adicionar novo item
                itensOrcamento.push({
                    id: item.id,
                    tipo: type.slice(0, -1).toUpperCase(), // PRODUTO ou SERVICO
                    codigo: item.codigo,
                    descricao: item.descricao,
                    quantidade: 1,
                    valorUnitario: item.preco || 0,
                    total: item.preco || 0,
                    unidade: item.unidade || 'UN'
                });
            }

            updateItemsTable();
            updateSummary();
            showNotification(`${item.codigo} adicionado ao orçamento`, 'success');
        }

        // Atualizar tabela de itens
        function updateItemsTable() {
            const tbody = document.getElementById('itensTableBody');

            if (itensOrcamento.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-plus-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                            Adicione produtos ou serviços ao orçamento
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            itensOrcamento.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="status-badge ${item.tipo === 'PRODUTO' ? 'status-ativo' : 'status-aprovado'}">
                            <i class="fas ${item.tipo === 'PRODUTO' ? 'fa-box' : 'fa-tools'}"></i>
                            ${item.tipo}
                        </span>
                    </td>
                    <td>${item.codigo}</td>
                    <td>${item.descricao}</td>
                    <td>
                        <input type="number" value="${item.quantidade}" min="1" step="0.01"
                               style="width: 80px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;"
                               onchange="updateItemQuantity(${index}, this.value)">
                    </td>
                    <td>
                        <input type="number" value="${item.valorUnitario.toFixed(2)}" min="0" step="0.01"
                               style="width: 100px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;"
                               onchange="updateItemPrice(${index}, this.value)">
                    </td>
                    <td style="font-weight: 600;">R$ ${item.total.toFixed(2)}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                onclick="removeItem(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Atualizar quantidade do item
        window.updateItemQuantity = function(index, newQuantity) {
            const quantity = parseFloat(newQuantity) || 1;
            itensOrcamento[index].quantidade = quantity;
            itensOrcamento[index].total = quantity * itensOrcamento[index].valorUnitario;
            updateItemsTable();
            updateSummary();
        };

        // Atualizar preço do item
        window.updateItemPrice = function(index, newPrice) {
            const price = parseFloat(newPrice) || 0;
            itensOrcamento[index].valorUnitario = price;
            itensOrcamento[index].total = itensOrcamento[index].quantidade * price;
            updateItemsTable();
            updateSummary();
        };

        // Remover item
        window.removeItem = function(index) {
            if (confirm('Deseja remover este item do orçamento?')) {
                itensOrcamento.splice(index, 1);
                updateItemsTable();
                updateSummary();
                showNotification('Item removido do orçamento', 'info');
            }
        };

        // Atualizar resumo financeiro
        function updateSummary() {
            const subtotalProdutos = itensOrcamento
                .filter(item => item.tipo === 'PRODUTO')
                .reduce((sum, item) => sum + item.total, 0);

            const subtotalServicos = itensOrcamento
                .filter(item => item.tipo === 'SERVICO')
                .reduce((sum, item) => sum + item.total, 0);

            const valorTotal = subtotalProdutos + subtotalServicos;

            document.getElementById('subtotalProdutos').textContent = `R$ ${subtotalProdutos.toFixed(2)}`;
            document.getElementById('subtotalServicos').textContent = `R$ ${subtotalServicos.toFixed(2)}`;
            document.getElementById('valorTotal').textContent = `R$ ${valorTotal.toFixed(2)}`;
        }

        // Salvar orçamento
        window.saveQuote = async function() {
            try {
                const clienteId = document.getElementById('clienteSelect').value;
                const dataValidade = document.getElementById('dataValidade').value;

                if (!clienteId) {
                    showNotification('Selecione um cliente', 'error');
                    return;
                }

                if (!dataValidade) {
                    showNotification('Informe a data de validade', 'error');
                    return;
                }

                if (itensOrcamento.length === 0) {
                    showNotification('Adicione pelo menos um item ao orçamento', 'error');
                    return;
                }

                showLoading(true);

                const subtotalProdutos = itensOrcamento
                    .filter(item => item.tipo === 'PRODUTO')
                    .reduce((sum, item) => sum + item.total, 0);

                const subtotalServicos = itensOrcamento
                    .filter(item => item.tipo === 'SERVICO')
                    .reduce((sum, item) => sum + item.total, 0);

                const orcamentoData = {
                    numero: document.getElementById('numeroOrcamento').value,
                    clienteId: clienteId,
                    dataValidade: Timestamp.fromDate(new Date(dataValidade)),
                    status: document.getElementById('statusOrcamento').value,
                    observacoes: document.getElementById('observacoes').value,
                    itens: itensOrcamento,
                    subtotalProdutos: subtotalProdutos,
                    subtotalServicos: subtotalServicos,
                    valorTotal: subtotalProdutos + subtotalServicos,
                    dataCriacao: Timestamp.now(),
                    usuarioCriacao: 'usuario_atual' // Implementar autenticação
                };

                if (currentQuoteId) {
                    await updateDoc(doc(db, "orcamentos", currentQuoteId), orcamentoData);
                    showNotification('Orçamento atualizado com sucesso!', 'success');
                } else {
                    const docRef = await addDoc(collection(db, "orcamentos"), orcamentoData);
                    currentQuoteId = docRef.id;
                    showNotification('Orçamento salvo com sucesso!', 'success');
                }

            } catch (error) {
                console.error('Erro ao salvar orçamento:', error);
                showNotification('Erro ao salvar orçamento: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        };

        // Gerar PDF
        window.generatePDF = function() {
            showNotification('Funcionalidade de PDF em desenvolvimento', 'info');
        };

        // Funções utilitárias
        function showLoading(show) {
            document.getElementById('loading').classList.toggle('active', show);
        }

        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification notification-${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    </script>
</body>
</html>
