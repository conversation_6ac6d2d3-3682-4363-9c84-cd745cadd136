<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PLAZHA - SISTEMAS - Orçamentos</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    :root {
      --primary-color: #0854a0;
      --primary-hover: #0a4d8c;
      --secondary-color: #f0f3f6;
      --border-color: #d4d4d4;
      --text-color: #333;
      --success-color: #107e3e;
      --danger-color: #bb0000;
      --header-bg: #354a5f;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      font-size: 13px;
    }

    .sap-header {
      background-color: var(--header-bg);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sap-logo {
      font-weight: bold;
      font-size: 24px;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .sap-title {
      color: var(--primary-color);
      font-size: 20px;
      margin: 0 0 20px 0;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .totvs-form {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .totvs-form h2 {
      color: var(--primary-color);
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-group {
      margin-bottom: 10px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }

    .totvs-select, .totvs-input {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid var(--border-color);
      border-radius: 3px;
      font-size: 13px;
      background-color: white;
    }

    .btn-totvs-primary {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
    }

    .btn-totvs-secondary {
      background-color: white;
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      justify-content: flex-end;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .table th, .table td {
      padding: 12px;
      border: 1px solid #ddd;
      text-align: left;
    }

    .table th {
      background: #34495e;
      color: white;
    }

    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      display: none;
    }

    .notification-success {
      background-color: #d4edda;
      color: #155724;
      display: block;
    }

    .notification-error {
      background-color: #f8d7da;
      color: #721c24;
      display: block;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      background-color: white;
      margin: 5% auto;
      padding: 20px;
      width: 90%;
      max-width: 800px;
      border-radius: 5px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .close-button {
      float: right;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .item-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr 50px;
      gap: 10px;
      margin-bottom: 10px;
      align-items: center;
    }

    .btn-remove {
      background: #dc3545;
      color: white;
      border: none;
      padding: 5px 8px;
      border-radius: 3px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="sap-header">
    <div class="sap-logo">PLAZHA - SISTEMAS</div>
    <div id="authContainer">Usuário: <span id="userStatus">Não logado</span> | Empresa: 1000</div>
  </div>

  <div class="container">
    <div class="sap-title"><i class="fas fa-file-invoice-dollar"></i> Orçamentos</div>

    <div class="totvs-form">
      <h2>Filtros</h2>
      <div class="form-row">
        <div class="form-group">
          <label>Buscar Orçamento</label>
          <input type="text" id="searchInput" class="totvs-input" placeholder="Número ou cliente...">
        </div>
        <div class="form-group">
          <label>Filtrar por Status</label>
          <select id="statusFilter" class="totvs-select">
            <option value="">Todos os status</option>
            <option value="Aberto">Aberto</option>
            <option value="Aprovado">Aprovado</option>
            <option value="Rejeitado">Rejeitado</option>
          </select>
        </div>
      </div>
    </div>

    <table class="table">
      <thead>
        <tr>
          <th>Número</th>
          <th>Cliente</th>
          <th>Valor Total (R$)</th>
          <th>Data Validade</th>
          <th>Status</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody id="quoteTableBody">
        <tr>
          <td colspan="6" style="text-align: center; padding: 40px;">
            Carregando orçamentos...
          </td>
        </tr>
      </tbody>
    </table>

    <div class="form-actions">
      <button class="btn-totvs-primary" onclick="openQuoteModal()">
        <i class="fas fa-plus"></i> Novo Orçamento
      </button>
      <button class="btn-totvs-secondary" onclick="window.location.href='index.html'">
        <i class="fas fa-arrow-left"></i> Voltar
      </button>
    </div>
  </div>

  <!-- Modal de Orçamento -->
  <div id="quoteModal" class="modal">
    <div class="modal-content">
      <span class="close-button" onclick="closeModal()">&times;</span>
      <h2 id="modalTitle">Novo Orçamento</h2>
      
      <form id="quoteForm" onsubmit="saveQuote(event)">
        <div class="form-row">
          <div class="form-group">
            <label>Número</label>
            <input type="text" id="numero" class="totvs-input" readonly>
          </div>
          <div class="form-group">
            <label>Cliente *</label>
            <select id="clienteId" class="totvs-select" required>
              <option value="">Selecione o cliente...</option>
            </select>
          </div>
          <div class="form-group">
            <label>Data Validade *</label>
            <input type="date" id="dataValidade" class="totvs-input" required>
          </div>
          <div class="form-group">
            <label>Status</label>
            <select id="status" class="totvs-select">
              <option value="Aberto">Aberto</option>
              <option value="Aprovado">Aprovado</option>
              <option value="Rejeitado">Rejeitado</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Observações</label>
          <textarea id="observacoes" class="totvs-input" rows="3"></textarea>
        </div>

        <h3>Itens do Orçamento</h3>
        <div id="itemsList">
          <div class="item-row">
            <div class="form-group">
              <label>Produto *</label>
              <select class="totvs-select produto-select" required>
                <option value="">Selecione o produto...</option>
              </select>
            </div>
            <div class="form-group">
              <label>Quantidade *</label>
              <input type="number" class="totvs-input quantidade-input" min="0.01" step="0.01" required>
            </div>
            <div class="form-group">
              <label>Valor Unitário *</label>
              <input type="number" class="totvs-input valor-input" min="0.01" step="0.01" required>
            </div>
            <div class="form-group">
              <label>Total</label>
              <input type="number" class="totvs-input total-input" readonly>
            </div>
            <button type="button" class="btn-remove" onclick="removeItem(this)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <button type="button" class="btn-totvs-secondary" onclick="addItem()">
          <i class="fas fa-plus"></i> Adicionar Item
        </button>

        <div class="form-row" style="margin-top: 20px;">
          <div class="form-group">
            <label>Valor Total</label>
            <input type="number" id="valorTotal" class="totvs-input" readonly>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-totvs-primary">
            <i class="fas fa-save"></i> Salvar
          </button>
          <button type="button" class="btn-totvs-secondary" onclick="closeModal()">
            <i class="fas fa-times"></i> Cancelar
          </button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import { db } from './firebase-config.js';
    import { 
      collection, 
      getDocs, 
      addDoc, 
      updateDoc, 
      deleteDoc, 
      doc,
      Timestamp 
    } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

    let orcamentos = [];
    let clientes = [];
    let produtos = [];
    let usuarioAtual = null;
    let editingId = null;

    window.onload = async function() {
      usuarioAtual = JSON.parse(localStorage.getItem('currentUser'));
      if (!usuarioAtual) {
        window.location.href = 'login.html';
        return;
      }

      document.getElementById('userStatus').textContent = usuarioAtual.nome;
      await loadData();
    };

    async function loadData() {
      try {
        const [orcamentosSnap, clientesSnap, produtosSnap] = await Promise.all([
          getDocs(collection(db, "orcamentos")),
          getDocs(collection(db, "fornecedores")),
          getDocs(collection(db, "produtos"))
        ]);

        orcamentos = orcamentosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        clientes = clientesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        produtos = produtosSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        populateSelects();
        renderTable();
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        showNotification("Erro ao carregar dados", "error");
      }
    }

    function populateSelects() {
      const clienteSelect = document.getElementById('clienteId');
      clienteSelect.innerHTML = '<option value="">Selecione o cliente...</option>';
      
      clientes.forEach(cliente => {
        const nome = cliente.nome || cliente.razaoSocial || 'Cliente sem nome';
        clienteSelect.innerHTML += `<option value="${cliente.id}">${nome}</option>`;
      });

      updateProductSelects();
    }

    function updateProductSelects() {
      const produtoSelects = document.querySelectorAll('.produto-select');
      produtoSelects.forEach(select => {
        select.innerHTML = '<option value="">Selecione o produto...</option>';
        produtos.forEach(produto => {
          select.innerHTML += `<option value="${produto.id}" data-preco="${produto.precoVenda || 0}">${produto.codigo} - ${produto.descricao}</option>`;
        });
      });
    }

    function renderTable() {
      const tbody = document.getElementById('quoteTableBody');
      tbody.innerHTML = '';

      if (orcamentos.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">Nenhum orçamento encontrado</td></tr>';
        return;
      }

      orcamentos.forEach(orcamento => {
        const cliente = clientes.find(c => c.id === orcamento.clienteId);
        const nomeCliente = cliente ? (cliente.nome || cliente.razaoSocial || 'Desconhecido') : 'Desconhecido';
        
        let dataValidade = 'N/A';
        if (orcamento.dataValidade) {
          try {
            if (orcamento.dataValidade.seconds) {
              dataValidade = new Date(orcamento.dataValidade.seconds * 1000).toLocaleDateString('pt-BR');
            } else if (orcamento.dataValidade instanceof Date) {
              dataValidade = orcamento.dataValidade.toLocaleDateString('pt-BR');
            }
          } catch (error) {
            console.warn('Erro ao converter data:', error);
          }
        }

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${orcamento.numero || 'N/A'}</td>
          <td>${nomeCliente}</td>
          <td style="text-align: right;">R$ ${(orcamento.valorTotal || 0).toFixed(2)}</td>
          <td>${dataValidade}</td>
          <td><span style="padding: 3px 8px; border-radius: 3px; background: #e3f2fd; color: #1976d2;">${orcamento.status || 'N/A'}</span></td>
          <td>
            <button onclick="editQuote('${orcamento.id}')" style="padding: 4px 8px; margin: 2px; border: 1px solid #ccc; background: white; cursor: pointer;">
              <i class="fas fa-edit"></i> Editar
            </button>
            <button onclick="deleteQuote('${orcamento.id}')" style="padding: 4px 8px; margin: 2px; border: 1px solid #dc3545; background: #dc3545; color: white; cursor: pointer;">
              <i class="fas fa-trash"></i> Excluir
            </button>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    window.openQuoteModal = function() {
      editingId = null;
      document.getElementById('modalTitle').textContent = 'Novo Orçamento';
      document.getElementById('numero').value = generateQuoteNumber();
      document.getElementById('quoteForm').reset();
      document.getElementById('quoteModal').style.display = 'block';
      
      // Reset items
      const itemsList = document.getElementById('itemsList');
      itemsList.innerHTML = `
        <div class="item-row">
          <div class="form-group">
            <label>Produto *</label>
            <select class="totvs-select produto-select" required>
              <option value="">Selecione o produto...</option>
            </select>
          </div>
          <div class="form-group">
            <label>Quantidade *</label>
            <input type="number" class="totvs-input quantidade-input" min="0.01" step="0.01" required>
          </div>
          <div class="form-group">
            <label>Valor Unitário *</label>
            <input type="number" class="totvs-input valor-input" min="0.01" step="0.01" required>
          </div>
          <div class="form-group">
            <label>Total</label>
            <input type="number" class="totvs-input total-input" readonly>
          </div>
          <button type="button" class="btn-remove" onclick="removeItem(this)">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      `;
      updateProductSelects();
      setupItemEvents();
    };

    window.editQuote = function(id) {
      const orcamento = orcamentos.find(o => o.id === id);
      if (!orcamento) return;

      editingId = id;
      document.getElementById('modalTitle').textContent = 'Editar Orçamento';
      document.getElementById('numero').value = orcamento.numero;
      document.getElementById('clienteId').value = orcamento.clienteId;
      document.getElementById('status').value = orcamento.status;
      document.getElementById('observacoes').value = orcamento.observacoes || '';
      
      if (orcamento.dataValidade && orcamento.dataValidade.seconds) {
        const date = new Date(orcamento.dataValidade.seconds * 1000);
        document.getElementById('dataValidade').value = date.toISOString().split('T')[0];
      }

      // Load items
      const itemsList = document.getElementById('itemsList');
      itemsList.innerHTML = '';
      
      if (orcamento.itens && orcamento.itens.length > 0) {
        orcamento.itens.forEach(item => {
          addItemWithData(item);
        });
      } else {
        addItem();
      }

      document.getElementById('quoteModal').style.display = 'block';
    };

    window.deleteQuote = async function(id) {
      if (!confirm('Tem certeza que deseja excluir este orçamento?')) return;

      try {
        await deleteDoc(doc(db, "orcamentos", id));
        showNotification('Orçamento excluído com sucesso!', 'success');
        await loadData();
      } catch (error) {
        console.error("Erro ao excluir:", error);
        showNotification('Erro ao excluir orçamento', 'error');
      }
    };

    window.closeModal = function() {
      document.getElementById('quoteModal').style.display = 'none';
    };

    window.addItem = function() {
      const itemsList = document.getElementById('itemsList');
      const newItem = document.createElement('div');
      newItem.className = 'item-row';
      newItem.innerHTML = `
        <div class="form-group">
          <label>Produto *</label>
          <select class="totvs-select produto-select" required>
            <option value="">Selecione o produto...</option>
          </select>
        </div>
        <div class="form-group">
          <label>Quantidade *</label>
          <input type="number" class="totvs-input quantidade-input" min="0.01" step="0.01" required>
        </div>
        <div class="form-group">
          <label>Valor Unitário *</label>
          <input type="number" class="totvs-input valor-input" min="0.01" step="0.01" required>
        </div>
        <div class="form-group">
          <label>Total</label>
          <input type="number" class="totvs-input total-input" readonly>
        </div>
        <button type="button" class="btn-remove" onclick="removeItem(this)">
          <i class="fas fa-trash"></i>
        </button>
      `;
      itemsList.appendChild(newItem);
      updateProductSelects();
      setupItemEvents();
    };

    function addItemWithData(item) {
      const itemsList = document.getElementById('itemsList');
      const newItem = document.createElement('div');
      newItem.className = 'item-row';
      newItem.innerHTML = `
        <div class="form-group">
          <label>Produto *</label>
          <select class="totvs-select produto-select" required>
            <option value="">Selecione o produto...</option>
          </select>
        </div>
        <div class="form-group">
          <label>Quantidade *</label>
          <input type="number" class="totvs-input quantidade-input" min="0.01" step="0.01" value="${item.quantidade}" required>
        </div>
        <div class="form-group">
          <label>Valor Unitário *</label>
          <input type="number" class="totvs-input valor-input" min="0.01" step="0.01" value="${item.valorUnitario}" required>
        </div>
        <div class="form-group">
          <label>Total</label>
          <input type="number" class="totvs-input total-input" value="${item.total}" readonly>
        </div>
        <button type="button" class="btn-remove" onclick="removeItem(this)">
          <i class="fas fa-trash"></i>
        </button>
      `;
      itemsList.appendChild(newItem);
      updateProductSelects();
      
      // Set selected product
      const select = newItem.querySelector('.produto-select');
      select.value = item.produtoId;
      
      setupItemEvents();
      calculateTotal();
    }

    window.removeItem = function(button) {
      const itemRow = button.closest('.item-row');
      if (document.querySelectorAll('.item-row').length > 1) {
        itemRow.remove();
        calculateTotal();
      }
    };

    function setupItemEvents() {
      document.querySelectorAll('.produto-select').forEach(select => {
        select.onchange = function() {
          const option = this.selectedOptions[0];
          const preco = option.dataset.preco || 0;
          const row = this.closest('.item-row');
          const valorInput = row.querySelector('.valor-input');
          valorInput.value = preco;
          calculateItemTotal(row);
        };
      });

      document.querySelectorAll('.quantidade-input, .valor-input').forEach(input => {
        input.oninput = function() {
          const row = this.closest('.item-row');
          calculateItemTotal(row);
        };
      });
    }

    function calculateItemTotal(row) {
      const quantidade = parseFloat(row.querySelector('.quantidade-input').value) || 0;
      const valor = parseFloat(row.querySelector('.valor-input').value) || 0;
      const total = quantidade * valor;
      row.querySelector('.total-input').value = total.toFixed(2);
      calculateTotal();
    }

    function calculateTotal() {
      let total = 0;
      document.querySelectorAll('.total-input').forEach(input => {
        total += parseFloat(input.value) || 0;
      });
      document.getElementById('valorTotal').value = total.toFixed(2);
    }

    window.saveQuote = async function(event) {
      event.preventDefault();

      const formData = {
        numero: document.getElementById('numero').value,
        clienteId: document.getElementById('clienteId').value,
        dataValidade: Timestamp.fromDate(new Date(document.getElementById('dataValidade').value)),
        status: document.getElementById('status').value,
        observacoes: document.getElementById('observacoes').value,
        valorTotal: parseFloat(document.getElementById('valorTotal').value) || 0,
        itens: [],
        dataCriacao: Timestamp.now(),
        criadoPor: usuarioAtual.id
      };

      // Collect items
      document.querySelectorAll('.item-row').forEach(row => {
        const produtoId = row.querySelector('.produto-select').value;
        const quantidade = parseFloat(row.querySelector('.quantidade-input').value) || 0;
        const valorUnitario = parseFloat(row.querySelector('.valor-input').value) || 0;
        const total = parseFloat(row.querySelector('.total-input').value) || 0;

        if (produtoId && quantidade > 0 && valorUnitario > 0) {
          formData.itens.push({
            produtoId,
            quantidade,
            valorUnitario,
            total
          });
        }
      });

      if (formData.itens.length === 0) {
        showNotification('Adicione pelo menos um item ao orçamento', 'error');
        return;
      }

      try {
        if (editingId) {
          await updateDoc(doc(db, "orcamentos", editingId), formData);
          showNotification('Orçamento atualizado com sucesso!', 'success');
        } else {
          await addDoc(collection(db, "orcamentos"), formData);
          showNotification('Orçamento criado com sucesso!', 'success');
        }

        closeModal();
        await loadData();
      } catch (error) {
        console.error("Erro ao salvar:", error);
        showNotification('Erro ao salvar orçamento', 'error');
      }
    };

    function generateQuoteNumber() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
      return `ORC${year}${month}${day}${time}`;
    }

    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Filtros
    document.getElementById('searchInput').addEventListener('input', filterTable);
    document.getElementById('statusFilter').addEventListener('change', filterTable);

    function filterTable() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;
      
      const rows = document.querySelectorAll('#quoteTableBody tr');
      
      rows.forEach(row => {
        if (row.cells.length < 6) return;
        
        const numero = row.cells[0].textContent.toLowerCase();
        const cliente = row.cells[1].textContent.toLowerCase();
        const status = row.cells[4].textContent.trim();
        
        const matchesSearch = numero.includes(searchTerm) || cliente.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        
        row.style.display = matchesSearch && matchesStatus ? '' : 'none';
      });
    }

    // Initialize
    setupItemEvents();
  </script>
</body>
</html>