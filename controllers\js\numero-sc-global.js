
// ===================================================================
// FUNÇÃO GLOBAL PARA GERAÇÃO DE NÚMEROS DE SOLICITAÇÕES DE COMPRA
// ===================================================================

/**
 * Função global para gerar números de SC
 * Centraliza o uso do NumberGeneratorService
 */
window.generateSolicitacaoNumber = async function() {
    try {
        // Verificar se o serviço está disponível
        if (window.NumberGeneratorService) {
            return await window.NumberGeneratorService.generateSolicitacaoNumber();
        }
        
        // Importar se não estiver disponível
        const { NumberGeneratorService } = await import('../services/number-generator-service.js');
        return await NumberGeneratorService.generateSolicitacaoNumber();
        
    } catch (error) {
        console.error('❌ Erro ao gerar número SC:', error);
        
        // Fallback seguro
        const hoje = new Date();
        const ano = hoje.getFullYear().toString().slice(-2);
        const mes = (hoje.getMonth() + 1).toString().padStart(2, '0');
        const timestamp = Date.now().toString().slice(-4);
        const fallbackNumber = `SC-${ano}${mes}-${timestamp}`;
        
        console.warn('⚠️ Usando numeração fallback:', fallbackNumber);
        return fallbackNumber;
    }
};

/**
 * Função para validar formato de número SC
 */
window.validateSolicitacaoNumber = function(numero) {
    const regex = /^SC-\d{4}-\d{4}$/;
    return regex.test(numero);
};

/**
 * Função para extrair informações do número SC
 */
window.parseSolicitacaoNumber = function(numero) {
    const match = numero.match(/^SC-(\d{2})(\d{2})-(\d{4})$/);
    if (!match) return null;
    
    return {
        prefix: 'SC',
        year: '20' + match[1],
        month: match[2],
        sequence: parseInt(match[3]),
        full: numero
    };
};

console.log('🔢 Funções globais de numeração SC carregadas');
